# 🔒 Advanced JavaScript & HTML Security Analyzer

A comprehensive Python-based security analysis tool designed to identify vulnerabilities, secrets, and security issues in JavaScript and HTML files. This tool supports both local file analysis and web-based content extraction with advanced pattern matching capabilities.

## ✨ Features

### 🔍 **Comprehensive Security Analysis**
- **API Key Detection**: Identifies exposed API keys from major services (Google, AWS, Facebook, Twilio, etc.)
- **Vulnerability Scanning**: Detects XSS, SQL injection, CSRF, open redirects, and more
- **Secret Discovery**: Finds passwords, tokens, private keys, and sensitive data
- **Code Analysis**: Analyzes functions, variables, imports, and code structure
- **Obfuscation Detection**: Identifies potentially obfuscated or minified code

### 🌐 **Web Content Analysis**
- **Single Page Download**: Extract and analyze JavaScript/HTML from any URL
- **Recursive Crawling**: Automatically discover and analyze all JavaScript files across a website
- **Multi-threaded Downloads**: Fast concurrent downloading with progress tracking
- **Smart Filtering**: Automatically skips CSS and non-relevant content

### 📁 **File System Analysis**
- **Recursive Directory Scanning**: Analyze entire directory structures
- **Multiple File Extensions**: Supports `.js`, `.html`, `.htm`, `.js.backup`, `.js.download`, etc.
- **Memory Optimization**: Handles large files efficiently with chunked processing
- **Progress Tracking**: Real-time progress bars and status updates

### 📊 **Advanced Reporting**
- **Detailed Findings**: Comprehensive vulnerability reports with context
- **Severity Classification**: Critical, High, Medium, Low risk categorization
- **Testing Guides**: Includes exploitation steps and PoC examples
- **Export Options**: JSON and text-based report generation
- **Cross-file Analysis**: Tracks issues spanning multiple files

## 🚀 Installation

### Prerequisites
- Python 3.7 or higher
- Required Python packages (install via pip)

### Setup
```bash
# Clone or download the script files
git clone <repository-url>
cd JSFucker

# Install required dependencies
pip install requests beautifulsoup4

# Ensure pattern files are present
# - api_patterns.py (API key patterns)
# - vuln_patterns.py (Vulnerability patterns)
```

### Required Files
- `sucrit.py` - Main analysis script
- `api_patterns.py` - API key and secret detection patterns
- `vuln_patterns.py` - Vulnerability detection patterns

## 📖 Usage

### Command Line Interface

#### Basic Directory Analysis
```bash
python sucrit.py
# Follow interactive prompts to select analysis type
```

#### Web URL Analysis
```bash
python sucrit.py
# Choose option 2 for single URL analysis
# Choose option 3 for recursive website crawling
```

#### Advanced Options
```bash
# Direct file analysis
python sucrit.py --path /path/to/js/files

# URL analysis with custom depth
python sucrit.py --url https://example.com --depth 3

# Skip CSS content (recommended)
python sucrit.py --skip-css
```

### Interactive Menu Options

1. **📁 Analyze Local Directory**
   - Recursively scan local JavaScript/HTML files
   - Supports multiple file extensions
   - Memory-optimized for large codebases

2. **🌐 Analyze Single URL**
   - Download and analyze a single webpage
   - Extracts all linked JavaScript files
   - Saves files locally for offline analysis

3. **🚀 Recursive Website Analysis**
   - Crawl entire website for JavaScript files
   - Multi-threaded downloading
   - Configurable crawl depth
   - Progress tracking and statistics

## 🔧 Configuration

### Pattern Customization
Modify `api_patterns.py` and `vuln_patterns.py` to add custom detection patterns:

```python
# api_patterns.py
API_PATTERNS = {
    'custom_api_key': [
        r'custom-key-[a-zA-Z0-9]{32}',
    ],
    # ... more patterns
}

# vuln_patterns.py
VULN_PATTERNS = {
    'custom_vulnerability': [
        r'dangerous_function\s*\([^)]*user_input',
    ],
    # ... more patterns
}
```

### Memory Optimization
- Large files (>50MB) are automatically skipped
- Content is processed in 50KB chunks for memory efficiency
- Concurrent processing limits prevent resource exhaustion

## 📋 Output Examples

### Vulnerability Report
```
🔴 CRITICAL: XSS Reflected Vulnerability
File: /path/to/vulnerable.js:42
Pattern: .innerHTML = location.search
Context: element.innerHTML = location.search.substring(1);
Testing Guide: Try payload: <script>alert('XSS')</script>
```

### API Key Detection
```
🔑 API Key Found: Google API Key
File: /path/to/config.js:15
Type: google_api
Match: AIzaSyC4E1Dz4W8aW5W5W5W5W5W5W5W5W5W5W5W
```

### Analysis Summary
```
📊 Analysis Complete
Files Processed: 156
Vulnerabilities: 23 (5 Critical, 8 High, 10 Medium)
API Keys: 7
Secrets: 12
Processing Time: 45.2s
```

## 🛡️ Security Features

### Vulnerability Detection
- **XSS**: Reflected, Stored, DOM-based
- **Injection**: SQL, NoSQL, Command injection
- **CSRF**: Missing token validation
- **Open Redirect**: Unvalidated redirects
- **Path Traversal**: Directory traversal attempts
- **Insecure Crypto**: Weak encryption usage

### Secret Detection
- **API Keys**: 50+ service providers
- **Credentials**: Passwords, tokens, certificates
- **Database URLs**: Connection strings
- **Private Keys**: RSA, SSH, PGP keys
- **JWT Tokens**: JSON Web Tokens

## ⚡ Performance

### Optimization Features
- **Multi-threading**: Concurrent file processing
- **Memory Management**: Chunked processing for large files
- **Smart Filtering**: Skip irrelevant content automatically
- **Progress Tracking**: Real-time status updates
- **Caching**: Avoid duplicate processing

### Benchmarks
- **Local Analysis**: ~1000 files/minute
- **Web Crawling**: ~10 pages/second
- **Memory Usage**: <500MB for typical projects
- **Pattern Matching**: 100+ patterns per file

## 🤝 Contributing

### Adding New Patterns
1. Edit `api_patterns.py` or `vuln_patterns.py`
2. Add regex patterns following existing format
3. Test patterns against known samples
4. Update documentation

### Reporting Issues
- Include sample code that triggers false positives/negatives
- Provide system information (Python version, OS)
- Describe expected vs actual behavior

## 📄 License

This tool is provided for educational and security research purposes. Use responsibly and only on systems you own or have explicit permission to test.

## ⚠️ Disclaimer

This tool is designed for legitimate security testing and code review purposes. Users are responsible for ensuring they have proper authorization before analyzing any systems or code they do not own.

---

**Author**: Security Research Team  
**Version**: 2.0  
**Last Updated**: 2024
