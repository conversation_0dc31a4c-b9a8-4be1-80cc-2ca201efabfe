#!/usr/bin/env python3
"""
API Keys and Sensitive Data Patterns
Comprehensive regex patterns for detecting API keys, secrets, and sensitive data
"""

# Comprehensive API key and sensitive data patterns
API_PATTERNS = {
    'google_api': [
        r'AIza[0-9A-Za-z-_]{35}',
    ],
    'google_captcha': [
        r'6L[0-9A-Za-z-_]{38}',
        r'^6[0-9a-zA-Z_-]{39}$',
    ],
    'google_oauth': [
        r'ya29\.[0-9A-Za-z\-_]+',
    ],
    'amazon_aws_access_key': [
        r'A[SK]IA[0-9A-Z]{16}',
    ],
    'amazon_mws_auth_token': [
        r'amzn\.mws\.[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}',
    ],
    'amazon_aws_url': [
        r's3\.amazonaws.com[/]+',
        r'[a-zA-Z0-9_-]*\.s3\.amazonaws.com',
    ],
    'facebook_access_token': [
        r'EAACEdEose0cBA[0-9A-Za-z]+',
    ],
    'authorization_basic': [
        r'basic\s*[a-zA-Z0-9=:_\+\/-]+',
    ],
    'authorization_bearer': [
        r'bearer\s*[a-zA-Z0-9_\-\.=:_\+\/]+',
    ],
    'authorization_api': [
        r'api[key|\s*]+[a-zA-Z0-9_\-]+',
    ],
    'mailgun_api_key': [
        r'key-[0-9a-zA-Z]{32}',
    ],
    'twilio_api_key': [
        r'SK[0-9a-fA-F]{32}',
    ],
    'twilio_account_sid': [
        r'AC[a-zA-Z0-9_\-]{32}',
    ],
    'twilio_app_sid': [
        r'AP[a-zA-Z0-9_\-]{32}',
    ],
    'paypal_braintree_access_token': [
        r'access_token\$production\$[0-9a-z]{16}\$[0-9a-f]{32}',
    ],
    'square_oauth_secret': [
        r'sq0csp-[0-9A-Za-z\-_]{43}',
        r'sq0[a-z]{3}-[0-9A-Za-z\-_]{22,43}',
    ],
    'square_access_token': [
        r'sqOatp-[0-9A-Za-z\-_]{22}',
        r'EAAA[a-zA-Z0-9]{60}',
    ],
    'stripe_standard_api': [
        r'sk_live_[0-9a-zA-Z]{24}',
    ],
    'stripe_restricted_api': [
        r'rk_live_[0-9a-zA-Z]{24}',
    ],
    'github_access_token': [
        r'[a-zA-Z0-9_-]*:[a-zA-Z0-9_\-]+@github\.com',
    ],
    'rsa_private_key': [
        r'-----BEGIN RSA PRIVATE KEY-----',
    ],
    'ssh_dsa_private_key': [
        r'-----BEGIN DSA PRIVATE KEY-----',
    ],
    'ssh_ec_private_key': [
        r'-----BEGIN EC PRIVATE KEY-----',
    ],
    'pgp_private_block': [
        r'-----BEGIN PGP PRIVATE KEY BLOCK-----',
    ],
    'json_web_token': [
        r'ey[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*',
    ],
    'generic_api_keys': [
        r'(?i)(api[_-]?key|apikey)\s*[:=]\s*["\']([A-Za-z0-9_\-]{20,})["\']',
        r'(?i)(secret[_-]?key|secretkey)\s*[:=]\s*["\']([A-Za-z0-9_\-]{20,})["\']',
        r'(?i)(access[_-]?token|accesstoken)\s*[:=]\s*["\']([A-Za-z0-9_\-]{20,})["\']',
    ],
    'passwords': [
        r'(?i)(?:password|passwd|pwd)\s*[:=]\s*["\']([^"\']{6,50})["\']',
    ],
    'database_urls': [
        r'(?i)(?:database[_-]?url|db[_-]?url)\s*[:=]\s*["\']([^"\']+)["\']',
        r'\bmongodb://[^\s"\'<>]+',
        r'\bmysql://[^\s"\'<>]+',
        r'\bpostgres://[^\s"\'<>]+',
    ],
    'emails': [
        r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b',
    ],
    'ip_addresses': [
        r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b',
    ]
}
