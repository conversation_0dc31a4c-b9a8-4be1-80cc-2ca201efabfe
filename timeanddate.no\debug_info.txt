=== DEBUG INFORMATION ===

1. Debug Statement
   File: page.html
   Line: 2493
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

2. Inline Javascript
   File: page.html
   Line: 25
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

3. Inline Javascript
   File: page.html
   Line: 139
   Content: 
       {"@context" : "http://schema.org",
"@type":"Organization",
"name":"timeanddate.no",
"alternateName" : "Time and Date Norge",
"url" : "http://www.timeanddate.no",
"logo": "https://c.tadst.com/g
   Risk: LOW - Debug code should be removed from production

4. Inline Javascript
   File: page.html
   Line: 170
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

5. Inline Javascript
   File: page.html
   Line: 638
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

6. Inline Javascript
   File: page.html
   Line: 641
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

7. Inline Javascript
   File: page.html
   Line: 2471
   Content: 
        AdMgr.dispSlot(1);
       
   Risk: LOW - Debug code should be removed from production

8. Inline Javascript
   File: page.html
   Line: 2480
   Content: 
    et=1752668248;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d.ge
   Risk: LOW - Debug code should be removed from production

9. Inline Javascript
   File: page.html
   Line: 2760
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

10. Debug Statement
   File: tag_o_5174239513018368_upapi_true.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

11. Debug Statement
   File: tag_o_5174239513018368_upapi_true.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

12. Debug Statement
   File: wcommon_34.js
   Line: 47
   Statement: alert(
   Risk: LOW - Debug code should be removed from production

