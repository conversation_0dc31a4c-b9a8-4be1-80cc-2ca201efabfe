(function(sttc){var window=this;if(window.googletag&&googletag.evalScripts){googletag.evalScripts();}if(window.googletag&&googletag._loaded_)return;var q,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");},da=ca(this),ea=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",u={},fa={},v=function(a,b,c){if(!c||a!=null){c=fa[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}},w=function(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],f;!a&&e in u?f=u:f=da;for(e=0;e<d.length-1;e++){var g=d[e];if(!(g in f))break a;f=f[g]}d=d[d.length-1];c=ea&&c==="es6"?f[d]:null;b=b(c);b!=null&&(a?ba(u,d,{configurable:!0,writable:!0,value:b}):b!==c&&(fa[d]===void 0&&(a=Math.random()*1E9>>>0,fa[d]=ea?da.Symbol(d):"$jscp$"+a+"$"+d),ba(f,fa[d],{configurable:!0,writable:!0,value:b})))}};w("Symbol",function(a){if(a)return a;var b=function(f,g){this.g=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.g};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6");w("Symbol.iterator",function(a){if(a)return a;a=(0,u.Symbol)("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=da[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ha(aa(this))}})}return a},"es6");var ha=function(a){a={next:a};a[v(u.Symbol,"iterator")]=function(){return this};return a},ia=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ja;if(ea&&typeof Object.setPrototypeOf=="function")ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var na=ja,x=function(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(na)na(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Nb=b.prototype},y=function(a){var b=typeof u.Symbol!="undefined"&&v(u.Symbol,"iterator")&&a[v(u.Symbol,"iterator")];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},B=function(a){if(!(a instanceof Array)){a=y(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a},qa=function(a){return oa(a,a)},oa=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},C=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},ra=ea&&typeof v(Object,"assign")=="function"?v(Object,"assign"):function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)C(d,e)&&(a[e]=d[e])}return a};w("Object.assign",function(a){return a||ra},"es6");var sa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};w("globalThis",function(a){return a||da},"es_2020");w("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}},"es6");w("WeakMap",function(a){function b(){}function c(g){var h=typeof g;return h==="object"&&g!==null||h==="function"}if(function(){if(!a||!Object.seal)return!1;try{var g=Object.seal({}),h=Object.seal({}),k=new a([[g,2],[h,3]]);if(k.get(g)!=2||k.get(h)!=3)return!1;k.delete(g);k.set(h,4);return!k.has(g)&&k.get(h)==4}catch(m){return!1}}())return a;var d="$jscomp_hidden_"+Math.random(),e=0,f=function(g){this.g=(e+=Math.random()+1).toString();if(g){g=y(g);for(var h;!(h=g.next()).done;)h=h.value,this.set(h[0],h[1])}};f.prototype.set=function(g,h){if(!c(g))throw Error("Invalid WeakMap key");if(!C(g,d)){var k=new b;ba(g,d,{value:k})}if(!C(g,d))throw Error("WeakMap key fail: "+g);g[d][this.g]=h;return this};f.prototype.get=function(g){return c(g)&&C(g,d)?g[d][this.g]:void 0};f.prototype.has=function(g){return c(g)&&C(g,d)&&C(g[d],this.g)};f.prototype.delete=function(g){return c(g)&&C(g,d)&&C(g[d],this.g)?delete g[d][this.g]:!1};return f},"es6");w("Map",function(a){if(function(){if(!a||typeof a!="function"||!v(a.prototype,"entries")||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),k=new a(y([[h,"s"]]));if(k.get(h)!="s"||k.size!=1||k.get({x:4})||k.set({x:4},"t")!=k||k.size!=2)return!1;var m=v(k,"entries").call(k),n=m.next();if(n.done||n.value[0]!=h||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(l){return!1}}())return a;var b=new u.WeakMap,c=function(h){this[0]={};this[1]=f();this.size=0;if(h){h=y(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=h===0?0:h;var m=d(this,h);m.list||(m.list=this[0][m.id]=[]);m.entry?m.entry.value=k:(m.entry={next:this[1],H:this[1].H,head:this[1],key:h,value:k},m.list.push(m.entry),this[1].H.next=m.entry,this[1].H=m.entry,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.entry.H.next=h.entry.next,h.entry.next.H=h.entry.H,h.entry.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].H=f();this.size=0};c.prototype.has=function(h){return!!d(this,h).entry};c.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=function(h,k){for(var m=v(this,"entries").call(this),n;!(n=m.next()).done;)n=n.value,h.call(k,n[1],n[0],this)};c.prototype[v(u.Symbol,"iterator")]=v(c.prototype,"entries");var d=function(h,k){var m=k&&typeof k;m=="object"||m=="function"?b.has(k)?m=b.get(k):(m=""+ ++g,b.set(k,m)):m="p_"+k;var n=h[0][m];if(n&&C(h[0],m))for(h=0;h<n.length;h++){var l=n[h];if(k!==k&&l.key!==l.key||k===l.key)return{id:m,list:n,index:h,entry:l}}return{id:m,list:n,index:-1,entry:void 0}},e=function(h,k){var m=h[1];return ha(function(){if(m){for(;m.head!=h[1];)m=m.H;for(;m.next!=m.head;)return m=m.next,{done:!1,value:k(m)};m=null}return{done:!0,value:void 0}})},f=function(){var h={};return h.H=h.next=h.head=h},g=0;return c},"es6");w("Set",function(a){if(function(){if(!a||typeof a!="function"||!v(a.prototype,"entries")||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(y([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=v(d,"entries").call(d),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;var b=function(c){this.g=new u.Map;if(c){c=y(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size};b.prototype.add=function(c){c=c===0?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return v(this.g,"entries").call(this.g)};b.prototype.values=function(){return v(this.g,"values").call(this.g)};b.prototype.keys=v(b.prototype,"values");b.prototype[v(u.Symbol,"iterator")]=v(b.prototype,"values");b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b},"es6");w("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)C(b,d)&&c.push(b[d]);return c}},"es8");w("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}},"es6");w("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||v(Object,"is").call(Object,f,b))return!0}return!1}},"es7");var ta=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};w("String.prototype.includes",function(a){return a?a:function(b,c){return ta(this,b,"includes").indexOf(b,c||0)!==-1}},"es6");w("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(h){return h};var e=[],f=typeof u.Symbol!="undefined"&&v(u.Symbol,"iterator")&&b[v(u.Symbol,"iterator")];if(typeof f=="function"){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}},"es6");w("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)C(b,d)&&c.push([d,b[d]]);return c}},"es8");w("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}},"es6");w("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991},"es6");w("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991},"es6");w("Number.isInteger",function(a){return a?a:function(b){return v(Number,"isFinite").call(Number,b)?b===Math.floor(b):!1}},"es6");w("Number.isSafeInteger",function(a){return a?a:function(b){return v(Number,"isInteger").call(Number,b)&&Math.abs(b)<=v(Number,"MAX_SAFE_INTEGER")}},"es6");w("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=ta(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}},"es6");var ua=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[v(u.Symbol,"iterator")]=function(){return e};return e};w("Array.prototype.entries",function(a){return a?a:function(){return ua(this,function(b,c){return[b,c]})}},"es6");w("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}},"es6");w("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}},"es6");w("Array.prototype.keys",function(a){return a?a:function(){return ua(this,function(b){return b})}},"es6");w("Array.prototype.values",function(a){return a?a:function(){return ua(this,function(b,c){return c})}},"es8");w("String.prototype.repeat",function(a){return a?a:function(b){var c=ta(this,null,"repeat");if(b<0||b>**********)throw new RangeError("Invalid count value");b|=0;for(var d="";b;)if(b&1&&(d+=c),b>>>=1)c+=c;return d}},"es6");w("String.prototype.padStart",function(a){return a?a:function(b,c){var d=ta(this,null,"padStart");b-=d.length;c=c!==void 0?String(c):" ";return(b>0&&c?v(c,"repeat").call(c,Math.ceil(b/c.length)).substring(0,b):"")+d}},"es8");/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var D=this||self,va=function(a){a=a.split(".");for(var b=D,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b},wa=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"},xa=function(a,b,c){a=a.split(".");c=c||D;for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};function ya(a){D.setTimeout(function(){throw a;},0)};var za=function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};function Aa(a,b){var c=0;a=za(String(a)).split(".");b=za(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",g=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];if(f[0].length==0&&g[0].length==0)break;c=Ba(f[1].length==0?0:parseInt(f[1],10),g[1].length==0?0:parseInt(g[1],10))||Ba(f[2].length==0,g[2].length==0)||Ba(f[2],g[2]);f=f[3];g=g[3]}while(c==0)}return c}function Ba(a,b){return a<b?-1:a>b?1:0};var Ca,Da=va("CLOSURE_FLAGS"),Ea=Da&&Da[610401301];Ca=Ea!=null?Ea:!1;var Fa,Ga=D.navigator;Fa=Ga?Ga.userAgentData||null:null;function Ha(a){if(!Ca||!Fa)return!1;for(var b=0;b<Fa.brands.length;b++){var c=Fa.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function E(a){var b;a:{if(b=D.navigator)if(b=b.userAgent)break a;b=""}return b.indexOf(a)!=-1};function Ia(){return Ca?!!Fa&&Fa.brands.length>0:!1}function Ja(){return Ia()?!1:E("Opera")}function Ka(){return E("Firefox")||E("FxiOS")}function La(){return E("Safari")&&!(Ma()||(Ia()?0:E("Coast"))||Ja()||(Ia()?0:E("Edge"))||(Ia()?Ha("Microsoft Edge"):E("Edg/"))||(Ia()?Ha("Opera"):E("OPR"))||Ka()||E("Silk")||E("Android"))}function Ma(){return Ia()?Ha("Chromium"):(E("Chrome")||E("CriOS"))&&!(Ia()?0:E("Edge"))||E("Silk")};var Na=function(a,b){return Array.prototype.map.call(a,b,void 0)};function Oa(a,b){a:{for(var c=typeof a==="string"?a.split(""):a,d=a.length-1;d>=0;d--)if(d in c&&b.call(void 0,c[d],d,a)){b=d;break a}b=-1}return b<0?null:typeof a==="string"?a.charAt(b):a[b]};var Pa=function(a){Pa[" "](a);return a};Pa[" "]=function(){};var Qa=null,Sa=function(a){var b=[];Ra(a,function(c){b.push(c)});return b},Ra=function(a,b){function c(k){for(;d<a.length;){var m=a.charAt(d++),n=Qa[m];if(n!=null)return n;if(!/^[\s\xa0]*$/.test(m))throw Error("Unknown base64 encoding at char: "+m);}return k}Ta();for(var d=0;;){var e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}},Ta=function(){if(!Qa){Qa={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++)for(var d=a.concat(b[c].split("")),e=0;e<d.length;e++){var f=d[e];Qa[f]===void 0&&(Qa[f]=e)}}};function Ua(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};var Va=void 0,Wa;function Xa(a){if(Wa)throw Error("");Wa=function(b){D.setTimeout(function(){a(b)},0)}}function Ya(a){if(Wa)try{Wa(a)}catch(b){throw b.cause=a,b;}}function Za(a){a=Error(a);Ua(a,"warning");Ya(a);return a};var $a=typeof u.Symbol==="function"&&typeof(0,u.Symbol)()==="symbol";function ab(a,b,c){return typeof u.Symbol==="function"&&typeof(0,u.Symbol)()==="symbol"?(c===void 0?0:c)&&u.Symbol.for&&a?u.Symbol.for(a):a!=null?(0,u.Symbol)(a):(0,u.Symbol)():b}var bb=ab("jas",void 0,!0),eb=ab(void 0,"0di"),fb=ab(void 0,"1oa"),gb=ab(void 0,"0actk"),hb=ab("m_m","Kb",!0);var ib={eb:{value:0,configurable:!0,writable:!0,enumerable:!1}},jb=Object.defineProperties,F=$a?bb:"eb",kb,lb=[];G(lb,7);kb=Object.freeze(lb);function mb(a,b){$a||F in a||jb(a,ib);a[F]|=b}function G(a,b){$a||F in a||jb(a,ib);a[F]=b}function nb(a){if(4&a)return 512&a?512:1024&a?1024:0}function ob(a){mb(a,32);return a};function pb(){return typeof BigInt==="function"};var qb={};function H(a,b){return b===void 0?a.g!==rb&&!!(2&(a.i[F]|0)):!!(2&b)&&a.g!==rb}var rb={};function sb(a,b){if(typeof b!=="number"||b<0||b>=a.length)throw Error();}var tb=Object.freeze({}),ub=Object.freeze({});function vb(a){var b=wb;if(!a)throw Error((typeof b==="function"?b():b)||String(a));}function xb(a){a.Jb=!0;return a}var wb=void 0;var yb=xb(function(a){return typeof a==="number"}),zb=xb(function(a){return typeof a==="string"}),Ab=xb(function(a){return typeof a==="boolean"});var Bb=typeof D.BigInt==="function"&&typeof D.BigInt(0)==="bigint";function Cb(a){var b=a;if(zb(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(yb(b)&&!v(Number,"isSafeInteger").call(Number,b))throw Error(String(b));return Bb?BigInt(a):a=Ab(a)?a?"1":"0":zb(a)?a.trim()||"0":String(a)}var Ib=xb(function(a){return Bb?a>=Db&&a<=Eb:a[0]==="-"?Fb(a,Gb):Fb(a,Hb)}),Gb=v(Number,"MIN_SAFE_INTEGER").toString(),Db=Bb?BigInt(v(Number,"MIN_SAFE_INTEGER")):void 0,Hb=v(Number,"MAX_SAFE_INTEGER").toString(),Eb=Bb?BigInt(v(Number,"MAX_SAFE_INTEGER")):void 0;function Fb(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};var I=0,J=0;function Jb(a){var b=a>>>0;I=b;J=(a-b)/4294967296>>>0}function Kb(a){if(a<0){Jb(-a);var b=y(Lb(I,J));a=b.next().value;b=b.next().value;I=a>>>0;J=b>>>0}else Jb(a)}function Mb(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else pb()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+Nb(c)+Nb(a));return c}function Nb(a){a=String(a);return"0000000".slice(a.length)+a}function Ob(){var a=I,b=J;b&2147483648?pb()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=y(Lb(a,b)),a=b.next().value,b=b.next().value,a="-"+Mb(a,b)):a=Mb(a,b);return a}function Lb(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};function Pb(a){return Array.prototype.slice.call(a)};function Qb(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var Rb=typeof BigInt==="function"?BigInt.asIntN:void 0,Sb=v(Number,"isSafeInteger"),Tb=v(Number,"isFinite"),Ub=v(Math,"trunc");function Vb(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function Wb(a){if(typeof a!=="boolean")throw Error("Expected boolean but got "+wa(a)+": "+a);return a}var Xb=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function Yb(a){switch(typeof a){case "bigint":return!0;case "number":return Tb(a);case "string":return Xb.test(a);default:return!1}}function Zb(a){if(!Tb(a))throw Za("enum");return a|0}function $b(a){return a==null?a:Tb(a)?a|0:void 0}function ac(a){if(typeof a!=="number")throw Za("int32");if(!Tb(a))throw Za("int32");return a|0}function bc(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Tb(a)?a|0:void 0}function cc(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Tb(a)?a>>>0:void 0}function dc(a){var b=0;b=b===void 0?0:b;if(!Yb(a))throw Za("int64");var c=typeof a;switch(b){case 512:switch(c){case "string":return ec(a);case "bigint":return String(Rb(64,a));default:return fc(a)}case 1024:switch(c){case "string":return hc(a);case "bigint":return Cb(Rb(64,a));default:return ic(a)}case 0:switch(c){case "string":return ec(a);case "bigint":return Cb(Rb(64,a));default:return jc(a)}default:return Qb(b,"Unknown format requested type for int64")}}function kc(a){return a==null?a:dc(a)}function lc(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}function mc(a){if(lc(a))return a;if(a.length<16)Kb(Number(a));else if(pb())a=BigInt(a),I=Number(a&BigInt(4294967295))>>>0,J=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");J=I=0;for(var c=a.length,d=b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),J*=1E6,I=I*1E6+d,I>=4294967296&&(J+=v(Math,"trunc").call(Math,I/4294967296),J>>>=0,I>>>=0);b&&(b=y(Lb(I,J)),a=b.next().value,b=b.next().value,I=a,J=b)}return Ob()}function jc(a){a=Ub(a);if(!Sb(a)){Kb(a);var b=I,c=J;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);var d=c*4294967296+(b>>>0);b=v(Number,"isSafeInteger").call(Number,d)?d:Mb(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function fc(a){a=Ub(a);if(Sb(a))a=String(a);else{var b=String(a);lc(b)?a=b:(Kb(a),a=Ob())}return a}function ec(a){var b=Ub(Number(a));if(Sb(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return mc(a)}function hc(a){var b=Ub(Number(a));if(Sb(b))return Cb(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return pb()?Cb(Rb(64,BigInt(a))):Cb(mc(a))}function ic(a){return Sb(a)?Cb(jc(a)):Cb(fc(a))}function nc(a){if(typeof a!=="string")throw Error();return a}function oc(a){if(a!=null&&typeof a!=="string")throw Error();return a}function pc(a){return a==null||typeof a==="string"?a:void 0}function qc(a,b,c,d){if(a!=null&&a[hb]===qb)return a;if(!Array.isArray(a))return c?d&2?((a=b[eb])||(a=new b,mb(a.i,34),a=b[eb]=a),b=a):b=new b:b=void 0,b;c=a[F]|0;d=c|d&32|d&2;d!==c&&G(a,d);return new b(a)};function rc(a){return a};function sc(a,b,c,d){var e=d!==void 0;d=!!d;var f=[],g=a.length,h=4294967295,k=!1,m=!!(b&64),n=m?b&128?0:-1:void 0;if(!(b&1)){var l=g&&a[g-1];l!=null&&typeof l==="object"&&l.constructor===Object?(g--,h=g):l=void 0;if(m&&!(b&128)&&!e){k=!0;var p;h=((p=tc)!=null?p:rc)(h-n,n,a,l)+n}}b=void 0;for(e=0;e<g;e++)if(p=a[e],p!=null&&(p=c(p,d))!=null)if(m&&e>=h){var r=e-n,t=void 0;((t=b)!=null?t:b={})[r]=p}else f[e]=p;if(l)for(var z in l)Object.prototype.hasOwnProperty.call(l,z)&&(a=l[z],a!=null&&(a=c(a,d))!=null&&(g=+z,e=void 0,m&&!v(Number,"isNaN").call(Number,g)&&(e=g+n)<h?f[e]=a:(g=void 0,((g=b)!=null?g:b={})[z]=a)));b&&(k?f.push(b):f[h]=b);return f}function uc(a){switch(typeof a){case "number":return v(Number,"isFinite").call(Number,a)?a:""+a;case "bigint":return Ib(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[F]|0;return a.length===0&&b&1?void 0:sc(a,b,uc)}if(a!=null&&a[hb]===qb)return K(a);return}return a}var vc=typeof structuredClone!="undefined"?structuredClone:function(a){return sc(a,0,uc)},tc;function K(a){a=a.i;return sc(a,a[F]|0,uc)};function L(a,b,c){var d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("narr");e=a[F]|0;2048&e&&!(2&e)&&wc();if(e&256)throw Error("farr");if(e&64)return d!==0||e&2048||G(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("mid");a:{c=a;e|=64;var f=c.length;if(f){var g=f-1,h=c[g];if(h!=null&&typeof h==="object"&&h.constructor===Object){b=e&128?0:-1;g-=b;if(g>=1024)throw Error("pvtlmt");for(var k in h)Object.prototype.hasOwnProperty.call(h,k)&&(f=+k,f<g&&(c[f+b]=h[k],delete h[k]));e=e&-8380417|(g&1023)<<13;break a}}if(b){k=Math.max(b,f-(e&128?0:-1));if(k>1024)throw Error("spvt");e=e&-8380417|(k&1023)<<13}}}e|=64;d===0&&(e|=2048);G(a,e);return a}function wc(){if(gb!=null){var a;var b=(a=Va)!=null?a:Va={};a=b[gb]||0;a>=5||(b[gb]=a+1,b=Error(),Ua(b,"incident"),Wa?Ya(b):ya(b))}};function xc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[F]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=yc(a,c,!1,b&&!(c&16)):(mb(a,34),c&4&&Object.freeze(a)));return a}if(a!=null&&a[hb]===qb)return b=a.i,c=b[F]|0,H(a,c)?a:zc(a,b,c)?Ac(a,b):yc(b,c)}function Ac(a,b,c){a=new a.constructor(b);c&&(a.g=rb);a.j=rb;return a}function yc(a,b,c,d){d!=null||(d=!!(34&b));a=sc(a,b,xc,d);d=32;c&&(d|=2);b=b&8380609|d;G(a,b);return a}function Bc(a){var b=a.i,c=b[F]|0;return H(a,c)?zc(a,b,c)?Ac(a,b,!0):new a.constructor(yc(b,c,!1)):a}function Cc(a){if(a.g!==rb)return!1;var b=a.i;b=yc(b,b[F]|0);mb(b,2048);a.i=b;a.g=void 0;a.j=void 0;return!0}function Dc(a){if(!Cc(a)&&H(a,a.i[F]|0))throw Error();}function Ec(a,b){b===void 0&&(b=a[F]|0);b&32&&!(b&4096)&&G(a,b|4096)}function zc(a,b,c){return c&2?!0:c&32&&!(c&4096)?(G(b,c|2),a.g=rb,!0):!1};var Fc=Cb(0),M=function(a,b,c,d){a=Gc(a.i,b,c,d);if(a!==null)return a},Gc=function(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),f=a.length-1;if(!(f<1+(c?0:-1))){if(e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object){c=g[b];var h=!0}else if(e===f)c=g;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!v(Object,"is").call(Object,d,c))return h?g[b]=d:a[e]=d,d}return c}},O=function(a,b,c){Dc(a);var d=a.i;N(d,d[F]|0,b,c);return a};function N(a,b,c,d){var e=c+-1,f=a.length-1;if(f>=0&&e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;if(d!==void 0){var h;f=((h=b)!=null?h:b=a[F]|0)>>13&1023||536870912;c>=f?d!=null&&(e={},a[f+-1]=(e[c]=d,e)):a[e]=d}return b}var Ic=function(a,b,c){a=a.i;return Hc(a,a[F]|0,b,c)!==void 0},P=function(a){return a===tb?2:4};function Jc(a,b,c,d,e){var f=a.i,g=f[F]|0;d=H(a,g)?1:d;e=!!e||d===3;d===2&&Cc(a)&&(f=a.i,g=f[F]|0);a=Gc(f,b);a=Array.isArray(a)?a:kb;var h=a===kb?7:a[F]|0,k=Kc(h,g);var m=4&k?!1:!0;if(m){4&k&&(a=Pb(a),h=0,k=Lc(k,g),g=N(f,g,b,a));for(var n=0,l=0;n<a.length;n++){var p=c(a[n]);p!=null&&(a[l++]=p)}l<n&&(a.length=l);c=(k|4)&-513;k=c&=-1025;k&=-4097}k!==h&&(G(a,k),2&k&&Object.freeze(a));return a=Mc(a,k,f,g,b,d,m,e)}function Mc(a,b,c,d,e,f,g,h){var k=b;f===1||(f!==4?0:2&b||!(16&b)&&32&d)?Nc(b)||(b|=!a.length||g&&!(4096&b)||32&d&&!(4096&b||16&b)?2:256,b!==k&&G(a,b),Object.freeze(a)):(f===2&&Nc(b)&&(a=Pb(a),k=0,b=Lc(b,d),d=N(c,d,e,a)),Nc(b)||(h||(b|=16),b!==k&&G(a,b)));2&b||!(4096&b||16&b)||Ec(c,d);return a}function Kc(a,b){2&b&&(a|=2);return a|1}function Nc(a){return!!(2&a)&&!!(4&a)||!!(256&a)}function Oc(a,b,c,d){Dc(a);var e=a.i,f=e[F]|0;if(c==null)return N(e,f,b),a;var g=c===kb?7:c[F]|0,h=g,k=Nc(g),m=k||Object.isFrozen(c);k||(g=0);m||(c=Pb(c),h=0,g=Lc(g,f),m=!1);g|=5;var n;k=(n=nb(g))!=null?n:0;for(n=0;n<c.length;n++){var l=c[n],p=d(l,k);v(Object,"is").call(Object,l,p)||(m&&(c=Pb(c),h=0,g=Lc(g,f),m=!1),c[n]=p)}g!==h&&(m&&(c=Pb(c),g=Lc(g,f)),G(c,g));N(e,f,b,c);return a}function Pc(a,b,c,d){Dc(a);var e=a.i;N(e,e[F]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a}var Tc=function(a,b,c,d){Dc(a);var e=a.i,f=e[F]|0;if(d==null){var g=Qc(e);if(Rc(g,e,f,c)===b)g.set(c,0);else return a}else f=Sc(e,f,c,b);N(e,f,b,d);return a},Vc=function(a,b,c){return Uc(a,b)===c?c:-1},Uc=function(a,b){a=a.i;return Rc(Qc(a),a,void 0,b)};function Qc(a){if($a){var b;return(b=a[fb])!=null?b:a[fb]=new u.Map}if(fb in a)return a[fb];b=new u.Map;Object.defineProperty(a,fb,{value:b});return b}function Sc(a,b,c,d){var e=Qc(a),f=Rc(e,a,b,c);f!==d&&(f&&(b=N(a,b,f)),e.set(c,d));return b}function Rc(a,b,c,d){var e=a.get(d);if(e!=null)return e;for(var f=e=0;f<d.length;f++){var g=d[f];Gc(b,g)!=null&&(e!==0&&(c=N(b,c,e)),e=g)}a.set(d,e);return e}var Wc=function(a,b,c){Dc(a);a=a.i;var d=a[F]|0,e=Gc(a,c),f=void 0===ub;b=qc(e,b,!f,d);if(!f||b)return b=Bc(b),e!==b&&(d=N(a,d,c,b),Ec(a,d)),b};function Hc(a,b,c,d){var e=!1;d=Gc(a,d,void 0,function(f){var g=qc(f,c,!1,b);e=g!==f&&g!=null;return g});if(d!=null)return e&&!H(d)&&Ec(a,b),d}var Xc=function(a,b,c){a=a.i;(c=Hc(a,a[F]|0,b,c))||(c=b[eb])||(c=new b,mb(c.i,34),c=b[eb]=c);return c},Q=function(a,b,c){var d=a.i,e=d[F]|0;b=Hc(d,e,b,c);if(b==null)return b;e=d[F]|0;if(!H(a,e)){var f=Bc(b);f!==b&&(Cc(a)&&(d=a.i,e=d[F]|0),b=f,e=N(d,e,c,b),Ec(d,e))}return b};function Yc(a,b,c,d,e,f,g,h){var k=H(a,c);f=k?1:f;g=!!g||f===3;k=h&&!k;(f===2||k)&&Cc(a)&&(b=a.i,c=b[F]|0);a=Gc(b,e);a=Array.isArray(a)?a:kb;var m=a===kb?7:a[F]|0,n=Kc(m,c);if(h=!(4&n)){var l=a,p=c,r=!!(2&n);r&&(p|=2);for(var t=!r,z=!0,A=0,pa=0;A<l.length;A++){var cb=qc(l[A],d,!1,p);if(cb instanceof d){if(!r){var db=H(cb);t&&(t=!db);z&&(z=db)}l[pa++]=cb}}pa<A&&(l.length=pa);n|=4;n=z?n&-4097:n|4096;n=t?n|8:n&-9}n!==m&&(G(a,n),2&n&&Object.freeze(a));if(k&&!(8&n||!a.length&&(f===1||(f!==4?0:2&n||!(16&n)&&32&c)))){Nc(n)&&(a=Pb(a),n=Lc(n,c),c=N(b,c,e,a));d=a;k=n;for(m=0;m<d.length;m++)l=d[m],n=Bc(l),l!==n&&(d[m]=n);k|=8;n=k=d.length?k|4096:k&-4097;G(a,n)}return a=Mc(a,n,b,c,e,f,h,g)}var R=function(a,b,c,d){var e=a.i;return Yc(a,e,e[F]|0,b,c,d,!1,!0)};function Zc(a){a==null&&(a=void 0);return a}var $c=function(a,b,c){c=Zc(c);O(a,b,c);c&&!H(c)&&Ec(a.i);return a},ad=function(a,b,c,d){d=Zc(d);Tc(a,b,c,d);d&&!H(d)&&Ec(a.i);return a},bd=function(a,b,c){Dc(a);var d=a.i,e=d[F]|0;if(c==null)return N(d,e,b),a;for(var f=c===kb?7:c[F]|0,g=f,h=Nc(f),k=h||Object.isFrozen(c),m=!0,n=!0,l=0;l<c.length;l++){var p=c[l];h||(p=H(p),m&&(m=!p),n&&(n=p))}h||(f=m?13:5,f=n?f&-4097:f|4096);k&&f===g||(c=Pb(c),g=0,f=Lc(f,e));f!==g&&G(c,f);e=N(d,e,b,c);2&f||!(4096&f||16&f)||Ec(d,e);return a};function Lc(a,b){return a=(2&b?a|2:a&-3)&-273}function cd(a,b){Dc(a);a=Jc(a,4,pc,2,!0);var c,d=(c=nb(a===kb?7:a[F]|0))!=null?c:0;if(Array.isArray(b)){c=b.length;for(var e=0;e<c;e++)a.push(nc(b[e],d))}else for(b=y(b),c=b.next();!c.done;c=b.next())a.push(nc(c.value,d))}var dd=function(a,b){var c=c===void 0?!1:c;a=M(a,b);a=a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0;return a!=null?a:c},ed=function(a,b){var c=c===void 0?0:c;a=bc(M(a,b));return a!=null?a:c},fd=function(a,b){var c=c===void 0?0:c;a=cc(M(a,b));return a!=null?a:c},gd=function(a,b){var c=c===void 0?Fc:c;a=M(a,b);b=typeof a;a=a==null?a:b==="bigint"?Cb(Rb(64,a)):Yb(a)?b==="string"?hc(a):ic(a):void 0;return a!=null?a:c},hd=function(a,b){var c=c===void 0?0:c;a=M(a,b,void 0,Vb);return a!=null?a:c},S=function(a,b){var c=c===void 0?"":c;var d;return(d=pc(M(a,b)))!=null?d:c},T=function(a,b){var c=c===void 0?0:c;a=$b(M(a,b));return a!=null?a:c},id=function(a,b,c){a=Jc(a,b,bc,3,!0);sb(a,c);return a[c]},jd=function(a,b,c){return T(a,Vc(a,c,b))},kd=function(a,b,c){return Pc(a,b,c==null?c:ac(c),0)},ld=function(a,b,c){return Pc(a,b,kc(c),"0")},md=function(a,b,c){return Pc(a,b,oc(c),"")},nd=function(a,b,c){return O(a,b,c==null?c:Zb(c))},od=function(a,b,c){return Pc(a,b,c==null?c:Zb(c),0)},pd=function(a,b,c,d){return Tc(a,b,c,d==null?d:Zb(d))};var U=function(a,b,c){this.i=L(a,b,c)};U.prototype.toJSON=function(){return K(this)};var qd=function(a){var b=a.i,c=b[F]|0;return H(a,c)?a:zc(a,b,c)?Ac(a,b):new a.constructor(yc(b,c,!0))};U.prototype[hb]=qb;function rd(a,b){if(b==null)return new a;if(!Array.isArray(b))throw Error();if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error();return new a(ob(b))};function sd(a){return function(b){if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b=new a(ob(b))}return b}};var td=function(a){this.i=L(a)};x(td,U);var ud=function(a){return S(a,1)};var vd=function(a){this.i=L(a)};x(vd,U);var wd,xd=64;function yd(){try{return wd!=null||(wd=new Uint32Array(64)),xd>=64&&(crypto.getRandomValues(wd),xd=0),wd[xd++]}catch(a){return Math.floor(Math.random()*4294967296)}};function zd(a,b){if(!yb(a.goog_pvsid))try{var c=yd()+(yd()&2097151)*4294967296;Object.defineProperty(a,"goog_pvsid",{value:c,configurable:!1})}catch(d){b.G({methodName:784,I:d})}a=Number(a.goog_pvsid);(!a||a<=0)&&b.G({methodName:784,I:Error("Invalid correlator, "+a)});return a||-1};var Ad=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}};var Bd=function(){return Ca&&Fa?!Fa.mobile&&(E("iPad")||E("Android")||E("Silk")):E("iPad")||E("Android")&&!E("Mobile")||E("Silk")};function Cd(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var Dd;function Ed(){Dd===void 0&&(Dd=null);return Dd};var Fd=function(a){this.g=a};Fd.prototype.toString=function(){return this.g+""};function Gd(a){var b=Ed();a=b?b.createScriptURL(a):a;return new Fd(a)}function Hd(a){if(a instanceof Fd)return a.g;throw Error("");};var Id=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;var Jd=function(a){this.g=a};Jd.prototype.toString=function(){return this.g+""};function Kd(a){a=a===void 0?document:a;var b,c;a=(c=(b=a).querySelector)==null?void 0:c.call(b,"script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};function Ld(a,b){a.src=Hd(b);(b=Kd(a.ownerDocument))&&a.setAttribute("nonce",b)};var Md="alternate author bookmark canonical cite help icon license modulepreload next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" ");function Nd(a,b){var c=a.write;if(b instanceof Jd)b=b.g;else throw Error("");c.call(a,b)};function Od(a,b){a=Hd(a).toString();a='<script src="'+Pd(a)+'"';if(b==null?0:b.async)a+=" async";(b==null?void 0:b.attributionSrc)!==void 0&&(a+=' attributionsrc="'+Pd(b.attributionSrc)+'"');if(b==null?0:b.Va)a+=' custom-element="'+Pd(b.Va)+'"';if(b==null?0:b.defer)a+=" defer";if(b==null?0:b.id)a+=' id="'+Pd(b.id)+'"';if(b==null?0:b.nonce)a+=' nonce="'+Pd(b.nonce)+'"';if(b==null?0:b.type)a+=' type="'+Pd(b.type)+'"';if(b==null?0:b.Fa)a+=' crossorigin="'+Pd(b.Fa)+'"';b=a+">\x3c/script>";b=(a=Ed())?a.createHTML(b):b;return new Jd(b)}function Pd(a){return a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")};function Qd(a){var b=sa.apply(1,arguments);if(b.length===0)return Gd(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return Gd(c)}function Rd(a,b){a=Hd(a).toString();var c=a.split(/[?#]/),d=/[?]/.test(a)?"?"+c[1]:"";return Sd(c[0],d,/[#]/.test(a)?"#"+(d?c[2]:c[1]):"",b)}function Sd(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(function(k){return e(k,h)}):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}var f=b.length?"&":"?";d.constructor===Object&&(d=v(Object,"entries").call(Object,d));Array.isArray(d)?d.forEach(function(g){return e(g[1],g[0])}):d.forEach(e);return Gd(a+b+c)};var Td=function(a){var b=b===void 0?!1:b;var c=c===void 0?D:c;for(var d=0;c&&d++<40;){var e;if(!(e=b))try{var f;if(f=!!c&&c.location.href!=null)b:{try{Pa(c.foo);f=!0;break b}catch(h){}f=!1}e=f}catch(h){e=!1}if(e&&a(c))break;a:{try{var g=c.parent;if(g&&g!=c){c=g;break a}}catch(h){}c=null}}},Ud=function(a){var b=a;Td(function(c){b=c;return!1});return b},Vd=function(){if(!u.globalThis.crypto)return Math.random();try{var a=new Uint32Array(1);u.globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch(b){return Math.random()}},Wd=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Xd=Ad(function(){return(Ca&&Fa?Fa.mobile:!Bd()&&(E("iPod")||E("iPhone")||E("Android")||E("IEMobile")))?2:Bd()?1:0});function Yd(a,b){if(a.length&&b.head){a=y(a);for(var c=a.next();!c.done;c=a.next())if((c=c.value)&&b.head){var d=Zd("META");b.head.appendChild(d);d.httpEquiv="origin-trial";d.content=c}}}var $d=function(a){return zd(a,{G:function(){}})},Zd=function(a,b){b=b===void 0?document:b;return b.createElement(String(a).toLowerCase())};var ae={Eb:0,Db:1,Ab:2,ub:3,Bb:4,wb:5,Cb:6,yb:7,zb:8,tb:9,xb:10,Fb:11};var be={Hb:0,Ib:1,Gb:2};var ce=function(a){this.i=L(a)};x(ce,U);ce.prototype.getVersion=function(){return ed(this,2)};function de(a){return Sa(a.length%4!==0?a+"A":a).map(function(b){return(q=b.toString(2),v(q,"padStart")).call(q,8,"0")}).join("")}function ee(a){if(!/^[0-1]+$/.test(a))throw Error("Invalid input ["+a+"] not a bit string.");return parseInt(a,2)}function fe(a){if(!/^[0-1]+$/.test(a))throw Error("Invalid input ["+a+"] not a bit string.");for(var b=[1,2,3,5],c=0,d=0;d<a.length-1;d++)b.length<=d&&b.push(b[d-1]+b[d-2]),c+=parseInt(a[d],2)*b[d];return c};function ge(a){var b=de(a),c=ee(b.slice(0,6));a=ee(b.slice(6,12));var d=new ce;c=kd(d,1,c);a=kd(c,2,a);b=b.slice(12);c=ee(b.slice(0,12));d=[];for(var e=b.slice(12).replace(/0+$/,""),f=0;f<c;f++){if(e.length===0)throw Error("Found "+f+" of "+c+" sections ["+d+"] but reached end of input ["+b+"]");var g=ee(e[0])===0;e=e.slice(1);var h=he(e,b),k=d.length===0?0:d[d.length-1];k=fe(h)+k;e=e.slice(h.length);if(g)d.push(k);else{g=he(e,b);h=fe(g);for(var m=0;m<=h;m++)d.push(k+m);e=e.slice(g.length)}}if(e.length>0)throw Error("Found "+c+" sections ["+d+"] but has remaining input ["+e+"], entire input ["+b+"]");return Oc(a,3,d,ac)}function he(a,b){var c=a.indexOf("11");if(c===-1)throw Error("Expected section bitstring but not found in ["+a+"] part of ["+b+"]");return a.slice(0,c+2)};var ie="a".charCodeAt(),je=Cd(ae),ke=Cd(be);var le=function(a){this.i=L(a)};x(le,U);var me=function(){var a=new le;return ld(a,1,0)},ne=function(a){var b=Number;var c=c===void 0?"0":c;var d=M(a,1);var e=!0;e=e===void 0?!1:e;var f=typeof d;d=d==null?d:f==="bigint"?String(Rb(64,d)):Yb(d)?f==="string"?ec(d):e?fc(d):jc(d):void 0;b=b(d!=null?d:c);a=ed(a,2);return new Date(b*1E3+a/1E6)};var oe=function(a){if(/[^01]/.test(a))throw Error("Input bitstring "+a+" is malformed!");this.j=a;this.g=0},re=function(a){var b=V(a,16);return!!V(a,1)===!0?(a=pe(a),a.forEach(function(c){if(c>b)throw Error("ID "+c+" is past MaxVendorId "+b+"!");}),a):qe(a,b)},pe=function(a){for(var b=V(a,12),c=[];b--;){var d=!!V(a,1)===!0,e=V(a,16);if(d)for(d=V(a,16);e<=d;e++)c.push(e);else c.push(e)}c.sort(function(f,g){return f-g});return c},qe=function(a,b,c){for(var d=[],e=0;e<b;e++)if(V(a,1)){var f=e+1;if(c&&c.indexOf(f)===-1)throw Error("ID: "+f+" is outside of allowed values!");d.push(f)}return d},V=function(a,b){if(a.g+b>a.j.length)throw Error("Requested length "+b+" is past end of string.");var c=a.j.substring(a.g,a.g+b);a.g+=b;return parseInt(c,2)};oe.prototype.skip=function(a){this.g+=a};var te=function(a){try{var b=Sa(a.split(".")[0]).map(function(d){return(q=d.toString(2),v(q,"padStart")).call(q,8,"0")}).join(""),c=new oe(b);b={};b.tcString=a;b.gdprApplies=!0;c.skip(78);b.cmpId=V(c,12);b.cmpVersion=V(c,12);c.skip(30);b.tcfPolicyVersion=V(c,6);b.isServiceSpecific=!!V(c,1);b.useNonStandardStacks=!!V(c,1);b.specialFeatureOptins=se(qe(c,12,ke),ke);b.purpose={consents:se(qe(c,24,je),je),legitimateInterests:se(qe(c,24,je),je)};b.purposeOneTreatment=!!V(c,1);b.publisherCC=String.fromCharCode(ie+V(c,6))+String.fromCharCode(ie+V(c,6));b.vendor={consents:se(re(c),null),legitimateInterests:se(re(c),null)};return b}catch(d){return null}},se=function(a,b){var c={};if(Array.isArray(b)&&b.length!==0){b=y(b);for(var d=b.next();!d.done;d=b.next())d=d.value,c[d]=a.indexOf(d)!==-1}else for(a=y(a),b=a.next();!b.done;b=a.next())c[b.value]=!0;delete c[0];return c};var ue=function(a){this.i=L(a)};x(ue,U);var ve=function(a,b){var c=c===void 0?{}:c;this.error=a;this.meta=c;this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"};function we(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Zd("IMG",a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=Array.prototype.indexOf.call(g,e,void 0);h>=0&&Array.prototype.splice.call(g,h,1)}typeof e.removeEventListener==="function"&&e.removeEventListener("load",f,!1);typeof e.removeEventListener==="function"&&e.removeEventListener("error",f,!1)};typeof e.addEventListener==="function"&&e.addEventListener("load",f,!1);typeof e.addEventListener==="function"&&e.addEventListener("error",f,!1)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}function xe(a){var b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=rcs_internal";Wd(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});ye(c,b)}function ye(a,b){var c=window;b=b===void 0?!1:b;var d=d===void 0?!1:d;c.fetch?(b={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"},d&&(b.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?b.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:b.headers={"Attribution-Reporting-Eligible":"event-source"}),c.fetch(a,b)):we(c,a,b===void 0?!1:b,d===void 0?!1:d)};function ze(a,b){try{var c=function(d){var e={};return[(e[d.aa]=d.X,e)]};return JSON.stringify([a.filter(function(d){return d.N}).map(c),K(b),a.filter(function(d){return!d.N}).map(c)])}catch(d){return Ae(d,b),""}}function Ae(a,b){try{var c=a instanceof Error?a:Error(String(a)),d=c.toString();c.name&&d.indexOf(c.name)==-1&&(d+=": "+c.name);c.message&&d.indexOf(c.message)==-1&&(d+=": "+c.message);if(c.stack)a:{var e=c.stack;a=d;try{e.indexOf(a)==-1&&(e=a+"\n"+e);for(var f;e!=f;)f=e,e=e.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");d=e.replace(RegExp("\n *","g"),"\n");break a}catch(g){d=a;break a}d=void 0}xe({m:d,b:T(b,1)||null,v:S(b,2)||null})}catch(g){}}var Be=function(a,b){var c=new ue;a=od(c,1,a);b=md(a,2,b);this.o=qd(b)};var Ce=function(a){this.i=L(a)};x(Ce,U);var Ee=function(a,b){return Tc(a,3,De,b==null?b:Wb(b))},De=[1,2,3];var Fe=function(a){this.i=L(a)};x(Fe,U);var He=function(a,b){return Tc(a,2,Ge,kc(b))},Ge=[2,4];var Ie=function(a){this.i=L(a)};x(Ie,U);var Je=function(a){var b=new Ie;return md(b,1,a)},Ke=function(a,b){return $c(a,3,b)},Le=function(a,b){var c=b;Dc(a);b=a.i;var d=Yc(a,b,b[F]|0,Ce,4,2,!0);c=c!=null?c:new Ce;d.push(c);var e=d===kb?7:d[F]|0,f=e;(c=H(c))?(e&=-9,d.length===1&&(e&=-4097)):e|=4096;e!==f&&G(d,e);c||Ec(b);return a};var Me=function(a){this.i=L(a)};x(Me,U);var Ne=function(a){this.i=L(a)};x(Ne,U);var Oe=function(a,b){return od(a,1,b)},Pe=function(a,b){return od(a,2,b)};var Qe=function(a){this.i=L(a)};x(Qe,U);var Re=[1,2];var Se=function(a){this.i=L(a)};x(Se,U);var Te=function(a,b){return $c(a,1,b)},Ue=function(a,b){return bd(a,2,b)},Ve=function(a,b){return Oc(a,4,b,ac)},We=function(a,b){return bd(a,5,b)},Xe=function(a,b){return od(a,6,b)};var Ye=function(a){this.i=L(a)};x(Ye,U);var Ze=[1,2,3,4,6];var $e=function(a){this.i=L(a)};x($e,U);var af=function(a){this.i=L(a)};x(af,U);var bf=[2,3,4];var cf=function(a){this.i=L(a)};x(cf,U);var df=[3,4,5],ef=[6,7];var ff=function(a){this.i=L(a)};x(ff,U);var gf=[4,5];var hf=function(a){this.i=L(a)};x(hf,U);hf.prototype.getTagSessionCorrelator=function(){return gd(this,2)};var kf=function(a){var b=new hf;return ad(b,4,jf,a)},jf=[4,5,7,8,9];var lf=function(a){this.i=L(a)};x(lf,U);var mf=function(a){this.i=L(a)};x(mf,U);var nf=[1,2,4,5,6,9,10];var of=function(a){this.i=L(a)};x(of,U);of.prototype.getTagSessionCorrelator=function(){return gd(this,2)};of.prototype.ca=function(a){return id(this,4,a)};var pf=function(a){this.i=L(a)};x(pf,U);pf.prototype.ab=function(){return ed(this,2)};pf.prototype.Za=function(a){var b=Jc(this,3,pc,3,!0);sb(b,a);return b[a]};var qf=function(a){this.i=L(a)};x(qf,U);var rf=function(a){this.i=L(a)};x(rf,U);rf.prototype.getTagSessionCorrelator=function(){return gd(this,1)};rf.prototype.ca=function(a){return id(this,2,a)};var sf=function(a){this.i=L(a)};x(sf,U);var tf=[1,7],uf=[4,6,8];var wf=function(a){this.g=a;this.Ra=new vf(this.g)},vf=function(a){this.g=a;this.Ha=new xf(this.g)},xf=function(a){this.g=a;this.outstream=new yf;this.request=new zf;this.threadYield=new Af;this.cb=new Bf(this.g);this.gb=new Cf(this.g);this.nb=new Df(this.g)},Bf=function(a){this.g=a};Bf.prototype.W=function(a){this.g.C(Ke(Le(Le(Je("JwITQ"),Ee(new Ce,a.la)),Ee(new Ce,a.na)),He(new Fe,Math.round(a.Z))))};var Cf=function(a){this.g=a};Cf.prototype.W=function(a){this.g.C(Ke(Le(Le(Je("Pn3Upd"),Ee(new Ce,a.la)),Ee(new Ce,a.na)),He(new Fe,Math.round(a.Z))))};var Df=function(a){this.g=a};Df.prototype.W=function(a){var b=this.g,c=b.C,d=Je("rkgGzc");var e=new Ce;e=Tc(e,2,De,kc(a.source));d=Le(d,e);e=new Ce;e=Tc(e,2,De,kc(a.Ua));c.call(b,Ke(Le(d,e),He(new Fe,Math.round(a.Z))))};var yf=function(){},zf=function(){},Af=function(){},Ef=function(){Be.apply(this,arguments);this.Ka=new wf(this)};x(Ef,Be);var Ff=function(){Ef.apply(this,arguments)};x(Ff,Ef);Ff.prototype.rb=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{N:!0,aa:2,X:K(a)}})))};Ff.prototype.qb=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{N:!0,aa:29,X:K(a)}})))};Ff.prototype.ea=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{N:!0,aa:4,X:K(a)}})))};Ff.prototype.sb=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{N:!0,aa:15,X:K(a)}})))};Ff.prototype.C=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{N:!1,aa:1,X:K(a)}})))};function Gf(a,b){if(u.globalThis.fetch)u.globalThis.fetch(a,{method:"POST",body:b,keepalive:b.length<65536,credentials:"omit",mode:"no-cors",redirect:"follow"}).catch(function(){});else{var c=new XMLHttpRequest;c.open("POST",a,!0);c.send(b)}};var Hf=function(a,b,c,d,e,f,g,h){Ff.call(this,a,b);this.T=c;this.S=d;this.U=e;this.P=f;this.R=g;this.J=h;this.g=[];this.j=null;this.L=!1};x(Hf,Ff);var If=function(a){a.j!==null&&(clearTimeout(a.j),a.j=null);if(a.g.length){var b=ze(a.g,a.o);a.S(a.T+"?e=1",b);a.g=[]}};Hf.prototype.l=function(){var a=sa.apply(0,arguments),b=this;try{this.R&&ze(this.g.concat(a),this.o).length>=65536&&If(this),this.J&&!this.L&&(this.L=!0,this.J.g(function(){If(b)})),this.g.push.apply(this.g,B(a)),this.g.length>=this.P&&If(this),this.g.length&&this.j===null&&(this.j=setTimeout(function(){If(b)},this.U))}catch(c){Ae(c,this.o)}};var Jf=function(a,b,c,d,e,f){Hf.call(this,a,b,"https://pagead2.googlesyndication.com/pagead/ping",Gf,c===void 0?1E3:c,d===void 0?100:d,(e===void 0?!1:e)&&!!u.globalThis.fetch,f)};x(Jf,Hf);var Kf=function(a){this.g=a;this.defaultValue=!1},Lf=function(a,b){this.g=a;this.defaultValue=b===void 0?0:b};var Mf=new Lf(695925491,20),Nf=new Kf(45624259),Of=new Lf(635239304,100),Pf=new Kf(662101539),Qf=new Lf(682056200,100),Rf=new Lf(24),Sf=new function(a,b){b=b===void 0?[]:b;this.g=a;this.defaultValue=b}(1934,["AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==","Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==","A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9","A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"]);var Tf=function(a){this.i=L(a)};x(Tf,U);var Uf=function(a){this.i=L(a)};x(Uf,U);var Vf=function(a){this.i=L(a)};x(Vf,U);var Wf=function(a){this.i=L(a)};x(Wf,U);var Xf=sd(Wf);var Yf=function(a){this.g=a||{cookie:""}};Yf.prototype.set=function(a,b,c){var d=!1;if(typeof c==="object"){var e=c.Lb;d=c.Mb||!1;var f=c.domain||void 0;var g=c.path||void 0;var h=c.ib}if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');h===void 0&&(h=-1);this.g.cookie=a+"="+b+(f?";domain="+f:"")+(g?";path="+g:"")+(h<0?"":h==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+h*1E3)).toUTCString())+(d?";secure":"")+(e!=null?";samesite="+e:"")};Yf.prototype.get=function(a,b){for(var c=a+"=",d=(this.g.cookie||"").split(";"),e=0,f;e<d.length;e++){f=za(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};Yf.prototype.isEmpty=function(){return!this.g.cookie};Yf.prototype.clear=function(){for(var a=(this.g.cookie||"").split(";"),b=[],c=[],d,e,f=0;f<a.length;f++)e=za(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));for(a=b.length-1;a>=0;a--)c=b[a],this.get(c),this.set(c,"",{ib:0,path:void 0,domain:void 0})};function Zf(a){a=$f(a);try{var b=a?Xf(a):null}catch(c){b=null}return b?Q(b,Vf,4)||null:null}function $f(a){a=(new Yf(a)).get("FCCDCF","");if(a)if(v(a,"startsWith").call(a,"%"))try{var b=decodeURIComponent(a)}catch(c){b=null}else b=a;else b=null;return b};Cd(ae).map(function(a){return Number(a)});Cd(be).map(function(a){return Number(a)});var ag=function(a){this.g=a},cg=function(a){a.__tcfapiPostMessageReady||bg(new ag(a))},bg=function(a){a.j=function(b){var c=typeof b.data==="string";try{var d=c?JSON.parse(b.data):b.data}catch(f){return}var e=d.__tcfapiCall;e&&(e.command==="ping"||e.command==="addEventListener"||e.command==="removeEventListener")&&(0,a.g.__tcfapi)(e.command,e.version,function(f,g){var h={};h.__tcfapiReturn=e.command==="removeEventListener"?{success:f,callId:e.callId}:{returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f,b.origin);return f},e.parameter)};a.g.addEventListener("message",a.j);a.g.__tcfapiPostMessageReady=!0};var dg=function(a){this.g=a;this.j=null},fg=function(a){a.__uspapiPostMessageReady||eg(new dg(a))},eg=function(a){a.j=function(b){var c=typeof b.data==="string";try{var d=c?JSON.parse(b.data):b.data}catch(f){return}var e=d.__uspapiCall;e&&e.command==="getUSPData"&&a.g.__uspapi(e.command,e.version,function(f,g){var h={};h.__uspapiReturn={returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f,b.origin);return f})};a.g.addEventListener("message",a.j);a.g.__uspapiPostMessageReady=!0};var gg=function(a){this.i=L(a)};x(gg,U);var hg=function(a){this.i=L(a)};x(hg,U);var ig=sd(hg);function jg(a,b){function c(l){if(l.length<10)return null;var p=h(l.slice(0,4));p=k(p);l=h(l.slice(6,10));l=m(l);return"1"+p+l+"N"}function d(l){if(l.length<10)return null;var p=h(l.slice(0,6));p=k(p);l=h(l.slice(6,10));l=m(l);return"1"+p+l+"N"}function e(l){if(l.length<12)return null;var p=h(l.slice(0,6));p=k(p);l=h(l.slice(8,12));l=m(l);return"1"+p+l+"N"}function f(l){if(l.length<18)return null;var p=h(l.slice(0,8));p=k(p);l=h(l.slice(12,18));l=m(l);return"1"+p+l+"N"}function g(l){if(l.length<10)return null;var p=h(l.slice(0,6));p=k(p);l=h(l.slice(6,10));l=m(l);return"1"+p+l+"N"}function h(l){for(var p=[],r=0,t=0;t<l.length/2;t++)p.push(ee(l.slice(r,r+2))),r+=2;return p}function k(l){return l.every(function(p){return p===1})?"Y":"N"}function m(l){return l.some(function(p){return p===1})?"Y":"N"}if(a.length===0)return null;a=a.split(".");if(a.length>2)return null;a=de(a[0]);var n=ee(a.slice(0,6));a=a.slice(6);if(n!==1)return null;switch(b){case 8:return c(a);case 10:case 12:case 9:return d(a);case 11:return e(a);case 7:return f(a);case 13:return g(a);default:return null}};function kg(a,b){var c=a.document,d=function(){if(!a.frames[b])if(c.body){var e=Zd("IFRAME",c);e.style.display="none";e.style.width="0px";e.style.height="0px";e.style.border="none";e.style.zIndex="-1000";e.style.left="-1000px";e.style.top="-1000px";e.name=b;c.body.appendChild(e)}else a.setTimeout(d,5)};d()};function lg(a){if(a!=null)return mg(a)}function mg(a){return Ib(a)?Number(a):String(a)};var pg=function(a){this.g=a;var b=$f(this.g.document);try{var c=b?Xf(b):null}catch(e){c=null}(b=c)?(c=Q(b,Uf,5)||null,b=R(b,Tf,7,P()),b=ng(b!=null?b:[]),c={Da:c,Ga:b}):c={Da:null,Ga:null};b=c;c=og(b.Ga);b=b.Da;if(b!=null&&pc(M(b,2))!=null&&S(b,2).length!==0){var d=Ic(b,le,1)?Q(b,le,1):me();b={uspString:S(b,2),ia:ne(d)}}else b=null;this.l=b&&c?c.ia>b.ia?c.uspString:b.uspString:b?b.uspString:c?c.uspString:null;this.tcString=(c=Zf(a.document))&&pc(M(c,1))!=null?S(c,1):null;this.j=(a=Zf(a.document))&&pc(M(a,2))!=null?S(a,2):null},sg=function(a){a!==a.top||a.__uspapi||a.frames.__uspapiLocator||(a=new pg(a),qg(a),rg(a))},qg=function(a){!a.l||a.g.__uspapi||a.g.frames.__uspapiLocator||(a.g.__uspapiManager="fc",kg(a.g,"__uspapiLocator"),xa("__uspapi",function(b,c,d){typeof d==="function"&&b==="getUSPData"&&d({version:1,uspString:a.l},!0)},a.g),fg(a.g))},ng=function(a){a=v(a,"find").call(a,function(b){return b&&T(b,1)===13});if(a==null?0:pc(M(a,2))!=null)try{return ig(S(a,2))}catch(b){}return null},og=function(a){if(a==null||pc(M(a,1))==null||S(a,1).length===0||R(a,gg,2,P()).length===0)return null;var b=S(a,1);try{var c=ge(b.split("~")[0]);var d=v(b,"includes").call(b,"~")?b.split("~").slice(1):[]}catch(e){return null}a=R(a,gg,2,P()).reduce(function(e,f){var g=tg(e);g=gd(g,1);g=mg(g);var h=tg(f);h=gd(h,1);return g>mg(h)?e:f});c=Jc(c,3,bc,P()).indexOf(ed(a,1));return c===-1||c>=d.length?null:{uspString:jg(d[c],ed(a,1)),ia:ne(tg(a))}},tg=function(a){return Ic(a,le,2)?Q(a,le,2):me()},rg=function(a){!a.tcString||a.g.__tcfapi||a.g.frames.__tcfapiLocator||(a.g.__tcfapiManager="fc",kg(a.g,"__tcfapiLocator"),a.g.__tcfapiEventListeners=a.g.__tcfapiEventListeners||[],xa("__tcfapi",function(b,c,d,e){if(typeof d==="function")if(c&&(c>2.2||c<=1))d(null,!1);else switch(c=a.g.__tcfapiEventListeners,b){case "ping":d({gdprApplies:!0,cmpLoaded:!0,cmpStatus:"loaded",displayStatus:"disabled",apiVersion:"2.2",cmpVersion:2,cmpId:300});break;case "addEventListener":b=c.push(d)-1;a.tcString?(e=te(a.tcString),e.addtlConsent=a.j!=null?a.j:void 0,e.cmpStatus="loaded",e.eventStatus="tcloaded",b!=null&&(e.listenerId=b),b=e):b=null;d(b,!0);break;case "removeEventListener":e!==void 0&&c[e]?(c[e]=null,d(!0)):d(!1);break;case "getInAppTCData":case "getVendorList":d(null,!1);break;case "getTCData":d(null,!1)}},a.g),cg(a.g))};var ug=qa(["https://pagead2.googlesyndication.com/pagead/managed/dict/","/gpt"]),vg=qa(["https://securepubads.g.doubleclick.net/pagead/managed/dict/","/gpt"]);function wg(a,b,c){try{var d=a.createElement("link"),e,f;if(((e=d.relList)==null?0:(f=e.supports)==null?0:f.call(e,"compression-dictionary"))&&Ma()){if(b instanceof Fd)d.href=Hd(b).toString(),d.rel="compression-dictionary";else{if(Md.indexOf("compression-dictionary")===-1)throw Error('TrustedResourceUrl href attribute required with rel="compression-dictionary"');var g=Id.test(b)?b:void 0;g!==void 0&&(d.href=g,d.rel="compression-dictionary")}a.head.appendChild(d)}}catch(h){c.G({methodName:1296,I:h})}}function xg(a,b){return b?Qd(ug,a):Qd(vg,a)};var yg=null;function zg(a,b){var c=R(a,cf,2,P());if(!c.length)return Ag(a,b);a=T(a,1);if(a===1){var d=zg(c[0],b);return d.success?{success:!0,value:!d.value}:d}c=Na(c,function(h){return zg(h,b)});switch(a){case 2:var e;return(e=(d=v(c,"find").call(c,function(h){return h.success&&!h.value}))!=null?d:v(c,"find").call(c,function(h){return!h.success}))!=null?e:{success:!0,value:!0};case 3:var f,g;return(g=(f=v(c,"find").call(c,function(h){return h.success&&h.value}))!=null?f:v(c,"find").call(c,function(h){return!h.success}))!=null?g:{success:!0,value:!1};default:return{success:!1,B:3}}}function Ag(a,b){var c=Uc(a,df);a:{switch(c){case 3:var d=jd(a,3,df);break a;case 4:d=jd(a,4,df);break a;case 5:d=jd(a,5,df);break a}d=void 0}if(!d)return{success:!1,B:2};b=(b=b[c])&&b[d];if(!b)return{success:!1,O:d,Y:c,B:1};try{var e=b.apply;var f=Jc(a,8,pc,P());var g=e.call(b,null,B(f))}catch(h){return{success:!1,O:d,Y:c,B:2}}e=T(a,1);if(e===4)return{success:!0,value:!!g};if(e===5)return{success:!0,value:g!=null};if(e===12)a=S(a,Vc(a,ef,7));else a:{switch(c){case 4:a=hd(a,Vc(a,ef,6));break a;case 5:a=S(a,Vc(a,ef,7));break a}a=void 0}if(a==null)return{success:!1,O:d,Y:c,B:3};if(e===6)return{success:!0,value:g===a};if(e===9)return{success:!0,value:g!=null&&Aa(String(g),a)===0};if(g==null)return{success:!1,O:d,Y:c,B:4};switch(e){case 7:c=g<a;break;case 8:c=g>a;break;case 12:c=zb(a)&&zb(g)&&(new RegExp(a)).test(g);break;case 10:c=g!=null&&Aa(String(g),a)===-1;break;case 11:c=g!=null&&Aa(String(g),a)===1;break;default:return{success:!1,B:3}}return{success:!0,value:c}}function Bg(a,b){return a?b?zg(a,b):{success:!1,B:1}:{success:!0,value:!0}};var Cg=function(a){this.i=L(a)};x(Cg,U);var Dg=function(a){return Jc(a,4,pc,P())};var Eg=function(a){this.i=L(a)};x(Eg,U);Eg.prototype.getValue=function(){return Q(this,Cg,2)};var Fg=function(a){this.i=L(a)};x(Fg,U);var Gg=sd(Fg),Hg=[1,2,3,6,7,8];var Ig=function(a,b,c){var d=d===void 0?new Jf(6,"unknown",b):d;this.C=a;this.o=c;this.j=d;this.g=[];this.l=a>0&&Vd()<1/a},Kg=function(a,b,c,d,e,f){if(a.l){var g=Pe(Oe(new Ne,b),c);b=Xe(Ue(Te(We(Ve(new Se,d),e),g),a.g.slice()),f);b=kf(b);a.j.ea(Jg(a,b));if(f===1||f===3||f===4&&!a.g.some(function(h){return T(h,1)===T(g,1)&&T(h,2)===c}))a.g.push(g),a.g.length>100&&a.g.shift()}},Lg=function(a,b,c,d){if(a.l){var e=new Me;b=O(e,1,b==null?b:ac(b));c=O(b,2,c==null?c:ac(c));d=nd(c,3,d);c=new hf;d=ad(c,8,jf,d);a.j.ea(Jg(a,d))}},Mg=function(a,b,c,d,e){if(a.l){var f=new ff;b=$c(f,1,b);c=nd(b,2,c);d=O(c,3,d==null?d:ac(d));if(e.Y===void 0)pd(d,4,gf,e.B);else switch(e.Y){case 3:c=new af;c=pd(c,2,bf,e.O);e=nd(c,1,e.B);ad(d,5,gf,e);break;case 4:c=new af;c=pd(c,3,bf,e.O);e=nd(c,1,e.B);ad(d,5,gf,e);break;case 5:c=new af,c=pd(c,4,bf,e.O),e=nd(c,1,e.B),ad(d,5,gf,e)}e=new hf;e=ad(e,9,jf,d);a.j.ea(Jg(a,e))}},Jg=function(a,b){var c=Date.now();c=v(Number,"isFinite").call(Number,c)?Math.round(c):0;b=ld(b,1,c);c=$d(window);b=ld(b,2,c);return ld(b,6,a.C)};var W=function(a){var b="ka";if(a.ka&&a.hasOwnProperty(b))return a.ka;b=new a;return a.ka=b};var Ng=function(){var a={};this.A=(a[3]={},a[4]={},a[5]={},a)};var Og=/^true$/.test("false");function Pg(a,b){switch(b){case 1:return jd(a,1,Hg);case 2:return jd(a,2,Hg);case 3:return jd(a,3,Hg);case 6:return jd(a,6,Hg);case 8:return jd(a,8,Hg);default:return null}}function Qg(a,b){if(!a)return null;switch(b){case 1:return dd(a,1);case 7:return S(a,3);case 2:return hd(a,2);case 3:return S(a,3);case 6:return Dg(a);case 8:return Dg(a);default:return null}}var Rg=Ad(function(){if(!Og)return{};try{var a=a===void 0?window:a;try{var b=a.sessionStorage.getItem("GGDFSSK")}catch(c){b=null}if(b)return JSON.parse(b)}catch(c){}return{}});function Sg(a,b,c,d){var e=d=d===void 0?0:d,f,g;W(Tg).l[e]=(g=(f=W(Tg).l[e])==null?void 0:f.add(b))!=null?g:(new u.Set).add(b);e=Rg();if(e[b]!=null)return e[b];b=Ug(d)[b];if(!b)return c;b=Gg(JSON.stringify(b));b=Vg(b);a=Qg(b,a);return a!=null?a:c}function Vg(a){var b=W(Ng).A;if(b&&Uc(a,Hg)!==8){var c=Oa(R(a,Eg,5,P()),function(f){f=Bg(Q(f,cf,1),b);return f.success&&f.value});if(c){var d;return(d=c.getValue())!=null?d:null}}var e;return(e=Q(a,Cg,4))!=null?e:null}var Tg=function(){this.j={};this.o=[];this.l={};this.g=new u.Map};function Wg(a,b,c){return!!Sg(1,a,b===void 0?!1:b,c)}function Xg(a,b,c){b=b===void 0?0:b;a=Number(Sg(2,a,b,c));return isNaN(a)?b:a}function Yg(a,b,c){b=b===void 0?"":b;a=Sg(3,a,b,c);return typeof a==="string"?a:b}function Zg(a,b,c){b=b===void 0?[]:b;a=Sg(6,a,b,c);return Array.isArray(a)?a:b}function $g(a,b,c){b=b===void 0?[]:b;a=Sg(8,a,b,c);return Array.isArray(a)?a:b}function Ug(a){return W(Tg).j[a]||(W(Tg).j[a]={})}function ah(a,b){var c=Ug(b);Wd(a,function(d,e){if(c[e]){d=Gg(JSON.stringify(d));var f=Vc(d,Hg,8);if($b(M(d,f))!=null){var g=Gg(JSON.stringify(c[e]));f=Wc(d,Cg,4);g=Dg(Xc(g,Cg,4));cd(f,g)}c[e]=K(d)}else c[e]=d})}function bh(a,b,c,d,e){e=e===void 0?!1:e;var f=[],g=[];b=y(b);for(var h=b.next();!h.done;h=b.next()){h=h.value;for(var k=Ug(h),m=y(a),n=m.next();!n.done;n=m.next()){n=n.value;var l=Uc(n,Hg),p=Pg(n,l);if(p){var r=void 0,t=void 0,z=void 0;var A=(r=(z=W(Tg).g.get(h))==null?void 0:(t=z.get(p))==null?void 0:t.slice(0))!=null?r:[];a:{r=p;t=l;z=new Ye;switch(t){case 1:pd(z,1,Ze,r);break;case 2:pd(z,2,Ze,r);break;case 3:pd(z,3,Ze,r);break;case 6:pd(z,4,Ze,r);break;case 8:pd(z,6,Ze,r);break;default:A=void 0;break a}Oc(z,5,A,ac);A=z}if(r=A)t=void 0,r=!((t=W(Tg).l[h])==null||!t.has(p));r&&f.push(A);if(l===8&&k[p])A=Gg(JSON.stringify(k[p])),l=Wc(n,Cg,4),A=Dg(Xc(A,Cg,4)),cd(l,A);else{if(l=A)r=void 0,l=!((r=W(Tg).g.get(h))==null||!r.has(p));l&&g.push(A)}e||(l=p,A=h,r=d,t=W(Tg),t.g.has(A)||t.g.set(A,new u.Map),t.g.get(A).has(l)||t.g.get(A).set(l,[]),r&&t.g.get(A).get(l).push(r));k[p]=K(n)}}}if(f.length||g.length)a=d!=null?d:void 0,c.l&&c.o&&(d=new $e,f=bd(d,2,f),g=bd(f,3,g),a&&kd(g,1,a),f=new hf,g=ad(f,7,jf,g),c.j.ea(Jg(c,g)))}function ch(a,b){b=Ug(b);a=y(a);for(var c=a.next();!c.done;c=a.next()){c=c.value;var d=Gg(JSON.stringify(c)),e=Uc(d,Hg);(d=Pg(d,e))&&(b[d]||(b[d]=c))}}function dh(){return v(Object,"keys").call(Object,W(Tg).j).map(function(a){return Number(a)})}function eh(a){(q=W(Tg).o,v(q,"includes")).call(q,a)||ah(Ug(4),a)};function X(a,b,c){c.hasOwnProperty(a)||Object.defineProperty(c,String(a),{value:b})}function Y(a,b,c){return b[a]||c}function fh(a){X(5,Wg,a);X(6,Xg,a);X(7,Yg,a);X(8,Zg,a);X(17,$g,a);X(13,ch,a);X(15,eh,a)}function gh(a){X(4,function(b){W(Ng).A=b},a);X(9,function(b,c){var d=W(Ng);d.A[3][b]==null&&(d.A[3][b]=c)},a);X(10,function(b,c){var d=W(Ng);d.A[4][b]==null&&(d.A[4][b]=c)},a);X(11,function(b,c){var d=W(Ng);d.A[5][b]==null&&(d.A[5][b]=c)},a);X(14,function(b){for(var c=W(Ng),d=y([3,4,5]),e=d.next();!e.done;e=d.next())e=e.value,v(Object,"assign").call(Object,c.A[e],b[e])},a)}function hh(a){a.hasOwnProperty("init-done")||Object.defineProperty(a,"init-done",{value:!0})};var ih=function(){};ih.prototype.g=function(){};ih.prototype.j=function(){};ih.prototype.l=function(){return[]};var jh=function(a,b,c){a.j=function(d,e){Y(2,b,function(){return[]})(d,c,e)};a.l=function(){return Y(3,b,function(){return[]})(c)};a.g=function(d){Y(16,b,function(){})(d,c)}};function kh(a){W(ih).g(a)}function lh(){return W(ih).l()};function mh(a,b){try{var c=a.split(".");a=D;for(var d=0,e;a!=null&&d<c.length;d++)e=a,a=a[c[d]],typeof a==="function"&&(a=e[c[d]]());var f=a;if(typeof f===b)return f}catch(g){}}var nh={},oh={},ph={},qh={},rh=(qh[3]=(nh[8]=function(a){try{return va(a)!=null}catch(b){}},nh[9]=function(a){try{var b=va(a)}catch(c){return}if(a=typeof b==="function")b=b&&b.toString&&b.toString(),a=typeof b==="string"&&b.indexOf("[native code]")!=-1;return a},nh[10]=function(){return window===window.top},nh[6]=function(a){var b=lh();return Array.prototype.indexOf.call(b,Number(a),void 0)>=0},nh[27]=function(a){a=mh(a,"boolean");return a!==void 0?a:void 0},nh[60]=function(a){try{return!!D.document.querySelector(a)}catch(b){}},nh[80]=function(a){try{return!!D.matchMedia(a).matches}catch(b){}},nh[69]=function(a){var b=D.document;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!(q=c.features(),v(q,"includes")).call(q,a))},nh[70]=function(a){var b=D.document;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!(q=c.allowedFeatures(),v(q,"includes")).call(q,a))},nh[79]=function(a){var b=D.navigator;b=b===void 0?navigator:b;try{var c,d;var e=!!((c=b.protectedAudience)==null?0:(d=c.queryFeatureSupport)==null?0:d.call(c,a))}catch(f){e=!1}return e},nh),qh[4]=(oh[3]=function(){return Xd()},oh[6]=function(a){a=mh(a,"number");return a!==void 0?a:void 0},oh),qh[5]=(ph[2]=function(){return window.location.href},ph[3]=function(){try{return window.top.location.hash}catch(a){return""}},ph[4]=function(a){a=mh(a,"string");return a!==void 0?a:void 0},ph[12]=function(a){try{var b=mh(a,"string");if(b!==void 0)return atob(b)}catch(c){}},ph),qh);function sh(){var a=a===void 0?D:a;return a.ggeac||(a.ggeac={})};var th=function(a){this.i=L(a)};x(th,U);th.prototype.getId=function(){return ed(this,1)};var uh=function(a){this.i=L(a)};x(uh,U);var vh=function(a){return R(a,th,2,P())};var wh=function(a){this.i=L(a)};x(wh,U);var xh=function(a){this.i=L(a)};x(xh,U);var yh=function(a){this.i=L(a)};x(yh,U);function zh(a){var b={};return Ah((b[0]=new u.Map,b[1]=new u.Map,b[2]=new u.Map,b),a)}function Ah(a,b){for(var c=new u.Map,d=y(v(a[1],"entries").call(a[1])),e=d.next();!e.done;e=d.next()){var f=y(e.value);e=f.next().value;f=f.next().value;f=f[f.length-1];c.set(e,f.Qa+f.La*f.Ma)}b=y(b);for(d=b.next();!d.done;d=b.next())for(d=d.value,e=R(d,uh,2,P()),e=y(e),f=e.next();!f.done;f=e.next())if(f=f.value,vh(f).length!==0){var g=fd(f,8);if(T(f,4)&&!T(f,13)&&!T(f,14)){var h=void 0;g=(h=c.get(T(f,4)))!=null?h:0;h=fd(f,1)*vh(f).length;c.set(T(f,4),g+h)}h=[];for(var k=0;k<vh(f).length;k++){var m={Qa:g,La:fd(f,1),Ma:vh(f).length,jb:k,ba:T(d,1),fa:f,F:vh(f)[k]};h.push(m)}Bh(a[2],T(f,10),h)||Bh(a[1],T(f,4),h)||Bh(a[0],vh(f)[0].getId(),h)}return a}function Bh(a,b,c){if(!b)return!1;a.has(b)||a.set(b,[]);var d;(d=a.get(b)).push.apply(d,B(c));return!0};var Ch=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Dh=function(a){return a?decodeURI(a):a},Eh=/#|$/,Fh=function(a,b){var c=a.search(Eh);a:{var d=0;for(var e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)break a;d+=e+1}d=-1}if(d<0)return null;e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return decodeURIComponent(a.slice(d,e!==-1?e:0).replace(/\+/g," "))};function Gh(a){var b=a.length;if(b===0)return 0;for(var c=305419896,d=0;d<b;d++)c^=(c<<5)+(c>>2)+a.charCodeAt(d)&4294967295;return c>0?c:4294967296+c};function Hh(){var a=$d(window);a=a===void 0?Vd():a;return function(b){return Gh(b+" + "+a)%1E3}};var Ih=[12,13,20],Jh=function(a,b,c,d){d=d===void 0?{}:d;var e=d.ja===void 0?!1:d.ja;d=d.pb===void 0?[]:d.pb;this.M=a;this.u=c;this.o={};this.ja=e;a={};this.g=(a[b]=[],a[4]=[],a);this.j={};this.l={};var f=f===void 0?window:f;if(yg===null){yg="";try{b="";try{b=f.top.location.hash}catch(h){b=f.location.hash}if(b){var g=b.match(/\bdeid=([\d,]+)/);yg=g?g[1]:""}}catch(h){}}if(f=yg)for(f=y(f.split(",")||[]),g=f.next();!g.done;g=f.next())(g=Number(g.value))&&(this.j[g]=!0);d=y(d);for(f=d.next();!f.done;f=d.next())this.j[f.value]=!0},Mh=function(a,b,c,d){var e=[],f;if(f=b!==9)a.o[b]?f=!0:(a.o[b]=!0,f=!1);if(f)return Kg(a.u,b,c,e,[],4),e;f=v(Ih,"includes").call(Ih,b);for(var g=[],h=[],k=y([0,1,2]),m=k.next();!m.done;m=k.next()){m=m.value;for(var n=y(v(a.M[m],"entries").call(a.M[m])),l=n.next();!l.done;l=n.next()){var p=y(l.value);l=p.next().value;p=p.next().value;var r=l,t=p;l=new Qe;p=t.filter(function(db){return db.ba===b&&a.j[db.F.getId()]&&Kh(a,db)});if(p.length)for(l=y(p),p=l.next();!p.done;p=l.next())h.push(p.value.F);else if(!a.ja){p=void 0;m===2?(p=d[1],pd(l,2,Re,r)):p=d[0];var z=void 0,A=void 0;p=(A=(z=p)==null?void 0:z(String(r)))!=null?A:m===2&&T(t[0].fa,11)===1?void 0:d[0](String(r));if(p!==void 0){r=y(t);for(t=r.next();!t.done;t=r.next())if(t=t.value,t.ba===b){z=p-t.Qa;var pa=t;A=pa.La;var cb=pa.Ma;pa=pa.jb;z<0||z>=A*cb||z%cb!==pa||!Kh(a,t)||(z=T(t.fa,13),z!==0&&z!==void 0&&(A=a.l[String(z)],A!==void 0&&A!==t.F.getId()?Lg(a.u,a.l[String(z)],t.F.getId(),z):a.l[String(z)]=t.F.getId()),h.push(t.F))}Uc(l,Re)!==0&&(kd(l,3,p),g.push(l))}}}}d=y(h);for(h=d.next();!h.done;h=d.next())h=h.value,k=h.getId(),e.push(k),Lh(a,k,f?4:c),bh(R(h,Fg,2,P()),f?dh():[c],a.u,k);Kg(a.u,b,c,e,g,1);return e},Lh=function(a,b,c){a.g[c]||(a.g[c]=[]);a=a.g[c];v(a,"includes").call(a,b)||a.push(b)},Kh=function(a,b){var c=W(Ng).A,d=Bg(Q(b.fa,cf,3),c);if(!d.success)return Mg(a.u,Q(b.fa,cf,3),b.ba,b.F.getId(),d),!1;if(!d.value)return!1;c=Bg(Q(b.F,cf,3),c);return c.success?c.value?!0:!1:(Mg(a.u,Q(b.F,cf,3),b.ba,b.F.getId(),c),!1)},Nh=function(a,b){b=b.map(function(c){return new wh(c)}).filter(function(c){return!v(Ih,"includes").call(Ih,T(c,1))});a.M=Ah(a.M,b)},Oh=function(a,b){X(1,function(c){a.j[c]=!0},b);X(2,function(c,d,e){return Mh(a,c,d,e)},b);X(3,function(c){return(a.g[c]||[]).concat(a.g[4])},b);X(12,function(c){return void Nh(a,c)},b);X(16,function(c,d){return void Lh(a,c,d)},b)};var Ph=function(){var a={};this.g=function(b,c){return a[b]!=null?a[b]:c};this.j=function(b,c){return a[b]!=null?a[b]:c};this.J=function(b,c){return a[b]!=null?a[b]:c};this.l=function(b,c){return a[b]!=null?a[b]:c};this.C=function(b,c){return a[b]!=null?c.concat(a[b]):c};this.o=function(){}};function Qh(a){return W(Ph).j(a.g,a.defaultValue)};var Rh=function(){this.g=function(){}},Sh=function(a,b){a.g=Y(14,b,function(){})};function Th(a){W(Rh).g(a)};var Uh,Vh,Wh,Xh,Yh,Zh;function $h(a){var b=a.Ya;var c=a.A;var d=a.config;var e=a.Ta===void 0?sh():a.Ta;var f=a.Ca===void 0?0:a.Ca;var g=a.u===void 0?new Ig((Xh=lg((Uh=Q(b,xh,5))==null?void 0:gd(Uh,2)))!=null?Xh:0,(Yh=lg((Vh=Q(b,xh,5))==null?void 0:gd(Vh,4)))!=null?Yh:0,(Zh=(Wh=Q(b,xh,5))==null?void 0:dd(Wh,3))!=null?Zh:!1):a.u;a=a.M===void 0?zh(R(b,wh,2,P(tb))):a.M;e.hasOwnProperty("init-done")?(Y(12,e,function(){})(R(b,wh,2,P()).map(function(h){return K(h)})),Y(13,e,function(){})(R(b,Fg,1,P()).map(function(h){return K(h)}),f),c&&Y(14,e,function(){})(c),ai(f,e)):(Oh(new Jh(a,f,g,d),e),fh(e),gh(e),hh(e),ai(f,e),bh(R(b,Fg,1,P(tb)),[f],g,void 0,!0),Og=Og||!(!d||!d.ma),Th(rh),c&&Th(c))}function ai(a,b){var c=b=b===void 0?sh():b;jh(W(ih),c,a);bi(b,a);a=b;Sh(W(Rh),a);W(Ph).o()}function bi(a,b){var c=W(Ph);c.g=function(d,e){return Y(5,a,function(){return!1})(d,e,b)};c.j=function(d,e){return Y(6,a,function(){return 0})(d,e,b)};c.J=function(d,e){return Y(7,a,function(){return""})(d,e,b)};c.l=function(d,e){return Y(8,a,function(){return[]})(d,e,b)};c.C=function(d,e){return Y(17,a,function(){return[]})(d,e,b)};c.o=function(){Y(15,a,function(){})(b)}};var ci=qa(["https://pagead2.googlesyndication.com/pagead/js/err_rep.js"]),di=function(){var a=a===void 0?"jserror":a;var b=b===void 0?.01:b;var c=c===void 0?Qd(ci):c;this.g=a;this.l=b;this.j=c};var ei=function(){var a;this.V=a=a===void 0?{kb:yd()+(yd()&2097151)*4294967296,Wa:v(Number,"MAX_SAFE_INTEGER")}:a};function fi(a,b){return b>0&&a.kb*b<=a.Wa};var gi=function(a){this.i=L(a)};x(gi,U);var hi=function(a){return dd(a,1)},ii=function(a){return dd(a,2)};function ji(a){a=a===void 0?D:a;return(a=a.performance)&&a.now?a.now():null};function ki(a,b){b=b.google_js_reporting_queue=b.google_js_reporting_queue||[];b.length<2048&&b.push(a)}function li(a,b){var c=ji(b);c&&ki({label:a,type:9,value:c},b)}function mi(a,b,c){var d=!1;d=d===void 0?!1:d;var e=window,f=typeof queueMicrotask!=="undefined";return function(){var g=sa.apply(0,arguments);d&&f&&queueMicrotask(function(){e.google_rum_task_id_counter=e.google_rum_task_id_counter||1;e.google_rum_task_id_counter+=1});var h=ji(),k=3;try{var m=b.apply(this,g)}catch(n){k=13;if(!c)throw n;c(a,n)}finally{e.google_measure_js_timing&&h&&ki(v(Object,"assign").call(Object,{},{label:a.toString(),value:h,duration:(ji()||0)-h,type:k},d&&f&&{taskId:e.google_rum_task_id_counter=e.google_rum_task_id_counter||1}),e)}return m}}function ni(a,b){return mi(a,b,function(c,d){var e=new di;var f=f===void 0?e.l:f;var g=g===void 0?e.g:g;Math.random()>f||(d.error&&d.meta&&d.id||(d=new ve(d,{context:c,id:g})),D.google_js_errors=D.google_js_errors||[],D.google_js_errors.push(d),D.error_rep_loaded||(f=D.document,c=Zd("SCRIPT",f),Ld(c,e.j),(e=f.getElementsByTagName("script")[0])&&e.parentNode&&e.parentNode.insertBefore(c,e),D.error_rep_loaded=!0))})};function Z(a,b){return b==null?"&"+a+"=null":"&"+a+"="+Math.floor(b)}function oi(a,b){return"&"+a+"="+b.toFixed(3)}function pi(){var a=new u.Set;var b=window.googletag;b=(b==null?0:b.apiReady)?b:void 0;try{if(!b)return a;for(var c=b.pubads(),d=y(c.getSlots()),e=d.next();!e.done;e=d.next())a.add(e.value.getSlotId().getDomId())}catch(f){}return a}function qi(a){a=a.id;return a!=null&&(pi().has(a)||v(a,"startsWith").call(a,"google_ads_iframe_")||v(a,"startsWith").call(a,"aswift"))}function ri(a,b,c){if(!a.sources)return!1;switch(si(a)){case 2:var d=ti(a);if(d)return c.some(function(f){return ui(d,f)});break;case 1:var e=vi(a);if(e)return b.some(function(f){return ui(e,f)})}return!1}function si(a){if(!a.sources)return 0;a=a.sources.filter(function(b){return b.previousRect&&b.currentRect});if(a.length>=1){a=a[0];if(a.previousRect.top<a.currentRect.top)return 2;if(a.previousRect.top>a.currentRect.top)return 1}return 0}function vi(a){return wi(a,function(b){return b.currentRect})}function ti(a){return wi(a,function(b){return b.previousRect})}function wi(a,b){return a.sources.reduce(function(c,d){d=b(d);return c?d&&d.width*d.height!==0?d.top<c.top?d:c:c:d},null)}function ui(a,b){var c=Math.min(a.right,b.right)-Math.max(a.left,b.left);a=Math.min(a.bottom,b.bottom)-Math.max(a.top,b.top);return c<=0||a<=0?!1:c*a*100/((b.right-b.left)*(b.bottom-b.top))>=50}var xi=function(){var a={Ea:!0};this.Sa=a=a===void 0?{Ea:!1}:a;this.l=this.j=this.U=this.S=this.L=0;this.ya=this.va=Number.NEGATIVE_INFINITY;this.g=[];this.P={};this.sa=0;this.T=Infinity;this.qa=this.ta=this.ua=this.wa=this.Ba=this.C=this.Aa=this.ha=this.o=0;this.ra=!1;this.ga=this.R=this.J=0;this.u=null;this.xa=!1;this.pa=function(){};this.za=(a=document.querySelector("[data-google-query-id]"))?a.getAttribute("data-google-query-id"):null},yi,zi,Ci=function(){var a=new xi;if(W(Ph).g(Pf.g,Pf.defaultValue)){var b=window;if(!b.google_plmetrics&&window.PerformanceObserver){b.google_plmetrics=!0;b=["layout-shift","largest-contentful-paint","first-input","longtask"];a.Sa.Ea&&b.push("event");b=y(b);for(var c=b.next();!c.done;c=b.next()){c=c.value;var d={type:c,buffered:!0};c==="event"&&(d.durationThreshold=40);Ai(a).observe(d)}Bi(a)}}},Ai=function(a){a.u||(a.u=new PerformanceObserver(ni(640,function(b){Di(a,b)})));return a.u},Bi=function(a){var b=ni(641,function(){var d=document;if(d.prerendering)d=3;else{var e;d=(e={visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[d.visibilityState||d.webkitVisibilityState||d.mozVisibilityState||""])!=null?e:0}d===2&&Ei(a)}),c=ni(641,function(){return void Ei(a)});document.addEventListener("visibilitychange",b);document.addEventListener("pagehide",c);a.pa=function(){document.removeEventListener("visibilitychange",b);document.removeEventListener("pagehide",c);Ai(a).disconnect()}},Ei=function(a){if(!a.xa){a.xa=!0;Ai(a).takeRecords();var b="https://pagead2.googlesyndication.com/pagead/gen_204?id=plmetrics";window.LayoutShift&&(b+=oi("cls",a.L),b+=oi("mls",a.S),b+=Z("nls",a.U),window.LayoutShiftAttribution&&(b+=oi("cas",a.C),b+=Z("nas",a.wa),b+=oi("was",a.Ba)),b+=oi("wls",a.ha),b+=oi("tls",a.Aa));window.LargestContentfulPaint&&(b+=Z("lcp",a.ua),b+=Z("lcps",a.ta));window.PerformanceEventTiming&&a.ra&&(b+=Z("fid",a.qa));window.PerformanceLongTaskTiming&&(b+=Z("cbt",a.J),b+=Z("mbt",a.R),b+=Z("nlt",a.ga));for(var c=0,d=y(document.getElementsByTagName("iframe")),e=d.next();!e.done;e=d.next())qi(e.value)&&c++;b+=Z("nif",c);c=window.google_unique_id;b+=Z("ifi",typeof c==="number"?c:0);c=lh();b+="&eid="+encodeURIComponent(c.join());b+="&top="+(D===D.top?1:0);b+=a.za?"&qqid="+encodeURIComponent(a.za):Z("pvsid",$d(D));window.googletag&&(b+="&gpt=1");c=Math.min(a.g.length-1,Math.floor((a.u?a.sa:performance.interactionCount||0)/50));c>=0&&(c=a.g[c].latency,c>=0&&(b+=Z("inp",c)));window.fetch(b,{keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"});a.pa()}},Fi=function(a,b,c,d){if(!b.hadRecentInput){a.L+=Number(b.value);Number(b.value)>a.S&&(a.S=Number(b.value));a.U+=1;if(c=ri(b,c,d))a.C+=b.value,a.wa++;if(b.startTime-a.va>5E3||b.startTime-a.ya>1E3)a.va=b.startTime,a.j=0,a.l=0;a.ya=b.startTime;a.j+=b.value;c&&(a.l+=b.value);a.j>a.ha&&(a.ha=a.j,a.Ba=a.l,a.Aa=b.startTime+b.duration)}},Di=function(a,b){var c=yi!==window.scrollX||zi!==window.scrollY?[]:Gi,d=Hi();b=y(b.getEntries());for(var e=b.next(),f={};!e.done;f={D:void 0},e=b.next())switch(f.D=e.value,e=f.D.entryType,e){case "layout-shift":Fi(a,f.D,c,d);break;case "largest-contentful-paint":f=f.D;a.ua=Math.floor(f.renderTime||f.loadTime);a.ta=f.size;break;case "first-input":e=f.D;a.qa=Number((e.processingStart-e.startTime).toFixed(3));a.ra=!0;a.g.some(function(g){return function(h){return v(h,"entries").some(function(k){return g.D.duration===k.duration&&g.D.startTime===k.startTime})}}(f))||Ii(a,f.D);break;case "longtask":f=Math.max(0,f.D.duration-50);a.J+=f;a.R=Math.max(a.R,f);a.ga+=1;break;case "event":Ii(a,f.D);break;default:Qb(e)}},Ii=function(a,b){Ji(a,b);var c=a.g[a.g.length-1],d=a.P[b.interactionId];if(d||a.g.length<10||b.duration>c.latency)d?(v(d,"entries").push(b),d.latency=Math.max(d.latency,b.duration)):(b={id:b.interactionId,latency:b.duration,entries:[b]},a.P[b.id]=b,a.g.push(b)),a.g.sort(function(e,f){return f.latency-e.latency}),a.g.splice(10).forEach(function(e){delete a.P[e.id]})},Ji=function(a,b){b.interactionId&&(a.T=Math.min(a.T,b.interactionId),a.o=Math.max(a.o,b.interactionId),a.sa=a.o?(a.o-a.T)/7+1:0)},Hi=function(){var a=v(Array,"from").call(Array,document.getElementsByTagName("iframe")).filter(qi),b=[].concat(B(pi())).map(function(c){return document.getElementById(c)}).filter(function(c){return c!==null});yi=window.scrollX;zi=window.scrollY;return Gi=[].concat(B(a),B(b)).map(function(c){return c.getBoundingClientRect()})},Gi=[];var Ki=function(a){this.i=L(a)};x(Ki,U);Ki.prototype.getVersion=function(){return S(this,2)};var Li=function(a){this.i=L(a)};x(Li,U);var Mi=function(a,b){return O(a,2,oc(b))},Ni=function(a,b){return O(a,3,oc(b))},Oi=function(a,b){return O(a,4,oc(b))},Pi=function(a,b){return O(a,5,oc(b))},Qi=function(a,b){return O(a,9,oc(b))},Ri=function(a,b){return bd(a,10,b)},Si=function(a,b){return O(a,11,b==null?b:Wb(b))},Ti=function(a,b){return O(a,1,oc(b))},Ui=function(a,b){return O(a,7,b==null?b:Wb(b))};var Vi="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Wi(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Xi(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Yi(a){if(!Xi(a))return null;var b=Wi(a);if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Vi).then(function(c){b.uach!=null||(b.uach=c);return c});return b.uach_promise=a}function Zi(a){var b;return Si(Ri(Pi(Mi(Ti(Oi(Ui(Qi(Ni(new Li,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),((b=a.fullVersionList)==null?void 0:b.map(function(c){var d=new Ki;d=O(d,1,oc(c.brand));return O(d,2,oc(c.version))}))||[]),a.wow64||!1)}function $i(a){var b,c;return(c=(b=Yi(a))==null?void 0:b.then(function(d){return Zi(d)}))!=null?c:null};var aj=function(a){this.i=L(a)};x(aj,U);var bj=function(a){this.i=L(a)};x(bj,U);var cj=function(a){var b=new bj;return $c(b,1,a)};function dj(a,b,c){try{vb(!b._b_);var d={d:K(a.data),p:a.lb};b._b_=d}catch(e){c.G({methodName:1298,I:e})}};var ej=function(a,b,c){this.g=a;this.K=b;this.j=c};ej.prototype.G=function(a){var b=fi(this.K.V,1E3),c=Qh(Mf),d=fi(this.K.V,c);if(b||d){var e=this.j,f=e.Oa,g=e.Na,h=e.Ia,k=e.ca,m=e.ab;e=e.Za;k=k();var n=a.I;try{var l=zb(n==null?void 0:n.name)?n.name:"Unknown error"}catch(z){l="e.name threw"}try{var p=zb(n==null?void 0:n.message)?n.message:"Caught "+n}catch(z){p="e.message threw"}try{var r=zb(n==null?void 0:n.stack)?n.stack:Error().stack;var t=r?r.split(/\n\s*/):[]}catch(z){t=["e.stack threw"]}r=t;t=new sf;t=ld(t,5,1E3);n=new qf;a=od(n,1,a.methodName);a=md(a,2,l);a=md(a,3,p);a=Oc(a,4,r,nc);a=qd(a);a=ad(t,1,tf,a);l=new rf;f=ld(l,1,f);f=Oc(f,2,k,ac);g=md(f,3,g);g=qd(g);g=$c(a,2,g);g=qd(g);g=Bc(g);f=new pf;h=md(f,1,h);m=m==null?void 0:m();m=kd(h,2,m);e=e==null?void 0:e();e=Oc(m,3,e,nc);e=qd(e);e=ad(g,4,uf,e);b&&this.g.rb(e);if(d){ld(e,5,c);a:{Dc(e);if(void 0===ub){if(Vc(e,tf,1)!==1){b=void 0;break a}}else Sc(e.i,void 0,tf,1);b=Wc(e,qf,1)}b!=null&&O(b,4);this.g.qb(e)}}};function fj(a){var b={};b=(b[0]=Hh(),b);W(ih).j(a,b)};var gj={},hj=(gj[253]=!1,gj[246]=[],gj[150]="",gj[263]=!1,gj[36]=/^true$/.test("false"),gj[172]=null,gj[260]=void 0,gj[251]=null,gj),ij=function(){this.g=!1};function jj(a){W(ij).g=!0;return hj[a]}function kj(a,b){W(ij).g=!0;hj[a]=b};var lj=/^(?:https?:)?\/\/(?:www\.googletagservices\.com|securepubads\.g\.doubleclick\.net|(pagead2\.googlesyndication\.com))(\/tag\/js\/gpt(?:_[a-z]+)*\.js)/;function mj(a){var b=a.Ja;var c=a.hb;var d=a.Pa;var e=a.fb;var f=a.Xa;var g=a.bb;var h=b?!lj.test(b.src):!0;a={};b={};var k={};return k[3]=(a[3]=function(){return!h},a[59]=function(){var m=sa.apply(0,arguments),n=v(m,"includes"),l=String,p;var r=r===void 0?window:r;var t;r=(t=(p=Dh(r.location.href.match(Ch)[3]||null))==null?void 0:p.split("."))!=null?t:[];r.length<2?p=null:(p=r[r.length-1],p=p==="uk"||p==="br"||p==="nz"?r.length<3?null:Gh(r.splice(r.length-3).join(".")):Gh(r.splice(r.length-2).join(".")));return n.call(m,l(p))},a[74]=function(){return v(sa.apply(0,arguments),"includes").call(sa.apply(0,arguments),String(Gh(window.location.href)))},a[61]=function(){return e},a[63]=function(){return e||g===".google.ch"},a[73]=function(m){return v(d,"includes").call(d,Number(m))},a),k[4]=(b[1]=function(){return f},b[13]=function(){return c},b),k[5]={},k};function nj(a,b){if(W(Ph).g(Nf.g,Nf.defaultValue)){var c=function(d){d.data!=null&&d.data!==""||d.origin.indexOf("android-app://")!==0||(b(),a.removeEventListener("message",c))};a.addEventListener("message",c)}};function oj(a){return!(a==null||!a.src)&&Dh(a.src.match(Ch)[3]||null)==="pagead2.googlesyndication.com"};var pj=qa(["https://pagead2.googlesyndication.com/pagead/managed/js/gpt/","/pubads_impl.js"]),qj=qa(["https://securepubads.g.doubleclick.net/pagead/managed/js/gpt/","/pubads_impl.js"]);function rj(a){a=oj(a)?Qd(pj,"m202507080101"):Qd(qj,"m202507080101");var b=Qh(Rf);return b?Rd(a,new u.Map([["cb",b]])):a};function sj(a,b){var c=jj(246);c=vc(c);c=rd(yh,c);if(!R(c,Fg,1,P()).length&&R(a,Fg,1,P()).length){var d=R(a,Fg,1,P());bd(c,1,d)}!R(c,wh,2,P()).length&&R(a,wh,2,P()).length&&(d=R(a,wh,2,P()),bd(c,2,d));!Ic(c,xh,5)&&Ic(a,xh,5)&&(a=Q(a,xh,5),$c(c,5,a));kj(246,K(c));$h({Ya:c,A:mj(b),Ca:2,config:{ma:b.ma}});b.Pa.forEach(kh)};var tj=function(a,b,c){ej.call(this,a,b,c);this.j=c};x(tj,ej);var uj=qa(["https://pagead2.googlesyndication.com/pagead/ppub_config"]),vj=qa(["https://securepubads.g.doubleclick.net/pagead/ppub_config"]);function wj(a,b,c,d,e){a=a.location.host;var f=Fh(b.src,"domain");b=Fh(b.src,"network-code");if(a||f||b){var g=new u.Map;a&&g.set("ippd",a);f&&g.set("pppd",f);b&&g.set("pppnc",b);a=g}else a=void 0;a?(c=c?Qd(uj):Qd(vj),c=Rd(c,a),xj(c,d,e)):e(new u.globalThis.Error("no provided or inferred data"))}function xj(a,b,c){u.globalThis.fetch(a.toString(),{method:"GET",credentials:"omit"}).then(function(d){d.status<300?(li("13",window),d.status===204?b(""):d.text().then(function(e){b(e)})):c(new u.globalThis.Error("resp:"+d.status))}).catch(function(d){d instanceof Error?c(new u.globalThis.Error(d.message)):c(new u.globalThis.Error("fetch error: "+d))})};var yj=function(a,b,c){this.K=a;this.oa=b;this.da=c;this.g=[]},Cj=function(a,b,c,d,e){var f=e==null?void 0:ud(Xc(e,td,1));(f==null?0:f.length)&&v(b.location.hostname,"includes").call(b.location.hostname,f)?(zj(a),Aj(a,{mb:e})):(q=["http:","https:"],v(q,"includes")).call(q,b.location.protocol)?c?(zj(a),wj(Ud(b),c,d,function(g){return void Aj(a,{ob:g})},function(g){Aj(a,{error:g})})):Bj(a,5):Bj(a,4)},zj=function(a){jj(260);kj(260,function(b){a.j!==void 0||a.l?b(a.j,a.l):a.g.push(b)})},Aj=function(a,b){var c=b.ob;var d=b.mb;b=b.error;a.j=c!=null?c:d==null?void 0:JSON.stringify(K(d));a.l=b;d=y(a.g);for(var e=d.next();!e.done;e=d.next())e=e.value,e(a.j,a.l);a.g.length=0;Bj(a,b?6:c?3:2)},Bj=function(a,b){var c=Qh(Qf);fi(a.K.V,c)&&a.oa.Ka.Ra.Ha.nb.W({Z:c,source:b,Ua:!E("Android")||Ma()||Ka()||Ja()||E("Silk")?Ma()?2:(Ia()?0:E("Edge"))?3:Ka()?4:(Ia()?0:E("Trident")||E("MSIE"))?5:!E("iPad")&&!E("iPhone")||La()||Ma()||(Ia()?0:E("Coast"))||Ka()||!E("AppleWebKit")?Ja()?6:La()?7:E("Silk")?8:0:9:1});a=a.da;var d=Qh(Qf);if(fi(a.K.V,d)){var e=a.j,f=e.Ia,g=e.ca;c=e.Na;e=e.Oa;var h=new of;e=ld(h,2,e);f=md(e,3,f);e=Math;h=e.trunc;a:{if(u.globalThis.performance){var k=performance.timeOrigin+performance.now();if(v(Number,"isFinite").call(Number,k)&&k>0)break a}k=Date.now();k=v(Number,"isFinite").call(Number,k)&&k>0?k:0}f=ld(f,1,h.call(e,k));g=g();g=Oc(f,4,g,ac);d=ld(g,5,d);c=md(d,6,c);d=new mf;g=new lf;b=nd(g,1,b);b=qd(b);b=ad(d,10,nf,b);b=qd(b);b=$c(c,7,b);b=qd(b);a.g.sb(b)}};var Dj=function(a){return function(b){b=JSON.parse(b);if(!Array.isArray(b))throw Error("Expected jspb data to be an array, got "+wa(b)+": "+b);mb(b,34);return new a(b)}}(aj),Ej=function(a){return function(){var b;(b=a[eb])||(b=new a,mb(b.i,34),b=a[eb]=b);return b}}(aj);function Fj(a,b){try{var c=wb;if(!zb(a)){var d,e,f=(e=(d=typeof c==="function"?c():c)==null?void 0:d.concat("\n"))!=null?e:"";throw Error(f+String(a));}return Dj(a)}catch(g){return b.G({methodName:838,I:g}),Ej()}};function Gj(){var a;return(a=D.googletag)!=null?a:D.googletag={cmd:[]}}function Hj(a,b){var c=Gj();c.hasOwnProperty(a)||(c[a]=b)};function Ij(){var a=sttc,b=Jj(),c=b.K,d=b.oa,e=b.da;Xa(function(A){e.G({methodName:1189,I:A})});b=Gj();a=Fj(a,e);vb(!W(ij).g);v(Object,"assign").call(Object,hj,b._vars_);b._vars_=hj;a&&(dd(a,3)&&kj(36,!0),S(a,6)&&kj(150,S(a,6)),ii(Xc(a,gi,13))&&kj(263,!0));var f=Xc(a,yh,1),g={fb:hi(Xc(a,gi,13)),hb:ed(a,2),Pa:Jc(a,10,bc,P()),Xa:ed(a,7),bb:S(a,6),ma:dd(a,4)},h=Q(a,vd,9),k=window,m=k.document;Hj("_loaded_",!0);Hj("cmd",[]);var n,l=(n=Kj(m))!=null?n:Lj(m);Mj(f,k,v(Object,"assign").call(Object,{},{Ja:l},g));try{Ci()}catch(A){}li("1",k);n=rj(l);f=(l==null?void 0:l.crossOrigin)==="anonymous";g=Qh(Of);if(fi(c.V,g)){var p=d.Ka.Ra.Ha;p.gb.W({Z:g,la:(l==null?void 0:l.crossOrigin)==="anonymous",na:oj(l)});p.cb.W({Z:g,la:f,na:Dh(n.toString().match(Ch)[3]||null)==="pagead2.googlesyndication.com"})}var r=!1;dj({data:qd(cj(a)),lb:function(){return r}},b,e);if(!Nj(m)){g="gpt-impl-"+Math.random();try{Nd(m,Od(n,{id:g,nonce:Kd(document),Fa:f?"anonymous":void 0}))}catch(A){}m.getElementById(g)&&(b._loadStarted_=!0)}if(!b._loadStarted_){g=Zd("SCRIPT");Ld(g,n);g.async=!0;f&&(g.crossOrigin="anonymous");n=m.body;f=m.documentElement;var t,z;((z=(t=m.head)!=null?t:n)!=null?z:f).appendChild(g);b._loadStarted_=!0}if(k===k.top)try{sg(k,Xc(a,gi,13))}catch(A){e.G({methodName:1209,I:A})}Cj(new yj(c,d,e),k,l,oj(l),h);nj(k,function(){r=!0});S(a,14)&&wg(k.document,xg(S(a,14),oj(l)),e)}function Jj(){var a=$d(window),b=new ei,c=new Jf(11,"m202507080101",1E3);return{oa:c,K:b,da:new tj(c,b,{Oa:a,Na:window.document.URL,Ia:"m202507080101",ca:lh})}}function Kj(a){return(a=a.currentScript)?a:null}function Lj(a){var b;a=y((b=a.scripts)!=null?b:[]);for(b=a.next();!b.done;b=a.next())if(b=b.value,v(b.src,"includes").call(b.src,"/tag/js/gpt"))return b;return null}function Mj(a,b,c){kj(172,c.Ja);sj(a,c);fj(12);fj(5);(a=$i(b))&&a.then(function(d){return void kj(251,JSON.stringify(K(d)))});Yd(W(Ph).l(Sf.g,Sf.defaultValue),b.document)}function Nj(a){var b=Kj(a);return a.readyState==="complete"||a.readyState==="loaded"||!(b==null||!b.async)};try{Ij()}catch(a){try{Jj().da.G({methodName:420,I:a})}catch(b){}};}).call(this,"[[[[739271509,null,null,[1]],[740838399,null,null,[1]],[null,7,null,[null,0.1]],[null,null,null,[],[[[4,null,83],[null,null,null,[\"1 bidderRequests.bids bidder userIdAsEids.source\",\"2 bidderRequests.bids.userIdAsEids source provider\",\"3 bidderRequests.bids bidder ortb2Imp.ext.tid?\",\"5 bidderRequests.bids bidder mediaTypes.banner\",\"6 bidderRequests.bids bidder mediaTypes.native?\",\"7 bidderRequests.bids bidder mediaTypes.video\",\"8 bidderRequests.bids bidder ortb2Imp.ext.gpid?\",\"9 bidderRequests.bids bidder ortb2.site.content.data.ext.segment?\",\"10 bidderRequests.bids bidder ortb2.site.page\",\"11 bidderRequests.bids bidder ortb2.user.data.segment?\",\"12 bidderRequests.bids bidder ortb2.user.data.ext.segtax?\",\"13 bidsReceived adId creativeId\",\"14 bidderRequests.bids.userIdAsEids source uids.ext.provider\",\"15 bidderRequests.bids.userIdAsEids source uids.atype\",\"16 bidderRequests.bids.userIdAsEids source uids.length\",\"17 bidsReceived adId ttl\",\"18 bidsReceived adId meta.primaryCatId\",\"19 bidsReceived adId meta.secondaryCatIds\"]]]],*********],[null,*********,null,null,[[[4,null,83],[null,1]]]],[null,*********,null,null,[[[4,null,83],[null,100]]]],[null,*********,null,[null,-1]],[null,*********,null,[null,-1],[[[4,null,83],[null,5000]]]],[null,*********,null,[null,-1],[[[4,null,83],[null,60000]]]],[null,null,null,[null,null,null,[\"1 dbm\/(ad|clkk)\"]],[[[4,null,83],[null,null,null,[\"1 dbm\/(ad|clkk)\",\"2 (adsrvr|adserver)\\\\.org\/bid\/\",\"3 criteo.com\/(delivery|[a-z]+\/auction)\",\"4 yahoo.com\/bw\/[a-z]+\/imp\/\",\"5 (adnxs|adnxs-simple).com\/it\",\"6 amazon-adsystem.com\/[a-z\/]+\/impb\"]]]],*********],[null,*********,null,null,[[[4,null,83],[null,1]]]],[null,741624914,null,[null,100]],[741624915,null,null,[1]],[null,612427113,null,[null,100]],[null,699982590,null,null,[[[4,null,83],[null,100]]]],[null,720326000,null,[null,1]],[null,749055567,null,[null,100]],[null,732179314,null,[null,10]],[45681222,null,null,[]],[null,578655462,null,[null,1000]],[698519058,null,null,[1]],[667794963,null,null,[]],[736254283,null,null,[1]],[null,680683506,null,[null,1000]],[697841467,null,null,[1]],[null,704895900,null,[null,1000]],[764817240,null,null,[1]],[null,770246397,null,[null,1000]],[null,625028672,null,[null,100]],[null,629733890,null,[null,1000]],[null,695925491,null,[null,20]],[null,null,null,[],null,489560439],[null,null,null,[],null,505762507],[null,1921,null,[null,72]],[null,1920,null,[null,12]],[null,426169222,null,[null,1000]],[null,377289019,null,[null,10000]],[null,750987462,null,[null,10000]],[null,529,null,[null,20]],[null,573282293,null,[null,0.01]],[null,684553008,null,[null,100]],[776685356,null,null,[]],[776768199,null,null,[]],[45624259,null,null,[],[[[4,null,59,null,null,null,null,[\"2452487976\"]],[1]]]],[45627954,null,null,[1]],[45646888,null,null,[]],[45622305,null,null,[1]],[null,447000223,null,[null,0.01]],[360245597,null,null,[1]],[null,716359135,null,[null,10]],[null,716359138,null,[null,50]],[null,716359132,null,[null,100]],[null,716359134,null,[null,1000]],[null,716359137,null,[null,0.25]],[null,716359136,null,[null,10]],[null,716359133,null,[null,5]],[629201869,null,null,[1]],[null,729624435,null,[null,10000]],[null,729624436,null,[null,500]],[null,550718589,null,[null,250],[[[3,[[4,null,15,null,null,null,null,[\"22814497764\"]],[4,null,15,null,null,null,null,[\"6581\"]],[4,null,15,null,null,null,null,[\"18190176\"]],[4,null,15,null,null,null,null,[\"21881754602\"]],[4,null,15,null,null,null,null,[\"6782\"]],[4,null,15,null,null,null,null,[\"309565630\"]],[4,null,15,null,null,null,null,[\"22306534072\"]],[4,null,15,null,null,null,null,[\"7229\"]],[4,null,15,null,null,null,null,[\"28253241\"]],[4,null,15,null,null,null,null,[\"1254144\"]],[4,null,15,null,null,null,null,[\"21732118914\"]],[4,null,15,null,null,null,null,[\"5441\"]],[4,null,15,null,null,null,null,[\"162717810\"]],[4,null,15,null,null,null,null,[\"51912183\"]],[4,null,15,null,null,null,null,[\"23202586\"]],[4,null,15,null,null,null,null,[\"44520695\"]],[4,null,15,null,null,null,null,[\"1030006\"]],[4,null,15,null,null,null,null,[\"21830601346\"]],[4,null,15,null,null,null,null,[\"23081961\"]],[4,null,15,null,null,null,null,[\"21880406607\"]],[4,null,15,null,null,null,null,[\"93656639\"]],[4,null,15,null,null,null,null,[\"1020351\"]],[4,null,15,null,null,null,null,[\"5931321\"]],[4,null,15,null,null,null,null,[\"3355436\"]],[4,null,15,null,null,null,null,[\"22106840220\"]],[4,null,15,null,null,null,null,[\"22875833199\"]],[4,null,15,null,null,null,null,[\"32866417\"]],[4,null,15,null,null,null,null,[\"8095840\"]],[4,null,15,null,null,null,null,[\"71161633\"]],[4,null,15,null,null,null,null,[\"22668755367\"]],[4,null,15,null,null,null,null,[\"6177\"]],[4,null,15,null,null,null,null,[\"147246189\"]],[4,null,15,null,null,null,null,[\"22152718\"]],[4,null,15,null,null,null,null,[\"21751243814\"]],[4,null,15,null,null,null,null,[\"22013536576\"]],[4,null,15,null,null,null,null,[\"4444\"]],[4,null,15,null,null,null,null,[\"44890869\"]],[4,null,15,null,null,null,null,[\"248415179\"]],[4,null,15,null,null,null,null,[\"5293\"]],[4,null,15,null,null,null,null,[\"21675937462\"]],[4,null,15,null,null,null,null,[\"21726375739\"]],[4,null,15,null,null,null,null,[\"1002212\"]],[4,null,15,null,null,null,null,[\"6718395\"]]]],[null,500]]]],[null,575880738,null,[null,10]],[null,586681283,null,[null,100]],[null,45679761,null,[null,30000]],[null,732179799,null,[null,250]],[null,745376890,null,[null,1]],[null,745376891,null,[null,2]],[null,635239304,null,[null,100]],[null,740510593,null,[null,0.3]],[null,618260805,null,[null,10]],[696192462,null,null,[1]],[752401957,null,null,null,[[[1,[[4,null,59,null,null,null,null,[\"473346114\"]]]],[1]]]],[null,532520346,null,[null,30]],[null,723123766,null,[null,100]],[null,735866756,null,[null,100]],[null,758860411,null,[null,60]],[null,751161866,null,[null,30000]],[null,630428304,null,[null,100]],[730909248,null,null,null,[[[1,[[4,null,59,null,null,null,null,[\"473346114\"]]]],[1]]]],[746075837,null,null,[]],[null,624264750,null,[null,-1]],[759731353,null,null,[1]],[607106222,null,null,[1]],[null,398776877,null,[null,60000]],[null,374201269,null,[null,60000]],[null,371364213,null,[null,60000]],[null,682056200,null,[null,100]],[null,376149757,null,[null,0.0025]],[570764855,null,null,[1]],[null,null,579921177,[null,null,\"control_1\\\\.\\\\d\"]],[null,570764854,null,[null,50]],[578725095,null,null,[1]],[684149381,null,null,[1]],[377936516,null,null,[1]],[null,599575765,null,[null,1000]],[null,null,2,[null,null,\"1-0-45\"]],[null,707091695,null,[null,100]],[null,626653285,null,[null,1000]],[null,626653286,null,[null,2]],[null,642407444,null,[null,10]],[715057423,null,null,[1]],[null,741215712,null,[null,100]],[753273857,null,null,[1]],[760634449,null,null,[1]],[756891373,null,null,[1]],[738873455,null,null,[1]],[738498807,null,null,[1]],[748334750,null,null,[1]],[748358074,null,null,[1]],[742771044,null,null,[1]],[756322359,null,null,[1]],[752877076,null,null,[1]],[742253121,null,null,[1]],[740342407,null,null,[1]],[null,506394061,null,[null,100]],[null,733365673,null,[null,2],[[[4,null,59,null,null,null,null,[\"**********\"]],[null,1]]]],[null,null,null,[null,null,null,[\"95335247\"]],null,631604025],[null,694630217,null,[null,670]],[null,null,null,[],null,489],[392065905,null,null,null,[[[3,[[4,null,68],[4,null,83]]],[1]]]],[null,360245595,null,[null,500]],[null,715129739,null,[null,30]],[null,681088477,null,[null,100]],[676934885,null,null,[1],[[[4,null,59,null,null,null,null,[\"4214592683\",\"3186860772\",\"2930824068\",\"4127372480\",\"3985777170\",\"2998550476\",\"1946945953\",\"2901923877\",\"1931583037\",\"733037847\",\"287365726\",\"396735883\",\"2445204049\"]],[]]]],[776823724,null,null,[]],[751082905,null,null,[]],[703552063,null,null,[1]],[703102329,null,null,[1]],[703102335,null,null,[1]],[703102334,null,null,[1]],[703102333,null,null,[1]],[703102330,null,null,[1]],[703102332,null,null,[1]],[555237688,null,null,[],[[[2,[[4,null,70,null,null,null,null,[\"browsing-topics\"]],[1,[[4,null,27,null,null,null,null,[\"isSecureContext\"]]]]]],[1]]]],[555237686,null,null,[]],[null,638742197,null,[null,500]],[null,514795754,null,[null,2]],[null,697023992,null,[null,500]],[null,null,null,[null,null,null,[\"679602798\",\"965728666\",\"3786422334\",\"4071951799\"]],null,603422363],[590730356,null,null,null,[[[12,null,null,null,4,null,\"Chrome\\\\\/((?!1[0-1]\\\\d)(?!12[0-3])\\\\d{3,})\",[\"navigator.userAgent\"]],[1]]]],[564724551,null,null,null,[[[12,null,null,null,4,null,\"Chrome\\\\\/((?!10\\\\d)(?!11[0-6])\\\\d{3,})\",[\"navigator.userAgent\"]],[1]]]],[null,596918936,null,[null,500]],[null,607730666,null,[null,10]],[616896918,null,null,[1]],[638632925,null,null,[1]],[647331452,null,null,[1]],[647331451,null,null,[1]],[null,null,null,[null,null,null,[\"AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"Amm8\/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq\/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW\/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\",\"A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\"]],null,1934],[*********,null,null,[]]],[[3,[[null,[[1337,[[77,null,null,[1]],[78,null,null,[1]],[80,null,null,[1]],[76,null,null,[1]],[84,null,null,[1]],[188,null,null,[1]]]]]],[1000,[[31072561]],[2,[[4,null,70,null,null,null,null,[\"run-ad-auction\"]],[12,null,null,null,4,null,\"FLEDGE_GAM_EXTERNAL_TESTER\",[\"navigator.userAgent\"]]]]],[10,[[31088080],[31088081]]],[null,[[31092371,[[null,null,null,[null,null,null,[\"v\",\"1-0-45\"]],null,1]]],[31092372,[[null,null,2,[null,null,\"1-0-45\"]]]]]],[470,[[83321072],[83321073]],null,136],[10,[[83321253],[83321254]],null,136],[10,[[83321266],[83321267]],null,136],[10,[[83321442],[83321443]],null,136],[2,[[95364078],[95364079]]],[null,[[676982960],[676982998]]]]],[12,[[10,[[31061690],[31061691,[[83,null,null,[1]],[84,null,null,[1]]]]],null,59],[40,[[95340252],[95340253,[[662101537,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5],[40,[[95340254],[95340255,[[662101539,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5]]],[13,[[500,[[31061692],[31061693,[[77,null,null,[1]],[78,null,null,[1]],[80,null,null,[1]],[76,null,null,[1]]]]],[4,null,6,null,null,null,null,[\"31061691\"]]]]],[5,[[50,[[31067420],[31067421,[[360245597,null,null,[]]]]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[1000,[[31084129,null,[2,[[2,[[8,null,null,1,null,-1],[7,null,null,1,null,10]]],[4,null,3]]]]],null,80,null,null,null,null,null,null,null,null,4],[1000,[[31084130,null,[2,[[2,[[8,null,null,1,null,9],[7,null,null,1,null,20]]],[4,null,3]]]]],null,80,null,null,null,null,null,null,null,null,4],[50,[[31085776],[31085777,[[45624259,null,null,[1]]]]],null,114],[100,[[31086814],[31086815,[[null,665058368,null,[null,1]]]]]],[1,[[31089681],[31089682,[[710738456,null,null,[1]]]]]],[null,[[31090916],[31090917,[[45681222,null,null,[1]]]]]],[100,[[31091881],[31091882,[[555237687,null,null,[1]]]]]],[10,[[31092363],[31092364,[[null,751161865,null,[null,1]]]],[31092365,[[null,751161865,null,[null,2]]]]]],[50,[[31092987],[31092989,[[null,754057781,null,[null,2]]]]],[6,null,null,3,null,2]],[50,[[31092990],[31092991,[[761958081,null,null,[1]]]]]],[50,[[31093022],[31093023,[[767691923,null,null,[1]]]]],null,119],[50,[[31093080],[31093081,[[768109354,null,null,[1]]]]]],[10,[[31093082],[31093083,[[767688524,null,null,[1]]]]]],[10,[[31093145],[31093146,[[null,762520832,null,[null,1000]]]]]],[1,[[31093147],[31093148,[[null,771299702,null,[null,100]]]]]],[10,[[31093200],[31093201,[[738482525,null,null,[1]]]]]],[10,[[31093208],[31093209,[[45690337,null,null,[1]]]]]],[1,[[31093311],[31093312,[[null,null,null,[null,null,null,[\"\/18190176,22549796121\/AdThrive_Footer_1\/6009cd93adec3b6a941dc608\",\"\/21707781519\/ls-mweb\/ls_mweb_hp\",\"\/1211\/br.terra.homepage\/home360\/ancora\",\"7811748\/elpais_web\/portada\",\"\/15184186,21720760015\/zerogpt_sticky_footer\",\"\/34616581\/20minutos.es\/home\/<USER>\/Sticky\",\"85406138\/Mobile_HP_Anchor\",\"\/9176203,21954917504\/1875137\",\"\/15748617,22365852633\/Loteriasdominicanascom\/Loteriasdominicanascom-Mobile-pushup\",\"\/55964524\/md_w\/p\",\"\/308365556,23188822945\/nitro-banner\/nitro-banner-1928\"]],null,771226568]]]],null,149],[1,[[31093313,null,[6,null,null,3,null,2]],[31093314,[[null,null,null,[null,null,null,[\"\/8804\/uol\/home\/320x50_footer\",\"\/99071977\/mc-mv2\/portada\",\"\/22888152279\/us\/yhp\/main\/mw\/us_yhp_main_mw_btm_sticky\",\"\/5765\/dailymail.uk\/dm_dmhome_homehp\/sticky_banner_monews\"]],null,771226568]],[6,null,null,3,null,2]]],null,149],[1,[[31093315,null,[6,null,null,3,null,0]],[31093316,[[null,null,null,[null,null,null,[\"\/66738120\/sozcu_desktop_anasayfa_sticky\"]],null,771226568]],[6,null,null,3,null,0]]],null,149],[10,[[31093317],[31093318,[[748362437,null,null,[1]]]]]],[1000,[[31093377,[[null,24,null,[null,31093377]]],[6,null,null,13,null,31093377]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[1000,[[31093378,[[null,24,null,[null,31093378]]],[6,null,null,13,null,31093378]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[100,[[31093395],[31093396,[[772510493,null,null,[1]]]]]],[1000,[[31093411,[[null,24,null,[null,31093411]]],[6,null,null,13,null,31093411]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[1000,[[31093412,[[null,24,null,[null,31093412]]],[6,null,null,13,null,31093412]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[10,[[31093417],[31093418,[[776566803,null,null,[1]]]]]],[100,[[31093421],[31093422,[[776663113,null,null,[1]]]]]],[100,[[31093431],[31093432,[[762052991,null,null,[1]]]]]],[1000,[[31093443,[[null,24,null,[null,31093443]]],[6,null,null,13,null,31093443]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[1000,[[31093444,[[null,24,null,[null,31093444]]],[6,null,null,13,null,31093444]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[500,[[95353384],[95353385,[[667794963,null,null,[1]]]]]],[500,[[95365037],[95365038,[[751082905,null,null,[1]]]]]]]],[9,[[1000,[[31083577]],[4,null,13,null,null,null,null,[\"cxbbhbxm\"]]],[1000,[[31091121,null,[4,null,13,null,null,null,null,[\"zxcvbnms\"]]]],null,141,null,null,null,null,null,null,null,null,33],[1000,[[31091122,[[null,720326000,null,[null,1]]],[4,null,13,null,null,null,null,[\"qwrtplkj\"]]]],null,141,null,null,null,null,null,null,null,null,33]]],[2,[[10,[[31084489],[31084490]],null,null,null,null,32,null,null,142,1],[1000,[[31084739,[[null,*********,null,[null,100]]]]],[4,null,6,null,null,null,null,[\"31065645\"]]],[10,[[31084865],[31084866]],null,null,null,null,35,null,null,166,1],[1000,[[31087377,null,[2,[[4,null,86],[4,null,6,null,null,null,null,[\"31065644\"]]]]]],null,131,null,null,null,null,null,null,null,null,28],[1000,[[31087378,null,[2,[[4,null,86],[4,null,6,null,null,null,null,[\"31065645\"]]]]]],null,131,null,null,null,null,null,null,null,null,28],[1000,[[31087490,null,[2,[[1,[[4,null,86]]],[4,null,6,null,null,null,null,[\"31065644\"]]]]]],null,131,null,null,null,null,null,null,null,null,28],[1000,[[31087491,null,[2,[[1,[[4,null,86]]],[4,null,6,null,null,null,null,[\"31065645\"]]]]]],null,131,null,null,null,null,null,null,null,null,28],[50,[[31092216],[31092217,[[730909248,null,null,[]]]]],null,null,null,null,null,200,null,216,1],[10,[[31092998],[31092999,[[729624434,null,null,[1]],[null,729624435,null,[null,5000]]]],[31093000,[[725693774,null,null,[1]]]],[31093001,[[732179798,null,null,[1]]]]],null,null,null,null,null,null,null,198,1],[800,[[31093004,[[729624434,null,null,[1]],[725693774,null,null,[1]],[732179798,null,null,[1]],[null,729624435,null,[null,5000]]],[3,[[2,[[4,null,15,null,null,null,null,[\"6032\"]],[6,null,null,3,null,2],[1,[[4,null,59,null,null,null,null,[\"2004189832\"]]]]]],[4,null,15,null,null,null,null,[\"1012355\"]],[4,null,15,null,null,null,null,[\"9528481\"]],[4,null,15,null,null,null,null,[\"151127700\"]],[4,null,15,null,null,null,null,[\"8448570\"]],[4,null,15,null,null,null,null,[\"5302\"]],[4,null,15,null,null,null,null,[\"7811748\"]],[4,null,15,null,null,null,null,[\"138855687\"]],[4,null,15,null,null,null,null,[\"99287527\"]],[4,null,15,null,null,null,null,[\"19024548\"]],[4,null,15,null,null,null,null,[\"22897154128\"]]]]]],null,null,null,null,null,200,null,198,1],[90,[[31093009,[[729624434,null,null,[1]],[725693774,null,null,[1]],[732179798,null,null,[1]],[null,729624435,null,[null,5000]]],[3,[[2,[[4,null,15,null,null,null,null,[\"6032\"]],[3,[[6,null,null,3,null,2],[1,[[4,null,59,null,null,null,null,[\"2004189832\"]]]]]]]],[4,null,15,null,null,null,null,[\"1012355\"]],[4,null,15,null,null,null,null,[\"9528481\"]],[4,null,15,null,null,null,null,[\"151127700\"]],[4,null,15,null,null,null,null,[\"8448570\"]],[4,null,15,null,null,null,null,[\"5302\"]],[4,null,15,null,null,null,null,[\"7811748\"]],[4,null,15,null,null,null,null,[\"138855687\"]],[4,null,15,null,null,null,null,[\"99287527\"]],[4,null,15,null,null,null,null,[\"19024548\"]],[4,null,15,null,null,null,null,[\"22897154128\"]]]]]],null,null,null,null,null,110,null,198,1],[10,[[31093114,[[729624434,null,null,[]],[725693774,null,null,[]],[732179798,null,null,[]],[null,729624435,null,[null,5000]]],[3,[[2,[[4,null,15,null,null,null,null,[\"6032\"]],[3,[[6,null,null,3,null,2],[1,[[4,null,59,null,null,null,null,[\"2004189832\"]]]]]]]],[4,null,15,null,null,null,null,[\"1012355\"]],[4,null,15,null,null,null,null,[\"9528481\"]],[4,null,15,null,null,null,null,[\"151127700\"]],[4,null,15,null,null,null,null,[\"8448570\"]],[4,null,15,null,null,null,null,[\"5302\"]],[4,null,15,null,null,null,null,[\"7811748\"]],[4,null,15,null,null,null,null,[\"138855687\"]],[4,null,15,null,null,null,null,[\"99287527\"]],[4,null,15,null,null,null,null,[\"19024548\"]],[4,null,15,null,null,null,null,[\"22897154128\"]]]]]],null,null,null,null,null,90,null,198,1],[10,[[31093115,[[729624434,null,null,[1]],[725693774,null,null,[1]],[732179798,null,null,[1]],[null,729624435,null,[null,5000]]],[3,[[2,[[4,null,15,null,null,null,null,[\"6032\"]],[3,[[6,null,null,3,null,2],[1,[[4,null,59,null,null,null,null,[\"2004189832\"]]]]]]]],[4,null,15,null,null,null,null,[\"1012355\"]],[4,null,15,null,null,null,null,[\"9528481\"]],[4,null,15,null,null,null,null,[\"151127700\"]],[4,null,15,null,null,null,null,[\"8448570\"]],[4,null,15,null,null,null,null,[\"5302\"]],[4,null,15,null,null,null,null,[\"7811748\"]],[4,null,15,null,null,null,null,[\"138855687\"]],[4,null,15,null,null,null,null,[\"99287527\"]],[4,null,15,null,null,null,null,[\"19024548\"]],[4,null,15,null,null,null,null,[\"22897154128\"]]]]]],null,null,null,null,null,100,null,198,1],[1,[[31093193],[31093194,[[null,753762684,null,[null,1500]]]],[31093195,[[null,753762684,null,[null,2000]]]]],null,null,null,null,null,990,null,217,1],[10,[[95342027],[95342028]],[4,null,83],129],[50,[[95349880],[95349881,[[null,null,null,null,[[[4,null,83],[null,null,null,[\"1 dbm\/(ad|clkk)\",\"2 (adsrvr|adserver)\\\\.org\/bid\/\",\"3 criteo.com\/(delivery|[a-z]+\/auction)\",\"4 yahoo.com\/bw\/[a-z]+\/imp\/\",\"5 (adnxs|adnxs-simple).com\/it\",\"6 amazon-adsystem.com\/[a-z\/]+\/impb\",\"7 temu.com\/api\/[a-z0-9]+\/ads\",\"8 temu.com\/[a-z0-9]+\/impr\"]]]],*********]]]],[4,null,83],129],[50,[[95351361],[95351362,[[null,null,null,null,[[[4,null,83],[]]],*********]]]],[4,null,83],129],[50,[[95351363],[95351364,[[null,null,null,null,[[[4,null,83],[]]],*********]]]],[4,null,83],129],[1,[[95357519],[95357520,[[null,null,null,null,[[[4,null,83],[null,null,null,[\"1 bidderRequests.bids bidder userIdAsEids.source\",\"2 bidderRequests.bids.userIdAsEids source provider\",\"3 bidderRequests.bids bidder ortb2Imp.ext.tid?\",\"5 bidderRequests.bids bidder mediaTypes.banner\",\"6 bidderRequests.bids bidder mediaTypes.native?\",\"7 bidderRequests.bids bidder mediaTypes.video\",\"8 bidderRequests.bids bidder ortb2Imp.ext.gpid?\",\"9 bidderRequests.bids bidder ortb2.site.content.data.ext.segment?\",\"10 bidderRequests.bids bidder ortb2.site.page\",\"11 bidderRequests.bids bidder ortb2.user.data.segment?\",\"12 bidderRequests.bids bidder ortb2.user.data.ext.segtax?\",\"13 bidsReceived adId creativeId\",\"14 bidderRequests.bids.userIdAsEids source uids.ext.provider\",\"15 bidderRequests.bids.userIdAsEids source uids.atype\",\"16 bidderRequests.bids.userIdAsEids source uids.length\",\"17 bidsReceived adId ttl\",\"18 bidsReceived adId meta.primaryCatId\",\"19 bidsReceived adId meta.secondaryCatIds\",\"22 bidsReceived adId ad\"]]]],*********]]]],[4,null,83],129],[20,[[95357665],[95357666],[95357667],[95357668],[95357669],[95357670]],[4,null,89],null,null,null,37,780,null,166,1],[1,[[95365417,null,[4,null,92,null,null,null,null,[\"userId\"]]]]],[1,[[95365418,null,[4,null,92,null,null,null,null,[\"chromeAiRtdProvider\"]]]]],[10,[[95366360],[95366361,[[null,null,null,null,[[[4,null,83],[null,null,null,[\"1 bidderRequests.bids bidder userIdAsEids.source\",\"2 bidderRequests.bids.userIdAsEids source provider\",\"3 bidderRequests.bids bidder ortb2Imp.ext.tid?\",\"5 bidderRequests.bids bidder mediaTypes.banner\",\"6 bidderRequests.bids bidder mediaTypes.native?\",\"7 bidderRequests.bids bidder mediaTypes.video\",\"8 bidderRequests.bids bidder ortb2Imp.ext.gpid?\",\"9 bidderRequests.bids bidder ortb2.site.content.data.segment?\",\"10 bidderRequests.bids bidder ortb2.site.page\",\"11 bidderRequests.bids bidder ortb2.user.data.segment?\",\"12 bidderRequests.bids bidder ortb2.user.data.ext.segtax?\",\"13 bidsReceived adId creativeId\",\"14 bidderRequests.bids.userIdAsEids source uids.ext.provider\",\"15 bidderRequests.bids.userIdAsEids source uids.atype\",\"16 bidderRequests.bids.userIdAsEids source uids.length\",\"17 bidsReceived adId ttl\",\"18 bidsReceived adId meta.primaryCatId\",\"19 bidsReceived adId meta.secondaryCatIds\"]]]],*********]]]],[4,null,83],129]]],[27,[[50,[[31090502,null,[2,[[4,null,59,null,null,null,null,[\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"*********\",\"**********\",\"**********\",\"*********\",\"**********\",\"**********\",\"**********\",\"77481481\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"*********\",\"*********\",\"*********\",\"**********\"]],[8,null,null,17,null,0]]]],[31090503,[[*********,null,null,[1]]],[2,[[4,null,59,null,null,null,null,[\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"*********\",\"**********\",\"**********\",\"*********\",\"**********\",\"**********\",\"**********\",\"77481481\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"*********\",\"*********\",\"*********\",\"**********\"]],[8,null,null,17,null,0]]]]]]]],[4,[[null,[[44714449,[[null,7,null,[null,1]]]],[676982961,[[null,7,null,[null,0.4]],[212,null,null,[1]]]],[676982996,[[null,7,null,[null,1]]]]],null,78]]]],null,null,[null,1000,1,1000]],null,null,null,null,\".google.co.ma\",246,null,null,null,null,null,[0,0,0],\"m202507100101\"]")
