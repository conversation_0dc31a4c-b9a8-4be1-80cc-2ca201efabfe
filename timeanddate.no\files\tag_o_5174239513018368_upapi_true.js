!function(){"use strict";var e=function(){return e=Object.assign||function(e){for(var t,n=1,s=arguments.length;n<s;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},e.apply(this,arguments)};function t(e,t,n,s){return new(n||(n=Promise))((function(r,i){function o(e){try{c(s.next(e))}catch(e){i(e)}}function a(e){try{c(s.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,a)}c((s=s.apply(e,t||[])).next())}))}function n(e,t){var n,s,r,i,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,s&&(r=2&i[0]?s.return:i[0]?s.throw||((r=s.return)&&r.call(s),0):s.next)&&!(r=r.call(s,i[1])).done)return r;switch(s=0,r&&(i=[2&i[0],r.value]),i[0]){case 0:case 1:r=i;break;case 4:return o.label++,{value:i[1],done:!1};case 5:o.label++,s=i[1],i=[0];continue;case 7:i=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==i[0]&&2!==i[0])){o=0;continue}if(3===i[0]&&(!r||i[1]>r[0]&&i[1]<r[3])){o.label=i[1];break}if(6===i[0]&&o.label<r[1]){o.label=r[1],r=i;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(i);break}r[2]&&o.ops.pop(),o.trys.pop();continue}i=t.call(e,o)}catch(e){i=[6,e],s=0}finally{n=r=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}function s(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var s=Array(e),r=0;for(t=0;t<n;t++)for(var i=arguments[t],o=0,a=i.length;o<a;o++,r++)s[r]=i[o];return s}var r,i=function(){function e(e){this.renderedElements=[],this.baitConfig=e}return e.prototype.detect=function(){return t(this,void 0,void 0,(function(){var e,t=this;return n(this,(function(n){return e=this.baitConfig.flatMap((function(e){return e.classList})),this.injectStyles(e),this.baitConfig.forEach((function(e){return t.injectBaitElements(e)})),[2,new Promise((function(e){var n=setTimeout((function(){e(),t.cleanup(),clearTimeout(n)}),5e3);requestAnimationFrame((function(){var n=t.baitConfig.flatMap((function(e){var n=e.detectionKey,s=e.mainClass,r=e.classList,i=e.allowedClassListMapping;return Object.entries(i).map((function(e){var i,o=e[0],a=e[1];return{name:o,type:"element",signals:(i={},i[n]=t.isBaitSuccessful(s,r,a),i)}}))}));e(n),t.cleanup()}))}))]}))}))},e.prototype.injectStyles=function(e){var t,n=document.createElement("style");n.innerHTML=e.map((function(e){return"."+e})).join(", ").concat(" { display: block !important; }"),null===(t=document.body||document.documentElement)||void 0===t||t.appendChild(n),this.renderedElements.push(n)},e.prototype.injectBaitElements=function(e){var t=this;e.classList.forEach((function(n){var s,r=document.createElement("div");r.className=e.mainClass+" "+n,r.style.cssText="display: block; width: 1px; height: 1px; position: absolute; border: none; margin: 0; padding: 0;",null===(s=document.body||document.documentElement)||void 0===s||s.appendChild(r),t.renderedElements.push(r)}))},e.prototype.getBaitResults=function(e,t,n){var r=function(e,t){return Object.fromEntries(t.map((function(t){return[t,e[t]]})))},i={},o=document.querySelectorAll("."+e);return s(t,n).forEach((function(e){var t=Array.from(o).find((function(t){return t.classList.contains(e)}));i[e]=function(e){var t;if(!e)return!0;var n=(null===(t=window.getComputedStyle)||void 0===t?void 0:t.call(window,e))||e.style;return"none"===(null==n?void 0:n.display)||"hidden"===(null==n?void 0:n.visibility)||null===e.offsetParent||0===e.offsetHeight||0===e.clientHeight}(t)})),{allowedListResult:r(i,t),blockedListResult:r(i,n)}},e.prototype.isBaitSuccessful=function(e,t,n){var s=t.filter((function(e){return!n.includes(e)})),r=this.getBaitResults(e,n,s),i=r.allowedListResult,o=r.blockedListResult;return Object.values(i).every((function(e){return!e}))&&Object.values(o).every((function(e){return e}))},e.prototype.cleanup=function(){this.renderedElements.forEach((function(e){return e.remove()})),this.renderedElements=[]},e}(),o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";function a(e,t,n){if(void 0===e&&(e=8),void 0===t&&(t=10),void 0===n&&(n=o),!n||"number"!=typeof e||"number"!=typeof t||e<=0||t<=0||e>t)throw new TypeError;for(var s=Math.floor(Math.random()*(t+1-e))+e,r="",i=0;i<s;i++)r+=n.charAt(Math.floor(Math.random()*n.length));return r}function c(){return a()+"-"+a()+"-"+Date.now().toString(16).slice(-10)}!function(e){e[e.soft=0]="soft",e[e.hard=1]="hard",e[e.adaptive=2]="adaptive"}(r||(r={}));var l=function(){var e=window;try{if(top.document)return top}catch(e){}try{for(;e.parent.document;)e=e.parent}catch(e){}return e}();var u=function(){try{return""!==document.location.hostname&&document.location.hostname!==window.parent.location.hostname}catch(e){return!0}}();function d(){try{return new URL(u?l.document.referrer:l.location.href)}catch(e){return new URL(l.location.href)}}var p,h,f,g,v,m,b=d();function w(e){if(u){var t=l.sessionStorage.getItem(e);switch(e){case"BT_pvSent":case"BT_trustedIframeInjected":var n=void 0;try{n=JSON.parse(t)}catch(e){}return _(n)?n:{};default:return t}}}function y(e,t){if(u){var n=void 0;switch(e){case"BT_pvSent":case"BT_trustedIframeInjected":_(t)&&(n=JSON.stringify(t));break;default:n=t}n&&"string"==typeof n&&l.sessionStorage.setItem(e,n)}}function _(e){return e&&"object"==typeof e&&!Array.isArray(e)}function I(e){return Math.floor(Math.random()*Math.floor(e))}function E(e){return I(100)<e}function S(){try{if(window.__bt=window.__bt||{},"function"==typeof window.__bt.customDetectDomain)return window.__bt.customDetectDomain()}catch(e){}}function L(e){var t;if(window.TextEncoder)t=(new window.TextEncoder).encode(JSON.stringify(e)).buffer;else{for(var n=unescape(encodeURIComponent(JSON.stringify(e))),s=new Uint8Array(n.length),r=0;r<n.length;r++)s[r]=n.charCodeAt(r);t=s.buffer}return t}function C(e,t){var n,s,r;if(e){var i=document.createElement("script");i.type=null!==(n=null==t?void 0:t.type)&&void 0!==n?n:"text/javascript",i.async=!0,i.src=e,null!==(null==t?void 0:t.crossOrigin)&&(i.crossOrigin=null!==(s=null==t?void 0:t.crossOrigin)&&void 0!==s?s:"anonymous"),null===(r=window.document.head||window.document.body||window.document.documentElement)||void 0===r||r.appendChild(i)}}function A(e){var t=e.eventName,n=e.payload,s=n.bubbles,r=void 0!==s&&s,i=n.cancelable,o=void 0!==i&&i,a=n.detail,c=void 0===a?void 0:a;if(window.CustomEvent)try{var l=new window.CustomEvent(t,{bubbles:r,cancelable:o,detail:c});return void window.dispatchEvent(l)}catch(e){}var u=document.createEvent("CustomEvent");u.initCustomEvent(t,r,o,c),window.dispatchEvent(u)}function D(e,t){var n,s;void 0===t&&(t=!1);var r=(s=t?performance.getEntriesByName(e,"resource"):null===(n=performance.getEntriesByType("resource"))||void 0===n?void 0:n.filter((function(t){return t.name.includes(e)})))[s.length-1];if(!r)return null;var i=r,o=i.transferSize,a=i.duration;return 0===o?a<1?2:1:0}h="5174239513018368",f="btloader.com",g="api.btloader.com",v="2.1.124-1-gf72dfa3";try{m={"org":{"enabled":false,"allow_render_to_aa_users":true,"script_loading_mode":"inject_from_tag_script","wall_mode":0},"websites":{},"script":"https://cdn.btmessage.com/script/rlink.js?o=5174239513018368&bt_env=prod"}}catch(e){console.error("Error parsing RL settings",e),m={script:"",org:{enabled:!1,script_loading_mode:"inject_from_tag_script",wall_mode:r.soft,allow_render_to_aa_users:!0}}}var O={},R={"jammo.co.uk":{"ce":false,"me":false,"w":"5978163841073152","widget":false,"a":false},"timeanddate.com":{"ce":true,"me":true,"w":"5557713386340352","widget":false,"a":false}},T={countryCode:"ZZ",isRestricted:!1};try{T=[[CountryData]]}catch(e){}var P=new Proxy(new URLSearchParams(window.location.search),{get:function(e,t){return e.get(t)}});!function(){var e="BT_pvSent",t="BT_traceID",n="BT_trustedIframeInjected";u?l.addEventListener("unload",(function(){l.sessionStorage.removeItem(e),l.sessionStorage.removeItem(t),l.sessionStorage.removeItem(n)})):(l.sessionStorage.removeItem(e),l.sessionStorage.removeItem(t),l.sessionStorage.removeItem(n))}();var V=(null===(p=l.__bt_intrnl)||void 0===p?void 0:p.traceID)||w("BT_traceID")||c();y("BT_traceID",V);var N=l.sessionStorage.getItem("BT_sid");N||(N=c(),l.sessionStorage.setItem("BT_sid",N));var k={traceID:V,sessionID:N};var x=function(){var e,s,r,i,o,a,c,u,d;return t(this,void 0,void 0,(function(){var t,p,w,y,_,I,C,A,D,O,T;return n(this,(function(n){switch(n.label){case 0:if(t={websiteID:void 0,contentEnabled:!1,mobileContentEnabled:!1,widget:!1},p=function(e,t){e=e.replace(/[\[\]]/g,"\\$&");var n=new RegExp("[?&]"+e+"(=([^&#]*)|&|#|$)").exec(t);return n?n[2]?decodeURIComponent(n[2].replace(/\+/g," ")):"":null},w=S()||function(){var e,t=b.hostname;try{if("disqusservice.com"===t){var n=l.location.href;t=(null!=(e=p("sourceUrl",n).match(/:\/\/(www[0-9]?\.)?(.[^/:]+)/i))&&e.length>2&&"string"==typeof e[2]&&e[2].length>0?e[2]:null)+"-disqus"}}catch(e){t=l.location.hostname}return 0===t.indexOf("www.")&&(t=t.replace("www.","")),t}(),y=!1,0!==Object.keys(R).length)return[3,5];(_=new URL("https://"+f+"/websiteconfig")).searchParams.set("bt_env",P.bt_env||"prod"),_.searchParams.set("o",h),_.searchParams.set("w",w),n.label=1;case 1:return n.trys.push([1,4,,5]),[4,fetch(_.toString())];case 2:if(!(I=n.sent()).ok)throw new Error;return[4,I.json()];case 3:return C=n.sent(),t.websiteID=C.tagData.w,t.contentEnabled=C.tagData.ce,t.mobileContentEnabled=C.tagData.me,t.widget=null!==(e=C.tagData.widget)&&void 0!==e&&e,[2,{siteInfo:t,bundleData:C.bundleData,rlSettings:C.messageWall,checksum:C.checksum}];case 4:return n.sent(),[3,5];case 5:return w&&w in R?(D=R[w],t.websiteID=null!==(s=D.w)&&void 0!==s?s:D.website_id,t.contentEnabled=null!==(r=D.ce)&&void 0!==r?r:D.content_enabled,t.mobileContentEnabled=null!==(i=D.me)&&void 0!==i?i:D.mobile_content_enabled,t.widget=null!==(o=D.widget)&&void 0!==o&&o,y=!0):(A=Object.keys(R).reduce((function(e,t){var n=t.toLowerCase(),s=w.indexOf(n)>-1?w.indexOf(n):l.location.hostname.indexOf(n);return s>-1&&(!e||s<e.indexInHostname||s===e.indexInHostname&&t.length>e.site.length)?{site:t,indexInHostname:s}:e}),null))&&(y=!0,D=R[A.site],t.websiteID=null!==(a=D.w)&&void 0!==a?a:D.website_id,t.contentEnabled=null!==(c=D.ce)&&void 0!==c?c:D.content_enabled,t.mobileContentEnabled=null!==(u=D.me)&&void 0!==u?u:D.mobile_content_enabled,t.widget=null!==(d=D.widget)&&void 0!==d&&d),y?(O=function(e){var t;return(null===(t=null==m?void 0:m.websites)||void 0===t?void 0:t[e])||(null==m?void 0:m.org)}(t.websiteID),[2,{siteInfo:t,rlSettings:O}]):(E(1)&&(T={domain:w,orgID:parseInt(h)},function(e,t){new Promise((function(n,s){var r=new window.XMLHttpRequest;r.open("POST",e,!0),r.onerror=function(){s("Error in get request")},r.onload=function(){n(r.responseText)},r.send(t)}))}("https://"+g+"/events/unknown_domains?upapi=true&tid="+encodeURIComponent(k.traceID)+"&cv="+encodeURIComponent(v),L(T))),[2,{siteInfo:t}])}}))}))}();function U(){return x}x.then((function(e){window.__bt_tag_d={orgID:h,domain:f,siteInfo:e.siteInfo,apiDomain:g,version:v,websitesData:R}}));var B,F,M="nlf";function j(t,n){var s;void 0===t&&(t="/"),void 0===n&&(n={});var r=t;r.startsWith("/")||(r="/"+r);var i=e(e({},n),((s={}).tid=k.traceID,s.sid=k.sessionID,s.cv=v,s.upapi="true",s)),o="?";for(var a in i)if(i.hasOwnProperty(a)){var c=i[a];r+=""+o+a+"="+encodeURIComponent(c),o="&"}return"https://"+g+r}function H(e){return t(this,void 0,void 0,(function(){var t,s,r,i,o,a,c,u,d,p,f,g,v,m,w;return n(this,(function(n){return t=e.config,s=e.aaDetectionResults,r=e.bundleIDToLoad,i=e.isSPAViewChange,o=e.baitResults,a=e.consent,c=e.privateMode,u=t.siteInfo,d=t.checksum,p=s.ab,f=s.acceptable,g=s.nlf,(w={})[M]=!!g,v=w,m={w:u.websiteID,o:h,widget:u.widget,r:p,vr:{w:l.innerWidth,h:l.innerHeight},pageURL:b.href,checksum:d,spa_view_change:i},r&&(m.bv=parseInt(r)),p&&(m.rt=0,m.aa=f),(null==o?void 0:o.length)&&(m.br=o),null!==a&&(m.c=a),null!==c&&(m.pm=c),[2,fetch(j("/pv",v),{method:"POST",body:L(m)})]}))}))}function G(e){var t=e.siteInfo,n=e.extensions,s={websiteID:t.websiteID,orgID:h,pageUrl:b.href,extensions:n};return fetch(j("/ex"),{method:"POST",body:L(s)})}function K(e,s,r){return void 0===s&&(s={}),void 0===r&&(r={}),t(this,void 0,void 0,(function(){var t,i;return n(this,(function(n){switch(n.label){case 0:return r.sendPercentage&&!E(r.sendPercentage)?[3,2]:[4,U()];case 1:t=n.sent(),i={event:e,meta:s,orgID:h,severity:r.severity||1,websiteID:t.siteInfo.websiteID},fetch(j("/log"),{method:"POST",body:L(i)}),n.label=2;case 2:return[2]}}))}))}var W,z=Date.now(),X=window.performance.getEntriesByType("resource").find((function(e){return e.name.includes(document.currentScript.getAttribute("src"))})),q=Math.round(Date.now()-(null!==(F=null!==(B=null==X?void 0:X.startTime)&&void 0!==B?B:0+(null==X?void 0:X.duration))&&void 0!==F?F:0)),$=X&&E(1),J={shouldLog:$,tagScriptLoadTimestamp:z,navigationStartTimestamp:q,events:[]};function Q(e){var t=e||{};Z("timing.trustedIframeEnd",{status:t.status,details:t.details,timestamp:Date.now()})}function Y(t){var n=Date.now();return function(s){var r=Date.now(),i=e(e({},s),{eventDurationMS:r-n,timestamp:r});Z(t,i)}}function Z(e,t){void 0===t&&(t={}),$&&K(e,t),J.events.push({event:e,meta:t})}var ee,te="https://ad-delivery.net/px.gif?ch=2",ne="https://ad.doubleclick.net/favicon.ico?ad=300x250&ad_box_=1&adnet=1&showad=1&size=250x250",se="https://ad-delivery.net/px.gif?ch=1&e="+Math.random(),re="https://ag.dns-finder.com/meta/dns",ie=!1,oe={ADBLOCK_CLIENT_DETECTION:null,ADBLOCK_BAIT_PIXEL_URL:null,ADBLOCK_BAIT_PIXEL_OLD_URL:null,ADBLOCK_BAIT_ELEMENT:null,AA_BAIT_PIXEL_URL:null,NLF_BAIT_PIXEL_URL:null},ae=((W={})[se]="timing.adDeliveryAADetection",W[te]="timing.adDeliveryABDetection",W[ne]="timing.doubleclickABDetection",W),ce=null,le=new Promise((function(e){ce=e}));function ue(e){var t=e||{},n=t.ab,s=t.acceptable,r=t.nlf;return n&&s||r&&ie}function de(){return t(this,void 0,void 0,(function(){var s=this;return n(this,(function(r){return[2,new Promise((function(r){return t(s,void 0,void 0,(function(){var t,s,i,o,a,c,u,d,p,h,f;return n(this,(function(n){switch(n.label){case 0:return t=Y("timing.ABDetection"),s="BT_AA_DETECTION",i=l.localStorage&&JSON.parse(l.localStorage.getItem(s)),(o=ue(i))&&(t({cached:!0}),r(e(e({},i),{isCached:!0}))),[4,Promise.all([pe(),he(),ge()])];case 1:return a=n.sent(),c=a[0],u=a[1],d=a[2],p={ab:c,acceptable:u,nlf:d},null===(f=l.localStorage)||void 0===f||f.setItem(s,JSON.stringify(p)),window.__bt_intrnl.aaDetectionResults=p,h=ue(p),o&&!h&&(l.__bt_intrnl.stopFlag=!0),t({cached:!1}),null==ce||ce(p),r(p),[2]}}))}))}))]}))}))}function pe(){var e;return t(this,void 0,void 0,(function(){var t,s,r,i,o;return n(this,(function(n){switch(n.label){case 0:return n.trys.push([0,3,,4]),"function"!=typeof(null===(e=window.__bt)||void 0===e?void 0:e.customDetectAdBlock)?[3,2]:[4,window.__bt.customDetectAdBlock()];case 1:return t=n.sent(),oe.ADBLOCK_CLIENT_DETECTION=t,[2,t];case 2:return[3,4];case 3:return n.sent(),[3,4];case 4:return[4,Promise.all([fe(te),fe(ne)])];case 5:return s=n.sent(),r=s[0],i=s[1],o=function(){var e,t,n=document.createElement("div");n.innerHTML="&nbsp;",n.className="ad_row adbannertop ad-mobile ad_sidebar adpopup boxad contentAd",n.style.cssText="width: 1px !important; height: 1px !important; position: absolute !important; left: -5000px !important; top: -5000px !important;",null===(e=document.body||document.documentElement)||void 0===e||e.appendChild(n);var s=(null===(t=window.getComputedStyle)||void 0===t?void 0:t.call(window,n))||n.style,r="none"===(null==s?void 0:s.display)||"hidden"===(null==s?void 0:s.visibility)||null===n.offsetParent||0===n.offsetHeight||0===n.clientHeight;return n.remove(),r}(),oe.ADBLOCK_BAIT_PIXEL_URL=r,oe.ADBLOCK_BAIT_PIXEL_OLD_URL=i,oe.ADBLOCK_BAIT_ELEMENT=o,[2,r?o||i:o&&i]}}))}))}function he(){return t(this,void 0,void 0,(function(){var e;return n(this,(function(t){switch(t.label){case 0:return[4,fe(se)];case 1:return e=t.sent(),oe.AA_BAIT_PIXEL_URL=!e,[2,!e]}}))}))}function fe(e){return new Promise((function(t){var n,s=ae[e]&&Y(ae[e]),r=document.createElement("img");r.src=e,r.style.setProperty("display","none","important"),r.style.setProperty("width","1px","important"),r.style.setProperty("height","1px","important"),r.addEventListener("error",(function(){return s&&s({cacheLevel:D(e,!0)}),t(!0)})),r.addEventListener("load",(function(){return s&&s({cacheLevel:D(e,!0)}),t(!1)})),null===(n=document.body||document.documentElement)||void 0===n||n.appendChild(r)}))}function ge(){return t(this,void 0,void 0,(function(){var e,t;return n(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,fetch(re)];case 1:return e=n.sent(),t="ag"===e.headers.get("X-Resolver"),oe.NLF_BAIT_PIXEL_URL=t,[2,t];case 2:return n.sent(),[2,!1];case 3:return[2]}}))}))}function ve(){return t(this,void 0,void 0,(function(){return n(this,(function(e){return[2,new i([{detectionKey:"ab",mainClass:"content-showcase-itru-marvelous-3d",classList:["a3dh4dk","t7kduje","o9db5t","p05jdbn","b9dkjdj","i1er4h4"],allowedClassListMapping:{"abp-safari-ios":["b9dkjdj","i1er4h4"],"abp-safari-desktop":["t7kduje","p05jdbn"],"webextension-desktop":["o9db5t","b9dkjdj"]}},{detectionKey:"aa",mainClass:"content-showcase-itru-sufficient-2d",classList:["m5c2t8q","v8n6y4p","w3p9m6d","k2y8f7x","q7d5z1l","x9a7b3k"],allowedClassListMapping:{"element-aa-detection":["q7d5z1l","x9a7b3k"]}}]).detect()]}))}))}function me(e){if(!e)return 0;var t=0;for(var n in e)if(e.hasOwnProperty(n)){var s=e[n].max;s>t&&(t=s)}return t/100}function be(e,t){if(void 0===t&&(t={}),!e)return null;var n=0,s=1,r=me(t);return 1===r||(Object.values(t).length&&r<1&&(n=r,s=1-r),Object.keys(e).sort().forEach((function(r){var i=e[r];t[r]={min:Math.trunc(100*n),max:Math.trunc(100*(n+i*s))},n+=i*s}))),t}function we(e){var t=e.siteInfo,n=e.bundleData,s="latest",r="BT_BUNDLE_VERSION_"+t.websiteID,i="BT_DIGEST_VERSION_"+t.websiteID;!ee[t.websiteID]&&(null==n?void 0:n.bundles)&&(ee[t.websiteID]=n);var o=function(e,t,n){if(void 0===n&&(n="latest"),!e)return null;if(!t||"object"!=typeof t||Object.keys(t).length<=0)return null;var s={},r=t[e],i=t[0],o=t.global;if((null==r?void 0:r.bundles)&&(s=be(r.bundles)),(null==i?void 0:i.bundles)&&(s=be(i.bundles,s)),(null==o?void 0:o.bundles)&&(s=be(o.bundles,s)),Object.keys(s).length<=0)return null;var a=me(s);return a<1&&(s[n]={min:Math.trunc(100*a),max:100}),s}(t.websiteID,ee,s);if(window.__bt_tag_d&&(window.__bt_tag_d.probabilities=o),o&&!(Object.keys(o).length<=0)){var a=ee[t.websiteID]||ee[0]||ee.global;if(a){var c=a.digest;l.localStorage.getItem(i)!==String(c)&&(l.localStorage.setItem(i,String(c)),l.localStorage.removeItem(r));var u=l.localStorage.getItem(r);if(!u)u=function(e,t){if("number"!=typeof e||e<0||e>100)return null;if(!t||"object"!=typeof t)return null;for(var n=Object.keys(t),s=0;s<n.length;s++){var r=n[s],i=t[r];if(i.min<=e&&i.max>e)return r}return null}(I(100),o)||s,l.localStorage.setItem(r,u);return u===s?void 0:u}}}ee={};var ye=[];try{ye=[[RestrictedCountriesData]]}catch(e){}var _e=5;function Ie(e){var t,n;if("ZZ"===T.countryCode){if("function"==typeof window.__bt.customDetectCountry){var s=null===(n=null===(t=window.__bt.customDetectCountry())||void 0===t?void 0:t.toUpperCase)||void 0===n?void 0:n.call(t);if(2===s.length)return void Ee(s,ye.includes(s))}var r=Y("timing.countryDetection"),i=l.localStorage.getItem("btUserCountryExpiry"),o="https://"+g+"/country?o="+e;!i||parseInt(i)<=Date.now()?fetch(o).then((function(e){return e.json()})).then((function(e){Ee(e.country,e.isRestricted),r({cached:!1,cacheLevel:D(o,!0)})})).catch((function(){r({cached:!1,error:!0,cacheLevel:D(o,!0)})})):r({cached:!0,cacheLevel:D(o,!0)})}else Ee(T.countryCode,T.isRestricted)}function Ee(e,t){l.localStorage.setItem("btUserCountry",e),l.localStorage.setItem("btUserCountryExpiry",String(new Date(Date.now()+6e4*_e).getTime())),l.localStorage.setItem("btUserIsFromRestrictedCountry",String(t))}var Se,Le,Ce,Ae,De,Oe=function(){function e(e,t,n,s,i){void 0===e&&(e={enabled:!1,script_loading_mode:"inject_from_tag_script",wall_mode:r.soft,allow_render_to_aa_users:!0}),this.rlinkSettings=e,this.scriptSrc=t,this.insertScript=n,this.widget=s,this.isAAEnabled=i}return e.prototype.load=function(){this.shouldLoadRLinkScript()&&(this.cacheRLinkSettings(),this.insertScript(this.scriptSrc,{type:"module"}),window.__bt_rlink_loaded_from_tag=!0)},e.prototype.preventRecovery=function(){var e=!this.rlinkSettings.allow_render_to_aa_users&&this.isAAEnabled,t=this.rlinkSettings.wall_mode===r.hard;return this.rlinkSettings.enabled&&t&&!e},e.prototype.isRecoveryAttributionValid=function(){var e=localStorage.getItem("BT_AM_ATTRIBUTION_EXPIRY");return!!e&&Date.now()<Number(e)},e.prototype.shouldLoadRLinkScript=function(){var e=window.top!==window.self,t=this.scriptSrc&&"inject_from_tag_script"===this.rlinkSettings.script_loading_mode&&!this.widget&&!e;return this.rlinkSettings.enabled?t:t&&this.isRecoveryAttributionValid()},e.prototype.cacheRLinkSettings=function(){window.__bt_tag_am={settings:this.rlinkSettings}},e}();class Re extends Error{constructor(e){super(e),this.name="DecodingError"}}class Te extends Error{constructor(e){super(e),this.name="EncodingError"}}class Pe extends Error{constructor(e){super(e),this.name="GVLError"}}class Ve extends Error{constructor(e,t){super(`invalid value ${t} passed for ${e} ${arguments.length>2&&void 0!==arguments[2]?arguments[2]:""}`),this.name="TCModelError"}}class Ne{static DICT="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";static REVERSE_DICT=new Map([["A",0],["B",1],["C",2],["D",3],["E",4],["F",5],["G",6],["H",7],["I",8],["J",9],["K",10],["L",11],["M",12],["N",13],["O",14],["P",15],["Q",16],["R",17],["S",18],["T",19],["U",20],["V",21],["W",22],["X",23],["Y",24],["Z",25],["a",26],["b",27],["c",28],["d",29],["e",30],["f",31],["g",32],["h",33],["i",34],["j",35],["k",36],["l",37],["m",38],["n",39],["o",40],["p",41],["q",42],["r",43],["s",44],["t",45],["u",46],["v",47],["w",48],["x",49],["y",50],["z",51],["0",52],["1",53],["2",54],["3",55],["4",56],["5",57],["6",58],["7",59],["8",60],["9",61],["-",62],["_",63]]);static BASIS=6;static LCM=24;static encode(e){if(!/^[0-1]+$/.test(e))throw new Te("Invalid bitField");const t=e.length%this.LCM;e+=t?"0".repeat(this.LCM-t):"";let n="";for(let t=0;t<e.length;t+=this.BASIS)n+=this.DICT[parseInt(e.substr(t,this.BASIS),2)];return n}static decode(e){if(!/^[A-Za-z0-9\-_]+$/.test(e))throw new Re("Invalidly encoded Base64URL string");let t="";for(let n=0;n<e.length;n++){const s=this.REVERSE_DICT.get(e[n]).toString(2);t+="0".repeat(this.BASIS-s.length)+s}return t}}class ke{clone(){const e=new this.constructor;return Object.keys(this).forEach((t=>{const n=this.deepClone(this[t]);void 0!==n&&(e[t]=n)})),e}deepClone(e){const t=typeof e;if("number"===t||"string"===t||"boolean"===t)return e;if(null!==e&&"object"===t){if("function"==typeof e.clone)return e.clone();if(e instanceof Date)return new Date(e.getTime());if(void 0!==e[Symbol.iterator]){const t=[];for(const n of e)t.push(this.deepClone(n));return e instanceof Array?t:new e.constructor(t)}{const t={};for(const n in e)e.hasOwnProperty(n)&&(t[n]=this.deepClone(e[n]));return t}}}}class xe extends ke{root=null;getRoot(){return this.root}isEmpty(){return!this.root}add(e){const t={value:e,left:null,right:null};let n;if(this.isEmpty())this.root=t;else for(n=this.root;;)if(e<n.value){if(null===n.left){n.left=t;break}n=n.left}else{if(!(e>n.value))break;if(null===n.right){n.right=t;break}n=n.right}}get(){const e=[];let t=this.root;for(;t;)if(t.left){let n=t.left;for(;n.right&&n.right!=t;)n=n.right;n.right==t?(n.right=null,e.push(t.value),t=t.right):(n.right=t,t=t.left)}else e.push(t.value),t=t.right;return e}contains(e){let t=!1,n=this.root;for(;n;){if(n.value===e){t=!0;break}e>n.value?n=n.right:e<n.value&&(n=n.left)}return t}min(){let e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.root;for(;t;)t.left?t=t.left:(e=t.value,t=null);return e}max(){let e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.root;for(;t;)t.right?t=t.right:(e=t.value,t=null);return e}remove(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.root,n=null,s="left";for(;t;)if(e<t.value)n=t,t=t.left,s="left";else if(e>t.value)n=t,t=t.right,s="right";else{if(t.left||t.right)if(t.left)if(t.right){const e=this.min(t.right);this.remove(e,t.right),t.value=e}else n?n[s]=t.left:this.root=t.left;else n?n[s]=t.right:this.root=t.right;else n?n[s]=null:this.root=null;t=null}}static build(e){if(e&&0!==e.length){if(1===e.length){const t=new xe;return t.add(e[0]),t}{const t=e.length>>1,n=new xe;n.add(e[t]);const s=n.getRoot();if(s){if(t+1<e.length){const n=xe.build(e.slice(t+1));s.right=n?n.getRoot():null}if(t-1>0){const n=xe.build(e.slice(0,t-1));s.left=n?n.getRoot():null}}return n}}return null}}class Ue{static langSet=new Set(["BG","CA","CS","DA","DE","EL","EN","ES","ET","FI","FR","HR","HU","IT","JA","LT","LV","MT","NL","NO","PL","PT","RO","RU","SK","SL","SV","TR","ZH"]);has(e){return Ue.langSet.has(e)}forEach(e){Ue.langSet.forEach(e)}get size(){return Ue.langSet.size}}class Be{static cmpId="cmpId";static cmpVersion="cmpVersion";static consentLanguage="consentLanguage";static consentScreen="consentScreen";static created="created";static supportOOB="supportOOB";static isServiceSpecific="isServiceSpecific";static lastUpdated="lastUpdated";static numCustomPurposes="numCustomPurposes";static policyVersion="policyVersion";static publisherCountryCode="publisherCountryCode";static publisherCustomConsents="publisherCustomConsents";static publisherCustomLegitimateInterests="publisherCustomLegitimateInterests";static publisherLegitimateInterests="publisherLegitimateInterests";static publisherConsents="publisherConsents";static publisherRestrictions="publisherRestrictions";static purposeConsents="purposeConsents";static purposeLegitimateInterests="purposeLegitimateInterests";static purposeOneTreatment="purposeOneTreatment";static specialFeatureOptins="specialFeatureOptins";static useNonStandardStacks="useNonStandardStacks";static vendorConsents="vendorConsents";static vendorLegitimateInterests="vendorLegitimateInterests";static vendorListVersion="vendorListVersion";static vendorsAllowed="vendorsAllowed";static vendorsDisclosed="vendorsDisclosed";static version="version"}!function(e){e[e.NOT_ALLOWED=0]="NOT_ALLOWED",e[e.REQUIRE_CONSENT=1]="REQUIRE_CONSENT",e[e.REQUIRE_LI=2]="REQUIRE_LI"}(Se||(Se={}));class Fe extends ke{static hashSeparator="-";purposeId_;restrictionType;constructor(e,t){super(),void 0!==e&&(this.purposeId=e),void 0!==t&&(this.restrictionType=t)}static unHash(e){const t=e.split(this.hashSeparator),n=new Fe;if(2!==t.length)throw new Ve("hash",e);return n.purposeId=parseInt(t[0],10),n.restrictionType=parseInt(t[1],10),n}get hash(){if(!this.isValid())throw new Error("cannot hash invalid PurposeRestriction");return`${this.purposeId}${Fe.hashSeparator}${this.restrictionType}`}get purposeId(){return this.purposeId_}set purposeId(e){this.purposeId_=e}isValid(){return Number.isInteger(this.purposeId)&&this.purposeId>0&&(this.restrictionType===Se.NOT_ALLOWED||this.restrictionType===Se.REQUIRE_CONSENT||this.restrictionType===Se.REQUIRE_LI)}isSameAs(e){return this.purposeId===e.purposeId&&this.restrictionType===e.restrictionType}}class Me extends ke{bitLength=0;map=new Map;gvl_;has(e){return this.map.has(e)}isOkToHave(e,t,n){let s=!0;if(this.gvl?.vendors){const r=this.gvl.vendors[n];if(r)if(e===Se.NOT_ALLOWED)s=r.legIntPurposes.includes(t)||r.purposes.includes(t);else if(r.flexiblePurposes.length)switch(e){case Se.REQUIRE_CONSENT:s=r.flexiblePurposes.includes(t)&&r.legIntPurposes.includes(t);break;case Se.REQUIRE_LI:s=r.flexiblePurposes.includes(t)&&r.purposes.includes(t)}else s=!1;else s=!1}return s}add(e,t){if(this.isOkToHave(t.restrictionType,t.purposeId,e)){const n=t.hash;this.has(n)||(this.map.set(n,new xe),this.bitLength=0),this.map.get(n).add(e)}}restrictPurposeToLegalBasis(e){const t=this.gvl.vendorIds,n=e.hash,s=function(){let e;for(e of t);return e}(),r=[...Array(s).keys()].map((e=>e+1));for(let e=1;e<=s;e++)this.has(n)||(this.map.set(n,xe.build(r)),this.bitLength=0),this.map.get(n).add(e)}getVendors(e){let t=[];if(e){const n=e.hash;this.has(n)&&(t=this.map.get(n).get())}else{const e=new Set;this.map.forEach((t=>{t.get().forEach((t=>{e.add(t)}))})),t=Array.from(e)}return t}getRestrictionType(e,t){let n;return this.getRestrictions(e).forEach((e=>{e.purposeId===t&&(void 0===n||n>e.restrictionType)&&(n=e.restrictionType)})),n}vendorHasRestriction(e,t){let n=!1;const s=this.getRestrictions(e);for(let e=0;e<s.length&&!n;e++)n=t.isSameAs(s[e]);return n}getMaxVendorId(){let e=0;return this.map.forEach((t=>{e=Math.max(t.max(),e)})),e}getRestrictions(e){const t=[];return this.map.forEach(((n,s)=>{e?n.contains(e)&&t.push(Fe.unHash(s)):t.push(Fe.unHash(s))})),t}getPurposes(){const e=new Set;return this.map.forEach(((t,n)=>{e.add(Fe.unHash(n).purposeId)})),Array.from(e)}remove(e,t){const n=t.hash,s=this.map.get(n);s&&(s.remove(e),s.isEmpty()&&(this.map.delete(n),this.bitLength=0))}set gvl(e){this.gvl_||(this.gvl_=e,this.map.forEach(((e,t)=>{const n=Fe.unHash(t);e.get().forEach((t=>{this.isOkToHave(n.restrictionType,n.purposeId,t)||e.remove(t)}))})))}get gvl(){return this.gvl_}isEmpty(){return 0===this.map.size}get numRestrictions(){return this.map.size}}!function(e){e.COOKIE="cookie",e.WEB="web",e.APP="app"}(Le||(Le={})),function(e){e.CORE="core",e.VENDORS_DISCLOSED="vendorsDisclosed",e.VENDORS_ALLOWED="vendorsAllowed",e.PUBLISHER_TC="publisherTC"}(Ce||(Ce={}));class je{static ID_TO_KEY=[Ce.CORE,Ce.VENDORS_DISCLOSED,Ce.VENDORS_ALLOWED,Ce.PUBLISHER_TC];static KEY_TO_ID={[Ce.CORE]:0,[Ce.VENDORS_DISCLOSED]:1,[Ce.VENDORS_ALLOWED]:2,[Ce.PUBLISHER_TC]:3}}class He extends ke{bitLength=0;maxId_=0;set_=new Set;*[Symbol.iterator](){for(let e=1;e<=this.maxId;e++)yield[e,this.has(e)]}values(){return this.set_.values()}get maxId(){return this.maxId_}has(e){return this.set_.has(e)}unset(e){Array.isArray(e)?e.forEach((e=>this.unset(e))):"object"==typeof e?this.unset(Object.keys(e).map((e=>Number(e)))):(this.set_.delete(Number(e)),this.bitLength=0,e===this.maxId&&(this.maxId_=0,this.set_.forEach((e=>{this.maxId_=Math.max(this.maxId,e)}))))}isIntMap(e){let t="object"==typeof e;return t=t&&Object.keys(e).every((t=>{let n=Number.isInteger(parseInt(t,10));return n=n&&this.isValidNumber(e[t].id),n=n&&void 0!==e[t].name,n})),t}isValidNumber(e){return parseInt(e,10)>0}isSet(e){let t=!1;return e instanceof Set&&(t=Array.from(e).every(this.isValidNumber)),t}set(e){if(Array.isArray(e))e.forEach((e=>this.set(e)));else if(this.isSet(e))this.set(Array.from(e));else if(this.isIntMap(e))this.set(Object.keys(e).map((e=>Number(e))));else{if(!this.isValidNumber(e))throw new Ve("set()",e,"must be positive integer array, positive integer, Set<number>, or IntMap");this.set_.add(e),this.maxId_=Math.max(this.maxId,e),this.bitLength=0}}empty(){this.set_=new Set}forEach(e){for(let t=1;t<=this.maxId;t++)e(this.has(t),t)}get size(){return this.set_.size}setAll(e){this.set(e)}}class Ge{static[Be.cmpId]=12;static[Be.cmpVersion]=12;static[Be.consentLanguage]=12;static[Be.consentScreen]=6;static[Be.created]=36;static[Be.isServiceSpecific]=1;static[Be.lastUpdated]=36;static[Be.policyVersion]=6;static[Be.publisherCountryCode]=12;static[Be.publisherLegitimateInterests]=24;static[Be.publisherConsents]=24;static[Be.purposeConsents]=24;static[Be.purposeLegitimateInterests]=24;static[Be.purposeOneTreatment]=1;static[Be.specialFeatureOptins]=12;static[Be.useNonStandardStacks]=1;static[Be.vendorListVersion]=12;static[Be.version]=6;static anyBoolean=1;static encodingType=1;static maxId=16;static numCustomPurposes=6;static numEntries=12;static numRestrictions=12;static purposeId=6;static restrictionType=2;static segmentType=3;static singleOrRange=1;static vendorId=16}class Ke{static encode(e){return String(Number(e))}static decode(e){return"1"===e}}class We{static encode(e,t){let n;if("string"==typeof e&&(e=parseInt(e,10)),n=e.toString(2),n.length>t||e<0)throw new Te(`${e} too large to encode into ${t}`);return n.length<t&&(n="0".repeat(t-n.length)+n),n}static decode(e,t){if(t!==e.length)throw new Re("invalid bit length");return parseInt(e,2)}}class ze{static encode(e,t){return We.encode(Math.round(e.getTime()/100),t)}static decode(e,t){if(t!==e.length)throw new Re("invalid bit length");const n=new Date;return n.setTime(100*We.decode(e,t)),n}}class Xe{static encode(e,t){let n="";for(let s=1;s<=t;s++)n+=Ke.encode(e.has(s));return n}static decode(e,t){if(e.length!==t)throw new Re("bitfield encoding length mismatch");const n=new He;for(let s=1;s<=t;s++)Ke.decode(e[s-1])&&n.set(s);return n.bitLength=e.length,n}}class qe{static encode(e,t){const n=(e=e.toUpperCase()).charCodeAt(0)-65,s=e.charCodeAt(1)-65;if(n<0||n>25||s<0||s>25)throw new Te(`invalid language code: ${e}`);if(t%2==1)throw new Te(`numBits must be even, ${t} is not valid`);t/=2;return We.encode(n,t)+We.encode(s,t)}static decode(e,t){let n;if(t!==e.length||e.length%2)throw new Re("invalid bit length for language");{const t=65,s=e.length/2,r=We.decode(e.slice(0,s),s)+t,i=We.decode(e.slice(s),s)+t;n=String.fromCharCode(r)+String.fromCharCode(i)}return n}}class $e{static encode(e){let t=We.encode(e.numRestrictions,Ge.numRestrictions);return e.isEmpty()||e.getRestrictions().forEach((n=>{t+=We.encode(n.purposeId,Ge.purposeId),t+=We.encode(n.restrictionType,Ge.restrictionType);const s=e.getVendors(n),r=s.length;let i=0,o=0,a="";for(let t=0;t<r;t++){const n=s[t];0===o&&(i++,o=n);const c=s[r-1],l=e.gvl.vendorIds,u=e=>{for(;++e<=c&&!l.has(e););return e};if(t===r-1||s[t+1]>u(n)){const e=!(n===o);a+=Ke.encode(e),a+=We.encode(o,Ge.vendorId),e&&(a+=We.encode(n,Ge.vendorId)),o=0}}t+=We.encode(i,Ge.numEntries),t+=a})),t}static decode(e){let t=0;const n=new Me,s=We.decode(e.substr(t,Ge.numRestrictions),Ge.numRestrictions);t+=Ge.numRestrictions;for(let r=0;r<s;r++){const s=We.decode(e.substr(t,Ge.purposeId),Ge.purposeId);t+=Ge.purposeId;const r=We.decode(e.substr(t,Ge.restrictionType),Ge.restrictionType);t+=Ge.restrictionType;const i=new Fe(s,r),o=We.decode(e.substr(t,Ge.numEntries),Ge.numEntries);t+=Ge.numEntries;for(let s=0;s<o;s++){const s=Ke.decode(e.substr(t,Ge.anyBoolean));t+=Ge.anyBoolean;const r=We.decode(e.substr(t,Ge.vendorId),Ge.vendorId);if(t+=Ge.vendorId,s){const s=We.decode(e.substr(t,Ge.vendorId),Ge.vendorId);if(t+=Ge.vendorId,s<r)throw new Re(`Invalid RangeEntry: endVendorId ${s} is less than ${r}`);for(let e=r;e<=s;e++)n.add(e,i)}else n.add(r,i)}}return n.bitLength=t,n}}!function(e){e[e.FIELD=0]="FIELD",e[e.RANGE=1]="RANGE"}(Ae||(Ae={}));class Je{static encode(e){const t=[];let n,s=[],r=We.encode(e.maxId,Ge.maxId),i="";const o=Ge.maxId+Ge.encodingType,a=o+e.maxId,c=2*Ge.vendorId+Ge.singleOrRange+Ge.numEntries;let l=o+Ge.numEntries;return e.forEach(((r,o)=>{if(i+=Ke.encode(r),n=e.maxId>c&&l<a,n&&r){e.has(o+1)?0===s.length&&(s.push(o),l+=Ge.singleOrRange,l+=Ge.vendorId):(s.push(o),l+=Ge.vendorId,t.push(s),s=[])}})),n?(r+=String(Ae.RANGE),r+=this.buildRangeEncoding(t)):(r+=String(Ae.FIELD),r+=i),r}static decode(e,t){let n,s=0;const r=We.decode(e.substr(s,Ge.maxId),Ge.maxId);s+=Ge.maxId;const i=We.decode(e.charAt(s),Ge.encodingType);if(s+=Ge.encodingType,i===Ae.RANGE){if(n=new He,1===t){if("1"===e.substr(s,1))throw new Re("Unable to decode default consent=1");s++}const r=We.decode(e.substr(s,Ge.numEntries),Ge.numEntries);s+=Ge.numEntries;for(let t=0;t<r;t++){const t=Ke.decode(e.charAt(s));s+=Ge.singleOrRange;const r=We.decode(e.substr(s,Ge.vendorId),Ge.vendorId);if(s+=Ge.vendorId,t){const t=We.decode(e.substr(s,Ge.vendorId),Ge.vendorId);s+=Ge.vendorId;for(let e=r;e<=t;e++)n.set(e)}else n.set(r)}}else{const t=e.substr(s,r);s+=r,n=Xe.decode(t,r)}return n.bitLength=s,n}static buildRangeEncoding(e){const t=e.length;let n=We.encode(t,Ge.numEntries);return e.forEach((e=>{const t=1===e.length;n+=Ke.encode(!t),n+=We.encode(e[0],Ge.vendorId),t||(n+=We.encode(e[1],Ge.vendorId))})),n}}function Qe(){return{[Be.version]:We,[Be.created]:ze,[Be.lastUpdated]:ze,[Be.cmpId]:We,[Be.cmpVersion]:We,[Be.consentScreen]:We,[Be.consentLanguage]:qe,[Be.vendorListVersion]:We,[Be.policyVersion]:We,[Be.isServiceSpecific]:Ke,[Be.useNonStandardStacks]:Ke,[Be.specialFeatureOptins]:Xe,[Be.purposeConsents]:Xe,[Be.purposeLegitimateInterests]:Xe,[Be.purposeOneTreatment]:Ke,[Be.publisherCountryCode]:qe,[Be.vendorConsents]:Je,[Be.vendorLegitimateInterests]:Je,[Be.publisherRestrictions]:$e,segmentType:We,[Be.vendorsDisclosed]:Je,[Be.vendorsAllowed]:Je,[Be.publisherConsents]:Xe,[Be.publisherLegitimateInterests]:Xe,[Be.numCustomPurposes]:We,[Be.publisherCustomConsents]:Xe,[Be.publisherCustomLegitimateInterests]:Xe}}class Ye{1={[Ce.CORE]:[Be.version,Be.created,Be.lastUpdated,Be.cmpId,Be.cmpVersion,Be.consentScreen,Be.consentLanguage,Be.vendorListVersion,Be.purposeConsents,Be.vendorConsents]};2={[Ce.CORE]:[Be.version,Be.created,Be.lastUpdated,Be.cmpId,Be.cmpVersion,Be.consentScreen,Be.consentLanguage,Be.vendorListVersion,Be.policyVersion,Be.isServiceSpecific,Be.useNonStandardStacks,Be.specialFeatureOptins,Be.purposeConsents,Be.purposeLegitimateInterests,Be.purposeOneTreatment,Be.publisherCountryCode,Be.vendorConsents,Be.vendorLegitimateInterests,Be.publisherRestrictions],[Ce.PUBLISHER_TC]:[Be.publisherConsents,Be.publisherLegitimateInterests,Be.numCustomPurposes,Be.publisherCustomConsents,Be.publisherCustomLegitimateInterests],[Ce.VENDORS_ALLOWED]:[Be.vendorsAllowed],[Ce.VENDORS_DISCLOSED]:[Be.vendorsDisclosed]}}class Ze{1=[Ce.CORE];2=[Ce.CORE];constructor(e,t){if(2===e.version)if(e.isServiceSpecific)this[2].push(Ce.PUBLISHER_TC);else{const n=!(!t||!t.isForVendors);n&&!0!==e[Be.supportOOB]||this[2].push(Ce.VENDORS_DISCLOSED),n&&(e[Be.supportOOB]&&e[Be.vendorsAllowed].size>0&&this[2].push(Ce.VENDORS_ALLOWED),this[2].push(Ce.PUBLISHER_TC))}}}class et{static fieldSequence=new Ye;static encode(e,t){let n;try{n=this.fieldSequence[String(e.version)][t]}catch(n){throw new Te(`Unable to encode version: ${e.version}, segment: ${t}`)}let s="";t!==Ce.CORE&&(s=We.encode(je.KEY_TO_ID[t],Ge.segmentType));const r=Qe();return n.forEach((n=>{const i=e[n],o=r[n];let a=Ge[n];void 0===a&&this.isPublisherCustom(n)&&(a=Number(e[Be.numCustomPurposes]));try{s+=o.encode(i,a)}catch(e){throw new Te(`Error encoding ${t}->${n}: ${e.message}`)}})),Ne.encode(s)}static decode(e,t,n){const s=Ne.decode(e);let r=0;n===Ce.CORE&&(t.version=We.decode(s.substr(r,Ge[Be.version]),Ge[Be.version])),n!==Ce.CORE&&(r+=Ge.segmentType);const i=this.fieldSequence[String(t.version)][n],o=Qe();return i.forEach((e=>{const n=o[e];let i=Ge[e];if(void 0===i&&this.isPublisherCustom(e)&&(i=Number(t[Be.numCustomPurposes])),0!==i){const o=s.substr(r,i);if(t[e]=n===Je?n.decode(o,t.version):n.decode(o,i),Number.isInteger(i))r+=i;else{if(!Number.isInteger(t[e].bitLength))throw new Re(e);r+=t[e].bitLength}}})),t}static isPublisherCustom(e){return 0===e.indexOf("publisherCustom")}}class tt{static processor=[e=>e,(e,t)=>{e.publisherRestrictions.gvl=t,e.purposeLegitimateInterests.unset(1);const n=new Map;return n.set("legIntPurposes",e.vendorLegitimateInterests),n.set("purposes",e.vendorConsents),n.forEach(((n,s)=>{n.forEach(((r,i)=>{if(r){const r=t.vendors[i];if(!r||r.deletedDate)n.unset(i);else if(0===r[s].length)if("legIntPurposes"===s&&0===r.purposes.length&&0===r.legIntPurposes.length&&r.specialPurposes.length>0);else if(e.isServiceSpecific)if(0===r.flexiblePurposes.length)n.unset(i);else{const t=e.publisherRestrictions.getRestrictions(i);let r=!1;for(let e=0,n=t.length;e<n&&!r;e++)r=t[e].restrictionType===Se.REQUIRE_CONSENT&&"purposes"===s||t[e].restrictionType===Se.REQUIRE_LI&&"legIntPurposes"===s;r||n.unset(i)}else n.unset(i)}}))})),e.vendorsDisclosed.set(t.vendors),e}];static process(e,t){const n=e.gvl;if(!n)throw new Te("Unable to encode TCModel without a GVL");if(!n.isReady)throw new Te("Unable to encode TCModel tcModel.gvl.readyPromise is not resolved");(e=e.clone()).consentLanguage=n.language.toUpperCase(),t?.version>0&&t?.version<=this.processor.length?e.version=t.version:e.version=this.processor.length;const s=e.version-1;if(!this.processor[s])throw new Te(`Invalid version: ${e.version}`);return this.processor[s](e,n)}}class nt{static absCall(e,t,n,s){return new Promise(((r,i)=>{const o=new XMLHttpRequest;o.withCredentials=n,o.addEventListener("load",(()=>{if(o.readyState==XMLHttpRequest.DONE)if(o.status>=200&&o.status<300){let e=o.response;if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}r(e)}else i(new Error(`HTTP Status: ${o.status} response type: ${o.responseType}`))})),o.addEventListener("error",(()=>{i(new Error("error"))})),o.addEventListener("abort",(()=>{i(new Error("aborted"))})),null===t?o.open("GET",e,!0):o.open("POST",e,!0),o.responseType="json",o.timeout=s,o.ontimeout=()=>{i(new Error("Timeout "+s+"ms "+e))},o.send(t)}))}static post(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return this.absCall(e,JSON.stringify(t),n,s)}static fetch(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return this.absCall(e,null,t,n)}}class st extends ke{static LANGUAGE_CACHE=new Map;static CACHE=new Map;static LATEST_CACHE_KEY=0;static DEFAULT_LANGUAGE="EN";static consentLanguages=new Ue;static baseUrl_;static set baseUrl(e){if(/^https?:\/\/vendorlist\.consensu\.org\//.test(e))throw new Pe("Invalid baseUrl!  You may not pull directly from vendorlist.consensu.org and must provide your own cache");e.length>0&&"/"!==e[e.length-1]&&(e+="/"),this.baseUrl_=e}static get baseUrl(){return this.baseUrl_}static latestFilename="vendor-list.json";static versionedFilename="archives/vendor-list-v[VERSION].json";static languageFilename="purposes-[LANG].json";readyPromise;gvlSpecificationVersion;vendorListVersion;tcfPolicyVersion;lastUpdated;purposes;specialPurposes;features;specialFeatures;isReady_=!1;vendors_;vendorIds;fullVendorList;byPurposeVendorMap;bySpecialPurposeVendorMap;byFeatureVendorMap;bySpecialFeatureVendorMap;stacks;lang_;isLatest=!1;constructor(e){super();let t=st.baseUrl;if(this.lang_=st.DEFAULT_LANGUAGE,this.isVendorList(e))this.populate(e),this.readyPromise=Promise.resolve();else{if(!t)throw new Pe("must specify GVL.baseUrl before loading GVL json");if(e>0){const n=e;st.CACHE.has(n)?(this.populate(st.CACHE.get(n)),this.readyPromise=Promise.resolve()):(t+=st.versionedFilename.replace("[VERSION]",String(n)),this.readyPromise=this.fetchJson(t))}else st.CACHE.has(st.LATEST_CACHE_KEY)?(this.populate(st.CACHE.get(st.LATEST_CACHE_KEY)),this.readyPromise=Promise.resolve()):(this.isLatest=!0,this.readyPromise=this.fetchJson(t+st.latestFilename))}}static emptyLanguageCache(e){let t=!1;return void 0===e&&st.LANGUAGE_CACHE.size>0?(st.LANGUAGE_CACHE=new Map,t=!0):"string"==typeof e&&this.consentLanguages.has(e.toUpperCase())&&(st.LANGUAGE_CACHE.delete(e.toUpperCase()),t=!0),t}static emptyCache(e){let t=!1;return Number.isInteger(e)&&e>=0?(st.CACHE.delete(e),t=!0):void 0===e&&(st.CACHE=new Map,t=!0),t}cacheLanguage(){st.LANGUAGE_CACHE.has(this.lang_)||st.LANGUAGE_CACHE.set(this.lang_,{purposes:this.purposes,specialPurposes:this.specialPurposes,features:this.features,specialFeatures:this.specialFeatures,stacks:this.stacks})}async fetchJson(e){try{this.populate(await nt.fetch(e))}catch(e){throw new Pe(e.message)}}getJson(){return JSON.parse(JSON.stringify({gvlSpecificationVersion:this.gvlSpecificationVersion,vendorListVersion:this.vendorListVersion,tcfPolicyVersion:this.tcfPolicyVersion,lastUpdated:this.lastUpdated,purposes:this.purposes,specialPurposes:this.specialPurposes,features:this.features,specialFeatures:this.specialFeatures,stacks:this.stacks,vendors:this.fullVendorList}))}async changeLanguage(e){const t=e.toUpperCase();if(!st.consentLanguages.has(t))throw new Pe(`unsupported language ${e}`);if(t!==this.lang_)if(this.lang_=t,st.LANGUAGE_CACHE.has(t)){const e=st.LANGUAGE_CACHE.get(t);for(const t in e)e.hasOwnProperty(t)&&(this[t]=e[t])}else{const t=st.baseUrl+st.languageFilename.replace("[LANG]",e);try{await this.fetchJson(t),this.cacheLanguage()}catch(e){throw new Pe("unable to load language: "+e.message)}}}get language(){return this.lang_}isVendorList(e){return void 0!==e&&void 0!==e.vendors}populate(e){this.purposes=e.purposes,this.specialPurposes=e.specialPurposes,this.features=e.features,this.specialFeatures=e.specialFeatures,this.stacks=e.stacks,this.isVendorList(e)&&(this.gvlSpecificationVersion=e.gvlSpecificationVersion,this.tcfPolicyVersion=e.tcfPolicyVersion,this.vendorListVersion=e.vendorListVersion,this.lastUpdated=e.lastUpdated,"string"==typeof this.lastUpdated&&(this.lastUpdated=new Date(this.lastUpdated)),this.vendors_=e.vendors,this.fullVendorList=e.vendors,this.mapVendors(),this.isReady_=!0,this.isLatest&&st.CACHE.set(st.LATEST_CACHE_KEY,this.getJson()),st.CACHE.has(this.vendorListVersion)||st.CACHE.set(this.vendorListVersion,this.getJson())),this.cacheLanguage()}mapVendors(e){this.byPurposeVendorMap={},this.bySpecialPurposeVendorMap={},this.byFeatureVendorMap={},this.bySpecialFeatureVendorMap={},Object.keys(this.purposes).forEach((e=>{this.byPurposeVendorMap[e]={legInt:new Set,consent:new Set,flexible:new Set}})),Object.keys(this.specialPurposes).forEach((e=>{this.bySpecialPurposeVendorMap[e]=new Set})),Object.keys(this.features).forEach((e=>{this.byFeatureVendorMap[e]=new Set})),Object.keys(this.specialFeatures).forEach((e=>{this.bySpecialFeatureVendorMap[e]=new Set})),Array.isArray(e)||(e=Object.keys(this.fullVendorList).map((e=>+e))),this.vendorIds=new Set(e),this.vendors_=e.reduce(((e,t)=>{const n=this.vendors_[String(t)];return n&&void 0===n.deletedDate&&(n.purposes.forEach((e=>{this.byPurposeVendorMap[String(e)].consent.add(t)})),n.specialPurposes.forEach((e=>{this.bySpecialPurposeVendorMap[String(e)].add(t)})),n.legIntPurposes.forEach((e=>{this.byPurposeVendorMap[String(e)].legInt.add(t)})),n.flexiblePurposes&&n.flexiblePurposes.forEach((e=>{this.byPurposeVendorMap[String(e)].flexible.add(t)})),n.features.forEach((e=>{this.byFeatureVendorMap[String(e)].add(t)})),n.specialFeatures.forEach((e=>{this.bySpecialFeatureVendorMap[String(e)].add(t)})),e[t]=n),e}),{})}getFilteredVendors(e,t,n,s){const r=e.charAt(0).toUpperCase()+e.slice(1);let i;const o={};return i="purpose"===e&&n?this["by"+r+"VendorMap"][String(t)][n]:this["by"+(s?"Special":"")+r+"VendorMap"][String(t)],i.forEach((e=>{o[String(e)]=this.vendors[String(e)]})),o}getVendorsWithConsentPurpose(e){return this.getFilteredVendors("purpose",e,"consent")}getVendorsWithLegIntPurpose(e){return this.getFilteredVendors("purpose",e,"legInt")}getVendorsWithFlexiblePurpose(e){return this.getFilteredVendors("purpose",e,"flexible")}getVendorsWithSpecialPurpose(e){return this.getFilteredVendors("purpose",e,void 0,!0)}getVendorsWithFeature(e){return this.getFilteredVendors("feature",e)}getVendorsWithSpecialFeature(e){return this.getFilteredVendors("feature",e,void 0,!0)}get vendors(){return this.vendors_}narrowVendorsTo(e){this.mapVendors(e)}get isReady(){return this.isReady_}clone(){const e=new st(this.getJson());return this.lang_!==st.DEFAULT_LANGUAGE&&e.changeLanguage(this.lang_),e}static isInstanceOf(e){return"object"==typeof e&&"function"==typeof e.narrowVendorsTo}}class rt extends ke{static consentLanguages=st.consentLanguages;isServiceSpecific_=!1;supportOOB_=!0;useNonStandardStacks_=!1;purposeOneTreatment_=!1;publisherCountryCode_="AA";version_=2;consentScreen_=0;policyVersion_=2;consentLanguage_="EN";cmpId_=0;cmpVersion_=0;vendorListVersion_=0;numCustomPurposes_=0;gvl_;created;lastUpdated;specialFeatureOptins=new He;purposeConsents=new He;purposeLegitimateInterests=new He;publisherConsents=new He;publisherLegitimateInterests=new He;publisherCustomConsents=new He;publisherCustomLegitimateInterests=new He;customPurposes;vendorConsents=new He;vendorLegitimateInterests=new He;vendorsDisclosed=new He;vendorsAllowed=new He;publisherRestrictions=new Me;constructor(e){super(),e&&(this.gvl=e),this.updated()}set gvl(e){st.isInstanceOf(e)||(e=new st(e)),this.gvl_=e,this.publisherRestrictions.gvl=e}get gvl(){return this.gvl_}set cmpId(e){if(e=Number(e),!(Number.isInteger(e)&&e>1))throw new Ve("cmpId",e);this.cmpId_=e}get cmpId(){return this.cmpId_}set cmpVersion(e){if(e=Number(e),!(Number.isInteger(e)&&e>-1))throw new Ve("cmpVersion",e);this.cmpVersion_=e}get cmpVersion(){return this.cmpVersion_}set consentScreen(e){if(e=Number(e),!(Number.isInteger(e)&&e>-1))throw new Ve("consentScreen",e);this.consentScreen_=e}get consentScreen(){return this.consentScreen_}set consentLanguage(e){this.consentLanguage_=e}get consentLanguage(){return this.consentLanguage_}set publisherCountryCode(e){if(!/^([A-z]){2}$/.test(e))throw new Ve("publisherCountryCode",e);this.publisherCountryCode_=e.toUpperCase()}get publisherCountryCode(){return this.publisherCountryCode_}set vendorListVersion(e){if((e=Number(e)>>0)<0)throw new Ve("vendorListVersion",e);this.vendorListVersion_=e}get vendorListVersion(){return this.gvl?this.gvl.vendorListVersion:this.vendorListVersion_}set policyVersion(e){if(this.policyVersion_=parseInt(e,10),this.policyVersion_<0)throw new Ve("policyVersion",e)}get policyVersion(){return this.gvl?this.gvl.tcfPolicyVersion:this.policyVersion_}set version(e){this.version_=parseInt(e,10)}get version(){return this.version_}set isServiceSpecific(e){this.isServiceSpecific_=e}get isServiceSpecific(){return this.isServiceSpecific_}set useNonStandardStacks(e){this.useNonStandardStacks_=e}get useNonStandardStacks(){return this.useNonStandardStacks_}set supportOOB(e){this.supportOOB_=e}get supportOOB(){return this.supportOOB_}set purposeOneTreatment(e){this.purposeOneTreatment_=e}get purposeOneTreatment(){return this.purposeOneTreatment_}setAllVendorConsents(){this.vendorConsents.set(this.gvl.vendors)}unsetAllVendorConsents(){this.vendorConsents.empty()}setAllVendorsDisclosed(){this.vendorsDisclosed.set(this.gvl.vendors)}unsetAllVendorsDisclosed(){this.vendorsDisclosed.empty()}setAllVendorsAllowed(){this.vendorsAllowed.set(this.gvl.vendors)}unsetAllVendorsAllowed(){this.vendorsAllowed.empty()}setAllVendorLegitimateInterests(){this.vendorLegitimateInterests.set(this.gvl.vendors)}unsetAllVendorLegitimateInterests(){this.vendorLegitimateInterests.empty()}setAllPurposeConsents(){this.purposeConsents.set(this.gvl.purposes)}unsetAllPurposeConsents(){this.purposeConsents.empty()}setAllPurposeLegitimateInterests(){this.purposeLegitimateInterests.set(this.gvl.purposes)}unsetAllPurposeLegitimateInterests(){this.purposeLegitimateInterests.empty()}setAllSpecialFeatureOptins(){this.specialFeatureOptins.set(this.gvl.specialFeatures)}unsetAllSpecialFeatureOptins(){this.specialFeatureOptins.empty()}setAll(){this.setAllVendorConsents(),this.setAllPurposeLegitimateInterests(),this.setAllSpecialFeatureOptins(),this.setAllPurposeConsents(),this.setAllVendorLegitimateInterests()}unsetAll(){this.unsetAllVendorConsents(),this.unsetAllPurposeLegitimateInterests(),this.unsetAllSpecialFeatureOptins(),this.unsetAllPurposeConsents(),this.unsetAllVendorLegitimateInterests()}get numCustomPurposes(){let e=this.numCustomPurposes_;if("object"==typeof this.customPurposes){const t=Object.keys(this.customPurposes).sort(((e,t)=>Number(e)-Number(t)));e=parseInt(t.pop(),10)}return e}set numCustomPurposes(e){if(this.numCustomPurposes_=parseInt(e,10),this.numCustomPurposes_<0)throw new Ve("numCustomPurposes",e)}updated(){const e=new Date,t=new Date(Date.UTC(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()));this.created=t,this.lastUpdated=t}}class it{static encode(e,t){let n,s="";return e=tt.process(e,t),n=Array.isArray(t?.segments)?t.segments:new Ze(e,t)[""+e.version],n.forEach(((t,r)=>{let i="";r<n.length-1&&(i="."),s+=et.encode(e,t)+i})),s}static decode(e,t){const n=e.split("."),s=n.length;t||(t=new rt);for(let e=0;e<s;e++){const s=n[e],r=Ne.decode(s.charAt(0)).substr(0,Ge.segmentType),i=je.ID_TO_KEY[We.decode(r,Ge.segmentType).toString()];et.decode(s,t,i)}return t}}function ot(e){var t={};return e.forEach((function(e,n){t[n.toString()]=e})),t}function at(e){return t(this,void 0,void 0,(function(){var s,r=this;return n(this,(function(i){return De||(s=Y("timing.consent"),De=new Promise((function(i){return t(r,void 0,void 0,(function(){var t,r,o,a;return n(this,(function(n){switch(n.label){case 0:return n.trys.push([0,3,,4]),t=function(){var e,t,n=(null===(e=window.__bt)||void 0===e?void 0:e.tcf)||(null===(t=l.__bt)||void 0===t?void 0:t.tcf);if((null==n?void 0:n.hasOwnProperty("GDPRApplies"))&&(null==n?void 0:n.hasOwnProperty("TCFString")))try{var s=it.decode(n.TCFString),r=s.purposeConsents,i=s.vendorConsents;return{isCustomTCF:!0,gdprApplies:!!n.GDPRApplies,tcString:n.TCFString,purpose:{consents:ot(r)},vendor:{consents:ot(i)}}}catch(e){return console.warn("Failed to decode TCF string:",e),null}return null}(),t?(l.__bt_intrnl.tcData=t,s({customTCF:!0}),[2,i(t)]):e?[3,2]:[4,ct()];case 1:n.sent(),n.label=2;case 2:return r=dt(),o=r.tcfapiFunc,r.isFunction?o("addEventListener",2,(function(t,n){return!n||"tcloaded"!==(null==t?void 0:t.eventStatus)&&"useractioncomplete"!==(null==t?void 0:t.eventStatus)?(l.__bt_intrnl.tcData={eventStatus:null==t?void 0:t.eventStatus},e?i(null):void(!1===(null==t?void 0:t.gdprApplies)&&i(t))):(l.__bt_intrnl.tcData=t,s({cached:"tcloaded"===t.eventStatus}),i(t))})):i(null),[3,4];case 3:return a=n.sent(),l.__bt_intrnl.tcData={error:a},i(null),[3,4];case 4:return[2]}}))}))}))),[2,De]}))}))}function ct(){return t(this,void 0,void 0,(function(){return n(this,(function(e){return[2,new Promise((function(e){if(dt().isFunction||ut())e(!0);else var t=setInterval((function(){(dt().isFunction||ut())&&(clearInterval(t),e(!0))}),50)}))]}))}))}var lt=["AT","BE","BG","CH","CY","CZ","DE","DK","EE","ES","EU","FI","FR","GB","GR","HR","HU","IE","IS","IT","LI","LT","LU","LV","MT","NL","NO","PL","PT","RO","SE","SI","SK"];function ut(){var e=l.localStorage.getItem("btUserCountry");return e&&!lt.includes(e)}function dt(){var e=window.__tcfapi||l.__tcfapi;return{tcfapiFunc:e,isFunction:"function"==typeof e}}var pt,ht,ft=815,gt=[1,2,3,4,7,9,10];function vt(e){var s,r,i,o;return t(this,void 0,void 0,(function(){var t,a,c,l,u;return n(this,(function(n){switch(n.label){case 0:return[4,at(e)];case 1:if(!(t=n.sent())||!1===t.gdprApplies)return[2,null];try{return a=null!==(r=null===(s=t.purpose)||void 0===s?void 0:s.consents)&&void 0!==r?r:{},c=null!==(o=null===(i=t.vendor)||void 0===i?void 0:i.consents)&&void 0!==o?o:{},l=gt.every((function(e){return!!a[e]})),u=!0===c[ft],[2,l&&u]}catch(e){return[2,null]}return[2]}}))}))}var mt=(null===(ht=b.href)||void 0===ht?void 0:ht.includes("bt_debug=true"))||"true"===l.localStorage.getItem("bt_debug"),bt=function(){function e(){}return e.getDuration=function(){var t=performance.now()-e.startTimestamp;return t<1e3?Math.round(t)+"ms":(t/1e3).toFixed(2)+"s"},e.logToConsole=function(t,n){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];if(e.isLoggingEnabled){var o=["color: black; background-color: "+e.LEVEL_COLORS[t],"background-color: inherit","font-weight: bold;","font-weight: normal;","color: #757575;","color: inherit"],a="%c[Blockthrough - "+e.LEVEL_NAME[t]+"]%c %c[tag.js]%c %c["+e.getDuration()+"]%c "+n,c=r;1===r.length&&Array.isArray(r[0])&&(c=r[0]);var l=c.filter((function(e){return!(Array.isArray(e)&&!e.length)}));l.length?console.info.apply(console,s([a],o,l)):console.info.apply(console,s([a],o))}},e.sendToServer=function(e){if(null==e?void 0:e.event){var t=e.sendPercentage;K(e.event,e.meta,{sendPercentage:t,severity:e.severity})}},e.logWithServer=function(t,n,r){for(var i=[],o=3;o<arguments.length;o++)i[o-3]=arguments[o];e.logToConsole.apply(e,s([t,n],i)),e.sendToServer(r)},e.prototype.debug=function(t,n){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];e.logWithServer.apply(e,s([e.LEVELS.DEBUG,t,n],r))},e.prototype.info=function(t,n){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];e.logWithServer.apply(e,s([e.LEVELS.INFO,t,n],r))},e.prototype.warning=function(t,n){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];e.logWithServer.apply(e,s([e.LEVELS.WARNING,t,n],r))},e.prototype.error=function(t,n){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];e.logWithServer.apply(e,s([e.LEVELS.ERROR,t,n],r))},e.isLoggingEnabled=mt&&"function"==typeof(null===console||void 0===console?void 0:console.info),e.LEVELS={DEBUG:"DEBUG",INFO:"INFO",WARNING:"WARNING",ERROR:"ERROR"},e.LEVEL_NAME=((pt={})[e.LEVELS.DEBUG]="DBG",pt[e.LEVELS.INFO]="INF",pt[e.LEVELS.WARNING]="WRN",pt[e.LEVELS.ERROR]="ERR",pt),e.LEVEL_COLORS={DEBUG:"#DCEDC8",INFO:"#B3E5FC",WARNING:"#FFE0B2",ERROR:"#E57373"},e.startTimestamp=performance.now(),e}(),wt="BT_EXP_FLAGS",yt=5e3,_t=2,It=new bt,Et=function(){function t(e){this.config=e,this.responses={},this.respondedApiMethods=new Set}return t.normaliseResponses=function(n){var s=new Map,r=Object.values(t.SUPPORTED_EXTENSIONS),i=function(i){if(n.hasOwnProperty(i)){var o=n[i],a=t.API_HANDLERS[i];a&&o.forEach((function(t){var n=t.name,o=t.version,c=t.result,l=t.error;if(r.includes(n)){var u=s.get(n)||{name:n,version:o};c?s.set(n,e(e({},u),a(c))):l&&It.warning("Extension API error: %o. Extension details: %o",null,[l,{name:n,version:o,method:i}])}else It.warning("Unsupported extension: %o",null,{name:n,version:o,method:i})}))}};for(var o in n)i(o);return Array.from(s.values())},t.handleStatusResponse=function(e){if("object"!=typeof e||null===e)return{};var t=e.aa,n=e.allowlist,s=e.license;return{aa:t,ab:n&&!n.status,allowListStatus:null==n?void 0:n.status,premium:!!s}},t.handleExperimentFlagsResponse=function(e){return"object"!=typeof e||null===e?{}:{experimentFlags:Object.fromEntries(Object.entries(e).filter((function(e){return e[0],"boolean"==typeof e[1]})))}},t.prototype.getExtensionData=function(){var e=this;return new Promise((function(n,s){var r,i,o;e.trustedIframe=e.createIframe();var a=function(){window.removeEventListener("message",l),clearTimeout(i)},c=function(){a(),e.respondedApiMethods.size>0?n(t.normaliseResponses(e.responses)):n(null)},l=function(t){if(e.isTrustedIframeEvent(t)){var n=t.data,r=n.request,i=n.response,l=n.error;if(l)a(),s(l);else if(Array.isArray(i)){var u=r.method;e.respondedApiMethods.add(u),e.responses[u]=i,o({method:u,result:i.map((function(e){var t=e.name,n=e.version;return{name:t,status:e.result?"success":"errored",details:{version:n,error:e.error}}}))}),e.hasAllResponses()&&c()}}};e.trustedIframe.addEventListener("load",(function(){o=Y("timing.extensionCommand"),t.API_METHODS.forEach((function(t){var n;null===(n=e.trustedIframe.contentWindow)||void 0===n||n.postMessage({method:t},e.config.origin)})),i=setTimeout(c,e.config.timeout)}),{once:!0}),window.addEventListener("message",l),null===(r=document.head||document.body||document.documentElement)||void 0===r||r.appendChild(e.trustedIframe)}))},t.prototype.createIframe=function(){var e=document.createElement("iframe");return e.src=this.config.origin+"/trustedIframe.html?o="+this.config.orgID+"&tid="+k.traceID+"&upapi=true",e.referrerPolicy="origin",e.style.cssText="display: none !important; width: 0 !important; height: 0 !important;",e},t.prototype.isTrustedIframeEvent=function(e){var n,s=e.source,r=e.origin,i=e.data;return s===this.trustedIframe.contentWindow&&r===this.config.origin&&"string"==typeof(null===(n=null==i?void 0:i.request)||void 0===n?void 0:n.method)&&t.API_METHODS.includes(i.request.method)},t.prototype.hasAllResponses=function(){var e=this;return t.API_METHODS.every((function(t){return e.respondedApiMethods.has(t)}))},t.SUPPORTED_EXTENSIONS={ADBLOCK:"adblock",ADBLOCK_PLUS:"adblockplus"},t.API_METHODS=["getStatus","getExperimentFlags"],t.API_HANDLERS={getStatus:t.handleStatusResponse,getExperimentFlags:t.handleExperimentFlagsResponse},t}();function St(e,s){return t(this,void 0,void 0,(function(){var t,r,i,o,a,c,u,d,p,h,g,v;return n(this,(function(n){switch(n.label){case 0:Z("timing.trustedIframeStart",{timestamp:Date.now()}),function(){l.localStorage.removeItem("BT_EXPERIMENT_FLAGS");var e=l.localStorage.getItem(wt);try{l.__bt_intrnl.experimentFlags=JSON.parse(e)}catch(e){}}(),t={orgID:e,origin:"https://"+f,timeout:yt},r=1,n.label=1;case 1:if(!(r<=_t))return[3,6];i=new Et(t),n.label=2;case 2:return n.trys.push([2,4,,5]),[4,i.getExtensionData()];case 3:return null!==(o=n.sent())?(a=o.filter((function(e){return function(e){if("object"!=typeof e||null===e)return!1;return Lt.getStatus.every((function(t){return t in e}))}(e)})),a.length&&(c=a.find((function(e){return e.name===Et.SUPPORTED_EXTENSIONS.ADBLOCK})),u=a.find((function(e){return e.name===Et.SUPPORTED_EXTENSIONS.ADBLOCK_PLUS})),p=(d=c||{}).experimentFlags,h=d.premium,u||!p||h?l.localStorage.removeItem(wt):(l.__bt_intrnl.experimentFlags||(l.__bt_intrnl.experimentFlags=p),l.localStorage.setItem(wt,JSON.stringify(p))),G({extensions:a,siteInfo:s})),m=[],o.forEach((function(e){var t=Object.entries(Lt).filter((function(t){return t[0],t[1].some((function(t){return!(t in e)}))})).map((function(e){return e[0]}));t.length&&m.push({name:e.name,missing:t})})),(g=m.length?m:void 0)?(Q({status:"incomplete",details:{missingData:g,attempt:r}}),It.warning("Trusted iframe - missing data from extensions: %o",null,g)):Q({status:"success",details:{attempt:r}}),[2]):(It.warning("Trusted iframe did not load in time after "+r+" attempt"+(1===r?"":"s")),++r>_t&&Q({status:"timedOut",details:{attempt:r}}),[3,5]);case 4:return Q({status:"errored",details:{error:v=n.sent(),attempt:r}}),It.warning("Trusted iframe - critical error on attempt %o: %o.",null,[r,v]),[2];case 5:return[3,1];case 6:return[2]}var m}))}))}var Lt={getStatus:["name","version","aa","ab","allowListStatus","premium"],getExperimentFlags:["experimentFlags"]};function Ct(){return new Promise((function(e){switch(function(){var e=window.navigator.userAgent;if(/chrome|chromium/i.test(e))return At.CHROME;if(/firefox|iceweasel|fxios/i.test(e))return At.FIREFOX;if(/msie|trident/i.test(e))return At.IE;if(/^((?!chrome|android).)*safari/i.test(e))return At.SAFARI;if(window.chrome)return At.CHROME;return}()){case At.SAFARI:void 0!==window.navigator.maxTouchPoints?function(){var t=String(Math.random());try{window.indexedDB.open(t,1).onupgradeneeded=function(n){var s,r,i=null===(s=n.target)||void 0===s?void 0:s.result;try{i.createObjectStore("test",{autoIncrement:!0}).put(new Blob),e(!1)}catch(t){var o=t;if(t instanceof Error&&(o=null!==(r=t.message)&&void 0!==r?r:t),"string"!=typeof o)return e(!1);var a=/BlobURLs are not yet supported/.test(o);e(a)}finally{i.close(),window.indexedDB.deleteDatabase(t)}}}catch(t){return e(!1)}}():function(){var t=window.openDatabase,n=window.localStorage;try{t(null,null,null,null)}catch(t){return e(!0)}try{n.setItem("test","1"),n.removeItem("test")}catch(t){return e(!0)}e(!1)}();break;case At.CHROME:void 0!==window.Promise&&void 0!==window.Promise.allSettled?function(){try{window.navigator.webkitTemporaryStorage.queryUsageAndQuota((function(t,n){var s=Math.round(n/1048576),r=2*Math.round(function(){var e,t;return(null===(t=null===(e=window.performance)||void 0===e?void 0:e.memory)||void 0===t?void 0:t.jsHeapSizeLimit)||1073741824}()/1048576);e(s<r)}),(function(){return e(null)}))}catch(t){return e(null)}}():function(){try{(0,window.webkitRequestFileSystem)(0,1,(function(){return e(!1)}),(function(){return e(!0)}))}catch(t){return e(null)}}();break;case At.FIREFOX:e(void 0===window.navigator.serviceWorker);break;case At.IE:e(void 0===window.indexedDB);break;default:e(null)}}))}var At={SAFARI:"safari",CHROME:"chrome",FIREFOX:"firefox",IE:"ie"};l.__bt_intrnl=l.__bt_intrnl||{traceID:k.traceID,pvSent:{},stopFlag:!1,timings:J,aaDetectionResults:null,pageURL:b.toString(),tcData:void 0,experimentFlags:void 0,trustedIframeInjected:{}},window.__bt_intrnl=l.__bt_intrnl;var Dt=new bt;function Ot(e,s){return t(this,void 0,void 0,(function(){var t,r,i,o,a,u,p,h,f,g,v,m,b,w;return n(this,(function(n){switch(n.label){case 0:return r=(t=e||{}).websiteConfig,i=t.websiteID,o=t.bundleID,u=(a=r||{}).siteInfo,p=a.checksum,f=(h=s||{}).aaDetectionResults,g=h.baitResults,[4,Promise.all([vt(!0),Ct()])];case 1:return v=n.sent(),m=v[0],b=v[1],function(e){for(var t,n=0,s=[{name:"dns-finder",type:"url",key:"NLF_BAIT_PIXEL_URL",signalKey:"ag"},{name:"ad-delivery-ab",type:"url",key:"ADBLOCK_BAIT_PIXEL_URL",signalKey:"ab"},{name:"ad-delivery-aa",type:"url",key:"AA_BAIT_PIXEL_URL",signalKey:"aa"},{name:"doubleclick-ab",type:"url",key:"ADBLOCK_BAIT_PIXEL_OLD_URL",signalKey:"ab"},{name:"bt-legacy-bait-element",type:"element",key:"ADBLOCK_BAIT_ELEMENT",signalKey:"ab"},{name:"client-based-ab",type:"custom",key:"ADBLOCK_CLIENT_DETECTION",signalKey:"ab"}];n<s.length;n++){var r=s[n],i=r.key,o=r.name,a=r.type,c=r.signalKey,l=oe[i];null!==l&&e.push({name:o,type:a,signals:(t={},t[c]=l,t)})}}(g),w={config:{siteInfo:u,checksum:p},bundleIDToLoad:o,aaDetectionResults:f,baitResults:g,consent:m,privateMode:b},l.__bt_intrnl.pvSent=l.__bt_intrnl.pvSent||{},l.__bt_intrnl.pvSent[i]=!0,y("BT_pvSent",l.__bt_intrnl.pvSent),H(w),function(){try{history.pushState=(e=history.pushState,function(){var t=e.apply(this,arguments);return window.dispatchEvent(new Event("pushstate")),window.dispatchEvent(new Event("locationChange")),t}),history.replaceState=function(e){return function(){var t=e.apply(this,arguments);return window.dispatchEvent(new Event("replacestate")),window.dispatchEvent(new Event("locationChange")),t}}(history.replaceState),window.addEventListener("popstate",(function(){window.dispatchEvent(new Event("locationChange"))}))}catch(e){}var e}(),l.addEventListener("locationChange",(function(){var e,t=d();t.search="";var n=t.toString().split("#")[0];(null===(e=l.__bt_intrnl.pageURL)||void 0===e?void 0:e.split("#")[0])!==n&&(l.__bt_intrnl.pageURL=t.toString(),V=c(),l.__bt_intrnl.traceID=V,y("BT_traceID",V),k={traceID:V,sessionID:N},w.isSPAViewChange=!0,H(w))})),[2]}}))}))}!function(){var e,s,r,i,o,a;t(this,void 0,void 0,(function(){var c,u,d,p,g,_,I,E,R,T,P,V,N,k,x,B,F,H,G;return n(this,(function(K){switch(K.label){case 0:return window.__bt_already_invoked?[2]:(window.__bt_already_invoked=!0,function(){var e;Z("timing.navigationStart",{timestamp:q}),Z("timing.tagScriptLoaded",{eventDurationMS:Math.round(null!==(e=null==X?void 0:X.duration)&&void 0!==e?e:0),timestamp:z,cacheLevel:D(f+"/tag")})}(),[4,Promise.allSettled([de(),U(),ve()])]);case 1:return c=K.sent(),u=c[0],d=c[1],p=c[2],I=[],"fulfilled"===u.status&&(g=u.value),"fulfilled"===d.status&&(_=d.value),"fulfilled"===p.status&&I.push.apply(I,p.value||[]),E=(_||{}).siteInfo,(R=(E||{}).websiteID)?(P=(T=g||{}).acceptable,V=T.ab,N=T.nlf,A({eventName:"BTAADetection",payload:{detail:{ab:V,acceptable:P}}}),window.__bt.disableRecovery?(Dt.warning("Stop recovering because of __bt.disableRecovery flag."),[2]):((null===(e=l.__bt_intrnl.trustedIframeInjected)||void 0===e?void 0:e[R])||(null===(s=w("BT_trustedIframeInjected"))||void 0===s?void 0:s[R])||(St(h,E),l.__bt_intrnl.trustedIframeInjected=l.__bt_intrnl.trustedIframeInjected||{},l.__bt_intrnl.trustedIframeInjected[R]=!0,y("BT_trustedIframeInjected",l.__bt_intrnl.trustedIframeInjected)),Ie(h),k=ue(g)&&function(e){var t=e||{},n=t.contentEnabled,s=t.mobileContentEnabled;return function(e){(l.location.href.includes("bt_debug=true")||"true"===l.localStorage.getItem("bt_debug"))&&(e.contentEnabled="true"===l.localStorage.getItem("forceContent")||e.contentEnabled,e.mobileContentEnabled="true"===l.localStorage.getItem("forceMobileContent")||e.mobileContentEnabled)}(e),n&&(!function(){return"function"==typeof window.bt_isMobileCustom?window.bt_isMobileCustom():/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(navigator.userAgent||navigator.vendor||window.opera)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test((navigator.userAgent||navigator.vendor||window.opera).substr(0,4))}()||s)}(E),k&&(x=we(_),N&&C("https://cdn.btloader.com/gpt/latest/pubads_impl.js")),(null===(r=l.__bt_intrnl.pvSent)||void 0===r?void 0:r[R])||(null===(i=w("BT_pvSent"))||void 0===i?void 0:i[R])||(g.isCached?le.then((function(e){Ot({websiteConfig:_,websiteID:R,bundleID:x},{aaDetectionResults:e,baitResults:I})})):Ot({websiteConfig:_,websiteID:R,bundleID:x},{aaDetectionResults:g,baitResults:I}),k&&at(!1).then((function(e){return function(e){var s;return t(this,void 0,void 0,(function(){var t,r,i,o,a,c,l,u,d;return n(this,(function(n){var p,f;return t=e.siteInfo,r=e.nlfDetected,i=e.tcData,o={},!i||i.error?(o.gdprApplies=null,o.error=(f=1024,(p=(null==i?void 0:i.error)||"").length>f?p.substring(0,f):p)):(a=[10,13,25,28,32,52,69,76,91,755,815],o.cmpVersion=i.cmpVersion,o.gdprApplies=i.gdprApplies,o.tcfPolicyVersion=i.tcfPolicyVersion,o.tcString=i.tcString,i.gdprApplies&&(c={},Object.entries((null===(s=i.vendor)||void 0===s?void 0:s.consents)||{}).forEach((function(e){var t=e[0],n=e[1];a.includes(Number(t))&&(c[t]=n)})),Object.keys(c).length&&(o.vendor={consents:c}),o.purpose=i.purpose)),(d={})[M]=!!r,l=d,u={tcData:o,pageURL:b.href,organizationID:parseInt(h),websiteID:parseInt(t.websiteID),clientVersion:v},[2,fetch(j("/ce",l),{method:"POST",body:L(u)})]}))}))}({siteInfo:E,nlfDetected:N,tcData:e})}))),B=function(e,t){var n=e||{},s=n.siteInfo,r=n.rlSettings,i=new Oe(r,m.script,C,s.widget,t);return i.load(),i.preventRecovery()}(_,P),!k||B?[3,3]:[4,vt(!1)])):(Dt.error("Website ID for %o not found.",null,b.hostname),[2]);case 2:return(F=K.sent())||null===F?(H=function(e,t,n,s){var r=new URL("https://"+e);return r.pathname="recovery",r.searchParams.append("w",t),s&&r.searchParams.append("b",s),n&&n.split("&").forEach((function(e){var t=e.split("=");2==t.length&&t[0]&&t[1]&&r.searchParams.append(t[0],t[1])})),r.searchParams.append("upapi","true"),r.toString()}(f,R,"",x),C(H)):(Dt.warning("Consentless user - do not load recovery script"),(null===(a=null===(o=null==O?void 0:O.settings)||void 0===o?void 0:o.monetizeConsentlessUsers)||void 0===a?void 0:a._enabled)&&window===window.top&&(G=function(e){var t=S()||l.location.href;return"https://brwsrfrm.com/i/script/"+e+"?ref="+new URL(t).origin}(R),C(G,{crossOrigin:null}))),[3,4];case 3:A({eventName:"AcceptableAdsInit",payload:{detail:!1}}),A({eventName:"uponitInit",payload:{detail:!1}}),B&&Dt.warning("Recovery is not allowed by RLink settings."),K.label=4;case 4:return[2]}}))}))}()}();
