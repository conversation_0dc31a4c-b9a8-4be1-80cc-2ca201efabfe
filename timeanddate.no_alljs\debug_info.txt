=== DEBUG INFORMATION ===

1. Debug Statement
   File: wcommon_34.js
   Line: 47
   Statement: alert(
   Risk: LOW - Debug code should be removed from production

2. Debug Statement
   File: tag_o_5174239513018368_upapi_true.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

3. Debug Statement
   File: tag_o_5174239513018368_upapi_true.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

4. Debug Statement
   File: feedback_10.js
   Line: 3
   Statement: confirm(
   Risk: LOW - Debug code should be removed from production

5. Debug Statement
   File: sungraph_19.js
   Line: 8
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

6. Debug Statement
   File: sungraph_19.js
   Line: 9
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

7. Debug Statement
   File: page_1.html
   Line: 2493
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

8. Inline Javascript
   File: page_1.html
   Line: 25
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

9. Inline Javascript
   File: page_1.html
   Line: 139
   Content: 
       {"@context" : "http://schema.org",
"@type":"Organization",
"name":"timeanddate.no",
"alternateName" : "Time and Date Norge",
"url" : "http://www.timeanddate.no",
"logo": "https://c.tadst.com/g
   Risk: LOW - Debug code should be removed from production

10. Inline Javascript
   File: page_1.html
   Line: 170
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

11. Inline Javascript
   File: page_1.html
   Line: 638
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

12. Inline Javascript
   File: page_1.html
   Line: 641
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

13. Inline Javascript
   File: page_1.html
   Line: 2471
   Content: 
        AdMgr.dispSlot(1);
       
   Risk: LOW - Debug code should be removed from production

14. Inline Javascript
   File: page_1.html
   Line: 2480
   Content: 
    et=1752668259;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d.ge
   Risk: LOW - Debug code should be removed from production

15. Inline Javascript
   File: page_1.html
   Line: 2760
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

16. Inline Javascript
   File: page_11.html
   Line: 20
   Content: 
   window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
  
   Risk: LOW - Debug code should be removed from production

17. Inline Javascript
   File: page_11.html
   Line: 29
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

18. Inline Javascript
   File: page_11.html
   Line: 144
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"V\u00e6ret","item":"https://www.timeanddate.no/vaer/"},{"@type":"ListItem","
   Risk: LOW - Debug code should be removed from production

19. Inline Javascript
   File: page_11.html
   Line: 147
   Content: 
     mtt=1;
    
   Risk: LOW - Debug code should be removed from production

20. Inline Javascript
   File: page_11.html
   Line: 170
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

21. Inline Javascript
   File: page_11.html
   Line: 638
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

22. Inline Javascript
   File: page_11.html
   Line: 641
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

23. Inline Javascript
   File: page_11.html
   Line: 787
   Content: 
      TAD=window.TAD||{};TAD.lon=-5.828;TAD.lat=35.765;
     
   Risk: LOW - Debug code should be removed from production

24. Inline Javascript
   File: page_11.html
   Line: 1708
   Content: 
      TAD.dec=",";TAD.sep=" ";et=1752668264;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.ge
   Risk: LOW - Debug code should be removed from production

25. Inline Javascript
   File: page_11.html
   Line: 1724
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

26. Inline Javascript
   File: page_11.html
   Line: 1998
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

27. Inline Javascript
   File: page_12.html
   Line: 11
   Content: 
 top.location.href='https://www.linkedin.com/oauth/v2/authorization?client_id=77ye24b8op0jm1&redirect_uri=https%3A%2F%2Ftimeanddate.no%2Fscripts%2Faccountextlogin.php%3Fprovider%3Dlinkedin&response_t
   Risk: LOW - Debug code should be removed from production

28. Inline Javascript
   File: page_13.html
   Line: 22
   Content: 
     window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
    
   Risk: LOW - Debug code should be removed from production

29. Inline Javascript
   File: page_13.html
   Line: 31
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

30. Inline Javascript
   File: page_13.html
   Line: 144
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Info","item":"https://www.timeanddate.no/info/"},{"@type":"ListItem","positi
   Risk: LOW - Debug code should be removed from production

31. Inline Javascript
   File: page_13.html
   Line: 168
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

32. Inline Javascript
   File: page_13.html
   Line: 636
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

33. Inline Javascript
   File: page_13.html
   Line: 639
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

34. Inline Javascript
   File: page_13.html
   Line: 795
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

35. Inline Javascript
   File: page_13.html
   Line: 1069
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

36. Inline Javascript
   File: page_13.html
   Line: 1074
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(b){aelw("load",function(){var a=gf("ad-wrap"),c="A0";b.getComputedStyle&&(c="A1",a&&(c="A2","none"===getComputedStyle(a).d
   Risk: LOW - Debug code should be removed from production

37. Inline Javascript
   File: page_13.html
   Line: 1080
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(){function d(a,c){var b=this;b._element=a;b._target=c.target||a;"string"==typeof c.target&&(b._target=gf(c.target));b._cla
   Risk: LOW - Debug code should be removed from production

38. Inline Javascript
   File: page_15.html
   Line: 20
   Content: 
     window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
    
   Risk: LOW - Debug code should be removed from production

39. Inline Javascript
   File: page_15.html
   Line: 28
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"opprett bruker","item":"https://www.timeanddate.no/bruker/"},{"@type":"ListI
   Risk: LOW - Debug code should be removed from production

40. Inline Javascript
   File: page_15.html
   Line: 508
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

41. Inline Javascript
   File: page_15.html
   Line: 511
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

42. Inline Javascript
   File: page_15.html
   Line: 559
   Content: 
        document.cookie="FTA=1752496465_0ef28b5e18f1b576_2a77d4a1862e59927871249aff63a1b5;path=/;secure";if(!window.TAD){window.TAD={};}TAD.fta='1752496465_0ef28b5e18f1b576_2a77d4a1862e59927871249aff
   Risk: LOW - Debug code should be removed from production

43. Inline Javascript
   File: page_15.html
   Line: 594
   Content: 
    var forgotlink=gf("forgotlink");forgotlink.onclick=function(){var a=gf("email");window.location.href=forgotlink.href+"?email="+encodeURIComponent(a.value);return!1};
   
   Risk: LOW - Debug code should be removed from production

44. Inline Javascript
   File: page_15.html
   Line: 597
   Content: 
    window.social_login=function(name) {
var url='/scripts/accountextlogin.php?provider=' + name;
return !window.open(url,'_blank', 'height=600,width=1000')
}
   
   Risk: LOW - Debug code should be removed from production

45. Inline Javascript
   File: page_15.html
   Line: 868
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

46. Inline Javascript
   File: page_15.html
   Line: 873
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(b){aelw("load",function(){var a=gf("ad-wrap"),c="A0";b.getComputedStyle&&(c="A1",a&&(c="A2","none"===getComputedStyle(a).d
   Risk: LOW - Debug code should be removed from production

47. Inline Javascript
   File: page_16.html
   Line: 25
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

48. Inline Javascript
   File: page_16.html
   Line: 138
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Kalender","item":"https://www.timeanddate.no/kalender/"},{"@type":"ListItem","
   Risk: LOW - Debug code should be removed from production

49. Inline Javascript
   File: page_16.html
   Line: 159
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

50. Inline Javascript
   File: page_16.html
   Line: 627
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

51. Inline Javascript
   File: page_16.html
   Line: 630
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

52. Inline Javascript
   File: page_16.html
   Line: 1688
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

53. Inline Javascript
   File: page_16.html
   Line: 2031
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

54. Inline Javascript
   File: page_16.html
   Line: 2036
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(b){aelw("load",function(){var a=gf("ad-wrap"),c="A0";b.getComputedStyle&&(c="A1",a&&(c="A2","none"===getComputedStyle(a).d
   Risk: LOW - Debug code should be removed from production

55. Inline Javascript
   File: page_16.html
   Line: 2042
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(){function d(a,c){var b=this;b._element=a;b._target=c.target||a;"string"==typeof c.target&&(b._target=gf(c.target));b._cla
   Risk: LOW - Debug code should be removed from production

56. Inline Javascript
   File: page_16.html
   Line: 2162
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

57. Inline Javascript
   File: page_17.html
   Line: 25
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

58. Inline Javascript
   File: page_17.html
   Line: 140
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"V\u00e6ret","item":"https://www.timeanddate.no/vaer/"},{"@type":"ListItem","
   Risk: LOW - Debug code should be removed from production

59. Inline Javascript
   File: page_17.html
   Line: 143
   Content: 
     mtt=1;
    
   Risk: LOW - Debug code should be removed from production

60. Inline Javascript
   File: page_17.html
   Line: 166
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

61. Inline Javascript
   File: page_17.html
   Line: 634
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

62. Inline Javascript
   File: page_17.html
   Line: 637
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

63. Inline Javascript
   File: page_17.html
   Line: 663
   Content: 
        var data={"copyright":"Dette innholdet er bare intern bruk hos timeanddate.no","units":{"temp":"°C","prec":"mm","wind":"m\/s","baro":"mbar "},"sunrise":[{"date":17525016E5,"type":"r"},{"date"
   Risk: LOW - Debug code should be removed from production

64. Inline Javascript
   File: page_17.html
   Line: 666
   Content: 
        window.placeid="marokko\/tanger";window.mode="hourbyhour";
       
   Risk: LOW - Debug code should be removed from production

65. Inline Javascript
   File: page_17.html
   Line: 794
   Content: 
      TAD=window.TAD||{};TAD.lon=-5.828;TAD.lat=35.765;
     
   Risk: LOW - Debug code should be removed from production

66. Inline Javascript
   File: page_17.html
   Line: 1885
   Content: 
      TAD.dec=",";TAD.sep=" ";et=1752668266;
cks={};
     
   Risk: LOW - Debug code should be removed from production

67. Inline Javascript
   File: page_17.html
   Line: 1898
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

68. Inline Javascript
   File: page_17.html
   Line: 1909
   Content: 
    mtt=1;var st=new Sticky("wt-hbh");
//Copyright timeanddate.com 2021, do not use without permission
(function(y){function q(d){function q(a,b){var c={n:y,mode:d},g=a.split("-");z?(c.syear=g[0],c.e
   Risk: LOW - Debug code should be removed from production

69. Inline Javascript
   File: page_17.html
   Line: 2184
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

70. Inline Javascript
   File: page_18.html
   Line: 11
   Content: 
 top.location.href='https://www.facebook.com/v20.0/dialog/oauth?client_id=***************&redirect_uri=https%3A%2F%2Ftimeanddate.no%2Fscripts%2Faccountextlogin.php%3Fprovider%3Dfacebook&response_type
   Risk: LOW - Debug code should be removed from production

71. Inline Javascript
   File: page_19.html
   Line: 25
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

72. Inline Javascript
   File: page_19.html
   Line: 138
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Info","item":"https://www.timeanddate.no/info/"},{"@type":"ListItem","position
   Risk: LOW - Debug code should be removed from production

73. Inline Javascript
   File: page_19.html
   Line: 159
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

74. Inline Javascript
   File: page_19.html
   Line: 627
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

75. Inline Javascript
   File: page_19.html
   Line: 630
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

76. Inline Javascript
   File: page_19.html
   Line: 1040
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

77. Inline Javascript
   File: page_19.html
   Line: 1316
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

78. Inline Javascript
   File: page_19.html
   Line: 1321
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(){function d(a,c){var b=this;b._element=a;b._target=c.target||a;"string"==typeof c.target&&(b._target=gf(c.target));b._cla
   Risk: LOW - Debug code should be removed from production

79. Inline Javascript
   File: page_2.html
   Line: 25
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

80. Inline Javascript
   File: page_2.html
   Line: 138
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"opprett bruker","item":"https://www.timeanddate.no/bruker/"},{"@type":"ListI
   Risk: LOW - Debug code should be removed from production

81. Inline Javascript
   File: page_2.html
   Line: 161
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

82. Inline Javascript
   File: page_2.html
   Line: 629
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

83. Inline Javascript
   File: page_2.html
   Line: 632
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

84. Inline Javascript
   File: page_2.html
   Line: 668
   Content: 
         document.cookie="FTA=1752496460_561b56fb980010be_19a453162c560c1c13f53f7ca4995539;path=/;secure";if(!window.TAD){window.TAD={};}TAD.fta='1752496460_561b56fb980010be_19a453162c560c1c13f53f7ca
   Risk: LOW - Debug code should be removed from production

85. Inline Javascript
   File: page_2.html
   Line: 803
   Content: 
         function showPrivacySettings() {
if (!window.__tcfapi) {
// Try again in a second
setTimeout(showPrivacySettings, 1000);
return;
}
iH('privacysettings', '<h2 id=innstillinger>Innstillinger f
   Risk: LOW - Debug code should be removed from production

86. Inline Javascript
   File: page_2.html
   Line: 821
   Content: 
    TAD=window.TAD||{};
TAD.ftok = "";
   
   Risk: LOW - Debug code should be removed from production

87. Inline Javascript
   File: page_2.html
   Line: 827
   Content: 
    //Copyright timeanddate.com 2021, do not use without permission
(function(){function d(a,c){var b=this;b._element=a;b._target=c.target||a;"string"==typeof c.target&&(b._target=gf(c.target));b._cl
   Risk: LOW - Debug code should be removed from production

88. Inline Javascript
   File: page_2.html
   Line: 832
   Content: 
    window.social_login=function(name) {
var url='/scripts/accountextlogin.php?provider=' + name;
return !window.open(url,'_blank', 'height=600,width=1000')
}
   
   Risk: LOW - Debug code should be removed from production

89. Inline Javascript
   File: page_2.html
   Line: 1103
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

90. Inline Javascript
   File: page_2.html
   Line: 1108
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(b){aelw("load",function(){var a=gf("ad-wrap"),c="A0";b.getComputedStyle&&(c="A1",a&&(c="A2","none"===getComputedStyle(a).d
   Risk: LOW - Debug code should be removed from production

91. Inline Javascript
   File: page_20.html
   Line: 20
   Content: 
     window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
    
   Risk: LOW - Debug code should be removed from production

92. Inline Javascript
   File: page_20.html
   Line: 28
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"App-er","item":"https://www.timeanddate.no/app/"},{"@type":"ListItem","posit
   Risk: LOW - Debug code should be removed from production

93. Inline Javascript
   File: page_20.html
   Line: 521
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

94. Inline Javascript
   File: page_20.html
   Line: 524
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

95. Inline Javascript
   File: page_20.html
   Line: 944
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

96. Inline Javascript
   File: page_21.html
   Line: 18
   Content: 
   window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
  
   Risk: LOW - Debug code should be removed from production

97. Inline Javascript
   File: page_21.html
   Line: 27
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

98. Inline Javascript
   File: page_21.html
   Line: 140
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Tidssoner","item":"https://www.timeanddate.no/tidssone/"},{"@type":"ListItem",
   Risk: LOW - Debug code should be removed from production

99. Inline Javascript
   File: page_21.html
   Line: 161
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

100. Inline Javascript
   File: page_21.html
   Line: 629
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

101. Inline Javascript
   File: page_21.html
   Line: 632
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

102. Inline Javascript
   File: page_21.html
   Line: 873
   Content: 
       et=1752668267;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d
   Risk: LOW - Debug code should be removed from production

103. Inline Javascript
   File: page_21.html
   Line: 1178
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

104. Inline Javascript
   File: page_21.html
   Line: 1372
   Content: 
    /*//Copyright timeanddate.com 2021, do not use without permission
(function(){it(gebtn("table"),function(a){hC(a,"tb-click")&&it(gebtn("tr",a),function(a){var b=gebtn("a",a);1<=b.length&&ael(a,"c
   Risk: LOW - Debug code should be removed from production

105. Inline Javascript
   File: page_21.html
   Line: 1380
   Content: 
    //Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1
   Risk: LOW - Debug code should be removed from production

106. Inline Javascript
   File: page_21.html
   Line: 1660
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

107. Inline Javascript
   File: page_25.html
   Line: 23
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

108. Inline Javascript
   File: page_25.html
   Line: 136
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Tidssoner","item":"https://www.timeanddate.no/tidssone/"},{"@type":"ListItem",
   Risk: LOW - Debug code should be removed from production

109. Inline Javascript
   File: page_25.html
   Line: 157
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

110. Inline Javascript
   File: page_25.html
   Line: 625
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

111. Inline Javascript
   File: page_25.html
   Line: 628
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

112. Inline Javascript
   File: page_25.html
   Line: 869
   Content: 
       et=1752668267;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d
   Risk: LOW - Debug code should be removed from production

113. Inline Javascript
   File: page_25.html
   Line: 1191
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

114. Inline Javascript
   File: page_25.html
   Line: 1268
   Content: 
    /*//Copyright timeanddate.com 2021, do not use without permission
(function(){it(gebtn("table"),function(a){hC(a,"tb-click")&&it(gebtn("tr",a),function(a){var b=gebtn("a",a);1<=b.length&&ael(a,"c
   Risk: LOW - Debug code should be removed from production

115. Inline Javascript
   File: page_25.html
   Line: 1276
   Content: 
    //Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1
   Risk: LOW - Debug code should be removed from production

116. Inline Javascript
   File: page_25.html
   Line: 1556
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

117. Inline Javascript
   File: page_25.html
   Line: 1561
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(b){aelw("load",function(){var a=gf("ad-wrap"),c="A0";b.getComputedStyle&&(c="A1",a&&(c="A2","none"===getComputedStyle(a).d
   Risk: LOW - Debug code should be removed from production

118. Inline Javascript
   File: page_26.html
   Line: 18
   Content: 
   window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
  
   Risk: LOW - Debug code should be removed from production

119. Inline Javascript
   File: page_26.html
   Line: 27
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

120. Inline Javascript
   File: page_26.html
   Line: 140
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Tidssoner","item":"https://www.timeanddate.no/tidssone/"},{"@type":"ListItem",
   Risk: LOW - Debug code should be removed from production

121. Inline Javascript
   File: page_26.html
   Line: 161
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

122. Inline Javascript
   File: page_26.html
   Line: 629
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

123. Inline Javascript
   File: page_26.html
   Line: 632
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

124. Inline Javascript
   File: page_26.html
   Line: 873
   Content: 
       et=1752668267;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d
   Risk: LOW - Debug code should be removed from production

125. Inline Javascript
   File: page_26.html
   Line: 1190
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

126. Inline Javascript
   File: page_26.html
   Line: 1267
   Content: 
    /*//Copyright timeanddate.com 2021, do not use without permission
(function(){it(gebtn("table"),function(a){hC(a,"tb-click")&&it(gebtn("tr",a),function(a){var b=gebtn("a",a);1<=b.length&&ael(a,"c
   Risk: LOW - Debug code should be removed from production

127. Inline Javascript
   File: page_26.html
   Line: 1275
   Content: 
    //Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1
   Risk: LOW - Debug code should be removed from production

128. Inline Javascript
   File: page_26.html
   Line: 1555
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

129. Inline Javascript
   File: page_26.html
   Line: 1614
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

130. Inline Javascript
   File: page_3.html
   Line: 26
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

131. Inline Javascript
   File: page_3.html
   Line: 139
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Info","item":"https://www.timeanddate.no/info/"},{"@type":"ListItem","positi
   Risk: LOW - Debug code should be removed from production

132. Inline Javascript
   File: page_3.html
   Line: 162
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

133. Inline Javascript
   File: page_3.html
   Line: 630
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

134. Inline Javascript
   File: page_3.html
   Line: 633
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

135. Inline Javascript
   File: page_3.html
   Line: 815
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

136. Inline Javascript
   File: page_3.html
   Line: 1089
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

137. Inline Javascript
   File: page_3.html
   Line: 1094
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(){function d(a,c){var b=this;b._element=a;b._target=c.target||a;"string"==typeof c.target&&(b._target=gf(c.target));b._cla
   Risk: LOW - Debug code should be removed from production

138. Inline Javascript
   File: page_30.html
   Line: 23
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

139. Inline Javascript
   File: page_30.html
   Line: 136
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Tidssoner","item":"https://www.timeanddate.no/tidssone/"},{"@type":"ListItem",
   Risk: LOW - Debug code should be removed from production

140. Inline Javascript
   File: page_30.html
   Line: 157
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

141. Inline Javascript
   File: page_30.html
   Line: 625
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

142. Inline Javascript
   File: page_30.html
   Line: 628
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

143. Inline Javascript
   File: page_30.html
   Line: 869
   Content: 
       et=1752668268;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d
   Risk: LOW - Debug code should be removed from production

144. Inline Javascript
   File: page_30.html
   Line: 1156
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

145. Inline Javascript
   File: page_30.html
   Line: 1233
   Content: 
    /*//Copyright timeanddate.com 2021, do not use without permission
(function(){it(gebtn("table"),function(a){hC(a,"tb-click")&&it(gebtn("tr",a),function(a){var b=gebtn("a",a);1<=b.length&&ael(a,"c
   Risk: LOW - Debug code should be removed from production

146. Inline Javascript
   File: page_30.html
   Line: 1241
   Content: 
    //Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1
   Risk: LOW - Debug code should be removed from production

147. Inline Javascript
   File: page_30.html
   Line: 1521
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

148. Inline Javascript
   File: page_30.html
   Line: 1526
   Content: 
   window.runHeatMap && window.runHeatMap();
  
   Risk: LOW - Debug code should be removed from production

149. Inline Javascript
   File: page_31.html
   Line: 18
   Content: 
   window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
  
   Risk: LOW - Debug code should be removed from production

150. Inline Javascript
   File: page_31.html
   Line: 27
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

151. Inline Javascript
   File: page_31.html
   Line: 140
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Tidssoner","item":"https://www.timeanddate.no/tidssone/"},{"@type":"ListItem",
   Risk: LOW - Debug code should be removed from production

152. Inline Javascript
   File: page_31.html
   Line: 161
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

153. Inline Javascript
   File: page_31.html
   Line: 629
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

154. Inline Javascript
   File: page_31.html
   Line: 632
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

155. Inline Javascript
   File: page_31.html
   Line: 873
   Content: 
       et=1752668268;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d
   Risk: LOW - Debug code should be removed from production

156. Inline Javascript
   File: page_31.html
   Line: 1161
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

157. Inline Javascript
   File: page_31.html
   Line: 1248
   Content: 
    /*//Copyright timeanddate.com 2021, do not use without permission
(function(){it(gebtn("table"),function(a){hC(a,"tb-click")&&it(gebtn("tr",a),function(a){var b=gebtn("a",a);1<=b.length&&ael(a,"c
   Risk: LOW - Debug code should be removed from production

158. Inline Javascript
   File: page_31.html
   Line: 1256
   Content: 
    //Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1
   Risk: LOW - Debug code should be removed from production

159. Inline Javascript
   File: page_31.html
   Line: 1536
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

160. Inline Javascript
   File: page_32.html
   Line: 23
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

161. Inline Javascript
   File: page_32.html
   Line: 136
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Tidssoner","item":"https://www.timeanddate.no/tidssone/"},{"@type":"ListItem",
   Risk: LOW - Debug code should be removed from production

162. Inline Javascript
   File: page_32.html
   Line: 157
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

163. Inline Javascript
   File: page_32.html
   Line: 625
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

164. Inline Javascript
   File: page_32.html
   Line: 628
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

165. Inline Javascript
   File: page_32.html
   Line: 869
   Content: 
       et=1752668268;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d
   Risk: LOW - Debug code should be removed from production

166. Inline Javascript
   File: page_32.html
   Line: 1177
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

167. Inline Javascript
   File: page_32.html
   Line: 1254
   Content: 
    /*//Copyright timeanddate.com 2021, do not use without permission
(function(){it(gebtn("table"),function(a){hC(a,"tb-click")&&it(gebtn("tr",a),function(a){var b=gebtn("a",a);1<=b.length&&ael(a,"c
   Risk: LOW - Debug code should be removed from production

168. Inline Javascript
   File: page_32.html
   Line: 1262
   Content: 
    //Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1
   Risk: LOW - Debug code should be removed from production

169. Inline Javascript
   File: page_32.html
   Line: 1542
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

170. Inline Javascript
   File: page_33.html
   Line: 25
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

171. Inline Javascript
   File: page_33.html
   Line: 138
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Info","item":"https://www.timeanddate.no/info/"},{"@type":"ListItem","position
   Risk: LOW - Debug code should be removed from production

172. Inline Javascript
   File: page_33.html
   Line: 159
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

173. Inline Javascript
   File: page_33.html
   Line: 627
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

174. Inline Javascript
   File: page_33.html
   Line: 630
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

175. Inline Javascript
   File: page_33.html
   Line: 1040
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

176. Inline Javascript
   File: page_33.html
   Line: 1316
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

177. Inline Javascript
   File: page_33.html
   Line: 1321
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(){function d(a,c){var b=this;b._element=a;b._target=c.target||a;"string"==typeof c.target&&(b._target=gf(c.target));b._cla
   Risk: LOW - Debug code should be removed from production

178. Inline Javascript
   File: page_34.html
   Line: 18
   Content: 
   window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
  
   Risk: LOW - Debug code should be removed from production

179. Inline Javascript
   File: page_34.html
   Line: 27
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

180. Inline Javascript
   File: page_34.html
   Line: 140
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Tidssoner","item":"https://www.timeanddate.no/tidssone/"},{"@type":"ListItem",
   Risk: LOW - Debug code should be removed from production

181. Inline Javascript
   File: page_34.html
   Line: 161
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

182. Inline Javascript
   File: page_34.html
   Line: 629
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

183. Inline Javascript
   File: page_34.html
   Line: 632
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

184. Inline Javascript
   File: page_34.html
   Line: 873
   Content: 
       et=1752668268;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d
   Risk: LOW - Debug code should be removed from production

185. Inline Javascript
   File: page_34.html
   Line: 1168
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

186. Inline Javascript
   File: page_34.html
   Line: 1268
   Content: 
    /*//Copyright timeanddate.com 2021, do not use without permission
(function(){it(gebtn("table"),function(a){hC(a,"tb-click")&&it(gebtn("tr",a),function(a){var b=gebtn("a",a);1<=b.length&&ael(a,"c
   Risk: LOW - Debug code should be removed from production

187. Inline Javascript
   File: page_34.html
   Line: 1276
   Content: 
    //Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1
   Risk: LOW - Debug code should be removed from production

188. Inline Javascript
   File: page_34.html
   Line: 1556
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

189. Inline Javascript
   File: page_35.html
   Line: 18
   Content: 
   window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
  
   Risk: LOW - Debug code should be removed from production

190. Inline Javascript
   File: page_35.html
   Line: 27
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

191. Inline Javascript
   File: page_35.html
   Line: 140
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Tidssoner","item":"https://www.timeanddate.no/tidssone/"},{"@type":"ListItem",
   Risk: LOW - Debug code should be removed from production

192. Inline Javascript
   File: page_35.html
   Line: 161
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

193. Inline Javascript
   File: page_35.html
   Line: 629
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

194. Inline Javascript
   File: page_35.html
   Line: 632
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

195. Inline Javascript
   File: page_35.html
   Line: 873
   Content: 
       et=1752668268;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d
   Risk: LOW - Debug code should be removed from production

196. Inline Javascript
   File: page_35.html
   Line: 1221
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

197. Inline Javascript
   File: page_35.html
   Line: 1326
   Content: 
    /*//Copyright timeanddate.com 2021, do not use without permission
(function(){it(gebtn("table"),function(a){hC(a,"tb-click")&&it(gebtn("tr",a),function(a){var b=gebtn("a",a);1<=b.length&&ael(a,"c
   Risk: LOW - Debug code should be removed from production

198. Inline Javascript
   File: page_35.html
   Line: 1334
   Content: 
    //Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1
   Risk: LOW - Debug code should be removed from production

199. Inline Javascript
   File: page_35.html
   Line: 1614
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

200. Inline Javascript
   File: page_35.html
   Line: 1619
   Content: 
   window.runHeatMap && window.runHeatMap();
  
   Risk: LOW - Debug code should be removed from production

201. Inline Javascript
   File: page_36.html
   Line: 22
   Content: 
     window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
    
   Risk: LOW - Debug code should be removed from production

202. Inline Javascript
   File: page_36.html
   Line: 31
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

203. Inline Javascript
   File: page_36.html
   Line: 144
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Klokka","item":"https://www.timeanddate.no/klokka/"},{"@type":"ListItem","po
   Risk: LOW - Debug code should be removed from production

204. Inline Javascript
   File: page_36.html
   Line: 167
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

205. Inline Javascript
   File: page_36.html
   Line: 672
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

206. Inline Javascript
   File: page_36.html
   Line: 675
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

207. Inline Javascript
   File: page_36.html
   Line: 779
   Content: 
       AdMgr.dispSlot(1);
      
   Risk: LOW - Debug code should be removed from production

208. Inline Javascript
   File: page_36.html
   Line: 1110
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

209. Inline Javascript
   File: page_37.html
   Line: 23
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

210. Inline Javascript
   File: page_37.html
   Line: 136
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Tidssoner","item":"https://www.timeanddate.no/tidssone/"},{"@type":"ListItem",
   Risk: LOW - Debug code should be removed from production

211. Inline Javascript
   File: page_37.html
   Line: 157
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

212. Inline Javascript
   File: page_37.html
   Line: 625
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

213. Inline Javascript
   File: page_37.html
   Line: 628
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

214. Inline Javascript
   File: page_37.html
   Line: 869
   Content: 
       et=1752668269;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d
   Risk: LOW - Debug code should be removed from production

215. Inline Javascript
   File: page_37.html
   Line: 1190
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

216. Inline Javascript
   File: page_37.html
   Line: 1267
   Content: 
    /*//Copyright timeanddate.com 2021, do not use without permission
(function(){it(gebtn("table"),function(a){hC(a,"tb-click")&&it(gebtn("tr",a),function(a){var b=gebtn("a",a);1<=b.length&&ael(a,"c
   Risk: LOW - Debug code should be removed from production

217. Inline Javascript
   File: page_37.html
   Line: 1275
   Content: 
    //Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1
   Risk: LOW - Debug code should be removed from production

218. Inline Javascript
   File: page_37.html
   Line: 1555
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

219. Inline Javascript
   File: page_38.html
   Line: 24
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Innstillinger","item":"https://www.timeanddate.no/innstillinger/"},{"@type":
   Risk: LOW - Debug code should be removed from production

220. Inline Javascript
   File: page_38.html
   Line: 504
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

221. Inline Javascript
   File: page_38.html
   Line: 507
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

222. Inline Javascript
   File: page_38.html
   Line: 974
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

223. Inline Javascript
   File: page_40.html
   Line: 18
   Content: 
   window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
  
   Risk: LOW - Debug code should be removed from production

224. Inline Javascript
   File: page_40.html
   Line: 27
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

225. Inline Javascript
   File: page_40.html
   Line: 140
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"emneliste","item":"https://www.timeanddate.no/emner/"},{"@type":"ListItem","po
   Risk: LOW - Debug code should be removed from production

226. Inline Javascript
   File: page_40.html
   Line: 161
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

227. Inline Javascript
   File: page_40.html
   Line: 629
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

228. Inline Javascript
   File: page_40.html
   Line: 632
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

229. Inline Javascript
   File: page_40.html
   Line: 849
   Content: 
        AdMgr.dispSlot(1);
       
   Risk: LOW - Debug code should be removed from production

230. Inline Javascript
   File: page_40.html
   Line: 1123
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

231. Inline Javascript
   File: page_40.html
   Line: 1128
   Content: 
   window.runHeatMap && window.runHeatMap();
  
   Risk: LOW - Debug code should be removed from production

232. Inline Javascript
   File: page_45.html
   Line: 23
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

233. Inline Javascript
   File: page_45.html
   Line: 138
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Klokka","item":"https://www.timeanddate.no/klokka/"},{"@type":"ListItem","po
   Risk: LOW - Debug code should be removed from production

234. Inline Javascript
   File: page_45.html
   Line: 161
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

235. Inline Javascript
   File: page_45.html
   Line: 629
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

236. Inline Javascript
   File: page_45.html
   Line: 632
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

237. Inline Javascript
   File: page_45.html
   Line: 661
   Content: 
        window.placeid="marokko\/tanger";window.mode="dst";
       
   Risk: LOW - Debug code should be removed from production

238. Inline Javascript
   File: page_45.html
   Line: 787
   Content: 
      TAD=window.TAD||{};TAD.lon=-5.828;TAD.lat=35.765;
     
   Risk: LOW - Debug code should be removed from production

239. Inline Javascript
   File: page_45.html
   Line: 1216
   Content: 
      TAD.dec=",";TAD.sep=" ";et=1752668269;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.ge
   Risk: LOW - Debug code should be removed from production

240. Inline Javascript
   File: page_45.html
   Line: 1230
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

241. Inline Javascript
   File: page_45.html
   Line: 1508
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

242. Inline Javascript
   File: page_45.html
   Line: 1659
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

243. Inline Javascript
   File: page_45.html
   Line: 1730
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

244. Inline Javascript
   File: page_45.html
   Line: 2073
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

245. Inline Javascript
   File: page_45.html
   Line: 2078
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(){function d(a,c){var b=this;b._element=a;b._target=c.target||a;"string"==typeof c.target&&(b._target=gf(c.target));b._cla
   Risk: LOW - Debug code should be removed from production

246. Inline Javascript
   File: page_46.html
   Line: 26
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

247. Inline Javascript
   File: page_46.html
   Line: 139
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Tidssoner","item":"https://www.timeanddate.no/tidssoner/"},{"@type":"ListIte
   Risk: LOW - Debug code should be removed from production

248. Inline Javascript
   File: page_46.html
   Line: 162
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

249. Inline Javascript
   File: page_46.html
   Line: 630
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

250. Inline Javascript
   File: page_46.html
   Line: 633
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

251. Inline Javascript
   File: page_46.html
   Line: 1128
   Content: 
   et=1752668269;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d.get
   Risk: LOW - Debug code should be removed from production

252. Inline Javascript
   File: page_46.html
   Line: 1229
   Content: 
      AdMgr.dispSlot(0);
     
   Risk: LOW - Debug code should be removed from production

253. Inline Javascript
   File: page_46.html
   Line: 1306
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1,
   Risk: LOW - Debug code should be removed from production

254. Inline Javascript
   File: page_46.html
   Line: 1585
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

255. Inline Javascript
   File: page_46.html
   Line: 1590
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(){function d(a,c){var b=this;b._element=a;b._target=c.target||a;"string"==typeof c.target&&(b._target=gf(c.target));b._cla
   Risk: LOW - Debug code should be removed from production

256. Inline Javascript
   File: page_5.html
   Line: 23
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

257. Inline Javascript
   File: page_5.html
   Line: 136
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Klokka","item":"https://www.timeanddate.no/klokka/"},{"@type":"ListItem","posi
   Risk: LOW - Debug code should be removed from production

258. Inline Javascript
   File: page_5.html
   Line: 157
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

259. Inline Javascript
   File: page_5.html
   Line: 625
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

260. Inline Javascript
   File: page_5.html
   Line: 628
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

261. Inline Javascript
   File: page_5.html
   Line: 823
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

262. Inline Javascript
   File: page_5.html
   Line: 1101
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

263. Inline Javascript
   File: page_53.html
   Line: 20
   Content: 
   window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
  
   Risk: LOW - Debug code should be removed from production

264. Inline Javascript
   File: page_53.html
   Line: 29
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

265. Inline Javascript
   File: page_53.html
   Line: 144
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"V\u00e6ret","item":"https://www.timeanddate.no/vaer/"},{"@type":"ListItem","
   Risk: LOW - Debug code should be removed from production

266. Inline Javascript
   File: page_53.html
   Line: 147
   Content: 
     mtt=1;
    
   Risk: LOW - Debug code should be removed from production

267. Inline Javascript
   File: page_53.html
   Line: 170
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

268. Inline Javascript
   File: page_53.html
   Line: 638
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

269. Inline Javascript
   File: page_53.html
   Line: 641
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

270. Inline Javascript
   File: page_53.html
   Line: 667
   Content: 
        window.placeid="marokko\/tanger";window.mode="climate";
       
   Risk: LOW - Debug code should be removed from production

271. Inline Javascript
   File: page_53.html
   Line: 795
   Content: 
      TAD=window.TAD||{};TAD.lon=-5.828;TAD.lat=35.765;
     
   Risk: LOW - Debug code should be removed from production

272. Inline Javascript
   File: page_53.html
   Line: 1851
   Content: 
      var data={"months":[{"min":8,"max":17,"mean":13,"name":"jan","prec":84.7 },{"min":9,"max":17,"mean":13,"name":"feb","prec":59.0 },{"min":10,"max":19,"mean":15,"name":"mar","prec":68.1 },{"min":
   Risk: LOW - Debug code should be removed from production

273. Inline Javascript
   File: page_53.html
   Line: 1854
   Content: 
      TAD.dec=",";TAD.sep=" ";et=1752668271;
cks={};
     
   Risk: LOW - Debug code should be removed from production

274. Inline Javascript
   File: page_53.html
   Line: 1867
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

275. Inline Javascript
   File: page_53.html
   Line: 1878
   Content: 
    mtt=1;
   
   Risk: LOW - Debug code should be removed from production

276. Inline Javascript
   File: page_53.html
   Line: 2146
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

277. Inline Javascript
   File: page_53.html
   Line: 2221
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

278. Inline Javascript
   File: page_54.html
   Line: 23
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

279. Inline Javascript
   File: page_54.html
   Line: 136
   Content: 
   {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Dato","item":"https://www.timeanddate.no/dato/"},{"@type":"ListItem","position
   Risk: LOW - Debug code should be removed from production

280. Inline Javascript
   File: page_54.html
   Line: 157
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

281. Inline Javascript
   File: page_54.html
   Line: 625
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

282. Inline Javascript
   File: page_54.html
   Line: 628
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

283. Inline Javascript
   File: page_54.html
   Line: 729
   Content: 
               TAD=window.TAD||{};TAD.dayord="d.M.y";TAD.timord="H:m:s";TAD.ftime="HH:mm:ss";TAD.fyear=1701;TAD.lyear=2099;TAD.co="no";TAD.fdow=1;TAD.wnm={"fdow":1,"fodow":1,"reqd":4,"mo":1,"da":1,"r
   Risk: LOW - Debug code should be removed from production

284. Inline Javascript
   File: page_54.html
   Line: 869
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

285. Inline Javascript
   File: page_54.html
   Line: 1145
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

286. Inline Javascript
   File: page_55.html
   Line: 25
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

287. Inline Javascript
   File: page_55.html
   Line: 138
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Sol &amp; M\u00e5ne","item":"https://www.timeanddate.no/astronomi/"},{"@type
   Risk: LOW - Debug code should be removed from production

288. Inline Javascript
   File: page_55.html
   Line: 161
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

289. Inline Javascript
   File: page_55.html
   Line: 675
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

290. Inline Javascript
   File: page_55.html
   Line: 678
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

291. Inline Javascript
   File: page_55.html
   Line: 827
   Content: 
             TAD=window.TAD||{};TAD.dayord="d.M.y";TAD.timord="H:m:s";TAD.ftime="HH:mm:ss";TAD.fyear=1701;TAD.lyear=2099;TAD.co="no";TAD.fdow=1;TAD.wnm={"fdow":1,"fodow":1,"reqd":4,"mo":1,"da":1,"ro"
   Risk: LOW - Debug code should be removed from production

292. Inline Javascript
   File: page_55.html
   Line: 1360
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

293. Inline Javascript
   File: page_55.html
   Line: 1636
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

294. Inline Javascript
   File: page_58.html
   Line: 22
   Content: 
     window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
    
   Risk: LOW - Debug code should be removed from production

295. Inline Javascript
   File: page_58.html
   Line: 31
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

296. Inline Javascript
   File: page_58.html
   Line: 144
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Info","item":"https://www.timeanddate.no/info/"},{"@type":"ListItem","positi
   Risk: LOW - Debug code should be removed from production

297. Inline Javascript
   File: page_58.html
   Line: 167
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

298. Inline Javascript
   File: page_58.html
   Line: 635
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

299. Inline Javascript
   File: page_58.html
   Line: 638
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

300. Inline Javascript
   File: page_58.html
   Line: 1048
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

301. Inline Javascript
   File: page_58.html
   Line: 1324
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

302. Inline Javascript
   File: page_58.html
   Line: 1329
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(){function d(a,c){var b=this;b._element=a;b._target=c.target||a;"string"==typeof c.target&&(b._target=gf(c.target));b._cla
   Risk: LOW - Debug code should be removed from production

303. Inline Javascript
   File: page_61.html
   Line: 27
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

304. Inline Javascript
   File: page_61.html
   Line: 140
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Klokka","item":"https://www.timeanddate.no/klokka/"},{"@type":"ListItem","po
   Risk: LOW - Debug code should be removed from production

305. Inline Javascript
   File: page_61.html
   Line: 163
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

306. Inline Javascript
   File: page_61.html
   Line: 668
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

307. Inline Javascript
   File: page_61.html
   Line: 671
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

308. Inline Javascript
   File: page_61.html
   Line: 775
   Content: 
       AdMgr.dispSlot(1);
      
   Risk: LOW - Debug code should be removed from production

309. Inline Javascript
   File: page_61.html
   Line: 1106
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

310. Inline Javascript
   File: page_62.html
   Line: 23
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

311. Inline Javascript
   File: page_62.html
   Line: 138
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Klokka","item":"https://www.timeanddate.no/klokka/"},{"@type":"ListItem","po
   Risk: LOW - Debug code should be removed from production

312. Inline Javascript
   File: page_62.html
   Line: 161
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

313. Inline Javascript
   File: page_62.html
   Line: 629
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

314. Inline Javascript
   File: page_62.html
   Line: 632
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

315. Inline Javascript
   File: page_62.html
   Line: 661
   Content: 
        window.placeid="spania\/ceuta";window.mode="dst";
       
   Risk: LOW - Debug code should be removed from production

316. Inline Javascript
   File: page_62.html
   Line: 787
   Content: 
      TAD=window.TAD||{};TAD.lon=-5.319;TAD.lat=35.889;
     
   Risk: LOW - Debug code should be removed from production

317. Inline Javascript
   File: page_62.html
   Line: 1175
   Content: 
      TAD.dec=",";TAD.sep=" ";et=1752668275;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.ge
   Risk: LOW - Debug code should be removed from production

318. Inline Javascript
   File: page_62.html
   Line: 1189
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

319. Inline Javascript
   File: page_62.html
   Line: 1467
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

320. Inline Javascript
   File: page_64.html
   Line: 18
   Content: 
   window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
  
   Risk: LOW - Debug code should be removed from production

321. Inline Javascript
   File: page_64.html
   Line: 27
   Content: 
   AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0}

   Risk: LOW - Debug code should be removed from production

322. Inline Javascript
   File: page_64.html
   Line: 142
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Klokka","item":"https://www.timeanddate.no/klokka/"},{"@type":"ListItem","po
   Risk: LOW - Debug code should be removed from production

323. Inline Javascript
   File: page_64.html
   Line: 165
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

324. Inline Javascript
   File: page_64.html
   Line: 633
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

325. Inline Javascript
   File: page_64.html
   Line: 636
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

326. Inline Javascript
   File: page_64.html
   Line: 665
   Content: 
        window.placeid="spania\/cadiz";window.mode="dst";
       
   Risk: LOW - Debug code should be removed from production

327. Inline Javascript
   File: page_64.html
   Line: 791
   Content: 
      TAD=window.TAD||{};TAD.lon=-6.295;TAD.lat=36.531;
     
   Risk: LOW - Debug code should be removed from production

328. Inline Javascript
   File: page_64.html
   Line: 1168
   Content: 
      TAD.dec=",";TAD.sep=" ";et=1752668275;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.ge
   Risk: LOW - Debug code should be removed from production

329. Inline Javascript
   File: page_64.html
   Line: 1182
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

330. Inline Javascript
   File: page_64.html
   Line: 1460
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

331. Inline Javascript
   File: page_65.html
   Line: 28
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

332. Inline Javascript
   File: page_65.html
   Line: 141
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Klokka"}]}
    
   Risk: LOW - Debug code should be removed from production

333. Inline Javascript
   File: page_65.html
   Line: 164
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

334. Inline Javascript
   File: page_65.html
   Line: 632
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

335. Inline Javascript
   File: page_65.html
   Line: 635
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

336. Inline Javascript
   File: page_65.html
   Line: 997
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

337. Inline Javascript
   File: page_65.html
   Line: 2743
   Content: 
      et=1752668275;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d.
   Risk: LOW - Debug code should be removed from production

338. Inline Javascript
   File: page_65.html
   Line: 2767
   Content: 
    //Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1
   Risk: LOW - Debug code should be removed from production

339. Inline Javascript
   File: page_65.html
   Line: 3047
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

340. Inline Javascript
   File: page_65.html
   Line: 3052
   Content: 
   window.runHeatMap && window.runHeatMap();
  
   Risk: LOW - Debug code should be removed from production

341. Inline Javascript
   File: page_66.html
   Line: 22
   Content: 
     window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
    
   Risk: LOW - Debug code should be removed from production

342. Inline Javascript
   File: page_66.html
   Line: 31
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

343. Inline Javascript
   File: page_66.html
   Line: 144
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Sol &amp; M\u00e5ne","item":"https://www.timeanddate.no/astronomi/"},{"@type
   Risk: LOW - Debug code should be removed from production

344. Inline Javascript
   File: page_66.html
   Line: 151
   Content: 
   <svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMinYMin meet" class="solarEclipse"><defs><radialGradient id="sunGrad"><stop offset="0%"
   Risk: LOW - Debug code should be removed from production

345. Inline Javascript
   File: page_66.html
   Line: 154
   Content: 
   <svg width="400" height="400" viewBox="0 0 400 400" preserveAspectRatio="xMinYMin meet" class="lunarEclipse"><clipPath id="clip"><circle cx="0" cy="0" r="100" class="focusBody"></circle></clipPath
   Risk: LOW - Debug code should be removed from production

346. Inline Javascript
   File: page_66.html
   Line: 173
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

347. Inline Javascript
   File: page_66.html
   Line: 641
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

348. Inline Javascript
   File: page_66.html
   Line: 644
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

349. Inline Javascript
   File: page_66.html
   Line: 1445
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

350. Inline Javascript
   File: page_66.html
   Line: 1721
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

351. Inline Javascript
   File: page_7.html
   Line: 20
   Content: 
     window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
    
   Risk: LOW - Debug code should be removed from production

352. Inline Javascript
   File: page_7.html
   Line: 29
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

353. Inline Javascript
   File: page_7.html
   Line: 142
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"opprett bruker","item":"https://www.timeanddate.no/bruker/"},{"@type":"ListI
   Risk: LOW - Debug code should be removed from production

354. Inline Javascript
   File: page_7.html
   Line: 165
   Content: 
         AdMgr.dispSlot(0);
        
   Risk: LOW - Debug code should be removed from production

355. Inline Javascript
   File: page_7.html
   Line: 637
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

356. Inline Javascript
   File: page_7.html
   Line: 640
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

357. Inline Javascript
   File: page_7.html
   Line: 661
   Content: 
         document.cookie="FTA=1752496464_af26184919218d74_c2ca38cb68adb864e8d513f45dfbaaf3;path=/;secure";if(!window.TAD){window.TAD={};}TAD.fta='1752496464_af26184919218d74_c2ca38cb68adb864e8d513f45
   Risk: LOW - Debug code should be removed from production

358. Inline Javascript
   File: page_7.html
   Line: 829
   Content: 
    gf("createform").onsubmit=function(){var a=gf("create");a.disabled=!0;a.value="Please Wait..."};
   
   Risk: LOW - Debug code should be removed from production

359. Inline Javascript
   File: page_7.html
   Line: 1097
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

360. Inline Javascript
   File: page_8.html
   Line: 27
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

361. Inline Javascript
   File: page_8.html
   Line: 140
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Klokka","item":"https://www.timeanddate.no/klokka/"},{"@type":"ListItem","po
   Risk: LOW - Debug code should be removed from production

362. Inline Javascript
   File: page_8.html
   Line: 163
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

363. Inline Javascript
   File: page_8.html
   Line: 631
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='a';
   
   Risk: LOW - Debug code should be removed from production

364. Inline Javascript
   File: page_8.html
   Line: 634
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

365. Inline Javascript
   File: page_8.html
   Line: 964
   Content: 
        AdMgr.dispSlot(0);
       
   Risk: LOW - Debug code should be removed from production

366. Inline Javascript
   File: page_8.html
   Line: 1307
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

367. Inline Javascript
   File: page_8.html
   Line: 1312
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(){function d(a,c){var b=this;b._element=a;b._target=c.target||a;"string"==typeof c.target&&(b._target=gf(c.target));b._cla
   Risk: LOW - Debug code should be removed from production

368. Inline Javascript
   File: page_9.html
   Line: 21
   Content: 
     window.TAD = window.TAD || {};
TAD.stickyViewableTime=1500;
    
   Risk: LOW - Debug code should be removed from production

369. Inline Javascript
   File: page_9.html
   Line: 30
   Content: 
     AdMgr=(function(){var isLog=0,isSent=0,slots=[],slotsR=[],techs={"pb":1},isResent=0,techsR={"pb":1},vals={"gdpr":"na"};
function isSecond(){return(document.referrer||'').indexOf('timeanddate')>0
   Risk: LOW - Debug code should be removed from production

370. Inline Javascript
   File: page_9.html
   Line: 143
   Content: 
     {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Klokka","item":"https://www.timeanddate.no/klokka/"},{"@type":"ListItem","po
   Risk: LOW - Debug code should be removed from production

371. Inline Javascript
   File: page_9.html
   Line: 166
   Content: 
         AdMgr.dispSlot(1);
        
   Risk: LOW - Debug code should be removed from production

372. Inline Javascript
   File: page_9.html
   Line: 634
   Content: 
    window.TAD=window.TAD||{};TAD.abtest='b';
   
   Risk: LOW - Debug code should be removed from production

373. Inline Javascript
   File: page_9.html
   Line: 637
   Content: 
    window.TAD=window.TAD||{};window.TAD.toCdnAssetUrl=function(a){return '//c.tadst.com/no/common/frontend/29/' + a;}
   
   Risk: LOW - Debug code should be removed from production

374. Inline Javascript
   File: page_9.html
   Line: 1296
   Content: 
   et=1752668264;
function f0(d){return TAD.ld[d.getUTCDay()]+' '+d.getUTCDate()+'. '+TAD.lm[d.getUTCMonth()]+' '+d.getUTCFullYear()+' kl. '+p2(d.getUTCHours())+':'+p2(d.getUTCMinutes())+':'+p2(d.get
   Risk: LOW - Debug code should be removed from production

375. Inline Javascript
   File: page_9.html
   Line: 1397
   Content: 
      AdMgr.dispSlot(0);
     
   Risk: LOW - Debug code should be removed from production

376. Inline Javascript
   File: page_9.html
   Line: 1474
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1,
   Risk: LOW - Debug code should be removed from production

377. Inline Javascript
   File: page_9.html
   Line: 1753
   Content: 
   bli();main();
  
   Risk: LOW - Debug code should be removed from production

378. Inline Javascript
   File: page_9.html
   Line: 1758
   Content: 
   //Copyright timeanddate.com 2021, do not use without permission
(function(){function d(a,c){var b=this;b._element=a;b._target=c.target||a;"string"==typeof c.target&&(b._target=gf(c.target));b._cla
   Risk: LOW - Debug code should be removed from production

