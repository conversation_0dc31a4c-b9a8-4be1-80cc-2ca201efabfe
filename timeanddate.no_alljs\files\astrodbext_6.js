//Copyright timeanddate.com 2024, do not use without permission
astRes=function(){this.o=0};astRes.prototype.setDayOffset=function(a){this.o=a};astRes.prototype.getDayCount=function(){return this.d.length};astRes.prototype.getTimeChanges=function(){var a=TO.gTC(astrodb.cks_name),c=this;it(a,function(a){a.i=c.get_index_from_date(a.l)});return a};astRes.prototype.get_index_from_date=function(a){function c(e,l){if(4>l-e){var f=e;for(b=e;b<l&&!(d[b].getDate()>a);b++)f=b;return f}f=Mf((l+e)/2);return d[f].getDate()>a?c(e,f):c(f,l)}var b,d=this.d;return c(0,this.d.length)};
astRes.prototype.get_astDay=function(a){return this.d[a]};astRes.prototype.get_astDay_from_date=function(a){for(var c=0;c<this.d.length;c++)if(this.d[c].getDate().getTime()==a.getTime())return this.d[c];return!1};function astGetTypeText(a){switch(a){case -1:return"Meridian";case -2:return"Anti-Meridian ";case 1:return"Day";case 2:return"Alminnelig tussm\u00f8rke";case 3:return"Nautisk tussm\u00f8rke";case 4:return"Astronomisk tussm\u00f8rke";case 5:return"Natt";default:return""}}
astPeriod=function(a,c,b){this.m=a;this.s=c;this.e=b;this.l=b-c};astPeriod.prototype.getStartTime=function(){return this.s};astPeriod.prototype.getEndTime=function(){return this.e};astPeriod.prototype.getDurationTime=function(){return this.l};astPeriod.prototype.getMainType=function(){return this.m};astPeriod.prototype.getMainTypeText=function(){return astGetTypeText(this.m)};astDayEv=function(a,c,b,d,e){this.d=a;this.e=c;this.y=b;this.m=d;this.s=e};astDayEv.prototype.getMainType=function(){return this.m};
astDayEv.prototype.getType=function(){return this.y};astDayEv.prototype.getMainTypeText=function(){return astGetTypeText(this.m)};astDayEv.prototype.getTime=function(){return this.s};astDay=function(a,c){this.o=a;this.i=c;this.e=[];this.p=[];this.a=[];this.l=[]};astDay.prototype.getEventsCount=function(){return this.e.length};astDay.prototype.getEvent=function(a){return this.e[a]};astDay.prototype.getDayNumber=function(){return this.i};
astDay.prototype.getDate=function(){var a=this.o,c;a&&(c=a.o+this.i);if(c)return new Date(864E5*c)};astDay.prototype.getPassingsOfType=function(a){var c=[];it(this.a,function(b){b.m==a&&c.push(b)});return c};astDay.prototype.getMidDayPassings=function(){return this.getPassingsOfType(-1)};astDay.prototype.getMidnightPassings=function(){return this.getPassingsOfType(-2)};astDay.prototype.getPrevDay=function(){return this.o.d[this.i-1]};astDay.prototype.getNextDay=function(){return this.o.d[this.i+1]};
astDay.prototype.getMidnightToMidnightLength=function(){function a(){var a=e.getPrevDay();if(a){a=a.getMidnightPassings();var c=a.length;if(0<c)return a[c-1].s}return null}function c(){var a=e.getNextDay();return a&&(a=a.getMidnightPassings(),0<a.length)?a[0].s:null}var b=this.getMidnightPassings(),d=b.length,e=this;if(2==d)return b[1].s-b[0].s;if(1==d){var l=b[0].s;if(43200>l){if(b=c(),null!==b)return b+86400-l}else if(d=a(),null!==d)return l-d+86400;return 86400}b=c();d=a();return null!==b&&null!==
d?b-d+172800:0};
astrodb=function(){function a(a){switch(a){case 258:return 1;case 514:case 257:return 2;case 513:case 1026:return 3;case 1025:case 2050:return 4;case 2049:return 5;case 4097:case 4098:return-1;case 8193:case 8194:return-2;case 524288:return 6}return 0}function c(a){it(a.p,function(c){var b=c.m;a.l[b]=(a.l[b]||0)+c.l})}function b(b,l){var f=JSON.parse(b).sunev,e=0,t=f.length,m,p=[],q=0,y=0,u=null,r=new astRes;for(b=0;b<t;b++){var g=f[b],h=g[0];e+=g[1];var n=e;var k=window.TO?TO.uL(d,new Date(1E3*n)).getTime()/
1E3:n;n=k%86400;k=Mf(k/86400);if(0==b){var v=k;r.setDayOffset(v)}k-=v;var w=a(h);6==w&&(w=g[3]);g=new astDayEv(k,e,h,w,n);h=p[k];h||(h=new astDay(r,k),6!=w&&(p[k]=h),q=0);if(0<g.m){h.e.push(g);if(0==q){for(--k;0<=k&&(m=p[k])&&0==m.p.length;)m.p.push(new astPeriod(g.m,0,86400)),k--;u&&(u.p.push(new astPeriod(g.m,y,86400)),c(u))}h.p.push(new astPeriod(g.m,q,n));y=n;u=h;q=n}else h.a.push(g)}v=p.length-1;for(b=0;b<v;b++)for(h=p[b],h.o=r,f=h.e,m=e=0;m<f.length;m++)g=f[m],t=g.s,q=t-e,e=t,g.u=q;r.d=p;l(r)}
var d="smct";return{read:function(a,c,f){f&&(d=f);jcb("/scripts/astroserver.php?n="+a,function(a){b(a,c)})},read_with_year:function(a,c,f,x){x&&(d=x);jcb("/scripts/astroserver.php?n="+a+"&year="+c,function(a){b(a,f)})},cks_name:d}}();
