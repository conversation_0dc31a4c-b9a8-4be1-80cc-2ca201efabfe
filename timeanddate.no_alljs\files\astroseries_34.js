//Copyright timeanddate.com 2024, do not use without permission
var compass_dirs16="N N-N\u00d8 N\u00d8 \u00d8-N\u00d8 \u00d8 \u00d8-S\u00d8 S\u00d8 S-S\u00d8 S S-SV SV V-SV V V-NV NV N-NV".split(" ");function toFixed(a,b){var c=Math.pow(10,b),e=""+Math.round(parseFloat(a)*c)/c,d=e.indexOf(".");0>d&&(d=e.length,e+=".");b||(b=-1);e+="0000000";a=window.TAD;c=".";a&&a.dec&&(c=a.dec,e=e.replace(".",c));b=e.slice(0,d+1+b);if(a&&a.sep)for(c=b.indexOf(c),e="-"==b.charAt(0)?4:3,-1==c&&(c=b.length);c>e;)b=b.slice(0,c-3)+a.sep+b.slice(c-3),c-=3;return b}
function polynom_value(a,b){return a[0]+b*(a[1]+b*(a[2]+b*a[3]))}function Ser(a,b,c,e){this.s=a;this.o=b;this.t=c;this.d=e;this.e=this.i=this.a=null}Ser.prototype.maxTime=function(){var a=this.d;return a[a.length-1].e};Ser.prototype.time=function(a){return{s:a,d:0}};Ser.prototype.date=function(a){return this.s.date(a)};Ser.prototype.getAstroSeries=function(){return this.s};Ser.prototype.getObjectName=function(){return this.o};
Ser.prototype.getAbsoluteMaxValue=function(){function a(a,c,e,h){function d(d){if(!(d<c||d>e)){var g=polynom_value(a,d);g>b.value&&(b.value=g,b.x=d)}}if("undefined"!==typeof a.v)a.v>b.value&&(b.value=a.v,b.x=c);else if(a=a.p,d(c),h&&d(e),a[3]){h=3*a[3];var f=a[2],g=f*f-h*a[1];g?0<g&&(g=Math.sqrt(g),d((-f+g)/h),d((-f-g)/h)):d(-f/h)}else a[2]&&d(-a[1]/(2*a[2]))}var b={x:void 0,value:-Infinity,date:void 0},c=this.d,e=c.length-1;it(c,function(b,f){a(b,f?c[f-1].e:0,b.e,f===e)});b.date=this.date(b.x);return b};
Ser.prototype.get=function(a){var b=this;return function(a){"az"===b.t?360<a?a-=360:0>a&&(a+=360):"ra"===b.t?24<a?a-=24:0>a&&(a+=24):"ang"===b.t&&(a>2*Math.PI?a-=2*Math.PI:0>a&&(a+=2*Math.PI));return a}(function(a,b){for(var c=0,f=b.length;c<f;++c){var g=b[c];if(a<=g.e)return g.p?polynom_value(g.p,a):"undefined"!==typeof g.v?g.v:NaN}}(a,b.d))};
Ser.prototype.isObjectUp=function(a){if("alt"===this.t){var b=this.get(a),c=this.getAstroSeries(),e=this.getObjectName();if(c=c.events(e)){for(var d=null,f=null,g=0;g<c.count();++g){var h=c.get(g);h.isRise()?d=h.time():h.isSet()&&(f=h.time())}if(d&&f)return d<f?a>=d&&a<f:a<f||a>=d;if(d)return a>=d;if(f)return a<f;switch(e){case "moon":case "sun":a=.215;break;default:a=0}return b<=90+a}}};
Ser.prototype.getAltTxt=function(a){if("alt"===this.t){var b=this.get(a),c=this.getObjectName();if("sun"===c)return 108<b?"Natt":102<b?"Astronomisk tussm\u00f8rke":96<b?"Nautisk tussm\u00f8rke":this.isObjectUp(a)?"Day":"Alminnelig tussm\u00f8rke";if("moon"===c)return this.isObjectUp(a)?"M\u00e5nen over horisonten (synlig)":"M\u00e5nen under horisonten (ikke synlig)"}};Ser.prototype.getAzTxt=function(a){a=this.get(a);a=Mf((a+11.25)/22.5%16);return compass_dirs16[a]};
Ser.prototype.upd=function(a){a=this.get(a);if("alt"==this.t)a=toFixed(90-a,2)+"&deg;";else if("az"==this.t){var b=Mf((a+11.25)/22.5%16);a=a=toFixed(a,2)+"&deg; "+compass_dirs16[b]+"<span class='comp sa"+2*b+"'>&uarr;</span>"}"dist"==this.t&&(a*=unit_dist,a=toFixed(a,unit_dist_dig)+" "+unit_dist_txt);iH(this.e,a)};function AEvs(a,b,c){this.s=a;this.n=b;this.d=c}AEvs.prototype.count=function(){return this.d.length};AEvs.prototype.get=function(a){a=this.d[a];return new AEv(this.s,a.s,a.t)};
AEvs.prototype.getForPeriod=function(a,b){var c=this.s,e=c.toSecs(a),d=c.toSecs(b),f=[];it(this.d,function(a){a.s>=e&&a.s<=d&&f.push(a)});return new AEvs(this.s,this.n,f)};function AEv(a,b,c){this.s=a;this.t=b;this.y=c}AEv.prototype.time=function(){return this.t};AEv.prototype.date=function(){return this.s.date(this.t)};AEv.prototype.series=function(){return this.s};
AEv.prototype.typTxt=function(){var a=this.y;switch(a){case 257:return"Rise";case 258:return"Set";case 513:return"CivilTwilightStart";case 514:return"CivilTwilightEnd";case 1025:return"NauticalTwilightStart";case 1026:return"NauticalTwilightEnd";case 2049:return"AstronomicalTwilightStart";case 2050:return"AstronomicalTwilightEnd";case 4098:return"Meridian";case 16384:return"NewMoon";case 16385:return"FirstQuarter";case 16386:return"FullMoon";case 16387:return"ThirdQuarter";case 32768:return"Perigee";
case 65536:return"Apogee";case 2097153:return"GreatestElongation";case 2097154:return"InferiorConjunction";case 2097156:return"SuperiorConjunction";case 2097160:return"Quadrature";case 2097168:return"Conjunction";case 2097184:return"Opposition";case 4194306:return"PartialSolarEclipse";case 4194308:return"TotalSolarEclipse";case 4194312:return"AnnularSolarEclipse";case 8388609:return"PenumbralLunarEclipse";case 8388610:return"PartialLunarEclipse";case 8388612:return"TotalLunarEclipse";case 8388630:return"CloseCallLunarEclipse";
case 16777218:return"PartialMercuryTransit";case 16777224:return"FullMercuryTransit";case 33554434:return"PartialVenusTransit";case 33554440:return"FullVenusTransit";case 67108865:return"HorizonRise";case 67108866:return"HorizonSet";case 134217729:return"HorizonFullRise";case 134217730:return"HorizonPartSet"}var b="";a&1?b+="Penumbral":a&2?b="Partial":a&4?b="Total":a&8?b="Annular":a&16&&(b="CloseCall");a&4194304?b+="SolarEclipse":a&8388608?b+="LunarEclipse":a&16777216?b+="MercuryTransit":a&33554432&&
(b+="VenusTransit");a&32?b+="PenumbralStart":a&64?b+="TotalPenumbralStart":a&128?b+="PartialStart":a&256?b+="FullStart":a&512?b+="Maximum":a&1024?b+="FullEnd":a&2048?b+="PartialEnd":a&4096?b+="TotalPenumbralEnd":a&8192&&(b+="PenumbralEnd");return b};
AEv.prototype.typDesc=function(){switch(this.y){case 257:return"St\u00e5r opp";case 258:return"ned";case 513:return"Alminnelig tussm\u00f8rke start";case 514:return"Alminnelig tussm\u00f8rke slutt";case 1025:return"Nautisk tussm\u00f8rke start";case 1026:return"Nautisk tussm\u00f8rke slutt";case 2049:return"Astronomisk tussm\u00f8rke start";case 2050:return"Astronomisk tussm\u00f8rke slutt";case 4098:return"Meridian";case 16384:return"nym\u00e5ne";case 16385:return"f\u00f8rste kvarter";case 16386:return"fullm\u00e5ne";
case 16387:return"siste kvarter";case 32768:return"Perigeum";case 65536:return"Apogeum";case 2097153:return"St\u00f8rste elongasjon";case 2097154:return"Nedre konjuksjon";case 2097156:return"\u00d8vre konjuksjon";case 2097160:return"Kvadratur";case 2097168:return"Konjunksjon";case 2097184:return"Opposisjon";case 4194306:return"Delvis solform\u00f8rkelse";case 4194308:return"Total solform\u00f8rkelse";case 4194312:return"Ringformet solform\u00f8rkelse";case 8388609:return"Penumbraform\u00f8rkelse";
case 8388610:return"Delvis m\u00e5neform\u00f8rkelse";case 8388612:return"Total m\u00e5neform\u00f8rkelse";case 8388630:return"Nesten m\u00e5neform\u00f8rkelse";case 16777218:return"Delvis Merkur-passasje";case 16777224:return"Full merkurpassasje";case 33554434:return"Delvis Venus-passasje";case 33554440:return"Full venuspassasje";case 67108865:return"Horizon Rise";case 67108866:return"Horizon Set";case 134217729:return"Horizon Full Rise";case 134217730:return"Horizon Partial Set"}};
AEv.prototype.isPhaseEvent=function(){return 16384===this.y||16385===this.y||16386===this.y||16387===this.y};AEv.prototype.isMeridian=function(){return 4098===this.y};AEv.prototype.isRiseSet=function(){return this.isRise()||this.isSet()};AEv.prototype.isRise=function(){return 257===this.y};AEv.prototype.isSet=function(){return 258===this.y};function ASTS(){this.j=null;this.c={}}
ASTS.prototype.fetchWithOptions=function(a,b){var c=this,e="/scripts/astroserver.php?"+au(a);(a=c.c[e])?a&&(c.set(a,1),b&&b()):jcb(e,function(a){var d=window.JSON,g;d&&d.parse?g=d.parse(a.replace(/\bnan\b/g,"null")):eval("results="+a);g&&(c.c[e]=g)&&(c.set(g,1),b&&b())})};ASTS.prototype.fetch=function(a,b,c,e,d,f){this.fetchWithOptions({mode:a,n:b,year:c,month:e,day:d},f)};
ASTS.prototype.set=function(a,b){var c=this;c.j=a;if(!b){var e=[];ia(c.j.o,function(a,b){ia(b,function(b,d){if(d=gf(a+b))b=c.get(a,b),b.e=d,e.push(b)})});e&&TO.aC(function(a){var b=c.toSecs(new Date(a));it(e,function(a){a.upd(b)})})}};ASTS.prototype.get=function(a,b){if(this.j&&this.j.o){var c=this.j.o[a];if(c&&(c=c[b]))return new Ser(this,a,b,c)}};ASTS.prototype.events=function(a){if(this.j&&this.j.e){var b=this.j.e[a];if(b)return new AEvs(this,a,b)}};
ASTS.prototype.date=function(a){a=60*Mf((this.j.t+a)/60);return new Date(1E3*a)};ASTS.prototype.dateSecs=function(a){return new Date(1E3*(this.j.t+a))};ASTS.prototype.toSecs=function(a){var b=this.j;a=Mf(+a/1E3)-b.t;return Math.max(0,Math.min(b.m,a))};"undefined"!==typeof module&&module.exports?module.exports={ASTS:ASTS,AEv:AEv}:aSeries=new ASTS;
