//Copyright timeanddate.com 2024, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(b){var e=0;return function(){return e<b.length?{done:!1,value:b[e++]}:{done:!0}}};$jscomp.arrayIterator=function(b){return{next:$jscomp.arrayIteratorImpl(b)}};$jscomp.makeIterator=function(b){var e="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];return e?e.call(b):$jscomp.arrayIterator(b)};
$jscomp.getGlobal=function(b){return"undefined"!=typeof window&&window===b?b:"undefined"!=typeof global&&null!=global?global:b};$jscomp.global=$jscomp.getGlobal(this);$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(b,e,g){b!=Array.prototype&&b!=Object.prototype&&(b[e]=g.value)};
$jscomp.polyfill=function(b,e,g,k){if(e){g=$jscomp.global;b=b.split(".");for(k=0;k<b.length-1;k++){var a=b[k];a in g||(g[a]={});g=g[a]}b=b[b.length-1];k=g[b];e=e(k);e!=k&&null!=e&&$jscomp.defineProperty(g,b,{configurable:!0,writable:!0,value:e})}};$jscomp.FORCE_POLYFILL_PROMISE=!1;
$jscomp.polyfill("Promise",function(b){function e(){this.batch_=null}function g(d){return d instanceof a?d:new a(function(a,h){a(d)})}if(b&&!$jscomp.FORCE_POLYFILL_PROMISE)return b;e.prototype.asyncExecute=function(a){if(null==this.batch_){this.batch_=[];var d=this;this.asyncExecuteFunction(function(){d.executeBatch_()})}this.batch_.push(a)};var k=$jscomp.global.setTimeout;e.prototype.asyncExecuteFunction=function(a){k(a,0)};e.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var a=
this.batch_;this.batch_=[];for(var c=0;c<a.length;++c){var h=a[c];a[c]=null;try{h()}catch(l){this.asyncThrow_(l)}}}this.batch_=null};e.prototype.asyncThrow_=function(a){this.asyncExecuteFunction(function(){throw a;})};var a=function(a){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];var c=this.createResolveAndReject_();try{a(c.resolve,c.reject)}catch(h){c.reject(h)}};a.prototype.createResolveAndReject_=function(){function a(a){return function(d){h||(h=!0,a.call(c,d))}}var c=this,h=!1;
return{resolve:a(this.resolveTo_),reject:a(this.reject_)}};a.prototype.resolveTo_=function(c){if(c===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(c instanceof a)this.settleSameAsPromise_(c);else{a:switch(typeof c){case "object":var d=null!=c;break a;case "function":d=!0;break a;default:d=!1}d?this.resolveToNonPromiseObj_(c):this.fulfill_(c)}};a.prototype.resolveToNonPromiseObj_=function(a){var c=void 0;try{c=a.then}catch(h){this.reject_(h);return}"function"==typeof c?
this.settleSameAsThenable_(c,a):this.fulfill_(a)};a.prototype.reject_=function(a){this.settle_(2,a)};a.prototype.fulfill_=function(a){this.settle_(1,a)};a.prototype.settle_=function(a,c){if(0!=this.state_)throw Error("Cannot settle("+a+", "+c+"): Promise already settled in state"+this.state_);this.state_=a;this.result_=c;this.executeOnSettledCallbacks_()};a.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var a=0;a<this.onSettledCallbacks_.length;++a)c.asyncExecute(this.onSettledCallbacks_[a]);
this.onSettledCallbacks_=null}};var c=new e;a.prototype.settleSameAsPromise_=function(a){var c=this.createResolveAndReject_();a.callWhenSettled_(c.resolve,c.reject)};a.prototype.settleSameAsThenable_=function(a,c){var h=this.createResolveAndReject_();try{a.call(c,h.resolve,h.reject)}catch(l){h.reject(l)}};a.prototype.then=function(c,b){function h(a,c){return"function"==typeof a?function(c){try{l(a(c))}catch(f){d(f)}}:c}var l,d,m=new a(function(a,c){l=a;d=c});this.callWhenSettled_(h(c,l),h(b,d));return m};
a.prototype.catch=function(a){return this.then(void 0,a)};a.prototype.callWhenSettled_=function(a,b){function h(){switch(l.state_){case 1:a(l.result_);break;case 2:b(l.result_);break;default:throw Error("Unexpected state: "+l.state_);}}var l=this;null==this.onSettledCallbacks_?c.asyncExecute(h):this.onSettledCallbacks_.push(h)};a.resolve=g;a.reject=function(c){return new a(function(a,h){h(c)})};a.race=function(c){return new a(function(a,h){for(var b=$jscomp.makeIterator(c),d=b.next();!d.done;d=b.next())g(d.value).callWhenSettled_(a,
h)})};a.all=function(c){var b=$jscomp.makeIterator(c),h=b.next();return h.done?g([]):new a(function(a,c){function l(c){return function(h){d[c]=h;m--;0==m&&a(d)}}var d=[],m=0;do d.push(void 0),m++,g(h.value).callWhenSettled_(l(d.length-1),c),h=b.next();while(!h.done)})};return a},"es6","es3");$jscomp.SYMBOL_PREFIX="jscomp_symbol_";$jscomp.initSymbol=function(){$jscomp.initSymbol=function(){};$jscomp.global.Symbol||($jscomp.global.Symbol=$jscomp.Symbol)};
$jscomp.SymbolClass=function(b,e){this.$jscomp$symbol$id_=b;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:e})};$jscomp.SymbolClass.prototype.toString=function(){return this.$jscomp$symbol$id_};$jscomp.Symbol=function(){function b(g){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new $jscomp.SymbolClass($jscomp.SYMBOL_PREFIX+(g||"")+"_"+e++,g)}var e=0;return b}();
$jscomp.initSymbolIterator=function(){$jscomp.initSymbol();var b=$jscomp.global.Symbol.iterator;b||(b=$jscomp.global.Symbol.iterator=$jscomp.global.Symbol("Symbol.iterator"));"function"!=typeof Array.prototype[b]&&$jscomp.defineProperty(Array.prototype,b,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}});$jscomp.initSymbolIterator=function(){}};
$jscomp.initSymbolAsyncIterator=function(){$jscomp.initSymbol();var b=$jscomp.global.Symbol.asyncIterator;b||(b=$jscomp.global.Symbol.asyncIterator=$jscomp.global.Symbol("Symbol.asyncIterator"));$jscomp.initSymbolAsyncIterator=function(){}};$jscomp.iteratorPrototype=function(b){$jscomp.initSymbolIterator();b={next:b};b[$jscomp.global.Symbol.iterator]=function(){return this};return b};
$jscomp.iteratorFromArray=function(b,e){$jscomp.initSymbolIterator();b instanceof String&&(b+="");var g=0,k={next:function(){if(g<b.length){var a=g++;return{value:e(a,b[a]),done:!1}}k.next=function(){return{done:!0,value:void 0}};return k.next()}};k[Symbol.iterator]=function(){return k};return k};$jscomp.polyfill("Array.prototype.keys",function(b){return b?b:function(){return $jscomp.iteratorFromArray(this,function(b){return b})}},"es6","es3");
(function e$jscomp$0(b,e,g){function a(d,h){if(!e[d]){if(!b[d]){var l="function"==typeof require&&require;if(!h&&l)return l(d,!0);if(c)return c(d,!0);h=Error("Cannot find module '"+d+"'");throw h.code="MODULE_NOT_FOUND",h;}h=e[d]={exports:{}};b[d][0].call(h.exports,function(c){var h=b[d][1][c];return a(h?h:c)},h,h.exports,e$jscomp$0,b,e,g)}return e[d].exports}for(var c="function"==typeof require&&require,d=0;d<g.length;d++)a(g[d]);return a})({1:[function(b,e,g){b=b(2);_T.control.add("ToggleList",
b)},{2:2}],2:[function(b,e,g){b=function(b,a){var c=this;c._element=b;c._options=a;c._children=arrclone(b.children);c._defaultItem=c._options&&"undefined"!==typeof c._options.defaultitem?c._options.defaultitem:0;c._single=c._options&&"undefined"!==typeof c._options.single?c._options.single:!1;if(!c._children)throw"No child elements found";it(c._children,function(a){ael(a.firstChild,"click",function(){c._toggleActive(a)})});location.hash?c._toggleFromHash():-1!==c._defaultItem&&c._defaultItem<c._children.length&&
c._toggleActive(c._children[c._defaultItem]);window.onhashchange=function(){void 0;c._toggleFromHash()}};b.prototype={_toggleFromHash:function(){var b=location.hash.substr(1);(b=gf(b))&&b.parentNode==this._element&&(this._toggleActive(b,!0),siv(b,1))},_toggleActive:function(b,a){this._single&&it(this._children,function(a){ac(a,"active",0)});a=a?!1:hC(b,"active");ac(b,"active",!a)}};e.exports=b},{}],3:[function(b,e,g){ko.bindingHandlers.popad={init:function(b,a,c,d,m){a=ko.unwrap(a());"login"===a?
popadlogin(b):"register"===a&&popadreg(b)}}},{}],4:[function(b,e,g){ko.bindingHandlers.setToThis={init:function(b,a,c,d,m){var h=a();b.addEventListener("click",function(a){a.preventDefault();h(m.$data);return!1})}}},{}],5:[function(b,e,g){function k(a){7===a.length&&(a=a.substr(1));if(6===a.length){var c=parseInt(a.substr(0,2),16),b=parseInt(a.substr(2,2),16);a=parseInt(a.substr(4,2),16);return 128<=(299*c+587*b+114*a)/1E3?"black":"white"}return"black"}ko.components.register("color-picker",{template:{element:"tmpl-colorPicker"},
viewModel:function(a){var c=this;c.color=a.color;c.defaultColor=ko.utils.unwrapObservable(a.defaultColor);c.hasDefaultColor=!!a.defaultColor;c.hasDefaultColor||(c.defaultColor=a.color());c.visibleColor=ko.pureComputed(function(){return c.color()?c.color():c.defaultColor});c.contrastColor=ko.pureComputed(function(){return k(c.visibleColor())});c.tempColor=ko.observable(c.color()||c.defaultColor);c.contrastTempColor=ko.pureComputed(function(){return k(c.tempColor())});c.colorPalette=a.colorPalette||
"#FF0000 #00FF00 #0000FF #FFFF00 #00FFFF #FF00FF".split(" ");c.extraColorPalette=a.extraColorPalette||"#FF0000 #00FF00 #0000FF #FFFF00 #00FFFF #FF00FF #AA0000 #00AA00 #0000AA #AAAA00 #00AAAA #AA00AA #770000 #007700 #000077 #777700 #007777 #770077 #330000 #003300 #000033 #333300 #003333 #330033".split(" ");c.isPickerVisible=ko.observable(!1);c.isExtraPaletteVisible=ko.observable(!1);c.colors=ko.observableArray(c.colorPalette);c.showPicker=function(){c.isPickerVisible(!c.isPickerVisible())};c.setColor=
function(){c.color(this);c.isPickerVisible(!1)};c.setToDefault=function(){c.color(c.defaultColor);c.isPickerVisible(!1)};c.setTempColor=function(){c.tempColor(this)};c.confirmTempColor=function(){c.color(c.tempColor());c.isPickerVisible(!1)};c.toggleExtraPalette=function(){c.isExtraPaletteVisible()?c.colors(c.colorPalette):c.colors(c.extraColorPalette);c.isExtraPaletteVisible(!c.isExtraPaletteVisible())}}})},{}],6:[function(b,e,g){ko.components.register("date-picker",{viewModel:function(b){var a=
this;a.format=ko.utils.unwrapObservable(b.format);a.dayTemplate=b.dayTemplate;a.monthTemplate=b.monthTemplate;a.yearTemplate=b.yearTemplate;a.sepTemplate=b.sepTemplate;a.index=ko.utils.unwrapObservable(b.index);a.day=ko.observable(b.date().getDate());a.month=ko.observable(b.date().getMonth()+1);a.year=ko.observable(b.date().getFullYear());a._internalDate=ko.observable(b.date());var c=function(){a._noUpdate||b.date(new Date(a.year(),a.month()-1,a.day()))};a.day.subscribe(c);a.month.subscribe(c);a.year.subscribe(c);
b.date.subscribe(function(){a._noUpdate=!0;a.day(b.date().getDate());a.month(b.date().getMonth()+1);a.year(b.date().getFullYear());a._noUpdate=!1});a.inputs=[];c=a.format.split("");ko.utils.arrayForEach(c,function(c){switch(c){case "d":a.inputs.push({template:a.dayTemplate,value:a.day});break;case "M":a.inputs.push({template:a.monthTemplate,value:a.month});break;case "y":a.inputs.push({template:a.yearTemplate,value:a.year});break;default:a.inputs.push({template:a.sepTemplate,value:c})}})},template:'<div data-bind="foreach:inputs"><span data-bind="template:{name: template}"></span></div>'})},
{}],7:[function(b,e,g){function k(a){this.show=a.show;this.title=a.title;this.cancel=a.cancel;this.cancelText=a.cancelText;this.confirm=a.confirm;this.confirmText=a.confirmText}ko.components.register("modal",{template:{element:"modalTemplate"},viewModel:{createViewModel:function(a){var c=new k(a);a.noEscToClose||document.addEventListener("keydown",function(){27==event.keyCode&&c.show()&&c.cancel()});a.cancel||(c.cancel=function(){a.show(0)});return c}}})},{}],8:[function(b,e,g){ko.revertibleObservable=
function(b){var a=b,c=ko.observable(b);b=ko.computed({read:function(){return c()},write:function(a){c(a)}});b.commit=function(){c()!==a&&(c.valueHasMutated(),a=c())};b.reset=function(){c(a)};return b}},{}],9:[function(b,e,g){function k(c){c(a(this,t),a(this,r))}function a(a,c){return function(b){return m(a,c,b)}}function c(a,c,b,h){"function"===typeof b&&(c._onFulfilled=b);"function"===typeof h&&(c._onRejected=h);a._state===n?a[a._pCount++]=c:d(a,c);return c}function d(a,c){return setTimeout(function(){var b=
a._state?c._onFulfilled:c._onRejected;if(void 0===b)m(c,a._state,a._value);else{try{var d=b(a._value)}catch(u){m(c,r,u);return}h(c,d)}})}function m(a,c,b){if(a._state===n){a._state=c;a._value=b;c=0;for(b=a._pCount;c<b;)d(a,a[c++]);return a}}function h(a,c){if(c===a&&c)m(a,r,new TypeError("promise_circular_chain"));else{var b="undefined"===typeof c?"undefined":q(c);if(null===c||"function"!==b&&"object"!==b)m(a,t,c);else{try{var h=c.then}catch(u){m(a,r,u);return}"function"===typeof h?l(a,c,h):m(a,t,
c)}return a}}function l(a,c,b){try{b.call(c,function(b){c&&(c=null,h(a,b))},function(b){c&&(c=null,m(a,r,b))})}catch(v){c&&(m(a,r,v),c=null)}}var q="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"===typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a};k.prototype.then=function(a,b){return c(this,new k(function(){}),a,b)};var r=0,t=1,n=2;k.prototype._state=n;k.prototype._pCount=0;e.exports=k},{}],10:[function(b,
e,g){e.exports=function(b,a){return"undefined"==typeof b||null==b?a:b}},{}],11:[function(b,e,g){b(1);ko.options.deferUpdates=!0;b(4);b(3);b(8);b(7);b(18);b(6);b(5);"undefined"===typeof window.Promise&&(window.Promise=b(9));window.vm=b(14);ko.applyBindings(window.vm)},{1:1,14:14,18:18,3:3,4:4,5:5,6:6,7:7,8:8,9:9}],12:[function(b,e,g){b(11);_T.control.applyBindingsOnLoad()},{11:11}],13:[function(b,e,g){e.exports={dayAdjective:"Hver dag",dayPlural:"dager",weekAdjective:"Hver uke",weekPlural:"uker",monthAdjective:"Hver m\u00e5ned",
monthPlural:"maaneder",yearAdjective:"Hvert \u00e5r",yearPlural:"\u00e5r",never:"Aldri",today:"I dag",BLANK:""}},{}],14:[function(b,e,g){function k(){delete fh.d1;delete fh.m1;delete fh.y1;delete fh.d2;delete fh.m2;delete fh.y2}function a(a){a.ErrorMessage("");a.HasServerError(!1);a.HasNetworkError(!1);a.HasTimedout(!1);a.HasDuplicate(!1)}function c(a,c){a.IsUpdating(!1);c&&(c.errorcode===d.ERRORCODES.BADREQUEST?a.ErrorMessage(c.message.join(", ")):c.errorcode===d.ERRORCODES.SERVERERROR?a.HasServerError(!0):
c.errorcode===d.ERRORCODES.NETWORKERROR?a.HasNetworkError(!0):c.errorcode===d.ERRORCODES.TIMEOUT?a.HasTimedout(!0):"duplicate"===c&&a.HasDuplicate(!0))}var d=b(26),m=b(15),h=b(16);g=b(17);var l=b(22),q=b(23),r=b(19),t=b(21),n=b(20),p=new Date,f={Calendars:m,CurrentYear:ko.observable(p.getFullYear()),CurrentCalendarFilter:ko.observable(),Events:ko.pureComputed(function(){return ko.utils.arrayFilter(h(),function(a){return f.CurrentCalendarFilter()?a.calid()===f.CurrentCalendarFilter().id:h()})}),RepeatOptions:g,
GotoNextYear:function(){f.CurrentYear(parseInt(f.CurrentYear(),10)+1)},GotoPrevYear:function(){f.CurrentYear(parseInt(f.CurrentYear(),10)-1)},ClearCalendarFilter:function(){f.CurrentCalendarFilter("")},AddCalendar:function(){f.CalendarAdding(new r)},ImportCalendar:function(){f.CalendarImporting(new t)},AddEvent:function(){if(0<m().length){var a=f.CurrentCalendarFilter()?f.CurrentCalendarFilter().id:null;f.EventAdding(new n({calid:a}))}else f.noCalendarsModal(!0),f.CalendarAddingEvent=new n,f.CalendarAdding(new r)},
DayClick:function(){if(0<m().length){var a=f.CurrentCalendarFilter()?f.CurrentCalendarFilter().id:null;this.events().length?f.DayInSummary(this):f.EventAdding(new n({calid:a,data:{dtstart:this.date}}))}else f.noCalendarsModal(!0),f.CalendarAddingEvent=new n({data:{dtstart:this.date}}),f.CalendarAdding(new r)},CurrentView:ko.observable("calendar"),SetViewToCalendar:function(){f.CurrentView("calendar")},SetViewToList:function(){f.CurrentView("list")},noCalendarsModal:ko.observable(),EventToDelete:ko.observable(),
CancelEventDelete:function(){f.EventToDelete("")},ConfirmEventDelete:function(){f.IsUpdating(!0);q.DeleteEvent(f.EventToDelete()).then(function(){f.IsUpdating(!1);f.EventToDelete("");a(f)},function(a){c(f,a)})},CalendarToDelete:ko.observable(),CancelCalendarDelete:function(){f.CalendarToDelete("")},ConfirmCalendarDelete:function(){l.DeleteCalendar(f.CalendarToDelete()).then(function(){q.DeleteEventsByCalendar(f.CalendarToDelete());f.CalendarToDelete("");a(f)},function(a){c(f,a)})},CalendarInEdit:ko.observable(),
CancelCalendarEdit:function(){f.CalendarInEdit().resetAll();f.CalendarInEdit("")},ConfirmCalendarEdit:function(){l.EditCalendar(f.CalendarInEdit()).then(function(){f.CalendarInEdit().commitAll();f.CalendarInEdit("");a(f)},function(a){c(f,a)})},CalendarAdding:ko.observable(),CancelCalendarAdd:function(){f.CalendarAdding("")},ConfirmCalendarAdd:function(){a(f);return l.AddCalendar(f.CalendarAdding()).then(function(){f.CalendarAdding("")},function(a){c(f,a)})},CalendarImporting:ko.observable(),onCalendarImportFileChanged:function(a,
c){f.CalendarImporting().file(c.target.files[0])},CancelCalendarImport:function(){f.CalendarImporting("")},ConfirmCalendarImport:function(){a(f);return l.ImportCalendar(f.CalendarImporting()).then(function(){f.CalendarImporting("")},function(a){c(f,a)})},EventAdding:ko.observable(),inEditMode:ko.pureComputed(function(){return f.EventAdding()&&!!f.EventAdding().evid}),CancelEventAdd:function(){f.inEditMode()&&f.EventAdding().resetAll();f.EventAdding("");k();a(f)},ConfirmEventAdd:function(){f.IsUpdating(!0);
f.inEditMode()?q.EditEvent(f.EventAdding(),f.CurrentYear()).then(function(){f.IsUpdating(!1);f.EventAdding().commitAll();f.EventAdding("");a(f)},function(a){c(f,a)}):q.AddEvent(f.EventAdding(),f.CurrentYear()).then(function(){f.IsUpdating(!1);f.EventAdding("");k();a(f)},function(a){c(f,a)})},DayInSummary:ko.observable(),CancelDaySummary:function(){f.DayInSummary("")},AddNewEventFromDaySummary:function(){f.EventAdding(new n({data:{dtstart:f.DayInSummary().date}}));f.DayInSummary("")},HasCalendars:ko.pureComputed(function(){return!!m().length}),
HasEvents:ko.pureComputed(function(){return!!m().length}),HasCalendarFilter:ko.pureComputed(function(){return!!f.CurrentCalendarFilter()}),HasTimedout:ko.observable(!1),IsUpdating:ko.observable(!1),ErrorMessage:ko.observable(""),HasServerError:ko.observable(!1),HasNetworkError:ko.observable(!1),HasDuplicate:ko.observable(!1)};f.CurrentYear.subscribe(function(){0<f.CurrentYear()||f.CurrentYear(p.getFullYear());h.removeAll();f.IsUpdating(!0);q.GetEvents(f.CurrentYear()).then(function(){f.IsUpdating(!1);
a(f)},function(a){c(f,a)})});f.IsUpdating(!0);l.GetCalendars().then(function(){return q.GetEvents(f.CurrentYear()).then(function(){f.IsUpdating(!1)})});e.exports=f},{15:15,16:16,17:17,19:19,20:20,21:21,22:22,23:23,26:26}],15:[function(b,e,g){b=ko.observableArray();e.exports=b},{}],16:[function(b,e,g){b=ko.observableArray();e.exports=b},{}],17:[function(b,e,g){b=b(13);e.exports=[{id:0,name:b.never,plural:b.never},{id:1,name:b.dayAdjective,plural:b.dayPlural},{id:2,name:b.weekAdjective,plural:b.weekPlural},
{id:3,name:b.monthAdjective,plural:b.monthPlural},{id:4,name:b.yearAdjective,plural:b.yearPlural}]},{13:13}],18:[function(b,e,g){function k(c,b,m,h){var l=this;l.monthNumber=m;l.dayNumber=h;l.events=ko.pureComputed(function(){return b()[m]&&b()[m][h]?b()[m][h]:[]});var d=ko.utils.arrayMap(l.events(),function(a){return a.displayColor()});l.colors=ko.utils.arrayGetDistinctValues(d);l.colorSize=1/l.colors.length*100+"%";l.date=c;l.mouseOver=ko.observable(!1);l.setMouseOver=function(){l.mouseOver(!0)};
l.isToday=(new Date(c)).toDateString()===(new Date(a)).toDateString();l.isWeekend=6===c.getDay()||0===c.getDay()}var a=+dt();ko.components.register("calendar",{template:{element:"calendarTemplate"},viewModel:function(a){var c=this;c.year=a.year;c.monthNames=a.monthNames;c.weekDayNames=arrclone(a.weekDayNames);c.firstDay=a.firstDay;c.events=a.events;c.isReady=ko.observable(!1);c.dateOrderedEvents=ko.pureComputed(function(){var a=[];ko.utils.arrayForEach(c.events(),function(c){var b=c.dtstart().getMonth(),
h=c.dtstart().getDate();a[b]||(a[b]=[]);a[b][h]||(a[b][h]=[]);a[b][h].push(c)});return a});c.dates=ko.pureComputed(function(){for(var a=0,b=[];12>a;a++){for(var l=new Date(c.year(),a+1,0),d=-1,e=[],g=1;g<=l.getDate();g++){var n=new Date(c.year(),a,g),p=n.getDay();if(p===c.firstDay||-1===d)d++,e[d]={days:[]};p-=c.firstDay;p=0>p?p+7:p;e[d].days[p]=new k(n,c.dateOrderedEvents,a,g)}b.push({name:c.monthNames[a],weeks:e})}return b});c.weekDayNames=c.weekDayNames.concat(c.weekDayNames.splice(0,c.firstDay))}})},
{}],19:[function(b,e,g){function k(c){c=c||{};this.id=a(c.id,"");this.exportURL="/scripts/ownevents.php/cal/"+this.id+".ics?ft="+TAD.ftok;this.name=ko.revertibleObservable(a(c.name,""));this.anniversary=ko.revertibleObservable(a(c.anniversary,0));this.color=ko.revertibleObservable(a(c.color,"#ccc"))}var a=b(10);k.prototype.commitAll=function(){this.name.commit();this.anniversary.commit();this.color.commit()};k.prototype.resetAll=function(){this.name.reset();this.anniversary.reset();this.color.reset()};
e.exports=k},{10:10}],20:[function(b,e,g){function k(a){var b=this;a=a||{};"undefined"===typeof a.data&&(a.data={});b._raw=a;b.evid=d(a.evid,"");b.calid=ko.revertibleObservable(d(a.calid,""));b.calendar=ko.pureComputed(function(){return c.GetCalendarById(b.calid())});b.title=ko.revertibleObservable(d(a.title,""));b.dtstart_raw=ko.observable(a.dtstart);b.dtstart=ko.revertibleObservable(m(b.dtstart_raw()));b.dtstart_fmt=ko.observable(d(a.dtstart_fmt,""));b.dtend_raw=ko.observable(a.dtend);b.dtend=ko.revertibleObservable(m(b.dtend_raw()));
b.dtend_fmt=ko.observable(d(a.dtend_fmt,""));b.color=ko.revertibleObservable(d(a.color,""));b.displayColor=ko.pureComputed(function(){return b.color()?b.color():b.calendar().color()});b.parent={};b.parent.id=a.data.id;b.parent.summary=ko.revertibleObservable(d(a.data.summary,""));b.parent.dtstart_raw=ko.observable(a.data.dtstart);b.parent.dtstart=ko.revertibleObservable(m(b.parent.dtstart_raw()));b.parent.dtstart_fmt=ko.observable(d(a.data.dtstart_fmt,""));b.parent.dtend_raw=ko.observable(d(a.data.dtend,
a.data.dtstart));b.parent.dtend=ko.revertibleObservable(m(b.parent.dtend_raw()));b.parent.dtend_fmt=ko.observable(d(a.data.dtend_fmt,""));b.parent.rrule_freq=ko.revertibleObservable(d(a.data.rrule_freq,0));b.parent.rrule_count=ko.revertibleObservable(d(a.data.rrule_count,0));b.parent.rrule_interval=ko.revertibleObservable(d(a.data.rrule_interval,1));b.parent.rrule_until_raw=ko.observable(a.data.rrule_until?a.data.rrule_until:a.data.dtstart);b.parent.rrule_until=ko.revertibleObservable(m(b.parent.rrule_until_raw()));
b.parent.rrule_until_fmt=ko.observable(d(a.data.rrule_until_fmt,""));b.parent.rrule_ends=ko.observable(a.data.rrule_until?"2":0<a.data.rrule_count?"1":"0")}var a="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"===typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},c=b(22),d=b(10),m=function(c){if("object"===("undefined"===typeof c?"undefined":a(c)))return c;if("string"===typeof c){var b=/(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})/;
c=c?c.match(b):null;b=new Date;c&&(b=new Date(c[1],c[2]-1,c[3],c[4],c[5],c[6]));return b}return new Date};k.prototype.commitAll=function(){for(var a in this)this.hasOwnProperty(a)&&"function"===typeof this[a].commit&&this[a].commit()};k.prototype.resetAll=function(){for(var a in this)this.hasOwnProperty(a)&&"function"===typeof this[a].reset&&this[a].reset()};e.exports=k},{10:10,22:22}],21:[function(b,e,g){e.exports=function(){this.name=ko.observable();this.anniversary=ko.observable();this.color=ko.observable();
this.file=ko.observable()}},{}],22:[function(b,e,g){var k=(g=b(25))&&g.__esModule?g:{"default":g},a=b(15),c=b(19),d=["#1f9ed9","#77ba42","#ffc90a","#ed7823","#ed1766"];e.exports={GetCalendars:function(){return k["default"].getCalendars().then(function(b){void 0!==b.cal&&0<b.cal.length&&(a.removeAll(),b.cal.forEach(function(b){a.push(new c(b))}))})},AddCalendar:function(b){return b.name().trim()?ko.utils.arrayFirst(a(),function(a){return b.name()===a.name()})?Promise.reject("duplicate"):k["default"].addCalendar(b.name(),
b.anniversary(),d[a().length%d.length]).then(function(b){b.cal&&a.push(new c(b.cal))}):Promise.reject("empty")},ImportCalendar:function(b){return ko.utils.arrayFirst(a(),function(a){return b.name()===a.name()})?Promise.reject("duplicate"):k["default"].importCalendar(b.file(),b.name(),b.anniversary(),d[a().length%d.length]).then(function(b){b.cal&&a.push(new c(b.cal))})},DeleteCalendar:function(b){return k["default"].deleteCalendar(b.id).then(function(){a.remove(b)})},EditCalendar:function(b){return k["default"].editCalendar(b.id,
b.name(),b.anniversary(),b.color()).then(function(b){if(b.cal&&b.cal.id){var c=ko.utils.arrayFirst(a(),function(a){return+a.id===+b.cal.id});c.name(b.cal.name);c.color(b.cal.color);c.anniversary(b.cal.anniversary)}})},GetCalendarById:function(b){return ko.utils.arrayFirst(a(),function(a){return a.id===b})}}},{15:15,19:19,25:25}],23:[function(b,e,g){function k(b){return a["default"].getEvents(b).then(function(a){var b=[];void 0!==a.ev&&a.ev.forEach(function(a){b.push(new d(a))});c(b)})}var a=(g=b(26))&&
g.__esModule?g:{"default":g},c=b(16),d=b(20);e.exports={GetEvents:k,AddEvent:function(b,h){var l=b.parent,e={freq:parseInt(l.rrule_freq(),10),count:parseInt(l.rrule_count(),10),interval:parseInt(l.rrule_interval(),10),until:l.rrule_until(),ends:parseInt(l.rrule_ends(),10)};return a["default"].addEvent(l.summary(),l.dtstart(),b.calid(),b.color(),e).then(function(a){void 0!==a.ev&&(h&&a.year!==h?k(h):a.ev.forEach(function(a){c.push(new d(a))}))})},DeleteEvent:function(b){return a["default"].deleteEvent(b.evid).then(function(){c.remove(function(a){return a.parent.id===
b.parent.id})})},DeleteEventsByCalendar:function(a){c.remove(function(b){return b.calid()===a.id})},EditEvent:function(b,h){var e=b.parent,g={freq:parseInt(e.rrule_freq(),10),count:parseInt(e.rrule_count(),10),interval:parseInt(e.rrule_interval(),10),until:e.rrule_until(),ends:parseInt(e.rrule_ends(),10)};return a["default"].editEvent(b.evid,e.summary(),e.dtstart(),b.calid(),b.color(),g).then(function(a){c.remove(function(a){return a.parent.id===e.id});void 0!==a.ev&&(h&&a.year!==h?k(h):a.ev.forEach(function(a){c.push(new d(a))}))})}}},
{16:16,20:20,26:26}],24:[function(b,e,g){var k={BADREQUEST:1,SERVERERROR:2,NETWORKERROR:3,TIMEOUT:4};e.exports=function(a,b){var c=b||{},e=a;window.delay&&(e+=-1!==e.indexOf("?")?"&__sleep="+window.delay:"?__sleep="+window.delay);window.TAD&&TAD.ftok&&(e+=-1!==e.indexOf("?")?"&ft="+TAD.ftok:"?ft="+TAD.ftok);c={method:"undefined"!==typeof c.method?c.method:"GET",data:"undefined"!==typeof c.data?c.data:{},headers:"undefined"!==typeof c.headers?c.headers:{},formUrlencoded:"undefined"!==typeof c.formUrlencoded?
c.formUrlencoded:!1,multipartFormData:"undefined"!==typeof c.multipartFormData?c.multipartFormData:!1};return new Promise(function(a,b){var d=new XMLHttpRequest,h=new FormData,g="";Object.keys(c.data).forEach(function(a){h.append(a,c.data[a]);g+=a+"="+encodeURIComponent(c.data[a])+"&"});"GET"===c.method&&(e+=-1!==e.indexOf("?")?"&"+g:"?"+g);d.open(c.method,e);d.onload=function(){if(200===d.status){var c="";try{c=JSON.parse(d.responseText)}catch(p){throw Error(p.message+" \n "+d.responseText);}c?a(c):
b({errorcode:k.BADREQUEST,message:c.messages})}else b({errorcode:k.SERVERERROR,message:d.status})};d.onerror=function(){b({errorcode:k.NETWORKERROR,message:"Network"})};d.ontimeout=function(){b({errorcode:k.TIMEOUT,message:"Timeout"})};d.timeout=8E3;Object.keys(c.headers).forEach(function(a){d.setRequestHeader(a,c.headers[a])});"GET"===c.method?d.send():c.formUrlencoded?(d.setRequestHeader("Content-type","application/x-www-form-urlencoded"),d.send(g)):c.multipartFormData?(d.setRequestHeader("Content-type",
"multipart/form-data"),d.send(h)):d.send(JSON.stringify(c.data))})};e.exports.ERRORCODES=k},{}],25:[function(b,e,g){Object.defineProperty(g,"__esModule",{value:!0});var k=b(24);b={getCalendars:function(){return k("/scripts/ownevents.php/cal/")},addCalendar:function(a,b,d){return k("/scripts/ownevents.php/cal/",{method:"POST",data:{name:a,flag_anniversary:+b,color:d},formUrlencoded:!0})},importCalendar:function(a,b,d,e){return k("/scripts/icsupload.php",{method:"POST",data:{file:a,name:b,flag_anniversary:+d,
color:e},multipartFormData:!0})},deleteCalendar:function(a){return k("/scripts/ownevents.php/cal/"+a+"?REQUEST_METHOD=DELETE",{method:"DELETE"})},editCalendar:function(a,b,d,e){return k("/scripts/ownevents.php/cal/"+a,{method:"POST",data:{name:b,flag_anniversary:+d,color:e},formUrlencoded:!0})},ERRORCODES:b(24).ERRORCODES};g["default"]=b},{24:24}],26:[function(b,e,g){function k(a,b,e){var c=3<arguments.length&&void 0!==arguments[3]?arguments[3]:"",d=arguments[4];c={p1d:b.getDate(),p1m:b.getMonth()+
1,p1y:b.getFullYear(),p1i:0,p1s:0,summary:a,rrule_freq:+d.freq,cal:e,color:c};0<d.freq&&(2===d.ends?(c.p3d=d.until.getDate(),c.p3m=d.until.getMonth()+1,c.p3y=d.until.getFullYear()):1===d.ends&&(c.rrule_count=d.count),c.rrule_ends=d.ends,c.rrule_interval=d.interval);return c}Object.defineProperty(g,"__esModule",{value:!0});var a=b(24);b={getEvents:function(b){return a("/scripts/ownevents.php/ev/?year="+b)},addEvent:function(b,d,e,h,g){b=k(b,d,e,h,g);return a("/scripts/ownevents.php/ev/",{method:"POST",
data:b,formUrlencoded:!0})},deleteEvent:function(b){return a("/scripts/ownevents.php/ev/"+b+"?REQUEST_METHOD=DELETE",{method:"DELETE"})},editEvent:function(b,d,e,h,g,q){d=k(d,e,h,g,q);return a("/scripts/ownevents.php/ev/"+b,{method:"POST",data:d,formUrlencoded:!0})},ERRORCODES:b(24).ERRORCODES};g["default"]=b},{24:24}]},{},[12]);
