//Copyright timeanddate.com 2021, do not use without permission
(function(y){function q(d){function q(a,b){var c={n:y,mode:d},g=a.split("-");z?(c.syear=g[0],c.eyear=g[1]):A?(c.starty=a,c.eid=window.eclipseid,c.lat=window.latitude,c.lon=window.longitude):(c.hd=a,window.month&&(c.month=window.month),window.year&&(c.year=window.year));b&&(c.json="1");return"/scripts/cityajax.php?"+au(c)}function B(a){var b=l.innerHTML;"historic"==d?(b=b.split(" for "),0<=a.indexOf("Forrige")&&(a=a.toLowerCase()),ih(l,2==b.length?b[0]+" for "+a:a)):(b=b.split(" \u2014 "),ih(l,2==
b.length?b[0]+" \u2014 "+a:a))}function C(a){if(r[a.value])t(r[a.value]),"function"===typeof window.cityAjaxCallback&&window.cityAjaxCallback(g);else{var b=gx(),c=q(a.value,!0),d=a.options[a.selectedIndex];gp(b,c,function(){var c=rs4(b);if(c){try{var h=eval("("+c+")")}catch(f){return}r[a.value]=h;t(h);D&&l&&B(d.innerHTML);"function"===typeof window.cityAjaxCallback&&window.cityAjaxCallback(g)}})}}function u(a,b){b=cE(b?"TH":"TD");b.innerHTML=a.h;a.s&&(b.className=a.s);a.cs&&(b.colSpan=a.cs);return b}
function t(a){a.footer?(v&&(v.innerHTML=a.footer),w(a.data)):w(a)}function w(a){if(g){var b=g.getElementsByTagName("TR"),c=b.length,d=a.length,m;for(m=0;m<d;m++){var h=a[m],f=b[m],l=h.c.length,e;if(f){var n=f.children.length;f.className=h.s?h.s:"";for(e=0;e<l;e++){var p=h.c[e],k=f.cells[e];k?(k.className=p.s?p.s:"",k.colSpan=p.cs?p.cs:1,k.innerHTML=p.h):f.appendChild(u(p,0===e))}for(e=n-1;e>=l;e--)f.removeChild(f.lastChild)}else{f=cE("TR");f.className=h.s?h.s:"";for(e=0;e<l;e++)f.appendChild(u(h.c[e],
0===e));g.appendChild(f)}}for(m=c-1;m>=d;m--)g.removeChild(g.lastChild)}}var z="dst"==d||"zone"==d,D="hourbyhour"==d||"historic"==d,A="eclipse"==d;var n="dst"==d?"tb-dst":"zone"==d?"tb-zone":"hourbyhour"==d?"wt-hbh":"eclipse"==d?"tb_ec-city":"wt-his";var x=gf(n),k=gf(n+"-select"),l=gf(n+"-title"),g=gebtn0("tbody",x),v=(n=gebtn0("tfoot",x))?gebtn0("td",n):null;k&&(k.onchange=function(){C(this)});window.cityssi=function(a){k&&(k.selectedIndex=a,k.onchange(),siv(l,!0));return!1}}var r={};window.mode&&
q(window.mode)})(window.placeid);
