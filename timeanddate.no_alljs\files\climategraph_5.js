//Copyright timeanddate.com 2021, do not use without permission
(function e$jscomp$0(c,e,f){function k(a,g){if(!e[a]){if(!c[a]){var d="function"==typeof require&&require;if(!g&&d)return d(a,!0);if(b)return b(a,!0);g=Error("Cannot find module '"+a+"'");throw g.code="MODULE_NOT_FOUND",g;}g=e[a]={exports:{}};c[a][0].call(g.exports,function(b){var d=c[a][1][b];return k(d?d:b)},g,g.exports,e$jscomp$0,c,e,f)}return e[a].exports}for(var b="function"==typeof require&&require,a=0;a<f.length;a++)k(f[a]);return k})({1:[function(c,e,f){e=c(3);f=c(4);c=c(5);_T.control.add("ClimateGraph.Graph",
e);_T.control.add("ClimateGraph.Picker",f);_T.control.add("ClimateGraph.SetMonth",c)},{3:3,4:4,5:5}],2:[function(c,e,f){c(1);_T.control.applyBindingsOnLoad()},{1:1}],3:[function(c,e,f){function h(b,a){this._element=b;this._width=b.offsetWidth;this._height=b.offsetHeight;this._options=a;this._data=a.data;this._maxtemp=a.data.max+5;this._mintemp=a.data.min-5;this._maxprec=a.data.precmax+5;this._range=this._maxtemp-this._mintemp;this._draw();b=new Date(dt());this.setMonth(b.getMonth())}var k="january february march april may june july august september october november december".split(" ");
h.prototype={_template:cE("div"),_monthElements:[],_createMonth:function(b){var a=this,d=a._data.months[b.monthid];b.elements=arrclone(b.querySelectorAll("*[data-tad-type]"));it(b.elements,function(b){switch(b.getAttribute("data-tad-type")){case "monthLabel":b.innerHTML=d.name;break;case "highLabel":b.innerHTML=d.max;break;case "lowLabel":b.innerHTML=d.min;break;case "precLabel":b.innerHTML=d.prec||"";break;case "temp":var c=(d.min-a._mintemp)/a._range*100;b.style.height=(d.max-a._mintemp)/a._range*
100-c+"%";b.style.bottom=c+"%";break;case "prec":c=d.prec/a._maxprec*100,b.style.height=c+"%"}})},_draw:function(){var b=this;b._template=b._element.firstElementChild.cloneNode(!0);b._element.innerHTML="";b._monthElements=[];for(var a=0;11>=a;a++)b._monthElements[a]=b._template.cloneNode(!0),b._monthElements[a].monthid=a,b._monthElements[a].className+=" "+k[a],ael(b._monthElements[a],"click",function(){b.setMonth(this.monthid)}),b._element.appendChild(b._monthElements[a]);raf(function(){it(b._monthElements,
function(a){b._createMonth(a)})})},currentMonth:-1,setMonth:function(b){for(var a=document.body,d=0;d<k.length;d++)ac(a,k[d],b==d);ac(a,"allyear",-1==b);this.currentMonth=b;this.onMonthChange(b)},onMonthChange:function(){}};e.exports=h},{}],4:[function(c,e,f){function h(c,b){var a=this;a._element=c;a._options=b;a._graph=gf(b.graph);a._element.value=a._graph.tadControl.currentMonth;a._graph.tadControl.onMonthChange=function(b){a._onMonthChange(b)};ael(a._element,"change",function(){a._onChange()})}
h.prototype={_onMonthChange:function(c){this._element.value=c},_onChange:function(){this._graph.tadControl.setMonth(this._element.value)}};e.exports=h},{}],5:[function(c,e,f){function h(c,b){var a=this;a._element=c;a._options=b;a._graph=gf(b.graph);a._month=b.month;aelc(a._element,function(b){return a._onClick(b)})}h.prototype={_onClick:function(){this._graph.tadControl.setMonth(this._month)}};e.exports=h},{}]},{},[2]);
