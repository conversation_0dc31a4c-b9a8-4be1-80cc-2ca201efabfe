//Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(f,g,a){f!=Array.prototype&&f!=Object.prototype&&(f[g]=a.value)};$jscomp.getGlobal=function(f){return"undefined"!=typeof window&&window===f?f:"undefined"!=typeof global&&null!=global?global:f};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.polyfill=function(f,g,a,c){if(g){a=$jscomp.global;f=f.split(".");for(c=0;c<f.length-1;c++){var e=f[c];e in a||(a[e]={});a=a[e]}f=f[f.length-1];c=a[f];g=g(c);g!=c&&null!=g&&$jscomp.defineProperty(a,f,{configurable:!0,writable:!0,value:g})}};$jscomp.polyfill("Array.prototype.fill",function(f){return f?f:function(f,a,c){var e=this.length||0;0>a&&(a=Math.max(0,e+a));if(null==c||c>e)c=e;c=Number(c);0>c&&(c=Math.max(0,e+c));for(a=Number(a||0);a<c;a++)this[a]=f;return this}},"es6","es3");
(function(){function f(a,c,e){var b=this;b.w=c;b.S=window.gSeries||window.aSeries;var d=b.S;if(d)b.b=d.get(c,"alt"),b.x=d.get(c,"az"),b.af=d.get(c,"ill"),b.ag=d.get(c,"ang"),b.ar=d.get(c,"posang"),b.ae=d.events(c),b.z=b.b.maxTime();else throw"Missing astroseries.js";b.a="string"===typeof a?gf(a):a;b.ab=e||"white";b.h=b.a.offsetHeight;b.i=b.a.offsetWidth;b.c=cE("div",{height:b.h,"class":"cs-bg"},b.a);b.q=cE("div",{height:b.h/2,"class":"cs-ng"},b.a);b.m=0;b.v=[];g?(b.d=cE("canvas",{width:b.i,height:b.h,
"class":"cs-fg"},b.a),b.g()):(document.namespaces.add("v","urn:schemas-microsoft-com:vml"),b.f=cE("v:polyline",{coordorigin:"0,0",coordsize:b.i+","+b.h,width:b.i+"px",filled:"TRUE",stroked:"false",strokecolor:"black",strokeweight:"1px","class":"cs-fg",fillcolor:b.ab},b.a),b.p());b.ad();b.o=cE("div",{"class":"cs-bd"},b.a);b.a.onmouseover=function(){b.e(this)};aelw("resize",function(){b.h=b.a.offsetHeight;b.i=b.a.offsetWidth;b.n(b.m);b.h&&b.i&&b.redraw()})}var g=void 0!==cE("canvas").getContext;f.prototype=
{j:function(a){var c=this.b.get(a);return{x:a/60*(this.i/this.z)*60,y:c/180*this.h,alt:c}},aa:function(){var a=!1,c=!1,e=0;for(i=0;i<this.ae.count();i++){var b=this.ae.get(i);pos=this.j(b.time());var d=-1;if(i+1<this.ae.count()){var f=this.ae.get(i+1);f=this.j(f.time());c?(d=e,e=(e+1)%2,a=!0):pos.x+75>f.x?a?(c=!0,a=!1):(d=e,e=(e+1)%2,a=!0):a&&(a=!1)}else c&&(d=e);this.k(pos.x,b,d)}},k:function(a,c,e){var b=cE("div",{"class":"cs-l"},this.a),d=Math.round(this.x.get(c.time())),f=this.x.getAzTxt(c.time()),
g=this.h/2;b.className+=0===e||a>=this.i-35?" ol":1===e||35>=a?" or":"";c.isMeridian&&(d=178<=d&&182>=d?180:358<=d||2>=d?0:d);b.style.left=a+"px";b.style[-1<e||a>=this.i-35||35>=a?"bottom":"top"]=g+"px";iH(b,"<div>"+c.typDesc()+"</div>"+TO.gF("smct",c.date(),0,timeFmt)+"<div>"+d+"&deg;"+f+"</div>");this.v.push(b)},ad:function(){this.ac=cE("div",{"class":"cs-grid"},this.a);for(i=0;180>=i;i+=30){var a=cE("div",null,this.ac);iH(a,"<span>"+(-i+90)+"&deg;</span>");a.style.top=i/180*this.h+"px"}},p:function(){var a=
this.h/2,c=[],e=[];this.j(0);this.j(this.z);var b;for(b=0;b<=this.z;b+=60){var d=this.j(b);d.x=Math.round(d.x);d.y=Math.round(d.y);90>d.alt?(c.push(d.x),c.push(d.y)):(90<d.alt||(c.push(d.x),c.push(d.y)),e.push(d.x),e.push(d.y))}d=["m",-1,a,"l"];d=d.concat(c);d=d.concat([this.i,a]);d=d.concat([this.i,-1,-1,-1,-1,a]);d=d.concat(["m",-1,this.h]);d=d.concat(["l",-1,a]);d=d.concat(e);d=d.concat([this.i,a,this.i,this.h]);d=d.concat([-1,this.h,-1,a]);this.f.points.value=d.join(" ");this.aa()},g:function(){var a=
cE("canvas",{width:this.i,height:this.h}),c=a.getContext("2d"),e=this.d.getContext("2d"),b=this.j(0),d;this.d.width=this.i;this.d.height=this.h;e.fillStyle=this.ab;e.fillRect(0,0,this.i,this.h);c.beginPath();c.moveTo(0,b.y);for(d=0;d<=this.z;d+=60){var f=this.j(d);c.lineTo(f.x,f.y)}c.lineTo(this.i,this.h/2);c.lineTo(0,this.h/2);c.lineTo(0,b.y);c.fill();c.stroke();e.globalCompositeOperation="destination-out";e.drawImage(a,0,0);e.globalCompositeOperation="source-over";e.strokeStyle="rgba(200,200,200,0.5)";
e.beginPath();e.moveTo(0,b.y);for(d=0;d<=this.z;d+=60)f=this.j(d),e.lineTo(f.x,f.y);e.stroke();this.aa()},y:function(a){var c=this.b.time(a);return{time:c.s,time_formatted:TO.gF("smct",this.S.date(c.s),0,timeFmt),dst:c.d,altitude:-Math.round(this.b.get(a))+90,azimuth:Math.round(this.x.get(a)),cardinal:this.x.getAzTxt(a),phase:this.b.getAltTxt(a),directionValue:Mf((this.x.get(a)+11.25)/22.5%16),illumination:this.af?this.af.get(a):null,angle:this.ag?this.ag.get(a):null,rotation:this.ar?this.ar.get(a):
null}},n:function(a){var c=this.j(a);esa(this.c,{w:c.x});esa(this.o,{y:c.y,x:c.x});this.onupdate(this.y(a))},e:function(a){var c=this;c.u=!0;a.onmouseout=function(){c.u=!1;a.onmouseout="";a.onmousemove="";c.n(c.m)};a.onmousemove=function(a){a=a||event;c.n(60*(void 0===a.offsetX?a.layerX:a.offsetX)/(c.i/c.z*60))}},setTime:function(a,c){function e(a){a=(pn()-b.r)/c;a=lim(a,0,1);var d=b.s+Math.sin(a*Math.PI/2)*(b.t-b.s);b.u||b.n(d);b.m=d;1>a?raf(e):b.l=!1}c=void 0!==c?c:1E3;var b=this;b.r=pn();b.s=b.m;
b.t=lim(a,0,b.z);b.l||(raf(e),b.l=!0)},redraw:function(){for(;0<this.v.length;){var a=this.v.pop();a.parentElement.removeChild(a)}a=this.S;this.b=a.get(this.w,"alt");this.x=a.get(this.w,"az");this.af=a.get(this.w,"ill");this.ag=a.get(this.w,"ang");this.ar=a.get(this.w,"posang");this.ae=a.events(this.w);this.z=this.b.maxTime();g?this.g():this.p()},onupdate:function(a){}};window.CurrentSun=f})();
