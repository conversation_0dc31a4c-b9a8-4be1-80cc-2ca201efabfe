//Copyright timeanddate.com 2021, do not use without permission
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(c,f,d){c!=Array.prototype&&c!=Object.prototype&&(c[f]=d.value)};$jscomp.getGlobal=function(c){return"undefined"!=typeof window&&window===c?c:"undefined"!=typeof global&&null!=global?global:c};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.polyfill=function(c,f,d,e){if(f){d=$jscomp.global;c=c.split(".");for(e=0;e<c.length-1;e++){var h=c[e];h in d||(d[h]={});d=d[h]}c=c[c.length-1];e=d[c];f=f(e);f!=e&&null!=f&&$jscomp.defineProperty(d,c,{configurable:!0,writable:!0,value:f})}};$jscomp.polyfill("Array.prototype.fill",function(c){return c?c:function(c,d,e){var f=this.length||0;0>d&&(d=Math.max(0,f+d));if(null==e||e>f)e=f;e=Number(e);0>e&&(e=Math.max(0,f+e));for(d=Number(d||0);d<e;d++)this[d]=c;return this}},"es6","es3");
(function(){function c(a){return{circle:function(b,e,g,n,c,d){a.lineWidth=c||1;a.strokeStyle=n;a.fillStyle=d;a.beginPath();a.arc(b,e,g,0,2*Math.PI,!0);d&&a.fill();n&&a.stroke();a.closePath()},hand:function(b,e,g,d,c,f,h,l){c=l?0:h/c%f*(360/f);a.save();a.lineWidth=b;a.strokeStyle=e;a.fillStyle=e;a.rotate(c*Math.PI/180);a.beginPath();a.moveTo(0,d);a.lineTo(0,-g);a.stroke();a.closePath();a.restore()},tick:function(b,e){var g=Math.PI/180*(b-90),c=0===b%30?6:4;a.fillStyle="#e8e8e9";0===b%30&&a.fillText(b/
30,(e-5-c)*Math.cos(g)+e,(e-5-c)*Math.sin(g)+e)},helper:function(b,c,g){var d=-90*Math.PI/180,f=((c-b)/3600%12*30-90)*Math.PI/180;a.save();a.beginPath();a.rotate(b/3600%12*30*Math.PI/180);a.fillStyle=c>b?e.helperFwd:e.helperBkw;a.lineWidth=0;a.strokeStyle=e.helperStroke;a.moveTo(0,0);a.arc(0,0,g,f,d,d>f?!1:!0);a.stroke();a.fill();a.closePath();a.restore()},arrow:function(b,c,g){var d=-90*Math.PI/180,f=((c-b)/3600%12*30-90)*Math.PI/180,k=((c-b)/3600%12*30-(c>b?99:81))*Math.PI/180;a.save();a.rotate(b/
3600%12*30*Math.PI/180);a.fillStyle=c>b?e.arrowFwd:e.arrowBkw;a.beginPath();a.arc(0,0,g-25,k,d,d>k?!1:!0);a.arc(0,0,g-30,d,k,d>k?!0:!1);a.lineTo((g-33)*Math.cos(k),(g-33)*Math.sin(k));a.lineTo((g-27)*Math.cos(f),(g-27)*Math.sin(f));a.lineTo((g-22)*Math.cos(k),(g-22)*Math.sin(k));a.fill();a.closePath();a.restore()}}}function f(a){var b=a._canvas.getContext("2d"),d=a._size/2,g=Mf(a._size)/2,f=c(b);a._canvas.width=a._size;a._canvas.height=a._size;b.lineCap="butt";b.textAlign="center";b.textBaseline=
"middle";b.font="15px Arial";b.save();b.translate(d,d);f.circle(0,0,g,null,null,e.clockBg);f.circle(0,0,4,null,null,e.dotBg);f.helper(a._from,a._current,g-4);b.restore();for(var h=6;360>=h;h+=6)f.tick(h,d);b.save();b.translate(d,d);f.hand(3,e.hourFrom,.7*g,0,3600,12,a._from,null);f.hand(3,e.hourTo,.7*g,0,3600,12,a._current,null);f.hand(2,e.minuteFrom,.85*g,0,60,60,a._from,null);f.hand(2,e.minuteTo,.85*g,0,60,60,a._current,null);a._hideSeconds||f.hand(1,e.second,.88*g,.15*g,1,60,a._current,null);f.circle(0,
0,2,null,null,e.dotFg);a._showArrow&&f.arrow(a._from,a._current,g);b.restore()}function d(a,b,d,c){this._forceimage=c;this._element=a;this._from=parseInt(b,10);this._to=parseInt(d,10);this._from=21600>this._from&&64800<this._to?this._from+86400:this._from;this._to=21600>this._to&&64800<this._from?this._to+86400:this._to;this._isPlaying=!1;this._pos=1;this._showArrow=this._hideSeconds=!0;this._init()}var e={helperFwd:"#212121",helperBkw:"#212121",helperStroke:"#48484a",arrowFwd:"#fff176",arrowBkw:"#fff176",
clockBg:"#48484a",dotBg:"#FFFFFF",dotFg:"#49494b",hourFrom:"#AAAAAA",hourTo:"#FFFFFF",minuteFrom:"#AAAAAA",minuteTo:"#FFFFFF",second:"#f34e6c"},h=void 0!==dce("canvas").getContext;d.clocks=[];d.prototype={_init:function(){var a=this;if(!h||this._forceimage){var b=new Image;b.src="/scripts/dstclockimage.php?t1="+this._from+"&t2="+this._to;this._element.appendChild(b)}else this._canvas=dce("canvas",{},this._element),this._play=dce("button",{"class":"play"},this._element),ael(this._play,"click",function(){a.togglePlay()}),
this._size=Math.min(this._canvas.width,this._canvas.height),this._current=this._to,f(this)},_reset:function(){this._isPlaying=!1;this._pos=1;this._showArrow=this._hideSeconds=!0;this._current=this._to;f(this)},_animate:function(){function a(c){c-=d;b._pos=c/15E3;1<=b._pos?b._reset():(b._showArrow=!1,5E3>=c?(b._hideSeconds=!1,b._current=b._from-5+c/1E3):5E3<c&&1E4>=c?(b._hideSeconds=!0,b._current=(c-5E3)/5E3*(b._to-b._from)+b._from):(b._current=b._to+(c-1E4)/1E3,b._hideSeconds=!1),b._isPlaying&&(f(b),
raf(a)))}var b=this,d=pn();raf(a)},togglePlay:function(){this._isPlaying?this._reset():(this._isPlaying=!0,this._pos=0,this._animate())}};window.DstClock=d;var p=gebc("dst-clock"),l;for(l=0;l<p.length;l++){var m=p[l],q=m.getAttribute("data-dst-from"),r=m.getAttribute("data-dst-to"),t=m.getAttribute("data-debug-force-ie");void 0!==q&&void 0!==r&&d.clocks.push(new d(m,q,r,t))}})();
