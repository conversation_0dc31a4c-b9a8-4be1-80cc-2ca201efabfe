//Copyright timeanddate.com 2021, do not use without permission
(function e$jscomp$0(e,g,h){function d(a,c){if(!g[a]){if(!e[a]){var n="function"==typeof require&&require;if(!c&&n)return n(a,!0);if(b)return b(a,!0);c=Error("Cannot find module '"+a+"'");throw c.code="MODULE_NOT_FOUND",c;}c=g[a]={exports:{}};e[a][0].call(c.exports,function(b){var c=e[a][1][b];return d(c?c:b)},c,c.exports,e$jscomp$0,e,g,h)}return g[a].exports}for(var b="function"==typeof require&&require,c=0;c<h.length;c++)d(h[c]);return d})({1:[function(e,g,h){e=e(3);_T.control.add("EclipseSVG",
e)},{3:3}],2:[function(e,g,h){e(1);window.cityAjaxCallback=function(e){_T.control.applyBindings(e)};_T.control.applyBindingsOnLoad()},{1:1}],3:[function(e,g,h){var k=function(d,b){this._element=d;this._options=b;this._element.tadControl=this;!document.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#BasicStructure","1.1")||this._options.forceNoSVG?this._initNoSVG():this._init()};k._counter=0;k.prototype={scale:600,_initNoSVG:function(){var d=new Image,b="?",c=!0,a=this._options.settings;
if(a.hz<-a.sr)d.src="//c.tadst.com/gfx/eclipse-globe/under-horizon.png";else{for(var e in this._options.settings)c||(b+="&"),b+=e+"="+this._options.settings[e],b+="&v2",c=!1;d.src="//c.tadst.com/scripts/eclipsegfx.php"+b}this._element.appendChild(d)},_init:function(){k._counter++;if("undefined"===typeof this._options.template)throw Error("Must define a tempate");this._template=gf(this._options.template);if("undefined"===typeof this._template)throw Error("Template not found");this._element.innerHTML=
this._template.innerHTML;this._positionElements()},_positionElements:function(){var d=this,b=d._element.querySelector("svg"),c=b.width.baseVal.value/2,a=d._options.settings;b.style.width=a.w+"px";b.style.height=a.h+"px";var e="undefined"!==typeof a.sp,g=g=b.querySelectorAll(".focusBody"),h=b.querySelector(".bodies"),p=b.querySelector(".horizon"),f=b.querySelector(".underHorizon");b.querySelector("#clip").id="clip"+k._counter;if(a.hz<-a.sr)h.style.display="none",f.style.display="",void 0;else{if(e)f=
b.querySelector(".penumbra"),b=b.querySelector(".umbra"),f.setAttribute("r",a.sp*d.scale),f.setAttribute("cx",a.mx*d.scale+c),f.setAttribute("cy",a.my*d.scale+c),f.setAttribute("clip-path","url(#clip"+k._counter+")"),b.setAttribute("r",a.su*d.scale),b.setAttribute("cx",a.mx*d.scale+c),b.setAttribute("cy",a.my*d.scale+c),b.setAttribute("clip-path","url(#clip"+k._counter+")");else{f=b.querySelector(".moon");var m=b.querySelector(".totality");b=b.querySelector(".bailybead");f.setAttribute("r",a.mr*d.scale);
f.setAttribute("cx",a.mx*d.scale+c);f.setAttribute("cy",a.my*d.scale+c);f.setAttribute("clip-path","url(#clip"+k._counter+")");var l=a.mr-a.sr;0<l&&Math.abs(a.mx)<l&&Math.abs(a.my)<l&&(m.style.display="",m.setAttribute("r",a.mr*d.scale*1.1),f.setAttribute("clip-path",""),b.style.display="",m=a.mr*d.scale,b.setAttribute("transform","translate("+(m*Math.sin(2.3)+200)+","+(m*Math.cos(2.3)+200)+")"));0>l&&Math.abs(a.mx)<Math.abs(l)&&Math.abs(a.my)<Math.abs(l)&&.02>Math.abs(l)&&100>d._options.settings.w&&
f.setAttribute("r",(a.mr-(.02-Math.abs(l)))*d.scale)}var q=e?a.mr:a.sr;it(g,function(a){a.setAttribute("transform","translate(200,200) scale("+q*d.scale/100+")")});a.hz*d.scale<c&&(a=a.hz*d.scale,c=a<=c-10?-(a-(c-10)):0,d.moveBodies=c,p.style.display="block",h.setAttribute("transform","translate(0,"+c+")"))}}};g.exports=k},{}]},{},[2]);
