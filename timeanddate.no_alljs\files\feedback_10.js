//Copyright timeanddate.com 2021, do not use without permission
prevTopic="none";function updReads(a){he("read"+prevTopic,0);he("read"+a,1);prevTopic=a;checksize()}
function validateForm(){var a=document.feedback;try{fbinit()}catch(f){}var b=gf("bodycmt"),c="",d=a.body.value;3>d.length&&(c="Vennligst fyll inn navn og e-post ovenfor");0<=d.toLowerCase().indexOf("<a")&&(c="Vennligst bruk ren tekst, ikke HTML-kode");fct(a.clientTimeEnd);ih(b,c);he("bodycmt",c);if(c)return checksize(),!1;d=a.email.value;b=!0;chke(d)&&(b=confirm("Your email address is missing or invalid.\n\nWe recommend that you specify a correct email address so that we can contact you.\n\nMany messages without a valid email address are useless, because they do not contain enough details, and often we have a solution, but are not able to get back to the user.\n\nDo you still want to send the feedback without your email address?\n\nClick OK to send, Cancel to enter your address"));b&&
(a=a.sm,a.setAttribute("disabled",!0),a.setAttribute("data-loading",!0));return b}function chke(a){return 0>a.search(/[^@ ]+@[^@ ]+\.[^@ ]+/)}function siv(a){a.scrollIntoView&&a.scrollIntoView(!1)}function fbce(){var a=document.feedback.email.value,b=gf("emailcmt"),c="";chke(a)&&(c="Vennligst oppgi en gyldig e-postadresse");ih(b,c);he("emailcmt",c)}function fct(a){if(a){var b=new Date;a.value=b.toString()+" :: "+b.toLocaleString()}}function setloadtime(){fct(document.feedback.clientTime)}
function fbinit(){if(!window.fbinitRun){var a=window,b=a.document.feedback,c=a.screen,d=a.parent,f=navigator,h=["appCodeName","appName","appVersion","cookieEnabled","userAgent"],e=null;try{b.jsscreen.value=c.width+" "+c.height+" "+c.availWidth+" "+c.availHeight+" "+c.colorDepth+" "}catch(g){b.jsscreen.value="no jssscreen"}try{e=d.document.body,b.jswindow.value=e.offsetWidth+" "+e.offsetHeight+" "+e.clientWidth+" "+e.clientHeight}catch(g){b.jswindow.value="no jswindow"}try{b.referrer.value=d.document.referrer}catch(g){b.referrer.value=
"no referrer"}c="";try{if(e){var k=e.parentNode;k&&(c=k.innerHTML);c||(c=e.innerHTML)}}catch(g){c="no html"}b.pagehtml.value=c;b.purl.value=d.location.href;d!=a&&(b.inFrame.value="1");if(f)for(i in h)m=h[i],b[m].value=f[m];siv(b.sm);fct(b.clientTime);checksize();window.fbinitRun=1}}function checksize(){var a=window,b=a.parent,c=a.document.body;b!=a&&(a=c.scrollHeight-c.clientHeight,0<a&&(b=b.document.getElementById("FBF")))&&(b.height=b.height-0+a+2)}setloadtime();fbinit();
