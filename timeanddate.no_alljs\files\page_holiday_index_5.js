//Copyright timeanddate.com 2021, do not use without permission
/*
 **************** !*\
  !*** ./app.js ***!
  \*************** no exports provided  ./app/JumpList  ./app/ShowMore  ************************* !*\
  !*** ./app/JumpList.js ***!
  \************************ exports provided: default  ./eventBus  ************************* !*\
  !*** ./app/ShowMore.js ***!
  \************************ ************************* !*\
  !*** ./app/eventBus.js ***!
  \*************************/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};$jscomp.arrayIterator=function(a){return{next:$jscomp.arrayIteratorImpl(a)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,d){a!=Array.prototype&&a!=Object.prototype&&(a[b]=d.value)};$jscomp.getGlobal=function(a){return"undefined"!=typeof window&&window===a?a:"undefined"!=typeof global&&null!=global?global:a};$jscomp.global=$jscomp.getGlobal(this);$jscomp.SYMBOL_PREFIX="jscomp_symbol_";$jscomp.initSymbol=function(){$jscomp.initSymbol=function(){};$jscomp.global.Symbol||($jscomp.global.Symbol=$jscomp.Symbol)};
$jscomp.SymbolClass=function(a,b){this.$jscomp$symbol$id_=a;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:b})};$jscomp.SymbolClass.prototype.toString=function(){return this.$jscomp$symbol$id_};$jscomp.Symbol=function(){function a(d){if(this instanceof a)throw new TypeError("Symbol is not a constructor");return new $jscomp.SymbolClass($jscomp.SYMBOL_PREFIX+(d||"")+"_"+b++,d)}var b=0;return a}();
$jscomp.initSymbolIterator=function(){$jscomp.initSymbol();var a=$jscomp.global.Symbol.iterator;a||(a=$jscomp.global.Symbol.iterator=$jscomp.global.Symbol("Symbol.iterator"));"function"!=typeof Array.prototype[a]&&$jscomp.defineProperty(Array.prototype,a,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}});$jscomp.initSymbolIterator=function(){}};
$jscomp.initSymbolAsyncIterator=function(){$jscomp.initSymbol();var a=$jscomp.global.Symbol.asyncIterator;a||(a=$jscomp.global.Symbol.asyncIterator=$jscomp.global.Symbol("Symbol.asyncIterator"));$jscomp.initSymbolAsyncIterator=function(){}};$jscomp.iteratorPrototype=function(a){$jscomp.initSymbolIterator();a={next:a};a[$jscomp.global.Symbol.iterator]=function(){return this};return a};
$jscomp.makeIterator=function(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):$jscomp.arrayIterator(a)};$jscomp.arrayFromIterator=function(a){for(var b,d=[];!(b=a.next()).done;)d.push(b.value);return d};$jscomp.arrayFromIterable=function(a){return a instanceof Array?a:$jscomp.arrayFromIterator($jscomp.makeIterator(a))};
(function(a){function b(e){if(d[e])return d[e].exports;var c=d[e]={i:e,l:!1,exports:{}};a[e].call(c.exports,c,c.exports,b);c.l=!0;return c.exports}var d={};b.m=a;b.c=d;b.d=function(a,c,d){b.o(a,c)||Object.defineProperty(a,c,{enumerable:!0,get:d})};b.r=function(a){$jscomp.initSymbol();$jscomp.initSymbol();"undefined"!==typeof Symbol&&Symbol.toStringTag&&($jscomp.initSymbol(),Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}));Object.defineProperty(a,"__esModule",{value:!0})};b.t=function(a,
c){c&1&&(a=b(a));if(c&8||c&4&&"object"===typeof a&&a&&a.__esModule)return a;var e=Object.create(null);b.r(e);Object.defineProperty(e,"default",{enumerable:!0,value:a});if(c&2&&"string"!=typeof a)for(var d in a)b.d(e,d,function(b){return a[b]}.bind(null,d));return e};b.n=function(a){var c=a&&a.__esModule?function(){return a["default"]}:function(){return a};b.d(c,"a",c);return c};b.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};b.p="";return b(b.s="./app.js")})({"./app.js":function(a,
b,d){d.r(b);a=d("./app/JumpList.js");d=d("./app/ShowMore.js");_T.control.add("HolidayIndex.JumpList",a["default"]);_T.control.add("HolidayIndex.ShowMore",d["default"]);_T.control.applyBindingsOnLoad()},"./app/JumpList.js":function(a,b,d){d.r(b);d.d(b,"default",function(){return c});var e=d("./app/eventBus.js"),c=function(a,b){var c=this;this.element=a;this.options=b;this.items=[].concat($jscomp.arrayFromIterable(this.element.querySelectorAll("a")));this.items.forEach(function(a){a.addEventListener("click",
function(a){var b=document.querySelector(a.target.hash);b&&c.scrollTo(b);a.preventDefault()})})};c.prototype.scrollTo=function(a){e["default"].emit("showMore");setTimeout(function(){siv(a,1)},1)}},"./app/ShowMore.js":function(a,b,d){d.r(b);d.d(b,"default",function(){return c});var e=d("./app/eventBus.js"),c=function(a,b){var c=this;this.element=a;this.options=b;this.allCountriesURL="allcountries";this.visibleClass=this.options.visibleClass||"isVisible";this.clickElement=$jscomp.makeIterator(this.element.children).next().value;
this.clickElement.addEventListener("click",function(a){c.onClick();a.preventDefault()});e["default"].addListener("showMore",function(){c.showMore()});window.location.search.match(/[^?]+/)==this.allCountriesURL&&e["default"].emit("showMore")};c.prototype.showMore=function(){this.element.classList.add(this.visibleClass)};c.prototype.onClick=function(){e["default"].emit("showMore");this.setQuery(this.allCountriesURL)};c.prototype.setQuery=function(a){window.history.replaceState({},null,window.location.pathname+
"?"+a)}},"./app/eventBus.js":function(a,b,d){d.r(b);a=function(){this.events={}};a.prototype.addListener=function(a,b){"object"!==typeof this.events[a]&&(this.events[a]=[]);this.events[a].push(b)};a.prototype.removeListener=function(a,b){"object"===typeof this.events[a]&&(b=this.events[a].indexOf(b),-1<b&&this.events[a].splice(b,1))};a.prototype.emit=function(a,b){for(var d=[],c=1;c<arguments.length;++c)d[c-1]=arguments[c];if("object"===typeof this.events[a]){c=this.events[a].slice();var e=c.length,
f=0;for(f;f<e;f++)c[f].apply(this,d)}};a.prototype.once=function(a,b){this.on(a,function g(){this.removeListener(a,g);b.apply(this,arguments)})};a=new a;b["default"]=a}});
