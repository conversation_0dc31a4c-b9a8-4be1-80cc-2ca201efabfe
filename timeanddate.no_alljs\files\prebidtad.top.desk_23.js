window.pbv='top.desk';
/* prebid.js v9.28.0-pre
Updated: 2025-01-27
Modules: adfBidAdapter, adyoulikeBidAdapter, appnexusBidAdapter, criteoBidAdapter, ixBidAdapter, pubmaticBidAdapter, rubiconBidAdapter, priceFloors */
if(window.pbjs&&window.pbjs.libLoaded)try{window.pbjs.getConfig("debug")&&console.warn("Attempted to load a copy of Prebid.js that clashes with the existing 'pbjs' instance. Load aborted.")}catch(e){}else (function(){
(()=>{var r,t={70433:(r,t,e)=>{function n(r,t,e,n,o){for(t=t.split?t.split("."):t,n=0;n<t.length;n++)r=r?r[t[n]]:o;return r===o?e:r}e.d(t,{A:()=>n})},68128:r=>{
/*
* @license MIT
* Fun Hooks v0.9.10
* (c) @snapwich
*/
u.SYNC=1,u.ASYNC=2,u.QUEUE=4;var t="fun-hooks";var e=Object.freeze({useProxy:!0,ready:0}),n=new WeakMap,o="2,1,0"===[1].reduce((function(r,t,e){return[r,t,e]}),2).toString()?Array.prototype.reduce:function(r,t){var e,n=Object(this),o=n.length>>>0,i=0;if(t)e=t;else{for(;i<o&&!(i in n);)i++;e=n[i++]}for(;i<o;)i in n&&(e=r(e,n[i],i,n)),i++;return e};function i(r,t){return Array.prototype.slice.call(r,t)}var f=Object.assign||function(r){return o.call(i(arguments,1),(function(r,t){return t&&Object.keys(t).forEach((function(e){r[e]=t[e]})),r}),r)};function u(r){var a,c={},l=[];function p(r,t){return"function"==typeof r?h.call(null,"sync",r,t):"string"==typeof r&&"function"==typeof t?h.apply(null,arguments):"object"==typeof r?y.apply(null,arguments):void 0}function y(r,t,e){var n=!0;void 0===t&&(t=Object.getOwnPropertyNames(r),n=!1);var o={},i=["constructor"];do{(t=t.filter((function(t){return!("function"!=typeof r[t]||-1!==i.indexOf(t)||t.match(/^_/))}))).forEach((function(t){var n=t.split(":"),i=n[0],f=n[1]||"sync";if(!o[i]){var u=r[i];o[i]=r[i]=h(f,u,e?[e,i]:void 0)}})),r=Object.getPrototypeOf(r)}while(n&&r);return o}function s(r){var e=Array.isArray(r)?r:r.split(".");return o.call(e,(function(n,o,i){var f=n[o],u=!1;return f||(i===e.length-1?(a||l.push((function(){u||console.warn(t+": referenced '"+r+"' but it was never created")})),n[o]=v((function(r){n[o]=r,u=!0}))):n[o]={})}),c)}function v(r){var t=[],e=[],o=function(){},i={before:function(r,e){return a.call(this,t,"before",r,e)},after:function(r,t){return a.call(this,e,"after",r,t)},getHooks:function(r){var n=t.concat(e);"object"==typeof r&&(n=n.filter((function(t){return Object.keys(r).every((function(e){return t[e]===r[e]}))})));try{f(n,{remove:function(){return n.forEach((function(r){r.remove()})),this}})}catch(r){console.error("error adding `remove` to array, did you modify Array.prototype?")}return n},removeAll:function(){return this.getHooks().remove()}},u={install:function(n,i,f){this.type=n,o=f,f(t,e),r&&r(i)}};return n.set(i.after,u),i;function a(r,n,i,f){var u={hook:i,type:n,priority:f||10,remove:function(){var n=r.indexOf(u);-1!==n&&(r.splice(n,1),o(t,e))}};return r.push(u),r.sort((function(r,t){return t.priority-r.priority})),o(t,e),this}}function h(e,o,c){var p=o.after&&n.get(o.after);if(p){if(p.type!==e)throw t+": recreated hookable with different type";return o}var y,h,d=c?s(c):v(),b={get:function(r,t){return d[t]||Reflect.get.apply(Reflect,arguments)}};return a||l.push(g),r.useProxy&&"function"==typeof Proxy&&Proxy.revocable?h=new Proxy(o,b):(h=function(){return b.apply?b.apply(o,this,i(arguments)):o.apply(this,arguments)},f(h,d)),n.get(h.after).install(e,h,(function(r,t){var n,o=[];r.length||t.length?(r.forEach(f),n=o.push(void 0)-1,t.forEach(f),y=function(r,t,f){var u,a=0,c="async"===e&&"function"==typeof f[f.length-1]&&f.pop();function l(r){"sync"===e?u=r:c&&c.apply(null,arguments)}function p(r){if(o[a]){var n=i(arguments);return p.bail=l,n.unshift(p),o[a++].apply(t,n)}"sync"===e?u=r:c&&c.apply(null,arguments)}return o[n]=function(){var n=i(arguments,1);"async"===e&&c&&(delete p.bail,n.push(p));var o=r.apply(t,n);"sync"===e&&p(o)},p.apply(null,f),u}):y=void 0;function f(r){o.push(r.hook)}g()})),h;function g(){!a&&("sync"!==e||r.ready&u.SYNC)&&("async"!==e||r.ready&u.ASYNC)?"sync"!==e&&r.ready&u.QUEUE?b.apply=function(){var r=arguments;l.push((function(){h.apply(r[1],r[2])}))}:b.apply=function(){throw t+": hooked function not ready"}:b.apply=y}}return(r=f({},e,r)).ready?p.ready=function(){a=!0,function(r){for(var t;t=r.shift();)t()}(l)}:a=!0,p.get=s,p}r.exports=u},63172:(r,t,e)=>{function n(r,t,e){t.split&&(t=t.split("."));for(var n,o,i=0,f=t.length,u=r;i<f&&"__proto__"!=(o=""+t[i++])&&"constructor"!==o&&"prototype"!==o;)u=u[o]=i===f?e:typeof(n=u[o])==typeof t?n:0*t[i]!=0||~(""+t[i]).indexOf(".")?{}:[]}e.d(t,{J:()=>n})},45751:(r,t,e)=>{function n(r){var t,e,o;if(Array.isArray(r)){for(e=Array(t=r.length);t--;)e[t]=(o=r[t])&&"object"==typeof o?n(o):o;return e}if("[object Object]"===Object.prototype.toString.call(r)){for(t in e={},r)"__proto__"===t?Object.defineProperty(e,t,{value:n(r[t]),configurable:!0,enumerable:!0,writable:!0}):e[t]=(o=r[t])&&"object"==typeof o?n(o):o;return e}return r}e.d(t,{Q:()=>n})}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r].call(i.exports,i,i.exports,n),i.exports}n.m=t,r=[],n.O=(t,e,o,i)=>{if(!e){var f=1/0;for(l=0;l<r.length;l++){e=r[l][0],o=r[l][1],i=r[l][2];for(var u=!0,a=0;a<e.length;a++)(!1&i||f>=i)&&Object.keys(n.O).every((r=>n.O[r](e[a])))?e.splice(a--,1):(u=!1,i<f&&(f=i));if(u){r.splice(l--,1);var c=o();void 0!==c&&(t=c)}}return t}i=i||0;for(var l=r.length;l>0&&r[l-1][2]>i;l--)r[l]=r[l-1];r[l]=[e,o,i]},n.n=r=>{var t=r&&r.__esModule?()=>r.default:()=>r;return n.d(t,{a:t}),t},n.d=(r,t)=>{for(var e in t)n.o(t,e)&&!n.o(r,e)&&Object.defineProperty(r,e,{enumerable:!0,get:t[e]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(r){if("object"==typeof window)return window}}(),n.o=(r,t)=>Object.prototype.hasOwnProperty.call(r,t),n.r=r=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},(()=>{var r={39673:0};n.O.j=t=>0===r[t];var t=(t,e)=>{var o,i,f=e[0],u=e[1],a=e[2],c=0;if(f.some((t=>0!==r[t]))){for(o in u)n.o(u,o)&&(n.m[o]=u[o]);if(a)var l=a(n)}for(t&&t(e);c<f.length;c++)i=f[c],n.o(r,i)&&r[i]&&r[i][0](),r[i]=0;return n.O(l)},e=self.pbjsChunk=self.pbjsChunk||[];e.forEach(t.bind(null,0)),e.push=t.bind(null,e.push.bind(e))})();var o=n.O(void 0,[60802,51085],(()=>n(27718)));o=n.O(o)})();
(self.pbjsChunk=self.pbjsChunk||[]).push([[60802],{95789:(e,t,n)=>{n.d(t,{A4:()=>l,J7:()=>u,Pg:()=>g});var r=n(41580),i=n(91069),o=n(15901),s=n(7873),a=n(45569);const d=(0,s.m)(),c="outstream";function l(e){const{url:t,config:n,id:s,callback:l,loaded:u,adUnitCode:g,renderNow:f}=e;this.url=t,this.config=n,this.handlers={},this.id=s,this.renderNow=f,this.loaded=u,this.cmd=[],this.push=e=>{"function"==typeof e?this.loaded?e.call():this.cmd.push(e):(0,i.logError)("Commands given to Renderer.push must be wrapped in a function")},this.callback=l||(()=>{this.loaded=!0,this.process()}),this.render=function(){const e=arguments,n=()=>{this._render?this._render.apply(this,e):(0,i.logWarn)("No render function was provided, please use .setRender on the renderer")};!function(e){const t=d.adUnits,n=(0,o.I6)(t,(t=>t.code===e));if(!n)return!1;const r=n?.renderer,i=!!(r&&r.url&&r.render),s=n?.mediaTypes?.video?.renderer,a=!!(s&&s.url&&s.render);return!!(i&&!0!==r.backupOnly||a&&!0!==s.backupOnly)}(g)?f?n():(this.cmd.unshift(n),(0,r.R)(t,a.tp,c,this.callback,this.documentContext)):((0,i.logWarn)(`External Js not loaded by Renderer since renderer url and callback is already defined on adUnit ${g}`),n())}.bind(this)}function u(e){return!(!e||!e.url&&!e.renderNow)}function g(e,t,n){let r=null;e.config&&e.config.documentResolver&&(r=e.config.documentResolver(t,document,n)),r||(r=document),e.documentContext=r,e.render(t,e.documentContext)}l.install=function(e){let{url:t,config:n,id:r,callback:i,loaded:o,adUnitCode:s,renderNow:a}=e;return new l({url:t,config:n,id:r,callback:i,loaded:o,adUnitCode:s,renderNow:a})},l.prototype.getConfig=function(){return this.config},l.prototype.setRender=function(e){this._render=e},l.prototype.setEventHandlers=function(e){this.handlers=e},l.prototype.handleVideoEvent=function(e){let{id:t,eventName:n}=e;"function"==typeof this.handlers[n]&&this.handlers[n](),(0,i.logMessage)(`Prebid Renderer event for id ${t} type ${n}`)},l.prototype.process=function(){for(;this.cmd.length>0;)try{this.cmd.shift().call()}catch(e){(0,i.logError)("Error processing Renderer command: ",e)}}},76811:(e,t,n)=>{n.d(t,{DL:()=>l,Ml:()=>i,Ue:()=>r,VJ:()=>g,hE:()=>u,hq:()=>c,mo:()=>d,pY:()=>f,qX:()=>o,uc:()=>a,yl:()=>s});const r="accessDevice",i="syncUser",o="enrichUfpd",s="enrichEids",a="fetchBids",d="reportAnalytics",c="transmitEids",l="transmitUfpd",u="transmitPreciseGeo",g="transmitTid",f="loadExternalScript"},83441:(e,t,n)=>{n.d(t,{s:()=>i});var r=n(11445);const i=(0,n(2604).ZI)((e=>r.Ay.resolveAlias(e)))},45569:(e,t,n)=>{n.d(t,{Tn:()=>a,fW:()=>o,tW:()=>i,tp:()=>r,zu:()=>s});const r="prebid",i="bidder",o="userId",s="rtd",a="analytics"},2604:(e,t,n)=>{n.d(t,{Dk:()=>s,Ii:()=>o,TQ:()=>f,U3:()=>m,XG:()=>l,ZI:()=>p,Zw:()=>c,bt:()=>u,e3:()=>g,iK:()=>a,q7:()=>d});var r=n(45569),i=n(16833);const o="component",s=o+"Type",a=o+"Name",d="adapterCode",c="storageType",l="configName",u="syncType",g="syncUrl",f="_config";function p(e){return function(t,n,i){const c={[s]:t,[a]:n,[o]:`${t}.${n}`};return t===r.tW&&(c[d]=e(n)),m(Object.assign(c,i))}}const m=(0,i.A_)("sync",(e=>e))},96953:(e,t,n)=>{n.d(t,{Vx:()=>d,l7:()=>a,p4:()=>h,$V:()=>m,nl:()=>f,ZP:()=>b,$p:()=>y,uD:()=>p});var r=n(70433),i=n(43272),o=n(95139),s=n(76811);const a=["data","ext.data","yob","gender","keywords","kwarray","id","buyeruid","customdata"].map((e=>`user.${e}`)).concat("device.ext.cdep"),d=["user.eids","user.ext.eids"],c=["user.geo.lat","user.geo.lon","device.geo.lat","device.geo.lon"],l=["device.ip"],u=["device.ipv6"];function g(e){return Object.assign({get(){},run(e,t,n,r,i){const o=n&&n[r];if(m(o)&&i()){const e=this.get(o);void 0===e?delete n[r]:n[r]=e}}},e)}function f(e){return e.forEach((e=>{e.paths=e.paths.map((e=>{const t=e.split("."),n=t.pop();return[t.length>0?t.join("."):null,n]}))})),function(t,n){const i=[];for(var o=arguments.length,s=new Array(o>2?o-2:0),a=2;a<o;a++)s[a-2]=arguments[a];const d=p(t,...s);return e.forEach((e=>{if(!1!==t[e.name])for(const[o,s]of e.paths){const a=null==o?n:(0,r.A)(n,o);if(i.push(e.run(n,o,a,s,d.bind(null,e))),!1===t[e.name])return}})),i.filter((e=>null!=e))}}function p(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return function(t){return e.hasOwnProperty(t.name)||(e[t.name]=!!t.applies(...n)),e[t.name]}}function m(e){return null!=e&&("object"!=typeof e||Object.keys(e).length>0)}function h(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o.io;return function(n){return!t(e,n)}}function b(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o.io;return[{name:s.DL,paths:a,applies:h(s.DL,e)},{name:s.hq,paths:d,applies:h(s.hq,e)},{name:s.hE,paths:c,applies:h(s.hE,e),get:e=>Math.round(100*(e+Number.EPSILON))/100},{name:s.hE,paths:l,applies:h(s.hE,e),get:e=>function(e){if(!e)return null;let t=e.split(".").map(Number);if(4!=t.length)return null;let n=[];for(let e=0;e<4;e++){let t=Math.max(0,Math.min(8,24-8*e));n.push(255<<8-t&255)}return t.map(((e,t)=>e&n[t])).join(".")}(e)},{name:s.hE,paths:u,applies:h(s.hE,e),get:e=>function(e){if(!e)return null;let t=e.split(":").map((e=>parseInt(e,16)));for(t=t.map((e=>isNaN(e)?0:e));t.length<8;)t.push(0);if(8!=t.length)return null;let n=[];for(let e=0;e<8;e++){let t=Math.max(0,Math.min(16,64-16*e));n.push(65535<<16-t&65535)}return t.map(((e,t)=>e&n[t])).map((e=>e.toString(16))).join(":")}(e)},{name:s.VJ,paths:["source.tid"],applies:h(s.VJ,e)}].map(g)}const y=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o.io;const t=f(b(e)),n=f(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o.io;return[{name:s.hq,paths:["userId","userIdAsEids"],applies:h(s.hq,e)},{name:s.VJ,paths:["ortb2Imp.ext.tid"],applies:h(s.VJ,e)}].map(g)}(e));return function(e){const r={};return{ortb2:n=>(t(r,n,e),n),bidRequest:t=>(n(r,t,e),t)}}}();(0,o.qB)(s.VJ,"enableTIDs config",(()=>{if(!i.$W.getConfig("enableTIDs"))return{allow:!1,reason:"TIDs are disabled"}}))},95139:(e,t,n)=>{n.d(t,{io:()=>s,qB:()=>o});var r=n(91069),i=n(2604);const[o,s]=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:(0,r.prefixLog)("Activity control:");const t={};function n(e){return t[e]=t[e]||[]}function o(t,n,r,o){let s;try{s=r(o)}catch(r){e.logError(`Exception in rule ${n} for '${t}'`,r),s={allow:!1,reason:r}}return s&&Object.assign({activity:t,name:n,component:o[i.Ii]},s)}const s={};function a(t){let{activity:n,name:r,allow:i,reason:o,component:a}=t;const d=`${r} ${i?"allowed":"denied"} '${n}' for '${a}'${o?":":""}`,c=s.hasOwnProperty(d);if(c&&clearTimeout(s[d]),s[d]=setTimeout((()=>delete s[d]),1e3),!c){const t=[d];o&&t.push(o),(i?e.logInfo:e.logWarn).apply(e,t)}}return[function(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10;const o=n(e),s=o.findIndex((e=>{let[t]=e;return i<t})),a=[i,t,r];return o.splice(s<0?o.length:s,0,a),function(){const e=o.indexOf(a);e>=0&&o.splice(e,1)}},function(e,t){let r,i;for(const[s,d,c]of n(e)){if(r!==s&&i)break;r=s;const n=o(e,d,c,t);if(n){if(!n.allow)return a(n),!1;i=n}}return i&&a(i),!0}]}()},29075:(e,t,n)=>{n.d(t,{$A:()=>T,BS:()=>W,Hh:()=>N,Pk:()=>x,Uc:()=>O,XO:()=>P,_0:()=>D,bw:()=>U,n6:()=>w,qn:()=>j,vB:()=>q,vW:()=>B,vd:()=>k});var r=n(91069),i=n(75023),o=n(78969),s=n(43272),a=n(95789),d=n(71371),c=n(67314),l=n(46031),u=n(16833),g=n(12449),f=n(25555),p=n(11445),m=n(16894),h=n(97779);const{AD_RENDER_FAILED:b,AD_RENDER_SUCCEEDED:y,STALE_RENDER:v,BID_WON:E,EXPIRED_RENDER:A}=o.qY,{EXCEPTION:I}=o.as,T=(0,u.A_)("sync",(function(e){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:f.k.resolve()).then((t=>t??c.n.findBidByAdId(e))).catch((()=>{}))})),w=(0,u.A_)("sync",(function(e){i.emit(E,e),c.n.addWinningBid(e)}));function C(e){let{reason:t,message:n,bid:o,id:s}=e;const a={reason:t,message:n};o&&(a.bid=o,a.adId=o.adId),s&&(a.adId=s),(0,r.logError)(`Error rendering ad (id: ${s}): ${n}`),i.emit(b,a)}function S(e){let{doc:t,bid:n,id:r}=e;const o={doc:t};n&&(o.bid=n),r&&(o.adId=r),p.Ay.callAdRenderSucceededBidder(n.adapterCode||n.bidder,n),i.emit(y,o)}function O(e,t){switch(e.event){case o.qY.AD_RENDER_FAILED:C({bid:t,id:t.adId,reason:e.info.reason,message:e.info.message});break;case o.qY.AD_RENDER_SUCCEEDED:S({doc:null,bid:t,id:t.adId});break;default:(0,r.logError)(`Received event request for unsupported event: '${e.event}' (adId: '${t.adId}')`)}}function B(e,t,n){let{resizeFn:r,fireTrackers:i=g.vO}=n;if("resizeNativeHeight"===e.action)r(e.width,e.height);else i(e,t)}const R={[o.nl.EVENT]:O};R[o.nl.NATIVE]=B;const k=(0,u.A_)("sync",(function(e,t){const{ad:n,adUrl:i,cpm:o,originalCpm:s,width:a,height:d}=e,c={AUCTION_PRICE:s||o,CLICKTHROUGH:t?.clickUrl||""};return{ad:(0,r.replaceMacros)(n,c),adUrl:(0,r.replaceMacros)(i,c),width:a,height:d}})),D=(0,u.A_)("sync",(function(e){let{renderFn:t,resizeFn:n,bidResponse:i,options:s,doc:a,isMainDocument:c=a===document&&!(0,r.inIframe)()}=e;const l=i.mediaType===d.G_;if(c||l)return void C({reason:o.as.PREVENT_WRITING_ON_MAIN_DOCUMENT,message:l?"Cannot render video ad without a renderer":"renderAd was prevented from writing to the main document.",bid:i,id:i.adId});const u=k(i,s);t(Object.assign({adId:i.adId},u));const{width:g,height:f}=u;null!=(g??f)&&n(g,f)}));function U(e){let{renderFn:t,resizeFn:n,adId:a,options:d,bidResponse:c,doc:l}=e;N(c,(()=>{if(null!=c){if((c.status!==o.tl.RENDERED||((0,r.logWarn)(`Ad id ${a} has been rendered before`),i.emit(v,c),!s.$W.getConfig("auctionOptions")?.suppressStaleRender))&&(h.uW.isBidNotExpired(c)||((0,r.logWarn)(`Ad id ${a} has been expired`),i.emit(A,c),!s.$W.getConfig("auctionOptions")?.suppressExpiredRender)))try{D({renderFn:t,resizeFn:n,bidResponse:c,options:d,doc:l})}catch(e){C({reason:o.as.EXCEPTION,message:e.message,id:a,bid:c})}}else C({reason:o.as.CANNOT_FIND_AD,message:`Cannot find ad '${a}'`,id:a})}))}function j(e){const t=(0,m.BO)(e.metrics);t.checkpoint("bidRender"),t.timeBetween("bidWon","bidRender","render.deferred"),t.timeBetween("auctionEnd","bidRender","render.pending"),t.timeBetween("requestBids","bidRender","render.e2e"),e.status=o.tl.RENDERED}D.before((function(e,t){const{bidResponse:n,doc:r}=t;(0,a.J7)(n.renderer)?((0,a.Pg)(n.renderer,n,r),S({doc:r,bid:n,id:n.adId}),e.bail()):e(t)}),100);const _=new WeakMap,$=new WeakSet;function N(e,t){null!=e?(_.set(e,t),e.deferRendering||q(e),x(e)):t()}function x(e){$.has(e)||($.add(e),w(e))}function q(e){const t=_.get(e);t&&(t(),j(e),_.delete(e))}function W(e,t,n){let i;function s(e,n){C(Object.assign({id:t,bid:i},{reason:e,message:n}))}function a(t,n){e.defaultView&&e.defaultView.frameElement&&(t&&(e.defaultView.frameElement.width=t),n&&(e.defaultView.frameElement.height=n))}const d=(c={resizeFn:a},function(e,t,n){R.hasOwnProperty(e)&&R[e](t,n,c)});var c;function u(t){t.ad?(e.write(t.ad),e.close(),S({doc:e,bid:i,id:i.adId})):(0,l.H)(i).then((n=>n(t,{sendMessage:(e,t)=>d(e,t,i),mkFrame:r.createIframe},e.defaultView))).then((()=>S({doc:e,bid:i,id:i.adId})),(e=>{s(e?.reason||o.as.EXCEPTION,e?.message),e?.stack&&(0,r.logError)(e)}));const n=document.createComment(`Creative ${i.creativeId} served by ${i.bidder} Prebid.js Header Bidding`);(0,r.insertElement)(n,e,"html")}try{t&&e?T(t).then((r=>{i=r,U({renderFn:u,resizeFn:a,adId:t,options:{clickUrl:n?.clickThrough},bidResponse:r,doc:e})})):s(o.as.MISSING_DOC_OR_ADID,"missing "+(t?"doc":"adId"))}catch(e){s(I,e.message)}}function P(){if(!window.frames[o.IY])if(document.body){const e=(0,r.createInvisibleIframe)();e.name=o.IY,document.body.appendChild(e)}else window.requestAnimationFrame(P)}},10201:(e,t,n)=>{n.d(t,{U:()=>s});var r=n(7873),i=n(91069);const o=(0,r.m)();function s(e,t){o.adServers=o.adServers||{},o.adServers[e]=o.adServers[e]||{},Object.keys(t).forEach((n=>{o.adServers[e][n]?(0,i.logWarn)(`Attempting to add an already registered function property ${n} for AdServer ${e}.`):o.adServers[e][n]=t[n]}))}},69759:(e,t,n)=>{function r(e){var t=e;return{callBids:function(){},setBidderCode:function(e){t=e},getBidderCode:function(){return t}}}n.d(t,{A:()=>r})},11445:(e,t,n)=>{n.d(t,{S1:()=>R,Ay:()=>H,tS:()=>W,pX:()=>M,Mf:()=>G,K5:()=>x,Gs:()=>P});var r=n(91069),i=n(12449),o=n(57377),s=n(68044),a=n(43272),d=n(16833),c=n(15901);let l={};function u(e,t,n){let r=function(e,t){let n=l[e]=l[e]||{bidders:{}};return t?n.bidders[t]=n.bidders[t]||{}:n}(e,n);return r[t]=(r[t]||0)+1,r[t]}function g(e){return u(e,"auctionsCounter")}function f(e){return l?.[e]?.requestsCounter||0}function p(e,t){return l?.[e]?.bidders?.[t]?.requestsCounter||0}function m(e,t){return l?.[e]?.bidders?.[t]?.winsCounter||0}function h(e){return l?.[e]?.auctionsCounter||0}var b=n(27934),y=n(16916),v=n(75023),E=n(78969),A=n(16894),I=n(67314),T=n(45569),w=n(95139),C=n(76811),S=n(2604),O=n(96953);const B="pbsBidAdapter",R={CLIENT:"client",SERVER:"server"},k={isAllowed:w.io,redact:O.$p};let D={},U=D.bidderRegistry={},j=D.aliasRegistry={},_=[];a.$W.getConfig("s2sConfig",(e=>{e&&e.s2sConfig&&(_=(0,r.isArray)(e.s2sConfig)?e.s2sConfig:[e.s2sConfig])}));var $={};const N=(0,S.ZI)((e=>D.resolveAlias(e)));function x(e){return N(T.tp,B,{[S.XG]:e.configName})}const q=(0,d.A_)("sync",(function(e){let{bidderCode:t,auctionId:n,bidderRequestId:i,adUnits:o,src:s,metrics:a}=e;return o.reduce(((e,o)=>{const d=o.bids.filter((e=>e.bidder===t));return null==t&&0===d.length&&null!=o.s2sBid&&d.push({bidder:null}),e.push(d.reduce(((e,d)=>{const c=null==(d=Object.assign({},d,{ortb2Imp:(0,r.mergeDeep)({},o.ortb2Imp,d.ortb2Imp)},(0,r.getDefinedParams)(o,["nativeParams","nativeOrtbRequest","mediaType","renderer"]))).mediaTypes?o.mediaTypes:d.mediaTypes;return(0,r.isValidMediaTypes)(c)?d=Object.assign({},d,{mediaTypes:c}):(0,r.logError)(`mediaTypes is not correctly configured for adunit ${o.code}`),"client"===s&&function(e,t){u(e,"requestsCounter",t)}(o.code,t),e.push(Object.assign({},d,{adUnitCode:o.code,transactionId:o.transactionId,adUnitId:o.adUnitId,sizes:c?.banner?.sizes||c?.video?.playerSize||[],bidId:d.bid_id||(0,r.getUniqueIdentifierStr)(),bidderRequestId:i,auctionId:n,src:s,metrics:a,auctionsCount:h(o.code),bidRequestsCount:f(o.code),bidderRequestsCount:p(o.code,d.bidder),bidderWinsCount:m(o.code,d.bidder),deferBilling:!!o.deferBilling})),e}),[])),e}),[]).reduce(r.flatten,[]).filter((e=>""!==e))}),"getBids");const W=(0,d.A_)("sync",(function(e,t){let{getS2SBidders:n=M}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null==t)return e;{const r=n(t);return e.filter((e=>r.has(e.bidder)))}}),"filterBidsForAdUnit");const P=(0,d.A_)("sync",((e,t)=>e),"setupAdUnitMediaTypes");function M(e){(0,r.isArray)(e)||(e=[e]);const t=new Set([null]);return e.filter((e=>e&&e.enabled)).flatMap((e=>e.bidders)).forEach((e=>t.add(e))),t}const G=(0,d.A_)("sync",(function(e,t){let{getS2SBidders:n=M}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=n(t);return(0,r.getBidderCodes)(e).reduce(((e,t)=>(e[i.has(t)?R.SERVER:R.CLIENT].push(t),e)),{[R.CLIENT]:[],[R.SERVER]:[]})}),"partitionBidders");function F(e,t){const n=U[e],r=n?.getSpec&&n.getSpec();if(r&&r[t]&&"function"==typeof r[t])return[r,r[t]]}function L(e,t,n,i){try{(0,r.logInfo)(`Invoking ${e}.${t}`);for(var o=arguments.length,s=new Array(o>4?o-4:0),d=4;d<o;d++)s[d-4]=arguments[d];a.$W.runWithBidder(e,i.bind(n,...s))}catch(n){(0,r.logWarn)(`Error calling ${t} of ${e}`)}}function z(e,t,n){if(n?.source!==E.RW.SRC){const r=F(e,t);null!=r&&L(e,t,...r,n)}}D.makeBidRequests=(0,d.A_)("sync",(function(e,t,n,o,s){let d=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},l=arguments.length>6?arguments[6]:void 0;l=(0,A.BO)(l),v.emit(E.qY.BEFORE_REQUEST_BIDS,e),(0,i.nk)(e),e.map((e=>e.code)).filter(r.uniques).forEach(g),e.forEach((e=>{(0,r.isPlainObject)(e.mediaTypes)||(e.mediaTypes={}),e.bids=e.bids.filter((e=>!e.bidder||k.isAllowed(C.uc,N(T.tW,e.bidder)))),u(e.code,"requestsCounter")})),e=P(e,s);let{[R.CLIENT]:f,[R.SERVER]:p}=G(e,_);a.$W.getConfig("bidderSequence")===a.Ov&&(f=(0,r.shuffle)(f));const m=(0,b.EN)();let h=[];const I=d.global||{},w=d.bidder||{};function S(e,t){const i=k.redact(null!=t?t:N(T.tW,e.bidderCode)),o=Object.freeze(i.ortb2((0,r.mergeDeep)({source:{tid:n}},I,w[e.bidderCode])));return e.ortb2=o,e.bids=e.bids.map((e=>(e.ortb2=o,i.bidRequest(e)))),e}_.forEach((i=>{const o=x(i);if(i&&i.enabled&&k.isAllowed(C.uc,o)){let{adUnits:s,hasModuleBids:a}=function(e,t){let n=(0,r.deepClone)(e),i=!1;return n.forEach((e=>{const n=e.bids.filter((e=>e.module===B&&e.params?.configName===t.configName));1===n.length?(e.s2sBid=n[0],i=!0,e.ortb2Imp=(0,r.mergeDeep)({},e.s2sBid.ortb2Imp,e.ortb2Imp)):n.length>1&&(0,r.logWarn)('Multiple "module" bids for the same s2s configuration; all will be ignored',n),e.bids=W(e.bids,t).map((e=>(e.bid_id=(0,r.getUniqueIdentifierStr)(),e)))})),n=n.filter((e=>0!==e.bids.length||null!=e.s2sBid)),{adUnits:n,hasModuleBids:i}}(e,i),d=(0,r.generateUUID)();(0===p.length&&a?[null]:p).forEach((e=>{const a=(0,r.getUniqueIdentifierStr)(),c=l.fork(),u=S({bidderCode:e,auctionId:n,bidderRequestId:a,uniquePbsTid:d,bids:q({bidderCode:e,auctionId:n,bidderRequestId:a,adUnits:(0,r.deepClone)(s),src:E.RW.SRC,metrics:c}),auctionStart:t,timeout:i.timeout,src:E.RW.SRC,refererInfo:m,metrics:c},o);0!==u.bids.length&&h.push(u)})),s.forEach((e=>{let t=e.bids.filter((e=>(0,c.I6)(h,(t=>(0,c.I6)(t.bids,(t=>t.bidId===e.bid_id))))));e.bids=t})),h.forEach((e=>{void 0===e.adUnitsS2SCopy&&(e.adUnitsS2SCopy=s.filter((e=>e.bids.length>0||null!=e.s2sBid)))}))}}));let O=function(e){let t=(0,r.deepClone)(e);return t.forEach((e=>{e.bids=W(e.bids,null)})),t=t.filter((e=>0!==e.bids.length)),t}(e);return f.forEach((e=>{const i=(0,r.getUniqueIdentifierStr)(),a=l.fork(),d=S({bidderCode:e,auctionId:n,bidderRequestId:i,bids:q({bidderCode:e,auctionId:n,bidderRequestId:i,adUnits:(0,r.deepClone)(O),labels:s,src:"client",metrics:a}),auctionStart:t,timeout:o,refererInfo:m,metrics:a}),c=U[e];c||(0,r.logError)(`Trying to make a request for bidder that does not exist: ${e}`),c&&d.bids&&0!==d.bids.length&&h.push(d)})),h.forEach((e=>{y.mW.getConsentData()&&(e.gdprConsent=y.mW.getConsentData()),y.t6.getConsentData()&&(e.uspConsent=y.t6.getConsentData()),y.ad.getConsentData()&&(e.gppConsent=y.ad.getConsentData())})),h}),"makeBidRequests"),D.callBids=function(e,t,n,i,o,d,c){let l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:{};if(!t.length)return void(0,r.logWarn)("callBids executed with no bidRequests.  Were they filtered by labels or sizing?");let[u,g]=t.reduce(((e,t)=>(e[Number(void 0!==t.src&&t.src===E.RW.SRC)].push(t),e)),[[],[]]);var f=[];g.forEach((e=>{for(var t=-1,n=0;n<f.length;++n)if(e.uniquePbsTid===f[n].uniquePbsTid){t=n;break}t<=-1&&f.push(e)}));let p=0;_.forEach((e=>{if(e&&f[p]&&M(e).has(f[p].bidderCode)){const t=(0,s.g4)(d,o?{request:o.request.bind(null,"s2s"),done:o.done}:void 0);let a=e.bidders;const u=U[e.adapter];let m=f[p].uniquePbsTid,h=f[p].adUnitsS2SCopy,b=g.filter((e=>e.uniquePbsTid===m));if(u){let o={ad_units:h,s2sConfig:e,ortb2Fragments:l,requestBidsTimeout:d};if(o.ad_units.length){let e=b.map((e=>(e.start=(0,r.timestamp)(),function(t){t||c(e.bidderRequestId),i.apply(e,arguments)})));const s=(0,r.getBidderCodes)(o.ad_units).filter((e=>a.includes(e)));(0,r.logMessage)(`CALLING S2S HEADER BIDDERS ==== ${s.length>0?s.join(", "):'No bidder specified, using "ortb2Imp" definition(s) only'}`),b.forEach((e=>{v.emit(E.qY.BID_REQUESTED,{...e,tid:e.auctionId})})),u.callBids(o,g,n,(t=>e.forEach((e=>e(t)))),t)}}else(0,r.logError)("missing "+e.adapter);p++}})),u.forEach((e=>{e.start=(0,r.timestamp)();const t=U[e.bidderCode];a.$W.runWithBidder(e.bidderCode,(()=>{(0,r.logMessage)("CALLING BIDDER"),v.emit(E.qY.BID_REQUESTED,e)}));let l=(0,s.g4)(d,o?{request:o.request.bind(null,e.bidderCode),done:o.done}:void 0);const u=i.bind(e);try{a.$W.runWithBidder(e.bidderCode,t.callBids.bind(t,e,n,u,l,(()=>c(e.bidderRequestId)),a.$W.callbackWithBidder(e.bidderCode)))}catch(t){(0,r.logError)(`${e.bidderCode} Bid Adapter emitted an uncaught error when parsing their bidRequest`,{e:t,bidRequest:e}),u()}}))},D.videoAdapters=[],D.registerBidAdapter=function(e,t){let{supportedMediaTypes:n=[]}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};e&&t?"function"==typeof e.callBids?(U[t]=e,y.o2.register(T.tW,t,e.getSpec?.().gvlid),(0,c.mK)(n,"video")&&D.videoAdapters.push(t),(0,c.mK)(n,"native")&&i.mT.push(t)):(0,r.logError)("Bidder adaptor error for bidder code: "+t+"bidder must implement a callBids() function"):(0,r.logError)("bidAdapter or bidderCode not specified")},D.aliasBidAdapter=function(e,t,n){if(void 0===U[t]){let s=U[e];if(void 0===s){const n=[];_.forEach((r=>{if(r.bidders&&r.bidders.length){const i=r&&r.bidders;r&&(0,c.mK)(i,t)?j[t]=e:n.push(e)}})),n.forEach((e=>{(0,r.logError)('bidderCode "'+e+'" is not an existing bidder.',"adapterManager.aliasBidAdapter")}))}else try{let a,d=function(e){let t=[];return(0,c.mK)(D.videoAdapters,e)&&t.push("video"),(0,c.mK)(i.mT,e)&&t.push("native"),t}(e);if(s.constructor.prototype!=Object.prototype)a=new s.constructor,a.setBidderCode(t);else{const{useBaseGvlid:i=!1}=n||{};let d=s.getSpec();const c=i?d.gvlid:n?.gvlid;null==c&&null!=d.gvlid&&(0,r.logWarn)(`Alias '${t}' will NOT re-use the GVL ID of the original adapter ('${d.code}', gvlid: ${d.gvlid}). Functionality that requires TCF consent may not work as expected.`);let l=n&&n.skipPbsAliasing;a=(0,o.xb)(Object.assign({},d,{code:t,gvlid:c,skipPbsAliasing:l})),j[t]=e}D.registerBidAdapter(a,t,{supportedMediaTypes:d})}catch(t){(0,r.logError)(e+" bidder does not currently support aliasing.","adapterManager.aliasBidAdapter")}}else(0,r.logMessage)('alias name "'+t+'" has been already specified.')},D.resolveAlias=function(e){let t,n=e;for(;j[n]&&(!t||!t.has(n));)n=j[n],(t=t||new Set).add(n);return n},D.registerAnalyticsAdapter=function(e){let{adapter:t,code:n,gvlid:i}=e;t&&n?"function"==typeof t.enableAnalytics?(t.code=n,$[n]={adapter:t,gvlid:i},y.o2.register(T.Tn,n,i)):(0,r.logError)(`Prebid Error: Analytics adaptor error for analytics "${n}"\n        analytics adapter must implement an enableAnalytics() function`):(0,r.logError)("Prebid Error: analyticsAdapter or analyticsCode not specified")},D.enableAnalytics=function(e){(0,r.isArray)(e)||(e=[e]),e.forEach((e=>{const t=$[e.provider];t&&t.adapter?k.isAllowed(C.mo,N(T.Tn,e.provider,{[S.TQ]:e}))&&t.adapter.enableAnalytics(e):(0,r.logError)(`Prebid Error: no analytics adapter found in registry for '${e.provider}'.`)}))},D.getBidAdapter=function(e){return U[e]},D.getAnalyticsAdapter=function(e){return $[e]},D.callTimedOutBidders=function(e,t,n){t=t.map((t=>(t.params=(0,r.getUserConfiguredParams)(e,t.adUnitCode,t.bidder),t.timeout=n,t))),t=(0,r.groupBy)(t,"bidder"),Object.keys(t).forEach((e=>{z(e,"onTimeout",t[e])}))},D.callBidWonBidder=function(e,t,n){var i,o;t.params=(0,r.getUserConfiguredParams)(n,t.adUnitCode,t.bidder),i=t.adUnitCode,o=t.bidder,u(i,"winsCounter",o),z(e,"onBidWon",t)},D.triggerBilling=(()=>{const e=new WeakSet;return t=>{e.has(t)||(e.add(t),t.source===E.RW.SRC&&t.burl&&r.internal.triggerPixel(t.burl),z(t.bidder,"onBidBillable",t))}})(),D.callSetTargetingBidder=function(e,t){z(e,"onSetTargeting",t)},D.callBidViewableBidder=function(e,t){z(e,"onBidViewable",t)},D.callBidderError=function(e,t,n){z(e,"onBidderError",{error:t,bidderRequest:n})},D.callAdRenderSucceededBidder=function(e,t){z(e,"onAdRenderSucceeded",t)},D.callDataDeletionRequest=(0,d.A_)("sync",(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const i="onDataDeletionRequest";Object.keys(U).filter((e=>!j.hasOwnProperty(e))).forEach((e=>{const n=F(e,i);if(null!=n){const r=I.n.getBidsRequested().filter((t=>function(e){const t=new Set;for(;j.hasOwnProperty(e)&&!t.has(e);)t.add(e),e=j[e];return e}(t.bidderCode)===e));L(e,i,...n,r,...t)}})),Object.entries($).forEach((e=>{let[n,o]=e;const s=o?.adapter?.[i];if("function"==typeof s)try{s.apply(o.adapter,t)}catch(e){(0,r.logError)(`error calling ${i} of ${n}`,e)}}))}));const H=D},57377:(e,t,n)=>{n.d(t,{JN:()=>k,JS:()=>U,a$:()=>w,eI:()=>C,fn:()=>D,xb:()=>S});var r=n(69759),i=n(11445),o=n(43272),s=n(93597),a=n(38230),d=n(12449),c=n(63895),l=n(78969),u=n(75023),g=n(15901),f=n(91069),p=n(16833),m=n(67314),h=n(12693),b=n(16894),y=n(95139),v=n(83441),E=n(45569),A=n(76811);const I=["cpm","ttl","creativeId","netRevenue","currency"],T=["auctionId","transactionId"];function w(e){const t=Array.isArray(e.supportedMediaTypes)?{supportedMediaTypes:e.supportedMediaTypes}:void 0;function n(e){const n=S(e);i.Ay.registerBidAdapter(n,e.code,t)}n(e),Array.isArray(e.aliases)&&e.aliases.forEach((t=>{let r,o,s=t;(0,f.isPlainObject)(t)&&(s=t.code,r=t.gvlid,o=t.skipPbsAliasing),i.Ay.aliasRegistry[s]=e.code,n(Object.assign({},e,{code:s,gvlid:r,skipPbsAliasing:o}))}))}const C=(0,f.memoize)((e=>{let{bidderCode:t}=e;if((0,y.io)(A.VJ,(0,v.s)(E.tW,t)))return{bidRequest:e=>e,bidderRequest:e=>e};function n(e,t,n){return T.includes(t)?null:Reflect.get(e,t,n)}function r(e,t){const n=new Proxy(e,t);return Object.entries(e).filter((e=>{let[t,n]=e;return"function"==typeof n})).forEach((t=>{let[r,i]=t;return n[r]=i.bind(e)})),n}const i=(0,f.memoize)((e=>r(e,{get:n})),(e=>e.bidId));return{bidRequest:i,bidderRequest:e=>r(e,{get:(t,r,o)=>"bids"===r?e.bids.map(i):n(t,r,o)})}}));function S(e){return Object.assign(new r.A(e.code),{getSpec:function(){return Object.freeze(Object.assign({},e))},registerSyncs:t,callBids:function(n,r,a,d,c,g){if(!Array.isArray(n.bids))return;const p=C(n),m={};const y=[];function v(){a(),o.$W.runWithBidder(e.code,(()=>{u.emit(l.qY.BIDDER_DONE,n),t(y,n.gdprConsent,n.uspConsent,n.gppConsent)}))}const E=U(n).measureTime("validate",(()=>n.bids.filter((t=>function(t){if(!e.isBidRequestValid(t))return(0,f.logWarn)(`Invalid bid sent to bidder ${e.code}: ${JSON.stringify(t)}`),!1;return!0}(p.bidRequest(t))))));if(0===E.length)return void v();const A={};E.forEach((e=>{A[e.bidId]=e,e.adUnitCode||(e.adUnitCode=e.placementCode)})),B(e,E,n,d,g,{onRequest:e=>u.emit(l.qY.BEFORE_BIDDER_HTTP,n,e),onResponse:t=>{c(e.code),y.push(t)},onPaapi:e=>{const t=A[e.bidId];t?k(t,e):(0,f.logWarn)("Received fledge auction configuration for an unknown bidId",e)},onError:(t,r)=>{r.timedOut||c(e.code),i.Ay.callBidderError(e.code,r,n),u.emit(l.qY.BIDDER_ERROR,{error:r,bidderRequest:n}),(0,f.logError)(`Server call for ${e.code} failed: ${t} ${r.status}. Continuing without bids.`)},onBid:t=>{const n=A[t.requestId];if(n){if(t.adapterCode=n.bidder,function(e,t){let n=h.u.get(t,"allowAlternateBidderCodes")||!1,r=h.u.get(t,"allowedAlternateBidderCodes");if(e&&t&&t!==e&&(r=(0,f.isArray)(r)?r.map((e=>e.trim().toLowerCase())).filter((e=>!!e)).filter(f.uniques):r,!n||(0,f.isArray)(r)&&"*"!==r[0]&&!r.includes(e)))return!0;return!1}(t.bidderCode,n.bidder))return(0,f.logWarn)(`${t.bidderCode} is not a registered partner or known bidder of ${n.bidder}, hence continuing without bid. If you wish to support this bidder, please mark allowAlternateBidderCodes as true in bidderSettings.`),void r.reject(n.adUnitCode,t,l.Tf.BIDDER_DISALLOWED);t.originalCpm=t.cpm,t.originalCurrency=t.currency,t.meta=t.meta||Object.assign({},t[n.bidder]),t.deferBilling=n.deferBilling,t.deferRendering=t.deferBilling&&(t.deferRendering??"function"!=typeof e.onBidBillable);const i=Object.assign((0,s.O)(l.XQ.GOOD,n),t,(0,f.pick)(n,T));!function(e,t){const n=(0,b.BO)(t.metrics);n.checkpoint("addBidResponse"),m[e]=!0,n.measureTime("addBidResponse.validate",(()=>D(e,t)))?r(e,t):r.reject(e,t,l.Tf.INVALID)}(n.adUnitCode,i)}else(0,f.logWarn)(`Bidder ${e.code} made bid for unknown request ID: ${t.requestId}. Ignoring.`),r.reject(null,t,l.Tf.INVALID_REQUEST_ID)},onCompletion:v})}});function t(t,n,r,i){R(e,t,n,r,i)}}const O=["bids","paapi"],B=(0,p.A_)("sync",(function(e,t,n,r,i,o){let{onRequest:s,onResponse:a,onPaapi:d,onError:c,onBid:l,onCompletion:u}=o;const g=U(n);u=g.startTiming("total").stopBefore(u);const p=C(n);let m=g.measureTime("buildRequests",(()=>e.buildRequests(t.map(p.bidRequest),p.bidderRequest(n))));if(!m||0===m.length)return void u();Array.isArray(m)||(m=[m]);const b=(0,f.delayExecution)(u,m.length);m.forEach((t=>{const n=g.fork();function o(e){null!=e&&(e.metrics=n.fork().renameWith()),l(e)}const u=i((function(r,i){m();try{r=JSON.parse(r)}catch(e){}r={body:r,headers:{get:i.getResponseHeader.bind(i)}},a(r);try{r=n.measureTime("interpretResponse",(()=>e.interpretResponse(r,t)))}catch(t){return(0,f.logError)(`Bidder ${e.code} failed to interpret the server's response. Continuing without bids`,null,t),void b()}let s,c;r&&!Object.keys(r).some((e=>!O.includes(e)))?(s=r.bids,c=r.paapi):s=r,(0,f.isArray)(c)&&c.forEach(d),s&&((0,f.isArray)(s)?s.forEach(o):o(s)),b()})),p=i((function(e,t){m(),c(e,t),b()}));s(t);const m=n.startTiming("net");function I(n){const r=t.options;return Object.assign(n,r,{browsingTopics:!(r?.hasOwnProperty("browsingTopics")&&!r.browsingTopics)&&((h.u.get(e.code,"topicsHeader")??!0)&&(0,y.io)(A.DL,(0,v.s)(E.tW,e.code)))})}switch(t.method){case"GET":r(`${t.url}${function(e){if(e)return`?${"object"==typeof e?(0,f.parseQueryStringParameters)(e):e}`;return""}(t.data)}`,{success:u,error:p},void 0,I({method:"GET",withCredentials:!0}));break;case"POST":r(t.url,{success:u,error:p},"string"==typeof t.data?t.data:JSON.stringify(t.data),I({method:"POST",contentType:"text/plain",withCredentials:!0}));break;default:(0,f.logWarn)(`Skipping invalid request from ${e.code}. Request type ${t.type} must be GET or POST`),b()}}))}),"processBidderRequests"),R=(0,p.A_)("async",(function(e,t,n,r,s){const d=o.$W.getConfig("userSync.aliasSyncEnabled");if(e.getUserSyncs&&(d||!i.Ay.aliasRegistry[e.code])){let i=e.getUserSyncs({iframeEnabled:a.zt.canBidderRegisterSync("iframe",e.code),pixelEnabled:a.zt.canBidderRegisterSync("image",e.code)},t,n,r,s);i&&(Array.isArray(i)||(i=[i]),i.forEach((t=>{a.zt.registerSync(t.type,e.code,t.url)})),a.zt.bidderDone(e.code))}}),"registerSyncs"),k=(0,p.A_)("sync",((e,t)=>{}),"addPaapiConfig");function D(e,t){let{index:n=m.n.index}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};function r(e){return`Invalid bid from ${t.bidderCode}. Ignoring bid: ${e}`}return e?t?function(){let e=Object.keys(t);return I.every((n=>(0,g.mK)(e,n)&&!(0,g.mK)([void 0,null],t[n])))}()?"native"!==t.mediaType||(0,d.Bm)(t,{index:n})?"video"!==t.mediaType||(0,c.vk)(t,{index:n})?!("banner"===t.mediaType&&!function(e,t){let{index:n=m.n.index}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if((t.width||0===parseInt(t.width,10))&&(t.height||0===parseInt(t.height,10)))return t.width=parseInt(t.width,10),t.height=parseInt(t.height,10),!0;const r=n.getBidRequest(t),i=n.getMediaTypes(t),o=r&&r.sizes||i&&i.banner&&i.banner.sizes,s=(0,f.parseSizesInput)(o);if(1===s.length){const[e,n]=s[0].split("x");return t.width=parseInt(e,10),t.height=parseInt(n,10),!0}return!1}(e,t,{index:n}))||((0,f.logError)(r("Banner bids require a width and height")),!1):((0,f.logError)(r("Video bid does not have required vastUrl or renderer property")),!1):((0,f.logError)(r("Native bid missing some required properties.")),!1):((0,f.logError)(r(`Bidder ${t.bidderCode} is missing required params. Check http://prebid.org/dev-docs/bidder-adapter-1.html for list of params.`)),!1):((0,f.logWarn)(`Some adapter tried to add an undefined bid for ${e}.`),!1):((0,f.logWarn)("No adUnitCode was supplied to addBidResponse."),!1)}function U(e){return(0,b.BO)(e.metrics).renameWith((t=>[`adapter.client.${t}`,`adapters.client.${e.bidderCode}.${t}`]))}},41580:(e,t,n)=>{n.d(t,{R:()=>l});var r=n(76811),i=n(83441),o=n(95139),s=n(15901),a=n(91069);const d=new WeakMap,c=["debugging","outstream","improvedigital","showheroes-bs","aaxBlockmeter","adagio","adloox","akamaidap","arcspan","airgrid","browsi","brandmetrics","clean.io","humansecurity","confiant","contxtful","hadron","mediafilter","medianet","azerionedge","a1Media","geoedge","qortex","dynamicAdBoost","51Degrees","symitridap","wurfl","justtag","tncId","ftrackId","id5"];function l(e,t,n,l,u,g){if(!(0,o.io)(r.pY,(0,i.s)(t,n)))return;if(!n||!e)return void(0,a.logError)("cannot load external script without url and moduleCode");if(!(0,s.mK)(c,n))return void(0,a.logError)(`${n} not whitelisted for loading external JavaScript`);u||(u=document);const f=h(u,e);if(f)return l&&"function"==typeof l&&(f.loaded?l():f.callbacks.push(l)),f.tag;const p=d.get(u)||{},m={loaded:!1,tag:null,callbacks:[]};return p[e]=m,d.set(u,p),l&&"function"==typeof l&&m.callbacks.push(l),(0,a.logWarn)(`module ${n} is loading external JavaScript`),function(t,n,r,i){r||(r=document);var o=r.createElement("script");o.type="text/javascript",o.async=!0;const s=h(r,e);s&&(s.tag=o);o.readyState?o.onreadystatechange=function(){"loaded"!==o.readyState&&"complete"!==o.readyState||(o.onreadystatechange=null,n())}:o.onload=function(){n()};o.src=t,i&&(0,a.setScriptAttributes)(o,i);return(0,a.insertElement)(o,r),o}(e,(function(){m.loaded=!0;try{for(let e=0;e<m.callbacks.length;e++)m.callbacks[e]()}catch(e){(0,a.logError)("Error executing callback","adloader.js:loadExternalScript",e)}}),u,g);function h(e,t){const n=d.get(e);return n&&n[t]?n[t]:null}}},51692:(e,t,n)=>{n.d(t,{Q:()=>r});const r=(0,n(16833).A_)("sync",(()=>{}))},68044:(e,t,n)=>{n.d(t,{RD:()=>f,Rz:()=>g,g4:()=>u,hd:()=>p});var r=n(43272),i=n(91069);const o={fetch:window.fetch.bind(window),makeRequest:(e,t)=>new Request(e,t),timeout(e,t){const n=new AbortController;let r=setTimeout((()=>{n.abort(),(0,i.logError)(`Request timeout after ${e}ms`,t),r=null}),e);return{signal:n.signal,done(){r&&clearTimeout(r)}}}},s="GET",a="POST",d="Content-Type";function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3e3,{request:t,done:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=(t,n)=>{let i;null==e||null!=n?.signal||r.$W.getConfig("disableAjaxTimeout")||(i=o.timeout(e,t),n=Object.assign({signal:i.signal},n));let s=o.fetch(t,n);return null!=i?.done&&(s=s.finally(i.done)),s};return null==t&&null==n||(i=(e=>function(r,i){const o=new URL(null==r?.url?r:r.url,document.location).origin;let s=e(r,i);return t&&t(o),n&&(s=s.finally((()=>n(o)))),s})(i)),i}function l(e,t){let{status:n,statusText:r="",headers:o,url:s}=e,a=0;function c(e){if(0===a)try{a=(new DOMParser).parseFromString(t,o?.get(d)?.split(";")?.[0])}catch(t){a=null,e&&e(t)}return a}return{readyState:XMLHttpRequest.DONE,status:n,statusText:r,responseText:t,response:t,responseType:"",responseURL:s,get responseXML(){return c(i.logError)},getResponseHeader:e=>o?.has(e)?o.get(e):null,toJSON(){return Object.assign({responseXML:c()},this)},timedOut:!1}}function u(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3e3,{request:t,done:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=c(e,{request:t,done:n});return function(e,t,n){!function(e,t){const{success:n,error:r}="object"==typeof t&&null!=t?t:{success:"function"==typeof t?t:()=>null,error:(e,t)=>(0,i.logError)("Network error",e,t)};e.then((e=>e.text().then((t=>[e,t])))).then((e=>{let[t,i]=e;const o=l(t,i);t.ok||304===t.status?n(i,o):r(t.statusText,o)}),(e=>r("",Object.assign(l({status:0},""),{reason:e,timedOut:"AbortError"===e?.name}))))}(r(function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=n.method||(t?a:s);if(r===s&&t){const r=(0,i.parseUrl)(e,n);Object.assign(r.search,t),e=(0,i.buildUrl)(r)}const c=new Headers(n.customHeaders);c.set(d,n.contentType||"text/plain");const l={method:r,headers:c};return r!==s&&t&&(l.body=t),n.withCredentials&&(l.credentials="include"),isSecureContext&&["browsingTopics","adAuctionHeaders"].forEach((e=>{n[e]&&(l[e]=!0)})),n.keepalive&&(l.keepalive=!0),o.makeRequest(e,l)}(e,n,arguments.length>3&&void 0!==arguments[3]?arguments[3]:{})),t)}}function g(e,t){return!(!window.navigator||!window.navigator.sendBeacon)&&window.navigator.sendBeacon(e,t)}const f=u(),p=c()},81657:(e,t,n)=>{n.d(t,{AA:()=>O,HN:()=>J,UZ:()=>B,ZV:()=>F,mO:()=>G,mX:()=>_,sR:()=>$,v8:()=>W,w1:()=>x});var r=n(91069),i=n(86833),o=n(12449),s=n(68693),a=n(95789),d=n(43272),c=n(38230),l=n(16833),u=n(15901),g=n(63895),f=n(71371),p=n(67314),m=n(12693),h=n(75023),b=n(11445),y=n(78969),v=n(25555),E=n(16894),A=n(57176),I=n(7873),T=n(76853),w=n(27863);const{syncUsers:C}=c.zt,S="started",O="inProgress",B="completed";h.on(y.qY.BID_ADJUSTMENT,(function(e){!function(e){let t=(0,A.y)(e.cpm,e);t>=0&&(e.cpm=t)}(e)}));const R=4,k={},D={},U=[],j=(0,I.m)();function _(e){let{adUnits:t,adUnitCodes:n,callback:s,cbTimeout:c,labels:l,auctionId:m,ortb2Fragments:A,metrics:I}=e;I=(0,E.BO)(I);const _=t,G=l,F=n,L=m||(0,r.generateUUID)(),z=c,H=new Set,V=(0,v.v)(),K=(0,v.v)();let J,Y,Q,Z,ee=[],te=s,ne=[],re=(0,T.H)({startTime:e=>e.responseTimestamp,ttl:e=>null==(0,w.S9)()?null:1e3*Math.max((0,w.S9)(),e.ttl)}),ie=[],oe=[],se=[];function ae(){return{auctionId:L,timestamp:J,auctionEnd:Y,auctionStatus:Z,adUnits:_,adUnitCodes:F,labels:G,bidderRequests:ne,noBids:ie,bidsReceived:re.toArray(),bidsRejected:ee,winningBids:oe,timeout:z,metrics:I,seatNonBids:se}}function de(e){if(e?h.emit(y.qY.AUCTION_TIMEOUT,ae()):clearTimeout(Q),void 0===Y){let n=[];e&&((0,r.logMessage)(`Auction ${L} timedOut`),n=ne.filter((e=>!H.has(e.bidderRequestId))).flatMap((e=>e.bids)),n.length&&h.emit(y.qY.BID_TIMEOUT,n)),Z=B,Y=Date.now(),I.checkpoint("auctionEnd"),I.timeBetween("requestBids","auctionEnd","requestBids.total"),I.timeBetween("callBids","auctionEnd","requestBids.callBids"),V.resolve(),h.emit(y.qY.AUCTION_END,ae()),q(_,(function(){try{if(null!=te){const t=re.toArray().filter((e=>F.includes(e.adUnitCode))).reduce(X,{});te.apply(j,[t,e,L]),te=null}}catch(e){(0,r.logError)("Error executing bidsBackHandler",null,e)}finally{n.length&&b.Ay.callTimedOutBidders(t,n,z);let e=d.$W.getConfig("userSync")||{};e.enableOverride||C(e.syncDelay)}}))}}function ce(){d.$W.resetBidder(),(0,r.logInfo)(`Bids Received for Auction with id: ${L}`,re.toArray()),Z=B,de(!1)}function le(e){H.add(e)}function ue(e){e.forEach((e=>{var t;t=e,ne=ne.concat(t)}));let t={},n={bidRequests:e,run:()=>{Q=setTimeout((()=>de(!0)),z),Z=O,h.emit(y.qY.AUCTION_INIT,ae());let n=function(e,t){let{index:n=p.n.index}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=0,c=!1,l=new Set,m={};function b(){s--,c&&0===s&&e()}function E(e,t,n){return m[t.requestId]=!0,function(e,t){let{index:n=p.n.index}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=n.getBidderRequest(e),o=n.getAdUnit(e),s=i&&i.start||e.requestTimestamp;Object.assign(e,{responseTimestamp:e.responseTimestamp||(0,r.timestamp)(),requestTimestamp:e.requestTimestamp||s,cpm:parseFloat(e.cpm)||0,bidder:e.bidder||e.bidderCode,adUnitCode:t}),null!=o?.ttlBuffer&&(e.ttlBuffer=o.ttlBuffer);e.timeToRespond=e.responseTimestamp-e.requestTimestamp}(t,e),s++,n(b)}function A(e,s){E(e,s,(e=>{let c=function(e){let{index:t=p.n.index}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};h.emit(y.qY.BID_ADJUSTMENT,e);const n=t.getBidRequest(e)?.renderer||t.getAdUnit(e).renderer,r=e.mediaType,o=t.getMediaTypes(e),s=o&&o[r];var c=s&&s.renderer,l=null;!c||!c.render||!0===c.backupOnly&&e.renderer?!n||!n.render||!0===n.backupOnly&&e.renderer||(l=n):l=c;l&&(e.renderer=a.A4.install({url:l.url,config:l.options,renderNow:null==l.url}),e.renderer.setRender(l.render));const u=M(e.mediaType,o,d.$W.getConfig("mediaTypePriceGranularity")),g=(0,i.j)(e.cpm,"object"==typeof u?u:d.$W.getConfig("customPriceBucket"),d.$W.getConfig("currency.granularityMultiplier"));return e.pbLg=g.low,e.pbMg=g.med,e.pbHg=g.high,e.pbAg=g.auto,e.pbDg=g.dense,e.pbCg=g.custom,e}(s);h.emit(y.qY.BID_ACCEPTED,c),c.mediaType===f.G_?function(e,t,n){let{index:i=p.n.index}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=!0;const s=i.getMediaTypes({requestId:t.originalRequestId||t.requestId,adUnitId:t.adUnitId})?.video,a=s&&s?.context,c=s&&s?.useCacheKey;d.$W.getConfig("cache.url")&&(c||a!==g.H6)&&(!t.videoCacheKey||d.$W.getConfig("cache.ignoreBidderCacheKey")?(o=!1,P(e,t,n,s)):t.vastUrl||((0,r.logError)("videoCacheKey specified but not required vastUrl for video bid"),o=!1));o&&(W(e,t),n())}(t,c,e):((0,o.l6)(c)&&(0,o.gs)(c,n.getAdUnit(c)),W(t,c),e())}))}function I(e,n,i){return E(e,n,(e=>{n.rejectionReason=i,(0,r.logWarn)(`Bid from ${n.bidder||"unknown bidder"} was rejected: ${i}`,n),h.emit(y.qY.BID_REJECTED,n),t.addBidRejected(n),e()}))}function T(){let n=this,i=t.getBidRequests();const o=d.$W.getConfig("auctionOptions");if(l.add(n),o&&!(0,r.isEmpty)(o)){const e=o.secondaryBidders;e&&!i.every((t=>(0,u.mK)(e,t.bidderCode)))&&(i=i.filter((t=>!(0,u.mK)(e,t.bidderCode))))}c=i.every((e=>l.has(e))),n.bids.forEach((e=>{m[e.bidId]||(t.addNoBid(e),h.emit(y.qY.NO_BID,e))})),c&&0===s&&e()}return{addBidResponse:function(){function e(e,t){$.call({dispatch:A},e,t,(()=>{let n=!1;return r=>{n||(I(e,t,r),n=!0)}})())}return e.reject=I,e}(),adapterDone:function(){N(v.k.resolve()).finally((()=>T.call(this)))}}}(ce,this);b.Ay.callBids(_,e,n.addBidResponse,n.adapterDone,{request(e,n){c(k,n),c(t,e),D[e]||(D[e]={SRA:!0,origin:n}),t[e]>1&&(D[e].SRA=!1)},done(e){k[e]--,U[0]&&s(U[0])&&U.shift()}},z,le,A),K.resolve()}};function s(e){let t=!0,n=d.$W.getConfig("maxRequestsPerOrigin")||R;return e.bidRequests.some((e=>{let r=1,i=void 0!==e.src&&e.src===y.RW.SRC?"s2s":e.bidderCode;return D[i]&&(!1===D[i].SRA&&(r=Math.min(e.bids.length,n)),k[D[i].origin]+r>n&&(t=!1)),!t})),t&&e.run(),t}function c(e,t){void 0===e[t]?e[t]=1:e[t]++}s(n)||((0,r.logWarn)("queueing auction due to limited endpoint capacity"),U.push(n))}return(0,w.lc)((()=>re.refresh())),h.on(y.qY.SEAT_NON_BID,(e=>{var t;e.auctionId===L&&(t=e.seatnonbid,se=se.concat(t))})),{addBidReceived:function(e){re.add(e)},addBidRejected:function(e){ee=ee.concat(e)},addNoBid:function(e){ie=ie.concat(e)},callBids:function(){Z=S,J=Date.now();let e=I.measureTime("requestBids.makeRequests",(()=>b.Ay.makeBidRequests(_,J,L,z,G,A,I)));(0,r.logInfo)(`Bids Requested for Auction with id: ${L}`,e),I.checkpoint("callBids"),e.length<1?((0,r.logWarn)("No valid bid requests returned for auction"),ce()):x.call({dispatch:ue,context:this},e)},addWinningBid:function(e){oe=oe.concat(e),b.Ay.callBidWonBidder(e.adapterCode||e.bidder,e,t),e.deferBilling||b.Ay.triggerBilling(e)},setBidTargeting:function(e){b.Ay.callSetTargetingBidder(e.adapterCode||e.bidder,e)},getWinningBids:()=>oe,getAuctionStart:()=>J,getAuctionEnd:()=>Y,getTimeout:()=>z,getAuctionId:()=>L,getAuctionStatus:()=>Z,getAdUnits:()=>_,getAdUnitCodes:()=>F,getBidRequests:()=>ne,getBidsReceived:()=>re.toArray(),getNoBids:()=>ie,getNonBids:()=>se,getFPD:()=>A,getMetrics:()=>I,end:V.promise,requestsDone:K.promise,getProperties:ae}}const $=(0,l.A_)("sync",(function(e,t,n){!function(e){const t=d.$W.getConfig("maxBid");return!t||!e.cpm||t>=Number(e.cpm)}(t)?n(y.Tf.PRICE_TOO_HIGH):this.dispatch.call(null,e,t)}),"addBidResponse"),N=(0,l.A_)("sync",(e=>e),"responsesReady"),x=(0,l.A_)("sync",(function(e){this.dispatch.call(this.context,e)}),"addBidderRequests"),q=(0,l.A_)("async",(function(e,t){t&&t()}),"bidsBackCallback");function W(e,t){!function(e){let t;const n=!0===m.u.get(e.bidderCode,"allowZeroCpmBids")?e.cpm>=0:e.cpm>0;e.bidderCode&&(n||e.dealId)&&(t=function(e,t){let{index:n=p.n.index}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!t)return{};const r=n.getBidRequest(t);var i={};const s=J(t.mediaType,e);Y(i,s,t,r),e&&m.u.getOwn(e,y.iD.ADSERVER_TARGETING)&&(Y(i,m.u.ownSettingsFor(e),t,r),t.sendStandardTargeting=m.u.get(e,"sendStandardTargeting"));t.native&&(i=Object.assign({},i,(0,o.Zj)(t)));return i}(e.bidderCode,e));e.adserverTargeting=Object.assign(e.adserverTargeting||{},t)}(t),(0,E.BO)(t.metrics).timeSince("addBidResponse","addBidResponse.total"),e.addBidReceived(t),h.emit(y.qY.BID_RESPONSE,t)}const P=(0,l.A_)("async",(function(e,t,n,r){(0,s.X5)(e,t,n)}),"callPrebidCache");function M(e,t,n){if(e&&n){if(e===f.G_){const e=t?.[f.G_]?.context??"instream";if(n[`${f.G_}-${e}`])return n[`${f.G_}-${e}`]}return n[e]}}const G=function(e){let{index:t=p.n.index}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=M(e.mediaType,t.getMediaTypes(e),d.$W.getConfig("mediaTypePriceGranularity"));return"string"==typeof e.mediaType&&n?"string"==typeof n?n:"custom":d.$W.getConfig("priceGranularity")},F=e=>t=>{const n=e||G(t);return n===y.UE.AUTO?t.pbAg:n===y.UE.DENSE?t.pbDg:n===y.UE.LOW?t.pbLg:n===y.UE.MEDIUM?t.pbMg:n===y.UE.HIGH?t.pbHg:n===y.UE.CUSTOM?t.pbCg:void 0},L=()=>e=>e.creativeId?e.creativeId:"",z=()=>e=>e.meta&&e.meta.advertiserDomains&&e.meta.advertiserDomains.length>0?[e.meta.advertiserDomains].flat()[0]:"",H=()=>e=>e.meta&&(e.meta.networkId||e.meta.networkName)?e?.meta?.networkName||e?.meta?.networkId:"",V=()=>e=>e.meta&&e.meta.primaryCatId?e.meta.primaryCatId:"";function K(e,t){return{key:e,val:"function"==typeof t?function(e,n){return t(e,n)}:function(e){return e[t]}}}function J(e,t){const n=Object.assign({},m.u.settingsFor(null));if(n[y.iD.ADSERVER_TARGETING]||(n[y.iD.ADSERVER_TARGETING]=[K(y.xS.BIDDER,"bidderCode"),K(y.xS.AD_ID,"adId"),K(y.xS.PRICE_BUCKET,F()),K(y.xS.SIZE,"size"),K(y.xS.DEAL,"dealId"),K(y.xS.SOURCE,"source"),K(y.xS.FORMAT,"mediaType"),K(y.xS.ADOMAIN,z()),K(y.xS.ACAT,V()),K(y.xS.DSP,H()),K(y.xS.CRID,L())]),"video"===e){const e=n[y.iD.ADSERVER_TARGETING].slice();if(n[y.iD.ADSERVER_TARGETING]=e,[y.xS.UUID,y.xS.CACHE_ID].forEach((t=>{void 0===(0,u.I6)(e,(e=>e.key===t))&&e.push(K(t,"videoCacheKey"))})),d.$W.getConfig("cache.url")&&(!t||!1!==m.u.get(t,"sendStandardTargeting"))){const t=(0,r.parseUrl)(d.$W.getConfig("cache.url"));void 0===(0,u.I6)(e,(e=>e.key===y.xS.CACHE_HOST))&&e.push(K(y.xS.CACHE_HOST,(function(e){return e?.adserverTargeting?.[y.xS.CACHE_HOST]||t.hostname})))}}return n}function Y(e,t,n,i){var o=t[y.iD.ADSERVER_TARGETING];return n.size=n.getSize(),(o||[]).forEach((function(o){var s=o.key,a=o.val;if(e[s]&&(0,r.logWarn)("The key: "+s+" is being overwritten"),(0,r.isFn)(a))try{a=a(n,i)}catch(e){(0,r.logError)("bidmanager","ERROR",e)}(void 0===t.suppressEmptyKeys||!0!==t.suppressEmptyKeys)&&s!==y.xS.DEAL&&s!==y.xS.ACAT&&s!==y.xS.DSP&&s!==y.xS.CRID||!(0,r.isEmptyStr)(a)&&null!=a?e[s]=a:(0,r.logInfo)("suppressing empty key '"+s+"' from adserver targeting")})),e}function X(e,t){return e[t.adUnitCode]||(e[t.adUnitCode]={bids:[]}),e[t.adUnitCode].bids.push(t),e}},67314:(e,t,n)=>{n.d(t,{n:()=>l});var r=n(91069),i=n(81657);function o(e){Object.assign(this,{getAuction(t){let{auctionId:n}=t;if(null!=n)return e().find((e=>e.getAuctionId()===n))},getAdUnit(t){let{adUnitId:n}=t;if(null!=n)return e().flatMap((e=>e.getAdUnits())).find((e=>e.adUnitId===n))},getMediaTypes(e){let{adUnitId:t,requestId:n}=e;if(null!=n){const e=this.getBidRequest({requestId:n});if(null!=e&&(null==t||e.adUnitId===t))return e.mediaTypes}else if(null!=t){const e=this.getAdUnit({adUnitId:t});if(null!=e)return e.mediaTypes}},getBidderRequest(t){let{requestId:n,bidderRequestId:r}=t;if(null!=n||null!=r){let t=e().flatMap((e=>e.getBidRequests()));return null!=r&&(t=t.filter((e=>e.bidderRequestId===r))),null==n?t[0]:t.find((e=>e.bids&&null!=e.bids.find((e=>e.bidId===n))))}},getBidRequest(t){let{requestId:n}=t;if(null!=n)return e().flatMap((e=>e.getBidRequests())).flatMap((e=>e.bids)).find((e=>e&&e.bidId===n))},getOrtb2(e){return this.getBidderRequest(e)?.ortb2||this.getAuction(e)?.getFPD()?.global?.ortb2}})}var s=n(78969),a=n(16894),d=n(76853),c=n(27863);const l=function(){const e=(0,d.H)({startTime:e=>e.end.then((()=>e.getAuctionEnd())),ttl:e=>null==(0,c.S9)()?null:e.end.then((()=>1e3*Math.max((0,c.S9)(),...e.getBidsReceived().map((e=>e.ttl)))))});(0,c.lc)((()=>e.refresh()));const t={onExpiry:e.onExpiry};function n(t){for(const n of e)if(n.getAuctionId()===t)return n}function l(){return e.toArray().flatMap((e=>e.getBidsReceived()))}return t.addWinningBid=function(e){const t=(0,a.BO)(e.metrics);t.checkpoint("bidWon"),t.timeBetween("auctionEnd","bidWon","adserver.pending"),t.timeBetween("requestBids","bidWon","adserver.e2e");const i=n(e.auctionId);i?i.addWinningBid(e):(0,r.logWarn)("Auction not found when adding winning bid")},Object.entries({getAllWinningBids:{name:"getWinningBids"},getBidsRequested:{name:"getBidRequests"},getNoBids:{},getAdUnits:{},getBidsReceived:{pre:e=>e.getAuctionStatus()===i.UZ},getAdUnitCodes:{post:r.uniques}}).forEach((n=>{let[r,{name:i=r,pre:o,post:s}]=n;const a=null==o?e=>e[i]():e=>o(e)?e[i]():[],d=null==s?e=>e:e=>e.filter(s);t[r]=()=>d(e.toArray().flatMap(a))})),t.getAllBidsForAdUnitCode=function(e){return l().filter((t=>t&&t.adUnitCode===e))},t.createAuction=function(t){const n=(0,i.mX)(t);return function(t){e.add(t)}(n),n},t.findBidByAdId=function(e){return l().find((t=>t.adId===e))},t.getStandardBidderAdServerTargeting=function(){return(0,i.HN)()[s.iD.ADSERVER_TARGETING]},t.setStatusForBids=function(e,r){let i=t.findBidByAdId(e);if(i&&(i.status=r),i&&r===s.tl.BID_TARGETING_SET){const e=n(i.auctionId);e&&e.setBidTargeting(i)}},t.getLastAuctionId=function(){const t=e.toArray();return t.length&&t[t.length-1].getAuctionId()},t.clearAllAuctions=function(){e.clear()},t.index=new o((()=>e.toArray())),t}()},27863:(e,t,n)=>{n.d(t,{S9:()=>l,cT:()=>c,lc:()=>u});var r=n(43272),i=n(91069);const o="minBidCacheTTL";let s=1,a=null;const d=[];function c(e){return e.ttl-(e.hasOwnProperty("ttlBuffer")?e.ttlBuffer:s)}function l(){return a}function u(e){d.push(e)}r.$W.getConfig("ttlBuffer",(e=>{"number"==typeof e.ttlBuffer?s=e.ttlBuffer:(0,i.logError)("Invalid value for ttlBuffer",e.ttlBuffer)})),r.$W.getConfig(o,(e=>{const t=a;a=e?.[o],a="number"==typeof a?a:null,t!==a&&d.forEach((e=>e(a)))}))},12693:(e,t,n)=>{n.d(t,{u:()=>a});var r=n(70433),i=n(91069),o=n(7873),s=n(78969);const a=new class{constructor(e,t){this.getSettings=e,this.defaultScope=t}get(e,t){let n=this.getOwn(e,t);return void 0===n&&(n=this.getOwn(null,t)),n}getOwn(e,t){return e=this.#e(e),(0,r.A)(this.getSettings(),`${e}.${t}`)}getScopes(){return Object.keys(this.getSettings()).filter((e=>e!==this.defaultScope))}settingsFor(e){return(0,i.mergeDeep)({},this.ownSettingsFor(null),this.ownSettingsFor(e))}ownSettingsFor(e){return e=this.#e(e),this.getSettings()[e]||{}}#e(e){return null==e?this.defaultScope:e}}((()=>(0,o.m)().bidderSettings||{}),s.iD.BD_SETTING_STANDARD)},93597:(e,t,n)=>{n.d(t,{O:()=>o});var r=n(91069);function i(e){let{src:t="client",bidder:n="",bidId:i,transactionId:o,adUnitId:s,auctionId:a}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var d=t,c=e||0;Object.assign(this,{bidderCode:n,width:0,height:0,statusMessage:function(){switch(c){case 0:return"Pending";case 1:return"Bid available";case 2:return"Bid returned empty or error response";case 3:return"Bid timed out"}}(),adId:(0,r.getUniqueIdentifierStr)(),requestId:i,transactionId:o,adUnitId:s,auctionId:a,mediaType:"banner",source:d}),this.getStatusCode=function(){return c},this.getSize=function(){return this.width+"x"+this.height},this.getIdentifiers=function(){return{src:this.source,bidder:this.bidderCode,bidId:this.requestId,transactionId:this.transactionId,adUnitId:this.adUnitId,auctionId:this.auctionId}}}function o(e,t){return new i(e,t)}},43272:(e,t,n)=>{n.d(t,{$W:()=>m,Ov:()=>l});var r=n(86833),i=n(15901),o=n(91069),s=n(70433),a=n(78969);const d="TRUE"===(0,o.getParameterByName)(a.M).toUpperCase(),c={},l="random",u={};u[l]=!0,u.fixed=!0;const g=l,f={LOW:"low",MEDIUM:"medium",HIGH:"high",AUTO:"auto",DENSE:"dense",CUSTOM:"custom"};function p(e){const t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1]?{priceGranularity:f.MEDIUM,customPriceBucket:{},mediaTypePriceGranularity:{},bidderSequence:g,auctionOptions:{}}:{};function n(e){return t[e]}function s(n,r){t.hasOwnProperty(n)||Object.defineProperty(e,n,{enumerable:!0}),t[n]=r}const a={publisherDomain:{set(e){null!=e&&(0,o.logWarn)("publisherDomain is deprecated and has no effect since v7 - use pageUrl instead"),s("publisherDomain",e)}},priceGranularity:{set(e){c(e)&&("string"==typeof e?s("priceGranularity",d(e)?e:f.MEDIUM):(0,o.isPlainObject)(e)&&(s("customPriceBucket",e),s("priceGranularity",f.CUSTOM),(0,o.logMessage)("Using custom price granularity")))}},customPriceBucket:{},mediaTypePriceGranularity:{set(e){null!=e&&s("mediaTypePriceGranularity",Object.keys(e).reduce(((t,r)=>(c(e[r])?"string"==typeof e?t[r]=d(e[r])?e[r]:n("priceGranularity"):(0,o.isPlainObject)(e)&&(t[r]=e[r],(0,o.logMessage)(`Using custom price granularity for ${r}`)):(0,o.logWarn)(`Invalid price granularity for media type: ${r}`),t)),{}))}},bidderSequence:{set(e){u[e]?s("bidderSequence",e):(0,o.logWarn)(`Invalid order: ${e}. Bidder Sequence was not set.`)}},auctionOptions:{set(e){(function(e){if(!(0,o.isPlainObject)(e))return(0,o.logWarn)("Auction Options must be an object"),!1;for(let t of Object.keys(e)){if("secondaryBidders"!==t&&"suppressStaleRender"!==t&&"suppressExpiredRender"!==t)return(0,o.logWarn)(`Auction Options given an incorrect param: ${t}`),!1;if("secondaryBidders"===t){if(!(0,o.isArray)(e[t]))return(0,o.logWarn)(`Auction Options ${t} must be of type Array`),!1;if(!e[t].every(o.isStr))return(0,o.logWarn)(`Auction Options ${t} must be only string`),!1}else if(("suppressStaleRender"===t||"suppressExpiredRender"===t)&&!(0,o.isBoolean)(e[t]))return(0,o.logWarn)(`Auction Options ${t} must be of type boolean`),!1}return!0})(e)&&s("auctionOptions",e)}}};return Object.defineProperties(e,Object.fromEntries(Object.entries(a).map((e=>{let[r,i]=e;return[r,Object.assign({get:n.bind(null,r),set:s.bind(null,r),enumerable:t.hasOwnProperty(r),configurable:!t.hasOwnProperty(r)},i)]})))),e;function d(e){return(0,i.I6)(Object.keys(f),(t=>e===f[t]))}function c(e){if(!e)return(0,o.logError)("Prebid Error: no value passed to `setPriceGranularity()`"),!1;if("string"==typeof e)d(e)||(0,o.logWarn)("Prebid Warning: setPriceGranularity was called with invalid setting, using `medium` as default.");else if((0,o.isPlainObject)(e)&&!(0,r.q)(e))return(0,o.logError)("Invalid custom price value passed to `setPriceGranularity()`"),!1;return!0}}const m=function(){let e,t,n,r=[],a=null;function l(){e={};let r=p({debug:d,bidderTimeout:3e3,enableSendAllBids:true,useBidCache:false,deviceAccess:true,disableAjaxTimeout:false,maxNestedIframes:10,maxBid:5e3,userSync:{topics:c}});t&&v(Object.keys(t).reduce(((e,n)=>(t[n]!==r[n]&&(e[n]=r[n]||{}),e)),{})),t=r,n={}}function u(){if(a&&n&&(0,o.isPlainObject)(n[a])){let e=n[a];const r=new Set(Object.keys(t).concat(Object.keys(e)));return(0,i.A6)(r).reduce(((n,r)=>(void 0===e[r]?n[r]=t[r]:void 0===t[r]?n[r]=e[r]:(0,o.isPlainObject)(e[r])?n[r]=(0,o.mergeDeep)({},t[r],e[r]):n[r]=e[r],n)),{})}return Object.assign({},t)}const[g,f]=[u,function(){const e=u();return Object.defineProperty(e,"ortb2",{get:function(){throw new Error("invalid access to 'orbt2' config - use request parameters instead")}}),e}].map((e=>function(){if(arguments.length<=1&&"function"!=typeof(arguments.length<=0?void 0:arguments[0])){const t=arguments.length<=0?void 0:arguments[0];return t?(0,s.A)(e(),t):u()}return y(...arguments)})),[m,h]=[f,g].map((e=>function(){let t=e(...arguments);return t&&"object"==typeof t&&(t=(0,o.deepClone)(t)),t}));function b(n){if(!(0,o.isPlainObject)(n))return void(0,o.logError)("setConfig options must be an object");let r=Object.keys(n),i={};r.forEach((r=>{let s=n[r];(0,o.isPlainObject)(e[r])&&(0,o.isPlainObject)(s)&&(s=Object.assign({},e[r],s));try{i[r]=t[r]=s}catch(e){(0,o.logWarn)(`Cannot set config for property ${r} : `,e)}})),v(i)}function y(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=t;if("string"!=typeof e&&(i=e,e="*",n=t||{}),"function"!=typeof i)return void(0,o.logError)("listener must be a function");const s={topic:e,callback:i};return r.push(s),n.init&&i("*"===e?f():{[e]:f(e)}),function(){r.splice(r.indexOf(s),1)}}function v(e){const t=Object.keys(e);r.filter((e=>(0,i.mK)(t,e.topic))).forEach((t=>{t.callback({[t.topic]:e[t.topic]})})),r.filter((e=>"*"===e.topic)).forEach((t=>t.callback(e)))}function E(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{!function(e){if(!(0,o.isPlainObject)(e))throw"setBidderConfig bidder options must be an object";if(!Array.isArray(e.bidders)||!e.bidders.length)throw"setBidderConfig bidder options must contain a bidders list with at least 1 bidder";if(!(0,o.isPlainObject)(e.config))throw"setBidderConfig bidder options must contain a config object"}(e),e.bidders.forEach((r=>{n[r]||(n[r]=p({},!1)),Object.keys(e.config).forEach((i=>{let s=e.config[i];const a=n[r][i];if((0,o.isPlainObject)(s)&&(null==a||(0,o.isPlainObject)(a))){const e=t?o.mergeDeep:Object.assign;n[r][i]=e({},a||{},s)}else n[r][i]=s}))}))}catch(e){(0,o.logError)(e)}}function A(e,t){a=e;try{return t()}finally{I()}}function I(){a=null}return l(),{getCurrentBidder:function(){return a},resetBidder:I,getConfig:f,getAnyConfig:g,readConfig:m,readAnyConfig:h,setConfig:b,mergeConfig:function(e){if(!(0,o.isPlainObject)(e))return void(0,o.logError)("mergeConfig input must be an object");const t=(0,o.mergeDeep)(u(),e);return b({...t}),t},setDefaults:function(n){(0,o.isPlainObject)(e)?(Object.assign(e,n),Object.assign(t,n)):(0,o.logError)("defaults must be an object")},resetConfig:l,runWithBidder:A,callbackWithBidder:function(e){return function(t){return function(){if("function"==typeof t){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return A(e,t.bind(this,...r))}(0,o.logWarn)("config.callbackWithBidder callback is not a function")}}},setBidderConfig:E,getBidderConfig:function(){return n},mergeBidderConfig:function(e){return E(e,!0)}}}()},16916:(e,t,n)=>{n.d(t,{B1:()=>s,SL:()=>p,ad:()=>l,et:()=>u,mW:()=>d,o2:()=>g,t6:()=>c});var r=n(91069),i=n(25555),o=n(43272);const s=Object.freeze({});class a{#t;#n;#r;#i;#o=!0;#s;generatedTime;hashFields;constructor(){this.reset()}#a(e){this.#i=!0,this.#n=e,this.#r.resolve(e)}reset(){this.#r=(0,i.v)(),this.#t=!1,this.#n=null,this.#i=!1,this.generatedTime=null}enable(){this.#t=!0}get enabled(){return this.#t}get ready(){return this.#i}get promise(){return this.#i?i.k.resolve(this.#n):(this.#t||this.#a(null),this.#r.promise)}setConsentData(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(0,r.timestamp)();this.generatedTime=t,this.#o=!0,this.#a(e)}getConsentData(){return this.#n}get hash(){return this.#o&&(this.#s=(0,r.cyrb53Hash)(JSON.stringify(this.#n&&this.hashFields?this.hashFields.map((e=>this.#n[e])):this.#n)),this.#o=!1),this.#s}}const d=new class extends a{hashFields=["gdprApplies","consentString"];getConsentMeta(){const e=this.getConsentData();if(e&&e.vendorData&&this.generatedTime)return{gdprApplies:e.gdprApplies,consentStringSize:(0,r.isStr)(e.vendorData.tcString)?e.vendorData.tcString.length:0,generatedAt:this.generatedTime,apiVersion:e.apiVersion}}},c=new class extends a{getConsentMeta(){if(this.getConsentData()&&this.generatedTime)return{generatedAt:this.generatedTime}}},l=new class extends a{hashFields=["applicableSections","gppString"];getConsentMeta(){if(this.getConsentData()&&this.generatedTime)return{generatedAt:this.generatedTime}}},u=(()=>{function e(){return!!o.$W.getConfig("coppa")}return{getCoppa:e,getConsentData:e,getConsentMeta:e,reset(){},get promise(){return i.k.resolve(e())},get hash(){return e()?"1":"0"}}})(),g=function(){const e={},t={},n={};return{register(r,i,o){o&&((e[i]=e[i]||{})[r]=o,t.hasOwnProperty(i)?t[i]!==o&&(t[i]=n):t[i]=o)},get(r){const i={modules:e[r]||{}};return t.hasOwnProperty(r)&&t[r]!==n&&(i.gvlid=t[r]),i}}}(),f={gdpr:d,usp:c,gpp:l,coppa:u};const p=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f;return e=Object.entries(e),Object.assign({get promise(){return i.k.all(e.map((e=>{let[t,n]=e;return n.promise.then((e=>[t,e]))}))).then((e=>Object.fromEntries(e)))},get hash(){return(0,r.cyrb53Hash)(e.map((e=>{let[t,n]=e;return n.hash})).join(":"))}},Object.fromEntries(["getConsentData","getConsentMeta","reset"].map((t=>{return[t,(n=t,function(){return Object.fromEntries(e.map((e=>{let[t,r]=e;return[t,r[n]()]})))})];var n}))))}()},78969:(e,t,n)=>{n.d(t,{IY:()=>A,M:()=>i,RW:()=>f,Tf:()=>m,UE:()=>c,XQ:()=>o,Zh:()=>u,_B:()=>v,as:()=>a,cA:()=>d,h0:()=>h,iD:()=>r,jO:()=>b,nl:()=>E,oA:()=>y,qY:()=>s,tl:()=>p,x5:()=>g,xS:()=>l});const r={PL_CODE:"code",PL_SIZE:"sizes",PL_BIDS:"bids",BD_BIDDER:"bidder",BD_ID:"paramsd",BD_PL_ID:"placementId",ADSERVER_TARGETING:"adserverTargeting",BD_SETTING_STANDARD:"standard"},i="pbjs_debug",o={GOOD:1},s={AUCTION_INIT:"auctionInit",AUCTION_TIMEOUT:"auctionTimeout",AUCTION_END:"auctionEnd",BID_ADJUSTMENT:"bidAdjustment",BID_TIMEOUT:"bidTimeout",BID_REQUESTED:"bidRequested",BID_RESPONSE:"bidResponse",BID_REJECTED:"bidRejected",NO_BID:"noBid",SEAT_NON_BID:"seatNonBid",BID_WON:"bidWon",BIDDER_DONE:"bidderDone",BIDDER_ERROR:"bidderError",SET_TARGETING:"setTargeting",BEFORE_REQUEST_BIDS:"beforeRequestBids",BEFORE_BIDDER_HTTP:"beforeBidderHttp",REQUEST_BIDS:"requestBids",ADD_AD_UNITS:"addAdUnits",AD_RENDER_FAILED:"adRenderFailed",AD_RENDER_SUCCEEDED:"adRenderSucceeded",TCF2_ENFORCEMENT:"tcf2Enforcement",AUCTION_DEBUG:"auctionDebug",BID_VIEWABLE:"bidViewable",STALE_RENDER:"staleRender",EXPIRED_RENDER:"expiredRender",BILLABLE_EVENT:"billableEvent",BID_ACCEPTED:"bidAccepted",RUN_PAAPI_AUCTION:"paapiRunAuction",PBS_ANALYTICS:"pbsAnalytics",PAAPI_BID:"paapiBid",PAAPI_NO_BID:"paapiNoBid",PAAPI_ERROR:"paapiError"},a={PREVENT_WRITING_ON_MAIN_DOCUMENT:"preventWritingOnMainDocument",NO_AD:"noAd",EXCEPTION:"exception",CANNOT_FIND_AD:"cannotFindAd",MISSING_DOC_OR_ADID:"missingDocOrAdid"},d={bidWon:"adUnitCode"},c={LOW:"low",MEDIUM:"medium",HIGH:"high",AUTO:"auto",DENSE:"dense",CUSTOM:"custom"},l={BIDDER:"hb_bidder",AD_ID:"hb_adid",PRICE_BUCKET:"hb_pb",SIZE:"hb_size",DEAL:"hb_deal",SOURCE:"hb_source",FORMAT:"hb_format",UUID:"hb_uuid",CACHE_ID:"hb_cache_id",CACHE_HOST:"hb_cache_host",ADOMAIN:"hb_adomain",ACAT:"hb_acat",CRID:"hb_crid",DSP:"hb_dsp"},u={BIDDER:"hb_bidder",AD_ID:"hb_adid",PRICE_BUCKET:"hb_pb",SIZE:"hb_size",DEAL:"hb_deal",FORMAT:"hb_format",UUID:"hb_uuid",CACHE_HOST:"hb_cache_host"},g={title:"hb_native_title",body:"hb_native_body",body2:"hb_native_body2",privacyLink:"hb_native_privacy",privacyIcon:"hb_native_privicon",sponsoredBy:"hb_native_brand",image:"hb_native_image",icon:"hb_native_icon",clickUrl:"hb_native_linkurl",displayUrl:"hb_native_displayurl",cta:"hb_native_cta",rating:"hb_native_rating",address:"hb_native_address",downloads:"hb_native_downloads",likes:"hb_native_likes",phone:"hb_native_phone",price:"hb_native_price",salePrice:"hb_native_saleprice",rendererUrl:"hb_renderer_url",adTemplate:"hb_adTemplate"},f={SRC:"s2s",DEFAULT_ENDPOINT:"https://prebid.adnxs.com/pbs/v1/openrtb2/auction",SYNCED_BIDDERS_KEY:"pbjsSyncs"},p={BID_TARGETING_SET:"targetingSet",RENDERED:"rendered",BID_REJECTED:"bidRejected"},m={INVALID:"Bid has missing or invalid properties",INVALID_REQUEST_ID:"Invalid request ID",BIDDER_DISALLOWED:"Bidder code is not allowed by allowedAlternateBidderCodes / allowUnknownBidderCodes",FLOOR_NOT_MET:"Bid does not meet price floor",CANNOT_CONVERT_CURRENCY:"Unable to convert currency",DSA_REQUIRED:"Bid does not provide required DSA transparency info",DSA_MISMATCH:"Bid indicates inappropriate DSA rendering method",PRICE_TOO_HIGH:"Bid price exceeds maximum value"},h={body:"desc",body2:"desc2",sponsoredBy:"sponsored",cta:"ctatext",rating:"rating",address:"address",downloads:"downloads",likes:"likes",phone:"phone",price:"price",salePrice:"saleprice",displayUrl:"displayurl"},b={sponsored:1,desc:2,rating:3,likes:4,downloads:5,price:6,saleprice:7,phone:8,address:9,desc2:10,displayurl:11,ctatext:12},y={ICON:1,MAIN:3},v=["privacyIcon","clickUrl","sendTargetingKeys","adTemplate","rendererUrl","type"],E={REQUEST:"Prebid Request",RESPONSE:"Prebid Response",NATIVE:"Prebid Native",EVENT:"Prebid Event"},A="__pb_locator__"},86833:(e,t,n)=>{n.d(t,{j:()=>g,q:()=>p});var r=n(15901),i=n(91069),o=n(43272);const s=2,a={buckets:[{max:5,increment:.5}]},d={buckets:[{max:20,increment:.1}]},c={buckets:[{max:20,increment:.01}]},l={buckets:[{max:3,increment:.01},{max:8,increment:.05},{max:20,increment:.5}]},u={buckets:[{max:5,increment:.05},{max:10,increment:.1},{max:20,increment:.5}]};function g(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=parseFloat(e);return isNaN(r)&&(r=""),{low:""===r?"":f(e,a,n),med:""===r?"":f(e,d,n),high:""===r?"":f(e,c,n),auto:""===r?"":f(e,u,n),dense:""===r?"":f(e,l,n),custom:""===r?"":f(e,t,n)}}function f(e,t,n){let a="";if(!p(t))return a;const d=t.buckets.reduce(((e,t)=>e.max>t.max?e:t),{max:0});let c=0,l=(0,r.I6)(t.buckets,(t=>{if(e>d.max*n){let e=t.precision;void 0===e&&(e=s),a=(t.max*n).toFixed(e)}else{if(e<=t.max*n&&e>=c*n)return t.min=c,t;c=t.max}}));return l&&(a=function(e,t,n){const r=void 0!==t.precision?t.precision:s,a=t.increment*n,d=t.min*n;let c=Math.floor,l=o.$W.getConfig("cpmRoundingFunction");"function"==typeof l&&(c=l);let u,g,f=Math.pow(10,r+2),p=(e*f-d*f)/(a*f);try{u=c(p)*a+d}catch(e){g=!0}(g||"number"!=typeof u)&&((0,i.logWarn)("Invalid rounding function passed in config"),u=Math.floor(p)*a+d);return u=Number(u.toFixed(10)),u.toFixed(r)}(e,l,n)),a}function p(e){if((0,i.isEmpty)(e)||!e.buckets||!Array.isArray(e.buckets))return!1;let t=!0;return e.buckets.forEach((e=>{e.max&&e.increment||(t=!1)})),t}},46031:(e,t,n)=>{n.d(t,{H:()=>a,k:()=>s});var r=n(25555),i=n(91069),o=n(34595);const s=(0,n(16833).A_)("sync",(function(e){return o.G})),a=function(){const e={};return function(t){const n=s(t);return e.hasOwnProperty(n)||(e[n]=new r.k((e=>{const t=(0,i.createInvisibleIframe)();t.srcdoc=`<script>${n}<\/script>`,t.onload=()=>e(t.contentWindow.render),document.body.appendChild(t)}))),e[n]}}()},49164:(e,t,n)=>{n.d(t,{L6:()=>h,ey:()=>u});var r=n(43272),i=n(16833),o=n(7873),s=n(91069),a=n(93597),d=n(41580),c=n(25555),l=n(45569);const u="__pbjs_debugging__";function g(){return(0,o.m)().installedModules.includes("debugging")}function f(e){return new c.k((t=>{(0,d.R)(e,l.tp,"debugging",t)}))}function p(){let{alreadyInstalled:e=g,script:t=f}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=null;return function(){return null==n&&(n=new c.k(((n,d)=>{setTimeout((()=>{if(e())n();else{const e="https://cdn.jsdelivr.net/npm/prebid.js@latest/dist/debugging-standalone.js";(0,s.logMessage)(`Debugging module not installed, loading it from "${e}"...`),(0,o.m)()._installDebugging=!0,t(e).then((()=>{(0,o.m)()._installDebugging({DEBUG_KEY:u,hook:i.A_,config:r.$W,createBid:a.O,logger:(0,s.prefixLog)("DEBUG:")})})).then(n,d)}}))}))),n}}const m=function(){let{load:e=p(),hook:t=(0,i.Yn)("requestBids")}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=null,r=!1;function o(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];return(n||c.k.resolve()).then((()=>e.apply(this,r)))}function s(){t.getHooks({hook:o}).remove(),r=!1}return{enable:function(){r||(n=e(),t.before(o,99),r=!0)},disable:s,reset:function(){n=null,s()}}}();m.reset;function h(){let e=null;try{e=window.sessionStorage}catch(e){}if(null!==e){let t=m,n=null;try{n=e.getItem(u)}catch(e){}null!==n&&t.enable()}}r.$W.getConfig("debugging",(function(e){let{debugging:t}=e;t?.enabled?m.enable():m.disable()}))},75023:(e,t,n)=>{n.r(t),n.d(t,{addEvents:()=>E,clearEvents:()=>I,emit:()=>v,get:()=>b,getEvents:()=>y,has:()=>A,off:()=>h,on:()=>m});var r=n(91069),i=n(78969),o=n(76853),s=n(43272);const a="eventHistoryTTL";let d=null;const c=(0,o.H)({monotonic:!0,ttl:()=>d});s.$W.getConfig(a,(e=>{const t=d;e=e?.[a],d="number"==typeof e?1e3*e:null,t!==d&&c.refresh()}));let l=Array.prototype.slice,u=Array.prototype.push,g=Object.values(i.qY);const f=i.cA,p=function(){let e={},t={};function n(e){return g.includes(e)}return t.has=n,t.on=function(t,i,o){if(n(t)){let n=e[t]||{que:[]};o?(n[o]=n[o]||{que:[]},n[o].que.push(i)):n.que.push(i),e[t]=n}else r.logError("Wrong event name : "+t+" Valid event names :"+g)},t.emit=function(t){!function(t,n){r.logMessage("Emitting event for: "+t);let i=n[0]||{},o=i[f[t]],s=e[t]||{que:[]};var a=Object.keys(s);let d=[];c.add({eventType:t,args:i,id:o,elapsedTime:r.getPerformanceNow()}),o&&a.includes(o)&&u.apply(d,s[o].que),u.apply(d,s.que),(d||[]).forEach((function(e){if(e)try{e.apply(null,n)}catch(e){r.logError("Error executing handler:","events.js",e,t)}}))}(t,l.call(arguments,1))},t.off=function(t,n,i){let o=e[t];r.isEmpty(o)||r.isEmpty(o.que)&&r.isEmpty(o[i])||i&&(r.isEmpty(o[i])||r.isEmpty(o[i].que))||(i?(o[i].que||[]).forEach((function(e){let t=o[i].que;e===n&&t.splice(t.indexOf(e),1)})):(o.que||[]).forEach((function(e){let t=o.que;e===n&&t.splice(t.indexOf(e),1)})),e[t]=o)},t.get=function(){return e},t.addEvents=function(e){g=g.concat(e)},t.getEvents=function(){return c.toArray().map((e=>Object.assign({},e)))},t}();r._setEventEmitter(p.emit.bind(p));const{on:m,off:h,get:b,getEvents:y,emit:v,addEvents:E,has:A}=p;function I(){c.clear()}},70068:(e,t,n)=>{n.d(t,{w:()=>y});var r=n(16833),i=n(27934),o=n(5973),s=n(91069),a=n(63172),d=n(43272),c=n(25250),l=n(25555),u=n(73858),g=n(95139),f=n(83441),p=n(76811),m=n(45569);const h={getRefererInfo:i.EN,findRootDomain:o.S,getWindowTop:s.getWindowTop,getWindowSelf:s.getWindowSelf,getHighEntropySUA:c.FD,getLowEntropySUA:c.zO},b=(0,u.i8)("FPD"),y=(0,r.A_)("sync",(e=>{const t=[e,E().catch((()=>null)),l.k.resolve("cookieDeprecationLabel"in navigator&&(0,g.io)(p.Ue,(0,f.s)(m.tp,"cdep"))&&navigator.cookieDeprecationLabel.getValue()).catch((()=>null))];return l.k.all(t).then((e=>{let[t,n,r]=e;const i=h.getRefererInfo();if(Object.entries(I).forEach((e=>{let[n,r]=e;const o=r(t,i);o&&Object.keys(o).length>0&&(t[n]=(0,s.mergeDeep)({},o,t[n]))})),n&&(0,a.J)(t,"device.sua",Object.assign({},n,t.device.sua)),r){const e={cdep:r};(0,a.J)(t,"device.ext",Object.assign({},e,t.device.ext))}t=b(t);for(let e of u.Dy)if((0,u.O$)(t,e)){t[e]=(0,s.mergeDeep)({},T(t,i),t[e]);break}return t}))}));function v(e){try{return e(h.getWindowTop())}catch(t){return e(h.getWindowSelf())}}function E(){const e=d.$W.getConfig("firstPartyData.uaHints");return Array.isArray(e)&&0!==e.length?h.getHighEntropySUA(e):l.k.resolve(h.getLowEntropySUA())}function A(e){return(0,s.getDefinedParams)(e,Object.keys(e))}const I={site(e,t){if(!u.Dy.filter((e=>"site"!==e)).some(u.O$.bind(null,e)))return A({page:t.page,ref:t.ref})},device:()=>v((e=>{const t=e.screen.width,n=e.screen.height,r=e.innerWidth||e.document.documentElement.clientWidth||e.document.body.clientWidth,i=e.innerHeight||e.document.documentElement.clientHeight||e.document.body.clientHeight,o={w:t,h:n,dnt:(0,s.getDNT)()?1:0,ua:e.navigator.userAgent,language:e.navigator.language.split("-").shift(),ext:{vpw:r,vph:i}};return e.navigator?.webdriver&&(0,a.J)(o,"ext.webdriver",!0),o})),regs(){const e={};v((e=>e.navigator.globalPrivacyControl))&&(0,a.J)(e,"ext.gpc","1");const t=d.$W.getConfig("coppa");return"boolean"==typeof t&&(e.coppa=t?1:0),e}};function T(e,t){const n=(0,i.gR)(t.page,{noLeadingWww:!0}),r=v((e=>e.document.querySelector("meta[name='keywords']")))?.content?.replace?.(/\s/g,"");return A({domain:n,keywords:r,publisher:A({domain:h.findRootDomain(n)})})}},73858:(e,t,n)=>{n.d(t,{Dy:()=>i,O$:()=>s,i8:()=>o});var r=n(91069);const i=["dooh","app","site"];function o(e){return function(t){return i.reduce(((n,i)=>(s(t,i)&&(null!=n?((0,r.logWarn)(`${e} specifies both '${n}' and '${i}'; dropping the latter.`),delete t[i]):n=i),n)),null),t}}function s(e,t){return null!=e[t]&&Object.keys(e[t]).length>0}},5973:(e,t,n)=>{n.d(t,{S:()=>o});var r=n(91069);const i=(0,n(12938).CK)("fpdEnrichment"),o=(0,r.memoize)((function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.location.host;if(!i.cookiesAreEnabled())return e;const t=e.split(".");if(2===t.length)return e;let n,o,s=-2;const a=`_rdc${Date.now()}`,d="writeable";do{n=t.slice(s).join(".");let e=new Date((0,r.timestamp)()+1e4).toUTCString();i.setCookie(a,d,e,"Lax",n,void 0);i.getCookie(a,void 0)===d?(o=!1,i.setCookie(a,"","Thu, 01 Jan 1970 00:00:01 GMT",void 0,n,void 0)):(s+=-1,o=Math.abs(s)<=t.length)}while(o);return n}))},25250:(e,t,n)=>{n.d(t,{CP:()=>l,FD:()=>c,zO:()=>d});var r=n(91069),i=n(25555);const o=2,s=["architecture","bitness","model","platformVersion","fullVersionList"],a=["brands","mobile","platform"],d=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator?.userAgentData;const t=e&&a.some((t=>void 0!==e[t]))?Object.freeze(u(1,e)):null;return function(){return t}}(),c=l();function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator?.userAgentData;const t={},n=new WeakMap;return function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s;if(!n.has(a)){const e=Array.from(a);e.sort(),n.set(a,e.join("|"))}const d=n.get(a);if(!t.hasOwnProperty(d))try{t[d]=e.getHighEntropyValues(a).then((e=>(0,r.isEmpty)(e)?null:Object.freeze(u(o,e)))).catch((()=>null))}catch(e){t[d]=i.k.resolve(null)}return t[d]}}function u(e,t){function n(e,t){const n={brand:e};return(0,r.isStr)(t)&&!(0,r.isEmptyStr)(t)&&(n.version=t.split(".")),n}const i={source:e};return t.platform&&(i.platform=n(t.platform,t.platformVersion)),(t.fullVersionList||t.brands)&&(i.browsers=(t.fullVersionList||t.brands).map((e=>{let{brand:t,version:r}=e;return n(t,r)}))),void 0!==t.mobile&&(i.mobile=t.mobile?1:0),["model","bitness","architecture"].forEach((e=>{const n=t[e];(0,r.isStr)(n)&&(i[e]=n)})),i}},16833:(e,t,n)=>{n.d(t,{A_:()=>s,Gc:()=>d,Y6:()=>p,Yn:()=>c,bz:()=>f,pT:()=>l,xG:()=>g});var r=n(68128),i=n.n(r),o=n(25555);let s=i()({ready:i().SYNC|i().ASYNC|i().QUEUE});const a=(0,o.v)();s.ready=(()=>{const e=s.ready;return function(){try{return e.apply(s,arguments)}finally{a.resolve()}}})();const d=a.promise,c=s.get;function l(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:15;0===e.getHooks({hook:t}).length&&e.before(t,n)}const u={};function g(e,t){let{postInstallAllowed:n=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};s("async",(function(r){r.forEach((e=>t(...e))),n&&(u[e]=t)}),e)([])}function f(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];const i=u[e];if(i)return i(...n);c(e).before(((e,t)=>{t.push(n),e(t)}))}function p(e,t){return Object.defineProperties(t,Object.fromEntries(["before","after","getHooks","removeAll"].map((t=>[t,{get:()=>e[t]}])))),t}},71371:(e,t,n)=>{n.d(t,{D4:()=>o,G_:()=>i,LM:()=>s,s6:()=>r});const r="native",i="video",o="banner",s="adpod"},12449:(e,t,n)=>{n.d(t,{Bm:()=>A,Ex:()=>q,Gg:()=>S,IX:()=>R,Nh:()=>u,Xj:()=>$,Zj:()=>w,gs:()=>T,l6:()=>b,mT:()=>l,nk:()=>v,rn:()=>U,vO:()=>I,yl:()=>k});var r=n(91069),i=n(15901),o=n(67314),s=n(78969),a=n(71371),d=n(29075),c=n(46031);const l=[],u=Object.keys(s.x5).map((e=>s.x5[e])),g={image:{ortb:{ver:"1.2",assets:[{required:1,id:1,img:{type:3,wmin:100,hmin:100}},{required:1,id:2,title:{len:140}},{required:1,id:3,data:{type:1}},{required:0,id:4,data:{type:2}},{required:0,id:5,img:{type:1,wmin:20,hmin:20}}]},image:{required:!0},title:{required:!0},sponsoredBy:{required:!0},clickUrl:{required:!0},body:{required:!1},icon:{required:!1}}},f=W(s.h0),p=W(s.jO),m={img:1,js:2,1:"img",2:"js"},h={impression:1,"viewable-mrc50":2,"viewable-mrc100":3,"viewable-video50":4};function b(e){return e.native&&"object"==typeof e.native}function y(e){if(e&&e.type&&function(e){if(!e||!(0,i.mK)(Object.keys(g),e))return(0,r.logError)(`${e} nativeParam is not supported`),!1;return!0}(e.type)&&(e=g[e.type]),!e||!e.ortb||E(e.ortb))return e}function v(e){e.forEach((e=>{const t=e.nativeParams||e?.mediaTypes?.native;t&&(e.nativeParams=y(t)),e.nativeParams&&(e.nativeOrtbRequest=e.nativeParams.ortb||U(e.nativeParams))}))}function E(e){const t=e.assets;if(!Array.isArray(t)||0===t.length)return(0,r.logError)("assets in mediaTypes.native.ortb is not an array, or it's empty. Assets: ",t),!1;const n=t.map((e=>e.id));return t.length!==new Set(n).size||n.some((e=>e!==parseInt(e,10)))?((0,r.logError)("each asset object must have 'id' property, it must be unique and it must be an integer"),!1):e.hasOwnProperty("eventtrackers")&&!Array.isArray(e.eventtrackers)?((0,r.logError)("ortb.eventtrackers is not an array. Eventtrackers: ",e.eventtrackers),!1):t.every((e=>function(e){if(!(0,r.isPlainObject)(e))return(0,r.logError)("asset must be an object. Provided asset: ",e),!1;if(e.img){if(!(0,r.isNumber)(e.img.w)&&!(0,r.isNumber)(e.img.wmin))return(0,r.logError)("for img asset there must be 'w' or 'wmin' property"),!1;if(!(0,r.isNumber)(e.img.h)&&!(0,r.isNumber)(e.img.hmin))return(0,r.logError)("for img asset there must be 'h' or 'hmin' property"),!1}else if(e.title){if(!(0,r.isNumber)(e.title.len))return(0,r.logError)("for title asset there must be 'len' property defined"),!1}else if(e.data){if(!(0,r.isNumber)(e.data.type))return(0,r.logError)("for data asset 'type' property must be a number"),!1}else if(e.video&&!(Array.isArray(e.video.mimes)&&Array.isArray(e.video.protocols)&&(0,r.isNumber)(e.video.minduration)&&(0,r.isNumber)(e.video.maxduration)))return(0,r.logError)("video asset is not properly configured"),!1;return!0}(e)))}function A(e){let{index:t=o.n.index}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=t.getAdUnit(e);if(!n)return!1;let s=n.nativeOrtbRequest;return function(e,t){if(!e?.link?.url)return(0,r.logError)("native response doesn't have 'link' property. Ortb response: ",e),!1;let n=t.assets.filter((e=>1===e.required)).map((e=>e.id)),o=e.assets.map((e=>e.id));const s=n.every((e=>(0,i.mK)(o,e)));s||(0,r.logError)(`didn't receive a bid with all required assets. Required ids: ${n}, but received ids in response: ${o}`);return s}(e.native?.ortb||x(e.native,s),s)}function I(e,t){const n=t.native.ortb||N(t.native);return"click"===e.action?function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,{fetchURL:n=r.triggerPixel}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(t){const r=(e.assets||[]).filter((e=>e.link)).reduce(((e,t)=>(e[t.id]=t.link,e)),{}),i=e.link?.clicktrackers||[];let o=r[t],s=i;o&&(s=o.clicktrackers||[]),s.forEach((e=>n(e)))}else(e.link?.clicktrackers||[]).forEach((e=>n(e)))}(n,e?.assetId):function(e){let{runMarkup:t=(e=>(0,r.insertHtmlIntoIframe)(e)),fetchURL:n=r.triggerPixel}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=(e.eventtrackers||[]).filter((e=>e.event===h.impression));let{img:o,js:s}=i.reduce(((e,t)=>(m.hasOwnProperty(t.method)&&e[m[t.method]].push(t.url),e)),{img:[],js:[]});e.imptrackers&&(o=o.concat(e.imptrackers));o.forEach((e=>n(e))),s=s.map((e=>`<script async src="${e}"><\/script>`)),e.jstracker&&(s=s.concat([e.jstracker]));s.length&&t(s.join("\n"))}(n),e.action}function T(e,t){const n=t?.nativeOrtbRequest,r=e.native?.ortb;if(n&&r){const t=q(r,n);Object.assign(e.native,t)}["rendererUrl","adTemplate"].forEach((n=>{const r=t?.nativeParams?.[n];r&&(e.native[n]=D(r))}))}function w(e){let{index:t=o.n.index}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n={};const r=t.getAdUnit(e),i=null==r?.nativeParams?.ortb&&!1!==r?.nativeParams?.sendTargetingKeys,a=function(e){const t={};e?.nativeParams?.ext&&Object.keys(e.nativeParams.ext).forEach((e=>{t[e]=`hb_native_${e}`}));return{...s.x5,...t}}(r),d={...e.native,...e.native.ext};return delete d.ext,Object.keys(d).forEach((t=>{const o=a[t];let s=D(e.native[t])||D(e?.native?.ext?.[t]);if("adTemplate"===t||!o||!s)return;let d=r?.nativeParams?.[t]?.sendId;if("boolean"!=typeof d&&(d=r?.nativeParams?.ext?.[t]?.sendId),d){s=`${o}:${e.adId}`}let c=r?.nativeParams?.[t]?.sendTargetingKeys;"boolean"!=typeof c&&(c=r?.nativeParams?.ext?.[t]?.sendTargetingKeys);("boolean"==typeof c?c:i)&&(n[o]=s)})),n}function C(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=[];return Object.entries(e).filter((e=>{let[r,i]=e;return i&&(!1===n&&"ext"===r||null==t||t.includes(r))})).forEach((e=>{let[i,o]=e;!1===n&&"ext"===i?r.push(...C(o,t,!0)):(n||s.x5.hasOwnProperty(i))&&r.push({key:i,value:D(o)})})),r}function S(e,t,n){const i={...(0,r.getDefinedParams)(e.native,["rendererUrl","adTemplate"]),assets:C(e.native,n),nativeKeys:s.x5};return e.native.ortb?i.ortb=e.native.ortb:t.mediaTypes?.native?.ortb&&(i.ortb=x(e.native,t.nativeOrtbRequest)),i}function O(e,t,n){let{index:r=o.n.index}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const i={message:"assetResponse",adId:e.adId};let{native:s,rendererVersion:a}=(0,d.vd)(t);return s?(Object.assign(i,{native:Object.assign({},s),renderer:(0,c.k)(t),rendererVersion:a}),null!=n&&(s.assets=s.assets.filter((e=>{let{key:t}=e;return n.includes(t)})))):s=S(t,r.getAdUnit(t),n),Object.assign(i,s)}const B=Object.fromEntries(Object.entries(s.x5).map((e=>{let[t,n]=e;return[n,t]})));function R(e,t){const n=e.assets.map((e=>B[e]));return O(e,t,n)}function k(e,t){return O(e,t,null)}function D(e){return e?.url||e}function U(e){if(!e&&!(0,r.isPlainObject)(e))return void(0,r.logError)("Native assets object is empty or not an object: ",e);const t={ver:"1.2",assets:[]};for(let n in e){if(s._B.includes(n))continue;if(!s.x5.hasOwnProperty(n)){(0,r.logError)(`Unrecognized native asset code: ${n}. Asset will be ignored.`);continue}if("privacyLink"===n){t.privacy=1;continue}const i=e[n];let o=0;i.required&&(0,r.isBoolean)(i.required)&&(o=Number(i.required));const a={id:t.assets.length,required:o};if(n in s.h0)a.data={type:s.jO[s.h0[n]]},i.len&&(a.data.len=i.len);else if("icon"===n||"image"===n){if(a.img={type:"icon"===n?s.oA.ICON:s.oA.MAIN},i.aspect_ratios)if((0,r.isArray)(i.aspect_ratios))if(i.aspect_ratios.length){const{min_width:e,min_height:t}=i.aspect_ratios[0];(0,r.isInteger)(e)&&(0,r.isInteger)(t)?(a.img.wmin=e,a.img.hmin=t):(0,r.logError)("image.aspect_ratios min_width or min_height are invalid: ",e,t);const n=i.aspect_ratios.filter((e=>e.ratio_width&&e.ratio_height)).map((e=>`${e.ratio_width}:${e.ratio_height}`));n.length>0&&(a.img.ext={aspectratios:n})}else(0,r.logError)("image.aspect_ratios was passed, but it's empty:",i.aspect_ratios);else(0,r.logError)("image.aspect_ratios was passed, but it's not a an array:",i.aspect_ratios);i.sizes&&(2===i.sizes.length&&(0,r.isInteger)(i.sizes[0])&&(0,r.isInteger)(i.sizes[1])?(a.img.w=i.sizes[0],a.img.h=i.sizes[1],delete a.img.hmin,delete a.img.wmin):(0,r.logError)("image.sizes was passed, but its value is not an array of integers:",i.sizes))}else"title"===n?a.title={len:i.len||140}:"ext"===n&&(a.ext=i,delete a.required);t.assets.push(a)}return t}function j(e,t){for(;e&&t&&e!==t;)e>t?e-=t:t-=e;return e||t}function _(e){if(!E(e))return;const t={};for(const n of e.assets){if(n.title){const e={required:!!n.required&&Boolean(n.required),len:n.title.len};t.title=e}else if(n.img){const e={required:!!n.required&&Boolean(n.required)};if(n.img.w&&n.img.h)e.sizes=[n.img.w,n.img.h];else if(n.img.wmin&&n.img.hmin){const t=j(n.img.wmin,n.img.hmin);e.aspect_ratios=[{min_width:n.img.wmin,min_height:n.img.hmin,ratio_width:n.img.wmin/t,ratio_height:n.img.hmin/t}]}n.img.type===s.oA.MAIN?t.image=e:t.icon=e}else if(n.data){let e=Object.keys(s.jO).find((e=>s.jO[e]===n.data.type)),r=Object.keys(s.h0).find((t=>s.h0[t]===e));t[r]={required:!!n.required&&Boolean(n.required)},n.data.len&&(t[r].len=n.data.len)}e.privacy&&(t.privacyLink={required:!1})}return t}function $(e){{if(!e||!(0,r.isArray)(e))return e;if(!e.some((e=>(e?.mediaTypes||{})[a.s6]?.ortb)))return e;let t=(0,r.deepClone)(e);for(const e of t)e.mediaTypes&&e.mediaTypes[a.s6]&&e.mediaTypes[a.s6].ortb&&(e.mediaTypes[a.s6]=Object.assign((0,r.pick)(e.mediaTypes[a.s6],s._B),_(e.mediaTypes[a.s6].ortb)),e.nativeParams=y(e.mediaTypes[a.s6]));return t}}function N(e){const t={link:{},eventtrackers:[]};return Object.entries(e).forEach((e=>{let[n,r]=e;switch(n){case"clickUrl":t.link.url=r;break;case"clickTrackers":t.link.clicktrackers=Array.isArray(r)?r:[r];break;case"impressionTrackers":(Array.isArray(r)?r:[r]).forEach((e=>{t.eventtrackers.push({event:h.impression,method:m.img,url:e})}));break;case"javascriptTrackers":t.jstracker=Array.isArray(r)?r.join(""):r;break;case"privacyLink":t.privacy=r}})),t}function x(e,t){const n={...N(e),assets:[]};function i(e,i){let o=t.assets.find(e);null!=o&&(o=(0,r.deepClone)(o),i(o),n.assets.push(o))}return Object.keys(e).filter((t=>!!e[t])).forEach((t=>{const n=D(e[t]);switch(t){case"title":i((e=>null!=e.title),(e=>{e.title={text:n}}));break;case"image":case"icon":const e="image"===t?s.oA.MAIN:s.oA.ICON;i((t=>null!=t.img&&t.img.type===e),(e=>{e.img={url:n}}));break;default:t in s.h0&&i((e=>null!=e.data&&e.data.type===s.jO[s.h0[t]]),(e=>{e.data={value:n}}))}})),n}function q(e,t){const n={},r=t?.assets||[];n.clickUrl=e.link?.url,n.privacyLink=e.privacy;for(const t of e?.assets||[]){const e=r.find((e=>t.id===e.id));t.title?n.title=t.title.text:t.img?n[e?.img?.type===s.oA.MAIN?"image":"icon"]={url:t.img.url,width:t.img.w,height:t.img.h}:t.data&&(n[f[p[e?.data?.type]]]=t.data.value)}n.impressionTrackers=[];let i=[];e.imptrackers&&n.impressionTrackers.push(...e.imptrackers);for(const t of e?.eventtrackers||[])t.event===h.impression&&t.method===m.img&&n.impressionTrackers.push(t.url),t.event===h.impression&&t.method===m.js&&i.push(t.url);return i=i.map((e=>`<script async src="${e}"><\/script>`)),e?.jstracker&&i.push(e.jstracker),i.length&&(n.javascriptTrackers=i.join("\n")),n}function W(e){var t={};for(var n in e)t[e[n]]=n;return t}},1e3:(e,t,n)=>{n.d(t,{Cf:()=>a,S3:()=>i,Tb:()=>o,WR:()=>s,e4:()=>c,pS:()=>u,qN:()=>d,yB:()=>g,zt:()=>r});const r=["request","imp","bidResponse","response"],[i,o,s,a]=r,[d,c]=["default","pbs"],l=new Set(r);const{registerOrtbProcessor:u,getProcessors:g}=function(){const e={};return{registerOrtbProcessor(t){let{type:n,name:i,fn:o,priority:s=0,dialects:a=[d]}=t;if(!l.has(n))throw new Error(`ORTB processor type must be one of: ${r.join(", ")}`);a.forEach((t=>{e.hasOwnProperty(t)||(e[t]={}),e[t].hasOwnProperty(n)||(e[t][n]={}),e[t][n][i]={priority:s,fn:o}}))},getProcessors:t=>e[t]||{}}}()},15901:(e,t,n)=>{function r(e,t,n){return e&&e.includes(t,n)||!1}function i(){return Array.from.apply(Array,arguments)}function o(e,t,n){return e&&e.find(t,n)}function s(e,t,n){return e&&e.findIndex(t,n)}n.d(t,{A6:()=>i,I6:()=>o,SL:()=>s,mK:()=>r})},27718:(e,t,n)=>{n.d(t,{WH:()=>Y,Z:()=>X,gH:()=>Z});var r=n(7873),i=n(91069),o=n(70433),s=n(63172),a=n(12449),d=n(78969),c=n(15901),l=n(29075),u=n(46031);const{REQUEST:g,RESPONSE:f,NATIVE:p,EVENT:m}=d.nl,h={[g]:function(e,t,n){(0,l.bw)({renderFn(t){e(Object.assign({message:f,renderer:(0,u.k)(n)},t))},resizeFn:y(t.adId,n),options:t.options,adId:t.adId,bidResponse:n})},[m]:function(e,t,n){if(null==n)return void(0,i.logError)(`Cannot find ad '${t.adId}' for x-origin event request`);if(n.status!==d.tl.RENDERED)return void(0,i.logWarn)(`Received x-origin event request without corresponding render request for ad '${n.adId}'`);return(0,l.Uc)(t,n)}};function b(){window.addEventListener("message",(function(e){!function(e){var t=e.message?"message":"data",n={};try{n=JSON.parse(e[t])}catch(e){return}if(n&&n.adId&&n.message&&h.hasOwnProperty(n.message))(0,l.$A)(n.adId,n.message===d.nl.REQUEST).then((t=>{var r,o;h[n.message]((r=n.adId,o=function(e){return null==e.origin&&0===e.ports.length?function(){const e="Cannot post message to a frame with null origin. Please update creatives to use MessageChannel, see https://github.com/prebid/Prebid.js/issues/7870";throw(0,i.logError)(e),new Error(e)}:e.ports.length>0?function(t){e.ports[0].postMessage(JSON.stringify(t))}:function(t){e.source.postMessage(JSON.stringify(t),e.origin)}}(e),function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return o(Object.assign({},e,{adId:r}),...n)}),n,t)}))}(e)}),!1)}function y(e,t){return function(n,r){!function(e){let{adId:t,adUnitCode:n,width:r,height:o}=e;function s(e){return e?e+"px":"100%"}function a(e){let r=d(t,n),i=document.getElementById(r);return i&&i.querySelector(e)}function d(e,t){return(0,i.isGptPubadsDefined)()?l(e):(0,i.isApnGetTagDefined)()?u(t):t}function l(e){const t=(0,c.I6)(window.googletag.pubads().getSlots(),(t=>(0,c.I6)(t.getTargetingKeys(),(n=>(0,c.mK)(t.getTargeting(n),e)))));return t?t.getSlotElementId():null}function u(e){let t=window.apntag.getTag(e);return t&&t.targetId}["div","iframe"].forEach((e=>{let t=a(e+':not([style*="display: none"])');if(t){let e=t.style;e.width=s(r),e.height=s(o)}else(0,i.logError)(`Unable to locate matching page element for adUnitCode ${n}.  Can't resize it to ad's dimensions.  Please review setup.`)}))}({...t,width:n,height:r,adId:e})}}Object.assign(h,{[p]:function(e,t,n){if(null==n)return void(0,i.logError)(`Cannot find ad for x-origin event request: '${t.adId}'`);switch(t.action){case"assetRequest":(0,l.Hh)(n,(()=>e((0,a.IX)(t,n))));break;case"allAssetRequest":(0,l.Hh)(n,(()=>e((0,a.yl)(t,n))));break;default:(0,l.vW)(t,n,{resizeFn:y(t.adId,n)}),(0,l.Pk)(n)}}});var v=n(38230),E=n(43272),A=n(67314),I=n(97779),T=n(16833),w=n(49164),C=n(93597),S=n(12938),O=n(11445),B=n(75023),R=n(16894),k=n(25555),D=n(70068),U=n(16916),j=n(12713),_=n(63895);const $=new Map([["format",e=>Array.isArray(e)&&e.length>0&&e.every((e=>"object"==typeof e))],["w",i.isInteger],["h",i.isInteger],["btype",i.isArrayOfNums],["battr",i.isArrayOfNums],["pos",i.isInteger],["mimes",e=>Array.isArray(e)&&e.length>0&&e.every((e=>"string"==typeof e))],["topframe",e=>[1,0].includes(e)],["expdir",i.isArrayOfNums],["api",i.isArrayOfNums],["id",i.isStr],["vcm",e=>[1,0].includes(e)]]);var N=n(71371);const x=(0,r.m)(),{triggerUserSyncs:q}=v.zt,{ADD_AD_UNITS:W,REQUEST_BIDS:P,SET_TARGETING:M}=d.qY,G={bidWon:function(e){if(!A.n.getBidsRequested().map((e=>e.bids.map((e=>e.adUnitCode)))).reduce(i.flatten).filter(i.uniques).includes(e))return void(0,i.logError)('The "'+e+'" placement is not defined.');return!0}};function F(e,t){let n=[];return(0,i.isArray)(e)&&(t?e.length===t:e.length>0)&&(e.every((e=>(0,i.isArrayOfNums)(e,2)))?n=e:(0,i.isArrayOfNums)(e,2)&&n.push(e)),n}function L(e,t){const n=(0,o.A)(e,`ortb2Imp.${t}`),r=(0,o.A)(e,`mediaTypes.${t}`);if(!n&&!r)return;const a={[N.G_]:_.Zy,[N.D4]:$}[t];a&&[...a].forEach((n=>{let[r,a]=n;const d=(0,o.A)(e,`mediaTypes.${t}.${r}`),c=(0,o.A)(e,`ortb2Imp.${t}.${r}`);null==d&&null==c||(null==d?(0,s.J)(e,`mediaTypes.${t}.${r}`,c):null==c?(0,s.J)(e,`ortb2Imp.${t}.${r}`,d):((0,i.logWarn)(`adUnit ${e.code}: specifies conflicting ortb2Imp.${t}.${r} and mediaTypes.${t}.${r}, the latter will be ignored`,e),(0,s.J)(e,`mediaTypes.${t}.${r}`,c)))}))}function z(e){const t=(0,i.deepClone)(e),n=t.mediaTypes.banner,r=F(n.sizes);return r.length>0?(n.sizes=r,t.sizes=r):((0,i.logError)("Detected a mediaTypes.banner object without a proper sizes field.  Please ensure the sizes are listed like: [[300, 250], ...].  Removing invalid mediaTypes.banner object from request."),delete t.mediaTypes.banner),L(t,"banner"),t}function H(e){const t=(0,i.deepClone)(e),n=t.mediaTypes.video;if(n.playerSize){let e="number"==typeof n.playerSize[0]?2:1;const r=F(n.playerSize,e);r.length>0?(2===e&&(0,i.logInfo)("Transforming video.playerSize from [640,480] to [[640,480]] so it's in the proper format."),n.playerSize=r,t.sizes=r):((0,i.logError)("Detected incorrect configuration of mediaTypes.video.playerSize.  Please specify only one set of dimensions in a format like: [[640, 480]]. Removing invalid mediaTypes.video.playerSize property from request."),delete t.mediaTypes.video.playerSize)}return(0,_.aP)(t),L(t,"video"),t}function V(e){function t(t){return(0,i.logError)(`Error in adUnit "${e.code}": ${t}. Removing native request from ad unit`,e),delete r.mediaTypes.native,r}function n(e){for(const t of["sendTargetingKeys","types"])if(o.hasOwnProperty(t)){const n=e(t);if(n)return n}}const r=(0,i.deepClone)(e),o=r.mediaTypes.native;if(o.ortb){if(o.ortb.assets?.some((e=>!(0,i.isNumber)(e.id)||e.id<0||e.id%1!=0)))return t("native asset ID must be a nonnegative integer");if(n((e=>t(`ORTB native requests cannot specify "${e}"`))))return r;const e=Object.keys(d.x5).filter((e=>d.x5[e].includes("hb_native_"))),s=Object.keys(o).filter((t=>e.includes(t)));s.length>0&&((0,i.logError)(`when using native OpenRTB format, you cannot use legacy native properties. Deleting ${s} keys from request.`),s.forEach((e=>delete r.mediaTypes.native[e])))}else n((e=>`mediaTypes.native.${e} is deprecated, consider using native ORTB instead`));return o.image&&o.image.sizes&&!Array.isArray(o.image.sizes)&&((0,i.logError)("Please use an array of sizes for native.image.sizes field.  Removing invalid mediaTypes.native.image.sizes property from request."),delete r.mediaTypes.native.image.sizes),o.image&&o.image.aspect_ratios&&!Array.isArray(o.image.aspect_ratios)&&((0,i.logError)("Please use an array of sizes for native.image.aspect_ratios field.  Removing invalid mediaTypes.native.image.aspect_ratios property from request."),delete r.mediaTypes.native.image.aspect_ratios),o.icon&&o.icon.sizes&&!Array.isArray(o.icon.sizes)&&((0,i.logError)("Please use an array of sizes for native.icon.sizes field.  Removing invalid mediaTypes.native.icon.sizes property from request."),delete r.mediaTypes.native.icon.sizes),r}function K(e,t){let n=e?.mediaTypes?.[t]?.pos;if(!(0,i.isNumber)(n)||isNaN(n)||!isFinite(n)){let n=`Value of property 'pos' on ad unit ${e.code} should be of type: Number`;(0,i.logWarn)(n),delete e.mediaTypes[t].pos}return e}function J(e){const t=t=>`adUnit.code '${e.code}' ${t}`,n=e.mediaTypes,r=e.bids;return null==r||(0,i.isArray)(r)?null==r&&null==e.ortb2Imp?((0,i.logError)(t("has no 'adUnit.bids' and no 'adUnit.ortb2Imp'. Removing adUnit from auction")),null):n&&0!==Object.keys(n).length?(null==e.ortb2Imp||null!=r&&0!==r.length||(e.bids=[{bidder:null}],(0,i.logMessage)(t("defines 'adUnit.ortb2Imp' with no 'adUnit.bids'; it will be seen only by S2S adapters"))),e):((0,i.logError)(t("does not define a 'mediaTypes' object.  This is a required field for the auction, so this adUnit has been removed.")),null):((0,i.logError)(t("defines 'adUnit.bids' that is not an array. Removing adUnit from auction")),null)}(0,w.L6)(),x.bidderSettings=x.bidderSettings||{},x.libLoaded=!0,x.version="v9.28.0-pre",(0,i.logInfo)("Prebid.js v9.28.0-pre loaded"),x.installedModules=x.installedModules||[],x.adUnits=x.adUnits||[],x.triggerUserSyncs=q;const Y={validateAdUnit:J,validateBannerMediaType:z,validateSizes:F};Object.assign(Y,{validateNativeMediaType:V}),Object.assign(Y,{validateVideoMediaType:H});const X=(0,T.A_)("sync",(function(e){const t=[];return e.forEach((e=>{if(null==(e=J(e)))return;const n=e.mediaTypes;let r,i,o;n.banner&&(r=z(e),n.banner.hasOwnProperty("pos")&&(r=K(r,"banner"))),n.video&&(i=H(r||e),n.video.hasOwnProperty("pos")&&(i=K(i,"video"))),n.native&&(o=V(i||(r||e)));const s=Object.assign({},r,i,o);t.push(s)})),t}),"checkAdUnitSetup");function Q(e){const t=A.n[e]().filter((e=>A.n.getAdUnitCodes().includes(e.adUnitCode))),n=A.n.getLastAuctionId();return t.map((e=>e.adUnitCode)).filter(i.uniques).map((e=>t.filter((t=>t.auctionId===n&&t.adUnitCode===e)))).filter((e=>e&&e[0]&&e[0].adUnitCode)).map((e=>({[e[0].adUnitCode]:{bids:e}}))).reduce(((e,t)=>Object.assign(e,t)),{})}x.getAdserverTargetingForAdUnitCodeStr=function(e){if((0,i.logInfo)("Invoking pbjs.getAdserverTargetingForAdUnitCodeStr",arguments),e){var t=x.getAdserverTargetingForAdUnitCode(e);return(0,i.transformAdServerTargetingObj)(t)}(0,i.logMessage)("Need to call getAdserverTargetingForAdUnitCodeStr with adunitCode")},x.getHighestUnusedBidResponseForAdUnitCode=function(e){if(e){const t=A.n.getAllBidsForAdUnitCode(e).filter(I.Yl);return t.length?t.reduce(j.Vk):{}}(0,i.logMessage)("Need to call getHighestUnusedBidResponseForAdUnitCode with adunitCode")},x.getAdserverTargetingForAdUnitCode=function(e){return x.getAdserverTargeting(e)[e]},x.getAdserverTargeting=function(e){return(0,i.logInfo)("Invoking pbjs.getAdserverTargeting",arguments),I.iS.getAllTargeting(e)},x.getConsentMetadata=function(){return(0,i.logInfo)("Invoking pbjs.getConsentMetadata"),U.SL.getConsentMeta()},x.getNoBids=function(){return(0,i.logInfo)("Invoking pbjs.getNoBids",arguments),Q("getNoBids")},x.getNoBidsForAdUnitCode=function(e){return{bids:A.n.getNoBids().filter((t=>t.adUnitCode===e))}},x.getBidResponses=function(){return(0,i.logInfo)("Invoking pbjs.getBidResponses",arguments),Q("getBidsReceived")},x.getBidResponsesForAdUnitCode=function(e){return{bids:A.n.getBidsReceived().filter((t=>t.adUnitCode===e))}},x.setTargetingForGPTAsync=function(e,t){(0,i.logInfo)("Invoking pbjs.setTargetingForGPTAsync",arguments),(0,i.isGptPubadsDefined)()?I.iS.setTargetingForGPT(e,t):(0,i.logError)("window.googletag is not defined on the page")},x.setTargetingForAst=function(e){(0,i.logInfo)("Invoking pbjs.setTargetingForAn",arguments),I.iS.isApntagDefined()?(I.iS.setTargetingForAst(e),B.emit(M,I.iS.getAllTargeting())):(0,i.logError)("window.apntag is not defined on the page")},x.renderAd=(0,T.A_)("async",(function(e,t,n){(0,i.logInfo)("Invoking pbjs.renderAd",arguments),(0,i.logMessage)("Calling renderAd with adId :"+t),(0,l.BS)(e,t,n)})),x.removeAdUnit=function(e){if((0,i.logInfo)("Invoking pbjs.removeAdUnit",arguments),!e)return void(x.adUnits=[]);let t;t=(0,i.isArray)(e)?e:[e],t.forEach((e=>{for(let t=x.adUnits.length-1;t>=0;t--)x.adUnits[t].code===e&&x.adUnits.splice(t,1)}))},x.requestBids=function(){const e=(0,T.A_)("async",(function(){let{bidsBackHandler:e,timeout:t,adUnits:n,adUnitCodes:r,labels:o,auctionId:s,ttlBuffer:a,ortb2:d,metrics:l,defer:u}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};B.emit(P);const g=t||E.$W.getConfig("bidderTimeout");(0,i.logInfo)("Invoking pbjs.requestBids",arguments),null==r||Array.isArray(r)||(r=[r]),r&&r.length?n=n.filter((e=>(0,c.mK)(r,e.code))):r=n&&n.map((e=>e.code)),r=r.filter(i.uniques);const f={global:(0,i.mergeDeep)({},E.$W.getAnyConfig("ortb2")||{},d||{}),bidder:Object.fromEntries(Object.entries(E.$W.getBidderConfig()).map((e=>{let[t,n]=e;return[t,(0,i.deepClone)(n.ortb2)]})).filter((e=>{let[t,n]=e;return null!=n})))};return(0,D.w)(k.k.resolve(f.global)).then((t=>(f.global=t,Z({bidsBackHandler:e,timeout:g,adUnits:n,adUnitCodes:r,labels:o,auctionId:s,ttlBuffer:a,ortb2Fragments:f,metrics:l,defer:u}))))}),"requestBids");return(0,T.Y6)(e,(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.adUnits||x.adUnits;return t.adUnits=(0,i.isArray)(n)?n.slice():[n],t.metrics=(0,R.K7)(),t.metrics.checkpoint("requestBids"),t.defer=(0,k.v)({promiseFactory:e=>new Promise(e)}),e.call(this,t),t.defer.promise}))}();const Z=(0,T.A_)("async",(function(){let{bidsBackHandler:e,timeout:t,adUnits:n,ttlBuffer:r,adUnitCodes:o,labels:a,auctionId:d,ortb2Fragments:l,metrics:u,defer:g}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const f=(0,O.pX)(E.$W.getConfig("s2sConfig")||[]);function p(t,n,r){if("function"==typeof e)try{e(t,n,r)}catch(e){(0,i.logError)("Error executing bidsBackHandler",null,e)}g.resolve({bids:t,timedOut:n,auctionId:r})}!function(e){e.forEach((e=>(0,_.V0)(e)))}(n),n=(0,R.BO)(u).measureTime("requestBids.validate",(()=>X(n)));const m={};if(n.forEach((e=>{const t=Object.keys(e.mediaTypes||{banner:"banner"}),n=e.bids.map((e=>e.bidder)),o=O.Ay.bidderRegistry,s=n.filter((e=>!f.has(e)));e.adUnitId=(0,i.generateUUID)();const a=e.ortb2Imp?.ext?.tid;a&&(m.hasOwnProperty(e.code)?(0,i.logWarn)(`Multiple distinct ortb2Imp.ext.tid were provided for twin ad units '${e.code}'`):m[e.code]=a),null==r||e.hasOwnProperty("ttlBuffer")||(e.ttlBuffer=r),s.forEach((n=>{const r=o[n],s=r&&r.getSpec&&r.getSpec(),a=s&&s.supportedMediaTypes||["banner"];t.some((e=>(0,c.mK)(a,e)))||((0,i.logWarn)((0,i.unsupportedBidderMessage)(e,n)),e.bids=e.bids.filter((e=>e.bidder!==n)))}))})),n&&0!==n.length){n.forEach((e=>{const t=e.ortb2Imp?.ext?.tid||m[e.code]||(0,i.generateUUID)();m.hasOwnProperty(e.code)||(m[e.code]=t),e.transactionId=t,(0,s.J)(e,"ortb2Imp.ext.tid",t)}));const e=A.n.createAuction({adUnits:n,adUnitCodes:o,callback:p,cbTimeout:t,labels:a,auctionId:d,ortb2Fragments:l,metrics:u});let r=n.length;r>15&&(0,i.logInfo)(`Current auction ${e.getAuctionId()} contains ${r} adUnits.`,n),o.forEach((t=>I.iS.setLatestAuctionForAdUnit(t,e.getAuctionId()))),e.callBids()}else(0,i.logMessage)("No adUnits configured. No bids requested."),p()}),"startAuction");x.requestBids.before((function(e,t){function n(e){for(var t;t=e.shift();)t()}n(S.s0),n(ee),e.call(this,t)}),49),x.addAdUnits=function(e){(0,i.logInfo)("Invoking pbjs.addAdUnits",arguments),x.adUnits.push.apply(x.adUnits,(0,i.isArray)(e)?e:[e]),B.emit(W)},x.onEvent=function(e,t,n){(0,i.logInfo)("Invoking pbjs.onEvent",arguments),(0,i.isFn)(t)?!n||G[e].call(null,n)?B.on(e,t,n):(0,i.logError)('The id provided is not valid for event "'+e+'" and no handler was set.'):(0,i.logError)('The event handler provided is not a function and was not set on event "'+e+'".')},x.offEvent=function(e,t,n){(0,i.logInfo)("Invoking pbjs.offEvent",arguments),n&&!G[e].call(null,n)||B.off(e,t,n)},x.getEvents=function(){return(0,i.logInfo)("Invoking pbjs.getEvents"),B.getEvents()},x.registerBidAdapter=function(e,t){(0,i.logInfo)("Invoking pbjs.registerBidAdapter",arguments);try{O.Ay.registerBidAdapter(e(),t)}catch(e){(0,i.logError)("Error registering bidder adapter : "+e.message)}},x.registerAnalyticsAdapter=function(e){(0,i.logInfo)("Invoking pbjs.registerAnalyticsAdapter",arguments);try{O.Ay.registerAnalyticsAdapter(e)}catch(e){(0,i.logError)("Error registering analytics adapter : "+e.message)}},x.createBid=function(e){return(0,i.logInfo)("Invoking pbjs.createBid",arguments),(0,C.O)(e)};const ee=[],te=(0,T.A_)("async",(function(e){e&&!(0,i.isEmpty)(e)?((0,i.logInfo)("Invoking pbjs.enableAnalytics for: ",e),O.Ay.enableAnalytics(e)):(0,i.logError)("pbjs.enableAnalytics should be called with option {}")}),"enableAnalyticsCb");function ne(e){if("function"==typeof e)try{e.call()}catch(e){(0,i.logError)("Error processing command :",e.message,e.stack)}else(0,i.logError)("Commands written into pbjs.cmd.push must be wrapped in a function")}function re(e){e.forEach((function(e){if(void 0===e.called)try{e.call(),e.called=!0}catch(e){(0,i.logError)("Error processing command :","prebid.js",e)}}))}x.enableAnalytics=function(e){ee.push(te.bind(this,e))},x.aliasBidder=function(e,t,n){(0,i.logInfo)("Invoking pbjs.aliasBidder",arguments),e&&t?O.Ay.aliasBidAdapter(e,t,n):(0,i.logError)("bidderCode and alias must be passed as arguments","pbjs.aliasBidder")},x.aliasRegistry=O.Ay.aliasRegistry,E.$W.getConfig("aliasRegistry",(e=>{"private"===e.aliasRegistry&&delete x.aliasRegistry})),x.getAllWinningBids=function(){return A.n.getAllWinningBids()},x.getAllPrebidWinningBids=function(){return A.n.getBidsReceived().filter((e=>e.status===d.tl.BID_TARGETING_SET))},x.getHighestCpmBids=function(e){return I.iS.getWinningBids(e)},x.clearAllAuctions=function(){A.n.clearAllAuctions()},x.markWinningBidAsUsed=function(e){let t,{adId:n,adUnitCode:r,analytics:o=!1}=e;r&&null==n?t=I.iS.getWinningBids(r):n?t=A.n.getBidsReceived().filter((e=>e.adId===n)):(0,i.logWarn)("Improper use of markWinningBidAsUsed. It needs an adUnitCode or an adId to function."),t.length>0&&(o?(0,l.n6)(t[0]):A.n.addWinningBid(t[0]),(0,l.qn)(t[0]))},x.getConfig=E.$W.getAnyConfig,x.readConfig=E.$W.readAnyConfig,x.mergeConfig=E.$W.mergeConfig,x.mergeBidderConfig=E.$W.mergeBidderConfig,x.setConfig=E.$W.setConfig,x.setBidderConfig=E.$W.setBidderConfig,x.que.push((()=>b())),x.processQueue=function(){x.que.push=x.cmd.push=ne,(0,l.XO)(),T.A_.ready(),re(x.que),re(x.cmd)},x.triggerBilling=e=>{let{adId:t,adUnitCode:n}=e;A.n.getAllWinningBids().filter((e=>e.adId===t||null==t&&e.adUnitCode===n)).forEach((e=>{O.Ay.triggerBilling(e),(0,l.vB)(e)}))}},7873:(e,t,n)=>{n.d(t,{E:()=>s,m:()=>o});const r=window,i=r.pbjs=r.pbjs||{};function o(){return i}function s(e){i.installedModules.push(e)}i.cmd=i.cmd||[],i.que=i.que||[],r===window&&(r._pbjsGlobals=r._pbjsGlobals||[],r._pbjsGlobals.push("pbjs"))},27934:(e,t,n)=>{n.d(t,{EN:()=>d,gR:()=>s});var r=n(43272),i=n(91069);function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;if(!e)return e;if(/\w+:\/\//.exec(e))return e;let n=t.location.protocol;try{n=t.top.location.protocol}catch(e){}return/^\/\//.exec(e)?n+e:`${n}//${e}`}function s(e){let{noLeadingWww:t=!1,noPort:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{e=new URL(o(e))}catch(e){return}return e=n?e.hostname:e.host,t&&e.startsWith("www.")&&(e=e.substring(4)),e}function a(e){try{const t=e.querySelector("link[rel='canonical']");if(null!==t)return t.href}catch(e){}return null}const d=function(e){let t,n,r,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;return i.top!==i?e:function(){const o=a(i.document),s=i.location.href;return t===o&&s===n||(t=o,n=s,r=e()),r}}((c=window,function(){const e=[],t=function(e){try{if(!e.location.ancestorOrigins)return;return e.location.ancestorOrigins}catch(e){}}(c),n=r.$W.getConfig("maxNestedIframes");let d,l,u,g,f=!1,p=0,m=!1,h=!1,b=!1;do{const n=d,r=h;let o,s=!1,g=null;h=!1,d=d?d.parent:c;try{o=d.location.href||null}catch(e){s=!0}if(s)if(r){const e=n.context;try{g=e.sourceUrl,l=g,b=!0,m=!0,d===c.top&&(f=!0),e.canonicalUrl&&(u=e.canonicalUrl)}catch(e){}}else{(0,i.logWarn)("Trying to access cross domain iframe. Continuing without referrer and location");try{const e=n.document.referrer;e&&(g=e,d===c.top&&(f=!0))}catch(e){}!g&&t&&t[p-1]&&(g=t[p-1],d===c.top&&(b=!0)),g&&!m&&(l=g)}else{if(o&&(g=o,l=g,m=!1,d===c.top)){f=!0;const e=a(d.document);e&&(u=e)}d.context&&d.context.sourceUrl&&(h=!0)}e.push(g),p++}while(d!==c.top&&p<n);e.reverse();try{g=c.top.document.referrer}catch(e){}const y=f||b?l:null,v=r.$W.getConfig("pageUrl")||u||null;let E=r.$W.getConfig("pageUrl")||y||o(v,c);return y&&y.indexOf("?")>-1&&-1===E.indexOf("?")&&(E=`${E}${y.substring(y.indexOf("?"))}`),{reachedTop:f,isAmp:m,numIframes:p-1,stack:e,topmostLocation:l||null,location:y,canonicalUrl:v,page:E,domain:s(E)||null,ref:g||null,legacy:{reachedTop:f,isAmp:m,numIframes:p-1,stack:e,referer:l||null,canonicalUrl:v}}}));var c},12938:(e,t,n)=>{n.d(t,{CK:()=>b,X0:()=>f,qk:()=>g,s0:()=>p,vM:()=>h});var r=n(91069),i=n(12693),o=n(45569),s=n(95139),a=n(2604),d=n(76811),c=n(43272),l=n(11445),u=n(83441);const g="html5",f="cookie";let p=[];function m(){let{moduleName:e,moduleType:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{isAllowed:n=s.io}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};function i(r,i){let s=e;const g=c.$W.getCurrentBidder();g&&t===o.tW&&l.Ay.aliasRegistry[g]===e&&(s=g);return r({valid:n(d.Ue,(0,u.s)(t,s,{[a.Zw]:i}))})}function m(e,t,n){if(!n||"function"!=typeof n)return i(e,t);p.push((function(){let r=i(e,t);n(r)}))}function h(e){const t=e.charAt(0).toUpperCase()+e.substring(1),n=()=>window[e],i=function(t){return m((function(t){if(t&&t.valid)try{return!!n()}catch(t){(0,r.logError)(`${e} api disabled`)}return!1}),g,t)};return{[`has${t}`]:i,[`${e}IsEnabled`]:e=>m((function(e){if(e&&e.valid)try{return n().setItem("prebid.cookieTest","1"),"1"===n().getItem("prebid.cookieTest")}catch(e){}finally{try{n().removeItem("prebid.cookieTest")}catch(e){}}return!1}),g,e),[`setDataIn${t}`]:(e,t,r)=>m((function(r){r&&r.valid&&i()&&n().setItem(e,t)}),g,r),[`getDataFrom${t}`]:(e,t)=>m((function(t){return t&&t.valid&&i()?n().getItem(e):null}),g,t),[`removeDataFrom${t}`]:(e,t)=>m((function(t){t&&t.valid&&i()&&n().removeItem(e)}),g,t)}}return{setCookie:function(e,t,n,r,i,o){return m((function(o){if(o&&o.valid){const o=i&&""!==i?` ;domain=${encodeURIComponent(i)}`:"",s=n&&""!==n?` ;expires=${n}`:"",a=null!=r&&"none"==r.toLowerCase()?"; Secure":"";document.cookie=`${e}=${encodeURIComponent(t)}${s}; path=/${o}${r?`; SameSite=${r}`:""}${a}`}}),f,o)},getCookie:function(e,t){return m((function(t){if(t&&t.valid){let t=window.document.cookie.match("(^|;)\\s*"+e+"\\s*=\\s*([^;]*)\\s*(;|$)");return t?decodeURIComponent(t[2]):null}return null}),f,t)},cookiesAreEnabled:function(e){return m((function(e){return!(!e||!e.valid)&&(0,r.checkCookieSupport)()}),f,e)},...h("localStorage"),...h("sessionStorage"),findSimilarCookies:function(e,t){return m((function(t){if(t&&t.valid){const t=[];if((0,r.hasDeviceAccess)()){const n=document.cookie.split(";");for(;n.length;){const r=n.pop();let i=r.indexOf("=");i=i<0?r.length:i;decodeURIComponent(r.slice(0,i).replace(/^\s+/,"")).indexOf(e)>=0&&t.push(decodeURIComponent(r.slice(i+1)))}}return t}}),f,t)}}}function h(){let{moduleType:e,moduleName:t,bidderCode:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};function r(){throw new Error("Invalid invocation for getStorageManager: must set either bidderCode, or moduleType + moduleName")}return n?((e&&e!==o.tW||t)&&r(),e=o.tW,t=n):t&&e||r(),m({moduleType:e,moduleName:t})}function b(e){return m({moduleName:e,moduleType:o.tp})}(0,s.qB)(d.Ue,"deviceAccess config",(function(){if(!(0,r.hasDeviceAccess)())return{allow:!1}})),(0,s.qB)(d.Ue,"bidderSettings.*.storageAllowed",(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i.u;if(e[a.Dk]!==o.tW)return;let n=t.get(e[a.q7],"storageAllowed");if(n&&!0!==n){const t=e[a.Zw];n=Array.isArray(n)?n.some((e=>e===t)):n===t}else n=!!n;return n?void 0:{allow:n}}))},97779:(e,t,n)=>{n.d(t,{Jp:()=>C,ME:()=>w,Yl:()=>T,iS:()=>O,m2:()=>S,uW:()=>I});var r=n(67314),i=n(27863),o=n(12693),s=n(43272),a=n(78969),d=n(75023),c=n(16833),l=n(71371),u=n(12449),g=n(15901),f=n(91069),p=n(70433),m=n(12713),h=[];const b=20,y="targetingControls.allowTargetingKeys",v="targetingControls.addTargetingKeys",E=`Only one of "${y}" or "${v}" can be set`,A=Object.keys(a.xS).map((e=>a.xS[e]));let I={isActualBid:e=>e.getStatusCode()===a.XQ.GOOD,isBidNotExpired:e=>e.responseTimestamp+1e3*(0,i.cT)(e)>(0,f.timestamp)(),isUnusedBid:e=>e&&(e.status&&!(0,g.mK)([a.tl.RENDERED],e.status)||!e.status)};function T(e){return!Object.values(I).some((t=>!t(e)))}const w=(0,c.A_)("sync",(function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:f.sortByHighestCpm;if(!r){const r=[],o=s.$W.getConfig("sendBidsControl.dealPrioritization");let a=(0,f.groupBy)(e,"adUnitCode");return Object.keys(a).forEach((e=>{let s=[],d=(0,f.groupBy)(a[e],"bidderCode");Object.keys(d).forEach((e=>{s.push(d[e].reduce(t))})),n?(s=o?s.sort(C(!0)):s.sort(((e,t)=>t.cpm-e.cpm)),r.push(...s.slice(0,n))):(s=s.sort(i),r.push(...s))})),r}return e}));function C(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return function(t,n){return void 0!==t.adserverTargeting.hb_deal&&void 0===n.adserverTargeting.hb_deal?-1:void 0===t.adserverTargeting.hb_deal&&void 0!==n.adserverTargeting.hb_deal?1:e?n.cpm-t.cpm:n.adserverTargeting.hb_pb-t.adserverTargeting.hb_pb}}function S(e,t){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>window.googletag.pubads().getSlots())().reduce(((e,n)=>{const r=(0,f.isFn)(t)&&t(n);return Object.keys(e).filter((0,f.isFn)(r)?r:(0,f.isAdUnitCodeMatchingSlot)(n)).forEach((t=>e[t].push(n))),e}),Object.fromEntries(e.map((e=>[e,[]]))))}const O=function(e){let t={},n={};function r(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=A.concat(u.Nh),i=s.$W.getConfig("targetingControls.allowSendAllBidsTargetingKeys"),o=i?i.map((e=>a.xS[e])):r;return e.reduce(((e,i)=>{if(t||n&&i.dealId){const t=function(e,t){return t.reduce(((t,n)=>(e.adserverTargeting[n]&&t.push({[`${n}_${e.bidderCode}`.substring(0,20)]:[e.adserverTargeting[n]]}),t)),[])}(i,r.filter((e=>void 0!==i.adserverTargeting[e]&&(n||-1!==o.indexOf(e)))));t&&e.push({[i.adUnitCode]:t})}return e}),[])}function i(t){return"string"==typeof t?[t]:(0,f.isArray)(t)?t:e.getAdUnitCodes()||[]}function I(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m.Bq,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,i=e.getBidsReceived().reduce(((e,t)=>{const r=s.$W.getConfig("useBidCache"),i=s.$W.getConfig("bidCacheFilterFunction"),o=n[t.adUnitCode]===t.auctionId,a=!(r&&!o&&"function"==typeof i)||!!i(t);return(r||o)&&a&&(0,p.A)(t,"video.context")!==l.LM&&T(t)&&(t.latestTargetedAuctionId=n[t.adUnitCode],e.push(t)),e}),[]);return w(i,t,void 0,void 0,void 0,r)}function O(){return e.getStandardBidderAdServerTargeting().map((e=>e.key)).concat(A).filter(f.uniques)}return t.setLatestAuctionForAdUnit=function(e,t){n[e]=t},t.resetPresetTargeting=function(e,t){if((0,f.isGptPubadsDefined)()){const n=i(e);Object.values(S(n,t)).forEach((e=>{e.forEach((e=>{!function(e){h.forEach((t=>{e.getTargeting(t)&&e.clearTargeting(t)}))}(e)}))}))}},t.resetPresetTargetingAST=function(e){i(e).forEach((function(e){const t=window.apntag.getTag(e);if(t&&t.keywords){const n=Object.keys(t.keywords),r={};n.forEach((e=>{(0,g.mK)(h,e.toLowerCase())||(r[e]=t.keywords[e])})),window.apntag.modifyTag(e,{keywords:r})}}))},t.getAllTargeting=function(t,n,d){let c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:m.Vk,l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:f.sortByHighestCpm;d||=I(c,l);const A=i(t),T=s.$W.getConfig("enableSendAllBids"),S=s.$W.getConfig("sendBidsControl.bidLimit"),B=T&&(n||S)||0,{customKeysByUnit:R,filteredBids:k}=function(e,t){const n=[],r={},i=s.$W.getConfig("targetingControls.alwaysIncludeDeals");return t.forEach((t=>{const s=(0,g.mK)(e,t.adUnitCode),a=!0===o.u.get(t.bidderCode,"allowZeroCpmBids")?t.cpm>=0:t.cpm>0,d=i&&t.dealId;s&&(d||a)&&(n.push(t),Object.keys(t.adserverTargeting).filter(function(){let e=O();e=e.concat(u.Nh);return function(t){return-1===e.indexOf(t)}}()).forEach((e=>{const n=e.substring(0,b),i=r[t.adUnitCode]||{},o=[t.adserverTargeting[e]];i[n]?i[n]=i[n].concat(o).filter(f.uniques):i[n]=o,r[t.adUnitCode]=i})))})),{filteredBids:n,customKeysByUnit:r}}(A,d);let D=function(t,n){const i=function(e){let t=[],n=e.reduce(((e,n)=>((0,g.mK)(t,n.adUnitCode)||(e.push(n),t.push(n.adUnitCode)),e)),[]),r=O();return n=n.map((e=>({[e.adUnitCode]:Object.keys(e.adserverTargeting).filter((t=>void 0===e.sendStandardTargeting||e.sendStandardTargeting||-1===r.indexOf(t))).reduce(((t,n)=>{const r=[e.adserverTargeting[n]],i={[n.substring(0,b)]:r};if(n===a.xS.DEAL){const o=`${n}_${e.bidderCode}`.substring(0,b),s={[o]:r};return[...t,i,s]}return[...t,i]}),[])}))),n}(t).concat(function(e,t){return e.reduce(((e,n)=>{const r=Object.assign({},n),i=t[r.adUnitCode],o=[];return i&&Object.keys(i).forEach((e=>{e&&i[e]&&o.push({[e]:i[e]})})),e.push({[r.adUnitCode]:o}),e}),[])}(t,n)).concat(function(e){const t=s.$W.getConfig("targetingControls.alwaysIncludeDeals");return r(e,s.$W.getConfig("enableSendAllBids"),t)}(t)).concat(function(){function t(e){return(0,p.A)(e,a.iD.ADSERVER_TARGETING)}function n(e){const n=t(e);return Object.keys(n).map((function(e){return(0,f.isStr)(n[e])&&(n[e]=n[e].split(",").map((e=>e.trim()))),(0,f.isArray)(n[e])||(n[e]=[n[e]]),{[e]:n[e]}}))}return e.getAdUnits().filter((e=>t(e))).reduce(((e,t)=>{const r=n(t);return r&&e.push({[t.code]:r}),e}),[])}());return i.forEach((e=>{!function(e){Object.keys(e).forEach((t=>{e[t].forEach((e=>{const t=Object.keys(e);-1===h.indexOf(t[0])&&(h=t.concat(h))}))}))}(e)})),i}(w(k,c,B,void 0,l),R);const U=Object.keys(Object.assign({},a.Zh,a.x5));let j=s.$W.getConfig(y);const _=s.$W.getConfig(v);if(null!=_&&null!=j)throw new Error(E);j=null!=_?U.concat(_):j||U,Array.isArray(j)&&j.length>0&&(D=function(e,t){const n=Object.assign({},a.xS,a.x5),r=Object.keys(n),i={};(0,f.logInfo)(`allowTargetingKeys - allowed keys [ ${t.map((e=>n[e])).join(", ")} ]`),e.map((e=>{const o=Object.keys(e)[0],s=e[o].filter((e=>{const o=Object.keys(e)[0],s=0===r.filter((e=>0===o.indexOf(n[e]))).length||(0,g.I6)(t,(e=>{const t=n[e];return 0===o.indexOf(t)}));return i[o]=!s,s}));e[o]=s}));const o=Object.keys(i).filter((e=>i[e]));return(0,f.logInfo)(`allowTargetingKeys - removed keys [ ${o.join(", ")} ]`),e.filter((e=>e[Object.keys(e)[0]].length>0))}(D,j)),D=function(e){let t=e.map((e=>({[Object.keys(e)[0]]:e[Object.keys(e)[0]].map((e=>({[Object.keys(e)[0]]:e[Object.keys(e)[0]].join(",")}))).reduce(((e,t)=>Object.assign(t,e)),{})})));return t=t.reduce((function(e,t){var n=Object.keys(t)[0];return e[n]=Object.assign({},e[n],t[n]),e}),{}),t}(D);const $=s.$W.getConfig("targetingControls.auctionKeyMaxChars");return $&&((0,f.logInfo)(`Detected 'targetingControls.auctionKeyMaxChars' was active for this auction; set with a limit of ${$} characters.  Running checks on auction keys...`),D=function(e,t){let n=(0,f.deepClone)(e),r=Object.keys(n).map((e=>({adUnitCode:e,adserverTargeting:n[e]}))).sort(C());return r.reduce((function(e,r,i,o){let s=(a=r.adserverTargeting,Object.keys(a).reduce((function(e,t){return e+`${t}%3d${encodeURIComponent(a[t])}%26`}),""));var a;i+1===o.length&&(s=s.slice(0,-3));let d=r.adUnitCode,c=s.length;return c<=t?(t-=c,(0,f.logInfo)(`AdUnit '${d}' auction keys comprised of ${c} characters.  Deducted from running threshold; new limit is ${t}`,n[d]),e[d]=n[d]):(0,f.logWarn)(`The following keys for adUnitCode '${d}' exceeded the current limit of the 'auctionKeyMaxChars' setting.\nThe key-set size was ${c}, the current allotted amount was ${t}.\n`,n[d]),i+1===o.length&&0===Object.keys(e).length&&(0,f.logError)("No auction targeting keys were permitted due to the setting in setConfig(targetingControls.auctionKeyMaxChars).  Please review setup and consider adjusting."),e}),{})}(D,$)),A.forEach((e=>{D[e]||(D[e]={})})),D},s.$W.getConfig("targetingControls",(function(e){null!=(0,p.A)(e,y)&&null!=(0,p.A)(e,v)&&(0,f.logError)(E)})),t.setTargetingForGPT=(0,c.A_)("sync",(function(n,r){let i=t.getAllTargeting(n),o=Object.fromEntries(h.map((e=>[e,null])));Object.entries(S(Object.keys(i),r)).forEach((e=>{let[t,n]=e;n.forEach((e=>{Object.keys(i[t]).forEach((e=>{let n=i[t][e];"string"==typeof n&&-1!==n.indexOf(",")&&(n=n.split(",")),i[t][e]=n})),(0,f.logMessage)(`Attempting to set targeting-map for slot: ${e.getSlotElementId()} with targeting-map:`,i[t]),e.updateTargetingFromMap(Object.assign({},o,i[t]))}))})),Object.keys(i).forEach((t=>{Object.keys(i[t]).forEach((n=>{"hb_adid"===n&&e.setStatusForBids(i[t][n],a.tl.BID_TARGETING_SET)}))})),t.targetingDone(i),d.emit(a.qY.SET_TARGETING,i)}),"setTargetingForGPT"),t.targetingDone=(0,c.A_)("sync",(function(e){return e}),"targetingDone"),t.getWinningBids=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:m.Vk,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:f.sortByHighestCpm;const a=[],d=t||I(n,r),c=i(e);return d.reduce(((e,t)=>{const n=t.adUnitCode,r=!0===o.u.get(n,"allowZeroCpmBids")?t.cpm>=0:t.cpm>0,i=s.$W.getConfig("targetingControls.alwaysIncludeDeals")&&t.dealId;return(0,g.mK)(c,n)&&!(0,g.mK)(a,n)&&(i||r)&&(e.push(t),a.push(n)),e}),[])},t.setTargetingForAst=function(e){let n=t.getAllTargeting(e);try{t.resetPresetTargetingAST(e)}catch(e){(0,f.logError)("unable to reset targeting for AST"+e)}Object.keys(n).forEach((e=>Object.keys(n[e]).forEach((t=>{if((0,f.logMessage)(`Attempting to set targeting for targetId: ${e} key: ${t} value: ${n[e][t]}`),(0,f.isStr)(n[e][t])||(0,f.isArray)(n[e][t])){let r={},i=/pt[0-9]/;t.search(i)<0?r[t.toUpperCase()]=n[e][t]:r[t]=n[e][t],window.apntag.setKeywords(e,r,{overrideKeyValue:!0})}}))))},t.isApntagDefined=function(){if(window.apntag&&(0,f.isFn)(window.apntag.setKeywords))return!0},t}(r.n)},38230:(e,t,n)=>{n.d(t,{qh:()=>g,zt:()=>p});var r=n(91069),i=n(43272),o=n(15901),s=n(12938),a=n(95139),d=n(76811),c=n(2604),l=n(45569),u=n(83441);const g={syncEnabled:!0,filterSettings:{image:{bidders:"*",filter:"include"}},syncsPerBidder:5,syncDelay:3e3,auctionDelay:500};i.$W.setDefaults({userSync:(0,r.deepClone)(g)});const f=(0,s.CK)("usersync");const p=function(e){let t={},n={image:[],iframe:[]},s=new Set,a={},g={image:!0,iframe:!1},f=e.config;function p(){if(f.syncEnabled&&e.browserSupportsCookies){try{!function(){if(!g.iframe)return;m(n.iframe,(e=>{let[t,i]=e;(0,r.logMessage)(`Invoking iframe user sync for bidder: ${t}`),(0,r.insertUserSyncIframe)(i),function(e,t){e.image=e.image.filter((e=>e[0]!==t))}(n,t)}))}(),function(){if(!g.image)return;m(n.image,(e=>{let[t,n]=e;(0,r.logMessage)(`Invoking image pixel user sync for bidder: ${t}`),(0,r.triggerPixel)(n)}))}()}catch(e){return(0,r.logError)("Error firing user syncs",e)}n={image:[],iframe:[]}}}function m(e,t){(0,r.shuffle)(e).forEach(t)}function h(e,t){let n=f.filterSettings;if(function(e,t){if(e.all&&e[t])return(0,r.logWarn)(`Detected presence of the "filterSettings.all" and "filterSettings.${t}" in userSync config.  You cannot mix "all" with "iframe/image" configs; they are mutually exclusive.`),!1;let n=e.all?e.all:e[t],i=e.all?"all":t;if(!n)return!1;let o=n.filter,s=n.bidders;if(o&&"include"!==o&&"exclude"!==o)return(0,r.logWarn)(`UserSync "filterSettings.${i}.filter" setting '${o}' is not a valid option; use either 'include' or 'exclude'.`),!1;if("*"!==s&&!(Array.isArray(s)&&s.length>0&&s.every((e=>(0,r.isStr)(e)&&"*"!==e))))return(0,r.logWarn)(`Detected an invalid setup in userSync "filterSettings.${i}.bidders"; use either '*' (to represent all bidders) or an array of bidders.`),!1;return!0}(n,e)){g[e]=!0;let r=n.all?n.all:n[e],i="*"===r.bidders?[t]:r.bidders;const s={include:(e,t)=>!(0,o.mK)(e,t),exclude:(e,t)=>(0,o.mK)(e,t)};return s[r.filter||"include"](i,t)}return!g[e]}return i.$W.getConfig("userSync",(e=>{if(e.userSync){let t=e.userSync.filterSettings;(0,r.isPlainObject)(t)&&(t.image||t.all||(e.userSync.filterSettings.image={bidders:"*",filter:"include"}))}f=Object.assign(f,e.userSync)})),e.regRule(d.Ml,"userSync config",(e=>{if(!f.syncEnabled)return{allow:!1,reason:"syncs are disabled"};if(e[c.Dk]===l.tW){const n=e[c.bt],r=e[c.iK];if(!t.canBidderRegisterSync(n,r))return{allow:!1,reason:`${n} syncs are not enabled for ${r}`}}})),t.registerSync=(t,i,o)=>s.has(i)?(0,r.logMessage)(`already fired syncs for "${i}", ignoring registerSync call`):f.syncEnabled&&(0,r.isArray)(n[t])?i?0!==f.syncsPerBidder&&Number(a[i])>=f.syncsPerBidder?(0,r.logWarn)(`Number of user syncs exceeded for "${i}"`):void(e.isAllowed(d.Ml,(0,u.s)(l.tW,i,{[c.bt]:t,[c.e3]:o}))&&(n[t].push([i,o]),a=function(e,t){return e[t]?e[t]+=1:e[t]=1,e}(a,i))):(0,r.logWarn)("Bidder is required for registering sync"):(0,r.logWarn)(`User sync type "${t}" not supported`),t.bidderDone=s.add.bind(s),t.syncUsers=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(e)return setTimeout(p,Number(e));p()},t.triggerUserSyncs=()=>{f.enableOverride&&t.syncUsers()},t.canBidderRegisterSync=(e,t)=>!f.filterSettings||!h(e,t),t}(Object.defineProperties({config:i.$W.getConfig("userSync"),isAllowed:a.io,regRule:a.qB},{browserSupportsCookies:{get:function(){return!(0,r.isSafariBrowser)()&&f.cookiesAreEnabled()}}}))},91069:(e,t,n)=>{n.r(t),n.d(t,{_each:()=>fe,_map:()=>me,_setEventEmitter:()=>w,binarySearch:()=>yt,buildUrl:()=>dt,canAccessWindowTop:()=>H,checkCookieSupport:()=>ze,cleanObj:()=>nt,compareCodeAndSlot:()=>Qe,contains:()=>pe,convertObjectToArray:()=>ht,createIframe:()=>te,createInvisibleIframe:()=>ne,createTrackPixelHtml:()=>Ae,createTrackPixelIframeHtml:()=>Te,cyrb53Hash:()=>ut,debugTurnedOn:()=>ee,deepAccess:()=>c.A,deepClone:()=>je,deepEqual:()=>ct,deepSetValue:()=>l.J,delayExecution:()=>He,encodeMacroURI:()=>Ie,extractDomainFromHost:()=>At,flatten:()=>Ce,formatQS:()=>st,generateUUID:()=>U,getBidIdParameter:()=>j,getBidRequest:()=>Se,getBidderCodes:()=>Be,getDNT:()=>Xe,getDefinedParams:()=>Ke,getDomLoadingDuration:()=>Fe,getParameterByName:()=>re,getPerformanceNow:()=>Ge,getPrebidInternal:()=>B,getSafeframeGeometry:()=>Ne,getUniqueIdentifierStr:()=>D,getUnixTimestampFromNow:()=>mt,getUserConfiguredParams:()=>Ye,getValue:()=>Oe,getWindowLocation:()=>z,getWindowSelf:()=>L,getWindowTop:()=>F,groupBy:()=>Ve,hasConsoleLogger:()=>Z,hasDeviceAccess:()=>Le,hasNonSerializableProperty:()=>vt,inIframe:()=>_e,insertElement:()=>he,insertHtmlIntoIframe:()=>ve,insertUserSyncIframe:()=>Ee,internal:()=>S,isA:()=>ie,isAdUnitCodeMatchingSlot:()=>Ze,isApnGetTagDefined:()=>ke,isArray:()=>ae,isArrayOfNums:()=>it,isBoolean:()=>le,isEmpty:()=>ue,isEmptyStr:()=>ge,isFn:()=>oe,isGptPubadsDefined:()=>Re,isInteger:()=>tt,isNumber:()=>de,isPlainObject:()=>ce,isSafariBrowser:()=>xe,isSafeFrameWindow:()=>$e,isStr:()=>se,isValidMediaTypes:()=>Je,logError:()=>Y,logInfo:()=>K,logMessage:()=>V,logWarn:()=>J,memoize:()=>pt,mergeDeep:()=>lt,parseGPTSingleSizeArray:()=>W,parseGPTSingleSizeArrayToRtbSize:()=>M,parseQS:()=>ot,parseQueryStringParameters:()=>_,parseSizesInput:()=>x,parseUrl:()=>at,pick:()=>rt,prefixLog:()=>X,replaceAuctionPrice:()=>We,replaceClickThrough:()=>Pe,replaceMacros:()=>qe,safeJSONEncode:()=>ft,safeJSONParse:()=>gt,setOnAny:()=>Et,setScriptAttributes:()=>bt,shuffle:()=>Ue,sizeTupleToRtbSize:()=>P,sizeTupleToSizeString:()=>q,sizesToSizeTuples:()=>N,sortByHighestCpm:()=>De,timestamp:()=>Me,transformAdServerTargetingObj:()=>$,triggerNurlWithCpm:()=>It,triggerPixel:()=>ye,uniques:()=>we,unsupportedBidderMessage:()=>et,waitForElementToLoad:()=>be});var r=n(43272),i=n(45751),o=n(15901),s=n(78969),a=n(25555),d=n(7873),c=n(70433),l=n(63172),u="String",g="Function",f="Number",p="Object",m="Boolean",h=Object.prototype.toString;let b,y=Boolean(window.console),v=Boolean(y&&window.console.log),E=Boolean(y&&window.console.info),A=Boolean(y&&window.console.warn),I=Boolean(y&&window.console.error);const T=(0,d.m)();function w(e){b=e}function C(){null!=b&&b(...arguments)}const S={checkCookieSupport:ze,createTrackPixelIframeHtml:Te,getWindowSelf:L,getWindowTop:F,canAccessWindowTop:H,getWindowLocation:z,insertUserSyncIframe:Ee,insertElement:he,isFn:oe,triggerPixel:ye,logError:Y,logWarn:J,logMessage:V,logInfo:K,parseQS:ot,formatQS:st,deepEqual:ct};let O={};function B(){return O}var R,k=(R=0,function(){return++R});function D(){return k()+Math.random().toString(16).substr(2)}function U(e){return e?(e^(window&&window.crypto&&window.crypto.getRandomValues?crypto.getRandomValues(new Uint8Array(1))[0]%16:16*Math.random())>>e/4).toString(16):([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,U)}function j(e,t){return t?.[e]||""}function _(e){let t="";for(var n in e)e.hasOwnProperty(n)&&(t+=n+"="+encodeURIComponent(e[n])+"&");return t=t.replace(/&$/,""),t}function $(e){return e&&Object.getOwnPropertyNames(e).length>0?Object.keys(e).map((t=>`${t}=${encodeURIComponent(e[t])}`)).join("&"):""}function N(e){return"string"==typeof e?e.split(/\s*,\s*/).map((e=>e.match(/^(\d+)x(\d+)$/i))).filter((e=>e)).map((e=>{let[t,n,r]=e;return[parseInt(n,10),parseInt(r,10)]})):Array.isArray(e)?G(e)?[e]:e.filter(G):[]}function x(e){return N(e).map(q)}function q(e){return e[0]+"x"+e[1]}function W(e){if(G(e))return q(e)}function P(e){return{w:e[0],h:e[1]}}function M(e){if(G(e))return P(e)}function G(e){return ae(e)&&2===e.length&&!isNaN(e[0])&&!isNaN(e[1])}function F(){return window.top}function L(){return window.self}function z(){return window.location}function H(){try{if(S.getWindowTop().location.href)return!0}catch(e){return!1}}function V(){ee()&&v&&console.log.apply(console,Q(arguments,"MESSAGE:"))}function K(){ee()&&E&&console.info.apply(console,Q(arguments,"INFO:"))}function J(){ee()&&A&&console.warn.apply(console,Q(arguments,"WARNING:")),C(s.qY.AUCTION_DEBUG,{type:"WARNING",arguments})}function Y(){ee()&&I&&console.error.apply(console,Q(arguments,"ERROR:")),C(s.qY.AUCTION_DEBUG,{type:"ERROR",arguments})}function X(e){function t(t){return function(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];t(e,...r)}}return{logError:t(Y),logWarn:t(J),logMessage:t(V),logInfo:t(K)}}function Q(e,t){e=[].slice.call(e);let n=r.$W.getCurrentBidder();return t&&e.unshift(t),n&&e.unshift(i("#aaa")),e.unshift(i("#3b88c3")),e.unshift("%cPrebid"+(n?`%c${n}`:"")),e;function i(e){return`display: inline-block; color: #fff; background: ${e}; padding: 1px 4px; border-radius: 3px;`}}function Z(){return v}function ee(){return!!r.$W.getConfig("debug")}const te=(()=>{const e={border:"0px",hspace:"0",vspace:"0",marginWidth:"0",marginHeight:"0",scrolling:"no",frameBorder:"0",allowtransparency:"true"};return function(t,n){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=t.createElement("iframe");return Object.assign(i,Object.assign({},e,n)),Object.assign(i.style,r),i}})();function ne(){return te(document,{id:D(),width:0,height:0,src:"about:blank"},{display:"none",height:"0px",width:"0px",border:"0px"})}function re(e){return ot(z().search)[e]||""}function ie(e,t){return h.call(e)==="[object "+t+"]"}function oe(e){return ie(e,g)}function se(e){return ie(e,u)}const ae=Array.isArray.bind(Array);function de(e){return ie(e,f)}function ce(e){return ie(e,p)}function le(e){return ie(e,m)}function ue(e){return!e||(ae(e)||se(e)?!(e.length>0):Object.keys(e).length<=0)}function ge(e){return se(e)&&(!e||0===e.length)}function fe(e,t){if(oe(e?.forEach))return e.forEach(t,this);Object.entries(e||{}).forEach((e=>{let[n,r]=e;return t.call(this,r,n)}))}function pe(e,t){return oe(e?.includes)&&e.includes(t)}function me(e,t){return oe(e?.map)?e.map(t):Object.entries(e||{}).map((n=>{let[r,i]=n;return t(i,r,e)}))}function he(e,t,n,r){let i;t=t||document,i=n?t.getElementsByTagName(n):t.getElementsByTagName("head");try{if(i=i.length?i:t.getElementsByTagName("body"),i.length){i=i[0];let t=r?null:i.firstChild;return i.insertBefore(e,t)}}catch(e){}}function be(e,t){let n=null;return new a.k((r=>{const i=function(){e.removeEventListener("load",i),e.removeEventListener("error",i),null!=n&&window.clearTimeout(n),r()};e.addEventListener("load",i),e.addEventListener("error",i),null!=t&&(n=window.setTimeout(i,t))}))}function ye(e,t,n){const r=new Image;t&&S.isFn(t)&&be(r,n).then(t),r.src=e}function ve(e){if(!e)return;const t=ne();var n;S.insertElement(t,document,"body"),(n=t.contentWindow.document).open(),n.write(e),n.close()}function Ee(e,t,n){let r=S.createTrackPixelIframeHtml(e,!1,"allow-scripts allow-same-origin"),i=document.createElement("div");i.innerHTML=r;let o=i.firstChild;t&&S.isFn(t)&&be(o,n).then(t),S.insertElement(o,document,"html",!0)}function Ae(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:encodeURI;if(!e)return"";let n='<div style="position:absolute;left:0px;top:0px;visibility:hidden;">';return n+='<img src="'+t(e)+'"></div>',n}function Ie(e){return Array.from(e.matchAll(/\$({[^}]+})/g)).map((e=>e[1])).reduce(((e,t)=>e.replace("$"+encodeURIComponent(t),"$"+t)),encodeURI(e))}function Te(e){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return e?((!(arguments.length>1&&void 0!==arguments[1])||arguments[1])&&(e=encodeURI(e)),t&&(t=`sandbox="${t}"`),`<iframe ${t} id="${D()}"\n      frameborder="0"\n      allowtransparency="true"\n      marginheight="0" marginwidth="0"\n      width="0" hspace="0" vspace="0" height="0"\n      style="height:0px;width:0px;display:none;"\n      scrolling="no"\n      src="${e}">\n    </iframe>`):""}function we(e,t,n){return n.indexOf(e)===t}function Ce(e,t){return e.concat(t)}function Se(e,t){if(e)return t.flatMap((e=>e.bids)).find((t=>["bidId","adId","bid_id"].some((n=>t[n]===e))))}function Oe(e,t){return e[t]}function Be(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:T.adUnits).map((e=>e.bids.map((e=>e.bidder)).reduce(Ce,[]))).reduce(Ce,[]).filter((e=>void 0!==e)).filter(we)}function Re(){if(window.googletag&&oe(window.googletag.pubads)&&oe(window.googletag.pubads().getSlots))return!0}function ke(){if(window.apntag&&oe(window.apntag.getTag))return!0}const De=(e,t)=>t.cpm-e.cpm;function Ue(e){let t=e.length;for(;t>0;){let n=Math.floor(Math.random()*t);t--;let r=e[t];e[t]=e[n],e[n]=r}return e}function je(e){return(0,i.Q)(e)||{}}function _e(){try{return S.getWindowSelf()!==S.getWindowTop()}catch(e){return!0}}function $e(){if(!_e())return!1;const e=S.getWindowSelf();return!(!e.$sf||!e.$sf.ext)}function Ne(){try{const e=L();return"function"==typeof e.$sf.ext.geom?e.$sf.ext.geom():void 0}catch(e){return void Y("Error getting SafeFrame geometry",e)}}function xe(){return/^((?!chrome|android|crios|fxios).)*safari/i.test(navigator.userAgent)}function qe(e,t){if(e)return Object.entries(t).reduce(((e,t)=>{let[n,r]=t;return e.replace(new RegExp("\\$\\{"+n+"\\}","g"),r||"")}),e)}function We(e,t){return qe(e,{AUCTION_PRICE:t})}function Pe(e,t){if(e&&t&&"string"==typeof t)return e.replace(/\${CLICKTHROUGH}/g,t)}function Me(){return(new Date).getTime()}function Ge(){return window.performance&&window.performance.now&&window.performance.now()||0}function Fe(e){let t=-1;const n=(e=e||L()).performance;if(e.performance?.timing&&e.performance.timing.navigationStart>0){const e=n.timing.domLoading-n.timing.navigationStart;e>0&&(t=e)}return t}function Le(){return!1!==r.$W.getConfig("deviceAccess")}function ze(){if(window.navigator.cookieEnabled||document.cookie.length)return!0}function He(e,t){if(t<1)throw new Error(`numRequiredCalls must be a positive number. Got ${t}`);let n=0;return function(){n++,n===t&&e.apply(this,arguments)}}function Ve(e,t){return e.reduce((function(e,n){return(e[n[t]]=e[n[t]]||[]).push(n),e}),{})}function Ke(e,t){return t.filter((t=>e[t])).reduce(((t,n)=>Object.assign(t,{[n]:e[n]})),{})}function Je(e){const t=["banner","native","video"],n=["instream","outstream","adpod"];return!!Object.keys(e).every((e=>(0,o.mK)(t,e)))&&(!e.video||!e.video.context||(0,o.mK)(n,e.video.context))}function Ye(e,t,n){return e.filter((e=>e.code===t)).flatMap((e=>e.bids)).filter((e=>e.bidder===n)).map((e=>e.params||{}))}function Xe(){return"1"===navigator.doNotTrack||"1"===window.doNotTrack||"1"===navigator.msDoNotTrack||"yes"===navigator.doNotTrack}const Qe=(e,t)=>e.getAdUnitPath()===t||e.getSlotElementId()===t;function Ze(e){return t=>Qe(e,t)}function et(e,t){const n=Object.keys(e.mediaTypes||{banner:"banner"}).join(", ");return`\n    ${e.code} is a ${n} ad unit\n    containing bidders that don't support ${n}: ${t}.\n    This bidder won't fetch demand.\n  `}const tt=Number.isInteger.bind(Number);function nt(e){return Object.fromEntries(Object.entries(e).filter((e=>{let[t,n]=e;return void 0!==n})))}function rt(e,t){return"object"!=typeof e?{}:t.reduce(((n,r,i)=>{if("function"==typeof r)return n;let o=r,s=r.match(/^(.+?)\sas\s(.+?)$/i);s&&(r=s[1],o=s[2]);let a=e[r];return"function"==typeof t[i+1]&&(a=t[i+1](a,n)),void 0!==a&&(n[o]=a),n}),{})}function it(e,t){return ae(e)&&(!t||e.length===t)&&e.every((e=>tt(e)))}function ot(e){return e?e.replace(/^\?/,"").split("&").reduce(((e,t)=>{let[n,r]=t.split("=");return/\[\]$/.test(n)?(n=n.replace("[]",""),e[n]=e[n]||[],e[n].push(r)):e[n]=r||"",e}),{}):{}}function st(e){return Object.keys(e).map((t=>Array.isArray(e[t])?e[t].map((e=>`${t}[]=${e}`)).join("&"):`${t}=${e[t]}`)).join("&")}function at(e,t){let n=document.createElement("a");t&&"noDecodeWholeURL"in t&&t.noDecodeWholeURL?n.href=e:n.href=decodeURIComponent(e);let r=t&&"decodeSearchAsString"in t&&t.decodeSearchAsString;return{href:n.href,protocol:(n.protocol||"").replace(/:$/,""),hostname:n.hostname,port:+n.port,pathname:n.pathname.replace(/^(?!\/)/,"/"),search:r?n.search:S.parseQS(n.search||""),hash:(n.hash||"").replace(/^#/,""),host:n.host||window.location.host}}function dt(e){return(e.protocol||"http")+"://"+(e.host||e.hostname+(e.port?`:${e.port}`:""))+(e.pathname||"")+(e.search?`?${S.formatQS(e.search||"")}`:"")+(e.hash?`#${e.hash}`:"")}function ct(e,t){let{checkTypes:n=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e===t)return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t||n&&e.constructor!==t.constructor)return!1;{const r=Object.keys(e);if(r.length!==Object.keys(t).length)return!1;for(let i of r){if(!t.hasOwnProperty(i))return!1;if(!ct(e[i],t[i],{checkTypes:n}))return!1}return!0}}function lt(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];if(!n.length)return e;const i=n.shift();if(ce(e)&&ce(i))for(const t in i)ce(i[t])?(e[t]||Object.assign(e,{[t]:{}}),lt(e[t],i[t])):ae(i[t])?e[t]?ae(e[t])&&i[t].forEach((n=>{let r=1;for(let i=0;i<e[t].length;i++)if(ct(e[t][i],n)){r=0;break}r&&e[t].push(n)})):Object.assign(e,{[t]:[...i[t]]}):Object.assign(e,{[t]:i[t]});return lt(e,...n)}function ut(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=function(e,t){if(oe(Math.imul))return Math.imul(e,t);var n=(4194303&e)*(t|=0);return 4290772992&e&&(n+=(4290772992&e)*t|0),0|n},r=3735928559^t,i=1103547991^t;for(let t,o=0;o<e.length;o++)t=e.charCodeAt(o),r=n(r^t,2654435761),i=n(i^t,1597334677);return r=n(r^r>>>16,2246822507)^n(i^i>>>13,3266489909),i=n(i^i>>>16,2246822507)^n(r^r>>>13,3266489909),(4294967296*(2097151&i)+(r>>>0)).toString()}function gt(e){try{return JSON.parse(e)}catch(e){}}function ft(e){try{return JSON.stringify(e)}catch(e){return""}}function pt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(e){return e};const n=new Map,r=function(){const r=t.apply(this,arguments);return n.has(r)||n.set(r,e.apply(this,arguments)),n.get(r)};return r.clear=n.clear.bind(n),r}function mt(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"d";if(["m","d"].indexOf(t)<0)return Date.now();const n=e/("m"===t?1440:1);return Date.now()+(e&&e>0?864e5*n:0)}function ht(e){return Object.keys(e).map((t=>({[t]:e[t]})))}function bt(e,t){Object.entries(t).forEach((t=>{let[n,r]=t;return e.setAttribute(n,r)}))}function yt(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e=>e,r=0,i=e.length&&e.length-1;const o=n(t);for(;i-r>1;){const t=r+Math.round((i-r)/2);o>n(e[t])?r=t:i=t}for(;e.length>r&&o>n(e[r]);)r++;return r}function vt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;for(const n in e){const r=e[n],i=typeof r;if(void 0===r||"function"===i||"symbol"===i||r instanceof RegExp||r instanceof Map||r instanceof Set||r instanceof Date||null!==r&&"object"===i&&r.hasOwnProperty("toJSON"))return!0;if(null!==r&&"object"===i&&r.constructor===Object){if(t.has(r))return!0;if(t.add(r),vt(r,t))return!0}}return!1}function Et(e,t){for(let n,r=0;r<e.length;r++)if(n=(0,c.A)(e[r],t),n)return n}function At(e){let t=null;try{let n=/[-\w]+\.([-\w]+|[-\w]{3,}|[-\w]{1,3}\.[-\w]{2})$/i.exec(e);if(null!=n&&n.length>0){t=n[0];for(let e=1;e<n.length;e++)n[e].length>t.length&&(t=n[e])}}catch(e){t=null}return t}function It(e,t){se(e.nurl)&&""!==e.nurl&&(e.nurl=e.nurl.replace(/\${AUCTION_PRICE}/,t),ye(e.nurl))}},57176:(e,t,n)=>{n.d(t,{y:()=>s});var r=n(67314),i=n(12693),o=n(91069);function s(e,t,n){let{index:s=r.n.index,bs:a=i.u}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};n=n||s.getBidRequest(t);const d=t?.adapterCode,c=t?.bidderCode||n?.bidder,l=a.get(t?.adapterCode,"adjustAlternateBids"),u=a.getOwn(c,"bidCpmAdjustment")||a.get(l?d:c,"bidCpmAdjustment");if(u&&"function"==typeof u)try{return u(e,Object.assign({},t),n)}catch(e){(0,o.logError)("Error during bid adjustment",e)}return e}},82621:(e,t,n)=>{function r(e){return!e?.gdprApplies||!0===e?.vendorData?.purpose?.consents?.[1]}n.d(t,{C:()=>r})},16894:(e,t,n)=>{n.d(t,{Ak:()=>h,BO:()=>f,K7:()=>p,NL:()=>b});var r=n(43272);const i="performanceMetrics",o=window.performance&&window.performance.now?()=>window.performance.now():()=>Date.now(),s=new WeakMap;function a(){let{now:e=o,mkNode:t=l,mkTimer:n=c,mkRenamer:r=(e=>e),nodes:i=s}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return function(){return function o(s){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>({forEach(t){t(e)}});a=r(a);const d=(c="timestamps",function(e){return s.dfWalk({visit(t,n){const r=n[c];if(r.hasOwnProperty(e))return r[e]}})});var c;function l(e,t){const n=a(e);s.dfWalk({follow:(e,t)=>t.propagate&&(!e||!e.stopPropagation),visit(e,r){n.forEach((n=>{null==e?r.metrics[n]=t:(r.groups.hasOwnProperty(n)||(r.groups[n]=[]),r.groups[n].push(t))}))}})}function u(t){return n(e,(e=>l(t,e)))}function g(){let e={};return s.dfWalk({visit(t,n){e=Object.assign({},!t||t.includeGroups?n.groups:null,n.metrics,e)}}),e}const f={startTiming:u,measureTime:function(e,t){return u(e).stopAfter(t)()},measureHookTime:function(e,t,n){const r=u(e);return n(function(e){const t=r.stopBefore(e);return t.bail=e.bail&&r.stopBefore(e.bail),t.stopTiming=r,t.untimed=e,t}(t))},checkpoint:function(t){s.timestamps[t]=e()},timeSince:function(t,n){const r=d(t),i=null!=r?e()-r:null;return null!=n&&l(n,i),i},timeBetween:function(e,t,n){const r=d(e),i=d(t),o=null!=r&&null!=i?i-r:null;return null!=n&&l(n,o),o},setMetric:l,getMetrics:g,fork:function(){let{propagate:e=!0,stopPropagation:n=!1,includeGroups:r=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o(t([[s,{propagate:e,stopPropagation:n,includeGroups:r}]]),a)},join:function(e){let{propagate:t=!0,stopPropagation:n=!1,includeGroups:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const o=i.get(e);null!=o&&o.addParent(s,{propagate:t,stopPropagation:n,includeGroups:r})},newMetrics:function(){return o(s.newSibling(),a)},renameWith:function(e){return o(s,e)},toJSON:()=>g()};return i.set(f,s),f}(t([]))}}function d(e,t,n){return function(){t&&t();try{return e.apply(this,arguments)}finally{n&&n()}}}function c(e,t){const n=e();let r=!1;function i(){r||(t(e()-n),r=!0)}return i.stopBefore=e=>d(e,i),i.stopAfter=e=>d(e,null,i),i}function l(e){return{metrics:{},timestamps:{},groups:{},addParent(t,n){e.push([t,n])},newSibling:()=>l(e.slice()),dfWalk(){let t,{visit:n,follow:r=(()=>!0),visited:i=new Set,inEdge:o}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!i.has(this)){if(i.add(this),t=n(o,this),null!=t)return t;for(const[s,a]of e)if(r(o,a)&&(t=s.dfWalk({visit:n,follow:r,visited:i,inEdge:a}),null!=t))return t}}}}const u=(()=>{const e=function(){},t=()=>({}),n={forEach:e},r=()=>null;r.stopBefore=e=>e,r.stopAfter=e=>e;const i=Object.defineProperties({dfWalk:e,newSibling:()=>i,addParent:e},Object.fromEntries(["metrics","timestamps","groups"].map((e=>[e,{get:t}]))));return a({now:()=>0,mkNode:()=>i,mkRenamer:()=>()=>n,mkTimer:()=>r,nodes:{get:e,set:e}})()})();let g=!0;function f(e){return g&&e||u}r.$W.getConfig(i,(e=>{g=!!e[i]}));const p=(()=>{const e=a();return function(){return g?e():u}})();function m(e,t){return function(n,r){return function(i){for(var o=arguments.length,s=new Array(o>1?o-1:0),a=1;a<o;a++)s[a-1]=arguments[a];const d=this;return f(t.apply(d,s)).measureHookTime(e+n,i,(function(e){return r.call(d,e,...s)}))}}}const h=m("requestBids.",(e=>e.metrics)),b=m("addBidResponse.",((e,t)=>t.metrics))},25555:(e,t,n)=>{n.d(t,{k:()=>o,v:()=>s});const r=0,i=1;class o{#d;#c;static timeout(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return new o((t=>{0===e?t():setTimeout(t,e)}))}constructor(e){if("function"!=typeof e)throw new Error("resolver not a function");const t=[],n=[];let[o,s]=[r,i].map((e=>function(i){if(e===r&&"function"==typeof i?.then)i.then(o,s);else if(!t.length)for(t.push(e,i);n.length;)n.shift()()}));try{e(o,s)}catch(e){s(e)}this.#d=t,this.#c=n}then(e,t){const n=this.#d;return new this.constructor(((i,o)=>{const s=()=>{let s=n[1],[a,d]=n[0]===r?[e,i]:[t,o];if("function"==typeof a){try{s=a(s)}catch(e){return void o(e)}d=i}d(s)};n.length?s():this.#c.push(s)}))}catch(e){return this.then(null,e)}finally(e){let t;return this.then((n=>(t=n,e())),(n=>(t=this.constructor.reject(n),e()))).then((()=>t))}static#l(e,t,n){let r=e.length;function i(){t.apply(this,arguments),--r<=0&&n&&n()}0===e.length&&n?n():e.forEach(((e,t)=>this.resolve(e).then((e=>i(!0,e,t)),(e=>i(!1,e,t)))))}static race(e){return new this(((t,n)=>{this.#l(e,((e,r)=>e?t(r):n(r)))}))}static all(e){return new this(((t,n)=>{let r=[];this.#l(e,((e,t,i)=>e?r[i]=t:n(t)),(()=>t(r)))}))}static allSettled(e){return new this((t=>{let n=[];this.#l(e,((e,t,r)=>n[r]=e?{status:"fulfilled",value:t}:{status:"rejected",reason:t}),(()=>t(n)))}))}static resolve(e){return new this((t=>t(e)))}static reject(e){return new this(((t,n)=>n(e)))}}function s(){let e,t,{promiseFactory:n=(e=>new o(e))}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};function r(e){return t=>e(t)}return{promise:n(((n,r)=>{e=n,t=r})),resolve:r(e),reject:r(t)}}},12713:(e,t,n)=>{function r(e,t){return e===t?0:e<t?-1:1}function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e=>e;return(t,n)=>r(e(t),e(n))}function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r;return(t,n)=>-e(t,n)||0}function s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,n){for(const r of t){const t=r(e,n);if(0!==t)return t}return 0}}function a(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r;return(t,n)=>e(n,t)<0?n:t}function d(){return a(o(arguments.length>0&&void 0!==arguments[0]?arguments[0]:r))}n.d(t,{Bp:()=>a,Bq:()=>g,NV:()=>i,Ph:()=>d,Vk:()=>u});const c=i((e=>e.cpm)),l=i((e=>e.responseTimestamp)),u=d(s(c,o(i((e=>e.timeToRespond))))),g=d(s(c,o(l)));d(s(c,l))},76853:(e,t,n)=>{n.d(t,{H:()=>l});var r=n(25555),i=n(91069);let o=null,s=0,a=[];function d(){document.hidden?o=Date.now():(s+=Date.now()-(o??0),o=null,a.forEach((e=>{let{callback:t,startTime:n,setTimerId:r}=e;return r(c(t,s-n)())})),a=[])}function c(e,t){const n=s;let r=setTimeout((()=>{s===n&&null==o?e():null!=o?a.push({callback:e,startTime:n,setTimerId(e){r=e}}):r=c(e,s-n)()}),t);return()=>r}function l(){let{startTime:e=i.timestamp,ttl:t=(()=>null),monotonic:n=!1,slack:o=5e3}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const s=new Map,a=[],d=[],l=n?e=>d.push(e):e=>d.splice((0,i.binarySearch)(d,e,(e=>e.expiry)),0,e);let u,g;function f(){if(g&&clearTimeout(g),d.length>0){const e=(0,i.timestamp)();u=Math.max(e,d[0].expiry+o),g=c((()=>{const e=(0,i.timestamp)();let t=0;for(const n of d){if(n.expiry>e)break;a.forEach((e=>{try{e(n.item)}catch(e){(0,i.logError)(e)}})),s.delete(n.item),t++}d.splice(0,t),g=null,f()}),u-e)}else g=null}function p(n){const i={},s=m;let a;const[d,c]=Object.entries({start:e,delta:t}).map((e=>{let t,[d,c]=e;return function(){const e=t={};r.k.resolve(c(n)).then((n=>{e===t&&(i[d]=n,s===m&&null!=i.start&&null!=i.delta&&(a=i.start+i.delta,l(p),(null==g||u>a+o)&&f()))}))}})),p={item:n,refresh:c,get expiry(){return a}};return d(),c(),p}let m={};return{[Symbol.iterator]:()=>s.keys(),add(e){!s.has(e)&&s.set(e,p(e))},clear(){d.length=0,f(),s.clear(),m={}},toArray:()=>Array.from(s.keys()),refresh(){d.length=0,f();for(const e of s.values())e.refresh()},onExpiry:e=>(a.push(e),()=>{const t=a.indexOf(e);t>=0&&a.splice(t,1)})}}document.addEventListener("visibilitychange",d)},63895:(e,t,n)=>{n.d(t,{E2:()=>f,H6:()=>a,V0:()=>l,Zy:()=>c,aP:()=>u,mn:()=>d,vk:()=>g});var r=n(91069),i=n(43272),o=n(16833),s=n(67314);const a="outstream",d="instream",c=new Map([["mimes",e=>Array.isArray(e)&&e.length>0&&e.every((e=>"string"==typeof e))],["minduration",r.isInteger],["maxduration",r.isInteger],["startdelay",r.isInteger],["maxseq",r.isInteger],["poddur",r.isInteger],["protocols",r.isArrayOfNums],["w",r.isInteger],["h",r.isInteger],["podid",r.isStr],["podseq",r.isInteger],["rqddurs",r.isArrayOfNums],["placement",r.isInteger],["plcmt",r.isInteger],["linearity",r.isInteger],["skip",e=>[1,0].includes(e)],["skipmin",r.isInteger],["skipafter",r.isInteger],["sequence",r.isInteger],["slotinpod",r.isInteger],["mincpmpersec",r.isNumber],["battr",r.isArrayOfNums],["maxextended",r.isInteger],["minbitrate",r.isInteger],["maxbitrate",r.isInteger],["boxingallowed",r.isInteger],["playbackmethod",r.isArrayOfNums],["playbackend",r.isInteger],["delivery",r.isArrayOfNums],["pos",r.isInteger],["api",r.isArrayOfNums],["companiontype",r.isArrayOfNums],["poddedupe",r.isArrayOfNums]]);function l(e){const t=e?.mediaTypes?.video;null!=t&&null==t.plcmt&&(t.context===a||[2,3,4].includes(t.placement)?t.plcmt=4:t.context!==a&&[2,6].includes(t.playbackmethod)&&(t.plcmt=2))}function u(e,t){const n=e?.mediaTypes?.video;(0,r.isPlainObject)(n)?null!=n&&Object.entries(n).forEach((i=>{let[o,s]=i;if(!c.has(o))return;c.get(o)(s)||("function"==typeof t?t(o,s,e):(delete n[o],(0,r.logWarn)(`Invalid prop in adUnit "${e.code}": Invalid value for mediaTypes.video.${o} ORTB property. The property has been removed.`)))})):(0,r.logWarn)("validateOrtbVideoFields: videoParams must be an object.")}function g(e){let{index:t=s.n.index}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=t.getMediaTypes(e)?.video,r=n&&n?.context,i=n&&n?.useCacheKey,o=t.getAdUnit(e);return f(e,o,n,r,i)}const f=(0,o.A_)("sync",(function(e,t,n,o,s){return n&&(s||o!==a)?i.$W.getConfig("cache.url")||!e.vastXml||e.vastUrl?!(!e.vastUrl&&!e.vastXml):((0,r.logError)('\n        This bid contains only vastXml and will not work when a prebid cache url is not specified.\n        Try enabling prebid cache with pbjs.setConfig({ cache: {url: "..."} });\n      '),!1):!(o===a&&!s)||!!(e.renderer||t&&t.renderer||n.renderer)}),"checkVideoBidSetup")},68693:(e,t,n)=>{n.d(t,{M_:()=>l,X5:()=>m});var r=n(68044),i=n(43272),o=n(67314),s=n(91069),a=n(81657);const d=15;function c(e){let{index:t=o.n.index}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=e.vastXml?e.vastXml:(r=e.vastUrl,s=e.vastImpUrl,`<VAST version="3.0">\n    <Ad>\n      <Wrapper>\n        <AdSystem>prebid.org wrapper</AdSystem>\n        <VASTAdTagURI><![CDATA[${r}]]></VASTAdTagURI>\n        ${(s=s&&(Array.isArray(s)?s:[s]))?s.map((e=>`<Impression><![CDATA[${e}]]></Impression>`)).join(""):""}\n        <Creatives></Creatives>\n      </Wrapper>\n    </Ad>\n  </VAST>`);var r,s;const a=t.getAuction(e);let c={type:"xml",value:n,ttlseconds:Number(e.ttl)+d};return i.$W.getConfig("cache.vasttrack")&&(c.bidder=e.bidder,c.bidid=e.requestId,c.aid=e.auctionId),null!=a&&(c.timestamp=a.getAuctionStart()),"string"==typeof e.customCacheKey&&""!==e.customCacheKey&&(c.key=e.customCacheKey),c}function l(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r.g4;const o={puts:e.map(c)};n(i.$W.getConfig("cache.timeout"))(i.$W.getConfig("cache.url"),function(e){return{success:function(t){let n;try{n=JSON.parse(t).responses}catch(t){return void e(t,[])}n?e(null,n):e(new Error("The cache server didn't respond with a responses property."),[])},error:function(t,n){e(new Error(`Error storing video ad in the cache: ${t}: ${JSON.stringify(n)}`),[])}}}(t),JSON.stringify(o),{contentType:"text/plain",withCredentials:!0})}const u={store:l};function g(e){const t=e.map((e=>e.bidResponse));u.store(t,(function(n,r){var o;n?(o=n,(0,s.logError)(`Failed to save to the video cache: ${o}. Video bids will be discarded:`,t)):e.length!==r.length?(0,s.logError)(`expected ${e.length} cache IDs, got ${r.length} instead`):r.forEach(((t,n)=>{const{auctionInstance:r,bidResponse:o,afterBidAdded:d}=e[n];var c;""===t.uuid?(0,s.logWarn)("Supplied video cache key was already in use by Prebid Cache; caching attempt was rejected. Video bid must be discarded."):(o.videoCacheKey=t.uuid,o.vastUrl||(o.vastUrl=(c=o.videoCacheKey,`${i.$W.getConfig("cache.url")}?uuid=${c}`)),(0,a.v8)(r,o),d())}))}))}let f,p;i.$W.getConfig("cache",(e=>{f="number"==typeof e.cache.batchSize&&e.cache.batchSize>0?e.cache.batchSize:1,p="number"==typeof e.cache.batchTimeout&&e.cache.batchTimeout>0?e.cache.batchTimeout:0}));const m=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:setTimeout,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:g,n=[[]],r=!1;const i=e=>e();return function(o,s,a){const d=p>0?e:i;n[n.length-1].length>=f&&n.push([]),n[n.length-1].push({auctionInstance:o,bidResponse:s,afterBidAdded:a}),r||(r=!0,d((()=>{n.forEach(t),n=[[]],r=!1}),p))}}()}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[51085],{34595:(s,d,e)=>{e.d(d,{G:()=>n});const n='(()=>{"use strict";window.render=function({ad:d,adUrl:e,width:i,height:r},{mkFrame:n},o){if(!d&&!e)throw{reason:"noAd",message:"Missing ad markup or URL"};{const s=o.document,t={width:i,height:r};e&&!d?t.src=e:t.srcdoc=d,s.body.appendChild(n(s,t))}}})();'}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[19147],{29495:(e,r,n)=>{function u(e){return e?.ortb2?.ext?.prebid?.adServerCurrency}n.d(r,{b:()=>u})}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[97247],{28656:(r,e,a)=>{a.d(e,{D:()=>o});var n=a(73858),t=a(70433);const s=["user.keywords"].concat(n.Dy.flatMap((r=>["keywords","content.keywords"].map((e=>`${r}.${e}`)))));function o(r){for(var e=arguments.length,a=new Array(e>1?e-1:0),n=1;n<e;n++)a[n-1]=arguments[n];return function(){const r=new Set;for(var e=arguments.length,a=new Array(e),n=0;n<e;n++)a[n]=arguments[n];return a.filter((r=>r)).flatMap((r=>Array.isArray(r)?r:r.split(","))).map((r=>r.replace(/^\s*/,"").replace(/\s*$/,""))).filter((r=>r)).forEach((e=>r.add(e))),Array.from(r.keys())}(...s.map((e=>(0,t.A)(r,e))),...a)}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[44982],{29906:(e,l,s)=>{function t(e,l){let s=[];for(let t=0;t<Math.ceil(e.length/l);t++){let h=t*l,n=h+l;s.push(e.slice(h,n))}return s}s.d(l,{i:()=>t})}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[95444],{554:(e,t,r)=>{r.d(t,{QF:()=>f,T_:()=>g,gg:()=>l});var n=r(91069),o=r(70433),i=r(28656),a=r(73858);const c={526:"1plusX",527:"1plusX",541:"captify_segments",540:"perid"},s=["user.data"].concat(a.Dy.map((e=>`${e}.content.data`)));function d(e,t,r){return null==t?r:(0,n.isStr)(t)?t:(0,n.isNumber)(t)?t.toString():void(0,n.logWarn)("Unsuported type for param: "+e+" required type: String")}function l(e){return(0,n.isStr)(e)&&""!==e?u(e.split(/\s*(?:,)\s*/)):{}}function u(e){const t={};return e.forEach((e=>{if(-1!==e.indexOf("=")){let r=e.split("="),n=r[0],o=r[1];t.hasOwnProperty(n)?t[n].push(o):t[n]=[o]}else t.hasOwnProperty(e)||(t[e]=[])})),t}function g(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"keywords";const r=[];return(0,n._each)(e,((e,o)=>{if((0,n.isArray)(e)){let r=[];(0,n._each)(e,(e=>{((e=d(t+"."+o,e))||""===e)&&r.push(e)})),e=r}else{if(e=d(t+"."+o,e),!(0,n.isStr)(e))return;e=[e]}e=e.filter((e=>""!==e));const i={key:o};e.length>0&&(i.value=e),r.push(i)})),r}((0,n.mergeDeep)(...t.map((e=>Object.fromEntries(Object.entries(e||{}).map((e=>{let[t,r]=e;return[t,(0,n.isNumber)(r)||(0,n.isStr)(r)?[r]:r]})))))))}function f(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return g(function(e){return u((0,i.D)(e))}(e),function(e){let t={};return s.forEach((r=>{((0,o.A)(e,r)||[]).forEach((e=>{const r=c[e?.ext?.segtax];r&&e.segment.forEach((e=>{t[r]?t[r].push(e.id):t[r]=[e.id]}))}))})),t}(e),...r)}},2349:(e,t,r)=>{r.d(t,{DX:()=>i,GS:()=>a,vk:()=>o});var n=r(91069);function o(e){return e.replace(/(?:^|\.?)([A-Z])/g,(function(e,t){return"_"+t.toLowerCase()})).replace(/^_/,"")}const i=[{code:"appnexusAst",gvlid:32},{code:"emxdigital",gvlid:183},{code:"emetriq",gvlid:213},{code:"pagescience",gvlid:32},{code:"gourmetads",gvlid:32},{code:"matomy",gvlid:32},{code:"featureforward",gvlid:32},{code:"oftmedia",gvlid:32},{code:"adasta",gvlid:32},{code:"beintoo",gvlid:618},{code:"projectagora",gvlid:1032},{code:"stailamedia",gvlid:32},{code:"uol",gvlid:32},{code:"adzymic",gvlid:723}];function a(e,t){let r=[];for(let o=0;o<t;o++){let t=(0,n.isPlainObject)(e)?(0,n.deepClone)(e):e;r.push(t)}return r}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[42698],{24673:(A,B,I)=>{I.d(B,{n:()=>s});const s={1:"IAB20-3",2:"IAB18-5",3:"IAB10-1",4:"IAB2-3",5:"IAB19-8",6:"IAB22-1",7:"IAB18-1",8:"IAB12-3",9:"IAB5-1",10:"IAB4-5",11:"IAB13-4",12:"IAB8-7",13:"IAB9-7",14:"IAB7-1",15:"IAB20-18",16:"IAB10-7",17:"IAB19-18",18:"IAB13-6",19:"IAB18-4",20:"IAB1-5",21:"IAB1-6",22:"IAB3-4",23:"IAB19-13",24:"IAB22-2",25:"IAB3-9",26:"IAB17-18",27:"IAB19-6",28:"IAB1-7",29:"IAB9-30",30:"IAB20-7",31:"IAB20-17",32:"IAB7-32",33:"IAB16-5",34:"IAB19-34",35:"IAB11-5",36:"IAB12-3",37:"IAB11-4",38:"IAB12-3",39:"IAB9-30",41:"IAB7-44",42:"IAB7-1",43:"IAB7-30",50:"IAB19-30",51:"IAB17-12",52:"IAB19-30",53:"IAB3-1",55:"IAB13-2",56:"IAB19-30",57:"IAB19-30",58:"IAB7-39",59:"IAB22-1",60:"IAB7-39",61:"IAB21-3",62:"IAB5-1",63:"IAB12-3",64:"IAB20-18",65:"IAB11-2",66:"IAB17-18",67:"IAB9-9",68:"IAB9-5",69:"IAB7-44",71:"IAB22-3",73:"IAB19-30",74:"IAB8-5",78:"IAB22-1",85:"IAB12-2",86:"IAB22-3",87:"IAB11-3",112:"IAB7-32",113:"IAB7-32",114:"IAB7-32",115:"IAB7-32",118:"IAB9-5",119:"IAB9-5",120:"IAB9-5",121:"IAB9-5",122:"IAB9-5",123:"IAB9-5",124:"IAB9-5",125:"IAB9-5",126:"IAB9-5",127:"IAB22-1",132:"IAB1-2",133:"IAB19-30",137:"IAB3-9",138:"IAB19-3",140:"IAB2-3",141:"IAB2-1",142:"IAB2-3",143:"IAB17-13",166:"IAB11-4",175:"IAB3-1",176:"IAB13-4",182:"IAB8-9",183:"IAB3-5"}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[12126],{25761:(e,n,r)=>{r.d(n,{$:()=>t});var s=r(91069);function t(e,n){return Object.keys(e).forEach((r=>{var t,u;n[r]&&((0,s.isFn)(e[r])?n[r]=e[r](n[r]):n[r]=(t=e[r],u=n[r],"string"===t?u&&u.toString():"number"===t?Number(u):u),isNaN(n[r])&&delete n.key)})),n}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[58498],{89766:(e,t,r)=>{r.d(t,{T:()=>l,A:()=>m});const n=new WeakMap;var i=r(91069),s=r(71371);var o=r(63895);var a=r(75561);var d=r(1e3),p=r(73858);const c={[d.S3]:{fpd:{priority:99,fn(e,t){(0,i.mergeDeep)(e,t.ortb2)}},onlyOneClient:{priority:-99,fn:(0,p.i8)("ORTB request")},props:{fn(e,t){Object.assign(e,{id:e.id||(0,i.generateUUID)(),test:e.test||0});const r=parseInt(t.timeout,10);isNaN(r)||(e.tmax=r)}}},[d.Tb]:{fpd:{priority:99,fn(e,t){(0,i.mergeDeep)(e,t.ortb2Imp)}},id:{fn(e,t){e.id=t.bidId}},banner:{fn:function(e,t,r){if(r.mediaType&&r.mediaType!==s.D4)return;const n=t?.mediaTypes?.banner;if(n){const t={topframe:!0===(0,i.inIframe)()?0:1};n.sizes&&(t.format=(0,i.sizesToSizeTuples)(n.sizes).map(i.sizeTupleToRtbSize)),n.hasOwnProperty("pos")&&(t.pos=n.pos),e.banner=(0,i.mergeDeep)(t,e.banner)}}},pbadslot:{fn(e){const t=e.ext?.data?.pbadslot;t&&"string"==typeof t||delete e.ext?.data?.pbadslot}},secure:{fn(e,t){e.secure=e.secure??1}}},[d.WR]:{mediaType:{priority:99,fn:a.K},banner:{fn:function(){let{createPixel:e=(e=>(0,i.createTrackPixelHtml)(decodeURIComponent(e),i.encodeMacroURI))}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return function(t,r){t.mediaType===s.D4&&(r.adm&&r.nurl?(t.ad=r.adm,t.ad+=e(r.nurl)):r.adm?t.ad=r.adm:r.nurl&&(t.adUrl=r.nurl))}}()},props:{fn(e,t,r){Object.entries({requestId:r.bidRequest?.bidId,seatBidId:t.id,cpm:t.price,currency:r.ortbResponse.cur||r.currency,width:t.w,height:t.h,dealId:t.dealid,creative_id:t.crid,creativeId:t.crid,burl:t.burl,ttl:t.exp||r.ttl,netRevenue:r.netRevenue}).filter((e=>{let[t,r]=e;return void 0!==r})).forEach((t=>{let[r,n]=t;return e[r]=n})),e.meta||(e.meta={}),t.adomain&&(e.meta.advertiserDomains=t.adomain),t.ext?.dsa&&(e.meta.dsa=t.ext.dsa),t.cat&&(e.meta.primaryCatId=t.cat[0],e.meta.secondaryCatIds=t.cat.slice(1)),t.attr&&(e.meta.attr=t.attr)}}}};c[d.Tb].native={fn:function(e,t,r){if(r.mediaType&&r.mediaType!==s.s6)return;let n=t.nativeOrtbRequest;n&&(n=Object.assign({},r.nativeRequest,n),n.assets?.length?e.native=(0,i.mergeDeep)({},{request:JSON.stringify(n),ver:n.ver},e.native):(0,i.logWarn)("mediaTypes.native is set, but no assets were specified. Native request skipped.",t))}},c[d.WR].native={fn:function(e,t){if(e.mediaType===s.s6){let r;if(r="string"==typeof t.adm?JSON.parse(t.adm):t.adm,!(0,i.isPlainObject)(r)||!Array.isArray(r.assets))throw new Error("ORTB native response contained no assets");e.native={ortb:r}}}},c[d.Tb].video={fn:function(e,t,r){if(r.mediaType&&r.mediaType!==s.G_)return;const n=t?.mediaTypes?.video;if(!(0,i.isEmpty)(n)){const t=Object.fromEntries(Object.entries(n).filter((e=>{let[t]=e;return o.Zy.has(t)})));if(n.playerSize){const e=(0,i.sizesToSizeTuples)(n.playerSize).map(i.sizeTupleToRtbSize);e.length>1&&(0,i.logWarn)("video request specifies more than one playerSize; all but the first will be ignored"),Object.assign(t,e[0])}e.video=(0,i.mergeDeep)(t,e.video)}}},c[d.WR].video={fn:function(e,t,r){e.mediaType===s.G_&&(r?.imp?.video?.w&&r?.imp?.video?.h&&([e.playerWidth,e.playerHeight]=[r.imp.video.w,r.imp.video.h]),t.adm&&(e.vastXml=t.adm),t.nurl&&(e.vastUrl=t.nurl))}};var u=r(99466);function m(){let{context:e={},processors:t=l,overrides:r={},imp:s,request:o,bidResponse:a,response:p}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const c=new WeakMap;function u(e,i,s,o){let a;return function(){return null==a&&(a=function(){let a=s.bind(this,function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!n.has(e)){const t=Object.entries(e);t.sort(((e,t)=>(e=e[1].priority||0)===(t=t[1].priority||0)?0:e>t?-1:1)),n.set(e,t.map((e=>{let[t,r]=e;return[t,r.fn]})))}const r=n.get(e).filter((e=>{let[r]=e;return!t.hasOwnProperty(r)||t[r]})).map((function(e){let[r,n]=e;return t.hasOwnProperty(r)?t[r].bind(this,n):n}));return function(){const e=Array.from(arguments);r.forEach((t=>{t.apply(this,e)}))}}(t()[e]||{},r[e]||{}));return i&&(a=i.bind(this,a)),function(){try{return a.apply(this,arguments)}catch(e){o.call(this,e,...arguments)}}}()),a.apply(this,arguments)}}const m=u(d.Tb,s,(function(e,t,r){const n={};return e(n,t,r),n}),(function(e,t,r){(0,i.logError)("Error while converting bidRequest to ORTB imp; request skipped.",{error:e,bidRequest:t,context:r})})),f=u(d.S3,o,(function(e,t,r,n){const i={imp:t};return e(i,r,n),i}),(function(e,t,r,n){throw(0,i.logError)("Error while converting to ORTB request",{error:e,imps:t,bidderRequest:r,context:n}),e})),b=u(d.WR,a,(function(e,t,r){const n={};return e(n,t,r),n}),(function(e,t,r){(0,i.logError)("Error while converting ORTB seatbid.bid to bidResponse; bid skipped.",{error:e,bid:t,context:r})})),y=u(d.Cf,p,(function(e,t,r,n){const i={bids:t};return e(i,r,n),i}),(function(e,t,r,n){throw(0,i.logError)("Error while converting from ORTB response",{error:e,bidResponses:t,ortbResponse:r,context:n}),e}));return{toORTB(t){let{bidderRequest:r,bidRequests:n,context:s={}}=t;n=n||r.bids;const o={req:Object.assign({bidRequests:n},e,s),imp:{}};o.req.impContext=o.imp;const a=n.map((t=>{const n=Object.assign({bidderRequest:r,reqContext:o.req},e,s),a=m(t,n);if(null!=a){if(a.hasOwnProperty("id"))return Object.assign(n,{bidRequest:t,imp:a}),o.imp[a.id]=n,a;(0,i.logError)("Converted ORTB imp does not specify an id, ignoring bid request",t,a)}})).filter(Boolean),d=f(a,r,o.req);return o.req.bidderRequest=r,null!=d&&c.set(d,o),d},fromORTB(e){let{request:t,response:r}=e;const n=c.get(t);if(null==n)throw new Error("ortbRequest passed to `fromORTB` must be the same object returned by `toORTB`");function s(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.assign(e,{ortbRequest:t},r)}const o=Object.fromEntries((t.imp||[]).map((e=>[e.id,e]))),a=(r.seatbid||[]).flatMap((e=>(e.bid||[]).map((t=>{if(o.hasOwnProperty(t.impid)&&n.imp.hasOwnProperty(t.impid))return b(t,s(n.imp[t.impid],{imp:o[t.impid],seatbid:e,ortbResponse:r}));(0,i.logError)("ORTB response seatbid[].bid[].impid does not match any imp in request; ignoring bid",t)})))).filter(Boolean);return y(a,r,s(n.req))}}}const l=(0,i.memoize)((()=>(0,u.U)(c,(0,d.yB)(d.qN))))},99466:(e,t,r)=>{r.d(t,{U:()=>i});var n=r(1e3);function i(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];const s=t.shift(),o=t.length>1?i(...t):t[0];return Object.fromEntries(n.zt.map((e=>[e,Object.assign({},s[e],o[e])])))}},75561:(e,t,r)=>{r.d(t,{K:()=>s,X:()=>i});var n=r(71371);const i={1:n.D4,2:n.G_,4:n.s6};function s(e,t,r){if(e.mediaType)return;const n=r.mediaType;if(!n&&!i.hasOwnProperty(t.mtype))throw new Error("Cannot determine mediaType for response");e.mediaType=n||i[t.mtype]}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[15889],{54406:(e,r,t)=>{t.d(r,{FE:()=>i});var n=t(70433),s=t(63172),o=t(91069);const u=Object.freeze([...["device.sua","source.schain","regs.gdpr","regs.us_privacy","regs.gpp","regs.gpp_sid","user.consent","user.eids"].map((e=>function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(e,r)=>`${e}.ext.${r}`;const[t,o]=function(e){const r=e.split(".");return[r.slice(0,r.length-1).join("."),r[r.length-1]]}(e);return r=r(t,o),e=>{const u=(0,n.A)(e,t);if(null!=u?.[o])return(0,s.J)(e,r,u[o]),()=>delete u[o]}}(e))),...["app","content","site","user"].map((function(e){return r=>{const t=r[e]?.kwarray;if(null!=t){let n=(r[e].keywords||"").split(",");return Array.isArray(t)&&n.push(...t),r[e].keywords=n.join(","),()=>delete r[e].kwarray}}}))]);function i(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;return function(t){return r.forEach((r=>{try{const n=r(t);"function"==typeof n&&e&&n()}catch(e){(0,o.logError)("Error translating request to ORTB 2.5",e)}})),t}}i()}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[33005],{51252:(t,n,o)=>{o.d(n,{Cn:()=>w,eu:()=>r,ho:()=>f,mw:()=>a,n9:()=>u,p:()=>s,ph:()=>l});var e=o(73858),g=o(15901),d=o(91069),i=o(70433);function a(t){return n=>(0,d.compareCodeAndSlot)(n,t)}function l(t,n){if(!t||"string"!=typeof t)return!1;window.googletag=window.googletag||{cmd:[]},window.googletag.cmd=window.googletag.cmd||[],window.googletag.cmd.push((()=>{window.googletag.pubads().setTargeting(t,n)}))}function u(t){let n;return(0,d.isGptPubadsDefined)()&&(n=(0,g.I6)(window.googletag.pubads().getSlots(),a(t))),n}function s(t){const n=u(t);return n?{gptSlot:n.getAdUnitPath(),divId:n.getSlotElementId()}:{}}const w=["IAB_AUDIENCE_1_1","IAB_CONTENT_2_2"];function r(t){return Object.entries({[w[0]]:c(t,["user.data"],4),[w[1]]:c(t,e.Dy.map((t=>`${t}.content.data`)),6)}).map((t=>{let[n,o]=t;return o.length?{taxonomy:n,values:o}:null})).filter((t=>t))}function c(t,n,o){return n.flatMap((n=>(0,i.A)(t,n)||[])).filter((t=>t.ext?.segtax===o)).flatMap((t=>t.segment?.map((t=>t.id)))).filter((t=>t)).filter(d.uniques)}function f(t){!function(t,n){const o=()=>window.googletag.pubads().addEventListener(t,n);(0,d.isGptPubadsDefined)()?o():(window.googletag=window.googletag||{},window.googletag.cmd=window.googletag.cmd||[],window.googletag.cmd.push(o))}("slotRenderEnded",t)}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[41225],{76743:(n,r,t)=>{t.d(r,{hZ:()=>o,x4:()=>u});var e=t(7873),l=t(12713);function o(n,r,t){let l=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(r===t)return n;let o=n;try{o=(0,e.m)().convertCurrency(n,r,t)}catch(n){if(!l)throw n}return o}function u(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n=>[n.cpm,n.currency],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:o;return function(e,l){return null==n&&(n=l),t(e,l,n,r)}}();return(0,l.NV)((t=>r.apply(null,n(t))))}},53812:(n,r,t)=>{t.d(r,{M:()=>o});var e=t(91069),l=t(70433);function o(n){if(!e.isFn(n.getFloor))return l.A(n,"params.bidfloor",0);try{const r=n.getFloor({currency:"USD",mediaType:"*",size:"*"});return r?.floor}catch(n){return 0}}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[35957],{86400:(s,e,t)=>{function n(){const s=[];return{submit(e,t,n){const u=[t,setTimeout((()=>{s.splice(s.indexOf(u),1),n()}),e)];s.push(u)},resume(){for(;s.length;){const[e,t]=s.shift();clearTimeout(t),e()}}}}t.d(e,{L:()=>n})}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[47618],{34610:(e,i,t)=>{t.d(i,{m:()=>m});var d=t(99466),r=t(1e3),n=t(70433),s=t(91069),a=t(25825),b=t(11445),o=t(43272),p=t(63172);var c=t(43323);var f=t(7873);var g=t(71371);const l={[r.S3]:{extPrebid:{fn:function(e,i){(0,p.J)(e,"ext.prebid",(0,s.mergeDeep)({auctiontimestamp:i.auctionStart,targeting:{includewinners:!0,includebidderkeys:!1}},e.ext?.prebid)),o.$W.getConfig("debug")&&(e.ext.prebid.debug=!0)}},extPrebidChannel:{fn:function(e){(0,p.J)(e,"ext.prebid.channel",Object.assign({name:"pbjs",version:(0,f.m)().version},e.ext?.prebid?.channel))}},extPrebidAliases:{fn:function(e,i,t){let{am:d=b.Ay}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(d.aliasRegistry[i.bidderCode]){const t=d.bidderRegistry[i.bidderCode];if(!t||!t.getSpec().skipPbsAliasing){(0,p.J)(e,`ext.prebid.aliases.${i.bidderCode}`,d.aliasRegistry[i.bidderCode]);const r=o.$W.getConfig(`gvlMapping.${i.bidderCode}`)||t?.getSpec?.().gvlid;r&&(0,p.J)(e,`ext.prebid.aliasgvlids.${i.bidderCode}`,r)}}}}},[r.Tb]:{params:{fn:c.W},adUnitCode:{fn:function(e,i){const t=i.adUnitCode;t&&(0,p.J)(e,"ext.prebid.adunitcode",t)}}},[r.WR]:{mediaType:{fn:a.o,priority:99},videoCache:{fn:function(e,i){if(e.mediaType===g.G_){let{cacheId:t,url:d}=(0,n.A)(i,"ext.prebid.cache.vastXml")||{};if(!t||!d){const{hb_uuid:e,hb_cache_host:r,hb_cache_path:s}=(0,n.A)(i,"ext.prebid.targeting")||{};e&&r&&s&&(t=e,d=`https://${r}${s}?uuid=${e}`)}t&&d&&Object.assign(e,{videoCacheKey:t,vastUrl:d})}},priority:-10},bidderCode:{fn(e,i,t){e.bidderCode=t.seatbid.seat,e.adapterCode=(0,n.A)(i,"ext.prebid.meta.adaptercode")||t.bidRequest?.bidder||e.bidderCode}},pbsBidId:{fn(e,i){const t=(0,n.A)(i,"ext.prebid.bidid");(0,s.isStr)(t)&&(e.pbsBidId=t)}},adserverTargeting:{fn(e,i){const t=(0,n.A)(i,"ext.prebid.targeting");(0,s.isPlainObject)(t)&&(e.adserverTargeting=t)}},extPrebidMeta:{fn(e,i){e.meta=(0,s.mergeDeep)({},(0,n.A)(i,"ext.prebid.meta"),e.meta)}},pbsWurl:{fn(e,i){const t=(0,n.A)(i,"ext.prebid.events.win");(0,s.isStr)(t)&&(e.pbsWurl=t)}}},[r.Cf]:{serverSideStats:{fn(e,i,t){Object.entries({errors:"serverErrors",responsetimemillis:"serverResponseTimeMs"}).forEach((e=>{let[d,r]=e;const s=(0,n.A)(i,`ext.${d}.${t.bidderRequest.bidderCode}`);s&&(t.bidderRequest[r]=s,t.bidRequests.forEach((e=>e[r]=s)))}))}}}};var u=t(89766);const m=(0,s.memoize)((()=>(0,d.U)((0,u.T)(),l,(0,r.yB)(r.e4))))},25825:(e,i,t)=>{t.d(i,{o:()=>s,s:()=>n});var d=t(71371),r=t(75561);const n={[d.D4]:"banner",[d.s6]:"native",[d.G_]:"video"};function s(e,i,t){let s=t.mediaType;s||(s=r.X.hasOwnProperty(i.mtype)?r.X[i.mtype]:i.ext?.prebid?.type,n.hasOwnProperty(s)||(s=d.D4)),e.mediaType=s}},43323:(e,i,t)=>{t.d(i,{W:()=>r});var d=t(63172);function r(e,i){let t=i.params;t&&(0,d.J)(e,`ext.prebid.bidder.${i.bidder}`,t)}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[46550],{8702:(p,n,e)=>{function t(p,n,e){let t={};return p&&("boolean"==typeof p.gdprApplies&&(t.gdpr=Number(p.gdprApplies)),"string"==typeof p.consentString&&(t.gdpr_consent=p.consentString)),n&&(t.us_privacy=encodeURIComponent(n)),e?.gppString&&(t.gpp=e.gppString,t.gpp_sid=e.applicableSections?.toString()),t}e.d(n,{d:()=>t})}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[14332],{87316:(e,t,r)=>{var s=r(7873),n=r(57377),a=r(71371),i=r(91069),d=r(63172),o=r(70433),p=r(43272),m=r(95789),u=r(29495);const{getConfig:c}=p.$W,l={code:"adf",aliases:[{code:"adformOpenRTB",gvlid:50},{code:"adform",gvlid:50}],gvlid:50,supportedMediaTypes:[a.s6,a.D4,a.G_],isBidRequestValid:e=>{const t=e.params||{},{mid:r,inv:s,mname:n}=t;return!!(r||s&&n)},buildRequests:(e,t)=>{let r,s;const n=t.ortb2||{};let a=n.user||{};"object"==typeof c("app")?(r=c("app")||{},n.app&&(0,i.mergeDeep)(r,n.app)):(s=c("site")||{},n.site&&(0,i.mergeDeep)(s,n.site),s.page||(s.page=t.refererInfo.page));let p=c("device")||{};n.device&&(0,i.mergeDeep)(p,n.device),p.w=p.w||window.innerWidth,p.h=p.h||window.innerHeight,p.ua=p.ua||navigator.userAgent;let m=n.source||{};m.fd=1;let l=n.regs||{};const v=(0,i.setOnAny)(e,"params.adxDomain")||"adx.adform.net",b=(0,i.setOnAny)(e,"params.pt")||(0,i.setOnAny)(e,"params.priceType")||"net",f=(0,i.setOnAny)(e,"params.test"),g=(0,u.b)(t),y=g&&[g],h=(0,i.setOnAny)(e,"userIdAsEids"),A=(0,i.setOnAny)(e,"schain");h&&(0,d.J)(a,"ext.eids",h),A&&(0,d.J)(m,"ext.schain",A);const I=e.map(((e,t)=>{e.netRevenue=b;const r=e.getFloor?e.getFloor({currency:g||"USD",size:"*",mediaType:"*"}):{},s=r?.floor,n=r?.currency,{mid:a,inv:d,mname:p}=e.params,m=e.ortb2Imp?.ext?.data,u={id:t+1,tagid:a,bidfloor:s,bidfloorcur:n,ext:{data:m,bidder:{inv:d,mname:p}}};if(e.nativeOrtbRequest&&e.nativeOrtbRequest.assets){let t=e.nativeOrtbRequest.assets,r=[];for(let e=0;e<t.length;e++){let s=(0,i.deepClone)(t[e]),n=s.img;if(n){let e=n.ext&&n.ext.aspectratios;if(e){let t=parseInt(e[0].split(":")[0],10),r=parseInt(e[0].split(":")[1],10);n.wmin=n.wmin||0,n.hmin=r*n.wmin/t|0}}r.push(s)}u.native={request:{assets:r}}}const c=(0,o.A)(e,"mediaTypes.banner");if(c&&c.sizes){const e=(0,i.parseSizesInput)(c.sizes).map((e=>{const[t,r]=e.split("x");return{w:parseInt(t,10),h:parseInt(r,10)}}));u.banner={format:e}}const l=(0,o.A)(e,"mediaTypes.video");return l&&(u.video=l),u})),O={id:t.bidderRequestId,site:s,app:r,user:a,device:p,source:m,ext:{pt:b},cur:y,imp:I,regs:l};return f&&(O.is_debug=!!f,O.test=1),{method:"POST",url:"https://"+v+"/adx/openrtb",data:JSON.stringify(O),bids:e}},interpretResponse:function(e,t){let{bids:r}=t;if(!e.body)return;const{seatbid:s,cur:n}=e.body,i=(d=s.map((e=>e.bid)),[].concat(...d)).reduce(((e,t)=>(e[t.impid-1]=t,e)),[]);var d;return r.map(((e,t)=>{const r=i[t];if(r){const t=(0,o.A)(r,"ext.prebid.type"),s=(0,o.A)(r,"ext.dsa"),i={requestId:e.bidId,cpm:r.price,creativeId:r.crid,ttl:360,netRevenue:"net"===e.netRevenue,currency:n,mediaType:t,width:r.w,height:r.h,dealId:r.dealid,meta:{mediaType:t,advertiserDomains:r.adomain,dsa:s,primaryCatId:r.cat?.[0],secondaryCatIds:r.cat?.slice(1)}};return r.native?i.native={ortb:r.native}:t===a.G_?(i.vastXml=r.adm,r.nurl&&(i.vastUrl=r.nurl)):i.ad=r.adm,e.renderer||t!==a.G_||"outstream"!==(0,o.A)(e,"mediaTypes.video.context")||(i.renderer=m.A4.install({id:e.bidId,url:"https://s2.adform.net/banners/scripts/video/outstream/render.js",adUnitCode:e.adUnitCode}),i.renderer.setRender(v)),i}})).filter(Boolean)}};function v(e){e.renderer.push((()=>{window.Adform.renderOutstream(e)}))}(0,n.a$)(l),(0,s.E)("adfBidAdapter")}},e=>{e.O(0,[19147,60802,51085],(()=>{return t=87316,e(e.s=t);var t}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[50678],{37510:(e,t,n)=>{var i=n(7873),r=n(70433),a=n(91069),o=n(57377),s=n(43272),c=n(15901),d=n(71371),p=n(12449);const u="USD",l={image:{required:!0},title:{required:!0},sponsoredBy:{required:!0},clickUrl:{required:!0},body:{required:!1},icon:{required:!1},cta:{required:!1}},m={code:"adyoulike",gvlid:259,supportedMediaTypes:[d.D4,d.s6,d.G_],aliases:["ayl"],isBidRequestValid:function(e){const t=b(I(e)),n=t.width>0&&t.height>0;return e.params&&e.params.placement&&(n||e.mediaTypes&&e.mediaTypes.native)},buildRequests:function(e,t){e=(0,p.Xj)(e);let n,i=!1;const a={Version:"1.0",Bids:e.reduce(((e,t)=>{let a=function(e){if((0,r.A)(e,"mediaTypes.banner"))return d.D4;if((0,r.A)(e,"mediaTypes.video"))return d.G_;if((0,r.A)(e,"mediaTypes.native"))return d.s6}(t),o=I(t),s=b(o);if(e[t.bidId]={},e[t.bidId].PlacementID=t.params.placement,e[t.bidId].TransactionID=t.ortb2Imp?.ext?.tid,e[t.bidId].Width=s.width,e[t.bidId].Height=s.height,e[t.bidId].AvailableSizes=o.join(","),"function"==typeof t.getFloor&&(e[t.bidId].Pricing=function(e,t,n){const i=e.getFloor({currency:u,mediaType:n,size:[t.width,t.height]});if(!isNaN(i?.floor)&&i?.currency===u)return i.floor}(t,s,a)),t.schain&&(e[t.bidId].SChain=t.schain),!n&&t.userIdAsEids&&t.userIdAsEids.length&&(n=t.userIdAsEids),a===d.s6){let n=t.mediaTypes.native;"image"===n.type&&(n=Object.assign({},l,n)),n.clickUrl={required:!0},e[t.bidId].Native=n}if(a===d.G_){i=!0,e[t.bidId].Video=t.mediaTypes.video;const n=t.mediaTypes.video.playerSize;Array.isArray(n)&&!Array.isArray(n[0])&&(e[t.bidId].Video.playerSize=[n])}return e}),{}),PageRefreshed:g()};t.gdprConsent&&(a.gdprConsent={consentString:t.gdprConsent.consentString,consentRequired:"boolean"==typeof t.gdprConsent.gdprApplies?t.gdprConsent.gdprApplies:null}),t.uspConsent&&(a.uspConsent=t.uspConsent),t.ortb2&&(a.ortb2=t.ortb2),n&&(a.eids=n),a.pbjs_version="9.28.0-pre";const o=JSON.stringify(a);return{method:"POST",url:h(e,t,i),data:o,options:{withCredentials:!0}}},interpretResponse:function(e,t){const n=[];var i={};if(!e||!e.body)return n;try{i=JSON.parse(t.data).Bids}catch(e){}return e.body.forEach((e=>{const t=function(e,t){if(!e||!e.Ad&&!e.Native&&!e.Vast)return;const n=t&&t[e.BidID];n&&(e.Width&&"0"!==e.Width||(e.Width=n.Width),e.Height&&"0"!==e.Height||(e.Height=n.Height));const i={requestId:e.BidID,ttl:3600,creativeId:e.CreativeID,cpm:e.Price,netRevenue:!0,currency:u,meta:e.Meta||{advertiserDomains:[]}},a=e.Vast;a?(i.width=e.Width,i.height=e.Height,i.vastXml=window.atob(a),i.mediaType="video"):n.Native?(i.native=function(e,t){if("object"==typeof e.Native)return e.Native;const n={};var i={},a={};if("string"==typeof e.Ad){i=JSON.parse(e.Ad.match(/\/\*PREBID\*\/(.*)\/\*PREBID\*\//)[1]),a=i.Content.Preview.Text;var o=i.TrackingPrefix+"/pixel?event_kind=IMPRESSION&attempt="+i.Attempt,s=i.TrackingPrefix+"/pixel?event_kind=INSERTION&attempt="+i.Attempt;i.Campaign&&(o+="&campaign="+i.Campaign,s+="&campaign="+i.Campaign),n.clickUrl=i.TrackingPrefix+"/ar?event_kind=CLICK&attempt="+i.Attempt+"&campaign="+i.Campaign+"&url="+encodeURIComponent(i.Content.Landing.Url),i.OnEvents?(n.clickTrackers=C(i.OnEvents.CLICK),n.impressionTrackers=C(i.OnEvents.IMPRESSION),n.javascriptTrackers=C(i.OnEvents.IMPRESSION,!0)):n.impressionTrackers=[],n.impressionTrackers.push(o,s)}return Object.keys(t).map((function(o,s){switch(o){case"title":n[o]=a.TITLE;break;case"body":n[o]=a.DESCRIPTION;break;case"cta":n[o]=a.CALLTOACTION;break;case"sponsoredBy":n[o]=i.Content.Preview.Sponsor.Name;break;case"image":const s=t.image.sizes||[];s.length||(s[0]=e.Width||300,s[1]=e.Height||250);const c=v(i,(0,r.A)(i,"Content.Preview.Thumbnail.Image"),s[0],s[1]);c&&(n[o]={url:c,width:s[0],height:s[1]});break;case"icon":const d=t.icon.sizes||[];d.length||(d[0]=50,d[1]=50);const p=v(i,(0,r.A)(i,"Content.Preview.Sponsor.Logo.Resource"),d[0],d[1]);p&&(n[o]={url:p,width:d[0],height:d[1]});break;case"privacyIcon":n[o]=v(i,(0,r.A)(i,"Content.Preview.Credit.Logo.Resource"),25,25);break;case"privacyLink":n[o]=(0,r.A)(i,"Content.Preview.Credit.Url")}})),n}(e,n.Native),i.mediaType="native"):(i.width=e.Width,i.height=e.Height,i.ad=e.Ad);return i}(e,i);t&&n.push(t)})),n},getUserSyncs:function(e,t,n,i,r){if(!e.iframeEnabled)return[];let a="";return n&&(a+="&gdpr="+(n.gdprApplies?1:0),a+="&gdpr_consent="+encodeURIComponent(n.consentString||"")),!0===s.$W.getConfig("coppa")&&(a+="&coppa=1"),i&&(a+="&us_privacy="+encodeURIComponent(i)),r?.gppString&&r?.applicableSections?.length&&(a+="&gpp="+encodeURIComponent(r.gppString),a+="&gpp_sid="+encodeURIComponent(r?.applicableSections?.join(","))),[{type:"iframe",url:`https://visitor.omnitagjs.com/visitor/isync?uid=19340f4f097d16f41f34fc0274981ca4${a}`}]}};function g(){try{if(performance&&performance.navigation)return performance.navigation.type===performance.navigation.TYPE_RELOAD}catch(e){}return!1}function h(e,t,n){let i=function(e){let t=(0,c.I6)(e,(e=>e.params.DC));return t?"-"+t.params.DC:""}(e);const r=n?"/hb-api/prebid-video/v1":"/hb-api/prebid/v1";return(0,a.buildUrl)({protocol:"https",host:`hb-api${i}.omnitagjs.com`,pathname:r,search:f(t)})}function f(e){const t={};if(e){const n=e.refererInfo;n&&(n.location&&(t.RefererUrl=encodeURIComponent(n.location),n.reachedTop||(t.SafeFrame=!0)),t.PageUrl=encodeURIComponent(n.topmostLocation),t.PageReferrer=encodeURIComponent(n.location));const i=e.ortb2?.site;i&&(t.PageUrl=encodeURIComponent(i.page||n?.topmostLocation),t.PageReferrer=encodeURIComponent(i.ref||n?.location))}const n=e?.refererInfo?.canonicalUrl;return n&&(t.CanonicalUrl=encodeURIComponent(n)),t}function I(e){let t=e.sizes||[];return e.mediaTypes&&e.mediaTypes.banner&&(t=e.mediaTypes.banner.sizes||[]),e.params&&Array.isArray(e.params.size)&&(t=e.params.size,Array.isArray(t[0])||(t=[t])),(0,a.parseSizesInput)(t)}function b(e){const t={},n=e[0];if("string"!=typeof n)return t;const i=n.toUpperCase().split("X"),r=parseInt(i[0],10);r&&(t.width=r);const a=parseInt(i[1],10);return a&&(t.height=a),t}function y(e){return e?"https://blobs.omnitagjs.com/blobs/"+e.substr(16,2)+"/"+e.substr(16)+"/"+e:""}function v(e,t,n,i){let r="";if(t&&t.Kind)switch(t.Kind){case"INTERNAL":r=y(t.Data.Internal.BlobReference.Uid);break;case"EXTERNAL":const a=e.DynamicPrefix;let o=t.Data.External.Url;if(o=o.replace(/\[height\]/i,""+i),o=o.replace(/\[width\]/i,""+n),o.indexOf(a)>=0){const e=/.*url=([^&]*)/gm.exec(o);r=e?e[1]:"",r||(r=y(/.*key=([^&]*)/gm.exec(o)[1]))}else r=o}return r}function C(e,t){const n=[];return e?(e.map(((e,i)=>{(t&&"JAVASCRIPT_URL"===e.Kind||!t&&"PIXEL_URL"===e.Kind)&&n.push(e.Url)})),n):n}(0,o.a$)(m),(0,i.E)("adyoulikeBidAdapter")}},e=>{e.O(0,[60802,51085],(()=>{return t=37510,e(e.s=t);var t}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[44599],{50791:(e,r,t)=>{t.d(r,{I:()=>q});var i=t(7873),a=t(91069),s=t(70433),o=t(95789),n=t(43272),d=t(57377),c=t(71371),p=t(15901),l=t(63895),u=t(12938),m=t(12693),g=t(82621),h=t(12449),_=t(24673),f=t(554),b=t(2349),y=t(25761),v=t(29906);const k="appnexus",w="https://ib.adnxs.com/ut/v3/prebid",I="https://ib.adnxs-simple.com/ut/v3/prebid",x=["id","minduration","maxduration","skippable","playback_method","frameworks","context","skipoffset"],C=["minduration","maxduration","skip","skipafter","playbackmethod","api","startdelay","placement","plcmt"],A=["age","externalUid","external_uid","segments","gender","dnt","language"],S=["geo","device_id"],T=["enabled","dongle","member_id","debug_timeout"],U={apn_debug_dongle:"dongle",apn_debug_member_id:"member_id",apn_debug_timeout:"debug_timeout"},O={playback_method:{unknown:0,auto_play_sound_on:1,auto_play_sound_off:2,click_to_play:3,mouse_over:4,auto_play_sound_unknown:5},context:{unknown:0,pre_roll:1,mid_roll:2,post_roll:3,outstream:4,"in-banner":5,"in-feed":6,interstitial:7,accompanying_content_pre_roll:8,accompanying_content_mid_roll:9,accompanying_content_post_roll:10}},E={body:"description",body2:"desc2",cta:"ctatext",image:{serverName:"main_image",requiredParams:{required:!0}},icon:{serverName:"icon",requiredParams:{required:!0}},sponsoredBy:"sponsored_by",privacyLink:"privacy_link",salePrice:"saleprice",displayUrl:"displayurl"},j="<script",N=/\/\/cdn\.adnxs\.com\/v|\/\/cdn\.adnxs\-simple\.com\/v/,D="trk.js",R=(0,u.vM)({bidderCode:k}),P=new Map([[1,"Mobile/Tablet - General"],[2,"Personal Computer"],[3,"Connected TV"],[4,"Phone"],[5,"Tablet"],[6,"Connected Device"],[7,"Set Top Box"],[8,"OOH Device"]]),q={code:k,gvlid:32,aliases:b.DX,supportedMediaTypes:[c.D4,c.G_,c.s6],isBidRequestValid:function(e){return!!(e.params.placementId||e.params.placement_id||e.params.member&&(e.params.invCode||e.params.inv_code))},buildRequests:function(e,r){const t=(e=(0,h.Xj)(e)).map(M),i=(0,p.I6)(e,B);let s={};!0===n.$W.getConfig("coppa")&&(s={coppa:!0}),i&&Object.keys(i.params.user).filter((e=>(0,p.mK)(A,e))).forEach((e=>{let r=(0,b.vk)(e);if("segments"===e&&(0,a.isArray)(i.params.user[e])){let t=[];i.params.user[e].forEach((e=>{(0,a.isNumber)(e)?t.push({id:e}):(0,a.isPlainObject)(e)&&t.push(e)})),s[r]=t}else"segments"!==e&&(s[r]=i.params.user[e])}));const o=(0,p.I6)(e,G);let d;o&&o.params&&o.params.app&&(d={},Object.keys(o.params.app).filter((e=>(0,p.mK)(S,e))).forEach((e=>d[e]=o.params.app[e])));const c=(0,p.I6)(e,K);let l;c&&c.params&&o.params.app&&o.params.app.id&&(l={appid:c.params.app.id});let u={},m={};const _=R.getCookie("apn_prebid_debug")||null;if(_)try{u=JSON.parse(_)}catch(e){(0,a.logError)("AppNexus Debug Auction Cookie Error:\n\n"+e)}else{Object.keys(U).forEach((e=>{let r=(0,a.getParameterByName)(e);(0,a.isStr)(r)&&""!==r&&(u[U[e]]=r,u.enabled=!0)})),u=(0,y.$)({member_id:"number",debug_timeout:"number"},u);const r=(0,p.I6)(e,L);r&&r.debug&&(u=r.debug)}u&&u.enabled&&Object.keys(u).filter((e=>(0,p.mK)(T,e))).forEach((e=>{m[e]=u[e]}));const k=(0,p.I6)(e,$),x=k?parseInt(k.params.member,10):0,C=e[0].schain,O=(0,p.I6)(e,H),E={tags:[...t],user:s,sdk:{source:"pbjs",version:"9.28.0-pre"},schain:C};O&&(E.iab_support={omidpn:"Appnexus",omidpv:"9.28.0-pre"}),x>0&&(E.member_id=x),o&&(E.device=d),c&&(E.app=l),r?.ortb2?.device&&(E.device=E.device||{},(0,a.mergeDeep)(E.device,function(e){const r={useragent:e.ua,devicetype:P.get(e.devicetype),make:e.make,model:e.model,os:e.os,os_version:e.osv,w:e.w,h:e.h,ppi:e.ppi,pxratio:e.pxratio};return Object.keys(r).reduce(((e,t)=>(r[t]&&(e[t]=r[t]),e)),{})}(r.ortb2.device)));let j=(0,a.deepClone)(r&&r.ortb2),N=(0,a.deepClone)(n.$W.getConfig("appnexusAuctionKeywords"))||{},D=(0,f.QF)(j,N);if(D.length>0&&(E.keywords=D),n.$W.getConfig("adpod.brandCategoryExclusion")&&(E.brand_category_uniqueness=!0),m.enabled&&(E.debug=m,(0,a.logInfo)("AppNexus Debug Auction Settings:\n\n"+JSON.stringify(m,null,4))),r&&r.gdprConsent&&(E.gdpr_consent={consent_string:r.gdprConsent.consentString,consent_required:r.gdprConsent.gdprApplies},r.gdprConsent.addtlConsent&&-1!==r.gdprConsent.addtlConsent.indexOf("~"))){let e=r.gdprConsent.addtlConsent,t=e.substring(e.indexOf("~")+1);E.gdpr_consent.addtl_consent=t.split(".").map((e=>parseInt(e,10)))}if(r&&r.uspConsent&&(E.us_privacy=r.uspConsent),r?.gppConsent?E.privacy={gpp:r.gppConsent.gppString,gpp_sid:r.gppConsent.applicableSections}:r?.ortb2?.regs?.gpp&&(E.privacy={gpp:r.ortb2.regs.gpp,gpp_sid:r.ortb2.regs.gpp_sid}),r&&r.refererInfo){let e={rd_ref:encodeURIComponent(r.refererInfo.topmostLocation),rd_top:r.refererInfo.reachedTop,rd_ifs:r.refererInfo.numIframes,rd_stk:r.refererInfo.stack.map((e=>encodeURIComponent(e))).join(",")},t=r.refererInfo.canonicalUrl;(0,a.isStr)(t)&&""!==t&&(e.rd_can=t),E.referrer_detection=e}(0,p.I6)(e,W)&&e.filter(W).forEach((e=>{const r=function(e,r){const{durationRangeSec:t,requireExactDuration:i}=r.mediaTypes.video,a=function(e){const{adPodDurationSec:r,durationRangeSec:t,requireExactDuration:i}=e,a=Math.min(...t),s=Math.floor(r/a);return i?Math.max(s,t.length):s}(r.mediaTypes.video),s=Math.max(...t),o=e.filter((e=>e.uuid===r.bidId));let n=(0,b.GS)(...o,a);if(i){const e=Math.ceil(a/t.length),r=(0,v.i)(n,e);t.forEach(((e,t)=>{r[t].map((r=>{V(r,"minduration",e),V(r,"maxduration",e)}))}))}else n.map((e=>V(e,"maxduration",s)));return n}(t,e),i=E.tags.filter((r=>r.uuid!==e.bidId));E.tags=[...i,...r]}));if(e[0].userId){let r=[];e[0].userIdAsEids.forEach((e=>{!e||!e.uids||e.uids.length<1||e.uids.forEach((t=>{let i={source:e.source,id:t.id};"adserver.org"==e.source?i.rti_partner="TDID":"uidapi.com"==e.source&&(i.rti_partner="UID2"),r.push(i)}))})),r.length&&(E.eids=r)}if(r?.ortb2?.regs?.ext?.dsa){const e=r.ortb2.regs.ext.dsa,t={};if(["dsarequired","pubrender","datatopub"].forEach((r=>{(0,a.isNumber)(e[r])&&(t[r]=e[r])})),(0,a.isArray)(e.transparency)&&e.transparency.every((e=>(0,a.isPlainObject)(e)))){const r=[];e.transparency.forEach((e=>{(0,a.isStr)(e.domain)&&""!=e.domain&&(0,a.isArray)(e.dsaparams)&&e.dsaparams.every((e=>(0,a.isNumber)(e)))&&r.push(e)})),r.length>0&&(t.transparency=r)}(0,a.isEmpty)(t)||(E.dsa=t)}t[0].publisher_id&&(E.publisher_id=t[0].publisher_id);const q=function(e,r){let t=[],i={withCredentials:!0},s=w;(0,g.C)(r?.gdprConsent)||(s=I);"TRUE"!==(0,a.getParameterByName)("apn_test").toUpperCase()&&!0!==n.$W.getConfig("apn_test")||(i.customHeaders={"X-Is-Test":1});if(e.tags.length>15){const o=(0,a.deepClone)(e);(0,v.i)(e.tags,15).forEach((e=>{o.tags=e;const a=JSON.stringify(o);t.push({method:"POST",url:s,data:a,bidderRequest:r,options:i})}))}else{const a=JSON.stringify(e);t={method:"POST",url:s,data:a,bidderRequest:r,options:i}}return t}(E,r);return q},interpretResponse:function(e,r){let{bidderRequest:t}=r;e=e.body;const i=[];if(!e||e.error){let r=`in response for ${t.bidderCode} adapter`;return e&&e.error&&(r+=`: ${e.error}`),(0,a.logError)(r),i}if(e.tags&&e.tags.forEach((e=>{const r=(n=e)&&n.ads&&n.ads.length&&(0,p.I6)(n.ads,(e=>e.rtb));var n;if(r){if((!0===m.u.get(t.bidderCode,"allowZeroCpmBids")?r.cpm>=0:r.cpm>0)&&(0,p.mK)(this.supportedMediaTypes,r.ad_type)){const n=function(e,r,t){const i=(0,a.getBidRequest)(e.uuid,[t]),n=(0,a.getUniqueIdentifierStr)(),d={adId:n,requestId:e.uuid,cpm:r.cpm,creativeId:r.creative_id,dealId:r.deal_id,currency:"USD",netRevenue:!0,ttl:300,adUnitCode:i.adUnitCode,appnexus:{buyerMemberId:r.buyer_member_id,dealPriority:r.deal_priority,dealCode:r.deal_code}};r.adomain&&(d.meta=Object.assign({},d.meta,{advertiserDomains:[r.adomain]}));r.advertiser_id&&(d.meta=Object.assign({},d.meta,{advertiserId:r.advertiser_id}));r.dsa&&(d.meta=Object.assign({},d.meta,{dsa:r.dsa}));function u(e){return{ver:"1.0",complete:0,nodes:[{bsid:e.buyer_member_id.toString()}]}}r.buyer_member_id&&(d.meta=Object.assign({},d.meta,{dchain:u(r)}));r.brand_id&&(d.meta=Object.assign({},d.meta,{brandId:r.brand_id}));if(r.rtb.video){Object.assign(d,{width:r.rtb.video.player_width,height:r.rtb.video.player_height,vastImpUrl:r.notify_url,ttl:3600});switch((0,s.A)(i,"mediaTypes.video.context")){case c.LM:const i=_.n[r.brand_category_id]?_.n[r.brand_category_id]:null;d.meta=Object.assign({},d.meta,{primaryCatId:i});const n=r.deal_priority;d.video={context:c.LM,durationSeconds:Math.floor(r.rtb.video.duration_ms/1e3),dealTier:n},d.vastUrl=r.rtb.video.asset_url;break;case l.H6:if(d.adResponse=e,d.adResponse.ad=d.adResponse.ads[0],d.adResponse.ad.video=d.adResponse.ad.rtb.video,d.vastXml=r.rtb.video.content,r.renderer_url){const i=(0,p.I6)(t.bids,(r=>r.bidId===e.uuid));let n=(0,s.A)(i,"mediaTypes.video.renderer.options");n||(n=(0,s.A)(i,"renderer.options")),d.renderer=function(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=o.A4.install({id:r.renderer_id,url:r.renderer_url,config:t,loaded:!1,adUnitCode:e});try{i.setRender(F)}catch(e){(0,a.logWarn)("Prebid Error calling setRender on renderer",e)}return i.setEventHandlers({impression:()=>(0,a.logMessage)("AppNexus outstream video impression event"),loaded:()=>(0,a.logMessage)("AppNexus outstream video loaded event"),ended:()=>{(0,a.logMessage)("AppNexus outstream renderer video event"),document.querySelector(`#${e}`).style.display="none"}}),i}(d.adUnitCode,r,n)}break;case l.mn:d.vastUrl=r.notify_url+"&redir="+encodeURIComponent(r.rtb.video.asset_url)}}else if(r.rtb[c.s6]){const e=r.rtb[c.s6];let t;if(function(e){if(!e||""===e)return!1;let r=e.match(N),t=null!=r&&r.length>=1,i=e.match(D),a=null!=i&&i.length>=1;return e.startsWith(j)&&a&&t}(r.viewability.config)){let e="pbjs_adid="+n+";pbjs_auc="+i.adUnitCode;t=r.viewability.config.replace("dom_id=%native_dom_id%",e)}let s=e.javascript_trackers;null==s?s=t:(0,a.isStr)(s)?s=[s,t]:s.push(t),d[c.s6]={title:e.title,body:e.desc,body2:e.desc2,cta:e.ctatext,rating:e.rating,sponsoredBy:e.sponsored,privacyLink:e.privacy_link,address:e.address,downloads:e.downloads,likes:e.likes,phone:e.phone,price:e.price,salePrice:e.saleprice,clickUrl:e.link.url,displayUrl:e.displayurl,clickTrackers:e.link.click_trackers,impressionTrackers:e.impression_trackers,video:e.video,javascriptTrackers:s},e.main_img&&(d[c.s6].image={url:e.main_img.url,height:e.main_img.height,width:e.main_img.width}),e.icon&&(d[c.s6].icon={url:e.icon.url,height:e.icon.height,width:e.icon.width}),d[c.s6].ext={video:e.video,customImage1:e.image1&&{url:e.image1.url,height:e.image1.height,width:e.image1.width},customImage2:e.image2&&{url:e.image2.url,height:e.image2.height,width:e.image2.width},customImage3:e.image3&&{url:e.image3.url,height:e.image3.height,width:e.image3.width},customImage4:e.image4&&{url:e.image4.url,height:e.image4.height,width:e.image4.width},customImage5:e.image5&&{url:e.image5.url,height:e.image5.height,width:e.image5.width},customIcon1:e.icon1&&{url:e.icon1.url,height:e.icon1.height,width:e.icon1.width},customIcon2:e.icon2&&{url:e.icon2.url,height:e.icon2.height,width:e.icon2.width},customIcon3:e.icon3&&{url:e.icon3.url,height:e.icon3.height,width:e.icon3.width},customIcon4:e.icon4&&{url:e.icon4.url,height:e.icon4.height,width:e.icon4.width},customIcon5:e.icon5&&{url:e.icon5.url,height:e.icon5.height,width:e.icon5.width},customSocialIcon1:e.socialicon1&&{url:e.socialicon1.url,height:e.socialicon1.height,width:e.socialicon1.width},customSocialIcon2:e.socialicon2&&{url:e.socialicon2.url,height:e.socialicon2.height,width:e.socialicon2.width},customSocialIcon3:e.socialicon3&&{url:e.socialicon3.url,height:e.socialicon3.height,width:e.socialicon3.width},customSocialIcon4:e.socialicon4&&{url:e.socialicon4.url,height:e.socialicon4.height,width:e.socialicon4.width},customSocialIcon5:e.socialicon5&&{url:e.socialicon5.url,height:e.socialicon5.height,width:e.socialicon5.width},customTitle1:e.title1,customTitle2:e.title2,customTitle3:e.title3,customTitle4:e.title4,customTitle5:e.title5,customBody1:e.body1,customBody2:e.body2,customBody3:e.body3,customBody4:e.body4,customBody5:e.body5,customCta1:e.ctatext1,customCta2:e.ctatext2,customCta3:e.ctatext3,customCta4:e.ctatext4,customCta5:e.ctatext5,customDisplayUrl1:e.displayurl1,customDisplayUrl2:e.displayurl2,customDisplayUrl3:e.displayurl3,customDisplayUrl4:e.displayurl4,customDisplayUrl5:e.displayurl5,customSocialUrl1:e.socialurl1,customSocialUrl2:e.socialurl2,customSocialUrl3:e.socialurl3,customSocialUrl4:e.socialurl4,customSocialUrl5:e.socialurl5}}else{Object.assign(d,{width:r.rtb.banner.width,height:r.rtb.banner.height,ad:r.rtb.banner.content});try{if(r.rtb.trackers)for(let e=0;e<r.rtb.trackers[0].impression_urls.length;e++){const t=r.rtb.trackers[0].impression_urls[e],i=(0,a.createTrackPixelHtml)(t);d.ad+=i}}catch(e){(0,a.logError)("Error appending tracking pixel",e)}}return d}(e,r,t);n.mediaType=function(e){const r=e.ad_type;return r===c.G_?c.G_:r===c.s6?c.s6:c.D4}(r),i.push(n)}}})),e.debug&&e.debug.debug_info){let r="AppNexus Debug Auction for Prebid\n\n"+e.debug.debug_info;r=r.replace(/(<td>|<th>)/gm,"\t").replace(/(<\/td>|<\/th>)/gm,"\n").replace(/^<br>/gm,"").replace(/(<br>\n|<br>)/gm,"\n").replace(/<h1>(.*)<\/h1>/gm,"\n\n===== $1 =====\n\n").replace(/<h[2-6]>(.*)<\/h[2-6]>/gm,"\n\n*** $1 ***\n\n").replace(/(<([^>]+)>)/gim,""),(0,a.logMessage)("https://console.appnexus.com/docs/understanding-the-debug-auction"),(0,a.logMessage)(r)}return i},getUserSyncs:function(e,r,t,i,a){if(e.iframeEnabled&&(0,g.C)(t))return[{type:"iframe",url:"https://acdn.adnxs.com/dmp/async_usersync.html"}];if(e.pixelEnabled){return["https://px.ads.linkedin.com/setuid?partner=appNexus"].map((e=>({type:"image",url:e})))}}};function M(e){const r={};Object.keys(e.params).forEach((r=>{let t=(0,b.vk)(r);t!==r&&(e.params[t]=e.params[r],delete e.params[r])})),r.sizes=z(e.sizes),r.primary_size=r.sizes[0],r.ad_types=[],r.uuid=e.bidId,e.params.placement_id?r.id=parseInt(e.params.placement_id,10):r.code=e.params.inv_code;const t=(0,a.getParameterByName)("ast_override_div");if((0,a.isStr)(t)&&""!==t){const i=decodeURIComponent(t).split(",").find((r=>r.startsWith(`${e.adUnitCode}:`)));if(i){const e=i.split(":")[1];e&&(r.force_creative_id=parseInt(e,10))}}r.allow_smaller_sizes=e.params.allow_smaller_sizes||!1,r.use_pmt_rule="boolean"==typeof e.params.use_payment_rule?e.params.use_payment_rule:"boolean"==typeof e.params.use_pmt_rule&&e.params.use_pmt_rule,r.prebid=!0,r.disable_psa=!0;let i=function(e){if(!(0,a.isFn)(e.getFloor))return e.params.reserve?e.params.reserve:null;let r=e.getFloor({currency:"USD",mediaType:"*",size:"*"});if((0,a.isPlainObject)(r)&&!isNaN(r.floor)&&"USD"===r.currency)return r.floor;return null}(e);if(i&&(r.reserve=i),e.params.position)r.position={above:1,below:2}[e.params.position]||0;else{let t=(0,s.A)(e,"mediaTypes.banner.pos")||(0,s.A)(e,"mediaTypes.video.pos");0!==t&&1!==t&&3!==t||(r.position=3===t?2:t)}e.params.traffic_source_code&&(r.traffic_source_code=e.params.traffic_source_code),e.params.private_sizes&&(r.private_sizes=z(e.params.private_sizes)),e.params.supply_type&&(r.supply_type=e.params.supply_type),e.params.pub_click&&(r.pubclick=e.params.pub_click),e.params.ext_inv_code&&(r.ext_inv_code=e.params.ext_inv_code),e.params.publisher_id&&(r.publisher_id=parseInt(e.params.publisher_id,10)),e.params.external_imp_id&&(r.external_imp_id=e.params.external_imp_id);const o=(0,f.T_)((0,f.gg)((0,s.A)(e,"ortb2Imp.ext.data.keywords")),e.params?.keywords);o.length>0&&(r.keywords=o);let n=(0,s.A)(e,"ortb2Imp.ext.gpid")||(0,s.A)(e,"ortb2Imp.ext.data.pbadslot");if(n&&(r.gpid=n),(e.mediaType===c.s6||(0,s.A)(e,`mediaTypes.${c.s6}`))&&(r.ad_types.push(c.s6),0===r.sizes.length&&(r.sizes=z([1,1])),e.nativeParams)){const t=function(e){const r={};return Object.keys(e).forEach((t=>{const i=E[t]&&E[t].serverName||E[t]||t,s=E[t]&&E[t].requiredParams;r[i]=Object.assign({},s,e[t]);if(!(i!==E.image.serverName&&i!==E.icon.serverName)&&r[i].sizes){let e=r[i].sizes;((0,a.isArrayOfNums)(e)||(0,a.isArray)(e)&&e.length>0&&e.every((e=>(0,a.isArrayOfNums)(e))))&&(r[i].sizes=z(r[i].sizes))}i===E.privacyLink&&(r.privacy_supported=!0)})),r}(e.nativeParams);r[c.s6]={layouts:[t]}}{const t=(0,s.A)(e,`mediaTypes.${c.G_}`),i=(0,s.A)(e,"mediaTypes.video.context");r.hb_source=t&&"adpod"===i?7:1,(e.mediaType===c.G_||t)&&r.ad_types.push(c.G_),(e.mediaType===c.G_||t&&"outstream"!==i)&&(r.require_asset_url=!0),e.params.video&&(r.video={},Object.keys(e.params.video).filter((e=>(0,p.mK)(x,e))).forEach((t=>{switch(t){case"context":case"playback_method":let i=e.params.video[t];i=(0,a.isArray)(i)?i[0]:i,r.video[t]=O[t][i];break;case"frameworks":break;default:r.video[t]=e.params.video[t]}})),e.params.video.frameworks&&(0,a.isArray)(e.params.video.frameworks)&&(r.video_frameworks=e.params.video.frameworks)),t&&(r.video=r.video||{},Object.keys(t).filter((e=>(0,p.mK)(C,e))).forEach((e=>{switch(e){case"minduration":case"maxduration":"number"!=typeof r.video[e]&&(r.video[e]=t[e]);break;case"skip":"boolean"!=typeof r.video.skippable&&(r.video.skippable=1===t[e]);break;case"skipafter":"number"!=typeof r.video.skipoffset&&(r.video.skippoffset=t[e]);break;case"playbackmethod":if("number"!=typeof r.video.playback_method){let i=t[e];i=(0,a.isArray)(i)?i[0]:i,i>=1&&i<=4&&(r.video.playback_method=i)}break;case"api":if(!r.video_frameworks&&(0,a.isArray)(t[e])){let i=t[e].map((e=>{let r=4===e?5:5===e?4:e;if(r>=1&&r<=5)return r})).filter((e=>e));r.video_frameworks=i}break;case"startdelay":case"plcmt":case"placement":if("number"!=typeof r.video.context){const e=t.plcmt,i=t.placement,a=t.startdelay,s=function(e,r){if(!e)return;if(2===e){if(void 0===r)return;if(0===r)return"accompanying_content_pre_roll";if(-1===r)return"accompanying_content_mid_roll";if(-2===r)return"accompanying_content_post_roll"}else{if(3===e)return"interstitial";if(4===e)return"outstream"}}(e,a)||function(e){if(!e)return;if(2===e)return"in-banner";if(3===e)return"outstream";if(4===e)return"in-feed";if(5===e)return"intersitial"}(i)||function(e){if(!e)return;if(0===e)return"pre_roll";if(-1===e)return"mid_roll";if(-2===e)return"post_roll"}(a);r.video.context=O.context[s]}}}))),e.renderer&&(r.video=Object.assign({},r.video,{custom_renderer_present:!0}))}return e.params.frameworks&&(0,a.isArray)(e.params.frameworks)&&(r.banner_frameworks=e.params.frameworks),(0,s.A)(e,`mediaTypes.${c.D4}`)&&r.ad_types.push(c.D4),0===r.ad_types.length&&delete r.ad_types,r}function z(e){let r=[],t={};if((0,a.isArray)(e)&&2===e.length&&!(0,a.isArray)(e[0]))t.width=parseInt(e[0],10),t.height=parseInt(e[1],10),r.push(t);else if("object"==typeof e)for(let i=0;i<e.length;i++){let a=e[i];t={},t.width=parseInt(a[0],10),t.height=parseInt(a[1],10),r.push(t)}return r}function B(e){return!!e.params.user}function $(e){return!!parseInt(e.params.member,10)}function G(e){if(e.params)return!!e.params.app}function K(e){return e.params&&e.params.app?!!e.params.app.id:!!e.params.app}function L(e){return!!e.debug}function W(e){return e.mediaTypes&&e.mediaTypes.video&&e.mediaTypes.video.context===c.LM}function H(e){let r=!1;const t=e.params,i=e.params.video;return t.frameworks&&(0,a.isArray)(t.frameworks)&&(r=(0,p.mK)(e.params.frameworks,6)),!r&&i&&i.frameworks&&(0,a.isArray)(i.frameworks)&&(r=(0,p.mK)(e.params.video.frameworks,6)),r}function V(e,r,t){(0,a.isEmpty)(e.video)&&(e.video={}),e.video[r]=t}function F(e,r){!function(e){try{const r=document.getElementById(e).querySelectorAll("div[id^='google_ads']");r[0]&&r[0].style.setProperty("display","none")}catch(e){}}(e.adUnitCode),function(e){try{const r=document.getElementById(e).querySelectorAll("script[id^='sas_script']");r[0].nextSibling&&"iframe"===r[0].nextSibling.localName&&r[0].nextSibling.style.setProperty("display","none")}catch(e){}}(e.adUnitCode),e.renderer.push((()=>{(r?.defaultView||window).ANOutstreamVideo.renderAd({tagId:e.adResponse.tag_id,sizes:[e.getSize().split("x")],targetId:e.adUnitCode,uuid:e.adResponse.uuid,adResponse:e.adResponse,rendererOptions:e.renderer.getConfig()},J.bind(null,e))}))}function J(e,r,t){e.renderer.handleVideoEvent({id:r,eventName:t})}(0,d.a$)(q),(0,i.E)("appnexusBidAdapter")}},e=>{e.O(0,[60802,97247,44982,95444,42698,12126,51085],(()=>{return r=50791,e(e.s=r);var r}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[21829],{91493:(e,t,i)=>{var a=i(7873),r=i(63172),n=i(91069),o=i(57377),s=i(71371),d=i(12938),p=i(27934),l=i(82621),c=i(95789),u=i(63895),m=i(68044),v=i(89766),g=i(54406);const y="criteo",f="https://grid-bidder.criteo.com/openrtb_2_5/pbjs/auction/request",b=(0,d.vM)({bidderCode:y}),h="Criteo: ",T=(0,g.FE)(),I="https://static.criteo.net/js/ld/publishertag.renderer.js",k="cto_optout",x="cto_bundle",P=(0,v.A)({context:{netRevenue:!0,ttl:60},imp:function(e,t,i){let a=e(t,i);const n=t.params;a.tagid=t.adUnitCode,(0,r.J)(a,"ext",{...t.params.ext,...a.ext,rwdd:a.rwdd,floors:U(t),bidder:{publishersubid:n?.publisherSubId,zoneid:n?.zoneId,uid:n?.uid}}),delete a.rwdd,!i.fledgeEnabled&&a.ext.igs?.ae&&delete a.ext.igs.ae;if(w(t)){const e=t.params.video;void 0!==e&&(0,r.J)(a,"video",{...a.video,skip:a.video.skip||e.skip||0,placement:a.video.placement||e.placement,minduration:a.video.minduration||e.minduration,playbackmethod:a.video.playbackmethod||e.playbackmethod,startdelay:a.video.startdelay||e.startdelay||0}),(0,r.J)(a,"video.ext",{context:t.mediaTypes.video.context,playersizes:E(t?.mediaTypes?.video?.playerSize,R),plcmt:t.mediaTypes.video.plcmt,poddur:t.mediaTypes.video.adPodDurationSec,rqddurs:t.mediaTypes.video.durationRangeSec})}if(a.native&&void 0!==a.native.request){let e=JSON.parse(a.native.request);e.assets&&(1!==e.assets.length||Object.keys(e.assets[0]).length)||delete e.assets,(0,r.J)(a,"native.request_native",e),delete a.native.request}return a},request:function(e,t,i,a){let n=e(t,i,a);void 0!==a.publisherId&&(void 0!==n.app?(0,r.J)(n,"app.publisher.id",a.publisherId):(0,r.J)(n,"site.publisher.id",a.publisherId));i&&i.gdprConsent&&(0,r.J)(n,"regs.ext.gdprversion",i.gdprConsent.apiVersion);return n=T(n),n},bidResponse:function(e,t,i){i.mediaType=t?.ext?.mediatype,i.mediaType===s.s6&&void 0!==t.adm_native&&(t.adm=t.adm_native,delete t.adm_native);let a=e(t,i);const{bidRequest:n}=i;a.currency=t?.ext?.cur,void 0!==t?.ext?.meta&&(0,r.J)(a,"meta",{...a.meta,...t.ext.meta});void 0!==t?.ext?.paf?.content_id&&(0,r.J)(a,"meta.paf.content_id",t.ext.paf.content_id);a.mediaType===s.G_&&(a.vastUrl=t.ext?.displayurl,n?.mediaTypes?.video?.context===u.H6&&(a.renderer=function(e){if(void 0===e.ext?.videoPlayerConfig||void 0===e.ext?.videoPlayerType)return;const t={documentResolver:(e,t,i)=>i??t},i=(t,i)=>{let a={slotid:e.id,vastUrl:e.ext?.displayurl,vastXml:e.adm,documentContext:i},r=e.ext.videoPlayerConfig;window.CriteoOutStream[e.ext.videoPlayerType].play(a,r)},a=c.A4.install({url:I,config:t});return a.setRender(i),a}(t)));return a},response:function(e,t,i,a){let n=e(t,i,a);const o=i?.ext?.paf?.transmission;return n.bids.forEach((e=>{void 0!==o&&void 0!==e?.meta?.paf?.content_id?(0,r.J)(e,"meta.paf.transmission",o):delete e.meta.paf})),n}});const S={code:y,gvlid:91,supportedMediaTypes:[s.D4,s.G_,s.s6],getUserSyncs:function(e,t,i,a){let r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},{gppString:o="",applicableSections:s=[]}=r;const d=(0,p.EN)(),c="criteoPrebidAdapter";if(e.iframeEnabled&&(0,l.C)(i)){const e=[];if(e.push(`origin=${c}`),e.push(`topUrl=${d.domain}`),i&&(i.gdprApplies&&e.push("gdpr="+(1==i.gdprApplies?1:0)),i.consentString&&e.push(`gdpr_consent=${i.consentString}`)),a&&e.push(`us_privacy=${a}`),e.push(`gpp=${o}`),Array.isArray(s))for(const t of s)e.push(`gpp_sid=${t}`);const t=Math.random().toString(),r={bundle:C(x),cw:b.cookiesAreEnabled(),lsw:b.localStorageIsEnabled(),optoutCookie:C(k),origin:c,requestId:t,tld:d.domain,topUrl:d.domain,version:"9.28.0-pre".replace(/\./g,"_")};window.addEventListener("message",(function e(i){if(!i.data||"https://gum.criteo.com"!=i.origin)return;if(i.data.requestId!==t)return;this.removeEventListener("message",e),i.stopImmediatePropagation();const a=i.data;a.optout?(_(x),q(k,!0,43200,d.domain)):(a.bundle&&q(x,a.bundle,9360,d.domain),a.callbacks&&a.callbacks.forEach(n.triggerPixel))}),!0);const p=JSON.stringify(r).replace(/"/g,"%22");return[{type:"iframe",url:`https://gum.criteo.com/syncframe?${e.join("&")}#${p}`}]}if(e.pixelEnabled&&(0,l.C)(i)){const e=[];if(e.push("profile=207"),i&&(!0===i.gdprApplies&&e.push("gdprapplies=true"),i.consentString&&e.push(`gdpr=${i.consentString}`)),a&&e.push(`ccpa=${a}`),e.push(`gpp=${o}`),Array.isArray(s))for(const t of s)e.push(`gpp_sid=${t}`);return[{type:"image",url:`https://ssp-sync.criteo.com/user-sync/redirect?${e.join("&")}`}]}return[]},isBidRequestValid:e=>!(!e||!e.params||!e.params.zoneId&&!e.params.networkId)&&!(w(e)&&!function(e){let t=!0;return["mimes","playerSize","maxduration","protocols","api","skip","placement","playbackmethod"].forEach((function(i){"placement"===i?void 0===e?.mediaTypes?.video?.[i]&&void 0===e?.params?.video?.[i]&&void 0===e?.mediaTypes?.video?.plcmt&&void 0===e?.params?.video?.plcmt&&(t=!1,(0,n.logError)("Criteo Bid Adapter: mediaTypes.video."+i+" or mediaTypes.video.plcmt is required")):void 0===e?.mediaTypes?.video?.[i]&&void 0===e?.params?.video?.[i]&&(t=!1,(0,n.logError)("Criteo Bid Adapter: mediaTypes.video."+i+" is required"))})),t}(e)),buildRequests:(e,t)=>{e.forEach((e=>{(function(e){return void 0!==e?.mediaTypes?.native})(e)&&(function(e){return!(e.nativeParams&&(e.nativeParams.image&&(!0!==e.nativeParams.image.sendId||!0===e.nativeParams.image.sendTargetingKeys)||e.nativeParams.icon&&(!0!==e.nativeParams.icon.sendId||!0===e.nativeParams.icon.sendTargetingKeys)||e.nativeParams.clickUrl&&(!0!==e.nativeParams.clickUrl.sendId||!0===e.nativeParams.clickUrl.sendTargetingKeys)||e.nativeParams.displayUrl&&(!0!==e.nativeParams.displayUrl.sendId||!0===e.nativeParams.displayUrl.sendTargetingKeys)||e.nativeParams.privacyLink&&(!0!==e.nativeParams.privacyLink.sendId||!0===e.nativeParams.privacyLink.sendTargetingKeys)||e.nativeParams.privacyIcon&&(!0!==e.nativeParams.privacyIcon.sendId||!0===e.nativeParams.privacyIcon.sendTargetingKeys)))}(e)||(0,n.logWarn)(h+"all native assets containing URL should be sent as placeholders with sendId(icon, image, clickUrl, displayUrl, privacyLink, privacyIcon)"),null==e?.nativeOrtbRequest?.assets&&((0,n.logWarn)(h+"native asset requirements are missing"),(0,r.J)(e,"nativeOrtbRequest.assets",[{}])))}));const i=function(e,t){const i=(0,n.parseUrl)(t?.refererInfo?.topmostLocation).search;return{url:t?.refererInfo?.page||"",debug:"1"===i.pbt_debug,noLog:"1"===i.pbt_nolog,fledgeEnabled:t.paapi?.enabled,amp:e.some((e=>"amp"===e.params.integrationMode)),networkId:e.find((e=>e.params?.networkId))?.params.networkId,publisherId:e.find((e=>e.params?.pubid))?.params.pubid}}(e,t),a=function(e){let t=f;t+="?profileId=207",t+="&av="+String(37),t+="&wv="+encodeURIComponent("9.28.0-pre"),t+="&cb="+String(Math.floor(99999999999*Math.random())),b.localStorageIsEnabled()?t+="&lsavail=1":t+="&lsavail=0";e.amp&&(t+="&im=1");e.debug&&(t+="&debug=1");e.noLog&&(t+="&nolog=1");const i=C(x);i&&(t+=`&bundle=${i}`);C(k)&&(t+="&optout=1");e.networkId&&(t+="&networkId="+e.networkId);return t}(i),o=P.toORTB({bidderRequest:t,bidRequests:e,context:i});if(o)return{method:"POST",url:a,data:o,bidRequests:e}},interpretResponse:(e,t)=>{if(void 0===e?.body)return[];const i=P.fromORTB({response:e.body,request:t.data}).bids||[],a=e.body?.ext?.igi?.filter((e=>(0,n.isArray)(e?.igs))).flatMap((e=>e.igs));return a?.length?{bids:i,paapi:a}:i},onDataDeletionRequest:e=>{const t=C(x);t&&(_(x),(0,m.RD)("https://privacy.criteo.com/api/privacy/datadeletionrequest",null,JSON.stringify({publisherUserId:t}),{contentType:"application/json",method:"POST"}))}};function C(e){const t=b.getCookie(e),i=b.getDataFromLocalStorage(e);return t||i||void 0}function q(e,t,i,a){const r=new Date;r.setTime(r.getTime()+60*i*60*1e3);const n=`expires=${r.toUTCString()}`,o=a.split(".");for(let i=0;i<o.length;++i){const a=o.slice(o.length-i-1,o.length).join(".");try{b.setCookie(e,t,n,null,"."+a);const i=b.getCookie(e);if(i&&i===t)break}catch(e){}}b.setDataInLocalStorage(e,t)}function _(e){b.setCookie(e,"",0),b.removeDataFromLocalStorage(e)}function E(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return null==e?[]:Array.isArray(e[0])?e.map((e=>t(e))):[t(e)]}function R(e){return e[0]+"x"+e[1]}function w(e){return void 0!==e?.mediaTypes?.video}function U(e){try{const t={},i=function(e){if(e.getFloor)return e.getFloor;if(e.params.bidFloor&&e.params.bidFloorCur)try{const t=parseFloat(e.params.bidFloor);return()=>({currency:e.params.bidFloorCur,floor:t})}catch{}}(e);if(i){if(e.mediaTypes?.banner){t.banner={};E(e?.mediaTypes?.banner?.sizes).forEach((a=>t.banner[R(a).toString()]=i.call(e,{size:a,mediaType:s.D4})))}if(e.mediaTypes?.video){t.video={};E(e?.mediaTypes?.video?.playerSize).forEach((a=>t.video[R(a).toString()]=i.call(e,{size:a,mediaType:s.G_})))}return e.mediaTypes?.native&&(t.native={},t.native["*"]=i.call(e,{size:"*",mediaType:s.s6})),t}}catch(e){(0,n.logError)("Could not parse floors from Prebid: "+e)}}(0,o.a$)(S),(0,a.E)("criteoBidAdapter")}},e=>{e.O(0,[60802,58498,15889,51085],(()=>{return t=91493,e(e.s=t);var t}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[53170],{77764:(e,t,i)=>{var r=i(7873),n=i(70433),o=i(91069),a=i(63172),s=i(71371),d=i(43272),p=i(12938),l=i(15901),c=i(57377),u=i(63895),m=i(95789),f=i(51252);const g="ix",b=[s.D4,s.G_,s.s6],x=50,h=[144,144],y={JPY:1},I={PBJS:"p",IX:"x"},v={SITE:["id","name","domain","cat","sectioncat","pagecat","page","ref","search","mobile","privacypolicy","publisher","content","keywords","ext"],USER:["id","buyeruid","yob","gender","keywords","customdata","geo","data","ext"]},A={"liveramp.com":"idl","netid.de":"NETID","neustar.biz":"fabrickId","zeotap.com":"zeotapIdPlus","uidapi.com":"UID2","adserver.org":"TDID"},C=["lipbid","criteoId","merkleId","parrableId","connectid","tapadId","quantcastId","pubProvidedId","pairId"],w=["mimes","minduration","maxduration"],O=["mimes","minduration","maxduration","protocols","protocol","startdelay","placement","linearity","skip","skipmin","skipafter","sequence","battr","maxextended","minbitrate","maxbitrate","boxingallowed","playbackmethod","playbackend","delivery","pos","companionad","api","companiontype","ext","playerSize","w","h","plcmt"],U=`${g}_features`,S=(0,p.vM)({bidderCode:g}),E={REQUESTED_FEATURE_TOGGLES:["pbjs_enable_multiformat","pbjs_allow_all_eids"],featureToggles:{},isFeatureEnabled:function(e){return(0,n.A)(this.featureToggles,`features.${e}.activated`,!1)},getFeatureToggles:function(){if(S.localStorageIsEnabled()){const e=(0,o.safeJSONParse)(S.getDataFromLocalStorage(U));(0,n.A)(e,"expiry")&&e.expiry>=(new Date).getTime()?this.featureToggles=e:this.clearFeatureToggles()}},setFeatureToggles:function(e){const t=e.body,i=new Date,r=(0,n.A)(t,"ext.features");r&&(this.featureToggles={expiry:i.setHours(i.getHours()+1),features:r},S.localStorageIsEnabled()&&S.setDataInLocalStorage(U,JSON.stringify(this.featureToggles)))},clearFeatureToggles:function(){this.featureToggles={},S.localStorageIsEnabled()&&S.removeDataFromLocalStorage(U)}};let P=0,T="",D="",_=!1;const k=2;function J(e){const t=j(e,s.G_),i=(0,n.A)(e,"mediaTypes.video"),r=(0,n.A)(e,"params.video");if($(i,r).length)return{};t.video=r?(0,o.deepClone)(e.params.video):{};let d=(0,n.A)(e,"ortb2Imp.ext.tid");d&&(0,a.J)(t,"ext.tid",d),function(e,t){if((0,n.A)(t,"mediaTypes.video.context")===u.H6){let i=(0,n.A)(t,"mediaTypes.video.renderer");if(i||(i=(0,n.A)(t,"renderer")),(0,n.A)(t,"schain",!1))e.displaymanager="pbjs_wrapper";else if(i&&"object"==typeof i){if(void 0!==i.url){let t="";try{t=new URL(i.url).hostname}catch{return}t.includes("js-sec.indexww")?e.displaymanager="ix":e.displaymanager=i.url}}else e.displaymanager="ix"}}(t,e),H(t,e);for(const e in i)-1===O.indexOf(e)||t.video.hasOwnProperty(e)||(t.video[e]=i[e]);if(t.video.minduration>t.video.maxduration)return(0,o.logError)(`IX Bid Adapter: video minduration [${t.video.minduration}] cannot be greater than video maxduration [${t.video.maxduration}]`),{};const p=r&&r.context||i&&i.context;if(function(e){e.video.hasOwnProperty("plcmt")&&(!(0,o.isInteger)(e.video.plcmt)||e.video.plcmt<1||e.video.plcmt>4)&&((0,o.logWarn)(`IX Bid Adapter: video.plcmt [${e.video.plcmt}] must be an integer between 1-4 inclusive`),delete e.video.plcmt)}(t),p&&!t.video.hasOwnProperty("placement")&&(p===u.mn?t.video.placement=1:p===u.H6?(0,n.A)(r,"playerConfig.floatOnScroll")?t.video.placement=5:(t.video.placement=3,_=!0):(0,o.logWarn)(`IX Bid Adapter: Video context '${p}' is not supported`)),!t.video.w||!t.video.h){const i=W((0,n.A)(t,"video.playerSize"))||W((0,n.A)(e,"params.size"));if(!i)return(0,o.logWarn)("IX Bid Adapter: Video size is missing in [mediaTypes.video]"),{};t.video.w=i[0],t.video.h=i[1]}return B(e,t,s.G_),t}function j(e,t){const i={};if(i.id=e.bidId,ne()&&(0,n.A)(e,"params.externalId")&&(0,a.J)(i,"ext.externalID",e.params.externalId),(0,n.A)(e,`params.${t}.siteId`)&&!isNaN(Number(e.params[t].siteId)))switch(t){case s.D4:(0,a.J)(i,"ext.siteID",e.params.banner.siteId.toString());break;case s.G_:(0,a.J)(i,"ext.siteID",e.params.video.siteId.toString());break;case s.s6:(0,a.J)(i,"ext.siteID",e.params.native.siteId.toString())}else e.params.siteId&&(0,a.J)(i,"ext.siteID",e.params.siteId.toString());return!e.params.hasOwnProperty("id")||"string"!=typeof e.params.id&&"number"!=typeof e.params.id||(0,a.J)(i,"ext.sid",String(e.params.id)),i}function B(e,t,i){let r=null,n=null;if(e.params.bidFloor&&e.params.bidFloorCur&&(r={floor:e.params.bidFloor,currency:e.params.bidFloorCur}),(0,o.isFn)(e.getFloor)){let r="*",a="*";if(i&&(0,o.contains)(b,i)){const{w:e,h:n}=t[i];r=i,a=[e,n]}try{n=e.getFloor({mediaType:r,size:a})}catch(e){(0,o.logWarn)("priceFloors module call getFloor failed, error : ",e)}}let d=!1;n?(t.bidfloor=n.floor,t.bidfloorcur=n.currency,(0,a.J)(t,"ext.fl",I.PBJS),d=!0):r&&(t.bidfloor=r.floor,t.bidfloorcur=r.currency,(0,a.J)(t,"ext.fl",I.IX),d=!0),d&&(i==s.D4?((0,a.J)(t,"banner.ext.bidfloor",t.bidfloor),(0,a.J)(t,"banner.ext.fl",t.ext.fl)):i==s.G_?((0,a.J)(t,"video.ext.bidfloor",t.bidfloor),(0,a.J)(t,"video.ext.fl",t.ext.fl)):((0,a.J)(t,"native.ext.bidfloor",t.bidfloor),(0,a.J)(t,"native.ext.fl",t.ext.fl)))}function F(e,t,i){const r={},a=!(!(0,n.A)(e,"exp")||!(0,o.isInteger)(e.exp)),d=(0,n.A)(e,"dealid")||(0,n.A)(e,"ext.dealid");y.hasOwnProperty(t)?r.cpm=e.price/y[t]:r.cpm=e.price/100,r.requestId=e.impid,d&&(r.dealId=d),r.netRevenue=true,r.currency=t,r.creativeId=e.hasOwnProperty("crid")?e.crid:"-",e.mtype!=k||(!e.ext||e.ext.vasturl)&&e.ext?e.ext&&e.ext.vasturl&&(r.vastUrl=e.ext.vasturl):r.vastXml=e.adm;let p=null;if("string"==typeof e.adm&&"{"===e.adm[0]&&"}"===e.adm[e.adm.length-1])try{p=JSON.parse(e.adm)}catch(e){(0,o.logWarn)("adm looks like JSON but failed to parse: ",e)}return e.ext&&e.ext.vasturl||e.mtype==k?(r.width=i.video.w,r.height=i.video.h,r.mediaType=s.G_,r.mediaTypes=i.mediaTypes,r.ttl=a?e.exp:3600):p&&p.native?(r.native={ortb:p.native},r.width=e.w?e.w:1,r.height=e.h?e.h:1,r.mediaType=s.s6,r.ttl=a?e.exp:3600):(r.ad=e.adm,r.width=e.w,r.height=e.h,r.mediaType=s.D4,r.ttl=a?e.exp:300),r.meta={},r.meta.networkId=(0,n.A)(e,"ext.dspid"),r.meta.brandId=(0,n.A)(e,"ext.advbrandid"),r.meta.brandName=(0,n.A)(e,"ext.advbrand"),e.adomain&&e.adomain.length>0&&(r.meta.advertiserDomains=e.adomain),e.ext?.dsa&&(r.meta.dsa=e.ext.dsa),r}function z(e){return Array.isArray(e)&&2===e.length&&(0,o.isInteger)(e[0])&&(0,o.isInteger)(e[1])}function R(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(z(e))return e[0]===t[0]&&e[1]===t[1];for(let i=0;i<e.length;i++)if(e[i][0]===t[0]&&e[i][1]===t[1])return!0;return!1}function $(e,t){const i=[];e||(0,o.logWarn)("IX Bid Adapter: mediaTypes.video is the preferred location for video params in ad unit");for(let r of w){const n=e&&e.hasOwnProperty(r),o=t&&t.hasOwnProperty(r);n||o||i.push(`IX Bid Adapter: ${r} is not included in either the adunit or params level`)}const r=e&&e.hasOwnProperty("protocol"),n=e&&e.hasOwnProperty("protocols"),a=t&&t.hasOwnProperty("protocol"),s=t&&t.hasOwnProperty("protocols");return r||n||a||s||i.push("IX Bid Adapter: protocol/protcols is not included in either the adunit or params level"),i}function W(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return z(e)?e:!!z(e[0])&&e[0]}function X(e,t,i){if(!e)return;return{...(0,l.I6)(i,(t=>t.bidId===e)),...(0,l.I6)(t,(t=>t.id===e))}}function N(e,t,i,r){let s="https://htlb.casalemedia.com/openrtb/pbjs",p=function(e){let t=[],i={};if((0,o.isArray)(e))for(const r of e){const e=A.hasOwnProperty(r.source);if((0,n.A)(r,"uids.0")&&(i[r.source]=!0,e&&""!==A[r.source]&&(r.uids[0].ext={rtiPartner:A[r.source]}),t.push(r),t.length>=x))break}return{toSend:t,seenSources:i}}((0,n.A)(e,"0.userIdAsEids")),l=p.toSend;window.headertag&&"function"==typeof window.headertag.getIdentityInfo&&function(e,t){let i=window.headertag.getIdentityInfo();if(i&&"object"==typeof i)for(const r in i){if(e.length>=x)return;if(i.hasOwnProperty(r)){let n=i[r];!n.responsePending&&n.data&&"object"==typeof n.data&&Object.keys(n.data).length&&!t.seenSources[n.data.source]&&e.push(n.data)}}}(l,p);const c=[];let u=function(e){const t={};return t.id=e[0].bidderRequestId.toString(),t.site={},t.ext={},t.ext.source="prebid",t.ext.ixdiag={},t.ext.ixdiag.ls=S.localStorageIsEnabled(),t.imp=[],t.at=1,t}(e);u=function(e,t){t.length>0&&(e.ext.features={},t.forEach((t=>{e.ext.features[t]={activated:E.isFeatureEnabled(t)}})));return e}(u,E.REQUESTED_FEATURE_TOGGLES);let m=function(e,t){var i=e.map((e=>e.adUnitCode)).filter(((e,t,i)=>i.indexOf(e)===t));let r=(0,n.A)(e,"0.userIdAsEids",[]),o={mfu:0,bu:0,iu:0,nu:0,ou:0,allu:0,ren:!1,version:"9.28.0-pre",userIds:V(e[0]),url:window.location.href.split("?")[0],vpd:_,ae:t,eidLength:r.length};for(let t of i){let i=e.filter((e=>e.adUnitCode===t))[0];(0,n.A)(i,"mediaTypes")&&(Object.keys(i.mediaTypes).length>1&&o.mfu++,(0,n.A)(i,"mediaTypes.native")&&o.nu++,(0,n.A)(i,"mediaTypes.banner")&&o.bu++,"outstream"===(0,n.A)(i,"mediaTypes.video.context")&&(o.ou++,re(i)&&(o.ren=!0)),"instream"===(0,n.A)(i,"mediaTypes.video.context")&&o.iu++,o.allu++)}return o}(e,(0,n.A)(t,"paapi.enabled"));for(let e in m)u.ext.ixdiag[e]=m[e];u=function(e,t,i,r,o){const a=(0,n.A)(t,"timeout");a&&(e.ext.ixdiag.tmax=a);d.$W.getConfig("userSync")&&(e.ext.ixdiag.syncsPerBidder=d.$W.getConfig("userSync").syncsPerBidder);e.ext.ixdiag.imps=Object.keys(i).length,e.source={tid:t?.ortb2?.source?.tid},r[0].schain&&(e.source.ext={},e.source.ext.schain=r[0].schain);o.length>0&&(e.user={},e.user.eids=o);document.referrer&&""!==document.referrer&&(e.site.ref=document.referrer);return e}(u,t,i,e,l),u=function(e,t){if(t){t.gdprConsent&&(T=t.gdprConsent,T.hasOwnProperty("gdprApplies")&&(e.regs={ext:{gdpr:T.gdprApplies?1:0}}),T.hasOwnProperty("consentString")&&(e.user=e.user||{},e.user.ext={consent:T.consentString||""},T.hasOwnProperty("addtlConsent")&&T.addtlConsent&&(e.user.ext.consented_providers_settings={addtl_consent:T.addtlConsent}))),t.uspConsent&&((0,a.J)(e,"regs.ext.us_privacy",t.uspConsent),D=t.uspConsent);const i=(0,n.A)(t,"refererInfo.page");i&&(e.site.page=i),t.gppConsent&&((0,a.J)(e,"regs.gpp",t.gppConsent.gppString),(0,a.J)(e,"regs.gpp_sid",t.gppConsent.applicableSections))}d.$W.getConfig("coppa")&&(0,a.J)(e,"regs.coppa",1);return e}(u,t);let f={};e[0].params.siteId&&(P=e[0].params.siteId,f.s=P);const g=Object.keys(i);let b=!1;for(let r=0;r<g.length&&!(c.length>=4);r++){u=G(i,g,u,r);const a=(0,n.A)(t,"ortb2")||{},p={...a.site||a.context};p.page=q(t);const l={...a.user};(0,o.isEmpty)(a)||b||(u=L(t,u,a,p,l),u.site=(0,o.mergeDeep)({},u.site,p),u.user=(0,o.mergeDeep)({},u.user,l),b=!0),u=M(i,u,g,r,f,s);const m=r===g.length-1;if(u=le(u),u=se(u),u=de(u),m){let t=`${s}?`;0!==P&&(t+=`s=${P}`),ne()&&(t+=0!==P?"&":"",t+=`p=${d.$W.getConfig("exchangeId")}`),c.push({method:"POST",url:t,data:(0,o.deepClone)(u),options:{contentType:"text/plain",withCredentials:!0},validBidRequests:e}),u.imp=[],b=!1}}return c}function G(e,t,i,r){const d=e[t[r]],{missingImps:p=[],ixImps:l=[]}=d,c={ixImps:l,missingBannerImpressions:p},u=Object.keys(c).map((e=>c[e])).filter((e=>Array.isArray(e))).reduce(((e,t)=>e.concat(...t)),[]),m=e[t[r]].gpid,f=e[t[r]].dfp_ad_unit_code,g=e[t[r]].tid,b=e[t[r]].sid,x=e[t[r]].ae,h=e[t[r]].paapi,y=u.filter((e=>s.D4 in e)),I=u.filter((e=>!(s.D4 in e)));if(y.length>0){const s=y.reduce(((e,t)=>(e[t.adunitCode]||(e[t.adunitCode]=[]),e[t.adunitCode].push(t),e)),{});for(const d in s){const p=s[d],{id:l,banner:{topframe:c}}=p[0];let u=(0,n.A)(p[0],"ext.externalID");const y={id:l,banner:{topframe:c,format:p.map((e=>{let{banner:{w:t,h:i},ext:r}=e;return{w:t,h:i,ext:r}}))}};for(let e=0;e<y.banner.format.length;e++)null!=y.banner.format[e].ext&&(null!=y.banner.format[e].ext.sid&&delete y.banner.format[e].ext.sid,null!=y.banner.format[e].ext.externalID&&delete y.banner.format[e].ext.externalID),"bidfloor"in p[e]&&(y.banner.format[e].ext.bidfloor=p[e].bidfloor),"{}"===JSON.stringify(y.banner.format[e].ext)&&delete y.banner.format[e].ext;const I=e[t[r]].pos;(0,o.isInteger)(I)&&(y.banner.pos=I),(f||m||g||b||x||u||h)&&(y.ext={},y.ext.dfp_ad_unit_code=f,y.ext.gpid=m,y.ext.tid=g,y.ext.sid=b,y.ext.externalID=u,1==x&&(y.ext.ae=1,y.ext.paapi=h)),"bidfloor"in p[0]&&(y.bidfloor=p[0].bidfloor),"bidfloorcur"in p[0]&&(y.bidfloorcur=p[0].bidfloorcur);const v=e[t[r]].adUnitFPD;v&&(0,a.J)(y,"ext.data",v),i.imp.push(y)}}return I.length>0&&I.forEach((e=>{if(m&&(0,a.J)(e,"ext.gpid",m),i.imp.length>0){let t=!1;i.imp.forEach(((r,o)=>{e.id===r.id&&s.G_ in e?(r.video=e.video,r.video.ext=Object.assign({},e.video.ext,e.ext),(0,n.A)(r,"video.ext.bidfloor",!1)&&(0,n.A)(r,"bidfloor",!1)&&r.video.ext.bidfloor<r.bidfloor&&(r.bidfloor=r.video.ext.bidfloor),!(0,n.A)(r,"ext.siteID",!1)&&(0,n.A)(e,"video.ext.siteID")&&((0,a.J)(r,"ext.siteID",e.video.ext.siteID),(0,a.J)(i,"ext.ixdiag.usid",!0)),t=!0):e.id===r.id&&s.s6 in e&&(r.native=e.native,r.native.ext=Object.assign({},e.native.ext,e.ext),(0,n.A)(r,"native.ext.bidfloor",!1)&&(0,n.A)(r,"bidfloor",!1)&&r.native.ext.bidfloor<r.bidfloor&&(r.bidfloor=r.native.ext.bidfloor),!(0,n.A)(r,"ext.siteID",!1)&&(0,n.A)(e,"native.ext.siteID",!1)&&((0,a.J)(r,"ext.siteID",e.native.ext.siteID),(0,a.J)(i,"ext.ixdiag.usid",!0)),t=!0)})),t||i.imp.push(e)}else i.imp.push(e)})),i}function q(e){const t=e&&e.bidderCode||"ix",i=d.$W.getConfig(t);let r="";if(r=(0,n.A)(e,"ortb2.site.page")?e.ortb2.site.page:(0,n.A)(e,"refererInfo.page"),i&&"object"==typeof i.firstPartyData){return function(e,t,i){let r;try{r=new URL(t)}catch(i){(0,o.logWarn)(`IX Bid Adapter: Invalid URL set in ortb2.site.page: ${t}. Using referer URL instead.`),r=new URL((0,n.A)(e,"refererInfo.page"))}const a=new URLSearchParams(r.search);for(const[e,t]of Object.entries(i))a.has(e)||a.append(e,t);return r.search=a.toString(),r.toString()}(e,r,i.firstPartyData)}return r}function L(e,t,i,r,n){if(t.ext.ixdiag.fpd=!0,Object.keys(r).forEach((e=>{-1===v.SITE.indexOf(e)&&delete r[e]})),Object.keys(n).forEach((e=>{-1===v.USER.indexOf(e)&&delete n[e]})),i.device){const e={...i.device.sua};(0,o.isEmpty)(e)||(0,a.J)(t,"device.sua",e)}if(i.hasOwnProperty("regs")&&!e.gppConsent&&(i.regs.hasOwnProperty("gpp")&&"string"==typeof i.regs.gpp&&(0,a.J)(t,"regs.gpp",i.regs.gpp),i.regs.hasOwnProperty("gpp_sid")&&Array.isArray(i.regs.gpp_sid)&&(0,a.J)(t,"regs.gpp_sid",i.regs.gpp_sid),i.regs.ext?.dsa)){const e=i.regs.ext.dsa,r={};if(["dsarequired","pubrender","datatopub"].forEach((t=>{(0,o.isNumber)(e[t])&&(r[t]=e[t])})),(0,o.isArray)(e.transparency)){const t=[];e.transparency.forEach((e=>{(0,o.isPlainObject)(e)&&(0,o.isStr)(e.domain)&&""!=e.domain&&(0,o.isArray)(e.dsaparams)&&e.dsaparams.every((e=>(0,o.isNumber)(e)))&&t.push(e)})),t.length>0&&(r.transparency=t)}(0,o.isEmpty)(r)||(0,a.J)(t,"regs.ext.dsa",r)}return t}function H(e,t){const i=(0,n.A)(t,"ortb2Imp.ext.data");i&&(0,a.J)(e,"ext.data",i)}function M(e,t,i,r,n,o){const a=e[i[r]].pbadslot,s=e[i[r]].tagId,d=e[i[r]].adUnitCode,p=e[i[r]].divId;return(a||s||d||p)&&(t.ext.ixdiag.pbadslot=a,t.ext.ixdiag.tagid=s,t.ext.ixdiag.adunitcode=d,t.ext.ixdiag.divId=p),t}function V(e){const t=e.userId||{};return C.filter((e=>t[e]))}function Q(e,t){if(t)for(let i=0;i<e.length;i++){const r=e[i];if(t[0]===r[0]&&t[1]===r[1]){e.splice(i,1);break}}}function Y(e,t){const i=function(e){const t=j(e,s.s6),i=e.nativeOrtbRequest;i.eventtrackers=[{event:1,methods:[1,2]}],i.privacy=1,t.native={request:JSON.stringify(i),ver:"1.2"};let r=(0,n.A)(e,"ortb2Imp.ext.tid");return r&&(0,a.J)(t,"ext.tid",r),H(t,e),B(e,t,s.s6),t}(e);if(0!=Object.keys(i).length){t[e.adUnitCode]={},t[e.adUnitCode].ixImps=[],t[e.adUnitCode].ixImps.push(i),t[e.adUnitCode].gpid=(0,n.A)(e,"ortb2Imp.ext.gpid"),t[e.adUnitCode].dfp_ad_unit_code=(0,n.A)(e,"ortb2Imp.ext.data.adserver.adslot"),t[e.adUnitCode].pbadslot=(0,n.A)(e,"ortb2Imp.ext.data.pbadslot"),t[e.adUnitCode].tagId=(0,n.A)(e,"params.tagId");const r=e.adUnitCode,o=document.getElementById(r)?r:(0,f.p)(r).divId;t[e.adUnitCode].adUnitCode=r,t[e.adUnitCode].divId=o}}function Z(e,t){const i=J(e);if(0!=Object.keys(i).length){t[e.adUnitCode]={},t[e.adUnitCode].ixImps=[],t[e.adUnitCode].ixImps.push(i),t[e.adUnitCode].gpid=(0,n.A)(e,"ortb2Imp.ext.gpid"),t[e.adUnitCode].dfp_ad_unit_code=(0,n.A)(e,"ortb2Imp.ext.data.adserver.adslot"),t[e.adUnitCode].pbadslot=(0,n.A)(e,"ortb2Imp.ext.data.pbadslot"),t[e.adUnitCode].tagId=(0,n.A)(e,"params.tagId");const r=e.adUnitCode,o=document.getElementById(r)?r:(0,f.p)(r).divId;t[e.adUnitCode].adUnitCode=r,t[e.adUnitCode].divId=o}}function K(e,t,i,r){let a=function(e){const t=j(e,s.D4);t.banner={},t.adunitCode=e.adUnitCode;const i=(0,n.A)(e,"params.size");return i&&(t.banner.w=i[0],t.banner.h=i[1]),t.banner.topframe=(0,o.inIframe)()?0:1,B(e,t,s.D4),t}(e);const d=R((0,n.A)(e,"mediaTypes.banner.sizes"),(0,n.A)(e,"params.size"));i.hasOwnProperty(e.adUnitCode)||(i[e.adUnitCode]={}),i[e.adUnitCode].gpid=(0,n.A)(e,"ortb2Imp.ext.gpid"),i[e.adUnitCode].dfp_ad_unit_code=(0,n.A)(e,"ortb2Imp.ext.data.adserver.adslot"),i[e.adUnitCode].tid=(0,n.A)(e,"ortb2Imp.ext.tid"),i[e.adUnitCode].pbadslot=(0,n.A)(e,"ortb2Imp.ext.data.pbadslot"),i[e.adUnitCode].tagId=(0,n.A)(e,"params.tagId"),i[e.adUnitCode].pos=(0,n.A)(e,"mediaTypes.banner.pos");if((0,n.A)(r,"paapi.enabled")){const t=(0,n.A)(e,"ortb2Imp.ext.ae"),r=(0,n.A)(e,"ortb2Imp.ext.paapi");r&&(i[e.adUnitCode].paapi=r),t&&((0,o.isInteger)(t)?i[e.adUnitCode].ae=t:(0,o.logWarn)("error setting auction environment flag - must be an integer"))}const p=(0,n.A)(e,"ortb2Imp.ext.data");p&&(i[e.adUnitCode].adUnitFPD=p);const l=(0,n.A)(e,"params.id");!l||"string"!=typeof l&&"number"!=typeof l||(i[e.adUnitCode].sid=String(l));const c=e.adUnitCode,u=document.getElementById(c)?c:(0,f.p)(c).divId;i[e.adUnitCode].adUnitCode=c,i[e.adUnitCode].divId=u,d&&(i[e.adUnitCode].hasOwnProperty("ixImps")||(i[e.adUnitCode].ixImps=[]),i[e.adUnitCode].ixImps.push(a)),function(e,t,i){if(t.hasOwnProperty(e.adUnitCode)){let i=[];t[e.adUnitCode].hasOwnProperty("missingSizes")&&(i=t[e.adUnitCode].missingSizes),Q(i,e.params.size),t[e.adUnitCode].missingSizes=i}else if((0,n.A)(e,"mediaTypes.banner.sizes")){let r=(0,o.deepClone)(e.mediaTypes.banner.sizes);Q(r,e.params.size);let n={missingSizes:r,impression:i};t[e.adUnitCode]=n}}(e,t,a)}function ee(e,t,i){const r=(0,o.deepClone)(t);return r.banner.w=i[0],r.banner.h=i[1],B(e,r,s.D4),r}function te(e){e.renderer.push((function(){const t=e.adUnitCode,i=document.getElementById(t)?t:(0,f.p)(t).divId;i?window.createIXPlayer(i,e):(0,o.logWarn)(`IX Bid Adapter: adUnitCode: ${i} not found on page.`)}))}function ie(e,t){const i=m.A4.install({id:e,url:t,loaded:!1});try{i.setRender(te)}catch(e){return(0,o.logWarn)("Prebid Error calling setRender on renderer",e),null}return t?i:((0,o.logWarn)("Outstream renderer URL not found"),null)}function re(e){if("outstream"!==(0,n.A)(e,"mediaTypes.video.context"))return!1;let t=(0,n.A)(e,"mediaTypes.video.renderer");t||(t=(0,n.A)(e,"renderer"));return!!("object"!=typeof t||!t.url||!t.render)||t.backupOnly}function ne(){let e=d.$W.getConfig("exchangeId");return!("number"!=typeof e||!isFinite(e))||!("string"!=typeof e||""===e.trim()||!isFinite(Number(e)))}const oe={code:g,gvlid:10,supportedMediaTypes:b,isBidRequestValid:function(e){const t=(0,n.A)(e,"params.video"),i=(0,n.A)(e,"params.size"),r=(0,n.A)(e,"mediaTypes.banner.sizes"),a=(0,n.A)(e,"mediaTypes.video"),s=(0,n.A)(e,"mediaTypes.video.playerSize"),d=e.params.hasOwnProperty("bidFloor"),p=e.params.hasOwnProperty("bidFloorCur");if(e.hasOwnProperty("mediaType")&&!(0,o.contains)(b,e.mediaType))return(0,o.logWarn)("IX Bid Adapter: media type is not supported."),!1;if((0,n.A)(e,"mediaTypes.banner")&&!r)return!1;if(i){const t=W(i);if(!t)return(0,o.logError)("IX Bid Adapter: size has invalid format."),!1;if(!R(e.sizes,t)&&!R(s,t)&&!R(r,t))return(0,o.logError)("IX Bid Adapter: bid size is not included in ad unit sizes or player size."),!1}if(!ne()&&null==e.params.siteId)return(0,o.logError)("IX Bid Adapter: Invalid configuration - either siteId or exchangeId must be configured."),!1;if(void 0!==e.params.siteId){if("string"!=typeof e.params.siteId&&"number"!=typeof e.params.siteId)return(0,o.logError)("IX Bid Adapter: siteId must be string or number type."),!1;if("string"!=typeof e.params.siteId&&isNaN(Number(e.params.siteId)))return(0,o.logError)("IX Bid Adapter: siteId must valid value"),!1}if((d||p)&&!(d&&p&&(l=e.params.bidFloor,c=e.params.bidFloorCur,Boolean("number"==typeof l&&"string"==typeof c&&c.match(/^[A-Z]{3}$/)))))return(0,o.logError)("IX Bid Adapter: bidFloor / bidFloorCur parameter has invalid format."),!1;var l,c;if(a&&t){const i=J(e).video,r=$(a,t);if((0,n.A)(e,"mediaTypes.video.context")===u.H6&&re(e)&&i){const e=[(0,n.A)(i,"w"),(0,n.A)(i,"h")];if(!(e[0]>=h[0]&&e[1]>=h[1]))return(0,o.logError)(`IX Bid Adapter: ${e} is an invalid size for IX outstream renderer`),!1}if(r.length)return r.forEach((e=>{(0,o.logError)(e)})),!1}return function(e){return void 0===(0,n.A)(e,"mediaTypes.native")||e.nativeOrtbRequest&&Array.isArray(e.nativeOrtbRequest.assets)&&e.nativeOrtbRequest.assets.length>0}(e)},resetSiteID:function(){P=0},buildRequests:function(e,t){const i=[],r={},a={},d={},p={};E.getFeatureToggles(),e.forEach((e=>{const i=Object.keys((0,n.A)(e,"mediaTypes",{}));for(const n in i)switch(i[n]){case s.D4:K(e,p,r,t);break;case s.G_:Z(e,a);break;case s.s6:Y(e,d);break;default:(0,o.logWarn)(`IX Bid Adapter: ad unit mediaTypes ${n} is not supported`)}}));for(let t in p)if(p.hasOwnProperty(t)){let i=p[t].missingSizes;r.hasOwnProperty(t)||(r[t]={}),r[t].hasOwnProperty("missingImps")||(r[t].missingImps=[],r[t].missingCount=0);let n=p[t].impression;for(let o=0;o<i.length;o++){let a=ee(e[0],n,i[o]);r[t].missingImps.push(a),r[t].missingCount++}}let l=[];return Object.keys(r).length>0&&l.push(r),Object.keys(a).length>0&&l.push(a),Object.keys(d).length>0&&l.push(d),E.isFeatureEnabled("pbjs_enable_multiformat")?i.push(...N(e,t,function(e){const t={};return e.forEach((e=>{Object.keys(e).forEach((i=>{Object.keys(t).includes(i)?t[i].hasOwnProperty("ixImps")&&e[i].hasOwnProperty("ixImps")?t[i].ixImps=[...t[i].ixImps,...e[i].ixImps]:t[i].hasOwnProperty("missingImps")&&e[i].hasOwnProperty("missingImps")?t[i].missingImps=[...t[i].missingImps,...e[i].missingImps]:e[i].hasOwnProperty("ixImps")?t[i].ixImps=e[i].ixImps:e[i].hasOwnProperty("missingImps")&&(t[i].missingImps=e[i].missingImps):t[i]=e[i]}))})),t}(l))):(Object.keys(r).length>0&&i.push(...N(e,t,r)),Object.keys(a).length>0&&i.push(...N(e,t,a)),Object.keys(d).length>0&&i.push(...N(e,t,d))),i},interpretResponse:function(e,t){const i=[];let r=null,a=(0,n.A)(e,"body.ext.protectedAudienceAuctionConfigs")||[];if(E.setFeatureToggles(e),!e.hasOwnProperty("body"))return i;const d=e.body,p=d.seatbid||[];for(let e=0;e<p.length;e++){if(!p[e].hasOwnProperty("bid"))continue;const a=p[e].bid,l=t.data;for(let e=0;e<a.length;e++){const o=X(a[e].impid,l.imp,t.validBidRequests);if(r=F(a[e],d.cur,o),r.mediaType===s.G_&&re(o)){const t=(0,n.A)(d,"ext.videoplayerurl");if(r.renderer=ie(a[e].bidId,t),!r.renderer)continue}i.push(r)}if((0,n.A)(l,"ext.ixdiag.err")&&S.localStorageIsEnabled())try{S.removeDataFromLocalStorage("ixdiag")}catch(e){(0,o.logError)("ix can not clear ixdiag from localStorage.")}}if(!(Array.isArray(a)&&a.length>0))return i;a=a.filter((e=>!!function(e){return"object"==typeof e&&null!==e}(e)||((0,o.logWarn)("Malformed auction config detected:",e),!1)));try{return{bids:i,paapi:a}}catch(e){return(0,o.logWarn)("Error attaching AuctionConfigs",e),i}},getUserSyncs:function(e,t){const i=[];let r=null;if(t.length>0&&(r=(0,n.A)(t[0],"body.ext.publishersyncsperbidderoverride")),void 0!==r&&0==r)return[];if(e.iframeEnabled)i.push({type:"iframe",url:"https://js-sec.indexww.com/um/ixmatch.html"});else{let e=null;d.$W.getConfig("userSync")&&(e=d.$W.getConfig("userSync").syncsPerBidder),0===e&&(e=r),e=r&&(0===e||e)?r>e?e:r:1;for(let t=0;t<e;t++)i.push({type:"image",url:ae(e,t)})}return i}};function ae(e,t){let i="",r="0";return T&&T.hasOwnProperty("gdprApplies")&&(r=T.gdprApplies?"1":"0"),T&&T.hasOwnProperty("consentString")&&(i=T.consentString||""),"https://dsum.casalemedia.com/pbusermatch?origin=prebid"+(0!==P?"&site_id="+P.toString():"")+"&p="+e.toString()+"&i="+t.toString()+"&gdpr="+r+"&gdpr_consent="+i+"&us_privacy="+(D||"")}function se(e){return e.imp.forEach(((t,i)=>{const r=t.ext;if(null==r)return e;pe(t)<2||Object.keys(r).forEach((n=>{if(s.D4 in t){const o=t.banner.ext;if(void 0!==o&&void 0!==o[n]&&o[n]==r[n]&&delete e.imp[i].banner.ext[n],void 0!==t.banner.format)for(let o=0;o<t.banner.format.length;o++)null!=t.banner.format[o].ext&&null!=t.banner.format[o].ext[n]&&t.banner.format[o].ext[n]==r[n]&&delete e.imp[i].banner.format[o].ext[n]}if(s.G_ in t){const o=t.video.ext;void 0!==o&&void 0!==o[n]&&o[n]==r[n]&&delete e.imp[i].video.ext[n]}if(s.s6 in t){const o=t.native.ext;void 0!==o&&void 0!==o[n]&&o[n]==r[n]&&delete e.imp[i].native.ext[n]}}))})),e}function de(e){return e.imp.forEach(((t,i)=>{if(null==t.ext)return e;if(!(pe(t)<2)){if(s.D4 in t){const r=t.banner.ext;if(void 0!==r&&void 0!==r.siteID&&delete e.imp[i].banner.ext.siteID,void 0!==t.banner.format)for(let r=0;r<t.banner.format.length;r++)void 0!==t.banner.format[r].ext&&void 0!==t.banner.format[r].ext.siteID&&((0,a.J)(e.imp[i],"ext.siteID",t.banner.format[r].ext.siteID),(0,a.J)(e,"ext.ixdiag.usid",!0),delete e.imp[i].banner.format[r].ext.siteID)}if(s.G_ in t){const r=t.video.ext;void 0!==r&&void 0!==r.siteID&&delete e.imp[i].video.ext.siteID}if(s.s6 in t){const r=t.native.ext;void 0!==r&&void 0!==r.siteID&&delete e.imp[i].native.ext.siteID}}})),e}function pe(e){let t=0;return void 0!==e.banner&&(t+=1),void 0!==e.video&&(t+=1),void 0!==e.native&&(t+=1),t}function le(e){return null==e.device&&(e.device={}),e.device.h=window.screen.height,e.device.w=window.screen.width,e}(0,c.a$)(oe),(0,r.E)("ixBidAdapter")}},e=>{e.O(0,[33005,60802,51085],(()=>{return t=77764,e(e.s=t);var t}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[13775],{29433:(e,o,t)=>{t.d(o,{ql:()=>w});var r=t(7873),n=t(91069),i=t(70433),a=t(63172),l=t(43272),s=t(68044),d=t(75023),c=t(78969),u=t(16833),f=t(15901),m=t(27934),p=t(12693),h=t(67314),g=t(1e3),y=t(16894),b=t(57176),v=t(51252),F=t(76743),A=t(86400);const k={NOT_FOUND:"not_found",RANDOM:"random"},S="Price Floors",C=(0,s.g4)(1e4),R=Symbol();let O=[R,"gptSlot","adUnitCode","size","domain","mediaType"],T=!1,j=!1,M={};const U=(0,A.L)();let $={};const x=(()=>{let e;return function(){return null==e&&(e=(0,n.parseUrl)((0,m.EN)().topmostLocation,{noDecodeWholeURL:!0}).hostname),e}})();function D(e,o){let{index:t=h.n.index}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return e?.adUnitCode||t.getAdUnit(o).code}let E={[R]:()=>"*",size:(e,o)=>(0,n.parseGPTSingleSizeArray)(o.size)||"*",mediaType:(e,o)=>o.mediaType||"banner",gptSlot:(e,o)=>function(e){let{index:o=h.n.index}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const t=o.getAdUnit({adUnitId:e});return"gam"===(0,i.A)(t,"ortb2Imp.ext.data.adserver.name")&&t.ortb2Imp.ext.data.adserver.adslot}((e||o).adUnitId)||(0,v.p)(D(e,o)).gptSlot,domain:x,adUnitCode:(e,o)=>D(e,o)};function I(e,o){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=function(e,o,t){return e.length?e.reduce(((e,r)=>{let n=E[r](o,t)||"*";return e.push("*"===n?["*"]:[n.toLowerCase(),"*"]),e}),[]):[]}((0,i.A)(e,"schema.fields")||[],o,t);if(!r.length)return{matchingFloor:void 0};let n=r.map((e=>e[0])).join("-"),l=(0,i.A)(e,`matchingInputs.${n}`);if(l)return{...l};let s=(d=r,c=(0,i.A)(e,"schema.delimiter")||"|",d.reduce(((e,o)=>{let t=[];return e.map((e=>{o.map((o=>{t.push(e+c+o)}))})),t})).sort(((e,o)=>e.split("*").length-o.split("*").length)));var d,c;let u=(0,f.I6)(s,(o=>e.values.hasOwnProperty(o))),m={floorMin:e.floorMin||0,floorRuleValue:e.values[u],matchingData:s[0],matchingRule:u===e.meta?.defaultRule?void 0:u};const p=(0,i.A)(o,"ortb2Imp.ext.prebid.floors.floorMin");return"number"==typeof p&&(m.floorMin=p),m.matchingFloor=Math.max(m.floorMin,m.floorRuleValue),(0,a.J)(e,`matchingInputs.${n}`,{...m}),m}function B(e,o,t){return parseFloat((0,b.y)(e,{...o,cpm:e},t))}const N={banner:e=>(0,i.A)(e,"mediaTypes.banner.sizes")||[],video:e=>(0,i.A)(e,"mediaTypes.video.playerSize")||[],native:e=>(0,i.A)(e,"mediaTypes.native.image.sizes")?[(0,i.A)(e,"mediaTypes.native.image.sizes")]:[]};function W(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{currency:"USD",mediaType:"*",size:"*"},o=this,t=$[o.auctionId];if(!t||t.skipped)return{};e=function(e,o){let t=Object.keys(e.mediaTypes||{});return"*"===o.mediaType&&1===t.length&&(o.mediaType=t[0]),"*"===o.size&&-1!==t.indexOf(o.mediaType)&&N[o.mediaType]&&1===N[o.mediaType](e).length&&(o.size=N[o.mediaType](e)[0]),o}(o,e);let i=I(t.data,{...o},{mediaType:e.mediaType,size:e.size}),a=e.currency||t.data.currency;if(i.matchingFloor&&a!==t.data.currency)try{i.matchingFloor=(0,r.m)().convertCurrency(i.matchingFloor,t.data.currency,a)}catch(e){(0,n.logWarn)(`${S}: Unable to get currency conversion for getFloor for bidder ${o.bidder}. You must have currency module enabled with defaultRates in your currency config`),a=t.data.currency}if(t.enforcement.bidAdjustment&&i.matchingFloor){const e=p.u.get(o.bidder,"inverseBidAdjustment");if(e)i.matchingFloor=e(i.matchingFloor,o);else{let e=B(i.matchingFloor,null,o);i.matchingFloor=e?function(e,o){const t=Math.pow(10,10);return e*t/(o*t)*(e*t)/t}(i.matchingFloor,e):i.matchingFloor}}return null===i.floorRuleValue?null:i.matchingFloor?{floor:(l=i.matchingFloor,s=4,Math.ceil((parseFloat(l)*Math.pow(10,s)).toFixed(1))/Math.pow(10,s)),currency:a}:{};var l,s}function q(e,o){let t=(0,n.deepClone)(e);return t.schema.delimiter=e.schema.delimiter||"|",t.values=function(e,o){let t=e.schema.fields,r=e.schema.delimiter,n=o&&-1===t.indexOf("adUnitCode")&&t.unshift("adUnitCode");return Object.keys(e.values).reduce(((t,i)=>(t[(n?`${o}${r}${i}`:i).toLowerCase()]=e.values[i],t)),{})}(t,o),t.currency=t.currency||"USD",t}function w(e,o){let t=(0,n.deepClone)(M);if(2===(0,i.A)(t,"data.floorsSchemaVersion")){let{modelGroups:e,...o}=t.data;t.data=Object.assign(o,function(e,o){let t=Math.floor(Math.random()*o+1);for(let o=0;o<e.length;o++)if(t-=e[o].modelWeight,t<=0)return e[o]}(e,o.modelWeightSum))}let r=0===Object.keys((0,i.A)(t,"data.values")||{}).length;if(t.data=r?function(e){const o=e.find((e=>null!=e.floors?.schema));return e.reduce(((e,t)=>{if(null!=t.floors?.schema&&!(0,n.deepEqual)(t.floors.schema,o?.floors?.schema))return(0,n.logError)(`${S}: adUnit '${t.code}' declares a different schema from one previously declared by adUnit '${o.code}'. Floor config for '${t.code}' will be ignored.`),e;const r=Object.assign({},o?.floors,{values:void 0},t.floors);if(G(r))if(e.values){let o=q(r,t.code).values;Object.assign(e.values,o)}else(e=q(r,t.code)).location="adUnit";else null!=t.floors&&(0,n.logWarn)(`adUnit '${t.code}' provides an invalid \`floor\` definition, it will be ignored for floor calculations`,t);return e}),{})}(e):q(t.data),0===Object.keys((0,i.A)(t,"data.values")||{}).length)t.skipped=!0,t.skippedReason=k.NOT_FOUND;else{const e=(0,n.getParameterByName)("pbjs_skipRate")||((0,i.A)(t,"data.skipRate")??t.skipRate),o=100*Math.random()<parseFloat(e);t.skipped=o,o&&(t.skippedReason=k.RANDOM)}return t.hasOwnProperty("floorMin")&&(t.data.floorMin=t.floorMin),function(e,o,t){const r=function(e){const{data:o,enforcement:t}=e;return o?.noFloorSignalBidders?.length>0?o.noFloorSignalBidders:t?.noFloorSignalBidders?.length>0?t.noFloorSignalBidders:[]}(o);e.forEach((e=>{e.bids.forEach((e=>{const a=r.some((o=>o===e.bidder));o.skipped||a?(a&&(0,n.logInfo)(`noFloorSignal to ${e.bidder}`),delete e.getFloor):e.getFloor=W,e.auctionId=t,e.floorData={noFloorSignaled:a,skipped:o.skipped,skipRate:(0,i.A)(o,"data.skipRate")??o.skipRate,skippedReason:o.skippedReason,floorMin:o.floorMin,modelVersion:(0,i.A)(o,"data.modelVersion"),modelWeight:(0,i.A)(o,"data.modelWeight"),modelTimestamp:(0,i.A)(o,"data.modelTimestamp"),location:(0,i.A)(o,"data.location","noData"),floorProvider:o.floorProvider,fetchStatus:M.fetchStatus}}))}))}(e,t,o),t}function z(e){e.hasExited||(e.reqBidsConfigObj.auctionId=e.reqBidsConfigObj.auctionId||(0,n.generateUUID)(),$[e.reqBidsConfigObj.auctionId]=w(e.reqBidsConfigObj.adUnits||(0,r.m)().adUnits,e.reqBidsConfigObj.auctionId),e.nextFn.apply(e.context,[e.reqBidsConfigObj]),e.hasExited=!0)}function P(e){return e=function(e){if((0,n.isNumber)(e.default)){let o="*";const t=(e.schema?.fields||[]).length;t?o=Array(t).fill("*").join(e.schema?.delimiter||"|"):(0,a.J)(e,"schema.fields",[R]),e.values=e.values||{},null==e.values[o]&&(e.values[o]=e.default,e.meta={defaultRule:o})}return e}(e),!!function(e){if(Array.isArray(e)&&e.length>0){if(e.every((e=>O.includes(e))))return!0;(0,n.logError)(`${S}: Fields received do not match allowed fields`)}return!1}((0,i.A)(e,"schema.fields"))&&(o=e,t=e.schema.fields.length,r=e.schema.delimiter||"|","object"==typeof o.values&&(o.values=Object.keys(o.values).reduce(((e,n)=>(function(e,o,t,r){return"string"==typeof e&&e.split(r).length===t&&("number"==typeof o||null===o)}(n,o.values[n],t,r)&&(e[n]=o.values[n]),e)),{}),Object.keys(o.values).length>0));var o,t,r}const V={1:e=>P(e),2:e=>!(!Array.isArray(e.modelGroups)||0===e.modelGroups.length)&&(e.modelWeightSum=0,e.modelGroups.every((o=>!("number"!=typeof o.modelWeight||!P(o))&&(e.modelWeightSum+=o.modelWeight,!0))))};function G(e){return"object"==typeof e&&(e.floorsSchemaVersion=e.floorsSchemaVersion||1,"function"!=typeof V[e.floorsSchemaVersion]?((0,n.logError)(`${S}: Unknown floorsSchemaVersion: `,e.floorsSchemaVersion),!1):V[e.floorsSchemaVersion](e))}function J(e,o){if(e&&"object"==typeof e&&G(e))return(0,n.logInfo)(`${S}: A ${o} set the auction floor data set to `,e),{...e,location:o};(0,n.logError)(`${S}: The floors data did not contain correct values`,e)}const L=(0,y.Ak)("priceFloors",(function(e,o){const t={reqBidsConfigObj:o,context:this,nextFn:e,hasExited:!1,timer:null};M.auctionDelay>0&&T?U.submit(M.auctionDelay,(()=>z(t)),(()=>{(0,n.logWarn)(`${S}: Fetch attempt did not return in time for auction`),M.fetchStatus="timeout",z(t)})):z(t)}));function _(e){let o;T=!1,M.fetchStatus="success";try{o=JSON.parse(e)}catch(t){o=e}const t=J(o,"fetch");t&&(M.data=t,M.skipRate=(0,n.isNumber)(t.skipRate)?t.skipRate:M.skipRate,M.floorProvider=t.floorProvider||M.floorProvider),U.resume()}function Y(e){T=!1,M.fetchStatus="error",(0,n.logError)(`${S}: Fetch errored with: `,e),U.resume()}function H(e){var o;M=(0,n.pick)(e,["floorMin","enabled",e=>!1!==e,"auctionDelay",e=>e||0,"floorProvider",o=>(0,i.A)(e,"data.floorProvider",o),"endpoint",e=>e||{},"skipRate",()=>isNaN((0,i.A)(e,"data.skipRate"))?e.skipRate||0:e.data.skipRate,"enforcement",e=>(0,n.pick)(e||{},["enforceJS",e=>!1!==e,"enforcePBS",e=>!0===e,"floorDeals",e=>!0===e,"bidAdjustment",e=>!1!==e,"noFloorSignalBidders",e=>e||[]]),"additionalSchemaFields",e=>{return"object"==typeof e&&Object.keys(e).length>0?(o=e,void Object.keys(o).forEach((e=>{-1===O.indexOf(e)&&"function"==typeof o[e]&&(O.push(e),E[e]=o[e])}))):void 0;var o},"data",e=>e&&J(e,"setConfig")||void 0]),M.enabled?((o=M.endpoint).url&&!T?"GET"!==(o.method||"GET")?(0,n.logError)(`${S}: 'GET' is the only request method supported at this time!`):(C(o.url,{success:_,error:Y},null,{method:"GET"}),T=!0):T&&(0,n.logWarn)(`${S}: A fetch is already occuring. Skipping.`),j||(d.on(c.qY.AUCTION_END,(e=>{setTimeout((()=>delete $[e.auctionId]),3e3)})),(0,r.m)().requestBids.before(L,50),(0,u.Yn)("addBidResponse").before(Z,(0,n.debugTurnedOn)()?4:50),j=!0)):((0,n.logInfo)(`${S}: Turning off module`),M={},$={},(0,u.Yn)("addBidResponse").getHooks({hook:Z}).remove(),(0,r.m)().requestBids.getHooks({hook:L}).remove(),j=!1)}const Z=(0,y.NL)("priceFloors",(function(e,o,t,a){let l=$[t.auctionId];if(!l||!t||l.skipped)return e.call(this,o,t,a);const s=h.n.index.getBidRequest(t);let d,u=I(l.data,s,{...t,size:[t.width,t.height]});if(!u.matchingFloor)return 0!==u.matchingFloor&&(0,n.logWarn)(`${S}: unable to determine a matching price floor for bidResponse`,t),e.call(this,o,t,a);let f=l.data.currency.toUpperCase(),m=t.currency||"USD";if(f===m.toUpperCase())d=t.cpm;else if(t.originalCurrency&&f===t.originalCurrency.toUpperCase())d=t.originalCpm;else try{d=(0,r.m)().convertCurrency(t.cpm,m.toUpperCase(),f)}catch(r){return(0,n.logError)(`${S}: Unable do get currency conversion for bidResponse to Floor Currency. Do you have Currency module enabled? ${t}`),e.call(this,o,t,a)}return d=B(d,t,s),function(e,o,t,r){t.floorData={floorValue:o.matchingFloor,floorRule:o.matchingRule,floorRuleValue:o.floorRuleValue,floorCurrency:e.data.currency,cpmAfterAdjustments:r,enforcements:{...e.enforcement},matchedFields:{}},e.data.schema.fields.forEach(((r,n)=>{let i=o.matchingData.split(e.data.schema.delimiter)[n];t.floorData.matchedFields[r]=i}))}(l,u,t,d),function(e,o,t){let r=!1!==(0,i.A)(e,"enforcement.enforceJS"),n=!0===(0,i.A)(e,"enforcement.floorDeals")||!t.dealId,a=t.floorData.cpmAfterAdjustments<o.matchingFloor;return r&&a&&n}(l,u,t)?(a(c.Tf.FLOOR_NOT_MET),void(0,n.logWarn)(`${S}: ${t.bidderCode}'s Bid Response for ${o} was rejected due to floor not met (adjusted cpm: ${t?.floorData?.cpmAfterAdjustments}, floor: ${u?.matchingFloor})`,t)):e.call(this,o,t,a)}));l.$W.getConfig("floors",(e=>H(e.floors))),(0,g.pS)({type:g.Tb,name:"bidfloor",fn:function(e,o,t){if("function"==typeof o.getFloor){let r,i;try{({currency:r,floor:i}=o.getFloor({currency:t.currency||l.$W.getConfig("currency.adServerCurrency")||"USD",mediaType:t.mediaType||"*",size:"*"})||{})}catch(e){return void(0,n.logWarn)("Cannot compute floor for bid",o)}i=parseFloat(i),null==r||null==i||isNaN(i)||Object.assign(e,{bidfloor:i,bidfloorcur:r})}}}),(0,g.pS)({type:g.Tb,name:"extPrebidFloors",fn:function(e,o,t){if(null!=e.bidfloor){let{floorMinCur:o,floorMin:r}=t.reqContext.floorMin||{};null==o&&(o=e.bidfloorcur);const n=e.ext?.prebid?.floors?.floorMinCur||e.ext?.prebid?.floorMinCur||o,i=e.ext?.prebid?.floors?.floorMin||e.ext?.prebid?.floorMin,l=(0,F.hZ)(e.bidfloor,e.bidfloorcur,o),s=!(!i||!n)&&(0,F.hZ)(i,n,o),d=s&&s<l?s:l;(0,a.J)(e,"ext.prebid.floors.floorMin",d),(null==r||r>d)&&(r=d),t.reqContext.floorMin={floorMin:r,floorMinCur:o}}},dialects:[g.e4],priority:-1}),(0,g.pS)({type:g.S3,name:"extPrebidFloors",fn:function(e,o,t){j&&(0,a.J)(e,"ext.prebid.floors.enabled",e.ext?.prebid?.floors?.enabled||!1),t?.floorMin&&(0,n.mergeDeep)(e,{ext:{prebid:{floors:t.floorMin}}})},dialects:[g.e4]}),(0,r.E)("priceFloors")}},e=>{e.O(0,[60802,33005,41225,35957,51085],(()=>{return o=29433,e(e.s=o);var o}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[41252],{63990:(e,r,t)=>{var a=t(7873),i=t(91069),n=t(70433),s=t(63172),o=t(57377),d=t(71371),p=t(43272),l=t(95789),m=t(12693),c=t(78969);const g="pubmatic",u="PubMatic: ",h="USD",f=void 0,b="https://pubmatic.bbvms.com/r/".concat("$RENDERER",".js"),y="Video.plcmt param missing",v={kadpageurl:"",gender:"",yob:"",lat:"",lon:"",wiid:"",profId:"",verId:""},w={NUMBER:"number",STRING:"string",BOOLEAN:"boolean",ARRAY:"array",OBJECT:"object"},x={mimes:w.ARRAY,minduration:w.NUMBER,maxduration:w.NUMBER,startdelay:w.NUMBER,playbackmethod:w.ARRAY,api:w.ARRAY,protocols:w.ARRAY,w:w.NUMBER,h:w.NUMBER,battr:w.ARRAY,linearity:w.NUMBER,placement:w.NUMBER,plcmt:w.NUMBER,minbitrate:w.NUMBER,maxbitrate:w.NUMBER,skip:w.NUMBER},I={ICON:1,IMAGE:3},R={battr:w.ARRAY},T={1:"PMP",5:"PREF",6:"PMPG"},O={bootstrapPlayer:function(e){const r={code:e.adUnitCode};if(e.vastXml?r.vastXml=e.vastXml:e.vastUrl&&(r.vastUrl=e.vastUrl),!e.vastXml&&!e.vastUrl)return void(0,i.logWarn)(`${u}: No vastXml or vastUrl on bid, bailing...`);const t=O.getRendererId("pubmatic",e.rendererCode),a=document.getElementById(e.adUnitCode);let n;for(let e=0;e<window.bluebillywig.renderers.length;e++)if(window.bluebillywig.renderers[e]._id===t){n=window.bluebillywig.renderers[e];break}n?n.bootstrap(r,a):(0,i.logWarn)(`${u}: Couldn't find a renderer with ${t}`)},newRenderer:function(e,r){var t=b.replace("$RENDERER",e);const a=l.A4.install({url:t,loaded:!1,adUnitCode:r});try{a.setRender(O.outstreamRender)}catch(e){(0,i.logWarn)(`${u}: Error tying to setRender on renderer`,e)}return a},outstreamRender:function(e){e.renderer.push((function(){O.bootstrapPlayer(e)}))},getRendererId:function(e,r){return`${e}-${r}`}},A=[d.D4,d.G_,d.s6],E={banner:360,video:1800,native:1800};let _=0,C=!1,S=["pubmatic"];const z=["all"];function U(e,r){if(!(0,i.isStr)(r))return r&&(0,i.logWarn)(u+"Ignoring param key: "+e+", expects string-value, found "+typeof r),f;switch(e){case"pmzoneid":return r.split(",").slice(0,50).map((e=>e.trim())).join();case"kadfloor":case"lat":case"lon":return parseFloat(r)||f;case"yob":return parseInt(r)||f;default:return r}}function P(e){var r;e.params.adUnit="",e.params.adUnitIndex="0",e.params.width=0,e.params.height=0,e.params.adSlot=(r=e.params.adSlot,(0,i.isStr)(r)?r.replace(/^\s+/g,"").replace(/\s+$/g,""):(r&&(0,i.logWarn)(g+": adSlot must be a string. Ignoring adSlot"),""));var t=e.params.adSlot,a=t.split(":");if(t=a[0],2==a.length&&(e.params.adUnitIndex=a[1]),a=t.split("@"),e.params.adUnit=a[0],a.length>1){if(2!=(a=a[1].split("x")).length)return void(0,i.logWarn)(u+"AdSlot Error: adSlot not in required format");e.params.width=parseInt(a[0],10),e.params.height=parseInt(a[1],10)}else if(e.hasOwnProperty("mediaTypes")&&e.mediaTypes.hasOwnProperty(d.D4)&&e.mediaTypes.banner.hasOwnProperty("sizes")){for(var n=0,s=[];n<e.mediaTypes.banner.sizes.length;n++)2===e.mediaTypes.banner.sizes[n].length&&s.push(e.mediaTypes.banner.sizes[n]);e.mediaTypes.banner.sizes=s,e.mediaTypes.banner.sizes.length>=1&&(e.params.width=e.mediaTypes.banner.sizes[0][0],e.params.height=e.mediaTypes.banner.sizes[0][1],e.mediaTypes.banner.sizes=e.mediaTypes.banner.sizes.splice(1,e.mediaTypes.banner.sizes.length-1))}}function W(){let e=window.navigator&&(window.navigator.connection||window.navigator.mozConnection||window.navigator.webkitConnection);switch(e?.effectiveType){case"ethernet":return 1;case"wifi":return 2;case"slow-2g":case"2g":return 4;case"3g":return 5;case"4g":return 6;default:return 0}}function N(e,r,t){var a,n="Ignoring param key: "+e+", expects "+t+", found "+typeof r;switch(t){case w.BOOLEAN:a=i.isBoolean;break;case w.NUMBER:a=i.isNumber;break;case w.STRING:a=i.isStr;break;case w.ARRAY:a=i.isArray}return a(r)?r:((0,i.logWarn)(u+n),f)}const $={desc:"desc",desc2:"desc2",body:"desc",body2:"desc2",sponsoredBy:"sponsored",cta:"ctatext",rating:"rating",address:"address",downloads:"downloads",likes:"likes",phone:"phone",price:"price",salePrice:"saleprice",displayUrl:"displayurl",saleprice:"saleprice",displayurl:"displayurl"},k=Object.values($);function j(e){var r;if(e.ortb){e=e.ortb,r={ver:"1.2",...e,assets:[]};const{assets:t}=e,a=e=>e.title||e.img||e.data||e.video;if(t.length<1||!t.some((e=>a(e))))return(0,i.logWarn)(`${u}: Native assets object is empty or contains some invalid object`),C=!0,r;t.forEach((e=>{var t=e;t.img&&(t.img.type==I.IMAGE?(t.w=t.w||t.width||(t.sizes?t.sizes[0]:f),t.h=t.h||t.height||(t.sizes?t.sizes[1]:f),t.wmin=t.wmin||t.minimumWidth||(t.minsizes?t.minsizes[0]:f),t.hmin=t.hmin||t.minimumHeight||(t.minsizes?t.minsizes[1]:f)):t.img.type==I.ICON&&(t.w=t.w||t.width||(t.sizes?t.sizes[0]:f),t.h=t.h||t.height||(t.sizes?t.sizes[1]:f))),t&&void 0!==t.id&&a(t)&&r.assets.push(t)}))}else r=function(e){if(!e&&!(0,i.isPlainObject)(e))return(0,i.logWarn)(`${u}: Native assets object is empty or not an object: ${e}`),void(C=!0);const r={ver:"1.2",assets:[]};for(let t in e){if(c._B.includes(t))continue;if(!c.x5.hasOwnProperty(t)&&!k.includes(t)){(0,i.logWarn)(`${u}: Unrecognized native asset code: ${t}. Asset will be ignored.`);continue}const a=e[t];let n=0;a.required&&(0,i.isBoolean)(a.required)&&(n=Number(a.required));const s={id:r.assets.length,required:n};if(t in $)s.data={type:c.jO[$[t]]},(a.len||a.length)&&(s.data.len=a.len||a.length),a.ext&&(s.data.ext=a.ext);else if("icon"===t||"image"===t){if(s.img={type:"icon"===t?c.oA.ICON:c.oA.MAIN},a.aspect_ratios)if((0,i.isArray)(a.aspect_ratios))if(a.aspect_ratios.length){const{min_width:e,min_height:r}=a.aspect_ratios[0];(0,i.isInteger)(e)&&(0,i.isInteger)(r)?(s.img.wmin=e,s.img.hmin=r):(0,i.logWarn)(`${u}: image.aspect_ratios min_width or min_height are invalid: ${e}, ${r}`);const t=a.aspect_ratios.filter((e=>e.ratio_width&&e.ratio_height)).map((e=>`${e.ratio_width}:${e.ratio_height}`));t.length>0&&(s.img.ext={aspectratios:t})}else(0,i.logWarn)(`${u}: image.aspect_ratios was passed, but it's empty: ${a.aspect_ratios}`);else(0,i.logWarn)(`${u}: image.aspect_ratios was passed, but it's not a an array: ${a.aspect_ratios}`);s.img.w=a.w||a.width,s.img.h=a.h||a.height,s.img.wmin=a.wmin||a.minimumWidth||(a.minsizes?a.minsizes[0]:f),s.img.hmin=a.hmin||a.minimumHeight||(a.minsizes?a.minsizes[1]:f),a.sizes&&(2===a.sizes.length&&(0,i.isInteger)(a.sizes[0])&&(0,i.isInteger)(a.sizes[1])?((0,i.logInfo)(`${u}: if asset.sizes exist, by OpenRTB spec we should remove wmin and hmin`),s.img.w=a.sizes[0],s.img.h=a.sizes[1],delete s.img.hmin,delete s.img.wmin):(0,i.logWarn)(`${u}: image.sizes was passed, but its value is not an array of integers: ${a.sizes}`)),a.ext&&(s.img.ext=a.ext),a.mimes&&(s.img.mimes=a.mimes)}else"title"===t?(s.title={len:a.len||a.length||140},a.ext&&(s.title.ext=a.ext)):"ext"===t&&(s.ext=a,delete s.required);r.assets.push(s)}return r.assets.length<1?((0,i.logWarn)(`${u}: Could not find any valid asset`),void(C=!0)):r}(e);return r}function B(e){var r,t=e.mediaTypes.banner.sizes,a=[];if(t!==f&&(0,i.isArray)(t)){if(r={},e.params.width||e.params.height)r.w=e.params.width,r.h=e.params.height;else{if(0===t.length)return r=f,(0,i.logWarn)(u+"Error: mediaTypes.banner.size missing for adunit: "+e.params.adUnit+". Ignoring the banner impression in the adunit."),r;r.w=parseInt(t[0][0],10),r.h=parseInt(t[0][1],10),t=t.splice(1,t.length-1)}t.length>0&&(a=[],t.forEach((function(e){e.length>1&&a.push({w:e[0],h:e[1]})})),a.length>0&&(r.format=a)),r.pos=0,r.topframe=(0,i.inIframe)()?0:1;const s={...(0,n.A)(e,"ortb2Imp.banner")};for(let e in R)s.hasOwnProperty(e)&&(r[e]=N(e,s[e],R[e]))}else(0,i.logWarn)(u+"Error: mediaTypes.banner.size missing for adunit: "+e.params.adUnit+". Ignoring the banner impression in the adunit."),r=f;return r}function M(e){var r,t=(0,i.mergeDeep)((0,n.A)(e.mediaTypes,"video"),e.params.video);if(t!==f){for(var a in r={},function(e,r){(0,n.A)(e,"plcmt")||(0,i.logWarn)(y+" for "+r)}(t,e.adUnitCode),x)t.hasOwnProperty(a)&&(r[a]=N(a,t[a],x[a]));(0,i.isArray)(e.mediaTypes.video.playerSize[0])?(r.w=parseInt(e.mediaTypes.video.playerSize[0][0],10),r.h=parseInt(e.mediaTypes.video.playerSize[0][1],10)):(0,i.isNumber)(e.mediaTypes.video.playerSize[0])&&(r.w=parseInt(e.mediaTypes.video.playerSize[0],10),r.h=parseInt(e.mediaTypes.video.playerSize[1],10))}else r=f,(0,i.logWarn)(u+"Error: Video config params missing for adunit: "+e.params.adUnit+" with mediaType set as video. Ignoring video impression in the adunit.");return r}function q(e,r){var t,a,o={},l={},m=e.hasOwnProperty("sizes")?e.sizes:[],c="",g=[],b=r?.paapi?.enabled;if(function(e,r){r.params.deals&&((0,i.isArray)(r.params.deals)?r.params.deals.forEach((function(r){(0,i.isStr)(r)&&r.length>3?(e.pmp||(e.pmp={private_auction:0,deals:[]}),e.pmp.deals.push({id:r})):(0,i.logWarn)(u+"Error: deal-id present in array bid.params.deals should be a strings with more than 3 charaters length, deal-id ignored: "+r)})):(0,i.logWarn)(u+"Error: bid.params.deals should be an array of strings."))}(o={id:e.bidId,tagid:e.params.adUnit||void 0,bidfloor:U("kadfloor",e.params.kadfloor),secure:1,ext:{pmZoneId:U("pmzoneid",e.params.pmzoneid)},bidfloorcur:e.params.currency?U("currency",e.params.currency):h,displaymanager:"Prebid.js",displaymanagerver:"9.28.0-pre",pmp:e.ortb2Imp?.pmp||void 0},e),function(e,r){var t,a="";if(r.params.dctr)if(a=r.params.dctr,(0,i.isStr)(a)&&a.length>0){var n=a.split("|");a="",n.forEach((e=>{a+=e.length>0?e.trim()+"|":""})),t=a.length,"|"===a.substring(t,t-1)&&(a=a.substring(0,t-1)),e.ext.key_val=a.trim()}else(0,i.logWarn)(u+"Ignoring param : dctr with value : "+a+", expects string-value, found empty or non-string value")}(o,e),function(e,r){var t=r.rtd&&r.rtd.jwplayer&&r.rtd.jwplayer.targeting||void 0,a="";if(void 0!==t&&""!==t&&t.hasOwnProperty("segments")){var i,n=t.segments.length;a+="jw-id="+t.content.id;for(var s=0;s<n;s++)a+="|jw-"+t.segments[s]+"=1";(i=e.ext)&&void 0===i.key_val?i.key_val=a:i.key_val+="|"+a}}(o,e),e.hasOwnProperty("mediaTypes"))for(c in e.mediaTypes)switch(c){case d.D4:(t=B(e))!==f&&(o.banner=t);break;case d.s6:l.request=JSON.stringify(j(e.nativeParams)),C?((0,i.logWarn)(u+"Error: Error in Native adunit "+e.params.adUnit+". Ignoring the adunit. Refer to http://prebid.org/dev-docs/show-native-ads.html for more details."),C=!1):o.native=l;break;case d.G_:(a=M(e))!==f&&(o.video=a)}else t={pos:0,w:e.params.width,h:e.params.height,topframe:(0,i.inIframe)()?0:1},(0,i.isArray)(m)&&m.length>1&&((m=m.splice(1,m.length-1)).forEach((e=>{g.push({w:e[0],h:e[1]})})),t.format=g),o.banner=t;return function(e,r){const t={...(0,n.A)(r,"ortb2Imp.ext.data")};Object.keys(t).forEach((r=>{"pbadslot"===r?"string"==typeof t[r]&&t[r]&&(0,s.J)(e,"ext.data.pbadslot",t[r]):"adserver"===r?["name","adslot"].forEach((r=>{const a=(0,n.A)(t,`adserver.${r}`);"string"==typeof a&&a&&((0,s.J)(e,`ext.data.adserver.${r.toLowerCase()}`,a),"adslot"===r&&(0,s.J)(e,"ext.dfp_ad_unit_code",a))})):(0,s.J)(e,`ext.data.${r}`,t[r])}));const a=(0,n.A)(r,"ortb2Imp.ext.gpid");a&&(0,s.J)(e,"ext.gpid",a)}(o,e),function(e,r){let t=-1;"function"!=typeof r.getFloor||p.$W.getConfig("pubmatic.disableFloors")||[d.D4,d.G_,d.s6].forEach((a=>{if(e.hasOwnProperty(a)){let n=[];"banner"===a&&(e[a].w&&e[a].h&&n.push([e[a].w,e[a].h]),(0,i.isArray)(e[a].format)&&e[a].format.forEach((e=>n.push([e.w,e.h])))),0===n.length&&n.push("*"),n.forEach((n=>{let s=r.getFloor({currency:e.bidfloorcur,mediaType:a,size:n});if((0,i.logInfo)(u,"floor from floor module returned for mediatype:",a," and size:",n," is: currency",s.currency,"floor",s.floor),(0,i.isPlainObject)(s)&&s.currency===e.bidfloorcur&&!isNaN(parseInt(s.floor))){let e=parseFloat(s.floor);(0,i.logInfo)(u,"floor from floor module:",e,"previous floor value",t,"Min:",Math.min(e,t)),t=-1===t?e:Math.min(e,t),(0,i.logInfo)(u,"new floor value:",t)}}))}}));e.bidfloor&&((0,i.logInfo)(u,"floor from floor module:",t,"impObj.bidfloor",e.bidfloor,"Max:",Math.max(t,e.bidfloor)),t=Math.max(t,e.bidfloor));e.bidfloor=!isNaN(t)&&t>0?t:f,(0,i.logInfo)(u,"new impObj.bidfloor value:",e.bidfloor)}(o,e),function(e,r,t){t?(e.ext=e.ext||{},void 0!==r?.ortb2Imp?.ext?.ae&&(e.ext.ae=r.ortb2Imp.ext.ae)):e.ext?.ae&&delete e.ext.ae}(o,e,b),o.hasOwnProperty(d.D4)||o.hasOwnProperty(d.s6)||o.hasOwnProperty(d.G_)?o:f}function J(e,r){(r=r.filter((function(e){return"string"==typeof e||((0,i.logWarn)(u+"acat: Each category should be a string, ignoring category: "+e),!1)})).map((e=>e.trim())).filter(((e,r,t)=>t.indexOf(e)===r))).length>0&&((0,i.logWarn)(u+"acat: Selected: ",r),e.ext.acat=r)}function D(e){return!0===(0,i.isArray)(e)&&e.length>0}const G={code:g,gvlid:76,supportedMediaTypes:[d.D4,d.G_,d.s6],isBidRequestValid:e=>{if(e&&e.params){if(!(0,i.isStr)(e.params.publisherId))return(0,i.logWarn)(u+"Error: publisherId is mandatory and cannot be numeric (wrap it in quotes in your config). Call to OpenBid will not be sent for ad unit: "+JSON.stringify(e)),!1;if(e.hasOwnProperty("mediaTypes")&&e.mediaTypes.hasOwnProperty(d.G_)){let r=(0,n.A)(e.mediaTypes,"video.mimes"),t=(0,n.A)(e,"params.video.mimes");if(!1===D(r)&&!1===D(t))return(0,i.logWarn)(u+"Error: For video ads, bid.mediaTypes.video.mimes OR bid.params.video.mimes should be present and must be a non-empty array. Call to OpenBid will not be sent for ad unit:"+JSON.stringify(e)),!1;if(!e.mediaTypes[d.G_].hasOwnProperty("context"))return(0,i.logError)(`${u}: no context specified in bid. Rejecting bid: `,e),!1;if("outstream"===e.mediaTypes[d.G_].context&&!(0,i.isStr)(e.params.outstreamAU)&&!e.hasOwnProperty("renderer")&&!e.mediaTypes[d.G_].hasOwnProperty("renderer"))return e.mediaTypes.hasOwnProperty(d.D4)||e.mediaTypes.hasOwnProperty(d.s6)?(delete e.mediaTypes[d.G_],(0,i.logWarn)(`${u}: for "outstream" bids either outstreamAU parameter must be provided or ad unit supplied renderer is required. Rejecting mediatype Video of bid: `,e),!0):((0,i.logError)(`${u}: for "outstream" bids either outstreamAU parameter must be provided or ad unit supplied renderer is required. Rejecting bid: `,e),!1)}return!0}return!1},buildRequests:(e,r)=>{var t;r&&r.refererInfo&&(t=r.refererInfo);var a,o=function(e){return{pageURL:e?.page||window.location.href,refURL:e?.ref||window.document.referrer}}(t),l=function(e){return{id:""+(new Date).getTime(),at:1,cur:[h],imp:[],site:{page:e.pageURL,ref:e.refURL,publisher:{}},device:{ua:navigator.userAgent,js:1,dnt:"yes"==navigator.doNotTrack||"1"==navigator.doNotTrack||"1"==navigator.msDoNotTrack?1:0,h:screen.height,w:screen.width,language:navigator.language,connectiontype:W()},user:{},ext:{}}}(o),c="",g=[],b=[],y=[],w=(0,i.generateUUID)();if(e.forEach((e=>{if(e.params.wiid=e.params.wiid||r.auctionId||w,(a=(0,i.deepClone)(e)).params.adSlot=a.params.adSlot||"",P(a),a.mediaTypes&&a.mediaTypes.hasOwnProperty("video")||a.params.hasOwnProperty("video"));else if(!(a.hasOwnProperty("mediaTypes")&&a.mediaTypes.hasOwnProperty(d.s6)||0!==a.params.width||0!==a.params.height))return void(0,i.logWarn)(u+"Skipping the non-standard adslot: ",a.params.adSlot,JSON.stringify(a));o.pubId=o.pubId||a.params.publisherId,(o=function(e,r){var t,a,n;for(t in r.kadpageurl||(r.kadpageurl=r.pageURL),v)v.hasOwnProperty(t)&&(a=e[t])&&("object"==typeof(n=v[t])&&(a=n.f(a,r)),(0,i.isStr)(a)?r[t]=a:(0,i.logWarn)(u+"Ignoring param : "+t+" with value : "+v[t]+", expects string-value, found "+typeof a));return r}(a.params,o)).transactionId=a.ortb2Imp?.ext?.tid,""===c?c=a.params.currency||f:a.params.hasOwnProperty("currency")&&c!==a.params.currency&&(0,i.logWarn)(u+"Currency specifier ignored. Only one currency permitted."),a.params.currency=c,a.params.hasOwnProperty("dctr")&&(0,i.isStr)(a.params.dctr)&&g.push(a.params.dctr),a.params.hasOwnProperty("bcat")&&(0,i.isArray)(a.params.bcat)&&(b=b.concat(a.params.bcat)),a.params.hasOwnProperty("acat")&&(0,i.isArray)(a.params.acat)&&(y=y.concat(a.params.acat));var t=q(a,r);t&&l.imp.push(t)})),0==l.imp.length)return;l.site.publisher.id=o.pubId.trim(),_=o.pubId.trim(),l.ext.wrapper={},l.ext.wrapper.profile=parseInt(o.profId)||f,l.ext.wrapper.version=parseInt(o.verId)||f,l.ext.wrapper.wiid=o.wiid||r.auctionId,l.ext.wrapper.wv="prebid_prebid_9.28.0-pre",l.ext.wrapper.transactionId=o.transactionId,l.ext.wrapper.wp="pbjs";const x=r?m.u.get(r.bidderCode,"allowAlternateBidderCodes"):void 0;if(void 0!==x){if(l.ext.marketplace={},r&&1==x){let e=m.u.get(r.bidderCode,"allowedAlternateBidderCodes");(0,i.isArray)(e)?(e=e.map((e=>e.trim().toLowerCase())).filter((e=>!!e)).filter(i.uniques),S=e.includes("*")?z:[...S,...e]):S=z}l.ext.marketplace.allowedbidders=S.filter(i.uniques)}l.user.gender=o.gender?o.gender.trim():f,l.user.geo={},l.user.yob=U("yob",o.yob),l.site.page=o.kadpageurl.trim()||l.site.page.trim(),l.site.domain=function(e){let r=document.createElement("a");return r.href=e,r.hostname}(l.site.page),"object"==typeof p.$W.getConfig("content")&&(l.site.content=p.$W.getConfig("content")),"object"==typeof p.$W.getConfig("device")&&(l.device=Object.assign(l.device,p.$W.getConfig("device"))),l.device.language=l.device.language&&l.device.language.split("-")[0],(0,s.J)(l,"source.tid",r?.ortb2?.source?.tid),-1!==window.location.href.indexOf("pubmaticTest=true")&&(l.test=1),e[0].schain&&(0,s.J)(l,"source.ext.schain",e[0].schain),r&&r.gdprConsent&&((0,s.J)(l,"user.ext.consent",r.gdprConsent.consentString),(0,s.J)(l,"regs.ext.gdpr",r.gdprConsent.gdprApplies?1:0)),r&&r.uspConsent&&(0,s.J)(l,"regs.ext.us_privacy",r.uspConsent),r?.gppConsent?.gppString?((0,s.J)(l,"regs.gpp",r.gppConsent.gppString),(0,s.J)(l,"regs.gpp_sid",r.gppConsent.applicableSections)):r?.ortb2?.regs?.gpp&&((0,s.J)(l,"regs.gpp",r.ortb2.regs.gpp),(0,s.J)(l,"regs.gpp_sid",r.ortb2.regs.gpp_sid)),!0===p.$W.getConfig("coppa")&&(0,s.J)(l,"regs.coppa",1),r?.ortb2?.regs?.ext?.dsa&&(0,s.J)(l,"regs.ext.dsa",r.ortb2.regs.ext.dsa),function(e,r){let t=(0,n.A)(r,"0.userIdAsEids");(0,i.isArray)(t)&&t.length>0&&(0,s.J)(e,"user.eids",t)}(l,e);const I=r&&r.ortb2||{},{user:R,device:T,site:O,bcat:A,badv:E}=I;if(O){const{page:e,domain:r,ref:t}=l.site;(0,i.mergeDeep)(l,{site:O}),l.site.page=e,l.site.domain=r,l.site.ref=t}if(R&&(0,i.mergeDeep)(l,{user:R}),E&&(0,i.mergeDeep)(l,{badv:E}),A&&(b=b.concat(A)),T?.sua&&(l.device.sua=T?.sua),T?.ext?.cdep&&(0,s.J)(l,"device.ext.cdep",T.ext.cdep),R?.geo&&T?.geo?(l.device.geo={...l.device.geo,...T.geo},l.user.geo={...l.user.geo,...R.geo}):(R?.geo||T?.geo)&&(l.user.geo=l.device.geo=R?.geo?{...l.user.geo,...R.geo}:{...l.user.geo,...T.geo}),r?.ortb2?.device&&(0,i.mergeDeep)(l.device,r.ortb2.device),I.ext?.prebid?.bidderparams?.[r.bidderCode]?.acat){const e=I.ext.prebid.bidderparams[r.bidderCode].acat;J(l,e)}else y.length&&J(l,y);return function(e,r){(r=r.filter((function(e){return"string"==typeof e||((0,i.logWarn)(u+"bcat: Each category should be a string, ignoring category: "+e),!1)})).map((e=>e.trim())).filter((function(e,r,t){if(e.length>3)return t.indexOf(e)===r;(0,i.logWarn)(u+"bcat: Each category should have a value of a length of more than 3 characters, ignoring category: "+e)}))).length>0&&((0,i.logWarn)(u+"bcat: Selected: ",r),e.bcat=r)}(l,b),l.tmax=r?.timeout?r.timeout:window?.PWT?.versionDetails?.timeout,l.ext.epoch=(new Date).getTime(),"object"==typeof p.$W.getConfig("app")&&(l.app=p.$W.getConfig("app"),l.app.publisher=l.site.publisher,l.app.ext=l.site.ext||f,"object"!=typeof l.app.content&&(l.app.content=l.site.content||f),delete l.site),{method:"POST",url:"https://hbopenbid.pubmatic.com/translator?source=prebid-client",data:JSON.stringify(l),bidderRequest:r}},interpretResponse:(e,r)=>{const t=[];var a=h;let s=JSON.parse(r.data),o=s.site&&s.site.ref?s.site.ref:"";try{e.body&&e.body.seatbid&&(0,i.isArray)(e.body.seatbid)&&(a=e.body.cur||a,e.body.seatbid.forEach((e=>{e.bid&&(0,i.isArray)(e.bid)&&e.bid.forEach((p=>{let l={requestId:p.impid,cpm:parseFloat((p.price||0).toFixed(2)),width:p.w,height:p.h,creativeId:p.crid||p.id,dealId:p.dealid,currency:a,netRevenue:true,ttl:360,referrer:o,ad:p.adm,pm_seat:e.seat||null,pm_dspid:p.ext&&p.ext.dspid?p.ext.dspid:null,partnerImpId:p.id||""};s.imp&&s.imp.length>0&&s.imp.forEach((e=>{if(p.impid===e.id)switch(function(e,r){if(e.ext&&null!=e.ext.bidtype)r.mediaType=A[e.ext.bidtype];else{(0,i.logInfo)(u+"bid.ext.bidtype does not exist, checking alternatively for mediaType");var t=e.adm,a="",n=new RegExp(/VAST\s+version/);if(t.indexOf('span class="PubAPIAd"')>=0)r.mediaType=d.D4;else if(n.test(t))r.mediaType=d.G_;else try{(a=JSON.parse(t.replace(/\\/g,"")))&&a.native&&(r.mediaType=d.s6)}catch(e){(0,i.logWarn)(u+"Error: Cannot parse native reponse for ad response: "+t)}}}(p,l),function(e,r){let t=E[r?.mediaType]||360;r.ttl=e.exp||t}(p,l),l.mediaType){case d.D4:break;case d.G_:l.width=p.hasOwnProperty("w")?p.w:e.video.w,l.height=p.hasOwnProperty("h")?p.h:e.video.h,l.vastXml=p.adm,function(e,r){let t,a,i;if(r.bidderRequest&&r.bidderRequest.bids){for(let n=0;n<r.bidderRequest.bids.length;n++)r.bidderRequest.bids[n].bidId===e.requestId&&(t=r.bidderRequest.bids[n].params,a=r.bidderRequest.bids[n].mediaTypes[d.G_].context,i=r.bidderRequest.bids[n].adUnitCode);a&&"outstream"===a&&t&&t.outstreamAU&&i&&(e.rendererCode=t.outstreamAU,e.renderer=O.newRenderer(e.rendererCode,i))}}(l,r),function(e,r,t){if(!r?.ext?.prebiddealpriority)return;const a=(0,i.getBidRequest)(e.requestId,[t.bidderRequest]),s=(0,n.A)(a,"mediaTypes.video");if(s?.context!=d.LM)return;const o=r?.ext?.video?.duration||s?.maxduration;e.video={context:d.LM,durationSeconds:o,dealTier:r.ext.prebiddealpriority}}(l,p,r);break;case d.s6:!function(e,r){if(e.hasOwnProperty("adm")){var t="";try{t=JSON.parse(e.adm.replace(/\\/g,""))}catch(e){return void(0,i.logWarn)(u+"Error: Cannot parse native reponse for ad response: "+r.adm)}r.native={ortb:{...t.native}},r.mediaType=d.s6,r.width||(r.width=0),r.height||(r.height=0)}}(p,l)}})),function(e,r,t){e.meta=e.meta||{},r.ext&&r.ext.dspid&&(e.meta.networkId=r.ext.dspid,e.meta.demandSource=r.ext.dspid),r.ext&&r.ext.dchain&&(e.meta.dchain=r.ext.dchain);const a=t||r.ext&&r.ext.advid;a&&(e.meta.advertiserId=a,e.meta.agencyId=a,e.meta.buyerId=a),r.adomain&&D(r.adomain)&&(e.meta.advertiserDomains=r.adomain,e.meta.clickUrl=r.adomain[0],e.meta.brandId=r.adomain[0]),r.cat&&D(r.cat)&&(e.meta.secondaryCatIds=r.cat,e.meta.primaryCatId=r.cat[0]),r.ext&&r.ext.dsa&&Object.keys(r.ext.dsa).length&&(e.meta.dsa=r.ext.dsa),e.mediaType&&(e.meta.mediaType=e.mediaType)}(l,p,e.seat),function(e,r){e?.ext?.ibv&&(r.ext=r.ext||{},r.ext.ibv=e.ext.ibv,r.meta=r.meta||{},r.meta.mediaType=d.G_)}(p,l),p.ext&&p.ext.deal_channel&&(l.dealChannel=T[p.ext.deal_channel]||null),e.ext&&e.ext.buyid&&(l.adserverTargeting={hb_buyid_pubmatic:e.ext.buyid}),p.ext&&p.ext.marketplace&&(l.bidderCode=p.ext.marketplace),t.push(l)}))})));let p=(0,n.A)(e.body,"ext.fledge_auction_configs");if(p)return p=Object.entries(p).map((e=>{let[r,t]=e;return{bidId:r,config:Object.assign({auctionSignals:{}},t)}})),{bids:t,paapi:p}}catch(e){(0,i.logError)(e)}return t},getUserSyncs:(e,r,t,a,i)=>{let n=""+_;return t&&(n+="&gdpr="+(t.gdprApplies?1:0),n+="&gdpr_consent="+encodeURIComponent(t.consentString||"")),a&&(n+="&us_privacy="+encodeURIComponent(a)),i?.gppString&&i?.applicableSections?.length&&(n+="&gpp="+encodeURIComponent(i.gppString),n+="&gpp_sid="+encodeURIComponent(i?.applicableSections?.join(","))),!0===p.$W.getConfig("coppa")&&(n+="&coppa=1"),e.iframeEnabled?[{type:"iframe",url:"https://ads.pubmatic.com/AdServer/js/user_sync.html?kdntuid=1&p="+n}]:[{type:"image",url:"https://image8.pubmatic.com/AdServer/ImgSync?p="+n}]}};(0,o.a$)(G),(0,a.E)("pubmaticBidAdapter")}},e=>{e.O(0,[60802,51085],(()=>{return r=63990,e(e.s=r);var r}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[62711],{99293:(e,t,r)=>{var i=r(7873),n=r(89766),o=r(34610),s=r(57377),a=r(43272),d=r(71371),c=r(95789),p=r(91069),u=r(63172),l=r(70433),m=r(28656),b=r(8702);const g="https://video-outstream.rubiconproject.com/apex-2.2.1.js";let x=a.$W.getConfig("rubicon")||{};a.$W.getConfig("rubicon",(e=>{(0,p.mergeDeep)(x,e.rubicon)}));let f={};var y={1:"468x60",2:"728x90",5:"120x90",7:"125x125",8:"120x600",9:"160x600",10:"300x600",13:"200x200",14:"250x250",15:"300x250",16:"336x280",17:"240x400",19:"300x100",31:"980x120",32:"250x360",33:"180x500",35:"980x150",37:"468x400",38:"930x180",39:"750x100",40:"750x200",41:"750x300",42:"2x4",43:"320x50",44:"300x50",48:"300x300",53:"1024x768",54:"300x1050",55:"970x90",57:"970x250",58:"1000x90",59:"320x80",60:"320x150",61:"1000x1000",64:"580x500",65:"640x480",66:"930x600",67:"320x480",68:"1800x1000",72:"320x320",73:"320x160",78:"980x240",79:"980x300",80:"980x400",83:"480x300",85:"300x120",90:"548x150",94:"970x310",95:"970x100",96:"970x210",101:"480x320",102:"768x1024",103:"480x280",105:"250x800",108:"320x240",113:"1000x300",117:"320x100",125:"800x250",126:"200x600",144:"980x600",145:"980x150",152:"1000x250",156:"640x320",159:"320x250",179:"250x600",195:"600x300",198:"640x360",199:"640x200",213:"1030x590",214:"980x360",221:"1x1",229:"320x180",230:"2000x1400",232:"580x400",234:"6x6",251:"2x2",256:"480x820",257:"400x600",258:"500x200",259:"998x200",261:"480x480",264:"970x1000",265:"1920x1080",274:"1800x200",278:"320x500",282:"320x400",288:"640x380",484:"720x1280",524:"1x2",548:"500x1000",550:"980x480",552:"300x200",558:"640x640",562:"300x431",564:"320x431",566:"320x300",568:"300x150",570:"300x125",572:"250x350",574:"620x891",576:"610x877",578:"980x552",580:"505x656",622:"192x160",632:"1200x450",634:"340x450",680:"970x570",682:"300x240",684:"970x550",686:"300x210",688:"300x220",690:"970x170"};(0,p._each)(y,((e,t)=>y[e]=t));const h=(0,n.A)({request(e,t,r,n){const{bidRequests:o}=n,s=e(t,r,n);var d;s.cur=["USD"],s.test=a.$W.getConfig("debug")?1:0,(0,u.J)(s,"ext.prebid.cache",{vastxml:{returnCreative:!0===x.returnVast}}),(0,u.J)(s,"ext.prebid.bidders",{rubicon:{integration:x.int_type||"pbjs"}}),(0,u.J)(s,"ext.prebid.targeting.pricegranularity",{ranges:{low:[{max:5,increment:.5}],medium:[{max:20,increment:.1}],high:[{max:20,increment:.01}],auto:[{max:5,increment:.05},{min:5,max:10,increment:.1},{min:10,max:20,increment:.5}],dense:[{max:3,increment:.01},{min:3,max:8,increment:.05},{min:8,max:20,increment:.5}],custom:(d=a.$W).getConfig("customPriceBucket")&&d.getConfig("customPriceBucket").buckets}[d.getConfig("priceGranularity")]});let c=(0,i.m)().installedModules;!c||c.length&&-1===c.indexOf("rubiconAnalyticsAdapter")||(0,u.J)(s,"ext.prebid.analytics",{rubicon:{"client-analytics":!0}}),function(e,t,r){let i={};const n=(0,m.D)(r,...t.map((e=>e.params.keywords)));t.forEach((t=>{const r={user:{ext:{data:{...t.params.visitor}}},site:{ext:{data:{...t.params.inventory}}}},n=e.imp.find((e=>e.ext?.prebid?.bidder?.rubicon?.video?.language));n&&(r.site.content={language:n.ext?.prebid?.bidder?.rubicon?.video?.language}),i=(0,p.mergeDeep)(i,t.ortb2||{},r);const o=a.$W.getConfig("user.id");i.user.id=i.user.id||o})),(0,p.mergeDeep)(e,i),n&&n.length&&(0,u.J)(e,"site.keywords",n.join(","));delete e?.ext?.prebid?.storedrequest}(s,o,r.ortb2),delete s?.ext?.prebid?.storedrequest,!0===x.disableFloors&&delete s.ext.prebid.floors;return o.filter((e=>"object"==typeof e.floorData)).length>0&&(s.ext.prebid.floors={enabled:!1}),s},imp(e,t,r){const i=S(t);if(i.includes(d.D4)&&1==i.length)return;const n=e(t,r);return n.id=t.adUnitCode,delete n.banner,"atf"===t.params.position&&n.video&&(n.video.pos=1),"btf"===t.params.position&&n.video&&(n.video.pos=3),delete n.ext?.prebid?.storedrequest,!0===t.params.bidonmultiformat&&i.length>1&&(0,u.J)(n,"ext.prebid.bidder.rubicon.formats",i),function(e,t){"USD"!=t.bidfloorcur&&(delete t.bidfloor,delete t.bidfloorcur);if(!t.bidfloor){let r=parseFloat((0,l.A)(e,"params.floor"));isNaN(r)||(t.bidfloor=r,t.bidfloorcur="USD")}}(t,n),n.id=f[n.id]?n.id+f[n.id]++:(f[n.id]=2,n.id),n},bidResponse(e,t,r){const i=e(t,r);i.meta.mediaType=(0,l.A)(t,"ext.prebid.type");const{bidRequest:n}=r;let[o,s]="outstream"===n.mediaTypes.video?.context?j(n,d.G_):[void 0,void 0];return i.width=t.w||o||i.playerWidth||0,i.height=t.h||s||i.playerHeight||0,i.mediaType===d.G_&&"outstream"===n.mediaTypes.video.context&&(i.renderer=function(e){const t=c.A4.install({id:e.adId,url:x.rendererUrl||g,config:x.rendererConfig||{},loaded:!1,adUnitCode:e.adUnitCode});try{t.setRender(A)}catch(e){(0,p.logWarn)("Prebid Error calling setRender on renderer",e)}return t}(i)),(0,l.A)(t,"ext.bidder.rp.advid")&&(0,u.J)(i,"meta.advertiserId",t.ext.bidder.rp.advid),i},context:{netRevenue:!1!==x.netRevenue,ttl:360},processors:o.m}),_={code:"rubicon",gvlid:52,supportedMediaTypes:[d.D4,d.G_,d.s6],isBidRequestValid:function(e){let t=!0;if("object"!=typeof e.params)return!1;for(let t=0,r=["accountId","siteId","zoneId"];t<r.length;t++)if(e.params[r[t]]=parseInt(e.params[r[t]]),isNaN(e.params[r[t]]))return(0,p.logError)("Rubicon: wrong format of accountId or siteId or zoneId."),!1;let r=S(e,!0);if(!r.length)return!1;r.includes(d.G_)&&(t=function(e){let t=!0,r=Object.prototype.toString.call([]),i=Object.prototype.toString.call(0);var n={mimes:r,protocols:r,linearity:i};return Object.keys(n).forEach((function(r){Object.prototype.toString.call((0,l.A)(e,"mediaTypes.video."+r))!==n[r]&&(t=!1,(0,p.logError)("Rubicon: mediaTypes.video."+r+" is required and must be of type: "+n[r]))})),t}(e));const i=[d.D4,d.s6].filter((e=>r.includes(e))).length>0;return i?t&&i:t},buildRequests:function(e,t){let r,i=[],n=[];if(r=e.filter((e=>{const t=S(e)||[],{length:r}=t,{bidonmultiformat:i,video:n}=e.params||{};return 1===r&&(t.includes(d.G_)||t.includes(d.s6))||2===r&&!t.includes(d.D4)||n&&t.includes(d.G_)||i&&(t.includes(d.G_)||t.includes(d.s6))})),r&&r.length){const e=h.toORTB({bidRequests:r,bidderRequest:t});R(),n.push({method:"POST",url:`https://${x.videoHost||"prebid-server"}.rubiconproject.com/openrtb2/auction`,data:e,bidRequest:r})}const o=e.filter((e=>{const t=S(e)||[],{bidonmultiformat:r,video:i}=e.params||{};return t.includes(d.D4)&&(1===t.length||r||!r&&!i||!r&&i&&!t.includes(d.G_))}));if(!0!==x.singleRequest)i=n.concat(o.map((e=>{const r=_.createSlotParams(e,t);return{method:"GET",url:`https://${x.bannerHost||"fastlane"}.rubiconproject.com/a/api/fastlane.json`,data:_.getOrderedParams(r).reduce(((e,t)=>{const i=r[t];return(0,p.isStr)(i)&&""!==i||(0,p.isNumber)(i)?`${e}${I(t,i)}&`:e}),"")+`slots=1&rand=${Math.random()}`,bidRequest:e}})));else{const e=o.reduce(((e,t)=>((e[t.params.siteId]=e[t.params.siteId]||[]).push(t),e)),{}),r=10;i=n.concat(Object.keys(e).reduce(((i,n)=>{var o,s;return(o=e[n],s=r,o.map(((e,t)=>t%s==0?o.slice(t,t+s):null)).filter((e=>e))).forEach((e=>{const r=_.combineSlotUrlParams(e.map((e=>_.createSlotParams(e,t))));i.push({method:"GET",url:`https://${x.bannerHost||"fastlane"}.rubiconproject.com/a/api/fastlane.json`,data:_.getOrderedParams(r).reduce(((e,t)=>{const i=r[t];return(0,p.isStr)(i)&&""!==i||(0,p.isNumber)(i)?`${e}${I(t,i)}&`:e}),"")+`slots=${e.length}&rand=${Math.random()}`,bidRequest:e})})),i}),[]))}return i},getOrderedParams:function(e){const t=/^tg_v/,r=/^tg_i/,i=/^eid_|^tpid_/,n=["account_id","site_id","zone_id","size_id","alt_size_ids","p_pos","gdpr","gdpr_consent","us_privacy","gpp","gpp_sid","rp_schain"].concat(Object.keys(e).filter((e=>i.test(e)))).concat(["x_liverampidl","ppuid","rf","p_geo.latitude","p_geo.longitude","kw"]).concat(Object.keys(e).filter((e=>t.test(e)))).concat(Object.keys(e).filter((e=>r.test(e)))).concat(["tk_flint","x_source.tid","l_pb_bid_id","p_screen_res","o_ae","o_cdep","rp_floor","rp_secure","tk_user_key"]);return n.concat(Object.keys(e).filter((e=>-1===n.indexOf(e))))},combineSlotUrlParams:function(e){if(1===e.length)return e[0];const t=e.reduce((function(t,r,i){return Object.keys(r).forEach((function(n){t.hasOwnProperty(n)||(t[n]=new Array(e.length)),t[n].splice(i,1,r[n])})),t}),{}),r=new RegExp("^([^;]*)(;\\1)+$");return Object.keys(t).forEach((function(e){const i=t[e].join(";"),n=i.match(r);t[e]=n?n[1]:i})),t},createSlotParams:function(e,t){e.startTime=(new Date).getTime();const r=e.params,i=j(e,"banner"),[n,o]=r.latLong||[],s={account_id:r.accountId,site_id:r.siteId,zone_id:r.zoneId,size_id:i[0],alt_size_ids:i.slice(1).join(",")||void 0,rp_floor:(r.floor=parseFloat(r.floor))>=.01?r.floor:void 0,rp_secure:"1",tk_flint:`${x.int_type||"pbjs_lite"}_v9.28.0-pre`,"x_source.tid":t.ortb2?.source?.tid,"x_imp.ext.tid":e.ortb2Imp?.ext?.tid,l_pb_bid_id:e.bidId,o_cdep:e.ortb2?.device?.ext?.cdep,ip:e.ortb2?.device?.ip,ipv6:e.ortb2?.device?.ipv6,p_screen_res:[window.screen.width,window.screen.height].join("x"),tk_user_key:r.userId,"p_geo.latitude":isNaN(parseFloat(n))?void 0:parseFloat(n).toFixed(4),"p_geo.longitude":isNaN(parseFloat(o))?void 0:parseFloat(o).toFixed(4),"tg_fl.eid":e.code,rf:v(e,t)};if("function"==typeof e.getFloor&&!x.disableFloors){let t;try{t=e.getFloor({currency:"USD",mediaType:"banner",size:"*"})}catch(e){(0,p.logError)("Rubicon: getFloor threw an error: ",e)}s.rp_hard_floor=(0,p.isPlainObject)(t)&&"USD"===t.currency&&!isNaN(parseInt(t.floor))?t.floor:void 0}!0===r.bidonmultiformat&&(0,l.A)(e,"mediaTypes")&&Object.keys(e.mediaTypes).length>1&&(s.p_formats=Object.keys(e.mediaTypes).join(","));let c={1:"atf",3:"btf"}[(0,l.A)(e,"mediaTypes.banner.pos")]||"";s.p_pos="atf"===r.position||"btf"===r.position?r.position:c;const u=a.$W.getConfig("user.id");return u&&(s.ppuid=u),e?.ortb2Imp?.ext?.ae&&(s.o_ae=1),"number"==typeof e?.ortb2?.site?.mobile&&(s["p_site.mobile"]=e.ortb2.site.mobile),function(e,t){if(!1===x.readTopics)return;let r=[1,2,5,6,7,507].concat(x.sendSiteSegtax?.map((e=>Number(e)))||[]),i=[4,508].concat(x.sendUserSegtax?.map((e=>Number(e)))||[]),n=e.ortb2?.user?.data||[],o=e.ortb2?.site?.content?.data||[];n.forEach($(t,"v",i)),o.forEach($(t,"i",r))}(t,s),e?.ortb2?.user?.ext?.eids&&e.ortb2.user.ext.eids.forEach((e=>{let{source:t,uids:r=[],inserter:i,matcher:n,mm:o,ext:a={}}=e;try{const e=r[0];if(!e)return;const a=e=>[e.id,e.atype||"","",i||"",n||"",o||"",e?.ext?.rtipartner||""].join("^"),d=a(e);if(s[`eid_${t}`]=d,!s.ppuid){const e=r.find((e=>"ppuid"===e.ext?.stype));e?.id&&(s.ppuid=e.id)}}catch(e){(0,p.logWarn)("Rubicon: error reading eid:",{source:t,uids:r},e)}})),t.gdprConsent&&("boolean"==typeof t.gdprConsent.gdprApplies&&(s.gdpr=Number(t.gdprConsent.gdprApplies)),s.gdpr_consent=t.gdprConsent.consentString),t.uspConsent&&(s.us_privacy=encodeURIComponent(t.uspConsent)),t.gppConsent?.gppString&&(s.gpp=t.gppConsent.gppString,s.gpp_sid=t.gppConsent?.applicableSections?.toString()),s.rp_maxbids=t.bidLimit||1,function(e,t,r){const i={user:{ext:{data:{...e.params.visitor}}},site:{ext:{data:{...e.params.inventory}}}};e.params.keywords&&(i.site.keywords=(0,p.isArray)(e.params.keywords)?e.params.keywords.join(","):e.params.keywords);let n=(0,p.mergeDeep)({},e.ortb2||{},i),o=(0,l.A)(e.ortb2Imp,"ext")||{},s=(0,l.A)(e.ortb2Imp,"ext.data")||{};const a=(0,l.A)(e,"ortb2Imp.ext.gpid"),c=(0,l.A)(n,"regs.ext.dsa"),u={user:[4],site:[1,2,5,6]},m={user:"tg_v.",site:"tg_i.",adserver:"tg_i.dfp_ad_unit_code",pbadslot:"tg_i.pbadslot",keywords:"kw"},b=function(e,t,r){return"data"===t&&Array.isArray(e)?e.filter((e=>e.segment&&(0,l.A)(e,"ext.segtax")&&u[r]&&-1!==u[r].indexOf((0,l.A)(e,"ext.segtax")))).map((e=>{let t=e.segment.filter((e=>e.id)).reduce(((e,t)=>(e.push(t.id),e)),[]);if(t.length>0)return t.toString()})).toString():("object"!=typeof e||Array.isArray(e))&&void 0!==e?Array.isArray(e)?e.filter((e=>{if("object"!=typeof e&&void 0!==e)return e.toString();(0,p.logWarn)("Rubicon: Filtered value: ",e,"for key",t,": Expected value to be string, integer, or an array of strings/ints")})).toString():e.toString():void 0},g=function(e,t,i){let n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],o=b(e,i,t),s=m[i]&&n?`${m[i]}`:"data"===i?`${m[t]}iab`:`${m[t]}${i}`;r[s]=r[s]?r[s].concat(",",o):o};if(t===d.D4){["site","user"].forEach((e=>{Object.keys(n[e]).forEach((t=>{"site"===e&&"content"===t&&n[e][t].data?g(n[e][t].data,e,"data"):"ext"!==t?g(n[e][t],e,t):n[e][t].data&&Object.keys(n[e].ext.data).forEach((t=>{g(n[e].ext.data[t],e,t,!1)}))}))})),Object.keys(s).forEach((e=>{"adserver"!==e?g(s[e],"site",e):"gam"===s[e].name&&g(s[e].adslot,name,e)})),a&&(r.p_gpid=a),c&&Object.keys(c).length&&(0,p.pick)(c,["dsainfo",e=>r.dsainfo=e,"dsarequired",e=>r.dsarequired=e,"pubrender",e=>r.dsapubrender=e,"datatopub",e=>r.dsadatatopubs=e,"transparency",e=>{Array.isArray(e)&&e.length&&(r.dsatransparency=e.reduce(((e,t)=>{const r=t.domain||"";if(!r)return e;const i=t.dsaparams||t.params;return Array.isArray(i)&&0!==i.length?(e&&(e+="~~"),e+`${r}~${i.join("_")}`):e}),""))}]),r["tg_i.pbadslot"]&&delete r["tg_i.dfp_ad_unit_code"];const e=(0,l.A)(n,"device.sua");e&&!1!==x.chEnabled&&(0,p.pick)(e,["architecture",e=>r.m_ch_arch=e,"bitness",e=>r.m_ch_bitness=e,"browsers",e=>{if(!Array.isArray(e))return;const[t,i]=e.reduce(((e,t)=>(e[0].push(`"${t?.brand}"|v="${t?.version?.[0]}"`),e[1].push(`"${t?.brand}"|v="${t?.version?.join?.(".")}"`),e)),[[],[]]);r.m_ch_ua=t?.join?.(","),r.m_ch_full_ver=i?.join?.(",")},"mobile",e=>r.m_ch_mobile=`?${e}`,"model",e=>r.m_ch_model=e,"platform",e=>{r.m_ch_platform=e?.brand,r.m_ch_platform_ver=e?.version?.join?.(".")}])}else Object.keys(o).length&&(0,p.mergeDeep)(r.imp[0].ext,o),a&&(r.imp[0].ext.gpid=a),(0,p.mergeDeep)(r,n)}(e,d.D4,s),!0===a.$W.getConfig("coppa")&&(s.coppa=1),e.schain&&C(e.schain)&&(s.rp_schain=_.serializeSupplyChain(e.schain)),s},serializeSupplyChain:function(e){if(!C(e))return"";const{ver:t,complete:r,nodes:i}=e;return`${t},${r}!${_.serializeSupplyChainNodes(i)}`},serializeSupplyChainNodes:function(e){const t=["asi","sid","hp","rid","name","domain"];return e.map((e=>t.map((t=>encodeURIComponent(e[t]||""))).join(","))).join("!")},interpretResponse:function(e,t){e=e.body;const{data:r}=t;if(!e||"object"!=typeof e)return[];if(e.seatbid){const t=(0,l.A)(e,"ext.errors.rubicon");Array.isArray(t)&&t.length>0&&(0,p.logWarn)("Rubicon: Error in video response");return h.fromORTB({request:r,response:e}).bids}let i,n=e.ads,o=0;const{bidRequest:s}=t;if("object"==typeof s&&!Array.isArray(s)&&S(s).includes(d.G_)&&"object"==typeof n&&(n=n[s.adUnitCode]),!Array.isArray(n)||n.length<1)return[];let a=n.reduce(((t,r,n)=>{if(r.impression_id&&i===r.impression_id?o++:i=r.impression_id,"ok"!==r.status)return t;const a=Array.isArray(s)?s[n-o]:s;if(a&&"object"==typeof a){let e={requestId:a.bidId,currency:"USD",creativeId:r.creative_id||`${r.network||""}-${r.advertiser||""}`,cpm:r.cpm||0,dealId:r.deal,ttl:360,netRevenue:!1!==x.netRevenue,rubicon:{advertiserId:r.advertiser,networkId:r.network},meta:{advertiserId:r.advertiser,networkId:r.network,mediaType:d.D4}};r.creative_type&&(e.mediaType=r.creative_type),r.dsa&&Object.keys(r.dsa).length&&(e.meta.dsa=r.dsa),r.adomain&&(e.meta.advertiserDomains=Array.isArray(r.adomain)?r.adomain:[r.adomain]),r.emulated_format&&(e.meta.mediaType=r.emulated_format),r.creative_type===d.G_?(e.width=a.params.video.playerWidth,e.height=a.params.video.playerHeight,e.vastUrl=r.creative_depot_url,e.impression_id=r.impression_id,e.videoCacheKey=r.impression_id):(e.ad=(c=r.script,`<html>\n<head><script type='text/javascript'>inDapIF=true;<\/script></head>\n<body style='margin : 0; padding: 0;'>\n\x3c!-- Rubicon Project Ad Tag --\x3e\n<div data-rp-impression-id='${r.impression_id}'>\n<script type='text/javascript'>${c}<\/script>\n</div>\n</body>\n</html>`),[e.width,e.height]=y[r.size_id].split("x").map((e=>Number(e)))),e.rubiconTargeting=(Array.isArray(r.targeting)?r.targeting:[]).reduce(((e,t)=>(e[t.key]=t.values[0],e)),{rpfl_elemid:a.adUnitCode}),t.push(e)}else(0,p.logError)(`Rubicon: bidRequest undefined at index position:${n}`,s,e);var c;return t}),[]).sort(((e,t)=>(t.cpm||0)-(e.cpm||0))),c=e.component_auction_config?.map((e=>({config:e,bidId:e.bidId})));return c?{bids:a,paapi:c}:a},getUserSyncs:function(e,t,r,i,n){if(!w&&e.iframeEnabled){let e=(0,b.d)(r,i,n);return e=Object.keys(e).length?`?${(0,p.formatQS)(e)}`:"",w=!0,{type:"iframe",url:`https://${x.syncHost||"eus"}.rubiconproject.com/usync.html`+e}}}};function v(e,t){let r;return r=e.params.referrer?e.params.referrer:t.refererInfo.page,e.params.secure?r.replace(/^http:/i,"https:"):r}function A(e){const t=document.getElementById(e.adUnitCode);!function(e){const t=e.querySelector("div[id^='google_ads']");t&&t.style.setProperty("display","none")}(t),function(e){const t=e.querySelector("script[id^='sas_script']"),r=t&&t.nextSibling;r&&"iframe"===r.localName&&r.style.setProperty("display","none")}(t);const r={...{align:"center",position:"append",closeButton:!1,label:void 0,collapse:!0},...e.renderer.getConfig()};e.renderer.push((()=>{window.MagniteApex.renderAd({width:e.width,height:e.height,vastUrl:e.vastUrl,placement:{attachTo:`#${e.adUnitCode}`,align:r.align,position:r.position},closeButton:r.closeButton,label:r.label,collapse:r.collapse})}))}function j(e,t){let r=e.params;if(t===d.G_){let t=[];return r.video&&r.video.playerWidth&&r.video.playerHeight?t=[r.video.playerWidth,r.video.playerHeight]:Array.isArray((0,l.A)(e,"mediaTypes.video.playerSize"))&&1===e.mediaTypes.video.playerSize.length?t=e.mediaTypes.video.playerSize[0]:Array.isArray(e.sizes)&&e.sizes.length>0&&Array.isArray(e.sizes[0])&&e.sizes[0].length>1&&(t=e.sizes[0]),t}let i=[];return Array.isArray(r.sizes)?i=r.sizes:void 0!==(0,l.A)(e,"mediaTypes.banner.sizes")?i=k(e.mediaTypes.banner.sizes):Array.isArray(e.sizes)&&e.sizes.length>0?i=k(e.sizes):(0,p.logWarn)("Rubicon: no sizes are setup or found"),function(e){const t=[15,2,9];return e.sort(((e,r)=>{const i=t.indexOf(e),n=t.indexOf(r);return i>-1||n>-1?-1===i?1:-1===n?-1:i-n:e-r}))}(i)}function $(e,t,r){return i=>{const n=Number(i.ext?.segtax);r.includes(n)&&(e[`tg_${t}.tax${n}`]=i.segment?.map((e=>e.id)).join(","))}}function k(e){return(0,p.parseSizesInput)(e).reduce(((e,t)=>{let r=parseInt(y[t],10);return r&&e.push(r),e}),[])}function S(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=[];if(function(e){let t=void 0!==(0,l.A)(e,`mediaTypes.${d.G_}`),r=void 0!==(0,l.A)(e,`mediaTypes.${d.D4}`),i=void 0!==(0,l.A)(e,"params.bidonmultiformat"),n="object"!=typeof(0,l.A)(e,"params.video");return!(!t||!i)||(r&&n&&(t=!1),t&&n&&(0,u.J)(e,"params.video",{}),t)}(e)){if(-1===["outstream","instream"].indexOf((0,l.A)(e,`mediaTypes.${d.G_}.context`)))return t&&(0,p.logError)("Rubicon: mediaTypes.video.context must be outstream or instream"),r;if(j(e,d.G_).length<2)return t&&(0,p.logError)("Rubicon: could not determine the playerSize of the video"),r;t&&(0,p.logMessage)("Rubicon: making video request for adUnit",e.adUnitCode),r.push(d.G_)}if(void 0!==(0,l.A)(e,`mediaTypes.${d.s6}`)&&r.push(d.s6),void 0!==(0,l.A)(e,`mediaTypes.${d.D4}`)){if(0===j(e,d.D4).length)return t&&(0,p.logError)("Rubicon: could not determine the sizes for banner request"),r;t&&(0,p.logMessage)("Rubicon: making banner request for adUnit",e.adUnitCode),r.push(d.D4)}return r}const R=()=>f={};function C(e){let t=!1;const r=["asi","sid","hp"];return e.nodes?(t=e.nodes.reduce(((e,t)=>e?r.every((e=>t.hasOwnProperty(e))):e),!0),t||(0,p.logError)("Rubicon: required schain params missing"),t):t}function I(e,t){return"rp_schain"===e?`rp_schain=${t}`:`${e}=${encodeURIComponent(t)}`}var w=!1;(0,s.a$)(_),(0,i.E)("rubiconBidAdapter")}},e=>{e.O(0,[60802,58498,97247,47618,46550,51085],(()=>{return t=99293,e(e.s=t);var t}));e.O()}]);
})(),pbjs.processQueue();'use strict';TADhba=function(){function p(b,a){if(b){let c=b.length,f;for(f=0;f<c;f++)a(b[f],f)}}function q(b,a){for(let c in b)p(b[c].bids,function(b){a(c,b)})}function r(b){return(b=b.match(/(\d+)x/))?b[1]:"0"}let v=pbjs.getAllWinningBids(),h=[],m={};var g=googletag.pubads().getSlots();p(g,function(b){let a=b.getAdUnitPath();b=b.getSlotElementId();m[a]=r(b)});let t={},u={},k={},n={};pbjs.getEvents().forEach(function(b){var a=b.eventType;let c=b.args;"bidRequested"==a?n[c.bidderCode]=b.elapsedTime:
"bidderDone"==a?t[c.bidderCode]=b.elapsedTime-n[c.bidderCode]:"bidderError"==a?(a=c.bidderRequest.bidderCode,k[a]||(u[a]=c.error.status)):"bidTimeout"==a&&c.forEach(function(a){a=a.bidder;k[a]=b.elapsedTime-n[a]})});q(pbjs.getBidResponses(),function(b,a){let c="",f=a.bidder;a.source&&"s2s"==a.source&&(c="s");let d=!1;v.forEach(function(b){b.adId==a.adId&&(d=!0)});d?c+="w":k[f]&&(c+="t");c&&(c="_"+c);h.push(m[b]+"_"+f+"_"+Math.floor(a.timeToRespond)+"_"+Math.floor(100*a.cpm)/100+c)});q(pbjs.getNoBids&&
pbjs.getNoBids()||{},function(b,a){let c="c";a.src&&"s2s"==a.src&&(c="s");a=a.bidder;let d=u[a],e=k[a],g=t[a]||0;d?c+="e_"+d:e&&(g=e,c+="t");h.push(m[b]+"_"+a+"_"+Math.floor(g)+"_"+c)});if(window.TADaps){g=TADaps.e-TADaps.t;for(var e=0;e<AD.s.length;e++){var d=AD.s[e],l=r(d.getSlotElementId());let b=TADaps.b[e];l+="_amazon";b&&2<b.length&&(l+="_"+g+"_"+b);(d=d.getResponseInformation())&&2205133651==d.campaignId&&(l+="_w");h.push(l)}}g=h.length;e=[];for(d=0;d<g;d++)e[d]="p"+d+"="+h[d];e.push("pbv="+
window.pbv);return e.join("&")};
