//Copyright timeanddate.com 2021, do not use without permission
(function(){function b(a){a="/scripts/logux.php?ts="+(dt()-l)+"&msg="+escape(a);document.cookie="FTA="+c+";path=/;secure";jcb(a,function(){})}function e(a){a="/scripts/saveprivacy.php?fta="+c+"&mode=jspatch&"+a;d.ft&&(a+="&ft="+escape(d.ft.value));jcb(a,function(a){f=!0;document.cookie="SAVEINFO=New privacy settings saved;domain=.timeanddate.com;path=/;secure";window.location="/"})}function g(){it(h,function(a){var b=d[a],k=!0;b&&(k=!!b.checked);a=gf("privacyframe_err_"+a);ac(a,"dn",k)})}var h=["cookie",
"third","social"],d=document.privacyframe_form,f=!1,l=dt(),c=d.fta.value;aelw("beforeunload",function(){f||b("left")});window.privacyframe={toggle:function(){var a=gf("modal-gdpr-details");hC(a,"dn")?(b("toggle on"),ac(a,"dn",0)):(b("toggle off"),ac(a,"dn",1))},accept:function(a){b("save all #"+a);e("cookie=1&third=1&social=1")},checked:function(a){b("checkbox "+a.name+" "+a.checked);g()},save:function(){var a={};it(h,function(b){var c=d[b];if(c)var e=c.checked?1:0;a[b]=e});var c=au(a);b("save custom "+
c);e(c)}};b("open");g()})();
