//Copyright timeanddate.com 2021, do not use without permission
(function(h){function g(c){if(d[c])return d[c].exports;var f=d[c]={i:c,l:!1,exports:{}};h[c].call(f.exports,f,f.exports,g);f.l=!0;return f.exports}var d={};g.m=h;g.c=d;g.d=function(c,f,e){g.o(c,f)||Object.defineProperty(c,f,{enumerable:!0,get:e})};g.r=function(c){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});Object.defineProperty(c,"__esModule",{value:!0})};g.t=function(c,f){f&1&&(c=g(c));if(f&8||f&4&&"object"===typeof c&&c&&c.__esModule)return c;
var e=Object.create(null);g.r(e);Object.defineProperty(e,"default",{enumerable:!0,value:c});if(f&2&&"string"!=typeof c)for(var a in c)g.d(e,a,function(b){return c[b]}.bind(null,a));return e};g.n=function(c){var f=c&&c.__esModule?function(){return c["default"]}:function(){return c};g.d(f,"a",f);return f};g.o=function(c,f){return Object.prototype.hasOwnProperty.call(c,f)};g.p="";return g(g.s=2)})([function(h,g){var d={sungraph:{},isReady:!1,onready:[],ready:function(){it(d.onready,function(c){c()})},
container:null,cityId:null,currentDetails:null,colors:" #b0dae8 #96bbc9 #6f8f9a #586a70 #2f454d".split(" ")};h.exports=d},function(h,g){var d={},c={};h.exports={GetAstroData:function(){return c},SetAstroData:function(f,e,a){d[f]&&d[f][e]?(c=d[f][e],a(c)):astrodb.read_with_year(f,e,function(b){c=b;a(b)})}}},function(h,g,d){d(3);_T.control.applyBindingsOnLoad()},function(h,g,d){h=d(4);g=d(7);var c=d(8),f=d(9);d=d(10);_T.control.add("Sungraph.Graph",h);_T.control.add("Sungraph.DetailedGraph",g);_T.control.add("Sungraph.UI",
c);_T.control.add("Sungraph.Date",f);_T.control.add("Sungraph.PhaseTimes",d)},function(h,g,d){var c=d(5),f=d(6),e=d(0),a=d(1);g=function(b,a){this._element=b;this._options=a;e.sungraph=this;if(a.cityid)e.cityId=a.cityid;else if(window.ASTDATA.n)e.cityId=window.ASTDATA.n;else throw"cityid must be set";e.year=a.year?a.year:window.ASTDATA.year?window.ASTDATA.year:(new Date).getFullYear();this._listeners={dateChanged:[],typeChanged:[],lockedChanged:[],resized:[]};this._init()};g.prototype={_init:function(){var b=
this;c?(b._renderer=new f,b._renderer.init(b._element),a.SetAstroData(e.cityId,e.year,function(a){b._renderer.render();b._astroData=a;b._width=b._element.getBoundingClientRect().width;ael(b._element,"mouseover",function(){b._onMouseOver()});ael(b._element,"touchstart",function(a){b._onTouchStart(a)});e.ready();b._setToToday()}),window.onresize=function(){b._width=b._element.getBoundingClientRect().width;b._notifySubscribers(b._listeners.resized,null)}):document.body.className+=" nocanvas"},_setToToday:function(){var b=
new Date;b=this._astroData.get_astDay_from_date(new Date(Date.UTC(e.year,b.getMonth(),b.getDate())));this._setTo(b)},_setTo:function(b){e.currentDetails=b;this._notifySubscribers(this._listeners.dateChanged,e.currentDetails)},_onTouchStart:function(b){b.preventDefault();var a=this;b=a._element;var e=b.getBoundingClientRect().left;a._width=b.getBoundingClientRect().width;a._isMouseOver=!0;a._element.ontouchmove=function(b){b=b.changedTouches[0].pageX-e;b=Math.min(a._astroData.getDayCount()-1,Math.max(0,
Mf(b/a._width*a._astroData.getDayCount())));console.log(b,a._astroData.getDayCount());a._setTo(a._astroData.get_astDay(b))};a._element.ontouchend=function(){a._isMouseOver=!1;a._element.ontouchmove=null;a._element.ontouchend=null}},_onMouseOver:function(){var b=this,a=b._element,e=a.getBoundingClientRect().left;b._width=a.getBoundingClientRect().width;b._isMouseOver=!0;a.onmouseout=function(){a.onmouseout="";a.onmousemove="";b._isMouseOver=!1;b._isLocked||b._setToToday()};a.onmousemove=function(a){b._isLocked||
(a=a||event,a=a.pageX-e,a=Math.min(b._astroData.getDayCount(),Math.max(0,Mf(a/b._width*b._astroData.getDayCount()))),console.log(a,b._astroData.getDayCount()),b._setTo(b._astroData.get_astDay(a)))};a.onmousedown=function(){b._isLocked=!b._isLocked;b._notifySubscribers(b._listeners.lockedChanged,b._isLocked)}},_notifySubscribers:function(b,a){it(b,function(b){b(a)})},subscribe:function(b,a){this._listeners[b].push(a)},setType:function(b){this._renderer.setType(b);this._notifySubscribers(this._listeners.typeChanged,
b)},toggleType:function(){var b=this._renderer.type===f.RenderTypes.PHASES?f.RenderTypes.DAYLENGTH:f.RenderTypes.PHASES;this._renderer.setType(b);this._notifySubscribers(this._listeners.typeChanged,b)}};h.exports=g},function(h,g,d){g=void 0!==cE("canvas").getContext;!(void 0!==g&&(h.exports=g))},function(h,g,d){function c(){}var f=d(1),e=d(0);c.RenderTypes={PHASES:"phases",DAYLENGTH:"daylength"};c.prototype={init:function(a){this._element=a;this._canvas=cE("canvas",{"class":"Sungraph__Canvas"},this._element);
this._ctx=this._canvas.getContext("2d");this._width=this._element.offsetWidth;this._height=this._element.offsetHeight},type:c.RenderTypes.PHASES,setType:function(a){this.type=a;this.render()},render:function(){var a=this,b=f.GetAstroData();a._data=b;a._width=4*b.getDayCount();a._height=a._width/3;a._canvas.width=a._width;a._canvas.height=a._height;a._xStepSize=a._width/b.getDayCount();a._yStepSize=a._height/86400;for(var e=b.getDayCount(),c=0;c<e;c++){var d=b.get_astDay(c);a._drawDay(d,c)}b=b.getTimeChanges();
it(b,function(b){a._drawTimeChange(b)})},_drawTimeChange:function(a){a=Mf(a.i*this._xStepSize);this._drawLine(a,0,a,this._height,1,"green")},_drawLine:function(a,b,e,c,f,d){var k=this._ctx;k.save();k.strokeStyle=d;k.lineWidth=f;k.beginPath();k.moveTo(a,b);k.lineTo(e,c);k.stroke();k.restore()},_drawDay:function(a,b){var k=this,f=k._ctx,d=a.getEventsCount();if(k.type===c.RenderTypes.PHASES){var g=Mf(b*k._xStepSize),h=Math.ceil(k._xStepSize);f.save();0<d?a.p.forEach(function(b){var a=k._height-Mf(b.getStartTime()*
k._yStepSize),c=-Math.ceil(b.getEndTime()*k._yStepSize);f.fillStyle=e.colors[b.getMainType()];f.fillRect(g,a,h,c)}):(f.fillStyle=a.p.length?e.colors[a.p[0].m]:e.colors[1],f.fillRect(g,0,h,k._height));f.restore();b=a.getMidDayPassings();b.length&&(b=b[0].getTime(),b=k._height-Mf(b*k._yStepSize),k._drawLine(g,b,g+h,b,2,"red"));b=a.getMidnightPassings();b.length&&(b=b[0].getTime(),b=k._height-Mf(b*k._yStepSize),k._drawLine(g,b,g+h,b,2,"yellow"))}else if(k.type===c.RenderTypes.DAYLENGTH){f.save();d=[];
for(var l=1;6>l;l++)for(var m=0;m<a.p.length;m++)a.p[m].getMainType()===l&&(d[l]?d[l].duration+=a.p[m].getDurationTime():(d[l]=a.p[m],d[l].duration=a.p[m].getDurationTime()));for(l=a=0;11>=l;l++)if(d[l]){var p=d[l].duration;m=d[l].getMainType();var n=p;p=Mf(b*k._xStepSize);var r=a,t=Math.ceil(k._xStepSize);n=Math.ceil(n*k._yStepSize);f.fillStyle=e.colors[m];f.fillRect(p,r,t,n);a+=n}f.restore()}}};h.exports=c},function(h,g,d){function c(e,a){var b=this;b._element=e;b._options=a;b._graph=f.sungraph;
b._renderType="phases";b._details=null;b._canvas=document.createElement("canvas");b._size=b._element.getBoundingClientRect();b._element.appendChild(b._canvas);b._canvas.style.width="100%";b._canvas.style.height="100%";b._canvas.width=b._size.width;b._canvas.height=b._size.height;f.isReady?b._init():f.onready.push(function(){b._init()})}var f=d(0);c.prototype={_init:function(){var e=this;e._graph=f.sungraph;e._graph.subscribe("dateChanged",function(a){e._details=a;e._onDateChanged(e._details)});e._graph.subscribe("typeChanged",
function(a){e._renderType=a;e._onDateChanged(e._details)})},_onDateChanged:function(e){"phases"===this._renderType?this._renderPhases(e):this._renderDayLength(e)},_renderPhases:function(e){var a=this._canvas.getContext("2d"),b=this._size,k=b.width;b=b.height;a.clearRect(0,0,k,b);for(var c=0;9>c;c++)if(e.p[c]){var d=e.p[c].getStartTime(),g=e.p[c].getDurationTime(),h=e.p[c].getMainType();a.fillStyle=f.colors[h];a.fillRect(d/86400*k,0,g/86400*k,b)}},_renderDayLength:function(e){for(var a=[],b=1;6>b;b++)for(var c=
0;c<e.p.length;c++)e.p[c].getMainType()===b&&(a[b]?a[b].duration+=e.p[c].getDurationTime():(a[b]=e.p[c],a[b].duration=e.p[c].getDurationTime()));e=0;b=this._canvas.getContext("2d");var d=this._size;c=d.width;d=d.height;b.clearRect(0,0,c,d);for(var g=0;9>g;g++)if(a[g]){var h=a[g].duration,q=a[g].getMainType();b.fillStyle=f.colors[q];b.fillRect(e,0,h/86400*c,d);e+=h/86400*c}}};h.exports=c},function(h,g,d){function c(a,b){var c=this;c._element=a;c._options=b;c._type=b.type;c._value=b.value;f.isReady?
c._init():f.onready.push(function(){c._init()})}var f=d(0),e=d(1);c.prototype={_init:function(){var a=this;a._graph=f.sungraph;switch(a._type){case "setType":ac(a._element,"active","phases"===a._value);a._graph.subscribe("typeChanged",function(b){ac(a._element,"active",b===a._value)});aelc(a._element,function(){a.setType()});break;case "toggleType":aelc(a._element,function(){a.toggleType()});break;case "lockedStatus":a._graph.subscribe("lockedChanged",function(b){a.lockedStatus(b)});break;case "highlighter":a._graph.subscribe("dateChanged",
function(b){a.highlighter(b)}),a._graph.subscribe("resized",function(){return a.highlighter(a.lastDetails)})}},toggleType:function(){this._graph.toggleType()},setType:function(){this._graph.setType(this._value)},lockedStatus:function(a){ac(this._element,"Sungraph__LockedIcon--Locked",a)},highlighter:function(a){var b=a.i,c=e.GetAstroData();this._element.style.transform="translateX(".concat(b*(this._graph._width/c.getDayCount()),"px)");this._element.style.width="".concat(this._graph._width/c.getDayCount(),
"px");this.lastDetails=a},lastDetails:null};h.exports=c},function(h,g,d){function c(c,a){var b=this;b._element=c;b._options=a;b._graph=f.sungraph;f.isReady?b._init():f.onready.push(function(){b._init()})}var f=d(0);c.prototype={_init:function(){var c=this;c._graph.subscribe("dateChanged",function(a){c._onDateChanged(a)})},_onDateChanged:function(c){c=c.getDate();this._element.innerHTML=shortDateFmt(c)}};h.exports=c},function(h,g,d){function c(a,b){this._element=a;this._options=b;this._type=b.type;
this._showDuration=b.showDuration;this._graph=f.sungraph;this._init()}var f=d(0),e=d(11);c.prototype={_init:function(){var a=this;a._graph.subscribe("dateChanged",function(b){a._onDateChanged(b)});0<a._type&&!a._showDuration&&(a._element.parentElement.setAttribute("data-type",a._type),a._element.parentElement.addEventListener("mouseenter",function(b){b=b.target.getAttribute("data-type");for(var a=document.getElementsByClassName("Sungraph-DetailedGraph__Phase"),c=0;c<a.length;c++)ac(a[c],"hover",a[c].getAttribute("data-type")===
b);a=document.getElementsByClassName("Sungraph-Legend__Item");for(c=0;c<a.length;c++)ac(a[c],"hover",a[c].getAttribute("data-type")===b)}),a._element.parentElement.addEventListener("mouseleave",function(b){b=document.getElementsByClassName("Sungraph-DetailedGraph__Phase");for(var a=0;a<b.length;a++)ac(b[a],"hover",0);b=document.getElementsByClassName("Sungraph-Legend__Item");for(a=0;a<b.length;a++)ac(b[a],"hover",0)}))},_onDateChanged:function(a){var b={};it(a.p,function(a){var c=a.getMainType();
b[c]=b[c]||[];b[c].push(a)});if(b[this._type]&&0<this._type){var c="";if(this._showDuration){var d=0;it(b[this._type],function(a){d+=a.getDurationTime()});d=60*Math.round(d/60);a=p2(Mf(d/60/60));var f=p2(Math.round((d-3600*a)/60));c="".concat(a,":").concat(f)}else it(b[this._type],function(a){var b=timeFmt(new Date(1E3*a.getStartTime()));a=timeFmt(new Date(1E3*a.getEndTime()));c+="<div><span>".concat(b,"</span> - <span>").concat(a,"</span></div>")});this._element.innerHTML=c}else 0>this._type?-1===
this._type?(a=a.getMidDayPassings(),a.length?(a=a[0].getTime(),this._element.innerHTML=timeFmt(new Date(1E3*a))):this._element.innerHTML=e.NOT_OCCUR):-2===this._type&&(a=a.getMidnightPassings(),a.length?(a=a[0].getTime(),this._element.innerHTML=timeFmt(new Date(1E3*a))):this._element.innerHTML=e.NOT_OCCUR):this._element.innerHTML=this._showDuration?"00:00":e.NOT_FOR_THIS_DAY},_highlightType:function(a){}};h.exports=c},function(h,g){h.exports={NOT_OCCUR:"Skjer ikke",NOT_FOR_THIS_DAY:"Ikke dette d\u00f8gnet",
BLANK:""}}]);
