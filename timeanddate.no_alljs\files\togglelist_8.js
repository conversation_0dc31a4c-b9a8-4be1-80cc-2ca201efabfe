//Copyright timeanddate.com 2021, do not use without permission
(function e$jscomp$0(b,f,h){function c(d,g){if(!f[d]){if(!b[d]){var e="function"==typeof require&&require;if(!g&&e)return e(d,!0);if(a)return a(d,!0);g=Error("Cannot find module '"+d+"'");throw g.code="MODULE_NOT_FOUND",g;}g=f[d]={exports:{}};b[d][0].call(g.exports,function(a){var e=b[d][1][a];return c(e?e:a)},g,g.exports,e$jscomp$0,b,f,h)}return f[d].exports}for(var a="function"==typeof require&&require,e=0;e<h.length;e++)c(h[e]);return c})({1:[function(b,f,h){b=b(3);_T.control.add("ToggleList",
b)},{3:3}],2:[function(b,f,h){b(1);_T.control.applyBindingsOnLoad()},{1:1}],3:[function(b,f,h){b=function(b,c){var a=this;a._element=b;a._options=c;a._children=arrclone(b.children);a._defaultItem=a._options&&"undefined"!==typeof a._options.defaultitem?a._options.defaultitem:0;a._single=a._options&&"undefined"!==typeof a._options.single?a._options.single:!1;if(!a._children)throw"No child elements found";it(a._children,function(b){ael(b.firstChild,"click",function(){a._toggleActive(b)})});location.hash?
a._toggleFromHash():-1!==a._defaultItem&&a._defaultItem<a._children.length&&a._toggleActive(a._children[a._defaultItem]);window.onhashchange=function(){void 0;a._toggleFromHash()}};b.prototype={_toggleFromHash:function(){var b=location.hash.substr(1);(b=gf(b))&&b.parentNode==this._element&&(this._toggleActive(b,!0),siv(b,1))},_toggleActive:function(b,c){this._single&&it(this._children,function(a){ac(a,"active",0)});c=c?!1:hC(b,"active");ac(b,"active",!c)}};f.exports=b},{}]},{},[2]);
