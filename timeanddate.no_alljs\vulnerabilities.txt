=== SECURITY VULNERABILITIES ===

=== HIGH SEVERITY (3 found) ===

1. Csrf Vulnerabilities
   Files: Unknown
   Lines: 0
   Severity: HIGH
   Pattern: 
   Description: Cross-Site Request Forgery (CSRF) - Unauthorized actions on behalf of user
   Fix: Implement CSRF tokens for state-changing operations

2. Xss Dom
   Files: Unknown
   Lines: 0
   Severity: HIGH
   Pattern: 
   Description: DOM-based Cross-Site Scripting (XSS) - Client-side script manipulation
   Fix: Use safe DOM methods, avoid innerHTML with user data

3. Ldap Injection
   Files: Unknown
   Lines: 0
   Severity: HIGH
   Pattern: 
   Description: LDAP Injection - LDAP queries vulnerable to malicious input
   Fix: Review code and implement proper security controls

=== MEDIUM SEVERITY (2 found) ===

1. Weak Crypto
   Files: Unknown
   Lines: 0
   Severity: MEDIUM
   Pattern: 
   Description: Weak Cryptography - Use of deprecated or weak cryptographic algorithms
   Fix: Review code and implement proper security controls

2. Information Disclosure
   Files: Unknown
   Lines: 0
   Severity: MEDIUM
   Pattern: 
   Description: Information Disclosure - Sensitive data exposed in logs/errors
   Fix: Review code and implement proper security controls

=== LOW SEVERITY (1 found) ===

1. Insecure Randomness
   Files: Unknown
   Lines: 0
   Severity: LOW
   Pattern: 
   Description: Insecure Randomness - Predictable random number generation
   Fix: Review code and implement proper security controls

