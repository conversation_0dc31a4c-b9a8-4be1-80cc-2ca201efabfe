#!/usr/bin/env python3
"""
Vulnerability Patterns
Advanced vulnerability patterns for sophisticated security issues detection
"""

# Advanced vulnerability patterns for sophisticated security issues
VULN_PATTERNS = {
    'xss_reflected': [
        r'\.innerHTML\s*=\s*[^;]*(?:location\.|document\.|window\.|\$\(|params\.|req\.)',
        r'document\.write\s*\([^)]*(?:location\.|document\.|window\.|\$\(|params\.|req\.)',
        r'\.outerHTML\s*=\s*[^;]*(?:location\.|document\.|window\.|\$\(|params\.|req\.)',
        r'\.insertAdjacentHTML\s*\([^)]*(?:location\.|document\.|window\.|\$\(|params\.|req\.)',
    ],
    'xss_stored': [
        r'\.innerHTML\s*=\s*[^;]*(?:localStorage\.|sessionStorage\.|database\.|db\.)',
        r'document\.write\s*\([^)]*(?:localStorage\.|sessionStorage\.|database\.|db\.)',
        r'\.html\s*\([^)]*(?:localStorage\.|sessionStorage\.|database\.|db\.)',
    ],
    'xss_dom': [
        r'eval\s*\([^)]*(?:location\.|document\.|window\.)',
        r'setTimeout\s*\([^)]*(?:location\.|document\.|window\.)',
        r'setInterval\s*\([^)]*(?:location\.|document\.|window\.)',
        r'Function\s*\([^)]*(?:location\.|document\.|window\.)',
        r'\.src\s*=\s*[^;]*(?:location\.|document\.|window\.)',
    ],
    'csrf_vulnerabilities': [
        r'(?i)(?:method\s*[:=]\s*["\'](?:post|put|delete)["\'])[^}]*(?!csrf|token)',
        r'(?i)\.(?:post|put|delete)\s*\([^)]*\)(?![^{]*(?:csrf|token|_token))',
        r'(?i)fetch\s*\([^)]*method\s*:\s*["\'](?:POST|PUT|DELETE)["\'][^}]*(?!csrf|token)',
        r'(?i)XMLHttpRequest[^;]*\.open\s*\(\s*["\'](?:POST|PUT|DELETE)["\'][^;]*(?!csrf|token)',
    ],
    'open_redirect': [
        r'(?i)(?:location|window\.location)\s*=\s*[^;]*(?:params\.|req\.|query\.|url\.)',
        r'(?i)(?:location\.href|window\.location\.href)\s*=\s*[^;]*(?:params\.|req\.|query\.)',
        r'(?i)window\.open\s*\([^)]*(?:params\.|req\.|query\.|url\.)',
        r'(?i)(?:redirect|sendRedirect)\s*\([^)]*(?:params\.|req\.|query\.)',
        r'(?i)res\.redirect\s*\([^)]*(?:params\.|req\.|query\.)',
    ],
    'sql_injection': [
        r'(?i)(?:SELECT|INSERT|UPDATE|DELETE)\s+[^;]*\+\s*[^;]*(?:WHERE|VALUES|SET)',
        r'(?i)query\s*\(\s*["\'][^"\']*\+[^"\']*["\']',
        r'(?i)execute\s*\(\s*["\'][^"\']*\+[^"\']*["\']',
        r'(?i)(?:mysql|postgres|sqlite)\.[^(]*\([^)]*\+[^)]*\)',
    ],
    'nosql_injection': [
        r'(?i)(?:find|findOne|update|remove)\s*\(\s*\{[^}]*\$[^}]*\}',
        r'(?i)(?:find|findOne|update|remove)\s*\([^)]*params\.[^)]*\)',
        r'(?i)(?:find|findOne|update|remove)\s*\([^)]*req\.[^)]*\)',
    ],
    'ldap_injection': [
        r'(?i)ldap[^(]*\([^)]*\+[^)]*\)',
        r'(?i)(?:search|bind)\s*\([^)]*\+[^)]*\)',
    ],
    'command_injection': [
        r'(?:exec|spawn|system|shell)\s*\([^)]*(?:params\.|req\.|query\.)',
        r'child_process\.\w+\([^)]*(?:params\.|req\.|query\.)',
        r'(?i)(?:system|shell_exec|exec|passthru)\s*\([^)]*\+[^)]*\)',
    ],
    'path_traversal': [
        r'(?:fs\.readFile|fs\.writeFile|require)\s*\([^)]*(?:params\.|req\.|query\.)',
        r'(?:fs\.readFile|fs\.writeFile|require)\s*\([^)]*\.\.[^)]*\)',
        r'(?:path\.join|path\.resolve)\s*\([^)]*(?:params\.|req\.|query\.)',
    ],
    'xxe_vulnerabilities': [
        r'(?i)(?:parseXML|DOMParser|XMLParser)\s*\([^)]*(?:params\.|req\.)',
        r'(?i)libxml[^(]*\([^)]*LIBXML_NOENT[^)]*\)',
        r'(?i)simplexml_load_string\s*\([^)]*(?:params\.|req\.)',
    ],
    'ssrf_vulnerabilities': [
        r'(?i)(?:fetch|axios|request|http\.get|http\.post)\s*\([^)]*(?:params\.|req\.|query\.)',
        r'(?i)(?:curl|file_get_contents|fopen)\s*\([^)]*(?:params\.|req\.)',
        r'(?i)(?:urllib|requests)\.[^(]*\([^)]*(?:params\.|req\.)',
    ],
    'deserialization_attacks': [
        r'(?i)(?:unserialize|pickle\.loads|yaml\.load)\s*\([^)]*(?:params\.|req\.)',
        r'(?i)JSON\.parse\s*\([^)]*(?:params\.|req\.|query\.)',
        r'(?i)eval\s*\([^)]*(?:params\.|req\.|query\.)',
    ],
    'prototype_pollution': [
        r'__proto__\s*\[',
        r'constructor\s*\[\s*["\']prototype["\']',
        r'(?:merge|extend|assign)\s*\([^)]*(?:params\.|req\.|query\.)',
    ],
    'jwt_vulnerabilities': [
        r'(?i)jwt\.sign\s*\([^)]*algorithm\s*:\s*["\']none["\']',
        r'(?i)jwt\.verify\s*\([^)]*\{\s*algorithms\s*:\s*\[[^]]*["\']none["\']',
        r'(?i)jwt\.decode\s*\([^)]*verify\s*:\s*false',
    ],
    'race_conditions': [
        r'(?i)(?:setTimeout|setInterval)\s*\([^)]*(?:fs\.|database\.|db\.)',
        r'(?i)(?:async|await)[^;]*(?:fs\.|database\.|db\.)[^;]*(?:fs\.|database\.|db\.)',
    ],
    'timing_attacks': [
        r'(?i)(?:bcrypt\.compare|crypto\.timingSafeEqual)\s*\([^)]*\)',
        r'(?i)(?:password|hash)\s*===?\s*[^;]*',
    ],
    'insecure_randomness': [
        r'Math\.random\(\)',
        r'(?i)(?:rand|random)\s*\(\s*\)',
        r'new\s+Date\(\)\.getTime\(\)',
    ],
    'weak_crypto': [
        r'(?i)(?:md5|sha1|des|rc4)\s*\(',
        r'(?i)crypto\.createHash\s*\(\s*["\'](?:md5|sha1)["\']',
        r'(?i)algorithm\s*:\s*["\'](?:des|rc4|md5|sha1)["\']',
    ],
    'information_disclosure': [
        r'(?i)(?:console\.log|console\.error|console\.warn)\s*\([^)]*(?:password|token|key|secret)',
        r'(?i)(?:alert|confirm|prompt)\s*\([^)]*(?:password|token|key|secret)',
        r'(?i)(?:throw|error)\s*\([^)]*(?:password|token|key|secret)',
    ],
    'cors_misconfiguration': [
        r'Access-Control-Allow-Origin\s*:\s*["\']?\*["\']?',
        r'cors\s*\(\s*\{\s*origin\s*:\s*true',
        r'Access-Control-Allow-Credentials\s*:\s*true[^}]*Access-Control-Allow-Origin\s*:\s*\*',
    ],
    'clickjacking': [
        r'(?i)(?:X-Frame-Options|frame-options)\s*:\s*["\']?(?:DENY|SAMEORIGIN)["\']?',
        r'(?i)(?:Content-Security-Policy|csp)[^;]*frame-ancestors',
    ],
    'session_fixation': [
        r'(?i)session\.regenerate\s*\(',
        r'(?i)session\.id\s*=',
        r'(?i)(?:JSESSIONID|PHPSESSID|session_id)\s*=',
    ]
}
