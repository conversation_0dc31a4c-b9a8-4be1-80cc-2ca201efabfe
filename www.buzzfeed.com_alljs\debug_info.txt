=== DEBUG INFORMATION ===

1. Debug Statement
   File: 297-333f0fe9ae8254bc.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

2. Debug Statement
   File: 727-c7a9562bc43d7ec1.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

3. Debug Statement
   File: index-a24ecfbf331a17c6.js
   Line: 1
   Statement: console.debug(
   Risk: LOW - Debug code should be removed from production

4. Debug Statement
   File: 592-c7023aad55ee8b34.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

5. Debug Statement
   File: 592-c7023aad55ee8b34.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

6. Debug Statement
   File: 195-cf6b2abe773a422c.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

7. Debug Statement
   File: 195-cf6b2abe773a422c.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

8. Debug Statement
   File: 195-cf6b2abe773a422c.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

9. Debug Statement
   File: 195-cf6b2abe773a422c.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

10. Debug Statement
   File: 195-cf6b2abe773a422c.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

11. Debug Statement
   File: 195-cf6b2abe773a422c.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

12. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

13. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

14. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

15. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

16. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

17. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

18. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

19. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

20. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

21. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

22. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

23. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

24. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

25. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

26. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

27. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

28. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: console.debug(
   Risk: LOW - Debug code should be removed from production

29. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: alert(
   Risk: LOW - Debug code should be removed from production

30. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: alert(
   Risk: LOW - Debug code should be removed from production

31. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: confirm(
   Risk: LOW - Debug code should be removed from production

32. Debug Statement
   File: 995-24dfd29dd122d8f3.js
   Line: 1
   Statement: confirm(
   Risk: LOW - Debug code should be removed from production

33. Debug Statement
   File: 167-e3a97434bfca6614.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

34. Debug Statement
   File: 167-e3a97434bfca6614.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

35. Debug Statement
   File: 167-e3a97434bfca6614.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

36. Debug Statement
   File: 167-e3a97434bfca6614.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

37. Debug Statement
   File: 167-e3a97434bfca6614.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

38. Debug Statement
   File: 167-e3a97434bfca6614.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

39. Debug Statement
   File: 941-f7474f408b2824c4.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

40. Debug Statement
   File: 941-f7474f408b2824c4.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

41. Debug Statement
   File: 941-f7474f408b2824c4.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

42. Debug Statement
   File: 941-f7474f408b2824c4.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

43. Debug Statement
   File: 941-f7474f408b2824c4.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

44. Debug Statement
   File: 941-f7474f408b2824c4.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

45. Debug Statement
   File: 847-630b23a17f0a467e.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

46. Debug Statement
   File: 847-630b23a17f0a467e.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

47. Debug Statement
   File: 127-986861906dd813d9.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

48. Debug Statement
   File: 127-986861906dd813d9.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

49. Debug Statement
   File: index-c9d6d8f815c54ae9.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

50. Debug Statement
   File: 862-36989a28007dad1a.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

51. Debug Statement
   File: category-1caa5c62ec10ad01.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

52. Debug Statement
   File: 884-1e5bb3e5299f3027.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

53. Debug Statement
   File: 884-1e5bb3e5299f3027.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

54. Debug Statement
   File: 553-4fee0a65752085b0.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

55. Debug Statement
   File: 403-6ce5eb073f06f34c.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

56. Debug Statement
   File: widgets.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

57. Debug Statement
   File: widgets.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

58. Debug Statement
   File: commons.ce6779684f98b382dd5b.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

59. Debug Statement
   File: commons.ce6779684f98b382dd5b.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

60. Debug Statement
   File: commons.ce6779684f98b382dd5b.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

61. Debug Statement
   File: main-26e11a14474b3711dfea.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

62. Debug Statement
   File: main-26e11a14474b3711dfea.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

63. Debug Statement
   File: embed.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

64. Debug Statement
   File: embed.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

65. Debug Statement
   File: embed.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

66. Debug Statement
   File: embed.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

67. Debug Statement
   File: a29ae703-c6fc0f98da28dcbe.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

68. Debug Statement
   File: a29ae703-c6fc0f98da28dcbe.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

69. Debug Statement
   File: 335-ba7ec9410f2248c0.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

70. Debug Statement
   File: 335-ba7ec9410f2248c0.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

71. Debug Statement
   File: 554-f3177a7fbcfc6d49.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

72. Inline Javascript
   File: page_100.html
   Line: 10
   Content: 
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new wind
   Risk: LOW - Debug code should be removed from production

73. Inline Javascript
   File: page_100.html
   Line: 276
   Content: 
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"Contact","created_at":"2023-08-01T
   Risk: LOW - Debug code should be removed from production

74. Inline Javascript
   File: page_101.html
   Line: 10
   Content: 
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new wind
   Risk: LOW - Debug code should be removed from production

75. Inline Javascript
   File: page_101.html
   Line: 344
   Content: 
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"Jobs","created_at":"2023-07-19T20:
   Risk: LOW - Debug code should be removed from production

76. Inline Javascript
   File: page_12.html
   Line: 10
   Content: 
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new wind
   Risk: LOW - Debug code should be removed from production

77. Inline Javascript
   File: page_12.html
   Line: 145
   Content: 
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"Accessibility","created_at":"2023-
   Risk: LOW - Debug code should be removed from production

78. Inline Javascript
   File: page_2.html
   Line: 10
   Content: 
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new wind
   Risk: LOW - Debug code should be removed from production

79. Inline Javascript
   File: page_2.html
   Line: 558
   Content: 
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"User Agreement","created_at":"2023
   Risk: LOW - Debug code should be removed from production

80. Inline Javascript
   File: page_63.html
   Line: 10
   Content: 
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new wind
   Risk: LOW - Debug code should be removed from production

81. Inline Javascript
   File: page_63.html
   Line: 406
   Content: 
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"About","created_at":"2023-07-18T14
   Risk: LOW - Debug code should be removed from production

82. Inline Javascript
   File: page_73.html
   Line: 10
   Content: 
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new wind
   Risk: LOW - Debug code should be removed from production

83. Inline Javascript
   File: page_73.html
   Line: 270
   Content: 
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"Press","created_at":"2023-07-31T18
   Risk: LOW - Debug code should be removed from production

84. Inline Javascript
   File: page_74.html
   Line: 10
   Content: 
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new wind
   Risk: LOW - Debug code should be removed from production

85. Inline Javascript
   File: page_74.html
   Line: 306
   Content: 
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"DMCA","created_at":"2023-08-09T17:
   Risk: LOW - Debug code should be removed from production

86. Inline Javascript
   File: page_83.html
   Line: 10
   Content: 
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new wind
   Risk: LOW - Debug code should be removed from production

87. Inline Javascript
   File: page_83.html
   Line: 216
   Content: 
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"Membership Refund Policy","created
   Risk: LOW - Debug code should be removed from production

88. Inline Javascript
   File: page_84.html
   Line: 10
   Content: 
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new wind
   Risk: LOW - Debug code should be removed from production

89. Inline Javascript
   File: page_84.html
   Line: 558
   Content: 
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"User Agreement","created_at":"2023
   Risk: LOW - Debug code should be removed from production

90. Inline Javascript
   File: page_86.html
   Line: 10
   Content: 
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new wind
   Risk: LOW - Debug code should be removed from production

91. Inline Javascript
   File: page_86.html
   Line: 192
   Content: 
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"EU Digital Services Act","created_
   Risk: LOW - Debug code should be removed from production

92. Inline Javascript
   File: page_87.html
   Line: 10
   Content: 
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new wind
   Risk: LOW - Debug code should be removed from production

93. Inline Javascript
   File: page_87.html
   Line: 270
   Content: 
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"Press","created_at":"2023-07-31T18
   Risk: LOW - Debug code should be removed from production

94. Inline Javascript
   File: page_92.html
   Line: 10
   Content: 
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new wind
   Risk: LOW - Debug code should be removed from production

95. Inline Javascript
   File: page_92.html
   Line: 306
   Content: 
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"DMCA","created_at":"2023-08-09T17:
   Risk: LOW - Debug code should be removed from production

96. Inline Javascript
   File: page_93.html
   Line: 10
   Content: 
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new wind
   Risk: LOW - Debug code should be removed from production

97. Inline Javascript
   File: page_93.html
   Line: 306
   Content: 
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"DMCA","created_at":"2023-08-09T17:
   Risk: LOW - Debug code should be removed from production

