(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[167],{78051:function(e,t){"use strict";function n(e,t){const n=e.match(t);return n&&n.length?n[0]:null}var r={getBuzzfeedSubdomainOrWildcard(e){const t=n(e,"(dev|stage|www).(buzzfeed|buzzfeednews).(com|io)$");return t||n(e,".?[a-z]+.[a-z]+$")},get(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const n=`${e}=`;if("undefined"===typeof document)return t;const r=document.cookie.split(";");for(let i=0;i<r.length;i++){let e=r[i];for(;" "===e.charAt(0);)e=e.substring(1,e.length);if(0===e.indexOf(n))return e.substring(n.length,e.length)}return t},set(e){let{name:t,value:n,days:r,domain:i}=e,o="";if(r){let e=new Date;e.setTime(e.getTime()+24*r*60*60*1e3),o=`; expires=${e.toGMTString()}`}let a="";return void 0!==i&&(a=`; domain=${i}`),document.cookie=`${t}=${n}${o}${a}; path=/`},remove(e,t){return this.set({name:e,value:"",days:-1,domain:t})}};t.Z=r},95517:function(e,t){"use strict";t.Z=function(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(e=>(e^(()=>{try{return crypto.getRandomValues(new Uint8Array(1))[0]}catch(e){return 255*Math.random()}})()&15>>e/4).toString(16)))}},25616:function(e,t,n){"use strict";n.d(t,{H:function(){return oe}});var r=n(94776),i=n.n(r),o=n(78051),a=n(47526),u=n(95517),c={isEqual:!0,isMatchingKey:!0,isPromise:!0,maxSize:!0,onCacheAdd:!0,onCacheChange:!0,onCacheHit:!0,transformKey:!0},s=Array.prototype.slice;function l(e){var t=e.length;return t?1===t?[e[0]]:2===t?[e[0],e[1]]:3===t?[e[0],e[1],e[2]]:s.call(e,0):[]}function f(e,t){return e===t||e!==e&&t!==t}function d(e,t){var n={};for(var r in e)n[r]=e[r];for(var r in t)n[r]=t[r];return n}var p=function(){function e(e){this.keys=[],this.values=[],this.options=e;var t="function"===typeof e.isMatchingKey;t?this.getKeyIndex=this._getKeyIndexFromMatchingKey:e.maxSize>1?this.getKeyIndex=this._getKeyIndexForMany:this.getKeyIndex=this._getKeyIndexForSingle,this.canTransformKey="function"===typeof e.transformKey,this.shouldCloneArguments=this.canTransformKey||t,this.shouldUpdateOnAdd="function"===typeof e.onCacheAdd,this.shouldUpdateOnChange="function"===typeof e.onCacheChange,this.shouldUpdateOnHit="function"===typeof e.onCacheHit}return Object.defineProperty(e.prototype,"size",{get:function(){return this.keys.length},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"snapshot",{get:function(){return{keys:l(this.keys),size:this.size,values:l(this.values)}},enumerable:!0,configurable:!0}),e.prototype._getKeyIndexFromMatchingKey=function(e){var t=this.options,n=t.isMatchingKey,r=t.maxSize,i=this.keys,o=i.length;if(!o)return-1;if(n(i[0],e))return 0;if(r>1)for(var a=1;a<o;a++)if(n(i[a],e))return a;return-1},e.prototype._getKeyIndexForMany=function(e){var t=this.options.isEqual,n=this.keys,r=n.length;if(!r)return-1;if(1===r)return this._getKeyIndexForSingle(e);var i,o,a=e.length;if(a>1){for(var u=0;u<r;u++)if((i=n[u]).length===a){for(o=0;o<a&&t(i[o],e[o]);o++);if(o===a)return u}}else for(u=0;u<r;u++)if((i=n[u]).length===a&&t(i[0],e[0]))return u;return-1},e.prototype._getKeyIndexForSingle=function(e){var t=this.keys;if(!t.length)return-1;var n=t[0],r=n.length;if(e.length!==r)return-1;var i=this.options.isEqual;if(r>1){for(var o=0;o<r;o++)if(!i(n[o],e[o]))return-1;return 0}return i(n[0],e[0])?0:-1},e.prototype.orderByLru=function(e,t,n){for(var r=this.keys,i=this.values,o=r.length,a=n;a--;)r[a+1]=r[a],i[a+1]=i[a];r[0]=e,i[0]=t;var u=this.options.maxSize;o===u&&n===o?(r.pop(),i.pop()):n>=u&&(r.length=i.length=u)},e.prototype.updateAsyncCache=function(e){var t=this,n=this.options,r=n.onCacheChange,i=n.onCacheHit,o=this.keys[0],a=this.values[0];this.values[0]=a.then((function(n){return t.shouldUpdateOnHit&&i(t,t.options,e),t.shouldUpdateOnChange&&r(t,t.options,e),n}),(function(e){var n=t.getKeyIndex(o);throw-1!==n&&(t.keys.splice(n,1),t.values.splice(n,1)),e}))},e}();var h=function e(t,n){if(void 0===n&&(n={}),function(e){return"function"===typeof e&&e.isMemoized}(t))return e(t.fn,d(t.options,n));if("function"!==typeof t)throw new TypeError("You must pass a function to `memoize`.");var r=n.isEqual,i=void 0===r?f:r,o=n.isMatchingKey,a=n.isPromise,u=void 0!==a&&a,s=n.maxSize,h=void 0===s?1:s,v=n.onCacheAdd,m=n.onCacheChange,y=n.onCacheHit,g=n.transformKey,b=d({isEqual:i,isMatchingKey:o,isPromise:u,maxSize:h,onCacheAdd:v,onCacheChange:m,onCacheHit:y,transformKey:g},function(e){var t={};for(var n in e)c[n]||(t[n]=e[n]);return t}(n)),_=new p(b),w=_.keys,S=_.values,O=_.canTransformKey,E=_.shouldCloneArguments,A=_.shouldUpdateOnAdd,x=_.shouldUpdateOnChange,k=_.shouldUpdateOnHit,j=function e(){var n=E?l(arguments):arguments;O&&(n=g(n));var r=w.length?_.getKeyIndex(n):-1;if(-1!==r)k&&y(_,b,e),r&&(_.orderByLru(w[r],S[r],r),x&&m(_,b,e));else{var i=t.apply(this,arguments),o=E?n:l(arguments);_.orderByLru(o,i,w.length),u&&_.updateAsyncCache(e),A&&v(_,b,e),x&&m(_,b,e)}return S[0]};return j.cache=_,j.fn=t,j.isMemoized=!0,j.options=b,j};function v(e){return(e+="").indexOf("#")>-1?e.substr(e.indexOf("#"),e.length):""}function m(e){return(e+="").indexOf("#")>-1?e.substr(0,e.indexOf("#")):e}function y(e){return e.indexOf("?")>-1}function g(e){return e=function(e){if(!y(e))return e;const t=v(e);return(e=m(e)).substr(0,e.indexOf("?"))+t}(e=m(e)),e}const b=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window;var t=e.location.hostname;const n=["buzzfeed","buzzfeednews","tasty","huffpost","huffingtonpost"],r=t.split(".")[0];return r&&"stage"!==r&&-1===n.indexOf(r)&&t.split(".").length>=3&&(t=t.substring(r.length+1)),t};function _(e,t,n){var r,i,o,a,u,c,s;for(this.version="1.0.1",this.minAlphabetLength=16,this.sepDiv=3.5,this.guardDiv=12,this.errorAlphabetLength="error: alphabet must contain at least X unique characters",this.errorAlphabetSpace="error: alphabet cannot contain spaces",this.alphabet="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",this.seps="cfhistuCFHISTU",this.minHashLength=parseInt(t,10)>0?t:0,this.salt="string"===typeof e?e:"","string"===typeof n&&(this.alphabet=n),r="",i=0,a=this.alphabet.length;i!==a;i++)-1===r.indexOf(this.alphabet[i])&&(r+=this.alphabet[i]);if(this.alphabet=r,this.alphabet.length<this.minAlphabetLength)throw this.errorAlphabetLength.replace("X",this.minAlphabetLength);if(-1!==this.alphabet.search(" "))throw this.errorAlphabetSpace;for(i=0,a=this.seps.length;i!==a;i++)-1===(o=this.alphabet.indexOf(this.seps[i]))?this.seps=this.seps.substr(0,i)+" "+this.seps.substr(i+1):this.alphabet=this.alphabet.substr(0,o)+" "+this.alphabet.substr(o+1);this.alphabet=this.alphabet.replace(/ /g,""),this.seps=this.seps.replace(/ /g,""),this.seps=this.consistentShuffle(this.seps,this.salt),(!this.seps.length||this.alphabet.length/this.seps.length>this.sepDiv)&&(1===(u=Math.ceil(this.alphabet.length/this.sepDiv))&&u++,u>this.seps.length?(c=u-this.seps.length,this.seps+=this.alphabet.substr(0,c),this.alphabet=this.alphabet.substr(c)):this.seps=this.seps.substr(0,u)),this.alphabet=this.consistentShuffle(this.alphabet,this.salt),s=Math.ceil(this.alphabet.length/this.guardDiv),this.alphabet.length<3?(this.guards=this.seps.substr(0,s),this.seps=this.seps.substr(s)):(this.guards=this.alphabet.substr(0,s),this.alphabet=this.alphabet.substr(s))}_.prototype.encode=function(){var e,t,n="",r=Array.prototype.slice.call(arguments);if(!r.length)return n;for(r[0]instanceof Array&&(r=r[0]),e=0,t=r.length;e!==t;e++)if("number"!==typeof r[e]||r[e]%1!==0||r[e]<0)return n;return this._encode(r)},_.prototype.decode=function(e){return e.length&&"string"===typeof e?this._decode(e,this.alphabet):[]},_.prototype.encodeHex=function(e){var t,n,r;if(e=e.toString(),!/^[0-9a-fA-F]+$/.test(e))return"";for(t=0,n=(r=e.match(/[\w\W]{1,12}/g)).length;t!==n;t++)r[t]=parseInt("1"+r[t],16);return this.encode.apply(this,r)},_.prototype.decodeHex=function(e){var t,n,r=[],i=this.decode(e);for(t=0,n=i.length;t!==n;t++)r+=i[t].toString(16).substr(1);return r},_.prototype._encode=function(e){var t,n,r,i,o,a,u,c,s,l,f,d=this.alphabet,p=e.length,h=0;for(r=0,i=e.length;r!==i;r++)h+=e[r]%(r+100);for(n=t=d[h%d.length],r=0,i=e.length;r!==i;r++)o=e[r],a=n+this.salt+d,d=this.consistentShuffle(d,a.substr(0,d.length)),t+=u=this.hash(o,d),r+1<p&&(c=(o%=u.charCodeAt(0)+r)%this.seps.length,t+=this.seps[c]);for(t.length<this.minHashLength&&(s=(h+t[0].charCodeAt(0))%this.guards.length,(t=this.guards[s]+t).length<this.minHashLength&&(s=(h+t[2].charCodeAt(0))%this.guards.length,t+=this.guards[s])),l=parseInt(d.length/2,10);t.length<this.minHashLength;)(f=(t=(d=this.consistentShuffle(d,d)).substr(l)+t+d.substr(0,l)).length-this.minHashLength)>0&&(t=t.substr(f/2,this.minHashLength));return t},_.prototype._decode=function(e,t){var n,r,i,o,a=[],u=0,c=new RegExp("["+this.guards+"]","g"),s=e.replace(c," "),l=s.split(" ");if(3!==l.length&&2!==l.length||(u=1),"undefined"!==typeof(s=l[u])[0]){for(n=s[0],s=s.substr(1),c=new RegExp("["+this.seps+"]","g"),u=0,r=(l=(s=s.replace(c," ")).split(" ")).length;u!==r;u++)i=l[u],o=n+this.salt+t,t=this.consistentShuffle(t,o.substr(0,t.length)),a.push(this.unhash(i,t));this._encode(a)!==e&&(a=[])}return a},_.prototype.consistentShuffle=function(e,t){var n,r,i,o,a,u;if(!t.length)return e;for(o=e.length-1,a=0,u=0;o>0;o--,a++)u+=n=t[a%=t.length].charCodeAt(0),i=e[r=(n+a+u)%o],e=(e=e.substr(0,r)+e[o]+e.substr(r+1)).substr(0,o)+i+e.substr(o+1);return e},_.prototype.hash=function(e,t){var n="",r=t.length;do{n=t[e%r]+n,e=parseInt(e/r,10)}while(e);return n},_.prototype.unhash=function(e,t){var n,r=0;for(n=0;n<e.length;n++)r+=t.indexOf(e[n])*Math.pow(t.length,e.length-n-1);return r};const w=parseInt(1e10*Math.random(),10),S=(0,u.Z)(),O=e=>{if(0!==e.indexOf(".")){const t=/[0-9A-Za-z]+/.exec(e);return null!==t&&t[0]===e&&parseInt(e,36)}const t=e.substr(1,2);return function(e){let{salt:t=null}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new _(t).decode(e)[0]}(e.substr(3),{salt:t})},E=e=>{const t=parseInt(25*Math.random()+10,10).toString(36)+parseInt(25*Math.random()+10,10).toString(36);return`.${t}${function(e){let{salt:t=null,length:n=32}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new _(t,n).encode(e)}(e,{salt:t,length:0})}`},A=e=>{const{u:t,uuid:n}=decodeURIComponent(e).split("&").map((e=>e.split("="))).reduce(((e,t)=>{let[n,r]=t;return{...e,[n]:r}}),{});return{legacyIdentifier:O(t||""),identifier:n}},x=function(){let{legacy:e=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t={name:"bf_visit",days:1e4,domain:b()},n=o.Z.get(t.name),{legacyIdentifier:r,identifier:i}=A(n),a=E(w);return e?r||(o.Z.set({...t,value:encodeURIComponent(`u=${a}&uuid=${i||S}&v=2`)}),w):i||r?i||String(r):(o.Z.set({...t,value:encodeURIComponent(`u=${a}&uuid=${S}&v=2`)}),S)};function k(e){return e+"|expiration"}const j=function(){try{return localStorage.setItem("ls_test",!0),localStorage.removeItem("ls_test"),!0}catch(e){return!1}}();var I={set(e){j&&"undefined"!==typeof e&&(localStorage.setItem(e.key,e.value),e.expires&&localStorage.setItem(k(e.key),Date.now()+e.expires))},get(e){return j?function(e){var t=localStorage.getItem(k(e));return t&&t<=Date.now()}(e)?(this.remove(e),null):localStorage.getItem(e):null},sessionSet(e){j&&"undefined"!==typeof e&&sessionStorage.setItem(e.key,e.value)},sessionGet:e=>j?sessionStorage.getItem(e):null,remove(e){j&&(localStorage.removeItem(k(e)),localStorage.removeItem(e))},clear(){j&&localStorage.clear()}};const C="CLIENT_EVENT_TRACKING";var P=e=>{const t="pdv3-previous_page_session_id",n="cet-page_session_id";if("undefined"===typeof window)return{page_session_id:"",previous_page_session_id:""};const r=window.frameElement?window.parent:window;let i=I.get(n);if(r[C]=r[C]||{},r[C].current_page_session_url===e&&i)return{page_session_id:i,previous_page_session_id:I.get(t)||""};r[C].current_page_session_url=e,i=(0,u.Z)()||"00000000-0000-0000-0000-000000000000";const o=I.get(n)||"";return I.set({key:n,value:i}),I.set({expires:18e5,key:t,value:o}),{page_session_id:i,previous_page_session_id:o}};function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function L(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function z(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){L(e,t,n[t])}))}return e}function M(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(c){u=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(u)throw i}}return o}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return T(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return T(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Z=h((function(e){var t=e.url,n={name:"bf-xdomain-session-uuid",days:.5/24,domain:b()};return h((function(){var e=String(x()),r=o.Z.get(n.name,(0,u.Z)());o.Z.set(z({},n,{value:r}));var i=window.frameElement?window.parent:window;return z({client_uuid:e,client_session_id:r,random_user_uuid:o.Z.get("user_uuid","unauthenticated"),referrer_uri:i.document.referrer},P(g(t)))}),{transformKey:JSON.stringify})}),{transformKey:function(e){return g(M(e,1)[0].url)}}),D=function(e){var t=e.url;return function(){return{event_uri:t,event_ts:Math.round(Date.now()/1e3),event_uuid:(0,u.Z)()}}};function q(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}var B=h((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.track_amp,n=void 0!==t&&t,r=q(e,["track_amp"]);return n?h((function(){return{amp_id:(0,a.G_)(r)}}),{transformKey:JSON.stringify}):{}}),{transformKey:JSON.stringify}),F=B,R=function(e){var t=e.page_edition,n=void 0===t?(0,a.q9)("page_edition"):t;return function(){var e,t=window.frameElement?window.parent:window;return{mode:(e={win:t},e.win.matchMedia("screen and (max-width:51.9rem)").matches?"mobile":"desktop"),page_edition:n,viewport_size:{width:Number(t.screen.width),height:Number(t.screen.height)}}}},U=function(e){var t=e.type,n=void 0===t?(0,a.q9)("type"):t,r=e.source,i=void 0===r?(0,a.q9)("source"):r;return function(){return{type:n,source:i}}};var H=["web_impression","web_pageview"],N=["web_performance_metric","web_time_spent"],$=function(e){var t=["event_ts","mode","viewport_size"];H.includes(e.type)&&t.push("event_uuid");var n,r=Object.keys(e).sort();"function"===typeof Set&&(n=new Set(t));var i=r.filter((function(e){return n?!n.has(e):!t.includes(e)}));return(0,a.pE)(JSON.stringify(e,i))},K={dev:{tracking_url:"https://nsq-api-public.dev.buzzfeed.io",debug:!0},test:{tracking_url:"https://nsq-api-public.dev.buzzfeed.io",debug:!0},stage:{tracking_url:"https://pixiedust-stage.buzzfeed.com",debug:!0},prod:{tracking_url:"https://pixiedust.buzzfeed.com",debug:!1},"app-west":{tracking_url:"https://pixiedust.buzzfeed.com",debug:!1}};function W(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function J(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(c){u=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(u)throw i}}return o}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return W(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return W(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function V(e){var t=e.env,n=void 0===t?"dev":t,r=J(function(e){var t,n=e.debug,r=e.tracking_url,i=[],o=function(){if(i.length){var e=JSON.stringify(i),o=document.createEvent("CustomEvent");if(o.initCustomEvent("cet-event",!1,!1,i),dispatchEvent(o),n)window.fetch("".concat(r,"/events"),{method:"POST",body:e,keepalive:!0}).then((function(e){return e.json()})).then((function(e){e.errors&&e.debug&&(console.group("%c \ud83d\udea8 Rejected client events \ud83d\udea8","background-color: #250201; color: #ee8783; border: 1px solid #540b06"),e.debug.forEach((function(e){return console.table(e.validation)})),console.groupEnd())})).catch((function(){var t=JSON.parse(e);console.group("%cClient Event Tracking: %crun nsq_api_public to verify events","font-weight: bold;","color: gray; font-size: .5rem;"),t.forEach((function(e){console.groupCollapsed('"%c'.concat(e.type,'"'),"font-weight: bold; font-family: monospace;"),console.table(e),console.groupEnd()})),console.groupEnd()}));else{var a;if(navigator&&navigator.sendBeacon)a=navigator.sendBeacon("".concat(r,"/events"),e);else{var u=new XMLHttpRequest;u.open("POST","".concat(r,"/events"),!1),u.onerror=function(){},u.setRequestHeader("Accept","*/*"),u.setRequestHeader("Content-Type","text/plain;charset=UTF-8"),a=u.send(e)}!a&&window.raven&&Math.random()<.1&&window.raven.captureException("Client Event Tracking: sendBeacon could not process a queue.")}clearTimeout(t),t=null,i=[]}};return[function(e){i.push(e),10===i.length&&o(),t||(t=setTimeout(o,200))},o]}(K[n]),2),i=r[0],o=r[1],u=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).length,t=void 0===e?1e3:e,n=[];return{push:function(e){if(!N.includes(e.type)){var r=$(e);n.unshift(r)>t&&(n.length=t)}},includes:function(e){var t=$(e);return n.includes(t)},get history(){return n},get length(){return n.length}}}({length:1e3});return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.flush,c=void 0!==r&&r,s=(0,a.IJ)(e,{debug:K[n].debug});u.includes(s)||(u.push(s),i(s),c&&o())}}const G=()=>{const e=Y().location.search;return"true"===new URLSearchParams(e).get("wv_debug")},Y=()=>window.frameElement?window.parent:window,X=()=>{const e=(()=>{const e=Y(),t=e.navigator.userAgent.toLowerCase(),n=new RegExp("buzzfeed/[0-9.]+"),r=e.location.search,i=new URLSearchParams(r).get("s"),o=n.test(t)||"mobile_app"===i;return G()&&console.log("WEBVIEW DEBUG: isBFAppWebview: ",{isBFAppWebview:o,userAgent:t,sParam:i}),o})(),t=Y();if(e){const n=!!t?.BZFD_APP?.feature_support;return G()&&console.log("WEBVIEW DEBUG: isEnhancedWebview:",{isEnhancedWebview:n,isBFAppWebview:e,BZFD_APP:t?.BZFD_APP}),n}return G()&&console.log("WEBVIEW DEBUG: isEnhancedWebview:",{isEnhancedWebview:!1,isBFAppWebview:e,BZFD_APP:t?.BZFD_APP}),!1},Q=e=>{let{feature:t,version:n=1}=e;const r=X();if(r){const e=Y(),r=e.BZFD_APP.feature_support||{};if(t in r&&r[t].version>=n)return G()&&console.log("WEBVIEW DEBUG: isEnhancedWebview: ",{isEnhancedWebviewFeatureSupported:!0,feature:t,version:n,feature_support:e.BZFD_APP.feature_support}),!0}return G()&&console.log("WEBVIEW DEBUG: isEnhancedWebview: ",{isEnhancedWebviewFeatureSupported:!1,isWebviewSupported:r,feature:t,version:n}),!1};function ee(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function te(e,t,n,r,i,o,a){try{var u=e[o](a),c=u.value}catch(s){return void n(s)}u.done?t(c):Promise.resolve(c).then(r,i)}function ne(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function re(e){return function(e){if(Array.isArray(e))return ee(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return ee(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ee(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ie(){var e="true"===o.Z.get("is_bot");if(window.location.search.includes("e2e_test"))try{return{e2e_test:new URLSearchParams(window.location.search).get("e2e_test"),is_bot:!0}}catch(t){}return e?{is_bot:!0}:{}}var oe=function(e){var t=e.amp_options,n=void 0===t?{}:t,r=e.env,o=e.source;var u=V({env:r});return function(){var e,t=(e=i().mark((function e(t){var c,s,l,f,d,p,h,v,m,y,g,b,_,w,S,O,E,A,x,k=arguments;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(c=t.flush,s=void 0!==c&&c,l=t.required_layers,f=void 0===l?[]:l,d=t.sample_rate,p=void 0===d?1:d,h=t.type,v=k.length,m=new Array(v>1?v-1:0),y=1;y<v;y++)m[y-1]=k[y];return e.next=4,(0,a.oR)({context:{env:r},layers:m});case 4:return g=e.sent,b=g.page_edition,_=ne(g,["page_edition"]),w=window.frameElement?window.parent:window,S=Q({feature:"cet",version:1}),O=!Q({feature:"cet",version:2}),E=S?h.replace("web_","app_"):h,O&&m.forEach((function(e){(null===e||void 0===e?void 0:e.random_user_uuid)&&(e.random_user_uuid=e.random_user_uuid.toLowerCase())})),A=[U({source:o,type:E}),D({url:w.document.URL}),Z({url:w.document.URL}),F(n),R({page_edition:b})].concat(re(m),re(f.map((function(e){return e(_)}))),[ie()]),e.next=15,(0,a.oR)({context:{env:r},layers:A});case 15:if(x=e.sent,!(window.location.search.includes("e2e_test")||Math.random()<=p)){e.next=19;break}return e.next=19,u(x,{flush:s});case 19:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){te(o,r,i,a,u,"next",e)}function u(e){te(o,r,i,a,u,"throw",e)}a(void 0)}))});return function(e){return t.apply(this,arguments)}}()}},57455:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var r=n(47526),i=function(e){var t=e.experiment_id,n=void 0===t?(0,r.q9)("experiment_id"):t;return{experiment_id:(0,r.c$)(n)}},o={required_layers:[n(36865).D,i],type:"web_ab_test"}},84645:function(e,t,n){"use strict";var r={required_layers:[n(36865).D],type:"web_addressability"};t.Z=r},34306:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});var r=n(47526),i=function(e){var t=e.action_type,n=void 0===t?(0,r.q9)("action_type"):t,i=e.action_value,o=void 0===i?(0,r.q9)("action_value"):i;return function(){return{action_type:n,action_value:o.toString()}}},o=n(36865),a=n(91373),u=n(73470),c=n(92915),s={flush:!0,required_layers:[i,o.D,a.Z,u.Z,c.Z],type:"web_content_action"}},33141:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});var r=n(36865),i=n(47526),o=function(e){var t=e.target_content_url,n=void 0===t?(0,i.q9)("target_content_url"):t;return function(){return{target_content_url:n}}},a=n(91373),u=n(73470),c=n(92915),s={flush:!0,required_layers:[r.D,o,a.Z,u.Z,c.Z],type:"web_external_link"}},70450:function(e,t,n){"use strict";var r=n(36865),i=n(92579),o=n(81433),a=n(91373),u=n(73470),c=n(92915),s={required_layers:[r.D,i.Z,o.Z,a.Z,u.Z,c.Z],type:"web_impression"};t.Z=s},12832:function(e,t,n){"use strict";var r=n(36865),i=n(92579),o=n(81433),a=n(91373),u=n(73470),c=n(92915),s={flush:!0,required_layers:[r.D,i.Z,o.Z,a.Z,u.Z,c.Z],type:"web_internal_link"};t.Z=s},62378:function(e,t,n){"use strict";var r={required_layers:[n(36865).D],type:"web_pageview"};t.Z=r},31768:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(36865),i=n(47526),o=function(e){var t=e.total_duration,n=void 0===t?(0,i.q9)("total_duration"):t,r=e.active_duration,o=void 0===r?(0,i.q9)("active_duration"):r;return function(){return{total_duration:n,active_duration:o}}},a={required_layers:[r.D,o],type:"web_time_spent"}},30362:function(e,t,n){"use strict";var r=n(36865),i=n(91373),o=n(73470),a=n(92915),u={required_layers:[r.D,i.Z,o.Z,a.Z],type:"web_videoview"};t.Z=u},95941:function(e,t){"use strict";function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function r(e){return function(e){if(Array.isArray(e))return n(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}t.Z=function(e){return function(t){for(var n=arguments.length,i=new Array(n>1?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];var a=function(t){e.apply(void 0,[{event:t,layers:[{click_type:"left"}]}].concat(r(i)))},u=function(t){e.apply(void 0,[{event:t,layers:[{click_type:"right"}]}].concat(r(i)))};return t.addEventListener("click",a),t.addEventListener("contextmenu",u),function(){t.removeEventListener("click",a),t.removeEventListener("contextmenu",u)}}}},98318:function(e,t){"use strict";function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function r(e){return function(e){if(Array.isArray(e))return n(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}t.Z=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.threshold,i=void 0===n?.5:n,o=t.visibility_time,a=void 0===o?1e3:o;var u=new Map,c=new Map,s=function(e,t){e.forEach((function(e){if(u.has(e.target)){if(!e.isIntersecting)return clearTimeout(c.get(e.target)),void c.delete(e.target);if(!(c.has(e.target)||e.intersectionRatio<i)){var n=setTimeout((function(){u.get(e.target).call(null),t.unobserve(e.target),c.delete(e.target)}),a);c.set(e.target,n)}}}))},l=new IntersectionObserver(s,{threshold:[0,i,1]}),f=function(t){for(var n=arguments.length,i=new Array(n>1?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];return u.has(t)||(u.set(t,(function(){return e.apply(void 0,r(i))})),l.observe(t)),function(){u.delete(t),l.unobserve(t),c.has(t)&&(clearTimeout(c.get(t)),c.delete(t))}};return function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return f.apply(void 0,[e].concat(r(n)))}}},3904:function(e,t){"use strict";function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),i.forEach((function(t){r(e,t,n[t])}))}return e}function o(e){return function(e){if(Array.isArray(e))return n(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}t.Z=function(e){var t=["web_internal_link","web_external_link"],n=Date.now(),r=n,a=0,u=Date.now(),c=1,s=!1,l=function(){for(var l=arguments.length,f=new Array(l),d=0;d<l;d++)f[d]=arguments[d];n=Date.now(),r=n,a=0,c=1,s=!1;var p=null,h=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=Date.now(),a={active_duration:i-r,total_duration:i-n};a.total_duration>0&&e.apply(void 0,[{layers:[a,t]}].concat(o(f))),n=Date.now(),r=Date.now()},v=function(){if("hidden"===document.visibilityState){s=!0,h({time_spent_event_type:"page_visibility"}),cancelAnimationFrame(p)}else"visible"===document.visibilityState&&(r=Date.now(),setTimeout((function(){return s=!1}),2e3),y(u,c))},m=function(e){var o=e.detail;if(o.find((function(e){return t.includes(e.type)}))){var a=o[0];r=n;var u=i({},a);delete u.event_ts,delete u.event_uuid,delete u.type,delete u.source,u.time_spent_event_type="link_click",h(u)}},y=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;function n(e){var t=Date.now()-u;if(Math.floor(t/1e3)>=e&&!s){s=!0;h({time_spent_event_type:"heartbeat"}),setTimeout((function(){return s=!1}),1e3),u=Date.now(),e=e<5?e+2:10*Math.pow(2,Math.floor((a-3)/2)),c=e,a++}cancelAnimationFrame(p),p=requestAnimationFrame((function(){return n(e)}))}u=e||Date.now(),n(t)};return y(),document.addEventListener("visibilitychange",v),window.addEventListener("cet-event",m),function(){cancelAnimationFrame(p),document.removeEventListener("visibilitychange",v),window.removeEventListener("cet-event",m)}};return function(){return l.apply(void 0,arguments)}}},36865:function(e,t,n){"use strict";n.d(t,{D:function(){return i}});var r=n(47526),i=function(e){var t=e.context_page_id,n=void 0===t?(0,r.q9)("context_page_id"):t,i=e.context_page_type,o=void 0===i?(0,r.q9)("context_page_type"):i,a=e.destination,u=void 0===a?(0,r.q9)("destination"):a;return function(){return{context_page_id:String(n),context_page_type:o,destination:u}}}},92579:function(e,t,n){"use strict";var r=n(47526);t.Z=function(e){var t=e.data_source_algorithm,n=e.data_source_algorithm_version,i=e.data_source_name,o=void 0===i?"":i;return function(){return{data_source_algorithm:(0,r.fU)((0,r.c$)(t)),data_source_algorithm_version:(0,r.fU)((0,r.c$)(n)),data_source_name:decodeURIComponent(o)}}}},81433:function(e,t,n){"use strict";var r=n(47526);t.Z=function(e){var t=e.item_name,n=void 0===t?(0,r.q9)("item_name"):t,i=e.target_content_id,o=void 0===i?n:i,a=e.target_content_type,u=void 0===a?(0,r.q9)("target_content_type"):a;return function(){return{target_content_id:String(o),target_content_type:u}}}},91373:function(e,t,n){"use strict";var r=n(47526);t.Z=function(e){var t=e.item_name,n=void 0===t?(0,r.q9)("item_name"):t,i=e.item_type,o=void 0===i?(0,r.q9)("item_type"):i,a=e.position_in_subunit,u=e.position_in_unit;return function(){return{item_name:String(n),item_type:o,position_in_subunit:(0,r.v9)(a),position_in_unit:(0,r.v9)(u)}}}},73470:function(e,t,n){"use strict";var r=n(47526);t.Z=function(e){var t=e.subunit_name,n=void 0===t?"":t,i=e.subunit_type,o=void 0===i?"":i;return function(){return{subunit_name:(0,r.EY)(n.toString()),subunit_type:o}}}},92915:function(e,t,n){"use strict";var r=n(47526);t.Z=function(e){var t=e.unit_name,n=void 0===t?(0,r.q9)("unit_name"):t,i=e.unit_type,o=void 0===i?(0,r.q9)("unit_type"):i;return function(){return{unit_type:o,unit_name:(0,r.EY)(n)}}}},47526:function(e,t,n){"use strict";n.d(t,{fU:function(){return S},EY:function(){return w},v9:function(){return O},pE:function(){return C},G_:function(){return x},c$:function(){return E},oR:function(){return A},q9:function(){return j},IJ:function(){return I}});var r=n(94776),i=n.n(r),o=n(78051),a=n(95517);function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function c(e,t,n,r,i,o,a){try{var u=e[o](a),c=u.value}catch(s){return void n(s)}u.done?t(c):Promise.resolve(c).then(r,i)}function s(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function l(e,t,n){return l=s()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&m(i,n.prototype),i},l.apply(null,arguments)}function f(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},d(e)}function p(e,t){return null!=t&&"undefined"!==typeof Symbol&&t[Symbol.hasInstance]?t[Symbol.hasInstance](e):e instanceof t}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){f(e,t,n[t])}))}return e}function v(e,t){return!t||"object"!==g(t)&&"function"!==typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function m(e,t){return m=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},m(e,t)}function y(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(c){u=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(u)throw i}}return o}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return u(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return u(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var g=function(e){return e&&"undefined"!==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};function b(e){var t="function"===typeof Map?new Map:void 0;return b=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return l(e,arguments,d(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),m(r,e)},b(e)}function _(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=d(e);if(t){var i=d(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return v(this,n)}}var w=function(e){return String(e).replace(/[^\w\s-_/|]/g,"").replace(/\s+/g,"_").toLowerCase()},S=function(e){return e.map((function(e){return"undefined"===e?"":e}))},O=function(e){return null===e||isNaN(Number(e))?null:Number(e)},E=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e&&Array.isArray(e)?e:t},A=function(){var e,t=(e=i().mark((function e(t){var n,r,o,a,u,c;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=t.context,r=t.layers,o=void 0===r?[]:r,a={},u=0;case 3:if(!(u<o.length)){e.next=13;break}if("function"!==typeof(c=o[u])){e.next=9;break}return e.next=8,c(n);case 8:c=e.sent;case 9:a=h({},a,c);case 10:u++,e.next=3;break;case 13:return e.abrupt("return",a);case 14:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){c(o,r,i,a,u,"next",e)}function u(e){c(o,r,i,a,u,"throw",e)}a(void 0)}))});return function(e){return t.apply(this,arguments)}}(),x=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.client_id_cookie_name,n=void 0===t?"_amp_pd":t,r=e.expires,i=void 0===r?365:r,u=o.Z.get(n);if(!u){var c=window.location.hostname.split(".").splice(-2,2).join(".");u="amp-".concat((0,a.Z)()),o.Z.set({name:n,value:u,days:i,domain:c})}return u},k=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&m(e,t)}(n,e);var t=_(n);function n(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),(r=t.call(this,e)).name="ClientEventSchemaLayerError",r}return n}(b(Error)),j=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return new k('Missing required field: "'.concat(e,'"'))},I=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.debug,r=void 0!==n&&n,i=Object.entries(e).filter((function(e){var t=y(e,2)[1];return!p(t,k)||(r&&console.warn(t),!1)})),o=i.reduce((function(e,t){var n=y(t,2),r=n[0],i=n[1];return e[r]=i,e}),{});return o},C=function(e){for(var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=3735928559^n,i=1103547991^n,o=0;o<e.length;o++)t=e.charCodeAt(o),r=Math.imul(r^t,2654435761),i=Math.imul(i^t,1597334677);return r=Math.imul(r^r>>>16,2246822507),r^=Math.imul(i^i>>>13,3266489909),i=Math.imul(i^i>>>16,2246822507),4294967296*(2097151&(i^=Math.imul(r^r>>>13,3266489909)))+(r>>>0)}},49215:function(e,t,n){"use strict";n.d(t,{oO:function(){return f}});var r=n(94776),i=n.n(r),o=n(57641);function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function u(e,t,n,r,i,o,a){try{var u=e[o](a),c=u.value}catch(s){return void n(s)}u.done?t(c):Promise.resolve(c).then(r,i)}function c(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){u(o,r,i,a,c,"next",e)}function c(e){u(o,r,i,a,c,"throw",e)}a(void 0)}))}}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(c){u=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(u)throw i}}return o}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var l=function(e){try{"Profiler"in window&&"Scheduler"in window&&(window.location.search.includes("e2e_test")||Math.random()<=e.sample_rate)&&(window.__jsProfiler=new window.Profiler({sampleInterval:e.profiler_init_options.sampleInterval||0,maxBufferSize:e.profiler_init_options.maxBufferSize||1e4}))}catch(t){}},f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.profiler_init_options,n=void 0===t?{}:t,r=e.sample_rate,i=void 0===r?.1:r,o="\n    (".concat(l.toString(),")(").concat(JSON.stringify({profiler_init_options:n,sample_rate:i}),");\n  ");return o.replace(/([;,{}:])\s+|(\s+){2,}/g,"$1$2")},d=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.ignore_pattern,i=void 0===r?"chunks/(?:framework|main|webpack)":r;performance.mark("getLongestRunningFrames_start");var o=[],a=[],u=new RegExp(i),c=t.samples.entries(),l=!0,f=!1,d=void 0;try{for(var p,h=e[Symbol.iterator]();!(l=(p=h.next()).done);l=!0){var v=p.value,m={},y=!0,g=!1,b=void 0;try{for(var _,w=c[Symbol.iterator]();!(y=(_=w.next()).done);y=!0){var S=s(_.value,2),O=S[1];if(O.stackId&&O.timestamp>=v.startTime){if(O.timestamp>v.startTime+v.duration)break;for(var E=t.stacks[O.stackId];"parentId"in E;)m[E.frameId]=(m[E.frameId]||0)+1,E=t.stacks[E.parentId]}}}catch(q){g=!0,b=q}finally{try{y||null==w.return||w.return()}finally{if(g)throw b}}Object.keys(m).length&&a.push([v,m])}}catch(q){f=!0,d=q}finally{try{l||null==h.return||h.return()}finally{if(f)throw d}}a.sort((function(e,t){return t[0].duration-e[0].duration}));var A=function(e,n){if(n[1]<5)return e;if(e[1]>n[1])return e;var r=t.frames[n[0]];if(!("resourceId"in r))return e;var i=t.resources[r.resourceId];return u&&u.test(i)?e:n},x=!0,k=!1,j=void 0;try{for(var I,C=a[Symbol.iterator]();!(x=(I=C.next()).done);x=!0){var P=s(I.value,2),T=P[0],L=P[1],z=s(Object.entries(L).reduce(A,[]),1),M=z[0];if(M){var Z=t.frames[M],D=t.resources[Z.resourceId];o.push([T,Z,D])}}}catch(q){k=!0,j=q}finally{try{x||null==C.return||C.return()}finally{if(k)throw j}}return performance.measure("getLongestRunningFrames","getLongestRunningFrames_start"),o};t.ZP=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.ignore_pattern,r=t.limit,a=void 0===r?10:r;(0,o.Fu)((function(){"__jsProfiler"in window&&window.scheduler.postTask(c(i().mark((function t(){var r,o;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return performance.mark("__jsProfiler_start"),t.next=3,window.__jsProfiler.stop();case 3:r=t.sent,performance.measure("__jsProfiler","__jsProfiler_start"),o=new PerformanceObserver(function(){var t=c(i().mark((function t(u){var c,l,f,p,h,v,m,y,g,b,_;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:o.disconnect(),c=u.getEntries(),l=d(c,r,{ignore_pattern:n}),f=!0,p=!1,h=void 0,t.prev=4,v=l[Symbol.iterator]();case 6:if(f=(m=v.next()).done){t.next=14;break}if(y=s(m.value,3),g=y[0],b=y[1],_=y[2],--a){t.next=10;break}return t.abrupt("break",14);case 10:e({metric_name:"longtask-longest-frame",metric_type:"custom",metric_value:g.duration,metric_metadata_type:"stacktrace",metric_metadata_value:"".concat(_,":").concat(b.line,":").concat(b.column)});case 11:f=!0,t.next=6;break;case 14:t.next=20;break;case 16:t.prev=16,t.t0=t.catch(4),p=!0,h=t.t0;case 20:t.prev=20,t.prev=21,f||null==v.return||v.return();case 23:if(t.prev=23,!p){t.next=26;break}throw h;case 26:return t.finish(23);case 27:return t.finish(20);case 28:case"end":return t.stop()}}),t,null,[[4,16,20,28],[21,,23,27]])})));return function(e){return t.apply(this,arguments)}}()),o.observe({type:"longtask",buffered:!0});case 7:case"end":return t.stop()}}),t)}))),{priority:"background"})}))}},8897:function(e,t,n){"use strict";n.d(t,{Z:function(){return q}});var r=n(94776),i=n.n(r),o=n(36865),a=function(){var e="undefined"!==typeof navigator&&(navigator.connection||navigator.mozConnection||navigator.webkitConnection);return e?e.effectiveType:""},u=function(){return function(){return{connection_type:a()}}};function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function s(e,t,n,r,i,o,a){try{var u=e[o](a),c=u.value}catch(s){return void n(s)}u.done?t(c):Promise.resolve(c).then(r,i)}function l(e){return function(e){if(Array.isArray(e))return c(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var f=function(e,t){var n,r=t.flush,a=void 0!==r&&r,c=t.layers,f=void 0===c?[]:c,d=t.sample_rate,p=void 0===d?.1:d,h=t.type;return n=i().mark((function t(){var n,r,c,s=arguments;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:for(n=s.length,r=new Array(n),c=0;c<n;c++)r[c]=s[c];return t.next=3,e.apply(void 0,[{flush:a,required_layers:[o.D,u],sample_rate:p,type:h}].concat(l(f),l(r)));case 3:case"end":return t.stop()}}),t)})),function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(e){s(o,r,i,a,u,"next",e)}function u(e){s(o,r,i,a,u,"throw",e)}a(void 0)}))}},d=function(){"__trackAbandons"in window&&document.removeEventListener("visibilitychange",window.__trackAbandons)},p=n(57641),h=function(e){(0,p.Yn)((function(t){e({metric_name:"interaction-to-next-paint",metric_type:"custom",metric_value:t.value})}))},v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:250,n="";try{for(;e&&9!==e.nodeType;){var r=e.id?"#".concat(e.id):e.nodeName.toLowerCase()+(e.className&&e.className.length?".".concat(Array.from(e.classList.values()).join(".")):"");if(n.length+r.length>t-1)return n||r;if(n=n?"".concat(r," > ").concat(n):r,e.id)break;e=e.parentNode}}catch(i){}return n};function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function y(e){return function(e){if(Array.isArray(e))return m(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return m(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return m(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var g=function(e){(0,p.mw)((function(t){var n=t.entries;if(n.length)try{var r=y(n);r.sort((function(e,t){return e&&e.value>t.value?-1:1})),r.slice(0,3).forEach((function(t,n){if(t&&t.sources&&t.sources.length){var r=t.sources.reduce((function(e,t){return!!e.node&&(e.previousRect.width*e.previousRect.height>t.previousRect.width*t.previousRect.height?e:t)}));e({metric_name:"largest-layout-shift-node-".concat(n),metric_type:"custom",metric_value:t.value,metric_metadata_type:"css-selector",metric_metadata_value:v(r.node)||""})}}))}catch(i){}}))},b=function(e){(0,p.Fu)((function(){if("PerformanceLongTaskTiming"in window){var t=new PerformanceObserver((function(n){var r=n.getEntries(),i=r.length,o=r.reduce((function(e,t){return e+t.duration-50}),0);i&&(e({metric_name:"cumulative-longtask-count",metric_type:"custom",metric_value:i}),e({metric_name:"cumulative-blocking-time",metric_type:"custom",metric_value:o})),t.disconnect()}));t.observe({type:"longtask",buffered:!0})}}))},_=n(49215),w=function(e){(0,p.mw)((function(){if(performance.memory){var t=performance.memory,n={jsHeapSizeLimit:t.jsHeapSizeLimit,totalJSHeapSize:t.totalJSHeapSize,usedJSHeapSize:t.usedJSHeapSize};e({metric_name:"memory",metric_type:"custom",metric_value:0,metric_metadata_type:"json",metric_metadata_value:JSON.stringify(n)})}}))};function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){S(e,t,n[t])}))}return e}var E=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.layers,r=void 0===n?[]:n,i=t.profiling,o=void 0===i?{}:i,a=t.sample_rate,u=void 0===a?.1:a,c={layers:r,sample_rate:u,type:"web_performance_metric"},s=f(e,O({},c,{flush:!0})),l=f(e,c),p=f(e,O({},c,{sample_rate:1}));d(),h(l),g(s),b(l),(0,_.ZP)(p,o),w(s)},A=function(e){(0,p.mw)((function(t){e({metric_name:"cumulative-layout-shift",metric_type:"web-vital",metric_value:t.value})}))},x=function(e){(0,p.a4)((function(t){e({metric_name:"first-contentful-paint",metric_type:"web-vital",metric_value:t.value})}))},k=function(e){(0,p.Fu)((function(t){var n=t.entries,r=t.value,i=n[n.length-1],o=i.startTime,a=i.target,u={css:v(a),timeStamp:o};e({metric_name:"first-input-delay",metric_type:"web-vital",metric_value:r,metric_metadata_type:"json",metric_metadata_value:JSON.stringify(u)})}))},j=function(e){(0,p.NO)((function(t){var n=t.entries,r=t.value,i=n[n.length-1],o=i.element,a=i.size,u=i.url,c=void 0===u?"":u,s={css:v(o),size:a,url:c};e({metric_name:"largest-contentful-paint",metric_type:"web-vital",metric_value:r,metric_metadata_type:"json",metric_metadata_value:JSON.stringify(s)})}))};function I(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function C(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){I(e,t,n[t])}))}return e}var P=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.layers,r=void 0===n?[]:n,i=t.sample_rate,o=void 0===i?.1:i,a={layers:r,sample_rate:o,type:"web_performance_metric"},u=f(e,a),c=f(e,C({},a,{flush:!0}));A(c),x(u),k(c),j(c)};function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function L(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(c){u=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(u)throw i}}return o}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return T(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return T(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var z=function(e){if("PerformanceNavigationTiming"in window){var t=function(){var t=L(performance.getEntriesByType("navigation"),1)[0];e({timing_duration:t.duration,timing_name:t.name,timing_start_time:t.startTime,timing_type:t.entryType,resource_connect_end:t.connectEnd,resource_connect_start:t.connectStart,resource_decoded_body_size:void 0===t.decodedBodySize?null:t.decodedBodySize,resource_domain_lookup_end:t.domainLookupEnd,resource_domain_lookup_start:t.domainLookupStart,resource_encoded_body_size:void 0===t.encodedBodySize?null:t.encodedBodySize,resource_fetch_start:t.fetchStart,resource_initiator_type:t.initiatorType,resource_next_hop_protocol:void 0===t.nextHopProtocol?null:t.nextHopProtocol,resource_redirect_end:t.redirectEnd,resource_redirect_start:t.redirectStart,resource_request_start:t.requestStart,resource_response_end:t.responseEnd,resource_response_start:t.responseStart,resource_secure_connection_start:void 0===t.secureConnectionStart?null:t.secureConnectionStart,resource_transfer_size:void 0===t.transferSize?null:t.transferSize,resource_worker_start:void 0===t.workerStart?null:t.workerStart,navigation_dom_complete:t.domComplete,navigation_dom_content_loaded_event_end:t.domContentLoadedEventEnd,navigation_dom_content_loaded_event_start:t.domContentLoadedEventStart,navigation_dom_interactive:t.domInteractive,navigation_load_event_end:t.loadEventEnd,navigation_load_event_start:t.loadEventStart,navigation_redirect_count:t.redirectCount,navigation_type:t.type,navigation_unload_event_end:t.unloadEventEnd,navigation_unload_event_start:t.unloadEventStart})};"complete"===document.readyState?t():window.addEventListener("load",(function(){return requestAnimationFrame(t)}))}},M=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.layers,r=void 0===n?[]:n,i=t.sample_rate,o=void 0===i?.1:i,a={layers:r,sample_rate:o,type:"web_performance_navigation_timing"};z(f(e,a))};function Z(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function D(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Z(e,t,n[t])}))}return e}var q=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.layers,r=void 0===n?[]:n,i=t.sample_rate,o=void 0===i?.1:i,a=t.profiling,u=void 0===a?{}:a,c={layers:r,sample_rate:o};P(e,c),M(e,c),E(e,D({},c,{profiling:u}))}},11423:function(e,t,n){"use strict";function r(){return r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(this,arguments)}n.d(t,{qw:function(){return d},ez:function(){return _},VA:function(){return p},BN:function(){return h},QU:function(){return v},P7:function(){return m},JT:function(){return y},_8:function(){return g},Ub:function(){return b}});var i=n(2784);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(){return a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},a.apply(this,arguments)}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){o(e,t,n[t])}))}return e}function c(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function s(e){var t=e.viewBox,n=e.title,o=e.path,a=c(e,["viewBox","title","path"]);return i.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",viewBox:t||"0 0 38 38"},a),i.createElement("title",null,n),i.createElement("path",{d:o}))}function l(e){return i.createElement(s,e)}function f(e){if(!e.contentFill)return i.createElement(s,e);var t=e.viewBox,n=e.title,o=e.path,a=e.contentFill,u=c(e,["viewBox","title","path","contentFill"]);return i.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",viewBox:t||"0 0 38 38"},u),i.createElement("title",null,n),i.createElement("circle",{cx:"50%",cy:"50%",r:"35%",fill:a}),i.createElement("path",{d:o}))}var d=function(e){return l(u({title:"Apple",path:"M35.3 27.9c-.9 2-1.3 2.9-2.5 4.6-1.5 2.4-3.8 5.5-6.6 5.5-2.5 0-3.1-1.6-6.5-1.6S15.6 38 13.1 38c-2.8 0-4.9-2.8-6.5-5.2C2.1 26 1.6 18 4.4 13.7c2-3 5.1-4.8 8-4.8 3 0 4.8 1.6 7.3 1.6 2.4 0 3.8-1.6 7.3-1.6 2.6 0 5.3 1.4 7.3 3.9-6.5 3.5-5.5 12.6 1 15.1zm-11-21.7c1.2-1.6 2.2-3.9 1.9-6.2-2 .1-4.4 1.4-5.8 3.1-1.3 1.5-2.3 3.8-1.9 6 2.2.1 4.5-1.2 5.8-2.9z"},e))},p=function(e){return l(u({title:"Caret Down",path:"M19 29.3L2.6 12.9c-.8-.8-.8-2 0-2.8.8-.8 2-.8 2.8 0L19 23.7l13.6-13.6c.8-.8 2-.8 2.8 0 .8.8.8 2 0 2.8L19 29.3z"},e))},h=function(e){return l(u({title:"Caret Left",path:"M26.5 36c-.5 0-1-.2-1.4-.6L8.7 19 25.1 2.6c.8-.8 2-.8 2.8 0 .8.8.8 2 0 2.8L14.3 19l13.6 13.6c.8.8.8 2 0 2.8-.4.4-.9.6-1.4.6z"},e))},v=function(e){return l(u({title:"Caret Right",path:"M11.5 36c-.5 0-1-.2-1.4-.6-.8-.8-.8-2 0-2.8L23.7 19 10.1 5.4c-.8-.8-.8-2 0-2.8.8-.8 2-.8 2.8 0L29.3 19 12.9 35.4c-.4.4-.9.6-1.4.6z"},e))},m=function(e){return f(u({title:"Circle Exclamation",path:"M19 0C8.5 0 0 8.5 0 19s8.5 19 19 19 19-8.5 19-19S29.5 0 19 0zm-2 7h4v14h-4V7zm2 24c-1.7 0-3-1.3-3-3s1.3-3 3-3 3 1.3 3 3-1.3 3-3 3z"},e))},y=function(e){return l(u({title:"Facebook",path:"M38,19.12A19,19,0,1,0,16,38V24.64H11.21V19.12H16V14.9c0-4.79,2.84-7.43,7.18-7.43a29.21,29.21,0,0,1,4.25.37v4.7H25.07a2.76,2.76,0,0,0-3.1,3v3.59h5.27l-.84,5.52H22V38A19.08,19.08,0,0,0,38,19.12Z"},e))},g=function(e){var t=a({},e);return i.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 48 48",title:"Google"},t),i.createElement("path",{d:"m43.611 20.083h-1.611v-.083h-18v8h11.303c-1.649 4.657-6.08 8-11.303 8-6.627 0-12-5.373-12-12s5.373-12 12-12c3.059 0 5.842 1.154 7.961 3.039l5.657-5.657c-3.572-3.329-8.35-5.382-13.618-5.382-11.045 0-20 8.955-20 20s8.955 20 20 20 20-8.955 20-20c0-1.341-.138-2.65-.389-3.917z",fill:"#ffc107"}),i.createElement("path",{d:"m6.306 14.691 6.571 4.819c1.778-4.402 6.084-7.51 11.123-7.51 3.059 0 5.842 1.154 7.961 3.039l5.657-5.657c-3.572-3.329-8.35-5.382-13.618-5.382-7.682 0-14.344 4.337-17.694 10.691z",fill:"#ff3d00"}),i.createElement("path",{d:"m24 44c5.166 0 9.86-1.977 13.409-5.192l-6.19-5.238c-2.008 1.521-4.504 2.43-7.219 2.43-5.202 0-9.619-3.317-11.283-7.946l-6.522 5.025c3.31 6.477 10.032 10.921 17.805 10.921z",fill:"#4caf50"}),i.createElement("path",{d:"m43.611 20.083h-1.611v-.083h-18v8h11.303c-.792 2.237-2.231 4.166-4.087 5.571.001-.001.002-.001.003-.002l6.19 5.238c-.438.398 6.591-4.807 6.591-14.807 0-1.341-.138-2.65-.389-3.917z",fill:"#1976d2"}))},b=function(e){var t=e.height,n=e.width,o=e.circleClassName,a=e.birdClassName,u=c(e,["height","width","circleClassName","birdClassName"]);return i.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",heght:t,width:n,viewBox:"0 0 38 38"},u),i.createElement("title",null,"Twitter"),i.createElement("circle",{cx:"19",cy:"19",r:"19",className:o}),i.createElement("path",{d:"M15.52,29a13,13,0,0,0,13-13c0-.2,0-.4,0-.59A9.43,9.43,0,0,0,30.84,13a9.37,9.37,0,0,1-2.63.73,4.65,4.65,0,0,0,2-2.54,9.18,9.18,0,0,1-2.91,1.11,4.58,4.58,0,0,0-7.8,4.18,13,13,0,0,1-9.44-4.79,4.58,4.58,0,0,0,1.43,6.11,4.47,4.47,0,0,1-2.08-.57v.06A4.58,4.58,0,0,0,13.1,21.8a4.47,4.47,0,0,1-1.21.16,4.11,4.11,0,0,1-.86-.08,4.56,4.56,0,0,0,4.27,3.18,9.21,9.21,0,0,1-5.69,2A8,8,0,0,1,8.52,27a12.71,12.71,0,0,0,7,2.07",className:a,fill:"#fff"}))},_=function(e){var t=e.color,n=void 0===t?"white":t,o=c(e,["color"]);return i.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 32 32",fill:"none"},o),i.createElement("path",{d:"M26.9454 13.3049C27.4426 8.04552 24.4593 5.62309 21.4761 6.05058C18.9929 6.40641 16.8542 8.58863 16.207 9.31029C16.101 9.42849 15.9125 9.42849 15.8065 9.31029C15.1593 8.58864 13.0206 6.40642 10.5374 6.05058C7.55417 5.62308 4.50296 7.89378 5.06811 13.3049C5.6215 18.6034 10.3351 23.7848 15.6659 25.8711C15.8853 25.957 16.1282 25.9571 16.3475 25.871C21.681 23.7787 26.4585 18.4549 26.9454 13.3049Z",stroke:n,strokeWidth:"2",strokeLinejoin:"round"}))}},97729:function(e,t,n){e.exports=n(75913)},57641:function(e,t,n){"use strict";n.d(t,{mw:function(){return j},a4:function(){return x},Fu:function(){return Z},Yn:function(){return V},NO:function(){return X}});var r,i,o,a,u,c=-1,s=function(e){addEventListener("pageshow",(function(t){t.persisted&&(c=t.timeStamp,e(t))}),!0)},l=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},f=function(){var e=l();return e&&e.activationStart||0},d=function(e,t){var n=l(),r="navigate";return c>=0?r="back-forward-cache":n&&(document.prerendering||f()>0?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},p=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},h=function(e,t,n,r){var i,o;return function(a){t.value>=0&&(a||r)&&((o=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=o,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,n),e(t))}},v=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},m=function(e){var t=function(t){"pagehide"!==t.type&&"hidden"!==document.visibilityState||e(t)};addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0)},y=function(e){var t=!1;return function(n){t||(e(n),t=!0)}},g=-1,b=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},_=function(e){"hidden"===document.visibilityState&&g>-1&&(g="visibilitychange"===e.type?e.timeStamp:0,S())},w=function(){addEventListener("visibilitychange",_,!0),addEventListener("prerenderingchange",_,!0)},S=function(){removeEventListener("visibilitychange",_,!0),removeEventListener("prerenderingchange",_,!0)},O=function(){return g<0&&(g=b(),w(),s((function(){setTimeout((function(){g=b(),w()}),0)}))),{get firstHiddenTime(){return g}}},E=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},A=[1800,3e3],x=function(e,t){t=t||{},E((function(){var n,r=O(),i=d("FCP"),o=p("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(o.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-f(),0),i.entries.push(e),n(!0)))}))}));o&&(n=h(e,i,A,t.reportAllChanges),s((function(r){i=d("FCP"),n=h(e,i,A,t.reportAllChanges),v((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))},k=[.1,.25],j=function(e,t){t=t||{},x(y((function(){var n,r=d("CLS",0),i=0,o=[],a=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=o[0],n=o[o.length-1];i&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,o.push(e)):(i=e.value,o=[e])}})),i>r.value&&(r.value=i,r.entries=o,n())},u=p("layout-shift",a);u&&(n=h(e,r,k,t.reportAllChanges),m((function(){a(u.takeRecords()),n(!0)})),s((function(){i=0,r=d("CLS",0),n=h(e,r,k,t.reportAllChanges),v((function(){return n()}))})),setTimeout(n,0))})))},I={passive:!0,capture:!0},C=new Date,P=function(e,t){r||(r=t,i=e,o=new Date,z(removeEventListener),T())},T=function(){if(i>=0&&i<o-C){var e={entryType:"first-input",name:r.type,target:r.target,cancelable:r.cancelable,startTime:r.timeStamp,processingStart:r.timeStamp+i};a.forEach((function(t){t(e)})),a=[]}},L=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var n=function(){P(e,t),i()},r=function(){i()},i=function(){removeEventListener("pointerup",n,I),removeEventListener("pointercancel",r,I)};addEventListener("pointerup",n,I),addEventListener("pointercancel",r,I)}(t,e):P(t,e)}},z=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(t){return e(t,L,I)}))},M=[100,300],Z=function(e,t){t=t||{},E((function(){var n,o=O(),u=d("FID"),c=function(e){e.startTime<o.firstHiddenTime&&(u.value=e.processingStart-e.startTime,u.entries.push(e),n(!0))},l=function(e){e.forEach(c)},f=p("first-input",l);n=h(e,u,M,t.reportAllChanges),f&&m(y((function(){l(f.takeRecords()),f.disconnect()}))),f&&s((function(){var o;u=d("FID"),n=h(e,u,M,t.reportAllChanges),a=[],i=-1,r=null,z(addEventListener),o=c,a.push(o),T()}))}))},D=0,q=1/0,B=0,F=function(e){e.forEach((function(e){e.interactionId&&(q=Math.min(q,e.interactionId),B=Math.max(B,e.interactionId),D=B?(B-q)/7+1:0)}))},R=function(){return u?D:performance.interactionCount||0},U=function(){"interactionCount"in performance||u||(u=p("event",F,{type:"event",buffered:!0,durationThreshold:0}))},H=[200,500],N=0,$=function(){return R()-N},K=[],W={},J=function(e){var t=K[K.length-1],n=W[e.interactionId];if(n||K.length<10||e.duration>t.latency){if(n)n.entries.push(e),n.latency=Math.max(n.latency,e.duration);else{var r={id:e.interactionId,latency:e.duration,entries:[e]};W[r.id]=r,K.push(r)}K.sort((function(e,t){return t.latency-e.latency})),K.splice(10).forEach((function(e){delete W[e.id]}))}},V=function(e,t){t=t||{},E((function(){U();var n,r=d("INP"),i=function(e){e.forEach((function(e){e.interactionId&&J(e),"first-input"===e.entryType&&!K.some((function(t){return t.entries.some((function(t){return e.duration===t.duration&&e.startTime===t.startTime}))}))&&J(e)}));var t,i=(t=Math.min(K.length-1,Math.floor($()/50)),K[t]);i&&i.latency!==r.value&&(r.value=i.latency,r.entries=i.entries,n())},o=p("event",i,{durationThreshold:t.durationThreshold||40});n=h(e,r,H,t.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),m((function(){i(o.takeRecords()),r.value<0&&$()>0&&(r.value=0,r.entries=[]),n(!0)})),s((function(){K=[],N=R(),r=d("INP"),n=h(e,r,H,t.reportAllChanges)})))}))},G=[2500,4e3],Y={},X=function(e,t){t=t||{},E((function(){var n,r=O(),i=d("LCP"),o=function(e){var t=e[e.length-1];t&&t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-f(),0),i.entries=[t],n())},a=p("largest-contentful-paint",o);if(a){n=h(e,i,G,t.reportAllChanges);var u=y((function(){Y[i.id]||(o(a.takeRecords()),a.disconnect(),Y[i.id]=!0,n(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,u,!0)})),m(u),s((function(r){i=d("LCP"),n=h(e,i,G,t.reportAllChanges),v((function(){i.value=performance.now()-r.timeStamp,Y[i.id]=!0,n(!0)}))}))}}))}}}]);
//# sourceMappingURL=167-e3a97434bfca6614.js.map