"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[195],{87195:function(e,n,t){t.d(n,{BE:function(){return m},mS:function(){return P},TZ:function(){return M}});var r=t(94776),o=t.n(r),a=t(42235),i=function(e,n){var t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];t&&(console.group("".concat(e," tracker event.")),console.log({data:n}),console.groupEnd())},c=6768151,u="65dfbb69-7cb1-4369-a536-cfabde03b7d4",s="3c873bb9-17eb-463a-9b8d-bdbbfdaf4524",f="5d79bce7-5d2b-427e-a6c4-b89b6c7bf048",p=t(75951),d=t(29875);function v(e,n,t,r,o,a,i){try{var c=e[a](i),u=c.value}catch(s){return void t(s)}c.done?n(u):Promise.resolve(u).then(r,o)}function l(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function w(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function g(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},r=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),r.forEach((function(n){w(e,n,t[n])}))}return e}var b={c1:2,c2:c,options:{enableFirstPartyCookie:!0}},m=function(){function e(n){var t=n.cluster;!function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}(this,e),"prod"!==t&&(window.COMSCORE={beacon:function(e){return i("Comscore",e,!0)}}),this._trackData()}var n,t,r;return n=e,t=[{key:"_trackData",value:function(){return(e=o().mark((function e(){var n,t,r,i,u;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=!1,window.COMSCORE){e.next=11;break}return e.prev=2,e.next=5,(0,a.v)("https://sb.scorecardresearch.com/cs/".concat(c,"/beacon.js"));case 5:e.next=11;break;case 7:e.prev=7,e.t0=e.catch(2),n=!0,console.error("Error loading comscore");case 11:if(!n){e.next=13;break}return e.abrupt("return");case 13:if(t={},r=p.MS.needsConsent(),i=d.Qk.get(p.TD),!r||!i){e.next=23;break}return e.next=19,p.jQ.fetchTrackingConsent();case 19:u=e.sent,t.cs_ucfr=parseFloat(Number(u)),e.next=27;break;case 23:return e.next=25,p.jQ.fetchCCPAOptOut();case 25:e.sent?t.cs_ucfr=0:r&&(t.cs_ucfr=1);case 27:window.COMSCORE&&window.COMSCORE.beacon(g({},b,t));case 28:case"end":return e.stop()}}),e,null,[[2,7]])})),function(){var n=this,t=arguments;return new Promise((function(r,o){var a=e.apply(n,t);function i(e){v(a,r,o,i,c,"next",e)}function c(e){v(a,r,o,i,c,"throw",e)}i(void 0)}))})();var e}}],t&&l(n.prototype,t),r&&l(n,r),e}();var h=t(3379);function k(e,n,t,r,o,a,i){try{var c=e[a](i),u=c.value}catch(s){return void t(s)}c.done?n(u):Promise.resolve(u).then(r,o)}function y(e){return function(){var n=this,t=arguments;return new Promise((function(r,o){var a=e.apply(n,t);function i(e){k(a,r,o,i,c,"next",e)}function c(e){k(a,r,o,i,c,"throw",e)}i(void 0)}))}}function x(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var P=function(){function e(n){var t=n.trackingId,r=n.cluster;!function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}(this,e),this.trackingId=t,this.cluster=r}var n,t,r;return n=e,t=[{key:"_loadFacebookScript",value:function(){var e=this;return y(o().mark((function n(){var t,r;return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if("prod"===e.cluster){n.next=4;break}return window.fbq=function(e,n,t){return i("Facebook",{eventName:e,eventType:n,eventData:t})},window.fbq.loaded=!0,n.abrupt("return",!0);case 4:return n.prev=4,n.next=7,function(){var e=y(o().mark((function e(n,t,r,i,c){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!n.fbq){e.next=2;break}return e.abrupt("return");case 2:return c=n.fbq=function(){c.callMethod?c.callMethod.apply(c,arguments):c.queue.push(arguments)},n._fbq||(n._fbq=c),c.push=c,c.loaded=!0,c.version="2.0",c.queue=[],e.next=10,(0,a.v)(i);case 10:case"end":return e.stop()}}),e)})));return function(n,t,r,o,a){return e.apply(this,arguments)}}()(window,document,"script","https://connect.facebook.net/en_US/fbevents.js");case 7:return t=h.Z.get("permutive-id")||!1,r={},t&&(r.external_id=t),n.next=12,p.jQ.fetchCCPAOptOut();case 12:return n.sent?window.fbq("dataProcessingOptions",["LDU"],1,1e3):window.fbq("dataProcessingOptions",[]),window.fbq("init",e.trackingId,r),n.abrupt("return",!0);case 18:return n.prev=18,n.t0=n.catch(4),n.abrupt("return",!1);case 21:case"end":return n.stop()}}),n,null,[[4,18]])})))()}},{key:"_getFacebookTracker",value:function(){var e=this;return y(o().mark((function n(){var t;return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!window.fbq||!window.fbq.loaded){n.next=2;break}return n.abrupt("return",window.fbq);case 2:if(p.MS.needsConsent()){n.next=9;break}return n.next=7,e._loadFacebookScript();case 7:n.next=20;break;case 9:return n.prev=9,n.next=12,p.jQ.hasConsented("tracking");case 12:if(!(t=n.sent)){n.next=16;break}return n.next=16,e._loadFacebookScript();case 16:n.next=20;break;case 18:n.prev=18,n.t0=n.catch(9);case 20:return window.fbq||(window.fbq=function(){},"undefined"===typeof t||t||(window.fbq.loaded=!0)),n.abrupt("return",window.fbq);case 22:case"end":return n.stop()}}),n,null,[[9,18]])})))()}},{key:"trackEvent",value:function(e){var n=e.eventType,t=e.eventData,r=e.options,a=this;return y(o().mark((function e(){var i;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,a._getFacebookTracker();case 2:i=e.sent,r&&r.custom?i("trackCustom",n,t||{}):i("track",n,t||{});case 4:case"end":return e.stop()}}),e)})))()}}],t&&x(n.prototype,t),r&&x(n,r),e}();var _=t(84952),C=t(9845);function S(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function O(e,n,t,r,o,a,i){try{var c=e[a](i),u=c.value}catch(s){return void t(s)}c.done?n(u):Promise.resolve(u).then(r,o)}function T(e){return function(){var n=this,t=arguments;return new Promise((function(r,o){var a=e.apply(n,t);function i(e){O(a,r,o,i,c,"next",e)}function c(e){O(a,r,o,i,c,"throw",e)}i(void 0)}))}}function j(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function q(e){return function(e){if(Array.isArray(e))return S(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,n){if(!e)return;if("string"===typeof e)return S(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return S(e,n)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var E=function(e){switch("undefined"===typeof e?"undefined":function(e){return e&&"undefined"!==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}(e)){case"string":var n=document.createElement("textarea");return n.innerHTML=e,n.value;case"object":var t=!0,r=Array.isArray(e)?[]:{};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t=!1,r[o]=E(e[o]));return t?void 0:r}return e},I=function(e){return["uk","ie","gb"].includes(e)},A=["addon","identify","track","trigger","query","segment","segments","ready","on","once","user","consent"],M=function(){function e(n){var t=n.cluster,r=n.projectId,o=n.apiKey;!function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}(this,e),this.projectId=r,this.apiKey=o,this.cluster=t,this._loadingPromise}var n,t,r;return n=e,t=[{key:"_loadPermutiveScript",value:function(){var e=this;return T(o().mark((function n(){return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,function(){var e=T(o().mark((function e(n,t,r,i,c){var u,s,f;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t)for(t=t||{},window.permutive=t,t.q=[],t.config=c||{},t.config.projectId=r,t.config.apiKey=i,t.config.environment=t.config.environment||"production",u=q(A),s=0;s<u.length;s++)f=u[s],t[f]=function(e){return function(){var n=Array.prototype.slice.call(arguments,0);t.q.push({functionName:e,arguments:n})}}(f);return e.next=3,(0,a.v)("https://cdn.permutive.com/".concat(r,"-web.js"));case 3:case"end":return e.stop()}}),e)})));return function(n,t,r,o,a){return e.apply(this,arguments)}}()(document,window.permutive,e.projectId,e.apiKey,{});case 3:return n.abrupt("return",!0);case 6:return n.prev=6,n.t0=n.catch(0),console.error(n.t0),n.abrupt("return",!1);case 10:case"end":return n.stop()}}),n,null,[[0,6]])})))()}},{key:"_loadIDNMLPermutiveScript",value:function(){return T(o().mark((function e(){var n,t,r;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=u,t=s,r=f,e.prev=1,e.next=4,function(){var e=T(o().mark((function e(n,t,r,a){var i,c,u,s;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!n)for(n=n||{},window.permutive=n,n.q=[],i=function(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(function(e){return(e^(window.crypto||window.msCrypto).getRandomValues(new Uint8Array(1))[0]&15>>e/4).toString(16)}))},n.config=a||{},n.config.apiKey=t,n.config.workspaceId=r,n.config.environment=n.config.environment||"production",(window.crypto||window.msCrypto)&&(n.config.viewId=i()),c=["addon","identify","track","trigger","query","segment","segments","ready","on","once","user","consent"],u=0;u<c.length;u++)n[s=c[u]]=function(e){return function(){var t=Array.prototype.slice.call(arguments,0);n.q.push({functionName:e,arguments:t})}}(s);case 1:case"end":return e.stop()}}),e)})));return function(n,t,r,o){return e.apply(this,arguments)}}()(window.permutive,n,t,{consentRequired:!0});case 4:return window.googletag=window.googletag||{},window.googletag.cmd=window.googletag.cmd||[],window.googletag.cmd.push((function(){if(0===window.googletag.pubads().getTargeting("permutive").length){var e=window.localStorage.getItem("_pdfps");window.googletag.pubads().setTargeting("permutive",e?JSON.parse(e):[]);var n=window.localStorage.getItem("permutive-id");n&&(window.googletag.pubads().setTargeting("puid",n),window.googletag.pubads().setTargeting("ptime",Date.now().toString())),window.permutive.config.viewId&&window.googletag.pubads().setTargeting("prmtvvid",window.permutive.config.viewId),window.permutive.config.workspaceId&&window.googletag.pubads().setTargeting("prmtvwid",window.permutive.config.workspaceId)}})),e.next=7,(0,a.v)("https://".concat(r,".edge.permutive.app/").concat(t,"-web.js"));case 7:e.next=13;break;case 9:return e.prev=9,e.t0=e.catch(1),console.error(e.t0),e.abrupt("return",!1);case 13:case"end":return e.stop()}}),e,null,[[1,9]])})))()}},{key:"_getPermutiveTracker",value:function(){var e=this;return T(o().mark((function n(){return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!e._loadingPromise){n.next=2;break}return n.abrupt("return",e._loadingPromise);case 2:return e._loadingPromise=new Promise(function(){var n=T(o().mark((function n(t){var r,a,i;return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!window.permutive||!window.permutive.config){n.next=3;break}return t(window.permutive),n.abrupt("return");case 3:return r=p.MS.needsConsent(),n.next=6,p.jQ.hasConsented("permutive");case 6:if(a=n.sent,i=(0,_.pP)().toLowerCase(),r){n.next=13;break}return n.next=11,e._loadPermutiveScript();case 11:n.next=21;break;case 13:if(!I(i)){n.next=18;break}return n.next=16,e._loadIDNMLPermutiveScript();case 16:n.next=21;break;case 18:if(I(i)||!a){n.next=21;break}return n.next=21,e._loadPermutiveScript();case 21:I(i)&&(a?permutive.consent({opt_in:!0,token:"CONSENT_CAPTURED"}):permutive.consent({opt_in:!1})),window.permutive||(window.permutive=e._mockPermutiveTracker(),delete window.permutive.config,"undefined"===typeof a||a||(window.permutive.config={})),t(window.permutive);case 24:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()),n.abrupt("return",e._loadingPromise);case 4:case"end":return n.stop()}}),n)})))()}},{key:"logPermutiveEvent",value:function(e,n){return T(o().mark((function t(){return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,p.jQ.hasConsented("permutive");case 2:t.sent&&(console.group("Permutive Tracking Event"),console.log({eventName:e}),console.log({eventData:n}),console.groupEnd());case 4:case"end":return t.stop()}}),t)})))()}},{key:"_mockPermutiveTracker",value:function(){var e={},n=!0,t=!1,r=void 0;try{for(var o,a=A[Symbol.iterator]();!(n=(o=a.next()).done);n=!0)e[o.value]="prod"===this.cluster?function(){}:this.logPermutiveEvent}catch(i){t=!0,r=i}finally{try{n||null==a.return||a.return()}finally{if(t)throw r}}return e.segments=[],e.config={},e}},{key:"identify",value:function(){var e=this;return T(o().mark((function n(){var t,r,a,i;return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,e._getPermutiveTracker();case 2:t=n.sent,r=(0,C.TQ)(),a=h.Z.get("hem"),i=[{id:r,tag:"buzzfeedUserId",priority:1}],a&&(a=a.replace(/"/g,""),i.push({id:atob(a),tag:"email_sha256",priority:2})),t.identify(i);case 8:case"end":return n.stop()}}),n)})))()}},{key:"trackEvent",value:function(e,n){var t=this;return T(o().mark((function r(){return o().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,t._getPermutiveTracker();case 2:r.sent.track(e,E(n));case 4:case"end":return r.stop()}}),r)})))()}},{key:"trackPageView",value:function(e){var n=this;return T(o().mark((function t(){return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,n._getPermutiveTracker();case 2:t.sent.addon("web",E(e));case 4:case"end":return t.stop()}}),t)})))()}},{key:"trackAffiliateClick",value:function(e){this.trackEvent("AffiliateLinkClick",e)}}],t&&j(n.prototype,t),r&&j(n,r),e}()}}]);
//# sourceMappingURL=195-cf6b2abe773a422c.js.map