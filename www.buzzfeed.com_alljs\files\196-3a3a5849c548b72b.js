"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[196],{97196:function(t,n,e){e.d(n,{cB:function(){return f},Jb:function(){return C},JG:function(){return H},ac:function(){return r.a},uV:function(){return L},kJ:function(){return A},sv:function(){return x},EF:function(){return i.E},Si:function(){return P}});var r=e(39786),o=(e(91254),e(2784)),i=e(60565),a=e(3379),u=e(92523),c=e(20238);function f(){var t=(0,i.E)().trackAddressability;(0,o.useEffect)((function(){var n=a.Z.get("hem"),e=a.Z.get("bf2-b_info"),r=[];((0,c.dn)(window.location.search).email_hash||u.Z.sessionGet("newsletterAddressable"))&&(u.Z.sessionSet({key:"newsletterAddressable",value:"true"}),r.push("newsletter")),e&&r.push("auth"),t({is_addressable:!!n,addressable_source:r,addressable_partner:[]})}),[])}e(15971);var l,s=e(99377),d=e(48243),p=e(87195),v=e(30353);function y(t){(l||(l=new p.mS({trackingId:v.r,cluster:v.ov})),l).trackEvent({eventType:"PageView",data:t})}var m,b=e(94776),h=e.n(b);function g(t,n,e,r,o,i,a){try{var u=t[i](a),c=u.value}catch(f){return void e(f)}u.done?n(c):Promise.resolve(c).then(r,o)}function w(t){return function(){var n=this,e=arguments;return new Promise((function(r,o){var i=t.apply(n,e);function a(t){g(i,r,o,a,u,"next",t)}function u(t){g(i,r,o,a,u,"throw",t)}a(void 0)}))}}function k(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}var E=function(t){var n=function(t){var n=v.zV[t]||v.zV.default,e=n.api_key;return{projectId:n.project_id,apiKey:e}}(t);return m||(m=new p.TZ(function(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{},r=Object.keys(e);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(e).filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})))),r.forEach((function(n){k(t,n,e[n])}))}return t}({cluster:v.ov},n)),m.identify()),m};function _(){return(_=w(h().mark((function t(n){var e,r,o,i,a,u;return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=n.edition,r=n.section,o=n.type,i={publisher:"buzzfeed",platform:"web",edition:e,type:o},r&&(i.section=r),a=E(e),u={page:{meta:i}},a.trackPageView(u);case 6:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function A(){var t,n,e,r=(0,i.E)(),a=r.pageInfo,u=r.trackPageview,c=(0,o.useContext)(d.z1).path,f=a.page_edition,l=null===(t=null===c||void 0===c?void 0:c.split("/"))||void 0===t?void 0:t.filter((function(t){return""!==t}));1===(null===a||void 0===a?void 0:a.context_page_id)?(n="homepage",e="homepage"):(e=l[0],l.length>1&&(n=l[1])),(0,o.useEffect)((function(){u({}),y({section:n,Edition:f,SocialReferral:(0,s.an)()}),function(t){_.apply(this,arguments)}({edition:f,section:n,type:e})}),[c])}function S(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function O(t){return function(t){if(Array.isArray(t))return S(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(!t)return;if("string"===typeof t)return S(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return S(t,n)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function x(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];var r=(0,i.E)().trackPerformance;(0,o.useEffect)((function(){r.apply(void 0,O(n))}),[])}function C(){(0,o.useEffect)((function(){new p.BE({cluster:v.ov})}),[])}function j(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function I(t){return function(t){if(Array.isArray(t))return j(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(!t)return;if("string"===typeof t)return j(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return j(t,n)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var P=function(t){var n=t.trackingData,e=void 0===n?{}:n,r=t.condition,a=void 0===r||r,u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],c=(0,i.E)().trackClientImpressionHandler,f=(0,o.useState)(null),l=f[0],s=f[1];return(0,o.useEffect)((function(){var t=null;return l&&a&&(t=c(l,e)),function(){t&&t()}}),[l].concat(I(u))),{setObservable:s}};function L(){var t=(0,o.useContext)(d.Ui),n=t.context_page_id,e=t.context_page_type,r=t.destination,i=t.page_edition;return(0,o.useEffect)((function(){var t={context_page_id:n,context_page_type:e,destination:r,page_edition:i};window.clientEventTracking={getPageContextLayer:function(){return function(){return t}},env:v.ov}}),[n,e,r,i]),function(){return window.clientEventTracking}}e(92893);function T(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function z(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{},r=Object.keys(e);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(e).filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})))),r.forEach((function(n){T(t,n,e[n])}))}return t}function H(t){var n=(0,o.useRef)(null),e=(0,i.E)(),r=e.trackClientInternalLinkHandler,a=e.trackClientExternalLinkHandler;return(0,o.useEffect)((function(){var e,o=n.current;if(o)return o.querySelectorAll("a[href]").forEach((function(n,o){var i=!!n.href&&!n.href.toLowerCase().match(/.*buzzfeed\.(com|io)|(^\/.*$)/),u={item_type:"text",item_name:n.text,position_in_unit:o};e=i?a(n,z({},t,u,{target_content_url:n.href})):r(n,z({},t,u,{target_content_type:"url",target_content_id:n.href}))})),e}),[n.current,t]),n}},15971:function(t,n,e){var r=e(25616),o=e(30353),i=e(60565),a=(0,r.H)({env:o.ov,source:"buzz_web"});function u(t){var n={trackPrebidEvents:function(t){!function(t){a({type:"instrumentation"},{target:"ads",value:t.eventName,tags:t.tags||{}})}(t)}};Object.keys(n).forEach((function(e){return t[e]=n[e]}))}n.Z=function(){var t={};return function(t,n){var e=n.trackClientInternalLinkHandler,r=n.trackClientExternalLinkHandler,o=n.trackClientContentAction,i=n.trackClientImpressionHandler,a=n.trackContentAction,u=n.trackInternalLink,c=n.trackExternalLink,f={trackBfpClientContentAction:function(t){var n=t.data;a(n)},trackBfpClientLinkClick:function(t){var n=t.data;n.target_content_url?c(n):u(n)},bindBfpClientContentAction:function(t){var n=t.element,e=t.data;n&&e&&o(n,e)},bindBfpProductTracking:function(t){var n=t.element,o=t.data,a=t.isWithImpression;n&&o&&((void 0===a||a)&&i(n,o),o.target_content_url?r(n,o):e(n,o))}};Object.keys(f).forEach((function(n){return t[n]=f[n]}))}(t,(0,i.E)()),u(t),t}},92893:function(t,n,e){e.d(n,{L:function(){return i}});var r=e(2784),o=e(60565);function i(t){var n=(0,o.E)().trackTimeSpent;(0,r.useEffect)((function(){return n(t)}),[])}},91254:function(t,n,e){var r=e(2784),o=e(74967),i=e(48243);function a(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function u(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,o,i=[],a=!0,u=!1;try{for(e=e.call(t);!(a=(r=e.next()).done)&&(i.push(r.value),!n||i.length!==n);a=!0);}catch(c){u=!0,o=c}finally{try{a||null==e.return||e.return()}finally{if(u)throw o}}return i}}(t,n)||function(t,n){if(!t)return;if("string"===typeof t)return a(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return a(t,n)}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}n.Z=function(){var t,n=(0,r.useContext)(i.WN).experiments,e=(0,r.useRef)(),a=(0,r.useRef)({});e.current||(t=u(function(){var t;return[new Promise((function(n){return t=n})),t]}(),2),a.current.loaded=t[0],a.current.resolve=t[1]);e.current=!0;var c=(0,r.useRef)({experiments:a.current.loaded,isOn:function(){var t=arguments;return a.current.loaded.then((function(n){return o.F7.apply(void 0,[n].concat(Array.prototype.slice.call(t)))}))},getExperimentVariant:function(){var t=arguments;return a.current.loaded.then((function(n){return o.ts.apply(void 0,[n].concat(Array.prototype.slice.call(t)))}))}});return(0,r.useEffect)((function(){n&&n.loaded&&!n.stale&&a.current.resolve(n)}),[n]),c.current}},39786:function(t,n,e){e.d(n,{a:function(){return i}});var r=e(76635),o=e(2784),i=function(){var t=(0,o.useState)(""),n=t[0],e=t[1];return(0,o.useEffect)((function(){var t=!0,n=(0,r.mapValues)({xs:"(max-width:40rem)",sm:"(min-width:40rem) and (max-width:52rem)",md:"(min-width:52rem) and (max-width:64rem)",lg:"(min-width:64rem)"},(function(t){return window.matchMedia(t)})),o=function(){if(t){var o=(0,r.findKey)(n,(function(t){return t.matches}));e(o)}};return(0,r.each)(n,(function(t){return t.addEventListener("change",o)})),o(),function(){t=!1,(0,r.each)(n,(function(t){return t.removeEventListener("change",o)}))}}),[]),{breakpoint:n,isMobile:"xs"===n||"sm"===n,isTablet:"sm"===n,isDesktop:"md"===n||"lg"===n,isLargeScreen:"lg"===n}}}}]);
//# sourceMappingURL=196-3a3a5849c548b72b.js.map