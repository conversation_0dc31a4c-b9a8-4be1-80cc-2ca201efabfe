(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[297],{56828:function(e){e.exports=function(e,t,n){var r=null,o=null,i=function(){r&&(clearTimeout(r),o=null,r=null)},a=function(){if(!t)return e.apply(this,arguments);var a=this,c=arguments,s=n&&!r;return i(),o=function(){e.apply(a,c)},r=setTimeout((function(){if(r=null,!s){var e=o;return o=null,e()}}),t),s?o():void 0};return a.cancel=i,a.flush=function(){var e=o;i(),e&&e()},a}},47396:function(e,t,n){"use strict";n.d(t,{F:function(){return o}});var r=n(52322),o=function(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("link",{rel:"dns-prefetch",href:"https://c.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://c.amazon-adsystem.com",crossOrigin:""}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"https://c.aps.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://c.aps.amazon-adsystem.com",crossOrigin:""}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"https://aax.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://aax.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"https://config.aps.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://config.aps.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"https://client.aps.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://client.aps.amazon-adsystem.com"})]})}},49593:function(e,t,n){"use strict";var r=n(76635);function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],a=!0,c=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(s){c=!0,o=s}finally{try{a||null==n.return||n.return()}finally{if(c)throw o}}return i}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return o(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}t.Z={add:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return(0,r.unionBy)(e,n,JSON.stringify)},exclude:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return(0,r.differenceBy)(e,n,JSON.stringify)},_filterProgrammatic:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return!0};return e=e.map(JSON.stringify),t.filter((function(t){return-1===e.indexOf(JSON.stringify(t))||n(t)}))},filterProgrammatic:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.min,o=void 0===r?null:r,i=n.max,a=void 0===i?null:i;return this._filterProgrammatic(e,t,(function(e){return!!(o&&e[0]>=o[0]||a&&e[0]<=a[0])}))},excludeProgrammatic:function(e,t){return this._filterProgrammatic(e,t,(function(){return!1}))},getProgrammatic:function(e,t){return(0,r.differenceBy)(t,this.excludeProgrammatic(e,t),JSON.stringify)},isProgrammatic:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.strict,o=void 0===r||r,a=this.getProgrammatic(e,[t]),c=1===a.length;if(c)return!0;if(!o)try{var s=i(t,2),l=s[0],u=s[1];return l>15&&u>15}catch(d){return!1}return!1},isEqual:function(e,t){return e===t||JSON.stringify(e)===JSON.stringify(t)},contains:function(e,t){var n=this;return e.filter((function(e){return n.isEqual(e,t)})).length>0}}},50142:function(e,t,n){"use strict";n.d(t,{z:function(){return u}});var r=n(2784),o=n(13980),i=n.n(o),a=n(70002),c=(n(90676),{backToTop:"backToTop__2VLzb",default:"default__1EElP",arrow:"arrow__2fsrk",inverted:"inverted__SQ4aq",visible:"visible__2UC0u",showSlow:"showSlow__1iRjw"}),s=n(56828),l=n.n(s),u=function(e){var t=e.theme,n=void 0===t?"default":t,o=e.label,i=void 0===o?"Back to top":o,s=e.className,u=void 0===s?"":s,d=e.scrollRef,p=e.track,f=(0,r.useState)(""),h=f[0],m=f[1],v=(0,r.useState)(!1),b=v[0],g=v[1],y=(0,r.useState)(),_=y[0],w=y[1];return(0,r.useEffect)((function(){var e=window;d&&d.current&&d.current.id&&(e=d.current,m(e.id));var t=l()((function(){var t=e.scrollTop,n=e.scrollY,r=e.pageYOffset;g(t>20||n>20||r>20)}),100);return e.addEventListener("scroll",t),w(e),function(){e.removeEventListener("scroll",t)}}),[d]),_?r.createElement("a",{href:"#".concat(h),onClick:function(e){_&&_.scroll&&(e.preventDefault(),e.stopPropagation(),_.scroll({top:0,left:0,behavior:"smooth"}),h&&_.setAttribute("tabindex","-1"),setTimeout((function(){_.focus()}),500),"function"===typeof p&&p({unitType:"buzz_bottom",unitName:"fixed",itemType:"button",itemName:"scroll_to_top",actionType:"navigate",actionValue:"scroll_to_top"}))},className:"".concat(c.backToTop," ").concat(c[n]," ").concat(u," ").concat(b?c.visible:""),title:i},r.createElement(a.Hf,{className:c.arrow,"aria-hidden":!0,title:i})):null};u.propTypes={theme:i().oneOf(["default","inverted"]),label:i().string,className:i().string}},70002:function(e,t,n){"use strict";n.d(t,{$K:function(){return h},Hf:function(){return u},QU:function(){return p},VA:function(){return d},b0:function(){return f}});var r=n(82391),o=n(2784);function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){i(e,t,n[t])}))}return e}function c(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function s(e){var t=e.viewBox,n=e.title,i=e.path,a=c(e,["viewBox","title","path"]);return o.createElement("svg",(0,r.Z)({xmlns:"http://www.w3.org/2000/svg",viewBox:t||"0 0 38 38"},a),o.createElement("title",null,n),o.createElement("path",{d:i}))}function l(e){return o.createElement(s,e)}var u=function(e){return l(a({title:"Arrow Up",path:"M27.8 13.2L19 4.4l-8.8 8.8c-.7.7-.7 1.9 0 ******* 1.9.7 2.6 0l4.2-4.2V33h4V11.6l4.2 4.2c.******* 1.3.5s.9-.2 1.3-.5c.7-.7.7-1.9 0-2.6z"},e))},d=function(e){return l(a({title:"Caret Down",path:"M19 29.3L2.6 12.9c-.8-.8-.8-2 0-2.8.8-.8 2-.8 2.8 0L19 23.7l13.6-13.6c.8-.8 2-.8 2.8 0 .8.8.8 2 0 2.8L19 29.3z"},e))},p=function(e){return l(a({title:"Caret Right",path:"M11.5 36c-.5 0-1-.2-1.4-.6-.8-.8-.8-2 0-2.8L23.7 19 10.1 5.4c-.8-.8-.8-2 0-2.8.8-.8 2-.8 2.8 0L29.3 19 12.9 35.4c-.4.4-.9.6-1.4.6z"},e))},f=function(e){return l(a({title:"X",path:"M30.3 10.5l-2.8-2.8-8.5 8.5-8.5-8.5-2.8 2.8 8.5 8.5-8.5 8.5 2.8 2.8 8.5-8.5 8.5 8.5 2.8-2.8-8.5-8.5z"},e))},h=function(e){return o.createElement("svg",(0,r.Z)({viewBox:"0 0 34 41",xmlns:"http://www.w3.org/2000/svg"},e),o.createElement("rect",{width:"34",height:"40",transform:"translate(0 0.0546875)",fill:"white"}),o.createElement("path",{d:"M0.5 39.5547V19.3557L17 10.6204L33.5 19.3557V39.5547H0.5Z",fill:"#FBF6EC",stroke:"black"}),o.createElement("path",{d:"M31 14.0547H3V19.9999L17 25.0547L31 19.9999V14.0547Z",fill:"white",stroke:"black"}),o.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.12019 4.46624C1.34748 4.23894 1.71599 4.23894 1.94329 4.46624L6.05877 8.58171C6.28606 8.80901 6.28606 9.17752 6.05877 9.40481C5.83147 9.6321 5.46296 9.6321 5.23567 9.40481L1.12019 5.28933C0.892899 5.06204 0.892899 4.69353 1.12019 4.46624Z",fill:"black"}),o.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M32.7012 4.64314C32.9285 4.87043 32.9285 5.23894 32.7012 5.46624L28.5858 9.58171C28.3585 9.80901 27.99 9.80901 27.7627 9.58171C27.5354 9.35442 27.5354 8.98591 27.7627 8.75862L31.8781 4.64314C32.1054 4.41585 32.474 4.41585 32.7012 4.64314Z",fill:"black"}),o.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.0034 0.0546875C17.3249 0.0546875 17.5854 0.315265 17.5854 0.636704L17.5854 6.45687C17.5854 6.77831 17.3249 7.03889 17.0034 7.03889C16.682 7.03889 16.4214 6.77831 16.4214 6.45687L16.4214 0.636704C16.4214 0.315265 16.682 0.0546875 17.0034 0.0546875Z",fill:"black"}))}},90113:function(e,t,n){"use strict";n.d(t,{Z:function(){return D}});var r=n(2784),o=n(13980),i=n.n(o);n(74337);!function(){try{localStorage.setItem("ls_test",!0),localStorage.removeItem("ls_test")}catch(e){return!1}}();n(53428);n(33812),n(4180),n(96989);const a="sticky:members-update",c={normal:1,medium:2,high:3},s=new Map,l=new Map,u={};function d(e,t){const n={priority:e},r=new Set;s.forEach(((t,n)=>{t.priority>e||r.add(n)})),l.forEach(((e,o)=>{o!==t&&(s.has(o)&&!r.has(o)||e.forEach((e=>{try{"function"===typeof e?e(n):"fire"in e&&e.fire(a,n)}catch(t){console.error(t)}})))}))}function p(e){return"fixed"===getComputedStyle(e).position}function f(e,t){void 0===t&&(t=p(e));let{top:n,right:r,bottom:o,left:i,width:a,height:c}=e.getBoundingClientRect();const s=window.pageXOffset;return t||(i+=s,r+=s),{top:n,right:r,bottom:o,left:i,width:a,height:c}}const h={get defaultPriorities(){return c},MEMBERS_UPDATE:a,validatePriority(e){if(isNaN(Number(e))){if("string"!==typeof e)throw new TypeError("Unrecognized priority, should be a number or a name");if(void 0===(e=c[e]))throw new TypeError(`Unknown priority name, should be one of ${Object.keys(c)}`)}return e},isFixed:p,getFixedRect(e,{priority:t=c.normal,requestedTop:n="auto"}={}){t=h.validatePriority(t);const r=f(e);let o;return o="auto"===n?h.getAvailableTop(e,{priority:t,boundingRect:r}):n,r.top=o,r.bottom=o+r.height,r},subscribe(e,t=u){l.has(t)||l.set(t,new Set);l.get(t).add(e)},unsubscribe(e,t=u){const n=l.get(t);n&&(n.delete(e),t!==u&&0===n.size&&l.delete(t))},add(e,{priority:t=c.normal,requestedTop:n="auto"}={}){if(s.has(e))return h.update(e);t=h.validatePriority(t);const r=h.getFixedRect(e,{priority:t,requestedTop:n});return s.set(e,{rect:r,priority:t,requestedTop:n}),d(t,e),r.top},update(e,{forceNotify:t=!1}={}){const n=s.get(e);if(!n)throw new Error("The element is not in the registry");const{priority:r,requestedTop:o}=n,i=n.rect,a=h.getFixedRect(e,{priority:r,requestedTop:o});return n.rect=a,s.set(e,n),(t||a.top!==i.top||a.bottom!==i.bottom||a.left!==i.left||a.right!==i.right)&&d(r,e),a.top},remove(e){const t=s.get(e);t&&(e.className.includes("sticky--fixed sticky--show")||s.delete(e),d(t.priority,e))},has:e=>s.has(e),getAvailableTop(e,{priority:t=c.normal,boundingRect:n}={}){t=h.validatePriority(t);const r=[];if(s.forEach(((n,o)=>{o!==e&&n.priority>=t&&r.push(n)})),0===r.length)return 0;if(!n){const t=s.get(e);n=t?t.rect:f(e)}const o=[];return r.forEach((({rect:e})=>{(e.right>=n.left||e.left<=n.right)&&o.push(e)})),Math.max(...o.map((({bottom:e})=>e)))},getTopmostPosition(e=c.normal){e=h.validatePriority(e);const t=[];return s.forEach((n=>{n.priority>e&&t.push(n.rect.bottom)})),Math.max(...t)},reset(){s.clear(),l.clear()}};var m=h;var v=n(22196),b=(n(99855),"aBeagleFlipper__1Sq4F"),g="aBeagleFlipperTitle__2tWV1",y="toggleOpen__2iMAk",_="toggleClosed__11-zU",w="visuallyHidden__TCHEd",E="panel__3Hima",O="controls__20xVx",x="experimentList__EunKK",k="experimentListItem__-wLwY",S="actions__1Anym",j="primary__3JC83",R="empty__27yvl",L=n(70002);function C(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var N="abeagle_";function P(e){var t=function(e){return function(t){var n=t.target,r=n.options[n.selectedIndex].value;r!==h[e]&&m(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){C(e,t,n[t])}))}return e}({},h,C({},e,r)))}},n=e.experiments,o=e.className,i=void 0===o?"":o,a=e.collapse,c=void 0!==a&&a,s=n.eligible||{},l=s&&Object.keys(s).length>0,u=(0,r.useState)(!1),d=u[0],p=u[1],f=(0,r.useState)({}),h=f[0],m=f[1],v=(0,r.useState)(!c),P=v[0],T=v[1],I=(0,r.useState)(""),A=I[0],z=I[1],D=(0,r.useState)(""),M=D[0],U=D[1];return(0,r.useEffect)((function(){var e=Object.keys(s).reduce((function(e,t){return e[t]=s[t].value,e}),{}),t=window.location.href.split("?"),n=t[0];if(t[1]&&t[1].length){var r=new URLSearchParams(t[1]);r.forEach((function(t,n){n.startsWith(N)&&(e[n.replace(N,"")]=t,r.delete(n))})),n+="?".concat(r.toString())}m(e),U(n),p(!0)}),[]),(0,r.useEffect)((function(){var e={};if(Object.keys(h).forEach((function(t){n.returned[t]&&n.returned[t].value!==h[t]&&(e["".concat(N).concat(t)]=h[t])})),Object.keys(e).length>0){var t=new URLSearchParams(e),r=M.includes("?")?"".concat(M,"&"):"".concat(M,"?");z("".concat(r).concat(t.toString()))}else z(M)}),[h,M,P]),d?r.createElement("section",{className:"".concat(b," ").concat(i),"aria-labelledby":"abeagle-flipper-title"},r.createElement("div",{className:g},r.createElement("h2",{id:"abeagle-flipper-title"},"Active A/B Tests on Current Page"),r.createElement("button",{type:"button",onClick:function(){T(!P)},title:P?"Hide All":"Show All",className:P?y:_},r.createElement("span",{className:w},P?"Hide":"Show"," All"),r.createElement(L.VA,{width:30,title:P?"Hide All":"Show All","aria-hidden":"true"}))),P&&r.createElement("div",{className:E},l&&r.createElement("div",{className:O},r.createElement("ul",{className:x},Object.keys(s).map((function(e){return r.createElement("li",{key:e,className:k},r.createElement("label",{htmlFor:e},e),r.createElement("select",{id:e,value:h[e]||n.declared[e].variations[0],onChange:t(e),onBlur:t(e)},n.declared[e].variations.map((function(e){return r.createElement("option",{key:e,value:e},e)}))))}))),r.createElement("div",{className:S},r.createElement("a",{href:A,className:j},"Save and Reload"),r.createElement("a",{href:M},"Reset All"))),!l&&r.createElement("p",{className:R},"No experiments active on this page."))):null}function T(e){var t=e.experiments,n=e.className,o=e.collapse;return t&&t.loaded?r.createElement(P,{experiments:t,className:n,collapse:o}):null}P.propTypes={experiments:i().object,className:i().string,collapse:i().bool};var I=n(21641),A=n(410);function z(e){var t=e.css,n=e.html,o=e.js,i=e.stickyHeaderClass,a=void 0===i?"js-main-nav":i,c=e.stickyRegistry,s=void 0===c?m:c,l=(0,r.useRef)(null);(0,r.useEffect)((function(){!function(e,t=document.head,n=!0){new Promise(((r,o)=>{const i=document.createElement("script");i.onload=()=>r(i),i.onerror=()=>{o(`Script at url ${e} failed to load`)},i.src=e,i.async=n,i.type="text/javascript",t.appendChild(i)}))}(o)}),[o]),(0,r.useEffect)((function(){if(!l.current||!a)return function(){};var e=l.current.querySelector(".".concat(a));return e&&s.add(e,{priority:"high"}),function(){e&&s.remove(e)}}),[a,s]);var u=(0,r.useContext)(v.Z).experiments,d=window.location.search.includes("abdebug"),p=window.location.search.includes("abdebug_collapse=true");return r.createElement("div",{ref:l},r.createElement("style",{dangerouslySetInnerHTML:{__html:t}}),r.createElement("link",{rel:"preload",href:o,as:"script"}),d?r.createElement(T,{experiments:u,collapse:p}):"",r.createElement("div",{dangerouslySetInnerHTML:{__html:n}}))}z.propTypes={css:i().string,html:i().string.isRequired,js:i().string.isRequired,stickyRegistry:i().object,stickyHeaderClass:i().string};var D=(0,I.Z)(z,{onError:A.Tb})},59656:function(e,t,n){"use strict";n.d(t,{Z:function(){return _}});var r=n(2784),o=n(13980),i=n.n(o),a=(n(87063),"subscribeModal__1iYyf"),c="container__3OWEG",s="closeButton__34sfr",l=(n(75174),"content__3GDbx"),u="hr__1pRc4",d="button__2GfA4",p="openEnvelopeWrapper__2GJ3O",f=n(70002);function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e){var t=e.email,n=e.topic,o=e.title,i=void 0===o?"You have been subscribed!":o,a=e.track;return r.createElement("div",{className:l},r.createElement("h4",null,i),r.createElement("div",{className:p},r.createElement(f.$K,{width:34})),n&&r.createElement("p",null,"You will now receive updates for ",n,"."),r.createElement("svg",{className:u,height:"1",width:"100%"},r.createElement("line",{x1:"0",x2:"100%",y1:"0",y2:"0",style:{stroke:"#EDEDED",strokeWidth:2}})),r.createElement("p",{dangerouslySetInnerHTML:{__html:"<b>Set up your free account</b> to engage with the community, create your own content and get a more personalized feed."}}),r.createElement("a",{className:d,onClick:function(e){e.preventDefault(),a&&"function"===typeof a.internalLink&&a.internalLink(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){h(e,t,n[t])}))}return e}({},a.commonTrackingData)),window.location.href="/auth/csrf?provider=auth0&connection=email&login_hint=".concat(t,"&redirect=").concat(encodeURIComponent(window.location.href))},href:"/auth/csrf?provider=auth0&connection=email&login_hint=".concat(t,"&redirect=").concat(window.location.href)},"Create an account"))}function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}m.propTypes={email:i().string,topic:i().string,track:i().object};var b=function(e){var t=e.trackingData,n=void 0===t?{}:t,o=e.trackImpression,i=e.options,a=void 0===i?{}:i,c=e.condition,s=void 0===c||c,l=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.pageId,n=e.once,o=void 0!==n&&n,i=e.root,a=void 0===i?null:i,c=e.rootMargin,s=void 0===c?"0px":c,l=e.threshold,u=void 0===l?0:l,d=e.defaultValue,p=void 0!==d&&d,f=(0,r.useState)(null),h=f[0],m=f[1],v=(0,r.useState)(p),b=v[0],g=v[1],y=(0,r.useRef)(null);return(0,r.useEffect)((function(){return h?(y.current?y.current.disconnect():y.current=new IntersectionObserver((function(e){var t;null!==(t=e[0])&&void 0!==t&&t.isIntersecting?(g(!0),o&&y.current&&y.current.disconnect()):b&&g(!1)}),{root:a,rootMargin:s,threshold:u}),y.current.observe(h),function(){y.current.disconnect()}):function(){}}),[h,t]),{isIntersecting:b,setObservable:m}}(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){v(e,t,n[t])}))}return e}({threshold:.5,once:!0},a)),u=l.isIntersecting,d=l.setObservable;return(0,r.useEffect)((function(){"function"===typeof o&&u&&s&&o(n)}),[u]),{isIntersecting:u,setObservable:d}};function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){g(e,t,n[t])}))}return e}function _(e){var t=e.email,n=e.topic,o=void 0===n?"":n,i=e.track,l=void 0===i?{commonTrackingData:{}}:i,u=e.onClose,d=o.replace(/'/g,"").replace(/\W+/g,"_").toLowerCase(),p=b({trackingData:y({},l.commonTrackingData,{subunit_type:"component",subunit_name:"newsletter_account_signup",item_type:"button",item_name:"create_account",target_content_type:"auth",target_content_id:"sign_in"}),trackImpression:l.impression}).setObservable;(0,r.useEffect)((function(){var e=function(e){27===e.keyCode&&u()};return document.addEventListener("keydown",e),function(){document.removeEventListener("keydown",e)}}),[u]),(0,r.useEffect)((function(){var e=function(e){"modal-backdrop"===e.target.id&&u()};return document.addEventListener("mousedown",e),function(){document.removeEventListener("mousedown",e)}}),[u]);return t?r.createElement("div",{id:"modal-backdrop",className:a,ref:function(e){return p(e)}},r.createElement("div",{className:c},r.createElement("button",{className:s,onClick:function(){l&&"function"===typeof l.contentAction&&l.contentAction(y({},l.commonTrackingData,{subunit_type:"component",subunit_name:"newsletter_account_signup",item_type:"button",item_name:"close_modal",action_type:"close",action_value:"signup_modal"})),u()},"aria-label":"Close subscription confirmation"},r.createElement(f.b0,null)),r.createElement(m,{email:t,topic:o,topicName:d,title:"You\u2019re subscribed!",track:y({},l,{commonTrackingData:y({},l.commonTrackingData,{subunit_type:"component",subunit_name:"newsletter_account_signup",item_type:"button",item_name:"create_account",target_content_type:"auth",target_content_id:"sign_in"})})}))):null}_.propTypes={email:i().string.isRequired,onClose:i().func.isRequired,topic:i().string,track:i().object}},30466:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(2784),o=n(56828),i=n.n(o);function a(){var e=function(){a({width:window.innerWidth,height:window.innerHeight})},t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:150,n=(0,r.useState)({width:null,height:null}),o=n[0],a=n[1];return(0,r.useEffect)((function(){var n=i()(e,t);return window.addEventListener("resize",n),e(),function(){return window.removeEventListener("resize",n)}}),[t]),o}},31387:function(e,t,n){"use strict";var r=n(52322),o=n(12524),i=n.n(o),a=n(13980),c=n.n(a),s=n(2784),l=n(65973),u=n.n(l);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var f={PRIMARY:"primary",SECONDARY:"secondary",WARNING:"warning"},h="md",m=s.forwardRef((function(e,t){var n=e.children,o=void 0===n?null:n,a=e.className,c=void 0===a?null:a,s=e.disabled,l=void 0!==s&&s,m=e.onClick,v=void 0===m?function(){}:m,b=e.size,g=void 0===b?h:b,y=e.type,_=void 0===y?"button":y,w=e.variant,E=void 0===w?f.PRIMARY:w,O=p(e,["children","className","disabled","onClick","size","type","variant"]),x=i()(u().button,u()["button__".concat(E)],u()["button__".concat(g)],d({},u().button__disabled,!!l),c);return(0,r.jsx)("button",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){d(e,t,n[t])}))}return e}({type:_,disabled:l,className:x,ref:t,onClick:v},O,{children:o}))}));m.displayName="Button";m.propTypes={variant:c().oneOf(Object.values(f))},t.ZP=m},65973:function(e){e.exports={button:"button_button__IY2Xk",button__primary:"button_button__primary__cm7rW",button__secondary:"button_button__secondary__DYFGe",button__warning:"button_button__warning__PdoY0",button__icon:"button_button__icon__YQ_aa",padLeft:"button_padLeft__fXAYu",padRight:"button_padRight__qpcmn",button__disabled:"button_button__disabled__Bzvcq",button__sm:"button_button__sm__KFFoo",button__md:"button_button__md__oYr1E",button__xl:"button_button__xl__Np03V"}},99855:function(){},90676:function(){},87063:function(){},75174:function(){},97729:function(e,t,n){e.exports=n(67016)},3176:function(e,t,n){"use strict";n.d(t,{Z:function(){return g}});var r=n(2784),o=n(13980),i=n.n(o);function a(){return a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},a.apply(this,arguments)}function c(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var s=function(e){var t,n;function o(){var t;return(t=e.call(this)||this).handleExpired=t.handleExpired.bind(c(t)),t.handleErrored=t.handleErrored.bind(c(t)),t.handleChange=t.handleChange.bind(c(t)),t.handleRecaptchaRef=t.handleRecaptchaRef.bind(c(t)),t}n=e,(t=o).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n;var i=o.prototype;return i.getValue=function(){return this.props.grecaptcha&&void 0!==this._widgetId?this.props.grecaptcha.getResponse(this._widgetId):null},i.getWidgetId=function(){return this.props.grecaptcha&&void 0!==this._widgetId?this._widgetId:null},i.execute=function(){var e=this.props.grecaptcha;if(e&&void 0!==this._widgetId)return e.execute(this._widgetId);this._executeRequested=!0},i.executeAsync=function(){var e=this;return new Promise((function(t,n){e.executionResolve=t,e.executionReject=n,e.execute()}))},i.reset=function(){this.props.grecaptcha&&void 0!==this._widgetId&&this.props.grecaptcha.reset(this._widgetId)},i.handleExpired=function(){this.props.onExpired?this.props.onExpired():this.handleChange(null)},i.handleErrored=function(){this.props.onErrored&&this.props.onErrored(),this.executionReject&&(this.executionReject(),delete this.executionResolve,delete this.executionReject)},i.handleChange=function(e){this.props.onChange&&this.props.onChange(e),this.executionResolve&&(this.executionResolve(e),delete this.executionReject,delete this.executionResolve)},i.explicitRender=function(){if(this.props.grecaptcha&&this.props.grecaptcha.render&&void 0===this._widgetId){var e=document.createElement("div");this._widgetId=this.props.grecaptcha.render(e,{sitekey:this.props.sitekey,callback:this.handleChange,theme:this.props.theme,type:this.props.type,tabindex:this.props.tabindex,"expired-callback":this.handleExpired,"error-callback":this.handleErrored,size:this.props.size,stoken:this.props.stoken,hl:this.props.hl,badge:this.props.badge}),this.captcha.appendChild(e)}this._executeRequested&&this.props.grecaptcha&&void 0!==this._widgetId&&(this._executeRequested=!1,this.execute())},i.componentDidMount=function(){this.explicitRender()},i.componentDidUpdate=function(){this.explicitRender()},i.componentWillUnmount=function(){void 0!==this._widgetId&&(this.delayOfCaptchaIframeRemoving(),this.reset())},i.delayOfCaptchaIframeRemoving=function(){var e=document.createElement("div");for(document.body.appendChild(e),e.style.display="none";this.captcha.firstChild;)e.appendChild(this.captcha.firstChild);setTimeout((function(){document.body.removeChild(e)}),5e3)},i.handleRecaptchaRef=function(e){this.captcha=e},i.render=function(){var e=this.props,t=(e.sitekey,e.onChange,e.theme,e.type,e.tabindex,e.onExpired,e.onErrored,e.size,e.stoken,e.grecaptcha,e.badge,e.hl,function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,["sitekey","onChange","theme","type","tabindex","onExpired","onErrored","size","stoken","grecaptcha","badge","hl"]));return r.createElement("div",a({},t,{ref:this.handleRecaptchaRef}))},o}(r.Component);s.displayName="ReCAPTCHA",s.propTypes={sitekey:i().string.isRequired,onChange:i().func,grecaptcha:i().object,theme:i().oneOf(["dark","light"]),type:i().oneOf(["image","audio"]),tabindex:i().number,onExpired:i().func,onErrored:i().func,size:i().oneOf(["compact","normal","invisible"]),stoken:i().string,hl:i().string,badge:i().oneOf(["bottomright","bottomleft","inline"])},s.defaultProps={onChange:function(){},theme:"light",type:"image",tabindex:0,size:"normal",badge:"bottomright"};var l=n(73463),u=n.n(l);function d(){return d=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},d.apply(this,arguments)}var p={},f=0;var h="onloadcallback";var m,v,b=(m=function(){return"https://"+(("undefined"!==typeof window&&window.recaptchaOptions||{}).useRecaptchaNet?"recaptcha.net":"www.google.com")+"/recaptcha/api.js?onload="+h+"&render=explicit"},v=(v={callbackName:h,globalName:"grecaptcha"})||{},function(e){var t=e.displayName||e.name||"Component",n=function(t){var n,o;function i(e,n){var r;return(r=t.call(this,e,n)||this).state={},r.__scriptURL="",r}o=t,(n=i).prototype=Object.create(o.prototype),n.prototype.constructor=n,n.__proto__=o;var a=i.prototype;return a.asyncScriptLoaderGetScriptLoaderID=function(){return this.__scriptLoaderID||(this.__scriptLoaderID="async-script-loader-"+f++),this.__scriptLoaderID},a.setupScriptURL=function(){return this.__scriptURL="function"===typeof m?m():m,this.__scriptURL},a.asyncScriptLoaderHandleLoad=function(e){var t=this;this.setState(e,(function(){return t.props.asyncScriptOnLoad&&t.props.asyncScriptOnLoad(t.state)}))},a.asyncScriptLoaderTriggerOnScriptLoaded=function(){var e=p[this.__scriptURL];if(!e||!e.loaded)throw new Error("Script is not loaded.");for(var t in e.observers)e.observers[t](e);delete window[v.callbackName]},a.componentDidMount=function(){var e=this,t=this.setupScriptURL(),n=this.asyncScriptLoaderGetScriptLoaderID(),r=v,o=r.globalName,i=r.callbackName,a=r.scriptId;if(o&&"undefined"!==typeof window[o]&&(p[t]={loaded:!0,observers:{}}),p[t]){var c=p[t];return c&&(c.loaded||c.errored)?void this.asyncScriptLoaderHandleLoad(c):void(c.observers[n]=function(t){return e.asyncScriptLoaderHandleLoad(t)})}var s={};s[n]=function(t){return e.asyncScriptLoaderHandleLoad(t)},p[t]={loaded:!1,observers:s};var l=document.createElement("script");for(var u in l.src=t,l.async=!0,v.attributes)l.setAttribute(u,v.attributes[u]);a&&(l.id=a);var d=function(e){if(p[t]){var n=p[t].observers;for(var r in n)e(n[r])&&delete n[r]}};i&&"undefined"!==typeof window&&(window[i]=function(){return e.asyncScriptLoaderTriggerOnScriptLoaded()}),l.onload=function(){var e=p[t];e&&(e.loaded=!0,d((function(t){return!i&&(t(e),!0)})))},l.onerror=function(){var e=p[t];e&&(e.errored=!0,d((function(t){return t(e),!0})))},document.body.appendChild(l)},a.componentWillUnmount=function(){var e=this.__scriptURL;if(!0===v.removeOnUnmount)for(var t=document.getElementsByTagName("script"),n=0;n<t.length;n+=1)t[n].src.indexOf(e)>-1&&t[n].parentNode&&t[n].parentNode.removeChild(t[n]);var r=p[e];r&&(delete r.observers[this.asyncScriptLoaderGetScriptLoaderID()],!0===v.removeOnUnmount&&delete p[e])},a.render=function(){var t=v.globalName,n=this.props,o=(n.asyncScriptOnLoad,n.forwardedRef),i=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(n,["asyncScriptOnLoad","forwardedRef"]);return t&&"undefined"!==typeof window&&(i[t]="undefined"!==typeof window[t]?window[t]:void 0),i.ref=o,(0,r.createElement)(e,i)},i}(r.Component),o=(0,r.forwardRef)((function(e,t){return(0,r.createElement)(n,d({},e,{forwardedRef:t}))}));return o.displayName="AsyncScriptLoader("+t+")",o.propTypes={asyncScriptOnLoad:i().func},u()(o,e)})(s),g=b},12524:function(e,t){var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=a(e,i(n)))}return e}function i(e){if("string"===typeof e||"number"===typeof e)return e;if("object"!==typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=a(t,n));return t}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},82391:function(e,t,n){"use strict";function r(){return r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(this,arguments)}n.d(t,{Z:function(){return r}})}}]);
//# sourceMappingURL=297-333f0fe9ae8254bc.js.map