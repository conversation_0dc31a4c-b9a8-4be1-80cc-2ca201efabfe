(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[338],{86325:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(2784),o=(n(75810),"productGridWrapper__18PGW"),i="product__RL2FL",a=n(24823),c=function(e){var t=e.products,n=void 0===t?[]:t,c=e.offset,s=void 0===c?0:c,u=e.len,d=void 0===u?6:u,p=e.tracking,l=void 0===p?{}:p,h=e.useUnitTracking;return!n||n.length<6?"":r.createElement("div",{className:"".concat(o," ")},r.createElement(a.Z,{products:n,offset:s,len:d,tracking:l,useUnitTracking:h,className:i}))}},27912:function(e,t,n){"use strict";n.d(t,{Z:function(){return w}});var r=n(2784),o=n(13980),i=n.n(o),a=(n(88282),"subscribeModal__1iYyf"),c="container__3OWEG",s="closeButton__34sfr",u=(n(78839),"content__3GDbx"),d="hr__1pRc4",p="button__2GfA4",l="openEnvelopeWrapper__2GJ3O",h=n(81298);function f(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e){var t=e.email,n=e.topic,o=e.title,i=void 0===o?"You have been subscribed!":o,a=e.track;return r.createElement("div",{className:u},r.createElement("h4",null,i),r.createElement("div",{className:l},r.createElement(h.$K,{width:34})),n&&r.createElement("p",null,"You will now receive updates for ",n,"."),r.createElement("svg",{className:d,height:"1",width:"100%"},r.createElement("line",{x1:"0",x2:"100%",y1:"0",y2:"0",style:{stroke:"#EDEDED",strokeWidth:2}})),r.createElement("p",{dangerouslySetInnerHTML:{__html:"<b>Set up your free account</b> to engage with the community, create your own content and get a more personalized feed."}}),r.createElement("a",{className:p,onClick:function(e){e.preventDefault(),a&&"function"===typeof a.internalLink&&a.internalLink(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){f(e,t,n[t])}))}return e}({},a.commonTrackingData)),window.location.href="/auth/csrf?provider=auth0&connection=email&login_hint=".concat(t,"&redirect=").concat(encodeURIComponent(window.location.href))},href:"/auth/csrf?provider=auth0&connection=email&login_hint=".concat(t,"&redirect=").concat(window.location.href)},"Create an account"))}m.propTypes={email:i().string,topic:i().string,track:i().object};var v=n(88163);function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var b=function(e){var t=e.trackingData,n=void 0===t?{}:t,o=e.trackImpression,i=e.options,a=void 0===i?{}:i,c=e.condition,s=void 0===c||c,u=(0,v.Z)(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){g(e,t,n[t])}))}return e}({threshold:.5,once:!0},a)),d=u.isIntersecting,p=u.setObservable;return(0,r.useEffect)((function(){"function"===typeof o&&d&&s&&o(n)}),[d]),{isIntersecting:d,setObservable:p}};function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){y(e,t,n[t])}))}return e}function w(e){var t=e.email,n=e.topic,o=void 0===n?"":n,i=e.track,u=void 0===i?{commonTrackingData:{}}:i,d=e.onClose,p=o.replace(/'/g,"").replace(/\W+/g,"_").toLowerCase(),l=b({trackingData:_({},u.commonTrackingData,{subunit_type:"component",subunit_name:"newsletter_account_signup",item_type:"button",item_name:"create_account",target_content_type:"auth",target_content_id:"sign_in"}),trackImpression:u.impression}).setObservable;(0,r.useEffect)((function(){var e=function(e){27===e.keyCode&&d()};return document.addEventListener("keydown",e),function(){document.removeEventListener("keydown",e)}}),[d]),(0,r.useEffect)((function(){var e=function(e){"modal-backdrop"===e.target.id&&d()};return document.addEventListener("mousedown",e),function(){document.removeEventListener("mousedown",e)}}),[d]);return t?r.createElement("div",{id:"modal-backdrop",className:a,ref:function(e){return l(e)}},r.createElement("div",{className:c},r.createElement("button",{className:s,onClick:function(){u&&"function"===typeof u.contentAction&&u.contentAction(_({},u.commonTrackingData,{subunit_type:"component",subunit_name:"newsletter_account_signup",item_type:"button",item_name:"close_modal",action_type:"close",action_value:"signup_modal"})),d()},"aria-label":"Close subscription confirmation"},r.createElement(h.b0,null)),r.createElement(m,{email:t,topic:o,topicName:p,title:"You\u2019re subscribed!",track:_({},u,{commonTrackingData:_({},u.commonTrackingData,{subunit_type:"component",subunit_name:"newsletter_account_signup",item_type:"button",item_name:"create_account",target_content_type:"auth",target_content_id:"sign_in"})})}))):null}w.propTypes={email:i().string.isRequired,onClose:i().func.isRequired,topic:i().string,track:i().object}},65831:function(e,t,n){"use strict";n.d(t,{P:function(){return s}});var r=n(94776),o=n.n(r),i=n(21038),a=n(25108);function c(e,t,n,r,o,i,a){try{var c=e[i](a),s=c.value}catch(u){return void n(u)}c.done?t(s):Promise.resolve(s).then(r,o)}var s=function(){var e,t=(e=o().mark((function e(t){var n,r;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=!1,r=!1,i.MS.needsConsent()){e.next=7;break}n=!0,r=!0,e.next=11;break;case 7:return e.next=9,i.jQ.hasConsented("tracking");case 9:r=e.sent,n=!0;case 11:(0,a.AZ)({email:t,isConsentReady:n,consentValue:r});case 12:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){c(i,r,o,a,s,"next",e)}function s(e){c(i,r,o,a,s,"throw",e)}a(void 0)}))});return function(e){return t.apply(this,arguments)}}()},75810:function(){},88282:function(){},78839:function(){},3176:function(e,t,n){"use strict";n.d(t,{Z:function(){return b}});var r=n(2784),o=n(13980),i=n.n(o);function a(){return a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},a.apply(this,arguments)}function c(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var s=function(e){var t,n;function o(){var t;return(t=e.call(this)||this).handleExpired=t.handleExpired.bind(c(t)),t.handleErrored=t.handleErrored.bind(c(t)),t.handleChange=t.handleChange.bind(c(t)),t.handleRecaptchaRef=t.handleRecaptchaRef.bind(c(t)),t}n=e,(t=o).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n;var i=o.prototype;return i.getValue=function(){return this.props.grecaptcha&&void 0!==this._widgetId?this.props.grecaptcha.getResponse(this._widgetId):null},i.getWidgetId=function(){return this.props.grecaptcha&&void 0!==this._widgetId?this._widgetId:null},i.execute=function(){var e=this.props.grecaptcha;if(e&&void 0!==this._widgetId)return e.execute(this._widgetId);this._executeRequested=!0},i.executeAsync=function(){var e=this;return new Promise((function(t,n){e.executionResolve=t,e.executionReject=n,e.execute()}))},i.reset=function(){this.props.grecaptcha&&void 0!==this._widgetId&&this.props.grecaptcha.reset(this._widgetId)},i.handleExpired=function(){this.props.onExpired?this.props.onExpired():this.handleChange(null)},i.handleErrored=function(){this.props.onErrored&&this.props.onErrored(),this.executionReject&&(this.executionReject(),delete this.executionResolve,delete this.executionReject)},i.handleChange=function(e){this.props.onChange&&this.props.onChange(e),this.executionResolve&&(this.executionResolve(e),delete this.executionReject,delete this.executionResolve)},i.explicitRender=function(){if(this.props.grecaptcha&&this.props.grecaptcha.render&&void 0===this._widgetId){var e=document.createElement("div");this._widgetId=this.props.grecaptcha.render(e,{sitekey:this.props.sitekey,callback:this.handleChange,theme:this.props.theme,type:this.props.type,tabindex:this.props.tabindex,"expired-callback":this.handleExpired,"error-callback":this.handleErrored,size:this.props.size,stoken:this.props.stoken,hl:this.props.hl,badge:this.props.badge}),this.captcha.appendChild(e)}this._executeRequested&&this.props.grecaptcha&&void 0!==this._widgetId&&(this._executeRequested=!1,this.execute())},i.componentDidMount=function(){this.explicitRender()},i.componentDidUpdate=function(){this.explicitRender()},i.componentWillUnmount=function(){void 0!==this._widgetId&&(this.delayOfCaptchaIframeRemoving(),this.reset())},i.delayOfCaptchaIframeRemoving=function(){var e=document.createElement("div");for(document.body.appendChild(e),e.style.display="none";this.captcha.firstChild;)e.appendChild(this.captcha.firstChild);setTimeout((function(){document.body.removeChild(e)}),5e3)},i.handleRecaptchaRef=function(e){this.captcha=e},i.render=function(){var e=this.props,t=(e.sitekey,e.onChange,e.theme,e.type,e.tabindex,e.onExpired,e.onErrored,e.size,e.stoken,e.grecaptcha,e.badge,e.hl,function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,["sitekey","onChange","theme","type","tabindex","onExpired","onErrored","size","stoken","grecaptcha","badge","hl"]));return r.createElement("div",a({},t,{ref:this.handleRecaptchaRef}))},o}(r.Component);s.displayName="ReCAPTCHA",s.propTypes={sitekey:i().string.isRequired,onChange:i().func,grecaptcha:i().object,theme:i().oneOf(["dark","light"]),type:i().oneOf(["image","audio"]),tabindex:i().number,onExpired:i().func,onErrored:i().func,size:i().oneOf(["compact","normal","invisible"]),stoken:i().string,hl:i().string,badge:i().oneOf(["bottomright","bottomleft","inline"])},s.defaultProps={onChange:function(){},theme:"light",type:"image",tabindex:0,size:"normal",badge:"bottomright"};var u=n(73463),d=n.n(u);function p(){return p=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},p.apply(this,arguments)}var l={},h=0;var f="onloadcallback";var m,v,g=(m=function(){return"https://"+(("undefined"!==typeof window&&window.recaptchaOptions||{}).useRecaptchaNet?"recaptcha.net":"www.google.com")+"/recaptcha/api.js?onload="+f+"&render=explicit"},v=(v={callbackName:f,globalName:"grecaptcha"})||{},function(e){var t=e.displayName||e.name||"Component",n=function(t){var n,o;function i(e,n){var r;return(r=t.call(this,e,n)||this).state={},r.__scriptURL="",r}o=t,(n=i).prototype=Object.create(o.prototype),n.prototype.constructor=n,n.__proto__=o;var a=i.prototype;return a.asyncScriptLoaderGetScriptLoaderID=function(){return this.__scriptLoaderID||(this.__scriptLoaderID="async-script-loader-"+h++),this.__scriptLoaderID},a.setupScriptURL=function(){return this.__scriptURL="function"===typeof m?m():m,this.__scriptURL},a.asyncScriptLoaderHandleLoad=function(e){var t=this;this.setState(e,(function(){return t.props.asyncScriptOnLoad&&t.props.asyncScriptOnLoad(t.state)}))},a.asyncScriptLoaderTriggerOnScriptLoaded=function(){var e=l[this.__scriptURL];if(!e||!e.loaded)throw new Error("Script is not loaded.");for(var t in e.observers)e.observers[t](e);delete window[v.callbackName]},a.componentDidMount=function(){var e=this,t=this.setupScriptURL(),n=this.asyncScriptLoaderGetScriptLoaderID(),r=v,o=r.globalName,i=r.callbackName,a=r.scriptId;if(o&&"undefined"!==typeof window[o]&&(l[t]={loaded:!0,observers:{}}),l[t]){var c=l[t];return c&&(c.loaded||c.errored)?void this.asyncScriptLoaderHandleLoad(c):void(c.observers[n]=function(t){return e.asyncScriptLoaderHandleLoad(t)})}var s={};s[n]=function(t){return e.asyncScriptLoaderHandleLoad(t)},l[t]={loaded:!1,observers:s};var u=document.createElement("script");for(var d in u.src=t,u.async=!0,v.attributes)u.setAttribute(d,v.attributes[d]);a&&(u.id=a);var p=function(e){if(l[t]){var n=l[t].observers;for(var r in n)e(n[r])&&delete n[r]}};i&&"undefined"!==typeof window&&(window[i]=function(){return e.asyncScriptLoaderTriggerOnScriptLoaded()}),u.onload=function(){var e=l[t];e&&(e.loaded=!0,p((function(t){return!i&&(t(e),!0)})))},u.onerror=function(){var e=l[t];e&&(e.errored=!0,p((function(t){return t(e),!0})))},document.body.appendChild(u)},a.componentWillUnmount=function(){var e=this.__scriptURL;if(!0===v.removeOnUnmount)for(var t=document.getElementsByTagName("script"),n=0;n<t.length;n+=1)t[n].src.indexOf(e)>-1&&t[n].parentNode&&t[n].parentNode.removeChild(t[n]);var r=l[e];r&&(delete r.observers[this.asyncScriptLoaderGetScriptLoaderID()],!0===v.removeOnUnmount&&delete l[e])},a.render=function(){var t=v.globalName,n=this.props,o=(n.asyncScriptOnLoad,n.forwardedRef),i=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(n,["asyncScriptOnLoad","forwardedRef"]);return t&&"undefined"!==typeof window&&(i[t]="undefined"!==typeof window[t]?window[t]:void 0),i.ref=o,(0,r.createElement)(e,i)},i}(r.Component),o=(0,r.forwardRef)((function(e,t){return(0,r.createElement)(n,p({},e,{forwardedRef:t}))}));return o.displayName="AsyncScriptLoader("+t+")",o.propTypes={asyncScriptOnLoad:i().func},d()(o,e)})(s),b=g}}]);
//# sourceMappingURL=338-2d9acda8a487fc5b.js.map