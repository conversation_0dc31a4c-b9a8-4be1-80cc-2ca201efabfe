(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[38],{37963:function(e,t,n){"use strict";var i=n(52322),r=n(2784),o=n(13980),a=n.n(o),l=n(48243),s=n(61360),c=n(45201),u=n(30353),m=n(26002),d=n(74967),_=n(60565),f=n(39283),p=n(29875),h=n(2706);function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function C(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),i.forEach((function(t){C(e,t,n[t])}))}return e}function b(e){return function(e){if(Array.isArray(e))return g(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return g(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return g(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(e){var t=e.children,n=e.pageName,o=(0,m.I)({cluster:u.ov}),a=(0,_.E)(),g=a.pageInfo,C=a.trackExperimentActive,v=(0,r.useContext)(l.z1).path,w=(0,r.useRef)(null),x=(null===g||void 0===g?void 0:g.page_edition)||"en-us",j=(0,f.Y)({isFeedUI:!0,isBPage:!1,isHomePage:"home"===n,isNewBPage:!1,isFeedPage:!0,isBFO:!0,isBFN:!1,localizationCountry:x,userCountry:(0,h.isServer)()?"":(0,p.pP)(),edition:x,isAdPost:function(){return!1}}),k=(0,r.useMemo)((function(){return{userId:o,data:y({},g)}}),[o]),S=(0,c.Z)(y({abeagleHost:u.in,experimentConfig:b(s.ZP).concat(b(j)),source:"buzz_web"},k));w.current&&w.current.loaded===S.loaded||(w.current=y({},S,{path:v})),w.current.stale=w.current.path!==v;var T=w.current.loaded,P=S.eligible?Object.keys(S.eligible).join("|"):"";(0,r.useEffect)((function(){if(T&&P.length){var e=[];Object.keys(S.eligible).forEach((function(t){var n=S.eligible[t];if(n&&n.value){var i=n.id,r=n.version,o=n.value,a=n.variant_id;e.push([t,i,r,o,a].join("|"))}})),C({experiment_id:e})}}),[v,T,P]);return(0,i.jsx)(l.WN.Provider,{value:{experiments:w.current,getFeatureFlagValue:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"on";return(0,d.F7)(S,e,t)},getExperimentValue:function(e,t){return(0,d.ts)(S,e,t)}},children:t})}v.propTypes={children:a().oneOfType([a().arrayOf(a().node),a().node])},t.Z=v},20708:function(e,t,n){"use strict";var i=n(52322),r=n(39097),o=n(5632),a=n(2945),l=n.n(a),s=n(65267),c=n.n(s);t.Z=function(){var e=(0,o.useRouter)().asPath,t="/community",n=[{name:"Featured posts",path:"".concat(t,"/featured")},{name:"Leaderboard",path:"".concat(t,"/leaderboard")},{name:"Become a Contributor",path:"".concat(t,"/contribute")},{name:"Guidelines",path:"".concat(t,"/guidelines")}];return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:l().wrapper,children:(0,i.jsx)("div",{className:l().background,style:{backgroundImage:"url(".concat(c(),")")},children:(0,i.jsxs)("h1",{children:[(0,i.jsx)("span",{className:l().title,children:"BuzzFeed"}),(0,i.jsx)("span",{className:l().desc,children:"Community"})]})})}),(0,i.jsx)("div",{className:l().navbar,children:(0,i.jsx)("ul",{className:l().nav,children:n.map((function(t){return(0,i.jsx)("li",{children:(0,i.jsx)(r.default,{href:t.path,children:(0,i.jsx)("a",{className:e===t.path?l().activeTab:void 0,children:t.name})})},t.name)}))})})]})}},91987:function(e,t,n){"use strict";var i=n(52322),r=n(72772),o=n.n(r);t.Z=function(e){var t=e.title,n=e.titleSmall,r=void 0!==n&&n;return(0,i.jsx)("p",{className:r?o().titleSmall:o().title,children:t})}},51383:function(e,t,n){"use strict";n.d(t,{Z:function(){return u}});var i=n(52322),r=n(73663),o=n.n(r),a=n(46993),l=function(e){var t=e.fillColor,n=void 0===t?"#ffffff":t;return(0,i.jsxs)("svg",{width:"29",height:"28",viewBox:"0 0 29 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsx)("title",{children:"Instagram"}),(0,i.jsx)("rect",{x:"0.777832",width:"28",height:"28",rx:"14",fill:"#222222"}),(0,i.jsxs)("g",{clipPath:"url(#clip0_472_1962)",children:[(0,i.jsx)("mask",{id:"mask0_472_1962",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:"5",y:"4",width:"20",height:"20",children:(0,i.jsx)("path",{d:"M10.9315 4.77732C9.9434 4.82396 9.26849 4.98224 8.67873 5.21333C8.06821 5.45133 7.55068 5.7703 7.03585 6.28724C6.52041 6.80449 6.20387 7.32293 5.96767 7.93406C5.73899 8.52533 5.58402 9.20056 5.54039 10.1893C5.49706 11.1799 5.48713 11.4958 5.49196 14.0177C5.49677 16.5392 5.5079 16.8557 5.55514 17.8466C5.60237 18.8345 5.76005 19.5091 5.99114 20.0992C6.22945 20.7097 6.54809 21.2269 7.06533 21.7421C7.58227 22.2572 8.10102 22.5735 8.71243 22.81C9.30309 23.0383 9.9786 23.1939 10.9671 23.2372C11.9576 23.2809 12.2738 23.2905 14.7951 23.2857C17.3175 23.2809 17.6337 23.2697 18.6243 23.2228C19.6124 23.1756 20.2867 23.0173 20.8771 22.7868C21.4876 22.5479 22.0051 22.2298 22.5199 21.7126C23.0348 21.1956 23.3513 20.6769 23.5875 20.0655C23.8162 19.4748 23.9718 18.7993 24.0148 17.8114C24.0581 16.8202 24.0684 16.5037 24.0635 13.9822C24.0587 11.4603 24.0473 11.1444 24.0004 10.1541C23.9534 9.16535 23.7952 8.49103 23.5644 7.90067C23.3258 7.29014 23.0074 6.77319 22.4905 6.25775C21.9735 5.74292 21.4548 5.42576 20.8434 5.19016C20.2524 4.96147 19.5772 4.80561 18.5887 4.76288C17.5982 4.71895 17.282 4.70932 14.7598 4.71414C12.2383 4.71895 11.9221 4.72979 10.9315 4.77732ZM11.0399 21.5657C10.1345 21.5263 9.64281 21.3759 9.31514 21.2498C8.88124 21.0819 8.57162 20.8806 8.24575 20.5571C7.92048 20.2325 7.71858 19.9237 7.54918 19.4907C7.42189 19.163 7.26874 18.672 7.22631 17.7666C7.18027 16.7877 7.16975 16.4941 7.16523 14.0143C7.16042 11.5352 7.16944 11.2416 7.21217 10.2618C7.25098 9.35702 7.40234 8.86475 7.52811 8.53737C7.696 8.10287 7.8967 7.79384 8.22077 7.46797C8.54543 7.1421 8.85415 6.94079 9.28744 6.77139C9.61482 6.6435 10.1059 6.49155 11.011 6.44852C11.9904 6.40218 12.2838 6.39225 14.7629 6.38744C17.2425 6.38262 17.5362 6.39135 18.5159 6.43438C19.4207 6.47379 19.913 6.62365 20.2401 6.75032C20.6743 6.91822 20.9839 7.11832 21.3095 7.443C21.635 7.76767 21.8369 8.07579 22.0063 8.50999C22.1342 8.83646 22.2862 9.32813 22.3289 10.2329C22.3755 11.2124 22.3861 11.506 22.3906 13.9852C22.3954 16.4649 22.3864 16.7586 22.3433 17.7377C22.3039 18.6431 22.1538 19.1351 22.0274 19.463C21.8595 19.8966 21.6588 20.2063 21.3344 20.5321C21.0101 20.8568 20.7013 21.0593 20.2677 21.2287C19.941 21.3563 19.4493 21.5086 18.5448 21.5516C17.5654 21.5976 17.272 21.6082 14.792 21.6127C12.313 21.6175 12.0196 21.6082 11.0399 21.5657ZM18.6107 9.03566C18.6119 9.6507 19.112 10.1487 19.727 10.1475C20.3424 10.1463 20.8404 9.64649 20.8395 9.03145C20.8382 8.41641 20.3382 7.91812 19.7228 7.91933C19.1075 7.92053 18.6095 8.42062 18.6107 9.03566ZM10.0093 14.0089C10.0144 16.6427 12.1535 18.7731 14.7866 18.768C17.4201 18.7629 19.5516 16.6244 19.5465 13.9906C19.5414 11.3577 17.402 9.22613 14.7683 9.23125C12.1351 9.23636 10.0042 11.3758 10.0093 14.0089ZM11.682 14.0056C11.679 12.2962 13.0625 10.9076 14.7716 10.9046C16.481 10.9012 17.8699 12.2842 17.8732 13.9939C17.8765 15.7036 16.493 17.092 14.7833 17.0953C13.0742 17.0986 11.6853 15.7153 11.682 14.0056Z",fill:"white"})}),(0,i.jsxs)("g",{mask:"url(#mask0_472_1962)",children:[(0,i.jsx)("mask",{id:"mask1_472_1962",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:"4",y:"3",width:"21",height:"22",children:(0,i.jsx)("path",{d:"M24.8003 3.9856H4.74055V24.0456H24.8003V3.9856Z",fill:"white"})}),(0,i.jsxs)("g",{mask:"url(#mask1_472_1962)",children:[(0,i.jsx)("mask",{id:"mask2_472_1962",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:"4",y:"3",width:"21",height:"22",children:(0,i.jsx)("path",{d:"M24.8931 3.89264H4.64766V24.1384H24.8931V3.89264Z",fill:"white"})}),(0,i.jsxs)("g",{mask:"url(#mask2_472_1962)",children:[(0,i.jsx)("mask",{id:"mask3_472_1962",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:"4",y:"3",width:"21",height:"22",children:(0,i.jsx)("path",{d:"M24.8931 3.89264H4.64766V24.1384H24.8931V3.89264Z",fill:"white"})}),(0,i.jsx)("g",{mask:"url(#mask3_472_1962)",children:(0,i.jsx)("rect",{x:"4.64099",y:"3.88953",width:"20.2558",height:"20.2558",fill:n})})]})]})]})]}),(0,i.jsx)("defs",{children:(0,i.jsx)("clipPath",{id:"clip0_472_1962",children:(0,i.jsx)("rect",{x:"5.4445",y:"4.66669",width:"18.6667",height:"18.6667",fill:"white"})})})]})},s=n(11423),c=n(91987),u=function(e){var t=e.titleSmall,n=void 0!==t&&t,r=e.fillColor,u=void 0===r?"#ffffff":r,m=e.twitterColorDefault,d=void 0===m||m;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(c.Z,{title:"Follow BuzzFeed Community",titleSmall:n}),(0,i.jsxs)("div",{className:o().socialIcons,children:[(0,i.jsx)("a",{className:o().socialIconLink,target:"_blank",href:"https://www.facebook.com/BuzzfeedCommunity",rel:"noreferrer",children:(0,i.jsx)(a.V,{bgColor:"#222",fillColor:u})}),(0,i.jsx)("a",{className:o().socialIconLink,target:"_blank",href:"https://www.instagram.com/buzzfeedcommunity",rel:"noreferrer",children:(0,i.jsx)(l,{fillColor:u})}),(0,i.jsx)("a",{className:"".concat(o().socialIconLink," ").concat(d?o().twitterWhite:o().twitterPurple),target:"_blank",href:"http://www.twitter.com/buzzfeeders",rel:"noreferrer",children:(0,i.jsx)(s.Ub,{})})]})]})}},97143:function(e,t,n){"use strict";n.d(t,{Z:function(){return l}});var i=n(2784),r=n(75951),o=n(74967),a=n(48243);function l(e){var t=e.isCommunity,n=(0,i.useContext)(a.WN).experiments;return t&&r.jQ.fetchCCPAValue(),(0,i.useEffect)((function(){if(n.loaded&&!n.stale){var e=(0,o.F7)(n,r.Yx.name);r.hi.configure({useFallback:!e})}}),[n.loaded,n.stale]),(0,i.useEffect)((function(){r.hi.init()}),[]),null}},31066:function(e,t,n){"use strict";var i=n(52322),r=n(2784),o=n(5632),a=n(97729),l=n(69186),s=n(48243),c=n(75127),u=n(18140),m=n(18977),d=n(99036),_=n.n(d);t.Z=function(e){var t=e.html,n=void 0===t?"":t,d=e.js,f=void 0===d?"":d,p=e.css,h=e.communityPage,g=void 0!==h&&h,C=(0,r.useContext)(s.oF),y=(0,o.useRouter)();return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(a.default,{children:[(0,i.jsx)("script",{dangerouslySetInnerHTML:{__html:"\n  window.BZFD = window.BZFD || {};\n  window.BZFD.Config = {\n    bfwInfoCookie: 'bf2-b_info'\n  };\n"}},"window-globals"),(0,i.jsx)("script",{dangerouslySetInnerHTML:{__html:(0,u.I)({badges:[],cms_tags:[],dfp_keyword:_()(y.query["dfp-keyword"])||null,id:"home",section:[],zone3:"",isTasty:!1,pagetype:"A",poe:(0,m.kH)({env:{isBPage:!1}}),getPageSessionId:(0,m.HU)({gdpr:c.Z})})}},"ads-context")]}),g?(0,i.jsx)(l.Z,{html:n,js:f,css:p}):(0,i.jsx)(l.Z,{html:n,js:f,css:p,stickyRegistry:C})]})}},61360:function(e,t,n){"use strict";n.d(t,{zx:function(){return l}});var i=n(75951),r=n(84952);function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function a(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return o(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var l=[{name:"RT-1710-adshield-experiment",variations:["control","adshield"],isEligible:function(){return!["gb","ie","uk","nz","au","jp"].includes((0,r.pP)().toLowerCase())}}],s=[i.Yx].concat(a([{name:"TimeSpentRO_1",variations:["on","control"],isEligible:function(){return!0}},{name:"RT-1559-AdShield-script-on-BFDC",variations:["control","on"],isEligible:function(){return!0}},{name:"BF-14546-member-shopping-control",variations:["control","on"],isEligible:function(){return!0}}]),a(l));t.ZP=s},74104:function(e,t,n){"use strict";n.d(t,{T:function(){return o}});var i=n(2784),r=n(48243);function o(){var e=(0,i.useContext)(r.z1),t=e.adsDisabled,n=e.isSponsored,o=e.membershipAvailable,a=function(e){var i;"topic-nav-loaded"!==(null===e||void 0===e||null===(i=e.data)||void 0===i?void 0:i.type)||t||n||!o||window.postMessage({type:"show-membership-promo-button"},"*")};(0,i.useEffect)((function(){return window.addEventListener("message",a),function(){window.removeEventListener("message",a)}}),[])}},2945:function(e){e.exports={wrapper:"header_wrapper__qOZFd",background:"header_background__7JmPL",titleWrapper:"header_titleWrapper__JnCQn",title:"header_title__UZV3V",desc:"header_desc__V8MKC",navbar:"header_navbar__l3WJz",nav:"header_nav__fkdMP",activeTab:"header_activeTab__lTKPk"}},72772:function(e){e.exports={title:"sectionTitle_title__LwhsM",titleSmall:"sectionTitle_titleSmall__HCdAk"}},73663:function(e){e.exports={socialIcons:"socialButtons_socialIcons__HQwpA",socialIconLink:"socialButtons_socialIconLink__rUlGl",twitterPurple:"socialButtons_twitterPurple__pLJY_",twitterWhite:"socialButtons_twitterWhite__fXeEO"}},42377:function(e){e.exports={mainContainer:"community_mainContainer__uTz2T",pageContainer:"community_pageContainer__YW5JJ",pageTitle:"community_pageTitle__iS98n",guidelinesFlexWrapper:"community_guidelinesFlexWrapper__fSN_m",guidelinesLeftContent:"community_guidelinesLeftContent__KV7aI",content:"community_content__XZNbe",title:"community_title__A_Yd1",guidelinesRightContent:"community_guidelinesRightContent__d5BIi",rightColumnSocialButtons:"community_rightColumnSocialButtons__X_mA9",leaderboardFlexWrapper:"community_leaderboardFlexWrapper__DYvYb",leaderboardLeftCol:"community_leaderboardLeftCol__o0iHn",leftColumnTopContributors:"community_leftColumnTopContributors__HoSIr",rightColumnTopContributors:"community_rightColumnTopContributors__veTkq",rightColumnTopPosts:"community_rightColumnTopPosts__x_WbU",leaderboardRightCol:"community_leaderboardRightCol__IWQ_H",loginContainer:"community_loginContainer__BB2_4",loginWrapper:"community_loginWrapper__k_4bQ",hidden:"community_hidden__riBsD",desc:"community_desc__sRuJs",loginLeftCol:"community_loginLeftCol__ytwUL",loginLeftColFullWidth:"community_loginLeftColFullWidth__ioxeG",loginRightCol:"community_loginRightCol__er4ac",wrapperChallengesSection:"community_wrapperChallengesSection__8RNWc",arrowsGraphicYellowBig:"community_arrowsGraphicYellowBig__sPyU3",arrowsGraphicYellowSmall:"community_arrowsGraphicYellowSmall__EUL61",triangleGraphicPurple:"community_triangleGraphicPurple__jqaFO",createChallengeContainer:"community_createChallengeContainer__syk3K",stepsWrapper:"community_stepsWrapper__6c_RK",sectionTitle:"community_sectionTitle__7EB1F",stepsDesc:"community_stepsDesc__siDK_",guidelinesWrapper:"community_guidelinesWrapper__xjNXS",guidelinesLeftCol:"community_guidelinesLeftCol__z11aW"}},65267:function(e){e.exports="/static-assets/feed-ui/_next/static/images/bf-pattern-waves-silveryellow-e6a4844b128e589e25f3c36cce452b83.png"}}]);
//# sourceMappingURL=38-edd0c35f84e6896d.js.map