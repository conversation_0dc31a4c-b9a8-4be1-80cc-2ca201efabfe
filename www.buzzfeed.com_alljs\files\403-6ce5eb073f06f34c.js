(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[403],{23879:function(e,t,r){"use strict";r.d(t,{zk:function(){return G},rG:function(){return K},G3:function(){return H},id:function(){return $}});var n=r(94776),s=r.n(n),i=r(52322),o=r(2784),c=r(13980),a=r.n(c),l=r(17437),u=r.n(l),C=/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/i,p=r(77749),d=r(41332),f=r(30353),m=r(33402);function h(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"===typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){h(e,t,r[t])}))}return e}var x=function(e){var t=e.error;return function(e){return _({},e,{submitting:!1,error:t})}},g=function(e){return function(t){return _({},t,{token:e})}},b=r(5103),w=r(26528),v=r.n(w);function j(e,t,r,n,s,i,o){try{var c=e[i](o),a=c.value}catch(l){return void r(l)}c.done?t(a):Promise.resolve(a).then(n,s)}function k(e){return function(){var t=this,r=arguments;return new Promise((function(n,s){var i=e.apply(t,r);function o(e){j(i,n,s,o,c,"next",e)}function c(e){j(i,n,s,o,c,"throw",e)}o(void 0)}))}}function y(e,t,r){return L.apply(this,arguments)}function L(){return(L=k(s().mark((function e(t,r,n){var i,o,c,a,l;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=n.hideResponseBody,o=void 0!==i&&i,c=n.headers,a=void 0===c?{}:c,e.prev=1,e.next=4,v()("".concat(t),{method:"POST",body:JSON.stringify(r),headers:a});case 4:if((l=e.sent).ok){e.next=7;break}return e.abrupt("return",{error:l});case 7:if(!o){e.next=11;break}e.t0={ok:"ok"},e.next=14;break;case 11:return e.next=13,l.json();case 13:e.t0=e.sent;case 14:return e.t1=e.t0,e.abrupt("return",{response:e.t1});case 18:return e.prev=18,e.t2=e.catch(1),e.abrupt("return",{error:e.t2});case 21:case"end":return e.stop()}}),e,null,[[1,18]])})))).apply(this,arguments)}var N=r(92523),B=r(16938),O=r(81298),Z=r(65831),z=r(99112),I=r(62336),T=r.n(I);var W=function(e){return(0,i.jsxs)("div",{className:"xs-text-6 ".concat(T().privacyPolicyBox),children:["This site is protected by reCAPTCHA and the Google ",(0,i.jsx)("a",{href:"https://policies.google.com/privacy",className:e.promotion&&"".concat(T().culturePopup),children:"Privacy Policy"})," and ",(0,i.jsx)("a",{href:"https://policies.google.com/terms",className:e.promotion&&"".concat(T().culturePopup),children:"Terms of Service"})," apply."]})};function M(e,t,r,n,s,i,o){try{var c=e[i](o),a=c.value}catch(l){return void r(l)}c.done?t(a):Promise.resolve(a).then(n,s)}function P(e){return function(){var t=this,r=arguments;return new Promise((function(n,s){var i=e.apply(t,r);function o(e){M(i,n,s,o,c,"next",e)}function c(e){M(i,n,s,o,c,"throw",e)}o(void 0)}))}}var S=0;function E(e){var t=e.email,r=t.value;return function(e){return C.test(e)}(r)?{email:r}:{error:{control:t,message:"newsletter_email_error"}}}function R(){return(0,i.jsx)("div",{className:u().spinner,children:(0,i.jsx)("span",{children:"Loading"})})}function A(e){var t=e.for,r=e.children,n=t?"".concat(t.getAttribute("id"),"-error"):"newsletter-error";return(0,i.jsx)("p",{id:n,className:u().error,role:"alert",children:r})}function D(e){var t=e.for,r=e.children,n=t?"".concat(t.getAttribute("id"),"-success"):"newsletter-success";return(0,i.jsx)("p",{id:n,className:u().success,role:"alert",children:r})}function F(e){var t=e.category,r=e.newsletterId,n=e.className,c=e.buttonClassName,a=void 0===c?"":c,l=e.submitBoxClassName,C=void 0===l?"":l,h=e.promotion,w=void 0===h?"":h,v=e.onSubmitSuccess,j=e.onSubmitError,k=(0,o.useReducer)((function(e,t){return t(e)}),{}),L=k[0],I=k[1],T=(0,o.useRef)(null),M=(0,b.useTranslation)("common").t,F=(0,o.useRef)(),G=(0,o.useContext)(z.Z),V=G.base_url,J=G.destination,K=(0,o.useContext)(d.Z).buzz,U=void 0===K?{}:K,q=(0,B.Z)("(max-width:500px)"),H=M("newsletter_placeholder"),Y=M("newsletter_success_message"),Q=M("server_error");(0,o.useEffect)((function(){L.submitting&&function(){var e=P(s().mark((function e(){var t,n,i,o,c,a,l;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=L.formData,n=t.token,i=t.email,o={brand:"buzzfeed",subscriptions:[r],source:q?"buzzfeed-mobileweb-hub":"buzzfeed_desktop_hub","g-recaptcha-response":n,email:i},e.next=4,y("".concat(V,"/newsletters/api/subhub/v1/users"),o,{hideResponseBody:!0,headers:{"Content-Type":"application/json"}});case 4:if(c=e.sent,a=c.response,!(l=c.error)&&a.ok){e.next=10;break}return console.error({error:l}),e.abrupt("return",I(x({error:{message:Q}})));case 10:return e.abrupt("return",I((function(e){return _({},e,{submitting:!1,error:null,success:!0})})));case 11:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()()}),[V,q,r,L.formData,L.submitting,M]),(0,o.useEffect)((function(){L.success?(v(L.formData.email),(0,Z.P)(L.formData.email),N.Z.sessionSet({key:"newsletterAddressable",value:"true"}),(0,m.Ey)(J,U,{category:t})):L.error&&"function"===typeof j&&j()}),[v,j,L.success,L.error,L.formData,J,t]),(0,o.useEffect)((function(){L.focused&&function(){var e=P(s().mark((function e(){var t;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,F.current.executeAsync();case 2:t=e.sent,F.current.reset(),I(g(t));case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()()}),[L.focused]);var $=function(e){var t=e.formElement,r="newsletter-email-signup",n=t.current?t.current.querySelector('input[id^="'.concat(r,'"]')):null;return n?n.id:(S++,"".concat(r,"-").concat(S))}({formElement:T});return(0,i.jsxs)("form",{className:"".concat(n||""," ").concat(u().form),ref:T,children:[(0,i.jsx)(p.Z,{ref:F,size:"invisible",sitekey:f.site_captcha_key,style:{visibility:"hidden"}}),L.submitting&&(0,i.jsx)(R,{}),(0,i.jsxs)("fieldset",{className:"".concat(u().fieldset," ").concat(L.submitting?u().loading:""),children:[(0,i.jsx)("legend",{className:u().legend,children:"Newsletter signup form"}),(0,i.jsxs)("div",{className:"".concat(u().submitBox," ").concat(C),children:[(0,i.jsx)("input",{className:u().input,onFocus:function(){I((function(e){return _({},e,{focused:!0})}))},id:$,type:"email",name:"email",autoComplete:"email",placeholder:H,"aria-label":H,"aria-describedby":L.error?"".concat($,"-error"):L.success?"newsletter-success":null,size:15,required:!0}),(0,i.jsx)("label",{className:u().label,htmlFor:$,children:H}),(0,i.jsx)("button",{className:"".concat(u().button," ").concat(a),type:"submit",onClick:function(e){if(e.preventDefault(),!L.submitting){L.success=!1;var t,r=E(T.current.elements),n=r.email,s=r.error;if(s){var i=s.control,o=s.message;return I(x({error:{message:M(o),formControl:i}})),void i.focus()}L.focused=!1,I((t={token:L.token,email:n},function(e){return _({},e,{formData:t,submitting:!0})}))}},disabled:L.submitting,children:L.success?(0,i.jsx)(O.KM,{className:u().checkmarkIcon,"aria-hidden":!0,title:"submit"}):M("sign_up")})]}),L.error&&(0,i.jsx)(A,{for:L.error.formControl,children:L.error.message}),L.success&&(0,i.jsx)(D,{children:Y})]}),(0,i.jsx)(W,{promotion:w})]})}A.propTypes={children:a().oneOfType([a().arrayOf(a().node),a().node]),for:a().object},D.propTypes={children:a().oneOfType([a().arrayOf(a().node),a().node]),for:a().object},F.propTypes={className:a().string,buttonClassName:a().string,submitBoxClassName:a().string,promotion:a().string,children:a().oneOfType([a().arrayOf(a().node),a().node]),onSubmitSuccess:a().func};var G=F,V=r(24760),J=function(e){var t=e.className,r=e.children;return(0,i.jsx)("div",{className:"".concat(t," ").concat(V.container),children:r})};J.propTypes={children:a().oneOfType([a().arrayOf(a().node),a().node]),className:a().string};var K=J,U=r(23471);function q(e){var t=e.styles,r=void 0===t?{}:t,n=e.children,s=(0,o.useRef)(null),c=(0,b.useTranslation)("common").t;return(0,o.useEffect)((function(){s.current&&s.current.focus()}),[]),(0,i.jsxs)("div",{role:"alert",ref:s,tabIndex:"-1",children:[(0,i.jsx)("p",{className:(U.title,r.title),children:c("newsletter_success_title")}),(0,i.jsx)("p",{className:(U.text,r.text),children:n})]})}q.propTypes={children:a().oneOfType([a().arrayOf(a().node),a().node])};var H=q,Y=r(26232);function Q(e){var t=e.children;return(0,i.jsx)("h2",{className:Y.title,children:t})}Q.propTypes={children:a().oneOfType([a().arrayOf(a().node),a().node])};var $=Q},12403:function(e,t,r){"use strict";r.d(t,{Z:function(){return _}});var n=r(52322),s=r(2784),i=r(23879),o=r(59910),c=r.n(o),a=r(468),l=r.n(a),u=function(e){var t=e.className,r=e.title;return(0,n.jsxs)("svg",{className:t,width:"112",height:"123",viewBox:"0 0 112 123",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("title",{children:r}),(0,n.jsxs)("g",{children:[(0,n.jsx)("path",{d:"M57.8202 3.7002L68.0802 52.9602L111.95 63.2202L68.0802 73.4702L57.8202 122.74L47.5702 73.4702L3.7002 63.2202L47.5702 52.9602L57.8202 3.7002Z",fill:"black"}),(0,n.jsx)("path",{d:"M54.59 0.459961L64.84 49.73L108.72 59.98L64.84 70.24L54.59 119.5L44.33 70.24L0.459961 59.98L44.33 49.73L54.59 0.459961Z",fill:"white",stroke:"black",strokeWidth:"0.93",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,n.jsx)("defs",{children:(0,n.jsx)("clipPath",{id:"clip0",children:(0,n.jsx)("rect",{width:"111.95",height:"122.74",fill:"white"})})})]})},C=function(e){var t=e.className,r=e.title;return(0,n.jsxs)("svg",{className:t,width:"152",height:"132",viewBox:"0 0 152 132",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("title",{children:r}),(0,n.jsxs)("g",{children:[(0,n.jsx)("path",{d:"M31.3705 63.2166C30.5701 64.7999 28.8889 68.7908 27.0415 69.1628C25.5313 69.4594 23.6487 68.9718 23.3165 67.303C22.1486 61.4071 30.3285 45.3377 33.223 39.6176L37.6527 30.8013C27.4291 33.033 17.5628 36.4259 8.39127 41.4221V41.5377C9.26211 41.5377 13.2841 41.1808 13.5207 42.3721C13.7573 43.5633 11.185 43.0858 8.80907 43.6237L5.36093 44.3776C3.99174 44.649 2.67792 44.9104 2.34065 43.302C2.20369 42.121 2.15154 40.9317 2.1846 39.7433C2.1242 35.7222 1.7668 34.5611 5.29549 32.0579C11.5122 27.5995 19.4555 25.1315 26.8552 23.6689C29.8631 23.0006 32.9353 22.6635 36.0167 22.6636C39.8827 22.7591 39.9531 24.6741 42.3291 28.464C43.1496 29.786 41.6848 32.3696 41.1109 33.8473L31.3453 57.8534L31.4863 57.9489C32.035 57.592 32.8454 57.6121 33.0266 58.5068C33.2632 59.678 31.9041 62.1811 31.3705 63.2166ZM150.918 33.2492C148.462 36.4661 142.678 44.7496 138.681 45.5488C134.684 46.348 131.095 42.357 128.674 39.8639C127.668 41.3003 126.884 42.8795 126.348 44.5485C126.046 45.5538 125.689 46.5591 124.556 46.7652C122.84 47.107 121.385 43.3221 120.766 41.1758C118.158 45.0612 114.725 49.967 111.498 50.6154C109.228 51.0678 107.773 49.3739 107.371 47.3482C106.651 43.7744 109.087 39.2004 110.446 36.079L110.3 35.9835C107.361 39.2838 104.885 42.9685 102.941 46.9361C102.276 48.2429 100.651 51.6056 99.0448 51.9273C96.6286 52.4299 95.2443 49.3839 94.6755 47.3281C91.8263 51.1029 87.2708 56.0137 85.0005 56.4661C83.0323 56.8582 81.8544 55.6116 81.5071 53.8825C81.1245 51.9775 81.9249 48.5345 82.3779 46.7702L82.2319 46.6747L77.631 52.2037C76.6746 53.3246 73.4782 57.1849 72.0939 57.4563C70.7096 57.7277 67.8454 55.7473 66.3101 53.3899C63.4811 56.5566 59.5597 60.6481 57.1385 61.1306C55.3515 61.4875 54.083 60.1253 53.7608 58.5219C53.0108 54.7672 56.2173 49.4191 57.7878 46.1922L57.6469 46.0966C48.8277 55.0788 47.1263 61.9901 45.2185 62.3671C43.1295 62.7843 41.9012 58.2002 41.6244 56.8381C40.8038 52.7315 41.7804 49.5598 43.1848 45.7498C47.8965 32.7264 57.6771 19.9342 59.7057 19.5271C60.6571 19.3361 62.3535 19.4316 62.5951 20.6229C63.4257 24.6641 48.3747 48.1072 45.3041 52.6259L45.7672 54.9481C48.2236 52.0429 57.1687 40.3414 60.4507 39.688C61.201 39.6003 61.958 39.7816 62.5868 40.1996C63.2156 40.6176 63.6753 41.245 63.8838 41.97C64.1153 43.1059 62.2528 46.2022 61.7041 47.1773C58.9003 52.3847 59.1168 52.5254 59.2376 53.1236C59.2675 53.2952 59.3327 53.4587 59.429 53.604C59.5254 53.7492 59.6508 53.8729 59.7974 53.9674C59.944 54.0618 60.1086 54.1249 60.2808 54.1527C60.453 54.1805 60.6291 54.1724 60.7981 54.1288C61.9659 53.8926 63.7428 52.3646 65.3285 50.8466C64.4727 43.4879 77.8727 33.3045 84.5676 31.7865C87.2959 31.1783 88.8715 33.1587 89.0175 33.8724C89.1635 34.5862 87.7288 35.4909 87.2254 35.7775C88.4537 36.0288 89.1836 36.5616 89.4454 37.8735C89.8279 39.7785 85.4737 44.9858 86.0375 47.7855C86.2388 48.7908 87.2154 49.0371 88.1718 48.8461C90.1853 48.4389 92.4102 45.9308 94.3583 43.8197C94.6755 39.9393 96.7141 35.2145 98.9542 32.3997C99.5985 31.5905 100.122 31.4246 101.134 31.2235C104.738 30.6254 104.607 31.5201 104.657 31.877C105.126 34.1992 98.8132 41.7187 99.1203 43.1461C99.1623 43.3152 99.2677 43.4617 99.4148 43.5552C99.5619 43.6488 99.7394 43.6823 99.9106 43.6488C100.328 43.5684 100.756 42.925 100.917 42.5782C104.471 35.5412 113.98 26.0313 115.712 25.6844C115.948 25.6342 116.366 25.8604 116.89 26.6244C117.522 27.5643 118.04 28.5768 118.43 29.6402C118.898 31.9624 112.465 37.9589 113.29 42.0655C113.356 42.3454 113.525 42.5901 113.764 42.7498C114.004 42.9094 114.295 42.9721 114.579 42.925C116.009 42.6385 118.218 40.0298 119.109 38.9039L120.308 37.4512C120.509 34.6013 121.792 31.8669 122.86 29.1326C127.274 17.7226 139.295 3.42253 141.953 3.08073C142.356 3.01934 142.767 3.11428 143.102 3.34608C143.437 3.57788 143.671 3.92897 143.755 4.32728C144.575 8.43887 127.234 32.4098 124.44 37.0542L124.647 38.6877C127.284 35.124 137.734 21.6985 141.797 20.8893C143.81 20.4821 145.411 22.8295 145.758 24.5585C146.871 30.0876 139.426 34.742 135.907 37.9237C137.352 39.2457 138.198 40.07 140.312 39.7684C141.454 39.6025 143.614 38.3057 149.831 30.927L150.918 33.2492ZM83.9686 38.0846L83.8276 37.9891C70.5032 45.1768 69.5015 49.1527 70.0854 52.063C70.2062 52.3495 70.6592 53.3749 71.515 53.018C72.6526 52.5505 74.5353 52.3595 83.9585 38.0846H83.9686ZM133.899 36.5314L138.726 31.5402C139.491 30.7058 141.208 29.0622 140.956 27.8106C140.886 27.4537 140.382 27.4286 140.085 27.4889C137.88 27.9312 132.449 33.3497 131.382 35.687C132.269 35.7993 133.124 36.0863 133.899 36.5314ZM121.863 90.9774L122.734 93.4906C120.277 97.009 115.329 105.132 110.914 106.011C108.705 106.454 107.023 104.87 106.606 102.784C106.248 101 106.57 99.2005 106.857 97.5318L106.711 97.4363C104.793 99.9243 101.869 106.087 98.5917 106.74C95.6872 107.323 93.422 104.353 92.8885 101.799C91.2978 103.533 89.3195 104.569 86.4804 105.137C84.4669 108.198 81.95 110.666 78.1898 111.44C76.0504 111.867 73.7248 110.791 72.0536 108.318C71.4649 109.182 70.7903 109.983 70.0401 110.711C66.7933 114.335 60.7125 121.744 58.4624 125.79C57.4959 127.469 55.8599 130.46 53.8313 130.862C52.1651 131.199 50.9469 130.078 50.6248 128.469C49.4318 122.513 60.0581 99.6982 63.2394 93.6112L63.0985 93.5157L50.0106 110.314C48.5307 112.219 45.3443 116.451 42.9029 116.939C40.9297 117.331 38.0302 115.868 37.9849 113.772C37.9598 112.415 36.9329 110.696 45.3292 95.3302C49.1851 88.243 51.3396 84.4631 51.0526 83.0356C51.0526 82.6084 50.5191 82.533 50.1466 82.533C41.9666 88.8763 37.557 92.8572 32.7196 96.7979C32.22 97.288 31.6185 97.6621 30.9577 97.8937C30.4191 98.0043 30.0366 97.954 29.8352 97.5619C29.6339 97.1699 29.5936 96.3707 29.7144 95.1091L29.9661 91.7112C30.1674 89.3186 31.0735 88.2681 32.79 86.6245C37.1342 82.6034 48.0978 74.149 53.7608 73.018C55.8498 72.6008 58.115 73.0784 58.5933 75.4609C59.1319 78.1399 55.6837 84.4682 54.3951 87.0216C47.3629 100.93 45.6463 103.191 45.8829 105.006C45.9786 105.79 47.3931 106.313 49.024 104.252C56.0713 95.5313 51.0375 102.669 66.3755 81.8896C66.9141 81.1658 68.2883 79.0296 69.2397 78.8436C70.7499 78.542 72.5066 80.6029 72.7634 81.8595C73.1006 83.5282 70.9764 86.5541 64.8352 100.482C61.3417 108.424 58.6084 114.3 60.2443 114.099C61.09 113.988 61.1152 113.179 67.3772 108.213C68.4365 107.326 69.6139 106.59 70.8757 106.026C70.6398 105.418 70.4431 104.795 70.2868 104.162C69.28 99.7183 70.8807 96.3003 73.7097 93.0683C76.3801 89.8686 79.9613 87.5547 83.9787 86.4335C85.2774 86.1118 87.3664 86.3129 87.6886 87.9414C87.8698 88.8361 87.2254 89.3337 86.6415 89.8213C88.5242 89.9419 89.1333 91.1231 89.4907 92.9075C89.994 95.5313 89.239 97.8535 88.5745 100.402C90.4924 100.367 91.6653 99.0648 92.7374 97.6876C93.2408 93.0432 95.7577 88.3938 98.3149 85.8806C99.0091 85.2331 99.8895 84.8199 100.832 84.6994C101.667 84.5335 104.602 83.3875 104.718 85.202C104.793 86.1771 103.515 88.1676 102.966 88.8361C101.537 91.289 101.285 91.8972 99.2914 94.0284C97.7058 95.7122 96.538 97.3056 97.0363 99.8087C97.0967 100.105 97.2779 100.688 97.6957 100.608C98.7679 100.392 101.723 98.0093 104.003 94.9482L108.851 88.4642C109.857 87.0819 114.584 80.2561 116.074 79.9595C116.577 79.864 117.247 79.9092 118.088 82.9753C118.495 84.3827 117.161 86.0716 116.371 87.097C114.811 89.2081 111.519 94.7572 112.057 97.4363C112.248 98.3913 112.852 98.6426 113.809 98.4416C116.069 97.9791 120.358 92.7818 121.863 90.9774ZM82.2722 103.599C81.6611 102.837 81.2277 101.95 81.0037 101C80.3845 97.9037 82.4383 93.5911 82.1715 93.5207C80.3845 95.1141 77.8676 97.1045 76.4229 99.1905C75.3759 100.764 74.7064 103.312 75.0789 105.157C75.4212 106.886 76.6646 107.198 78.1546 106.896C80.1228 106.524 81.1497 105.142 82.2722 103.619V103.599ZM128.17 91.2035C126.026 91.6308 125.754 97.0744 126.157 99.1603C126.464 100.708 128.397 101.674 129.947 101.372C132.691 100.824 133.038 96.3455 132.62 94.2747C132.426 93.2858 131.851 92.4124 131.019 91.842C130.187 91.2716 129.164 91.0496 128.17 91.2236V91.2035ZM137.281 57.1045C136.413 57.0297 135.539 57.0179 134.669 57.0693C133.119 57.3809 132.952 59.3362 132.655 60.633L128.095 81.4925C127.808 82.8497 127.088 85.6645 127.34 87.0216C127.591 88.3787 130.194 89.7962 131.568 89.5348C132.761 89.2935 133.224 87.5896 133.008 86.5189C132.921 86.1643 132.748 85.8363 132.504 85.5639L139.018 62.6435C139.305 61.5981 140.09 59.0245 139.889 58.0092C139.688 56.9939 137.926 57.1849 137.281 57.1246V57.1045Z",fill:"black",stroke:"black",strokeWidth:"1.49611",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M31.2093 55.793C31.0331 54.8983 30.2026 54.8782 29.6992 55.2351L29.5532 55.1396L39.3137 31.1637C39.8876 29.6859 41.3574 27.1023 40.5319 25.7804C38.156 21.9654 38.0855 20.0603 34.2296 19.9648C31.1482 19.9647 28.076 20.3018 25.0681 20.9701C17.6634 22.468 9.71504 24.9259 3.52348 29.3793C-0.000176281 31.8925 0.352189 33.0486 0.412595 37.0646C0.377914 38.253 0.42838 39.4422 0.563608 40.6233C0.905906 42.2217 2.21469 41.9704 3.58892 41.7191L7.04713 40.945C9.41805 40.4122 12.0054 40.945 11.7588 39.6935C11.5121 38.4419 7.50017 38.8742 6.62933 38.8641L6.60416 38.7435C15.7657 33.7523 25.6319 30.3544 35.8555 28.1478L31.4207 36.939C28.5313 42.659 20.3464 58.7284 21.5193 64.6244C21.8565 66.2932 23.7543 66.7807 25.2443 66.4842C27.0917 66.1122 28.7679 62.1212 29.5733 60.5379C30.112 59.5025 31.4711 56.9993 31.2093 55.793Z",fill:"#B5B3E5",stroke:"black",strokeWidth:"1.33954",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M148.039 28.2483C141.822 35.627 139.668 36.9238 138.52 37.0897C136.411 37.3913 135.56 36.5871 134.12 35.245C137.644 32.0633 145.079 27.4189 143.972 21.8798C143.624 20.1507 142.039 17.8034 140.01 18.2106C135.953 19.0198 125.493 32.4453 122.855 36.009L122.653 34.3755C125.442 29.7311 142.789 5.76017 141.963 1.64857C141.879 1.25026 141.645 0.899174 141.31 0.667371C140.975 0.435569 140.564 0.340625 140.161 0.402024C137.503 0.743819 125.477 15.0439 121.063 26.4538C119.996 29.1882 118.712 31.9226 118.516 34.7725L117.323 36.2503C116.427 37.3561 114.222 39.9648 112.792 40.2714C112.508 40.3197 112.216 40.2576 111.977 40.0977C111.737 39.9379 111.568 39.6925 111.504 39.4119C110.678 35.3053 117.106 29.3139 116.638 26.9866C116.247 25.9244 115.732 24.9121 115.103 23.9708C114.599 23.2068 114.161 22.9655 113.92 23.0309C112.188 23.3777 102.684 32.8977 99.1305 39.9246C98.9493 40.2714 98.5214 40.9299 98.1238 40.9952C97.9531 41.0299 97.7757 40.9968 97.6291 40.9029C97.4824 40.8091 97.3782 40.6619 97.3385 40.4926C97.0516 39.0651 103.379 31.5456 102.876 29.2234C102.8 28.8665 102.931 27.9718 99.352 28.57C98.3453 28.771 97.8117 28.9369 97.1674 29.7461C94.9273 32.5609 92.8886 37.2857 92.5715 41.1661C90.6083 43.2269 88.3935 45.7401 86.3648 46.1523C85.4135 46.3433 84.4319 46.1071 84.2305 45.0917C83.6718 42.292 88.021 37.0847 87.6384 35.1797C87.3766 33.8728 86.6316 33.335 85.4235 33.0837C85.9269 32.7972 87.3515 31.8924 87.2105 31.1787C87.0696 30.4649 85.494 28.4845 82.7607 29.0927C76.0657 30.6006 62.6708 40.7942 63.5265 48.1528C61.956 49.6607 60.1791 51.1988 58.9961 51.4351C58.8269 51.4794 58.6502 51.4881 58.4774 51.4607C58.3046 51.4333 58.1394 51.3703 57.9922 51.2758C57.845 51.1813 57.7191 51.0573 57.6224 50.9117C57.5258 50.766 57.4605 50.6019 57.4306 50.4298C57.3098 49.8316 57.0984 49.6909 59.8972 44.4835C60.4459 43.5084 62.3084 40.4122 62.0818 39.2812C61.8733 38.5549 61.4131 37.9262 60.7834 37.5073C60.1537 37.0884 59.3953 36.9066 58.6438 36.9942C55.3718 37.6627 46.4419 49.3642 43.9854 52.2745L43.5172 49.9523C46.5828 45.4285 61.6087 21.9904 60.7982 17.9391C60.5616 16.7479 58.8653 16.6524 57.9088 16.8434C55.8953 17.2505 46.1046 30.0477 41.393 43.0661C39.9835 46.8811 39.007 50.0478 39.8325 54.1594C40.1043 55.5265 41.3426 60.1156 43.4216 59.6884C45.3294 59.3114 47.0459 52.4001 55.85 43.3928L55.996 43.4883C54.4103 46.7153 51.1988 52.0634 51.969 55.8181C52.2911 57.4265 53.5546 58.7836 55.3416 58.4268C57.7629 57.9241 61.6892 53.8527 64.5132 50.6861C66.0485 53.0435 68.9781 55.0189 70.302 54.7575C71.6259 54.4961 74.8677 50.6208 75.8392 49.4999L80.4451 43.9708L80.5861 44.0663C80.133 45.8306 79.3326 49.2737 79.7152 51.1787C80.0625 52.9078 81.2253 54.1594 83.2137 53.7623C85.4839 53.3099 90.0345 48.4041 92.8836 44.6243C93.4524 46.6801 94.8367 49.706 97.2529 49.2234C98.8637 48.9017 100.485 45.5391 101.154 44.2322C103.094 40.2626 105.57 36.5774 108.514 33.2797L108.654 33.3752C107.295 36.4966 104.864 41.0756 105.579 44.6494C105.987 46.66 107.441 48.3639 109.707 47.9115C112.948 47.2631 116.371 42.3574 118.979 38.477C119.598 40.6182 121.048 44.4031 122.769 44.0613C123.902 43.8351 124.279 42.8349 124.556 41.8447C125.096 40.1893 125.878 38.6226 126.877 37.1953C129.298 39.7085 132.948 43.6592 136.889 42.8751C140.83 42.091 146.67 33.7974 149.126 30.5805L148.039 28.2483ZM69.7131 50.3644C68.8523 50.7213 68.3993 49.6959 68.2784 49.4094C67.6945 46.4941 68.6962 42.5132 82.0207 35.3355L82.1667 35.431C72.7434 49.6808 70.8557 49.8718 69.7131 50.3493V50.3644ZM129.575 32.9932C130.657 30.671 136.084 25.2525 138.293 24.8102C138.59 24.7499 139.089 24.775 139.164 25.1319C139.411 26.3835 137.694 28.0271 136.934 28.8615L132.102 33.8527C131.325 33.4006 130.466 33.1083 129.575 32.9932Z",fill:"#B5B3E5",stroke:"black",strokeWidth:"1.33954",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M112.002 95.7524C111.05 95.9434 110.441 95.6921 110.25 94.7471C109.711 92.063 113.003 86.5139 114.569 84.4028C115.354 83.3975 116.688 81.6885 116.285 80.2811C115.425 77.2351 114.735 77.1849 114.272 77.2653C112.762 77.5619 108.07 84.3877 107.043 85.77L102.186 92.2641C99.8853 95.3251 96.9506 97.7077 95.8734 97.9238C95.4556 98.0042 95.2794 97.4212 95.219 97.1246C94.7156 94.6114 95.8885 93.0281 97.4741 91.3442C99.4876 89.213 99.7141 88.6048 101.144 86.152C101.692 85.4835 102.971 83.493 102.901 82.5179C102.78 80.6832 99.845 81.8293 99.0094 82.0152C98.0754 82.1385 97.2038 82.5517 96.5177 83.1965C93.4476 86.4372 91.4936 90.5718 90.9402 94.9984C89.863 96.3757 88.6901 97.6775 86.7723 97.7127C87.4418 95.1643 88.217 92.8421 87.6884 90.2183C87.331 88.434 86.7219 87.2528 84.8393 87.1321C85.4232 86.6295 86.0675 86.1268 85.8914 85.2522C85.5642 83.6438 83.4802 83.4427 82.1764 83.7443C78.1607 84.8691 74.5805 87.1823 71.9075 90.3792C69.0785 93.6111 67.4727 97.0291 68.4845 101.472C68.6409 102.108 68.8376 102.732 69.0735 103.342C67.8118 103.904 66.6343 104.638 65.575 105.524C59.3129 110.49 59.2928 111.299 58.4421 111.41C56.8111 111.611 59.5395 105.735 63.0329 97.7931C69.1741 83.865 71.2984 80.8391 70.9662 79.1954C70.7145 77.9439 68.9526 75.883 67.4425 76.1796C66.4861 76.3706 65.1169 78.5018 64.5783 79.2256C49.2605 100.005 54.2842 92.8672 47.2218 101.588C45.5908 103.649 44.2015 103.126 44.0857 102.342C43.8441 100.532 45.5657 98.2656 52.5777 84.3525C53.8664 81.8041 57.3095 75.4759 56.7759 72.7918C56.2725 70.4093 54.0275 69.9318 51.9435 70.349C46.2805 71.4749 35.3169 79.9192 30.9777 83.9404C29.2612 85.584 28.3501 86.6345 28.1487 89.0271L27.9021 92.4249C27.7813 93.6865 27.8165 94.4857 28.0179 94.8778C28.2192 95.2699 28.6018 95.3201 29.1404 95.2095C29.8032 94.9792 30.4067 94.605 30.9073 94.1138C35.7598 90.1731 40.1745 86.1922 48.3947 79.8438C48.7621 79.8438 49.2756 79.9142 49.3007 80.3465C49.5877 81.774 47.4282 85.5538 43.5773 92.641C35.181 108.012 36.2078 109.726 36.2431 111.083C36.2934 113.179 39.1929 114.647 41.1611 114.25C43.6075 113.747 46.7889 109.535 48.2688 107.625L61.3567 90.8265L61.5026 90.922C58.3163 97.009 47.6899 119.824 48.8829 125.78C49.2051 127.389 50.4233 128.509 52.0945 128.173C54.108 127.771 55.7541 124.78 56.7205 123.101C58.9505 119.08 65.0364 111.651 68.2983 108.022C69.0472 107.293 69.7217 106.491 70.3118 105.629C71.978 108.102 74.3036 109.178 76.448 108.751C80.2032 107.997 82.7452 105.509 84.7386 102.448C87.5726 101.88 89.5509 100.849 91.1416 99.1151C91.6752 101.663 93.9404 104.644 96.8449 104.051C100.127 103.398 103.047 97.2352 104.969 94.7471L105.11 94.8426C104.823 96.5114 104.501 98.3108 104.864 100.095C105.276 102.181 106.963 103.764 109.168 103.322C113.582 102.443 118.53 94.3299 120.987 90.8014L120.116 88.3133C118.566 90.1027 114.267 95.3 112.002 95.7524ZM76.3574 104.237C74.8472 104.539 73.629 104.237 73.2817 102.498C72.9092 100.653 73.5787 98.1047 74.6308 96.5315C76.0704 94.4455 78.5873 92.4551 80.3793 90.8617C80.6411 90.9321 78.5873 95.2447 79.2115 98.341C79.4332 99.2905 79.865 100.178 80.475 100.94C79.3525 102.463 78.3256 103.845 76.3574 104.237Z",fill:"#B5B3E5",stroke:"black",strokeWidth:"1.33954",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M126.378 88.5444C124.228 88.9716 123.957 94.4152 124.364 96.5011C124.676 98.0493 126.609 99.0143 128.16 98.7178C130.903 98.1699 131.246 93.6914 130.828 91.6155C130.639 90.6223 130.067 89.7431 129.234 89.1684C128.401 88.5936 127.375 88.3694 126.378 88.5444Z",fill:"#B5B3E5",stroke:"black",strokeWidth:"1.33954",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M135.484 54.4511C134.616 54.3712 133.742 54.3595 132.872 54.4159C131.321 54.7225 131.155 56.6778 130.858 57.9746L126.293 78.8341C126.006 80.1912 125.286 83.006 125.543 84.3632C125.799 85.7203 128.392 87.1377 129.766 86.8764C130.959 86.6351 131.422 84.9311 131.211 83.8605C131.122 83.5081 130.949 83.1823 130.707 82.9105L137.216 59.9901C137.503 58.9396 138.293 56.3661 138.087 55.3558C137.881 54.3455 136.119 54.5063 135.484 54.4511Z",fill:"#B5B3E5",stroke:"black",strokeWidth:"1.33954",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,n.jsx)("defs",{children:(0,n.jsx)("clipPath",{id:"clip0",children:(0,n.jsx)("rect",{width:"151.351",height:"131.345",fill:"white"})})})]})},p=function(e){var t=e.className,r=e.title;return(0,n.jsxs)("svg",{className:t,xmlns:"http://www.w3.org/2000/svg",viewBox:"200 90 320 320",children:[(0,n.jsx)("title",{children:r}),(0,n.jsx)("defs",{children:(0,n.jsx)("style",{children:".d{fill:#676fec}.d,.e,.f{stroke:#222;stroke-miterlimit:10;stroke-width:1.14px}.e{fill:#222}.f{fill:#3a3a3a}.g{fill:#f9f7f7}"})}),(0,n.jsx)("path",{className:"e",d:"M379.36 189.68l-.87 160.25 64.86.35c11.92.06 21.63-9.54 21.69-21.46l.34-62.38c.02-4.36-1.28-8.63-3.73-12.24l-37.24-54.83a21.567 21.567 0 00-17.66-9.45l-27.4-.24z"}),(0,n.jsx)("rect",{className:"e",x:45.28,y:100.63,width:340.26,height:248.42,rx:24.48,ry:24.48,transform:"rotate(.31 215.007 224.906)"}),(0,n.jsx)("circle",{className:"e",cx:388.07,cy:365.54,r:41.45}),(0,n.jsx)("circle",{className:"e",cx:123.17,cy:364.11,r:41.45}),(0,n.jsx)("path",{className:"d",d:"M369.18 182.81l-.87 160.25 64.86.35c11.92.06 21.63-9.54 21.69-21.46l.34-62.38c.02-4.36-1.28-8.63-3.73-12.24l-37.24-54.83a21.567 21.567 0 00-17.66-9.45l-27.4-.24z"}),(0,n.jsx)("rect",{className:"d",x:35.1,y:93.76,width:340.26,height:248.42,rx:24.48,ry:24.48,transform:"rotate(.31 203.897 217.543)"}),(0,n.jsx)("circle",{className:"f",cx:377.89,cy:358.67,r:41.45}),(0,n.jsx)("circle",{className:"f",cx:112.99,cy:357.24,r:41.45}),(0,n.jsxs)("g",{children:[(0,n.jsx)("path",{id:"b",className:"g",d:"M284.74 216.21c-21.64 15.77-52.92 24.08-79.82 23.93-37.71-.2-71.59-14.34-97.15-37.67-2.01-1.84-.19-4.31 2.23-2.88 27.63 16.28 61.85 26.17 97.26 26.36 23.88.13 50.17-4.67 74.38-14.79 3.66-1.53 6.69 2.43 3.1 5.05"}),(0,n.jsx)("path",{id:"c",className:"g",d:"M293.76 206.01c-2.73-3.53-18.2-1.76-25.15-.98-2.12.25-2.43-1.6-.52-2.91 12.36-8.6 32.56-5.99 34.91-3.07 2.34 2.93-.74 23.18-12.37 32.79-1.78 1.48-3.47.68-2.67-1.29 2.63-6.48 8.54-20.99 5.8-24.54"})]})]})},d=r(27912),f=r(4366),m=r(41332),h=r(70448);var _=function(e){var t=e.trackSignupSuccess,r=e.title,o=void 0===r?"Get Our Newsletter":r,a=e.description,_=void 0===a?"Score buzz-worthy products, shopping advice, and deals delivered to your inbox!":a,x=e.promotion,g=void 0!==x&&x,b=(0,s.useContext)(m.Z).buzz,w=void 0===b?{}:b,v=(0,s.useState)(!1),j=v[0],k=v[1],y=(0,s.useState)(!1),L=y[0],N=y[1],B=(0,s.useRef)(),O=(0,s.useCallback)((function(e){k(!0),B.current=e,N(!0),t("shopping")}),[t]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.rG,{className:c().container,children:(0,n.jsx)("div",{className:c().contentWrapper,children:j?(0,n.jsxs)("div",{className:c().success,children:[(0,n.jsx)(u,{className:"".concat(c().star," ").concat(c().star1),title:"star"}),(0,n.jsx)(u,{className:"".concat(c().star," ").concat(c().star2),title:"star"}),(0,n.jsx)(u,{className:"".concat(c().star," ").concat(c().star3),title:"star"}),(0,n.jsx)(u,{className:"".concat(c().star," ").concat(c().star4),title:"star"}),(0,n.jsx)(C,{className:c().thankYouImg,title:"thank-you"}),(0,n.jsx)(i.G3,{styles:l(),children:"Check your inbox to confirm your subscription."})]}):(0,n.jsxs)(n.Fragment,{children:["newsletter-primeday"===g&&(0,n.jsx)("div",{className:c().truckWrapper,children:(0,n.jsx)(p,{id:"primeTruck",className:c().truckImg,title:"truck"})}),(0,n.jsxs)("div",{className:c().titleWrapper,children:["newsletter-primeday"!==g?(0,n.jsx)("div",{className:c().holidayIcon}):"",(0,n.jsxs)("div",{className:c().textWrapper,children:[(0,n.jsx)("h2",{children:(0,n.jsx)("span",{className:c().title,children:o})}),(0,n.jsx)("div",{className:c().description,children:_})]})]}),(0,n.jsx)(i.zk,{category:"shopping",newsletterId:"buzzfeed_email_shopping",className:c().form,onSubmitSuccess:O})]})})}),L&&!(0,h.zk)()&&(0,n.jsx)(d.Z,{email:B.current,topic:"Shopping",onClose:function(){return N(!1)},track:{commonTrackingData:{item_type:"submission",item_name:"email",unit_name:w.id,unit_type:"buzz_bottom"},internalLink:function(e){return(0,f.TW)(w,e)},contentAction:function(e){return(0,f.bC)(w,e)},impression:function(e){return(0,f.Oz)(w,e)}}})]})}},17437:function(e){e.exports={form:"newsletter-form_form__VOsPU",fieldset:"newsletter-form_fieldset__rkJ4a",legend:"newsletter-form_legend__yHwC1",submitBox:"newsletter-form_submitBox__dOsqW",label:"newsletter-form_label__rcnFp",input:"newsletter-form_input__WIIoU",button:"newsletter-form_button__eDnxJ",error:"newsletter-form_error__7hYVV",success:"newsletter-form_success__5A6s1",loading:"newsletter-form_loading__zTpwz",checkmarkIcon:"newsletter-form_checkmarkIcon__hfGT0",spinner:"newsletter-form_spinner__o1l_6",loader1:"newsletter-form_loader1__ghhBD",loader2:"newsletter-form_loader2__8RWbO"}},24760:function(e){e.exports={container:"newsletter-signup-container_container__LuE0z"}},23471:function(e){e.exports={title:"newsletter-success-message_title__hU5P3",text:"newsletter-success-message_text__J_nRn"}},26232:function(e){e.exports={title:"newsletter-title_title__mJure"}},62336:function(e){e.exports={privacyPolicyBox:"recaptcha-branding_privacyPolicyBox__h3HAK",culturePopup:"recaptcha-branding_culturePopup___9oUv"}},59910:function(e){e.exports={container:"newsletter-signup-shopping_container__KxQrK",holidayIcon:"newsletter-signup-shopping_holidayIcon__oKeOl",contentWrapper:"newsletter-signup-shopping_contentWrapper__W_0I1",titleWrapper:"newsletter-signup-shopping_titleWrapper__OEmqw",textWrapper:"newsletter-signup-shopping_textWrapper__kI0vB",logo:"newsletter-signup-shopping_logo__Fec2h",description:"newsletter-signup-shopping_description__R2ab9",eyesImg:"newsletter-signup-shopping_eyesImg__C3bqB",form:"newsletter-signup-shopping_form__R2tub",success:"newsletter-signup-shopping_success__h_ckc",star:"newsletter-signup-shopping_star__RJWug",spin:"newsletter-signup-shopping_spin__hFM8L",star1:"newsletter-signup-shopping_star1__bBfU_",star2:"newsletter-signup-shopping_star2__Li1Gr",star3:"newsletter-signup-shopping_star3__jOj4k",star4:"newsletter-signup-shopping_star4__ML7CN",title:"newsletter-signup-shopping_title__wEk7p",fadeIn:"newsletter-signup-shopping_fadeIn__W65lU",text:"newsletter-signup-shopping_text__uiCa4",slideInFromBottom:"newsletter-signup-shopping_slideInFromBottom__7c_mw",thankYouImg:"newsletter-signup-shopping_thankYouImg__bZ6QZ",slideInFromTop:"newsletter-signup-shopping_slideInFromTop__mhaSC",truckWrapper:"newsletter-signup-shopping_truckWrapper__axb6Q",truckImg:"newsletter-signup-shopping_truckImg__HISKB"}},468:function(e){e.exports={title:"newsletter-signup-success_title__Bj3BO",text:"newsletter-signup-success_text__nNafx"}}}]);
//# sourceMappingURL=403-6ce5eb073f06f34c.js.map