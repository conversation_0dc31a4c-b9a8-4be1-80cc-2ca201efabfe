(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[407],{39901:function(t,e,n){"use strict";n.d(e,{FW:function(){return Z},f_:function(){return mt},R3:function(){return E},qO:function(){return T},f0:function(){return o},Lj:function(){return j},Vn:function(){return F},gb:function(){return it},q2:function(){return b},YC:function(){return ut},nu:function(){return h},vp:function(){return ft},RM:function(){return L},og:function(){return P},bG:function(){return x},cS:function(){return S},Jv:function(){return y},VO:function(){return g},u2:function(){return p},gC:function(){return ct},Lo:function(){return st},dv:function(){return rt},S1:function(){return ht},$T:function(){return R},sB:function(){return u},oL:function(){return I},ye:function(){return lt},ZT:function(){return i},H3:function(){return G},j7:function(){return c},N8:function(){return l},UF:function(){return _},rT:function(){return z},Dh:function(){return C},Jn:function(){return d},bi:function(){return k},fL:function(){return O},VH:function(){return N},Ui:function(){return ot},et:function(){return at},km:function(){return v},G_:function(){return D}});var r=n(24027);function i(){}function o(t,e){for(const n in e)t[n]=e[n];return t}function a(t){return t()}function s(){return Object.create(null)}function c(t){t.forEach(a)}function u(t){return"function"===typeof t}function l(t,e){return t!=t?e==e:t!==e||t&&"object"===typeof t||"function"===typeof t}let f;function d(t,e){return f||(f=document.createElement("a")),f.href=e,t===f.href}function h(t,e,n,r){if(t){const i=m(t,e,n,r);return t[0](i)}}function m(t,e,n,r){return t[1]&&r?o(n.ctx.slice(),t[1](r(e))):n.ctx}function p(t,e,n,r){if(t[2]&&r){const i=t[2](r(n));if(void 0===e.dirty)return i;if("object"===typeof i){const t=[],n=Math.max(e.dirty.length,i.length);for(let r=0;r<n;r+=1)t[r]=e.dirty[r]|i[r];return t}return e.dirty|i}return e.dirty}function v(t,e,n,r,i,o){if(i){const a=m(e,n,r,o);t.p(a,i)}}function g(t){if(t.ctx.length>32){const e=[],n=t.ctx.length/32;for(let t=0;t<n;t++)e[t]=-1;return e}return-1}function y(t){const e={};for(const n in t)"$"!==n[0]&&(e[n]=t[n]);return e}function b(t,e){const n={};e=new Set(e);for(const r in t)e.has(r)||"$"===r[0]||(n[r]=t[r]);return n}const A="undefined"!==typeof window?window:"undefined"!==typeof globalThis?globalThis:n.g;let w=function(){function t(e){(0,r.PA)(this,t),this.options=e,this._listeners="WeakMap"in A?new WeakMap:void 0}return(0,r.qH)(t,[{key:"observe",value:function(t,e){var n=this;return this._listeners.set(t,e),this._getObserver().observe(t,this.options),function(){n._listeners.delete(t),n._observer.unobserve(t)}}},{key:"_getObserver",value:function(){var e,n=this;return null!==(e=this._observer)&&void 0!==e?e:this._observer=new ResizeObserver((function(e){var r;for(const i of e)t.entries.set(i.target,i),null===(r=n._listeners.get(i.target))||void 0===r||r(i)}))}}])}();function E(t,e){t.appendChild(e)}function T(t,e,n){const r=function(t){if(!t)return document;const e=t.getRootNode?t.getRootNode():t.ownerDocument;if(e&&e.host)return e;return t.ownerDocument}(t);if(!r.getElementById(e)){const t=x("style");t.id=e,t.textContent=n,function(t,e){E(t.head||t,e),e.sheet}(r,t)}}function R(t,e,n){t.insertBefore(e,n||null)}function P(t){t.parentNode&&t.parentNode.removeChild(t)}function L(t,e){for(let n=0;n<t.length;n+=1)t[n]&&t[n].d(e)}function x(t){return document.createElement(t)}function k(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function O(t){return document.createTextNode(t)}function C(){return O(" ")}function S(){return O("")}function I(t,e,n,r){return t.addEventListener(e,n,r),function(){return t.removeEventListener(e,n,r)}}function j(t,e,n){null==n?t.removeAttribute(e):t.getAttribute(e)!==n&&t.setAttribute(e,n)}w.entries="WeakMap"in A?new WeakMap:void 0;const M=["width","height"];function _(t,e){const n=Object.getOwnPropertyDescriptors(t.__proto__);for(const r in e)null==e[r]?t.removeAttribute(r):"style"===r?t.style.cssText=e[r]:"__value"===r?t.value=t[r]=e[r]:n[r]&&n[r].set&&-1===M.indexOf(r)?t[r]=e[r]:j(t,r,e[r])}function D(t,e,n){t.setAttributeNS("http://www.w3.org/1999/xlink",e,n)}function B(t){return Array.from(t.childNodes)}function z(t,e){e=""+e,t.data!==e&&(t.data=e)}function N(t,e,n){t.classList[n?"add":"remove"](e)}let $,Z=function(){return(0,r.qH)((function t(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];(0,r.PA)(this,t),this.is_svg=!1,this.is_svg=e,this.e=this.n=null}),[{key:"c",value:function(t){this.h(t)}},{key:"m",value:function(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;this.e||(this.is_svg?this.e=k(e.nodeName):this.e=x(11===e.nodeType?"TEMPLATE":e.nodeName),this.t="TEMPLATE"!==e.tagName?e:e.content,this.c(t)),this.i(n)}},{key:"h",value:function(t){this.e.innerHTML=t,this.n=Array.from("TEMPLATE"===this.e.nodeName?this.e.content.childNodes:this.e.childNodes)}},{key:"i",value:function(t){for(let e=0;e<this.n.length;e+=1)R(this.t,this.n[e],t)}},{key:"p",value:function(t){this.d(),this.h(t),this.i(this.a)}},{key:"d",value:function(){this.n.forEach(P)}}])}();function U(t){$=t}function G(t){(function(){if(!$)throw new Error("Function called outside component initialization");return $})().$$.on_mount.push(t)}const V=[],F=[];let J=[];const H=[],W=Promise.resolve();let q=!1;function Y(t){J.push(t)}const X=new Set;let Q=0;function K(){if(0!==Q)return;const t=$;do{try{for(;Q<V.length;){const t=V[Q];Q++,U(t),tt(t.$$)}}catch(e){throw V.length=0,Q=0,e}for(U(null),V.length=0,Q=0;F.length;)F.pop()();for(let t=0;t<J.length;t+=1){const e=J[t];X.has(e)||(X.add(e),e())}J.length=0}while(V.length);for(;H.length;)H.pop()();q=!1,X.clear(),U(t)}function tt(t){if(null!==t.fragment){t.update(),c(t.before_update);const e=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,e),t.after_update.forEach(Y)}}const et=new Set;let nt;function rt(){nt={r:0,c:[],p:nt}}function it(){nt.r||c(nt.c),nt=nt.p}function ot(t,e){t&&t.i&&(et.delete(t),t.i(e))}function at(t,e,n,r){if(t&&t.o){if(et.has(t))return;et.add(t),nt.c.push((function(){et.delete(t),r&&(n&&t.d(1),r())})),t.o(e)}else r&&r()}function st(t,e){const n={},r={},i={$$scope:1};let o=t.length;for(;o--;){const a=t[o],s=e[o];if(s){for(const t in a)t in s||(r[t]=1);for(const t in s)i[t]||(n[t]=s[t],i[t]=1);t[o]=s}else for(const t in a)i[t]=1}for(const a in r)a in n||(n[a]=void 0);return n}function ct(t){return"object"===typeof t&&null!==t?t:{}}function ut(t){t&&t.c()}function lt(t,e,n,r){const{fragment:i,after_update:o}=t.$$;i&&i.m(e,n),r||Y((function(){const e=t.$$.on_mount.map(a).filter(u);t.$$.on_destroy?t.$$.on_destroy.push(...e):c(e),t.$$.on_mount=[]})),o.forEach(Y)}function ft(t,e){const n=t.$$;null!==n.fragment&&(!function(t){const e=[],n=[];J.forEach((function(r){return-1===t.indexOf(r)?e.push(r):n.push(r)})),n.forEach((function(t){return t()})),J=e}(n.after_update),c(n.on_destroy),n.fragment&&n.fragment.d(e),n.on_destroy=n.fragment=null,n.ctx=[])}function dt(t,e){-1===t.$$.dirty[0]&&(V.push(t),q||(q=!0,W.then(K)),t.$$.dirty.fill(0)),t.$$.dirty[e/31|0]|=1<<e%31}function ht(t,e,n,r,o,a,u){let l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:[-1];const f=$;U(t);const d=t.$$={fragment:null,ctx:[],props:a,update:i,not_equal:o,bound:s(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(f?f.$$.context:[])),callbacks:s(),dirty:l,skip_bound:!1,root:e.target||f.$$.root};u&&u(d.root);let h=!1;if(d.ctx=n?n(t,e.props||{},(function(e,n){const r=!(arguments.length<=2)&&arguments.length-2?arguments.length<=2?void 0:arguments[2]:n;return d.ctx&&o(d.ctx[e],d.ctx[e]=r)&&(!d.skip_bound&&d.bound[e]&&d.bound[e](r),h&&dt(t,e)),n})):[],d.update(),h=!0,c(d.before_update),d.fragment=!!r&&r(d.ctx),e.target){if(e.hydrate){const t=B(e.target);d.fragment&&d.fragment.l(t),t.forEach(P)}else d.fragment&&d.fragment.c();e.intro&&ot(t.$$.fragment),lt(t,e.target,e.anchor,e.customElement),K()}U(f)}let mt=function(){return(0,r.qH)((function t(){(0,r.PA)(this,t)}),[{key:"$destroy",value:function(){ft(this,1),this.$destroy=i}},{key:"$on",value:function(t,e){if(!u(e))return i;const n=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return n.push(e),function(){const t=n.indexOf(e);-1!==t&&n.splice(t,1)}}},{key:"$set",value:function(t){var e;this.$$set&&(e=t,0!==Object.keys(e).length)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}}])}()},19785:function(t,e,n){"use strict";function r(t,e){if(e.length<t)throw new TypeError(t+" argument"+(t>1?"s":"")+" required, but only "+e.length+" present")}n.d(e,{Z:function(){return r}})},42646:function(t,e,n){"use strict";n.d(e,{Z:function(){return C}});var r={};function i(){return r}function o(t){var e=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return e.setUTCFullYear(t.getFullYear()),t.getTime()-e.getTime()}var a=n(66700),s=n(19785);function c(t,e){(0,s.Z)(2,arguments);var n=(0,a.Z)(t),r=(0,a.Z)(e),i=n.getTime()-r.getTime();return i<0?-1:i>0?1:i}function u(t,e){if(null==t)throw new TypeError("assign requires that input parameter not be null or undefined");for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}function l(t){return u({},t)}var f={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},d=function(t,e,n){var r,i=f[t];return r="string"===typeof i?i:1===e?i.one:i.other.replace("{{count}}",e.toString()),null!==n&&void 0!==n&&n.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r};function h(t){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.width?String(e.width):t.defaultWidth,r=t.formats[n]||t.formats[t.defaultWidth];return r}}var m={date:h({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:h({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:h({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},p={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},v=function(t,e,n,r){return p[t]};function g(t){return function(e,n){var r;if("formatting"===(null!==n&&void 0!==n&&n.context?String(n.context):"standalone")&&t.formattingValues){var i=t.defaultFormattingWidth||t.defaultWidth,o=null!==n&&void 0!==n&&n.width?String(n.width):i;r=t.formattingValues[o]||t.formattingValues[i]}else{var a=t.defaultWidth,s=null!==n&&void 0!==n&&n.width?String(n.width):t.defaultWidth;r=t.values[s]||t.values[a]}return r[t.argumentCallback?t.argumentCallback(e):e]}}var y={ordinalNumber:function(t,e){var n=Number(t),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:g({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:g({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:function(t){return t-1}}),month:g({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:g({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:g({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};function b(t){return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.width,i=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],o=e.match(i);if(!o)return null;var a,s=o[0],c=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],u=Array.isArray(c)?w(c,(function(t){return t.test(s)})):A(c,(function(t){return t.test(s)}));a=t.valueCallback?t.valueCallback(u):u,a=n.valueCallback?n.valueCallback(a):a;var l=e.slice(s.length);return{value:a,rest:l}}}function A(t,e){for(var n in t)if(t.hasOwnProperty(n)&&e(t[n]))return n}function w(t,e){for(var n=0;n<t.length;n++)if(e(t[n]))return n}var E,T={ordinalNumber:(E={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:function(t){return parseInt(t,10)}},function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.match(E.matchPattern);if(!n)return null;var r=n[0],i=t.match(E.parsePattern);if(!i)return null;var o=E.valueCallback?E.valueCallback(i[0]):i[0];o=e.valueCallback?e.valueCallback(o):o;var a=t.slice(r.length);return{value:o,rest:a}}),era:b({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:b({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:b({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:b({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:b({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},R={code:"en-US",formatDistance:d,formatLong:m,formatRelative:v,localize:y,match:T,options:{weekStartsOn:0,firstWeekContainsDate:1}},P=6e4,L=1440,x=43200,k=525600;function O(t,e,n){var r,f,d;(0,s.Z)(2,arguments);var h=i(),m=null!==(r=null!==(f=null===n||void 0===n?void 0:n.locale)&&void 0!==f?f:h.locale)&&void 0!==r?r:R;if(!m.formatDistance)throw new RangeError("locale must contain localize.formatDistance property");var p=c(t,e);if(isNaN(p))throw new RangeError("Invalid time value");var v,g,y=u(l(n),{addSuffix:Boolean(null===n||void 0===n?void 0:n.addSuffix),comparison:p});p>0?(v=(0,a.Z)(e),g=(0,a.Z)(t)):(v=(0,a.Z)(t),g=(0,a.Z)(e));var b,A=String(null!==(d=null===n||void 0===n?void 0:n.roundingMethod)&&void 0!==d?d:"round");if("floor"===A)b=Math.floor;else if("ceil"===A)b=Math.ceil;else{if("round"!==A)throw new RangeError("roundingMethod must be 'floor', 'ceil' or 'round'");b=Math.round}var w,E=g.getTime()-v.getTime(),T=E/P,O=o(g)-o(v),C=(E-O)/P,S=null===n||void 0===n?void 0:n.unit;if("second"===(w=S?String(S):T<1?"second":T<60?"minute":T<L?"hour":C<x?"day":C<k?"month":"year")){var I=b(E/1e3);return m.formatDistance("xSeconds",I,y)}if("minute"===w){var j=b(T);return m.formatDistance("xMinutes",j,y)}if("hour"===w){var M=b(T/60);return m.formatDistance("xHours",M,y)}if("day"===w){var _=b(C/L);return m.formatDistance("xDays",_,y)}if("month"===w){var D=b(C/x);return 12===D&&"month"!==S?m.formatDistance("xYears",1,y):m.formatDistance("xMonths",D,y)}if("year"===w){var B=b(C/k);return m.formatDistance("xYears",B,y)}throw new RangeError("unit must be 'second', 'minute', 'hour', 'day', 'month' or 'year'")}function C(t,e){return(0,s.Z)(1,arguments),O(t,Date.now(),e)}},42598:function(t,e,n){"use strict";n.d(e,{Z:function(){return o}});var r=n(66700),i=n(19785);function o(t,e){(0,i.Z)(2,arguments);var n=(0,r.Z)(t),o=(0,r.Z)(e);return n.getTime()>o.getTime()}},17396:function(t,e,n){"use strict";n.d(e,{Z:function(){return f}});var r=n(26893);function i(t){if(null===t||!0===t||!1===t)return NaN;var e=Number(t);return isNaN(e)?e:e<0?Math.ceil(e):Math.floor(e)}var o=n(66700),a=n(19785);function s(t,e){(0,a.Z)(2,arguments);var n=(0,o.Z)(t),r=i(e);return isNaN(r)?new Date(NaN):r?(n.setDate(n.getDate()+r),n):n}function c(t,e){(0,a.Z)(2,arguments);var n=i(e);return s(t,-n)}function u(t,e){(0,a.Z)(2,arguments);var n=(0,o.Z)(t),r=i(e);if(isNaN(r))return new Date(NaN);if(!r)return n;var s=n.getDate(),c=new Date(n.getTime());c.setMonth(n.getMonth()+r+1,0);var u=c.getDate();return s>=u?c:(n.setFullYear(c.getFullYear(),c.getMonth(),s),n)}function l(t,e){(0,a.Z)(2,arguments);var n=i(e);return u(t,-n)}function f(t,e){if((0,a.Z)(2,arguments),!e||"object"!==(0,r.Z)(e))return new Date(NaN);var n=e.years?i(e.years):0,o=e.months?i(e.months):0,s=e.weeks?i(e.weeks):0,u=e.days?i(e.days):0,f=e.hours?i(e.hours):0,d=e.minutes?i(e.minutes):0,h=e.seconds?i(e.seconds):0,m=l(t,o+12*n),p=c(m,u+7*s),v=d+60*f,g=h+60*v,y=1e3*g,b=new Date(p.getTime()-y);return b}},66700:function(t,e,n){"use strict";n.d(e,{Z:function(){return o}});var r=n(26893),i=n(19785);function o(t){(0,i.Z)(1,arguments);var e=Object.prototype.toString.call(t);return t instanceof Date||"object"===(0,r.Z)(t)&&"[object Date]"===e?new Date(t.getTime()):"number"===typeof t||"[object Number]"===e?new Date(t):("string"!==typeof t&&"[object String]"!==e||"undefined"===typeof console||(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn((new Error).stack)),new Date(NaN))}},2148:function(t,e){!function(t){var e=/\S/,n=/\"/g,r=/\n/g,i=/\r/g,o=/\\/g,a=/\u2028/,s=/\u2029/;function c(t){"}"===t.n.substr(t.n.length-1)&&(t.n=t.n.substring(0,t.n.length-1))}function u(t){return t.trim?t.trim():t.replace(/^\s*|\s*$/g,"")}function l(t,e,n){if(e.charAt(n)!=t.charAt(0))return!1;for(var r=1,i=t.length;r<i;r++)if(e.charAt(n+r)!=t.charAt(r))return!1;return!0}t.tags={"#":1,"^":2,"<":3,$:4,"/":5,"!":6,">":7,"=":8,_v:9,"{":10,"&":11,_t:12},t.scan=function(n,r){var i=n.length,o=0,a=null,s=null,f="",d=[],h=!1,m=0,p=0,v="{{",g="}}";function y(){f.length>0&&(d.push({tag:"_t",text:new String(f)}),f="")}function b(n,r){if(y(),n&&function(){for(var n=!0,r=p;r<d.length;r++)if(!(n=t.tags[d[r].tag]<t.tags._v||"_t"==d[r].tag&&null===d[r].text.match(e)))return!1;return n}())for(var i,o=p;o<d.length;o++)d[o].text&&((i=d[o+1])&&">"==i.tag&&(i.indent=d[o].text.toString()),d.splice(o,1));else r||d.push({tag:"\n"});h=!1,p=d.length}function A(t,e){var n="="+g,r=t.indexOf(n,e),i=u(t.substring(t.indexOf("=",e)+1,r)).split(" ");return v=i[0],g=i[i.length-1],r+n.length-1}for(r&&(r=r.split(" "),v=r[0],g=r[1]),m=0;m<i;m++)0==o?l(v,n,m)?(--m,y(),o=1):"\n"==n.charAt(m)?b(h):f+=n.charAt(m):1==o?(m+=v.length-1,"="==(a=(s=t.tags[n.charAt(m+1)])?n.charAt(m+1):"_v")?(m=A(n,m),o=0):(s&&m++,o=2),h=m):l(g,n,m)?(d.push({tag:a,n:u(f),otag:v,ctag:g,i:"/"==a?h-v.length:m+g.length}),f="",m+=g.length-1,o=0,"{"==a&&("}}"==g?m++:c(d[d.length-1]))):f+=n.charAt(m);return b(h,!0),d};var f={_t:!0,"\n":!0,$:!0,"/":!0};function d(e,n,r,i){var o,a=[],s=null,c=null;for(o=r[r.length-1];e.length>0;){if(c=e.shift(),o&&"<"==o.tag&&!(c.tag in f))throw new Error("Illegal content in < super tag.");if(t.tags[c.tag]<=t.tags.$||h(c,i))r.push(c),c.nodes=d(e,c.tag,r,i);else{if("/"==c.tag){if(0===r.length)throw new Error("Closing tag without opener: /"+c.n);if(s=r.pop(),c.n!=s.n&&!m(c.n,s.n,i))throw new Error("Nesting error: "+s.n+" vs. "+c.n);return s.end=c.i,a}"\n"==c.tag&&(c.last=0==e.length||"\n"==e[0].tag)}a.push(c)}if(r.length>0)throw new Error("missing closing tag: "+r.pop().n);return a}function h(t,e){for(var n=0,r=e.length;n<r;n++)if(e[n].o==t.n)return t.tag="#",!0}function m(t,e,n){for(var r=0,i=n.length;r<i;r++)if(n[r].c==t&&n[r].o==e)return!0}function p(t){var e=[];for(var n in t.partials)e.push('"'+g(n)+'":{name:"'+g(t.partials[n].name)+'", '+p(t.partials[n])+"}");return"partials: {"+e.join(",")+"}, subs: "+function(t){var e=[];for(var n in t)e.push('"'+g(n)+'": function(c,p,t,i) {'+t[n]+"}");return"{ "+e.join(",")+" }"}(t.subs)}t.stringify=function(e,n,r){return"{code: function (c,p,i) { "+t.wrapMain(e.code)+" },"+p(e)+"}"};var v=0;function g(t){return t.replace(o,"\\\\").replace(n,'\\"').replace(r,"\\n").replace(i,"\\r").replace(a,"\\u2028").replace(s,"\\u2029")}function y(t){return~t.indexOf(".")?"d":"f"}function b(t,e){var n="<"+(e.prefix||"")+t.n+v++;return e.partials[n]={name:t.n,partials:{}},e.code+='t.b(t.rp("'+g(n)+'",c,p,"'+(t.indent||"")+'"));',n}function A(t,e){e.code+="t.b(t.t(t."+y(t.n)+'("'+g(t.n)+'",c,p,0)));'}function w(t){return"t.b("+t+");"}t.generate=function(e,n,r){v=0;var i={code:"",subs:{},partials:{}};return t.walk(e,i),r.asString?this.stringify(i,n,r):this.makeTemplate(i,n,r)},t.wrapMain=function(t){return'var t=this;t.b(i=i||"");'+t+"return t.fl();"},t.template=t.Template,t.makeTemplate=function(t,e,n){var r=this.makePartials(t);return r.code=new Function("c","p","i",this.wrapMain(t.code)),new this.template(r,e,this,n)},t.makePartials=function(t){var e,n={subs:{},partials:t.partials,name:t.name};for(e in n.partials)n.partials[e]=this.makePartials(n.partials[e]);for(e in t.subs)n.subs[e]=new Function("c","p","t","i",t.subs[e]);return n},t.codegen={"#":function(e,n){n.code+="if(t.s(t."+y(e.n)+'("'+g(e.n)+'",c,p,1),c,p,0,'+e.i+","+e.end+',"'+e.otag+" "+e.ctag+'")){t.rs(c,p,function(c,p,t){',t.walk(e.nodes,n),n.code+="});c.pop();}"},"^":function(e,n){n.code+="if(!t.s(t."+y(e.n)+'("'+g(e.n)+'",c,p,1),c,p,1,0,0,"")){',t.walk(e.nodes,n),n.code+="};"},">":b,"<":function(e,n){var r={partials:{},code:"",subs:{},inPartial:!0};t.walk(e.nodes,r);var i=n.partials[b(e,n)];i.subs=r.subs,i.partials=r.partials},$:function(e,n){var r={subs:{},code:"",partials:n.partials,prefix:e.n};t.walk(e.nodes,r),n.subs[e.n]=r.code,n.inPartial||(n.code+='t.sub("'+g(e.n)+'",c,p,i);')},"\n":function(t,e){e.code+=w('"\\n"'+(t.last?"":" + i"))},_v:function(t,e){e.code+="t.b(t.v(t."+y(t.n)+'("'+g(t.n)+'",c,p,0)));'},_t:function(t,e){e.code+=w('"'+g(t.text)+'"')},"{":A,"&":A},t.walk=function(e,n){for(var r,i=0,o=e.length;i<o;i++)(r=t.codegen[e[i].tag])&&r(e[i],n);return n},t.parse=function(t,e,n){return d(t,0,[],(n=n||{}).sectionTags||[])},t.cache={},t.cacheKey=function(t,e){return[t,!!e.asString,!!e.disableLambda,e.delimiters,!!e.modelGet].join("||")},t.compile=function(e,n){n=n||{};var r=t.cacheKey(e,n),i=this.cache[r];if(i){var o=i.partials;for(var a in o)delete o[a].instance;return i}return i=this.generate(this.parse(this.scan(e,n.delimiters),e,n),e,n),this.cache[r]=i}}(e)},50112:function(t,e,n){var r=n(2148);r.Template=n(78719).Template,r.template=r.Template,t.exports=r},78719:function(t,e){!function(t){function e(t,e,n){var r;return e&&"object"==typeof e&&(void 0!==e[t]?r=e[t]:n&&e.get&&"function"==typeof e.get&&(r=e.get(t))),r}t.Template=function(t,e,n,r){t=t||{},this.r=t.code||this.r,this.c=n,this.options=r||{},this.text=e||"",this.partials=t.partials||{},this.subs=t.subs||{},this.buf=""},t.Template.prototype={r:function(t,e,n){return""},v:function(t){return t=c(t),s.test(t)?t.replace(n,"&amp;").replace(r,"&lt;").replace(i,"&gt;").replace(o,"&#39;").replace(a,"&quot;"):t},t:c,render:function(t,e,n){return this.ri([t],e||{},n)},ri:function(t,e,n){return this.r(t,e,n)},ep:function(t,e){var n=this.partials[t],r=e[n.name];if(n.instance&&n.base==r)return n.instance;if("string"==typeof r){if(!this.c)throw new Error("No compiler available.");r=this.c.compile(r,this.options)}if(!r)return null;if(this.partials[t].base=r,n.subs){for(key in e.stackText||(e.stackText={}),n.subs)e.stackText[key]||(e.stackText[key]=void 0!==this.activeSub&&e.stackText[this.activeSub]?e.stackText[this.activeSub]:this.text);r=function(t,e,n,r,i,o){function a(){}function s(){}var c;a.prototype=t,s.prototype=t.subs;var u=new a;for(c in u.subs=new s,u.subsText={},u.buf="",r=r||{},u.stackSubs=r,u.subsText=o,e)r[c]||(r[c]=e[c]);for(c in r)u.subs[c]=r[c];for(c in i=i||{},u.stackPartials=i,n)i[c]||(i[c]=n[c]);for(c in i)u.partials[c]=i[c];return u}(r,n.subs,n.partials,this.stackSubs,this.stackPartials,e.stackText)}return this.partials[t].instance=r,r},rp:function(t,e,n,r){var i=this.ep(t,n);return i?i.ri(e,n,r):""},rs:function(t,e,n){var r=t[t.length-1];if(u(r))for(var i=0;i<r.length;i++)t.push(r[i]),n(t,e,this),t.pop();else n(t,e,this)},s:function(t,e,n,r,i,o,a){var s;return(!u(t)||0!==t.length)&&("function"==typeof t&&(t=this.ms(t,e,n,r,i,o,a)),s=!!t,!r&&s&&e&&e.push("object"==typeof t?t:e[e.length-1]),s)},d:function(t,n,r,i){var o,a=t.split("."),s=this.f(a[0],n,r,i),c=this.options.modelGet,l=null;if("."===t&&u(n[n.length-2]))s=n[n.length-1];else for(var f=1;f<a.length;f++)void 0!==(o=e(a[f],s,c))?(l=s,s=o):s="";return!(i&&!s)&&(i||"function"!=typeof s||(n.push(l),s=this.mv(s,n,r),n.pop()),s)},f:function(t,n,r,i){for(var o=!1,a=!1,s=this.options.modelGet,c=n.length-1;c>=0;c--)if(void 0!==(o=e(t,n[c],s))){a=!0;break}return a?(i||"function"!=typeof o||(o=this.mv(o,n,r)),o):!i&&""},ls:function(t,e,n,r,i){var o=this.options.delimiters;return this.options.delimiters=i,this.b(this.ct(c(t.call(e,r)),e,n)),this.options.delimiters=o,!1},ct:function(t,e,n){if(this.options.disableLambda)throw new Error("Lambda features disabled.");return this.c.compile(t,this.options).render(e,n)},b:function(t){this.buf+=t},fl:function(){var t=this.buf;return this.buf="",t},ms:function(t,e,n,r,i,o,a){var s,c=e[e.length-1],u=t.call(c);return"function"==typeof u?!!r||(s=this.activeSub&&this.subsText&&this.subsText[this.activeSub]?this.subsText[this.activeSub]:this.text,this.ls(u,c,n,s.substring(i,o),a)):u},mv:function(t,e,n){var r=e[e.length-1],i=t.call(r);return"function"==typeof i?this.ct(c(i.call(r)),r,n):i},sub:function(t,e,n,r){var i=this.subs[t];i&&(this.activeSub=t,i(e,n,this,r),this.activeSub=!1)}};var n=/&/g,r=/</g,i=/>/g,o=/\'/g,a=/\"/g,s=/[&<>\"\']/;function c(t){return String(null===t||void 0===t?"":t)}var u=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)}}(e)},2601:function(t,e,n){var r=n(91616)("jsonp");t.exports=function(t,e,n){"function"==typeof e&&(n=e,e={});e||(e={});var a,s,c=e.prefix||"__jp",u=e.name||c+i++,l=e.param||"callback",f=null!=e.timeout?e.timeout:6e4,d=encodeURIComponent,h=document.getElementsByTagName("script")[0]||document.head;f&&(s=setTimeout((function(){m(),n&&n(new Error("Timeout"))}),f));function m(){a.parentNode&&a.parentNode.removeChild(a),window[u]=o,s&&clearTimeout(s)}return window[u]=function(t){r("jsonp got",t),m(),n&&n(null,t)},t=(t+=(~t.indexOf("?")?"&":"?")+l+"="+d(u)).replace("?&","?"),r('jsonp req "%s"',t),(a=document.createElement("script")).src=t,h.parentNode.insertBefore(a,h),function(){window[u]&&m()}};var i=0;function o(){}},91616:function(t,e,n){var r=n(34406);function i(){var t;try{t=e.storage.debug}catch(n){}return!t&&"undefined"!==typeof r&&"env"in r&&(t=r.env.DEBUG),t}(e=t.exports=n(80555)).log=function(){return"object"===typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},e.formatArgs=function(t){var n=this.useColors;if(t[0]=(n?"%c":"")+this.namespace+(n?" %c":" ")+t[0]+(n?"%c ":" ")+"+"+e.humanize(this.diff),!n)return;var r="color: "+this.color;t.splice(1,0,r,"color: inherit");var i=0,o=0;t[0].replace(/%[a-zA-Z%]/g,(function(t){"%%"!==t&&(i++,"%c"===t&&(o=i))})),t.splice(o,0,r)},e.save=function(t){try{null==t?e.storage.removeItem("debug"):e.storage.debug=t}catch(n){}},e.load=i,e.useColors=function(){if("undefined"!==typeof window&&window.process&&"renderer"===window.process.type)return!0;return"undefined"!==typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!==typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},e.storage="undefined"!=typeof chrome&&"undefined"!=typeof chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(t){}}(),e.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],e.formatters.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},e.enable(i())},80555:function(t,e,n){var r;function i(t){function n(){if(n.enabled){var t=n,i=+new Date,o=i-(r||i);t.diff=o,t.prev=r,t.curr=i,r=i;for(var a=new Array(arguments.length),s=0;s<a.length;s++)a[s]=arguments[s];a[0]=e.coerce(a[0]),"string"!==typeof a[0]&&a.unshift("%O");var c=0;a[0]=a[0].replace(/%([a-zA-Z%])/g,(function(n,r){if("%%"===n)return n;c++;var i=e.formatters[r];if("function"===typeof i){var o=a[c];n=i.call(t,o),a.splice(c,1),c--}return n})),e.formatArgs.call(t,a);var u=n.log||e.log||console.log.bind(console);u.apply(t,a)}}return n.namespace=t,n.enabled=e.enabled(t),n.useColors=e.useColors(),n.color=function(t){var n,r=0;for(n in t)r=(r<<5)-r+t.charCodeAt(n),r|=0;return e.colors[Math.abs(r)%e.colors.length]}(t),"function"===typeof e.init&&e.init(n),n}(e=t.exports=i.debug=i.default=i).coerce=function(t){return t instanceof Error?t.stack||t.message:t},e.disable=function(){e.enable("")},e.enable=function(t){e.save(t),e.names=[],e.skips=[];for(var n=("string"===typeof t?t:"").split(/[\s,]+/),r=n.length,i=0;i<r;i++)n[i]&&("-"===(t=n[i].replace(/\*/g,".*?"))[0]?e.skips.push(new RegExp("^"+t.substr(1)+"$")):e.names.push(new RegExp("^"+t+"$")))},e.enabled=function(t){var n,r;for(n=0,r=e.skips.length;n<r;n++)if(e.skips[n].test(t))return!1;for(n=0,r=e.names.length;n<r;n++)if(e.names[n].test(t))return!0;return!1},e.humanize=n(7241),e.names=[],e.skips=[],e.formatters={}},7241:function(t){var e=1e3,n=60*e,r=60*n,i=24*r,o=365.25*i;function a(t,e,n){if(!(t<e))return t<1.5*e?Math.floor(t/e)+" "+n:Math.ceil(t/e)+" "+n+"s"}t.exports=function(t,s){s=s||{};var c,u=typeof t;if("string"===u&&t.length>0)return function(t){if((t=String(t)).length>100)return;var a=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(t);if(!a)return;var s=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return s*o;case"days":case"day":case"d":return s*i;case"hours":case"hour":case"hrs":case"hr":case"h":return s*r;case"minutes":case"minute":case"mins":case"min":case"m":return s*n;case"seconds":case"second":case"secs":case"sec":case"s":return s*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}(t);if("number"===u&&!1===isNaN(t))return s.long?a(c=t,i,"day")||a(c,r,"hour")||a(c,n,"minute")||a(c,e,"second")||c+" ms":function(t){if(t>=i)return Math.round(t/i)+"d";if(t>=r)return Math.round(t/r)+"h";if(t>=n)return Math.round(t/n)+"m";if(t>=e)return Math.round(t/e)+"s";return t+"ms"}(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}},51344:function(t,e,n){t=n.nmd(t);var r="__lodash_hash_undefined__",i=9007199254740991,o="[object Arguments]",a="[object Function]",s="[object Object]",c=/^\[object .+?Constructor\]$/,u=/^(?:0|[1-9]\d*)$/,l={};l["[object Float32Array]"]=l["[object Float64Array]"]=l["[object Int8Array]"]=l["[object Int16Array]"]=l["[object Int32Array]"]=l["[object Uint8Array]"]=l["[object Uint8ClampedArray]"]=l["[object Uint16Array]"]=l["[object Uint32Array]"]=!0,l[o]=l["[object Array]"]=l["[object ArrayBuffer]"]=l["[object Boolean]"]=l["[object DataView]"]=l["[object Date]"]=l["[object Error]"]=l[a]=l["[object Map]"]=l["[object Number]"]=l[s]=l["[object RegExp]"]=l["[object Set]"]=l["[object String]"]=l["[object WeakMap]"]=!1;var f="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,d="object"==typeof self&&self&&self.Object===Object&&self,h=f||d||Function("return this")(),m=e&&!e.nodeType&&e,p=m&&t&&!t.nodeType&&t,v=p&&p.exports===m,g=v&&f.process,y=function(){try{var t=p&&p.require&&p.require("util").types;return t||g&&g.binding&&g.binding("util")}catch(e){}}(),b=y&&y.isTypedArray;function A(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}var w,E,T=Array.prototype,R=Function.prototype,P=Object.prototype,L=h["__core-js_shared__"],x=R.toString,k=P.hasOwnProperty,O=function(){var t=/[^.]+$/.exec(L&&L.keys&&L.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}(),C=P.toString,S=x.call(Object),I=RegExp("^"+x.call(k).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),j=v?h.Buffer:void 0,M=h.Symbol,_=h.Uint8Array,D=j?j.allocUnsafe:void 0,B=(w=Object.getPrototypeOf,E=Object,function(t){return w(E(t))}),z=Object.create,N=P.propertyIsEnumerable,$=T.splice,Z=M?M.toStringTag:void 0,U=function(){try{var t=mt(Object,"defineProperty");return t({},"",{}),t}catch(e){}}(),G=j?j.isBuffer:void 0,V=Math.max,F=Date.now,J=mt(h,"Map"),H=mt(Object,"create"),W=function(){function t(){}return function(e){if(!Lt(e))return{};if(z)return z(e);t.prototype=e;var n=new t;return t.prototype=void 0,n}}();function q(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Y(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function X(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Q(t){var e=this.__data__=new Y(t);this.size=e.size}function K(t,e){var n=wt(t),r=!n&&At(t),i=!n&&!r&&Tt(t),o=!n&&!r&&!i&&kt(t),a=n||r||i||o,s=a?function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}(t.length,String):[],c=s.length;for(var u in t)!e&&!k.call(t,u)||a&&("length"==u||i&&("offset"==u||"parent"==u)||o&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||pt(u,c))||s.push(u);return s}function tt(t,e,n){(void 0!==n&&!bt(t[e],n)||void 0===n&&!(e in t))&&rt(t,e,n)}function et(t,e,n){var r=t[e];k.call(t,e)&&bt(r,n)&&(void 0!==n||e in t)||rt(t,e,n)}function nt(t,e){for(var n=t.length;n--;)if(bt(t[n][0],e))return n;return-1}function rt(t,e,n){"__proto__"==e&&U?U(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}q.prototype.clear=function(){this.__data__=H?H(null):{},this.size=0},q.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},q.prototype.get=function(t){var e=this.__data__;if(H){var n=e[t];return n===r?void 0:n}return k.call(e,t)?e[t]:void 0},q.prototype.has=function(t){var e=this.__data__;return H?void 0!==e[t]:k.call(e,t)},q.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=H&&void 0===e?r:e,this},Y.prototype.clear=function(){this.__data__=[],this.size=0},Y.prototype.delete=function(t){var e=this.__data__,n=nt(e,t);return!(n<0)&&(n==e.length-1?e.pop():$.call(e,n,1),--this.size,!0)},Y.prototype.get=function(t){var e=this.__data__,n=nt(e,t);return n<0?void 0:e[n][1]},Y.prototype.has=function(t){return nt(this.__data__,t)>-1},Y.prototype.set=function(t,e){var n=this.__data__,r=nt(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this},X.prototype.clear=function(){this.size=0,this.__data__={hash:new q,map:new(J||Y),string:new q}},X.prototype.delete=function(t){var e=ht(this,t).delete(t);return this.size-=e?1:0,e},X.prototype.get=function(t){return ht(this,t).get(t)},X.prototype.has=function(t){return ht(this,t).has(t)},X.prototype.set=function(t,e){var n=ht(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this},Q.prototype.clear=function(){this.__data__=new Y,this.size=0},Q.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},Q.prototype.get=function(t){return this.__data__.get(t)},Q.prototype.has=function(t){return this.__data__.has(t)},Q.prototype.set=function(t,e){var n=this.__data__;if(n instanceof Y){var r=n.__data__;if(!J||r.length<199)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new X(r)}return n.set(t,e),this.size=n.size,this};var it,ot=function(t,e,n){for(var r=-1,i=Object(t),o=n(t),a=o.length;a--;){var s=o[it?a:++r];if(!1===e(i[s],s,i))break}return t};function at(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":Z&&Z in Object(t)?function(t){var e=k.call(t,Z),n=t[Z];try{t[Z]=void 0;var r=!0}catch(o){}var i=C.call(t);r&&(e?t[Z]=n:delete t[Z]);return i}(t):function(t){return C.call(t)}(t)}function st(t){return xt(t)&&at(t)==o}function ct(t){return!(!Lt(t)||function(t){return!!O&&O in t}(t))&&(Rt(t)?I:c).test(function(t){if(null!=t){try{return x.call(t)}catch(e){}try{return t+""}catch(e){}}return""}(t))}function ut(t){if(!Lt(t))return function(t){var e=[];if(null!=t)for(var n in Object(t))e.push(n);return e}(t);var e=vt(t),n=[];for(var r in t)("constructor"!=r||!e&&k.call(t,r))&&n.push(r);return n}function lt(t,e,n,r,i){t!==e&&ot(e,(function(o,a){if(i||(i=new Q),Lt(o))!function(t,e,n,r,i,o,a){var c=gt(t,n),u=gt(e,n),l=a.get(u);if(l)return void tt(t,n,l);var f=o?o(c,u,n+"",t,e,a):void 0,d=void 0===f;if(d){var h=wt(u),m=!h&&Tt(u),p=!h&&!m&&kt(u);f=u,h||m||p?wt(c)?f=c:xt(v=c)&&Et(v)?f=function(t,e){var n=-1,r=t.length;e||(e=Array(r));for(;++n<r;)e[n]=t[n];return e}(c):m?(d=!1,f=function(t,e){if(e)return t.slice();var n=t.length,r=D?D(n):new t.constructor(n);return t.copy(r),r}(u,!0)):p?(d=!1,f=function(t,e){var n=e?function(t){var e=new t.constructor(t.byteLength);return new _(e).set(new _(t)),e}(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)}(u,!0)):f=[]:function(t){if(!xt(t)||at(t)!=s)return!1;var e=B(t);if(null===e)return!0;var n=k.call(e,"constructor")&&e.constructor;return"function"==typeof n&&n instanceof n&&x.call(n)==S}(u)||At(u)?(f=c,At(c)?f=function(t){return function(t,e,n,r){var i=!n;n||(n={});var o=-1,a=e.length;for(;++o<a;){var s=e[o],c=r?r(n[s],t[s],s,n,t):void 0;void 0===c&&(c=t[s]),i?rt(n,s,c):et(n,s,c)}return n}(t,Ot(t))}(c):Lt(c)&&!Rt(c)||(f=function(t){return"function"!=typeof t.constructor||vt(t)?{}:W(B(t))}(u))):d=!1}var v;d&&(a.set(u,f),i(f,u,r,o,a),a.delete(u));tt(t,n,f)}(t,e,a,n,lt,r,i);else{var c=r?r(gt(t,a),o,a+"",t,e,i):void 0;void 0===c&&(c=o),tt(t,a,c)}}),Ot)}function ft(t,e){return yt(function(t,e,n){return e=V(void 0===e?t.length-1:e,0),function(){for(var r=arguments,i=-1,o=V(r.length-e,0),a=Array(o);++i<o;)a[i]=r[e+i];i=-1;for(var s=Array(e+1);++i<e;)s[i]=r[i];return s[e]=n(a),A(t,this,s)}}(t,e,It),t+"")}var dt=U?function(t,e){return U(t,"toString",{configurable:!0,enumerable:!1,value:(n=e,function(){return n}),writable:!0});var n}:It;function ht(t,e){var n=t.__data__;return function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}(e)?n["string"==typeof e?"string":"hash"]:n.map}function mt(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return ct(n)?n:void 0}function pt(t,e){var n=typeof t;return!!(e=null==e?i:e)&&("number"==n||"symbol"!=n&&u.test(t))&&t>-1&&t%1==0&&t<e}function vt(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||P)}function gt(t,e){if(("constructor"!==e||"function"!==typeof t[e])&&"__proto__"!=e)return t[e]}var yt=function(t){var e=0,n=0;return function(){var r=F(),i=16-(r-n);if(n=r,i>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}(dt);function bt(t,e){return t===e||t!==t&&e!==e}var At=st(function(){return arguments}())?st:function(t){return xt(t)&&k.call(t,"callee")&&!N.call(t,"callee")},wt=Array.isArray;function Et(t){return null!=t&&Pt(t.length)&&!Rt(t)}var Tt=G||function(){return!1};function Rt(t){if(!Lt(t))return!1;var e=at(t);return e==a||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}function Pt(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=i}function Lt(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function xt(t){return null!=t&&"object"==typeof t}var kt=b?function(t){return function(e){return t(e)}}(b):function(t){return xt(t)&&Pt(t.length)&&!!l[at(t)]};function Ot(t){return Et(t)?K(t,!0):ut(t)}var Ct,St=(Ct=function(t,e,n){lt(t,e,n)},ft((function(t,e){var n=-1,r=e.length,i=r>1?e[r-1]:void 0,o=r>2?e[2]:void 0;for(i=Ct.length>3&&"function"==typeof i?(r--,i):void 0,o&&function(t,e,n){if(!Lt(n))return!1;var r=typeof e;return!!("number"==r?Et(n)&&pt(e,n.length):"string"==r&&e in n)&&bt(n[e],t)}(e[0],e[1],o)&&(i=r<3?void 0:i,r=1),t=Object(t);++n<r;){var a=e[n];a&&Ct(t,a,n,i)}return t})));function It(t){return t}t.exports=St},26528:function(t){"use strict";var e=self.fetch.bind(self);t.exports=e,t.exports.default=t.exports},14007:function(t,e,n){"use strict";n.d(e,{Z:function(){return p}});var r=n(2784),i=n(13980),o=n.n(i),a=n(76635),s=n(58451),c=n(93802),u=n(9772),l=(n(80150),n(2023),n(99404),n(48705),n(63396),n(32249),n(84714),n(67419),n(17807),n(20848)),f=(n(186),n(70833),n(60736));function d(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function h(t,e){return null!=e&&"undefined"!==typeof Symbol&&e[Symbol.hasInstance]?e[Symbol.hasInstance](t):t instanceof e}function m(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){d(t,e,n[e])}))}return t}function p(t){var e=t.adUnitUtils,n=t.config,i=t.mode,o=t.onAdViewed,d=t.onAdRender,p=t.getBFPLayout,v=void 0===p?null:p,g=t.stickyManager,y=void 0===g?null:g,b=t.className,A=void 0===b?"":b,w=t.customDisclosure,E=void 0===w?"":w,T=t.pixiedust,R=t.destroyAdsOnScroll,P=void 0!==R&&R,L=(0,r.useContext)(u.$),x=(0,r.useRef)(null),k=(0,r.useRef)(L),O=(0,r.useRef)(n),C=(0,r.useRef)(null),S=(0,r.useRef)(null),I=(0,r.useState)(null),j=I[0],M=I[1],_=(0,r.useState)(null),D=_[0],B=_[1],z=(0,r.useState)(!1),N=z[0],$=z[1],Z=(0,r.useState)(!1),U=Z[0],G=Z[1],V=(0,r.useCallback)((function(t){var n=t.config;C.current&&C.current.$destroy(),C.current=(0,c.Z)({template:e.getTemplate(n),target:x.current,props:m({slot:n},e.getWireframeOptions(n),{isListItem:!1,customDisclosure:E,solid:{isAny:f.Z.isAny}})})}),[]),F=(0,r.useCallback)((function(t){var n=t.context,r=t.config;C.current||V({context:n,config:r});var i=e.getComponent(r),o=S.current=new i(m({},n,{element:x.current.firstElementChild,config:r,getBFPLayout:v,trackingData:{pixiedust:T},stickyManager:y}));/^awareness/.test(r.adPos)&&(o.noLazyRendering=!0),/^sidebar/.test(r.adPos)&&(o.noLazyRendering=!0),o.init().catch((function(t){return h(t,s.x9)||h(t,s.$j)?t:Promise.reject(t)})),M(o.getEventId()),$(!1),B(null)}),[]),J=(0,r.useCallback)((function(){var t=S.current;t&&(t.destroy(),S.current=null),C.current&&(C.current.$destroy(),C.current=null),x.current&&(x.current.innerHTML="")}),[]);return(0,r.useEffect)((function(){return"disabled"!==L.status&&V({context:L,config:n}),"loaded"===L.status&&F({context:L,config:n},"didMount"),function(){J({context:L,config:n},"willUnmount")}}),[]),(0,r.useEffect)((function(){var t=function(){if(x.current&&x.current.getBoundingClientRect){var e=x.current.getBoundingClientRect(),r=e.bottom,i=e.height,o=(0,l.JB)(n);if(!isNaN(r)&&!isNaN(o)){var a=r<-o;if(G(a),a){var s=x.current.parentNode,c=document.createElement("div");s.replaceChild(c,x.current),c.style.height="".concat(i,"px"),c.appendChild(x.current),J(),window.removeEventListener("scroll",t)}}}};return P&&!U&&D&&"programmatic"===D.type&&"toolbar"!==n.adType&&"awareness"!==n.adType&&f.Z.isAny(["xs","sm"])&&window.addEventListener("scroll",t),function(){window.removeEventListener("scroll",t)}}),[D,P]),(0,r.useEffect)((function(){var t=!k.current||L.status!==k.current.status,e=!(0,a.isEqual)(n,O.current);(!S.current||t||e)&&(S.current&&J({context:k.current,config:O.current},"didUpdate"),"loaded"===L.status&&F({context:L,config:n},"didUpdate"),k.current=L,O.current=n)}),[L,n]),(0,r.useEffect)((function(){var t=function(t){var e=null;"isEmpty"in t?t.isEmpty&&(e="empty"):e=t.type,e&&B({type:e})};return j&&"loaded"===L.status?(L.eventBus.on("gpt:slotRenderEnded:".concat(j),t),L.eventBus.on("ad-content-ready:".concat(j),t),function(){L.eventBus.off("gpt:slotRenderEnded:".concat(j),t),L.eventBus.off("ad-content-ready:".concat(j),t)}):function(){}}),[j,L]),(0,r.useEffect)((function(){D&&"function"===typeof d&&d(D)}),[D,d]),(0,r.useEffect)((function(){var t=function(){$(!0)};return j&&"loaded"===L.status?(L.eventBus.on("ad-is-seen:".concat(j),t),function(){return L.eventBus.off("ad-is-seen:".concat(j),t)}):function(){}}),[j,L]),(0,r.useEffect)((function(){N&&"function"===typeof o&&o()}),[N,o]),(0,r.useEffect)((function(){return j&&"loaded"===L.status&&i&&"function"===typeof o?N?(o(),function(){}):"preload"===i?(L.eventBus.on("gpt:slotRenderEnded:".concat(j),o),function(){L.eventBus.off("gpt:slotRenderEnded:".concat(j),o)}):"active"===i?(L.eventBus.on("ad-is-seen:".concat(j),o),function(){L.eventBus.off("ad-is-seen:".concat(j),o)}):function(){}:function(){}}),[j,L,i,N,o]),(0,r.useEffect)((function(){S.current&&(S.current.context.stickyManager=y)}),[y]),r.createElement("div",{ref:x,className:"Ad Ad--".concat(n.adPos," ").concat(A)})}p.propTypes={adUnitUtils:o().object.isRequired,config:o().object.isRequired,mode:o().oneOf(["active","preload"]),onAdViewed:o().func,onAdRender:o().func,getBFPLayout:o().func,pixiedust:o().object,className:o().string,customDisclosure:o().string}},33565:function(t,e,n){"use strict";n.d(e,{Z:function(){return p}});var r=n(2784),i=n(13980),o=n.n(i),a=n(75951),s=n(48705),c=n(67419),u=n(36606),l=n(75127),f=n(9772);function d(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function h(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){d(t,e,n[e])}))}return t}var m={eventBus:new s.Z,env:{},localization:{edition:"en-us",language:"en",country:"us"},abeagle:{getExperimentVariant:function(){return Promise.resolve("control")},isOn:function(){return Promise.resolve(!1)}},tracking:{track:function(){},trackPrebidEvents:function(){}},gdpr:h({},l.Z),ccpa:{getConsentValue:function(){return a.ZP.framework.needsConsent()?a.ZP.rules.fetchCCPAValue():Promise.resolve(null)}}};function p(t){var e=t.pageId,n=t.getPageContext,i=t.pageLanguage,o=void 0===i?"en":i,a=t.adsEnabled,s=void 0===a||a,l=t.getPageTargeting,d=t.getSlotTargeting,p=t.children,v=(0,r.useState)({status:"loading"}),g=v[0],y=v[1],b=(0,r.useRef)(!1),A=(0,r.useRef)();return A.current&&e===A.current||(s&&"loading"!==g.status?y({status:"loading"}):s||"disabled"===g.status||y({status:"disabled"}),(0,u.m0)(o)),(0,r.useEffect)((function(){e!==A.current&&(A.current=e,s&&Promise.resolve(n()).then((function(t){A.current&&(Object.keys(m).forEach((function(e){e in t&&"eventBus"!==e&&Object.assign(m[e],t[e])})),c.Z.configure({customTargetingPage:l,customTargetingSlot:d}),b.current?c.Z.reset():(c.Z.inject(m),c.Z.start().then((function(){b.current=!0}))),y(h({},m,t,{pageId:e,status:"loaded"})))})))}),[e,s,n,l,d]),r.createElement(f.$.Provider,{value:g},p)}p.propTypes={pageId:o().oneOfType([o().string,o().number]).isRequired,getPageContext:o().func.isRequired,pageLanguage:o().string,adsEnabled:o().bool,getPageTargeting:o().func,getSlotTargeting:o().func,children:o().oneOfType([o().arrayOf(o().node),o().node])}},9772:function(t,e,n){"use strict";n.d(e,{$:function(){return r}});var r=n(2784).createContext()},56758:function(t,e,n){"use strict";n.d(e,{vc:function(){return o},PP:function(){return r.P},J7:function(){return r.J}});var r=n(3843),i={awareness:{adType:"awareness",adPos:"awareness-bp",wid:42,size:[r.J.NATIVE,r.J.NATIVE_COMPLEX_6,r.J.NATIVE_COMPLEX_RECTANGLE,r.J.FLUID,r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD],viewability:"low"},bigstory:{adType:"post",adPos:"bigstory-bp",wid:13,size:[r.J.NATIVE,r.J.FLUID,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_VERTICAL],viewability:"low"},comment1:{adType:"comment",adPos:"comment1",wid:71101,size:[[320,50]],renderLookahead:"x0.175",viewability:"low"},comment2:{adType:"comment",adPos:"comment2",wid:71102,size:[[320,50]],renderLookahead:"x0.175",viewability:"low"},comment3:{adType:"comment",adPos:"comment3",wid:71103,size:[[320,50]],renderLookahead:"x0.175",viewability:"low"},comment4:{adType:"comment",adPos:"comment4",wid:71104,size:[[320,50]],renderLookahead:"x0.175",viewability:"low"},comment5:{adType:"comment",adPos:"comment5",wid:71105,size:[[320,50]],renderLookahead:"x0.175",viewability:"low"},comment6:{adType:"comment",adPos:"comment6",wid:71106,size:[[320,50]],renderLookahead:"x0.175",viewability:"low"},comment7:{adType:"comment",adPos:"comment7",wid:71107,size:[[320,50]],renderLookahead:"x0.175",viewability:"low"},comment8:{adType:"comment",adPos:"comment8",wid:71108,size:[[320,50]],renderLookahead:"x0.175",viewability:"low"},comment9:{adType:"comment",adPos:"comment9",wid:71109,size:[[320,50]],renderLookahead:"x0.175",viewability:"low"},comment10:{adType:"comment",adPos:"comment10",wid:71110,size:[[320,50]],renderLookahead:"x0.175",viewability:"low"},"comment-infinite":{adType:"comment",adPos:"comment-infinite",wid:71111,size:[[320,50]],renderLookahead:"x0.175",isInfinite:!0,viewability:"low"},pixel:{adType:"post",adPos:"pixel",wid:0,size:[r.J.RESEARCH_PIXEL]},popular_pixel:{adType:"post",adPos:"popular_pixel",wid:15,size:[r.J.NATIVE]},"promo-bottom1":{adType:"ex",adPos:"promo-bottom1",wid:200,renderLookahead:"x0.175",size:[r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.FLUID],viewability:"low"},"promo-bottom2":{adType:"ex",adPos:"promo-bottom2",wid:201,renderLookahead:"x0.175",size:[r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.FLUID],viewability:"low"},"promo-bottom3":{adType:"ex",adPos:"promo-bottom3",wid:202,renderLookahead:"x0.175",size:[r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.FLUID],viewability:"low"},"promo-bottom4":{adType:"ex",adPos:"promo-bottom4",wid:203,renderLookahead:"x0.175",size:[r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.FLUID],viewability:"low"},"promo-bottom5":{adType:"ex",adPos:"promo-bottom5",wid:204,renderLookahead:"x0.175",size:[r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.FLUID],viewability:"low"},"promo-bottom6":{adType:"ex",adPos:"promo-bottom6",wid:205,renderLookahead:"x0.175",size:[r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.FLUID],viewability:"low"},"promo-bottom7":{adType:"ex",adPos:"promo-bottom7",wid:206,renderLookahead:"x0.175",size:[r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.FLUID],viewability:"low"},"promo-bottom8":{adType:"ex",adPos:"promo-bottom8",wid:207,renderLookahead:"x0.175",size:[r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.FLUID],viewability:"low"},"promo-bottom9":{adType:"ex",adPos:"promo-bottom9",wid:208,renderLookahead:"x0.175",size:[r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.FLUID],viewability:"low"},"promo-bottom10":{adType:"ex",adPos:"promo-bottom10",wid:209,renderLookahead:"x0.175",size:[r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.FLUID],viewability:"low"},"promo-bottom-infinite":{adType:"ex",adPos:"promo-bottom-infinite",wid:2e3,renderLookahead:"x0.175",size:[r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.FLUID],isInfinite:!0,viewability:"low"},"promo-inline1":{adType:"ex",adPos:"promo-inline1",wid:210,size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.NATIVE,r.J.FLUID,r.J.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline2":{adType:"ex",adPos:"promo-inline2",wid:211,renderLookahead:"x2",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.NATIVE,r.J.FLUID,r.J.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline3":{adType:"ex",adPos:"promo-inline3",wid:212,renderLookahead:"x2",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.NATIVE,r.J.FLUID,r.J.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline4":{adType:"ex",adPos:"promo-inline4",wid:213,renderLookahead:"x2",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.NATIVE,r.J.FLUID,r.J.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline5":{adType:"ex",adPos:"promo-inline5",wid:214,renderLookahead:"x2",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.NATIVE,r.J.FLUID,r.J.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline6":{adType:"ex",adPos:"promo-inline6",wid:215,renderLookahead:"x3",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.NATIVE,r.J.FLUID,r.J.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline7":{adType:"ex",adPos:"promo-inline7",wid:216,renderLookahead:"x3",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.NATIVE,r.J.FLUID,r.J.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline8":{adType:"ex",adPos:"promo-inline8",wid:217,renderLookahead:"x3",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.NATIVE,r.J.FLUID,r.J.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline9":{adType:"ex",adPos:"promo-inline9",wid:218,renderLookahead:"x3",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.NATIVE,r.J.FLUID,r.J.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline10":{adType:"ex",adPos:"promo-inline10",wid:219,renderLookahead:"x3",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.NATIVE,r.J.FLUID,r.J.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline-infinite":{adType:"ex",adPos:"promo-inline-infinite",wid:2100,renderLookahead:"x3",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE_COMPLEX_100,r.J.NATIVE,r.J.FLUID,r.J.AMAZON_OUTSTREAM],isInfinite:!0,viewability:"high"},"promo-quiz-inline1":{adType:"ex",adPos:"promo-inline1",wid:210,size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD,r.J.NATIVE,r.J.FLUID],viewability:"high"},"promo-quiz-inline2":{adType:"ex",adPos:"promo-inline2",wid:211,renderLookahead:"x2",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline3":{adType:"ex",adPos:"promo-inline3",wid:212,renderLookahead:"x2",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline4":{adType:"ex",adPos:"promo-inline4",wid:213,renderLookahead:"x2",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline5":{adType:"ex",adPos:"promo-inline5",wid:214,renderLookahead:"x2",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline6":{adType:"ex",adPos:"promo-inline6",wid:215,renderLookahead:"x3",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline7":{adType:"ex",adPos:"promo-inline7",wid:216,renderLookahead:"x3",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline8":{adType:"ex",adPos:"promo-inline8",wid:217,renderLookahead:"x3",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline9":{adType:"ex",adPos:"promo-inline9",wid:218,renderLookahead:"x3",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline10":{adType:"ex",adPos:"promo-inline10",wid:219,renderLookahead:"x3",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline-infinite":{adType:"ex",adPos:"promo-inline-infinite",wid:2100,renderLookahead:"x3",size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_LEADERBOARD],isInfinite:!0,viewability:"high"},"promo-ai-quiz":{adType:"post",adPos:"aiquizzes",wid:2101,renderLookahead:"x1",size:[r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,[400,300]],viewability:"high"},"promo1-wide":{adType:"ex",adPos:"promo1-wide",wid:220,size:[r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.FLUID],viewability:"high"},"promo2-wide":{adType:"ex",adPos:"promo2-wide",wid:221,size:[r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.FLUID],viewability:"high"},"promo3-wide":{adType:"ex",adPos:"promo3-wide",wid:222,size:[r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.FLUID],viewability:"high"},"promo4-wide":{adType:"ex",adPos:"promo4-wide",wid:223,size:[r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.FLUID],viewability:"high"},"promo5-wide":{adType:"ex",adPos:"promo5-wide",wid:224,size:[r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.FLUID],viewability:"high"},"promo6-wide":{adType:"ex",adPos:"promo6-wide",wid:225,size:[r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.FLUID],viewability:"high"},"promo7-wide":{adType:"ex",adPos:"promo7-wide",wid:226,size:[r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.FLUID],viewability:"high"},"promo8-wide":{adType:"ex",adPos:"promo8-wide",wid:227,size:[r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.FLUID],viewability:"high"},"promo9-wide":{adType:"ex",adPos:"promo9-wide",wid:228,size:[r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.FLUID],viewability:"high"},"promo10-wide":{adType:"ex",adPos:"promo10-wide",wid:229,size:[r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.FLUID],viewability:"high"},"promo-wide-infinite":{adType:"ex",adPos:"promo-wide-infinite",wid:2200,size:[r.J.PROGRAMMATIC_SUPER_LEADERBOARD,r.J.PROGRAMMATIC_BILLBOARD,r.J.PROGRAMMATIC_LEADERBOARD,r.J.FLUID],isInfinite:!0,viewability:"high"},"sidebar1-bp":{adType:"post",adPos:"sidebar1-bp",wid:1301,size:[r.J.NATIVE,r.J.FLUID,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_VERTICAL,r.J.AMAZON_OUTSTREAM],viewability:"high"},"sidebar2-bp":{adType:"post",adPos:"sidebar2-bp",wid:1302,size:[r.J.NATIVE,r.J.FLUID,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_VERTICAL,r.J.AMAZON_OUTSTREAM],viewability:"high"},"sidebar3-bp":{adType:"post",adPos:"sidebar3-bp",wid:1303,size:[r.J.NATIVE,r.J.FLUID,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_VERTICAL,r.J.AMAZON_OUTSTREAM],viewability:"high"},"sidebar4-bp":{adType:"post",adPos:"sidebar4-bp",wid:1304,size:[r.J.NATIVE,r.J.FLUID,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_VERTICAL,r.J.AMAZON_OUTSTREAM],viewability:"high"},"sidebar5-bp":{adType:"post",adPos:"sidebar5-bp",wid:1305,size:[r.J.NATIVE,r.J.FLUID,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_VERTICAL,r.J.AMAZON_OUTSTREAM],viewability:"high"},"sidebar6-bp":{adType:"post",adPos:"sidebar6-bp",wid:1306,size:[r.J.NATIVE,r.J.FLUID,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_VERTICAL,r.J.AMAZON_OUTSTREAM],viewability:"high"},"sidebar7-bp":{adType:"post",adPos:"sidebar7-bp",wid:1307,size:[r.J.NATIVE,r.J.FLUID,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_VERTICAL,r.J.AMAZON_OUTSTREAM],viewability:"high"},"sidebar8-bp":{adType:"post",adPos:"sidebar8-bp",wid:1308,size:[r.J.NATIVE,r.J.FLUID,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_VERTICAL,r.J.AMAZON_OUTSTREAM],viewability:"high"},"sidebar9-bp":{adType:"post",adPos:"sidebar9-bp",wid:1309,size:[r.J.NATIVE,r.J.FLUID,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_VERTICAL,r.J.AMAZON_OUTSTREAM],viewability:"high"},"sidebar10-bp":{adType:"post",adPos:"sidebar10-bp",wid:1310,size:[r.J.NATIVE,r.J.FLUID,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_VERTICAL,r.J.AMAZON_OUTSTREAM],viewability:"high"},"sidebar-bp-infinite":{adType:"post",adPos:"sidebar-bp-infinite",wid:13e3,size:[r.J.FLUID,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_VERTICAL,r.J.AMAZON_OUTSTREAM],viewability:"high",isInfinite:!0},"story-bpage":{adType:"post",adPos:"story-bpage",wid:130,size:[r.J.NATIVE,r.J.FLUID,r.J.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J.PROGRAMMATIC_SMARTPHONE_BANNER],viewability:"low",isMobile:!0},"story-bpage-desktop":{adType:"post",adPos:"story-bpage",wid:9,size:[r.J.NATIVE,r.J.FLUID,r.J.PROGRAMMATIC_LEADERBOARD],viewability:"low"},subbuzz:{adType:"awareness",adPos:"subbuzz",wid:420,size:[r.J.NATIVE]},toolbar:{adType:"toolbar",adPos:"tb",wid:52,size:[r.J.PROGRAMMATIC_SMARTPHONE_BANNER,r.J.PROGRAMMATIC_SMARTPHONE_BANNER_WIDE,r.J.COMPLEX_XS_BANNER],viewability:"low"}},o=Object.freeze(i)},93468:function(t,e,n){"use strict";n.d(e,{Z:function(){return l}});var r=n(24027),i=n(39901),o=n(37101);function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t){for(var e,n,r=[t[0]],a={},c=0;c<r.length;c+=1)a=(0,i.f0)(a,r[c]);return e=new o.Z({props:a}),{c:function(){(0,i.YC)(e.$$.fragment)},m:function(t,r){(0,i.ye)(e,t,r),n=!0},p:function(t,n){var o=1&s(n,1)[0]?(0,i.Lo)(r,[(0,i.gC)(t[0])]):{};e.$set(o)},i:function(t){n||((0,i.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,i.et)(e.$$.fragment,t),n=!1},d:function(t){(0,i.vp)(e,t)}}}function u(t,e,n){return t.$$set=function(t){n(0,e=(0,i.f0)((0,i.f0)({},e),(0,i.Jv)(t)))},[e=(0,i.Jv)(e)]}var l=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,u,c,i.N8,{}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},37101:function(t,e,n){"use strict";n.d(e,{Z:function(){return f}});var r=n(24027),i=n(39901),o=n(11734);function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t){var e;return{c:function(){e=(0,i.bi)("path"),(0,i.Lj)(e,"d","M374 243.082l-211.818-211.542c-2.727-2.724-6.818-4.54-10.455-4.54-3.636 0-7.727 1.816-10.455 4.54l-22.727 22.698c-2.727 2.724-4.545 6.809-4.545 10.441 0 3.632 1.818 7.717 4.545 10.441l178.637 178.404-178.637 178.404c-2.727 2.724-4.545 6.809-4.545 10.441 0 4.086 1.818 7.717 4.545 10.441l22.727 22.698c2.727 2.724 6.818 4.54 10.455 4.54 3.636 0 7.727-1.816 10.455-4.54l211.818-211.542c2.727-2.724 4.545-6.809 4.545-10.441 0-3.632-1.818-7.717-4.545-10.441z")},m:function(t,n){(0,i.$T)(t,e,n)},p:i.ZT,d:function(t){t&&(0,i.og)(e)}}}function u(t){var e,n;return e=new o.Z({props:{title:t[0],extraClasses:t[1],$$slots:{default:[c]},$$scope:{ctx:t}}}),{c:function(){(0,i.YC)(e.$$.fragment)},m:function(t,r){(0,i.ye)(e,t,r),n=!0},p:function(t,n){var r=s(n,1)[0],i={};1&r&&(i.title=t[0]),2&r&&(i.extraClasses=t[1]),4&r&&(i.$$scope={dirty:r,ctx:t}),e.$set(i)},i:function(t){n||((0,i.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,i.et)(e.$$.fragment,t),n=!1},d:function(t){(0,i.vp)(e,t)}}}function l(t,e,n){var r=e.title,i=e.extraClasses;return t.$$set=function(t){"title"in t&&n(0,r=t.title),"extraClasses"in t&&n(1,i=t.extraClasses)},[r,i]}var f=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,l,u,i.N8,{title:0,extraClasses:1}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},11734:function(t,e,n){"use strict";n.d(e,{Z:function(){return u}});var r=n(24027),i=n(39901);function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function a(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return o(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return o(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t){var e,n,r,o,s=t[5].default,c=(0,i.nu)(s,t,t[4],null);return{c:function(){e=(0,i.bi)("svg"),n=(0,i.bi)("title"),r=(0,i.fL)(t[0]),c&&c.c(),(0,i.Lj)(n,"id",t[1]),(0,i.Lj)(e,"aria-labelledby",t[1]),(0,i.Lj)(e,"class",t[2]),(0,i.Lj)(e,"role","img"),(0,i.Lj)(e,"viewBox","0 0 512 512"),(0,i.Lj)(e,"style",t[3])},m:function(t,a){(0,i.$T)(t,e,a),(0,i.R3)(e,n),(0,i.R3)(n,r),c&&c.m(e,null),o=!0},p:function(t,u){var l=a(u,1)[0];(!o||1&l)&&(0,i.rT)(r,t[0]),(!o||2&l)&&(0,i.Lj)(n,"id",t[1]),c&&c.p&&(!o||16&l)&&(0,i.km)(c,s,t,t[4],o?(0,i.u2)(s,t[4],l,null):(0,i.VO)(t[4]),null),(!o||2&l)&&(0,i.Lj)(e,"aria-labelledby",t[1]),(!o||4&l)&&(0,i.Lj)(e,"class",t[2]),(!o||8&l)&&(0,i.Lj)(e,"style",t[3])},i:function(t){o||((0,i.Ui)(c,t),o=!0)},o:function(t){(0,i.et)(c,t),o=!1},d:function(t){t&&(0,i.og)(e),c&&c.d(t)}}}function c(t,e,n){var r=e.$$slots,i=void 0===r?{}:r,o=e.$$scope,a=e.title,s=e.a11yLabel,c=e.extraClasses,u=e.inlineStyles;return t.$$set=function(t){"title"in t&&n(0,a=t.title),"a11yLabel"in t&&n(1,s=t.a11yLabel),"extraClasses"in t&&n(2,c=t.extraClasses),"inlineStyles"in t&&n(3,u=t.inlineStyles),"$$scope"in t&&n(4,o=t.$$scope)},[a,s,c,u,o,i]}var u=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,c,s,i.N8,{title:0,a11yLabel:1,extraClasses:2,inlineStyles:3}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},35473:function(t,e,n){"use strict";n.d(e,{Z:function(){return jt}});var r=n(24027),i=n(60736),o=n(80150),a=n(2023),s=(n(37083),n(99404),n(58451),n(48705),n(63396),n(32249),n(84714),n(76635)),c=(n(67419),n(17807),n(20848)),u=n(17748),l=n(70833),f=n(70753),d=n(41871),h=n(2282),m=n(54341),p=function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).eligibleFormats=["bfp_spotlight_unit","bfp_spotlight_ad","bfp_spotlight_turnkey","bfp_spotlight_newsletter","bfp_spotlight_v3","bfp_display_card_quiz","bfp_spotlight_poll"],t}return(0,r.XW)(e,t),(0,r.qH)(e)}(n(82545).f);function v(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var g=0,y={},b=function(){function t(e){var n=e.throttleTimeout,r=e.parent,i=e.initialTrigger,o=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.callbacks={},this.add=this.add.bind(this),this.remove=this.remove.bind(this),this.triggerEvents=["scroll","resize"],i&&this.triggerEvents.push("load"),this.target=r||window,this._triggerThrottled=(0,s.throttle)((function(){return o.trigger()}),n),this.triggerEvents.forEach((function(t){return o.target.addEventListener(t,o._triggerThrottled)}))}var e,n,r;return e=t,(n=[{key:"add",value:function(t){return this.callbacks[++g]=t,g}},{key:"remove",value:function(t){delete this.callbacks[t]}},{key:"trigger",value:function(t){if(this.callbacks.hasOwnProperty(t))this.callbacks[t]();else for(var e in this.callbacks)this.callbacks.hasOwnProperty(e)&&this.callbacks[e]()}},{key:"destroy",value:function(){var t=this;this.triggerEvents.forEach((function(e){return t.target.removeEventListener(e,t._triggerThrottled)})),delete this.target,delete this.callbacks,delete this._triggerThrottled,delete this.add,delete this.remove}}])&&v(e.prototype,n),r&&v(e,r),t}(),A=function(t){var e=t.throttleTimeout,n=void 0===e?350:e,r=t.parent,i=t.callback,o=t.initialTrigger,a=void 0===o||o,s=y[n];s||(s=new b({throttleTimeout:n,parent:r,initialTrigger:a}),y[n]=s);var c=s.add(i);return a&&s.trigger(c),c},w=function(t){for(var e in y)y.hasOwnProperty(e)&&y[e].remove(t)},E=n(34686);function T(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function R(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function P(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){R(t,e,n[e])}))}return t}var L="stick-to-top",x="".concat(L,"--init"),k=("".concat(L,"--state"),function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.context=e,this.element=e.getElement();this.stickyConfig=P({},{breakpoints:["ALL"],positionMode:"top",directions:["up","down"]},(e.getConfig()||{}).stickToTop),this.isInitialized=!1,this.canStick=!0,this.isFixed=!1,this.pageScrollPosition=window.scrollY,this.onmessage=R({},(0,E.MA)(x,this.element),this.initSticky)}var e,n,r;return e=t,n=[{key:"stickyRegistryOpts",get:function(){return{priority:"priority"in this.stickyConfig?this.stickyConfig.priority:this.context.stickyRegistry.defaultPriorities.normal}}},{key:"init",value:function(){this.stickyConfig.initOnMsg||this.initSticky()}},{key:"initSticky",value:function(t){if(!this.isInitialized){this.isInitialized=!0,this.stickyConfig=P({},this.stickyConfig,t),this.element.classList.add("xs-relative","sticky");var e=this.filler=document.createElement("div");e.style.height="".concat(this.element.offsetHeight,"px"),e.className="sticky-filler xs-static xs-hide",this.element.insertAdjacentElement("afterend",e),this._checkEligibilityBound=this.checkEligibility.bind(this),-1===this.stickyConfig.breakpoints.indexOf("ALL")&&(this.context.eventManager.on("match",this._checkEligibilityBound),i.Z.breakpointObserver.subscribe(this.context.eventManager)),this.checkEligibility(),this._adjustFixedPositionBound=this.adjustFixedPosition.bind(this),this.context.stickyRegistry.subscribe(this._adjustFixedPositionBound)}}},{key:"manageSticky",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.force,n=void 0!==e&&e,r=t.stickMod,i=void 0===r?0:r,o=t.unstickMod,a=void 0===o?0:o;if(this.canStick){var s=this.getPosition().inViewport,c=window.scrollY,u=c-this.pageScrollPosition>=10,l=this.pageScrollPosition-c>=10,f=!1;(u&&this.stickyConfig.directions.includes("down")||l&&this.stickyConfig.directions.includes("up"))&&(f=!0),(!this.isFixed||n)&&s<this.fixAt+i&&f?this.stick():(this.isFixed||n)&&s>=this.fixAt+a?this.unstick():!this.isFixed&&!n||f||!l&&!u||this.unstick(),this.pageScrollPosition=c}}},{key:"adjustFixedPosition",value:function(t){if(!(t.priority<this.stickyRegistryOpts.priority)&&this.canStick){var e=this.context.stickyRegistry.getAvailableTop(this.element,this.stickyRegistryOpts);e!==this.fixAt&&(this.fixAt=e,this.manageSticky({force:!0}))}}},{key:"checkEligibility",value:function(){var t,e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).breakpoint;if(this.stickyConfig.breakpoints.indexOf("ALL")>-1?t=!0:(e||(e=i.Z.getBreakPoint()),t=this.stickyConfig.breakpoints.indexOf(e)>-1),t){var n=this;this.canStick=!0,this.fixAt=this.context.stickyRegistry.getAvailableTop(this.element,this.stickyRegistryOpts),this.scrollListenerId=A({throttleTimeout:150,callback:function(){return n.manageSticky()}})}else this.canStick=!1,w(this.scrollListenerId),this.unstick()}},{key:"getPosition",value:function(){var t=(this.isFixed?this.filler:this.element).getBoundingClientRect().top;return{inViewport:t,inDocument:t+window.pageYOffset}}},{key:"setPosition",value:function(t){if("translate"===this.stickyConfig.positionMode){var e="translateY(".concat(t,"px)");this.element.style.WebkitTransform=e,this.element.style.transform=e}else this.element.style.top="".concat(t,"px")}},{key:"toggleFiller",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"stick";"stick"===t&&this.filler?this.filler.classList.remove("xs-hide"):"unstick"===t&&this.filler&&this.filler.classList.add("xs-hide")}},{key:"stick",value:function(){this.isFixed=!0,this.toggleFiller("stick"),this.element.classList.add("xs-fixed","sticky--fixed"),this.setPosition(this.fixAt),this.context.stickyRegistry.add(this.element,this.stickyRegistryOpts)}},{key:"unstick",value:function(){this.isFixed=!1,this.toggleFiller("unstick"),this.element.classList.remove("xs-fixed","sticky--fixed"),this.setPosition(0),this.context.stickyRegistry.remove(this.element)}},{key:"destroy",value:function(){this.unstick(),this.context.stickyRegistry.remove(this.element),this.context.stickyRegistry.unsubscribe(this._adjustFixedPositionBound),w(this.scrollListenerId),this.context.eventManager.off("match",this._checkEligibilityBound),i.Z.breakpointObserver.unsubscribe(this.context.eventManager),this.filler&&this.filler.parentElement.removeChild(this.filler),delete this.onmessage,delete this.element,delete this.filler}}],n&&T(e.prototype,n),r&&T(e,r),t}()),O=n(11608),C=n(93802),S=n(39901),I=n(17480),j=n(93468);function M(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function _(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return M(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return M(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(t){var e,n;return e=new j.Z({}),{c:function(){(0,S.YC)(e.$$.fragment)},m:function(t,r){(0,S.ye)(e,t,r),n=!0},p:S.ZT,i:function(t){n||((0,S.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,S.et)(e.$$.fragment,t),n=!1},d:function(t){(0,S.vp)(e,t)}}}function B(t){var e,n,r,i,o,a;return{c:function(){e=(0,S.bi)("svg"),n=(0,S.bi)("title"),r=(0,S.fL)("Close"),i=(0,S.bi)("path"),o=(0,S.Dh)(),(a=(0,S.bG)("div")).textContent="".concat((0,I.U6)("CLOSE")),(0,S.Lj)(i,"d","M30.3 10.5l-2.8-2.8-8.5 8.5-8.5-8.5-2.8 2.8 8.5 8.5-8.5 8.5 2.8 2.8 8.5-8.5 8.5 8.5 2.8-2.8-8.5-8.5z"),(0,S.Lj)(e,"viewBox","0 0 38 38"),(0,S.Lj)(e,"xmlns","http://www.w3.org/2000/svg"),(0,S.Lj)(a,"class","mobile-close")},m:function(t,s){(0,S.$T)(t,e,s),(0,S.R3)(e,n),(0,S.R3)(n,r),(0,S.R3)(e,i),(0,S.$T)(t,o,s),(0,S.$T)(t,a,s)},p:S.ZT,i:S.ZT,o:S.ZT,d:function(t){t&&(0,S.og)(e),t&&(0,S.og)(o),t&&(0,S.og)(a)}}}function z(t){var e,n,r,i,o,a,s,c=function(t,e){return"x"===t[2]?0:1},u=[B,D],l=[];return n=c(t),r=l[n]=u[n](t),{c:function(){e=(0,S.bG)("div"),r.c(),(0,S.Lj)(e,"role","button"),(0,S.Lj)(e,"aria-label","Dismiss sticky ad"),(0,S.Lj)(e,"aria-controls",t[1]),(0,S.Lj)(e,"class",i="ad-awareness__dismiss ad-awareness__dismiss--"+t[2])},m:function(r,i){(0,S.$T)(r,e,i),l[n].m(e,null),o=!0,a||(s=(0,S.oL)(e,"click",(function(){(0,S.sB)(t[0])&&t[0].apply(this,arguments)})),a=!0)},p:function(a,s){var f=_(s,1)[0],d=n;(n=c(t=a))===d?l[n].p(t,f):((0,S.dv)(),(0,S.et)(l[d],1,1,(function(){l[d]=null})),(0,S.gb)(),(r=l[n])?r.p(t,f):(r=l[n]=u[n](t)).c(),(0,S.Ui)(r,1),r.m(e,null)),(!o||2&f)&&(0,S.Lj)(e,"aria-controls",t[1]),(!o||4&f&&i!==(i="ad-awareness__dismiss ad-awareness__dismiss--"+t[2]))&&(0,S.Lj)(e,"class",i)},i:function(t){o||((0,S.Ui)(r),o=!0)},o:function(t){(0,S.et)(r),o=!1},d:function(t){t&&(0,S.og)(e),l[n].d(),a=!1,s()}}}function N(t,e,n){var r=e.onClick,i=e.owner,o=e.style,a=void 0===o?"x":o;return t.$$set=function(t){"onClick"in t&&n(0,r=t.onClick),"owner"in t&&n(1,i=t.owner),"style"in t&&n(2,a=t.style)},[r,i,a]}var $=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,S.S1)(n,t,N,z,S.N8,{onClick:0,owner:1,style:2}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(S.f_);function Z(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function U(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function G(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){U(t,e,n[e])}))}return t}function V(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||J(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function F(t){return function(t){if(Array.isArray(t))return Z(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||J(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function J(t,e){if(t){if("string"===typeof t)return Z(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Z(t,e):void 0}}var H={detectTransitionEnd:O.SU},W={default:{maxScrollDepth:3e3,positionMode:"translate",priority:"medium",breakpoints:["ALL"]}},q=function(t){function e(t){var n,i=t.element,a=t.stickyRegistry;return(0,r.PA)(this,e),n=(0,r.$w)(this,e,[{getElement:function(){return i},getConfig:function(){return{stickToTop:G({},W.default)}},eventManager:null,stickyRegistry:a}]),o.ZP.apply(n,arguments),n.context.eventManager=n.privateEvents,n._fixAt=0,n.mediaHeight=0,n}return(0,r.XW)(e,t),(0,r.qH)(e)}(k);Object.assign(q.prototype,o.ZP.prototype),q.prototype.constructor=q;var Y=function(t){function e(){var t;(0,r.PA)(this,e);for(var n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];return t=(0,r.$w)(this,e,F(i)),(0,r._x)(t,"_onDismiss",(function(){t.userDismissed=!0,setTimeout((function(){return t.stickyHide()}),500)})),t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"setup",value:function(){var t=this;Object.keys(this.onmessage||{}).forEach((function(e){var n=t.onmessage[e];t.onPublicEvent(e,n.bind(t))})),delete this.onmessage,this.reset(),this.dismissTimer=0,this.unstickAfter=this.getPosition().inDocument+this.stickyConfig.maxScrollDepth;var n=this.onPublicEvent("ad-reveal:".concat(this.config.wid),(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=e.type,i=e.isSticky,o=void 0===i||i;n(),"spotlight"===r&&o?((0,C.Z)({template:$,target:t.element,props:{style:"x",onClick:t._onDismiss,owner:t.element.id}}),t.stickyConfig.breakpoints=["md","lg"],t.initSticky()):t.cleanup()}));return(0,r.Vx)(e,"setup",this,3)([])}},{key:"initSticky",value:function(){(0,r.Vx)(e,"initSticky",this,3)(arguments),this.mediaHeight=this.element.querySelector(".js-ad-media").clientHeight,this.filler.className="sticky-filler xs-fixed",this.filler.style.height=this.mediaHeight+90}},{key:"getPosition",value:function(){var t=(0,r.Vx)(e,"getPosition",this,3)([]);return t.inViewport=t.inViewport+.5*this.mediaHeight,t}},{key:"cleanup",value:function(){this.destroy()}},{key:"reset",value:function(){this.isHidden=!1,this.userDismissed=!1,this.element.classList.remove("sticky--show","sticky--hide"),this.element.classList.add("sticky--first-time")}},{key:"manageSticky",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.force,i=void 0!==n&&n;(0,r.Vx)(e,"manageSticky",this,3)([{force:i,stickMod:45,unstickMod:0}]),this.isFixed&&!this.userDismissed&&(document.querySelector(".scroll-up-mobile-nav--visible")?this.element.classList.add("mobile-share--adjust"):this.element.classList.remove("mobile-share--adjust"),window.pageYOffset<=this.unstickAfter?this.stickyShow():this.stickyHide())}},{key:"toggleFiller",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"stick";"stick"===t&&this.filler?(this.filler.classList.add("xs-static"),this.filler.classList.remove("xs-fixed")):"unstick"===t&&this.filler&&(this.filler.classList.remove("xs-static"),this.filler.classList.add("xs-fixed"))}},{key:"stick",value:function(){(0,r.Vx)(e,"stick",this,3)(arguments),this.isHidden=!0,this.element.querySelector(".js-ad-media").style.minHeight="";var t=document.querySelector("#js-header-container .js-sticky-container");if(t){var n="fixed"===getComputedStyle(t.firstElementChild).position||null;this.fixCheck=n?t.clientHeight:0}else this.fixCheck=0;this.context.stickyRegistry.remove(this.element),this.eventBus.trigger("ad-stick-".concat(this.config.wid))}},{key:"unstick",value:function(){(0,r.Vx)(e,"unstick",this,3)(arguments),this.eventBus.trigger("ad-unstick-".concat(this.config.wid)),this.reset()}},{key:"stickyShow",value:function(){var t=this;this.isFixed&&this.isHidden&&(this.isHidden=!1,this.element.classList.remove("sticky--hide"),H.detectTransitionEnd(this.element,{properties:["any"]}).then((function(){t.context.stickyRegistry.add(t.element,t.stickyRegistryOpts)})),this.context.env.isBFN&&H.detectTransitionEnd(this.element,{properties:["height"]}).then((function(){t.context.stickyRegistry.add(t.element,t.stickyRegistryOpts)})),this.element.classList.add("sticky--show"))}},{key:"stickyHide",value:function(){var t=this;this.isFixed&&!this.isHidden&&(this.isHidden=!0,this.element.classList.remove("sticky--show"),H.detectTransitionEnd(this.element,{properties:["any"]}).then((function(){t.context.stickyRegistry.remove(t.element)})),this.element.classList.add("sticky--hide"))}},{key:"destroy",value:function(){k.prototype.destroy.call(this),(0,r.Vx)(e,"destroy",this,3)([])}}])}(q),X=function(t){function e(){var t,n,i,a,s,c,u;return(0,r.PA)(this,e),u=(0,r.$w)(this,e,arguments),(0,r._x)(u,"onStickyManagerEvent",(function(t){var e=t.shouldStick;return u.isSticky=!1,u.lifecycleState!==o.Sx.DESTROYED&&(u.isDismissed?(u.stickyElement.classList.remove("sticky"),!1):e?(u.isSticky=!0,u.stickyElement.classList.add("sticky"),u.stickyElement.classList.remove("sticky--dismissed"),u.context.stickyRegistry.add(u.stickyElement,u.stickyRegistryOpts),!0):(u.dismiss(),!1))})),(0,r._x)(u,"adjustTopPos",(function(){var t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{priority:u.stickyRegistryOpts.priority}).priority;if(!(t<u.stickyRegistryOpts.priority)){var e=u.context.stickyRegistry.getAvailableTop(u.stickyElement,u.stickyRegistryOpts);u.stickyHelper.style.top="".concat(e-1,"px"),u.stickyElement.style.top="".concat(e,"px"),u.stickyObserver&&u.stickyObserver.disconnect();var n=u.stickyObserver=new IntersectionObserver((function(t){var e=V(t,1)[0];u.onStickyChange({isStatic:1===e.intersectionRatio})}),{rootMargin:"-".concat(e,"px 0px 0px 0px"),threshold:1});n.observe(u.stickyHelper)}})),(0,r._x)(u,"onUserDismiss",(function(){u.isDismissed=!0,u.stickToBottom?u.eventBus.trigger("sticky-ad-user-dismiss-bottom:".concat(u.config.wid)):u.eventBus.trigger("sticky-ad-user-dismiss-top:".concat(u.config.wid)),u.dismiss().then((function(){u.notifyStickyManager()}))})),u.isSticky=!1,u.isDismissed=!1,u.stickToBottom=(null===(t=u.config)||void 0===t||null===(n=t.stickyOptions)||void 0===n?void 0:n.stickToBottom)||!1,u.alwaysStickToBottom=(null===(i=u.config)||void 0===i||null===(a=i.stickyOptions)||void 0===a?void 0:a.alwaysStickToBottom)||!1,u.pageHasBottomFixedItems=(null===(s=u.config)||void 0===s||null===(c=s.stickyOptions)||void 0===c?void 0:c.pageHasBottomFixedItems)||!1,u.stickyRegistryOpts={priority:u.context.stickyRegistry.defaultPriorities.medium},u}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"isEnabled",value:function(){var t,e,n,r,o="tasty"===(null===this||void 0===this||null===(t=this.context)||void 0===t||null===(e=t.env)||void 0===e?void 0:e.destination)&&"home"===(null===this||void 0===this||null===(n=this.context)||void 0===n||null===(r=n.env)||void 0===r?void 0:r.pageSection);return i.Z.isAny(["xs"])&&this.context.stickyManager&&!o}},{key:"setup",value:function(){var t=this,n=this.onPublicEvent("ad-reveal:".concat(this.config.wid),(function(){var e,r;n(),(0,C.Z)({template:$,target:t.element,props:{style:"caret",onClick:t.onUserDismiss,owner:t.element.id}});var i=t.stickyElement=t.stickToBottom&&!(null===t||void 0===t||null===(e=t.context)||void 0===e||null===(r=e.env)||void 0===r?void 0:r.isBPage)?t.element:t.element.closest(".Ad")||t.element.closest(".ad-wireframe-wrapper")||t.element,o=t.stickyHelper=document.createElement("div");o.classList.add("ad-awareness-anchor","js-ad-awareness-anchor"),i.insertAdjacentElement("beforebegin",o),t.adjustTopPos(),t.context.stickyRegistry.subscribe(t.adjustTopPos),t.notifyStickyManager()}));return this.onPublicEvent("post-message:jumbotron:".concat(this.config.wid),(function(e){t.element.classList.add("ad-jumbotron"),"inited"===e.action&&n()})),(0,r.Vx)(e,"setup",this,3)([])}},{key:"onStickyChange",value:function(t){var e;t.isStatic?(this.isDismissed=!1,(null===(e=this.stickyElement.classList)||void 0===e?void 0:e.remove)&&this.stickyElement.classList.remove("sticky--fixed","sticky--dismissed","sticky--bottom","sticky--bottom-offset"),this.notifyStickyManager()):this.isSticky&&(this.stickyElement.classList.add("sticky--fixed"),this.stickToBottom&&this.stickyElement.classList.add("sticky--bottom","Ad--awareness--sticky"),this.pageHasBottomFixedItems&&this.stickyElement.classList.add("sticky--bottom-offset"))}},{key:"notifyStickyManager",value:function(){this.context.stickyManager.notify("top",{canStick:!this.isDismissed,callback:this.onStickyManagerEvent})}},{key:"dismiss",value:function(){var t=this;return new Promise((function(e){H.detectTransitionEnd(t.stickyElement,{properties:[t.alwaysStickToBottom?"bottom":"top"]}).then(e),t.stickyElement.classList.remove("sticky--bottom","sticky--bottom-offset"),t.stickyElement.classList.add("sticky--dismissed"),t.context.stickyRegistry.remove(t.stickyElement),t.isSticky=!1}))}},{key:"destroy",value:function(){this.isDismissed=!0,this.stickyElement&&(this.stickyElement.classList.remove("sticky","sticky--fixed","sticky--dismissed","sticky--bottom","sticky--bottom-offset"),this.stickyElement.style.top="",delete this.stickyElement),this.stickyHelper&&(this.stickyHelper.remove(),delete this.stickyHelper),this.stickyObserver&&(this.stickyObserver.disconnect(),delete this.stickyObserver),(0,r.Vx)(e,"destroy",this,3)([]),this.notifyStickyManager()}}])}(o.ZP),Q=function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"setup",value:function(){var t=this;Object.keys(this.onmessage||{}).forEach((function(e){var n=t.onmessage[e];t.onPublicEvent(e,n.bind(t))})),delete this.onmessage;var n=this.config.wid;return this.onPublicEvent("gpt:impressionViewable:".concat(n),(function(){setTimeout((function(){t.element.classList.add("temp-sticky--hide")}),2e3)})),(0,r.Vx)(e,"setup",this,3)([])}}])}(o.ZP),K=n(10533);function tt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function et(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return tt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return tt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nt(t){var e,n=t[7].default,r=(0,S.nu)(n,t,t[6],null);return{c:function(){r&&r.c()},m:function(t,n){r&&r.m(t,n),e=!0},p:function(t,i){r&&r.p&&(!e||64&i)&&(0,S.km)(r,n,t,t[6],e?(0,S.u2)(n,t[6],i,null):(0,S.VO)(t[6]),null)},i:function(t){e||((0,S.Ui)(r,t),e=!0)},o:function(t){(0,S.et)(r,t),e=!1},d:function(t){r&&r.d(t)}}}function rt(t){var e,n,r=t[7].default,i=(0,S.nu)(r,t,t[6],null);return{c:function(){e=(0,S.bG)("a"),i&&i.c(),(0,S.Lj)(e,"href",t[2]),(0,S.Lj)(e,"target",t[0]),(0,S.Lj)(e,"class",t[1])},m:function(t,r){(0,S.$T)(t,e,r),i&&i.m(e,null),n=!0},p:function(t,o){i&&i.p&&(!n||64&o)&&(0,S.km)(i,r,t,t[6],n?(0,S.u2)(r,t[6],o,null):(0,S.VO)(t[6]),null),(!n||4&o)&&(0,S.Lj)(e,"href",t[2]),(!n||1&o)&&(0,S.Lj)(e,"target",t[0]),(!n||2&o)&&(0,S.Lj)(e,"class",t[1])},i:function(t){n||((0,S.Ui)(i,t),n=!0)},o:function(t){(0,S.et)(i,t),n=!1},d:function(t){t&&(0,S.og)(e),i&&i.d(t)}}}function it(t){var e,n,r,i,o=function(t,e){return t[2]?0:1},a=[rt,nt],s=[];return e=o(t),n=s[e]=a[e](t),{c:function(){n.c(),r=(0,S.cS)()},m:function(t,n){s[e].m(t,n),(0,S.$T)(t,r,n),i=!0},p:function(t,i){var c=et(i,1)[0],u=e;(e=o(t))===u?s[e].p(t,c):((0,S.dv)(),(0,S.et)(s[u],1,1,(function(){s[u]=null})),(0,S.gb)(),(n=s[e])?n.p(t,c):(n=s[e]=a[e](t)).c(),(0,S.Ui)(n,1),n.m(r.parentNode,r))},i:function(t){i||((0,S.Ui)(n),i=!0)},o:function(t){(0,S.et)(n),i=!1},d:function(t){s[e].d(t),t&&(0,S.og)(r)}}}function ot(t,e,n){var r,i,o=e.$$slots,a=void 0===o?{}:o,s=e.$$scope,c=e.advertiserUrl,u=e.clickThroughUrl,l=e.dfpClickTracker,f=e.target,d=e.linkClasses,h=c&&c.length>0,m=u&&u.length>0;return(h||m)&&(!c&&u&&(c=u),i=c,r=(0,I.TV)(i,l||"")),t.$$set=function(t){"advertiserUrl"in t&&n(3,c=t.advertiserUrl),"clickThroughUrl"in t&&n(4,u=t.clickThroughUrl),"dfpClickTracker"in t&&n(5,l=t.dfpClickTracker),"target"in t&&n(0,f=t.target),"linkClasses"in t&&n(1,d=t.linkClasses),"$$scope"in t&&n(6,s=t.$$scope)},[f,d,r,c,u,l,s,a]}var at=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,S.S1)(n,t,ot,it,S.N8,{advertiserUrl:3,clickThroughUrl:4,dfpClickTracker:5,target:0,linkClasses:1}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(S.f_);function st(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function ct(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return st(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return st(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ut(t,e,n){var r=t.slice();return r[9]=e[n],r}function lt(t){var e,n,r;return{c:function(){e=(0,S.bG)("div"),n=(0,S.bG)("iframe"),(0,S.Lj)(n,"title","Advertisement media"),(0,S.Jn)(n.src,r=t[3].src)||(0,S.Lj)(n,"src",r),(0,S.Lj)(n,"class","js-ad-thumbnail"),(0,S.Lj)(e,"class","ad-slot-media-embed-wrapper")},m:function(t,r){(0,S.$T)(t,e,r),(0,S.R3)(e,n)},p:function(t,e){8&e&&!(0,S.Jn)(n.src,r=t[3].src)&&(0,S.Lj)(n,"src",r)},i:S.ZT,o:S.ZT,d:function(t){t&&(0,S.og)(e)}}}function ft(t){for(var e,n,r=[t[4],{target:"advertiser"}],i={$$slots:{default:[ht]},$$scope:{ctx:t}},o=0;o<r.length;o+=1)i=(0,S.f0)(i,r[o]);return e=new at({props:i}),{c:function(){(0,S.YC)(e.$$.fragment)},m:function(t,r){(0,S.ye)(e,t,r),n=!0},p:function(t,n){var i=16&n?(0,S.Lo)(r,[(0,S.gC)(t[4]),r[1]]):{};136&n&&(i.$$scope={dirty:n,ctx:t}),e.$set(i)},i:function(t){n||((0,S.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,S.et)(e.$$.fragment,t),n=!1},d:function(t){(0,S.vp)(e,t)}}}function dt(t){for(var e,n=["1:1","4:3","16:9"],r=[],i=0;i<3;i+=1)r[i]=mt(ut(t,n,i));return{c:function(){for(var t=0;t<3;t+=1)r[t].c();e=(0,S.cS)()},m:function(t,n){for(var i=0;i<3;i+=1)r[i]&&r[i].m(t,n);(0,S.$T)(t,e,n)},p:function(t,i){if(0&i){var o;for(n=["1:1","4:3","16:9"],o=0;o<3;o+=1){var a=ut(t,n,o);r[o]?r[o].p(a,i):(r[o]=mt(a),r[o].c(),r[o].m(e.parentNode,e))}for(;o<3;o+=1)r[o].d(1)}},i:S.ZT,o:S.ZT,d:function(t){(0,S.RM)(r,t),t&&(0,S.og)(e)}}}function ht(t){var e,n;return{c:function(){e=(0,S.bG)("img"),(0,S.Lj)(e,"alt","Advertisement media"),(0,S.Jn)(e.src,n=t[3].src)||(0,S.Lj)(e,"src",n),(0,S.Lj)(e,"class","js-ad-thumbnail ad-image-thumbnail")},m:function(t,n){(0,S.$T)(t,e,n)},p:function(t,r){8&r&&!(0,S.Jn)(e.src,n=t[3].src)&&(0,S.Lj)(e,"src",n)},d:function(t){t&&(0,S.og)(e)}}}function mt(t){var e,n;return{c:function(){e=(0,S.bG)("img"),(0,S.Jn)(e.src,n=I.xw[t[9]])||(0,S.Lj)(e,"src",n),(0,S.Lj)(e,"alt",""),(0,S.Lj)(e,"aria-hidden","true"),(0,S.Lj)(e,"class","ad-hidden ratio--"+t[9])},m:function(t,n){(0,S.$T)(t,e,n)},p:S.ZT,d:function(t){t&&(0,S.og)(e)}}}function pt(t){var e,n,r,i,o,a,s,c,u,l=function(t,e){return t[3].type===K.T.GAM_VIDEO?0:t[3].type===K.T.IMAGE?1:t[3].type===K.T.EMBED?2:-1},f=[dt,ft,lt],d=[];~(a=l(t))&&(s=d[a]=f[a](t));var h=t[6].default,m=(0,S.nu)(h,t,t[7],null);return{c:function(){e=(0,S.bG)("div"),n=(0,S.bG)("div"),r=(0,S.bG)("div"),o=(0,S.Dh)(),s&&s.c(),c=(0,S.Dh)(),m&&m.c(),(0,S.Lj)(r,"id",i="div-gpt-ad-"+t[0]),(0,S.Lj)(r,"class","ad-slot"),(0,S.Lj)(n,"class","ad-slot-media-inner js-ad-media"),(0,S.Lj)(e,"class","ad-slot-media js-slot-media"),(0,S.VH)(e,"ad-slot-sticky",t[1]),(0,S.VH)(e,"debug-pixel",t[2])},m:function(t,i){(0,S.$T)(t,e,i),(0,S.R3)(e,n),(0,S.R3)(n,r),(0,S.R3)(n,o),~a&&d[a].m(n,null),(0,S.R3)(e,c),m&&m.m(e,null),u=!0},p:function(t,o){var c=ct(o,1)[0];(!u||1&c&&i!==(i="div-gpt-ad-"+t[0]))&&(0,S.Lj)(r,"id",i);var p=a;(a=l(t))===p?~a&&d[a].p(t,c):(s&&((0,S.dv)(),(0,S.et)(d[p],1,1,(function(){d[p]=null})),(0,S.gb)()),~a?((s=d[a])?s.p(t,c):(s=d[a]=f[a](t)).c(),(0,S.Ui)(s,1),s.m(n,null)):s=null),m&&m.p&&(!u||128&c)&&(0,S.km)(m,h,t,t[7],u?(0,S.u2)(h,t[7],c,null):(0,S.VO)(t[7]),null),(!u||2&c)&&(0,S.VH)(e,"ad-slot-sticky",t[1]),(!u||4&c)&&(0,S.VH)(e,"debug-pixel",t[2])},i:function(t){u||((0,S.Ui)(s),(0,S.Ui)(m,t),u=!0)},o:function(t){(0,S.et)(s),(0,S.et)(m,t),u=!1},d:function(t){t&&(0,S.og)(e),~a&&d[a].d(),m&&m.d(t)}}}function vt(t,e,n){var r=e.$$slots,i=void 0===r?{}:r,o=e.$$scope,a=e.context,s=e.wid,c=e.stickyViewPixel,u=void 0!==c&&c,l=a.eventBus,f=!1,d={};return(0,S.H3)((function(){l.once("ad-tpl:".concat(s),(function(t){n(3,d=t)}));var t=window.location.search;n(2,f=t.indexOf("adlib-debug-mode")>-1&&t.indexOf("pixel")>-1)})),t.$$set=function(t){n(4,e=(0,S.f0)((0,S.f0)({},e),(0,S.Jv)(t))),"context"in t&&n(5,a=t.context),"wid"in t&&n(0,s=t.wid),"stickyViewPixel"in t&&n(1,u=t.stickyViewPixel),"$$scope"in t&&n(7,o=t.$$scope)},e=(0,S.Jv)(e),[s,u,f,d,e,a,i,o]}var gt=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,S.S1)(n,t,vt,pt,S.N8,{context:5,wid:0,stickyViewPixel:1}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(S.f_);function yt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function bt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return yt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return yt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function At(t){for(var e,n,r,i=[t[0]],o={},a=0;a<i.length;a+=1)o=(0,S.f0)(o,i[a]);return n=new gt({props:o}),{c:function(){e=(0,S.bG)("div"),(0,S.YC)(n.$$.fragment),(0,S.Lj)(e,"class","awareness-inner")},m:function(t,i){(0,S.$T)(t,e,i),(0,S.ye)(n,e,null),r=!0},p:function(t,e){var r=1&bt(e,1)[0]?(0,S.Lo)(i,[(0,S.gC)(t[0])]):{};n.$set(r)},i:function(t){r||((0,S.Ui)(n.$$.fragment,t),r=!0)},o:function(t){(0,S.et)(n.$$.fragment,t),r=!1},d:function(t){t&&(0,S.og)(e),(0,S.vp)(n)}}}function wt(t,e,n){return t.$$set=function(t){n(0,e=(0,S.f0)((0,S.f0)({},e),(0,S.Jv)(t)))},[e=(0,S.Jv)(e)]}var Et=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,S.S1)(n,t,wt,At,S.N8,{}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(S.f_);function Tt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Rt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return Tt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Tt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Pt(t){for(var e,n,r=[t[0]],i={},o=0;o<r.length;o+=1)i=(0,S.f0)(i,r[o]);return e=new Et({props:i}),{c:function(){(0,S.YC)(e.$$.fragment)},m:function(t,r){(0,S.ye)(e,t,r),n=!0},p:function(t,n){var i=1&Rt(n,1)[0]?(0,S.Lo)(r,[(0,S.gC)(t[0])]):{};e.$set(i)},i:function(t){n||((0,S.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,S.et)(e.$$.fragment,t),n=!1},d:function(t){(0,S.vp)(e,t)}}}function Lt(t,e,n){return t.$$set=function(t){n(0,e=(0,S.f0)((0,S.f0)({},e),(0,S.Jv)(t)))},[e=(0,S.Jv)(e)]}var xt=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,S.S1)(n,t,Lt,Pt,S.N8,{}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(S.f_),kt=n(15332),Ot=n(40026);function Ct(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function St(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Ct(t,e,n[e])}))}return t}function It(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}var jt=function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).config.stickyOptions={stickToBottom:!1,alwaysStickToBottom:!1,pageHasBottomFixedItems:!1},Ot.Eh.indexOf(t.stack.toLowerCase())>-1?(t.element.classList.add("temp-sticky"),t.plugins.add(Q)):t.plugins.add(Y),(0,kt.R)(t.context)||t.plugins.add(X),t.context.env.isAdPost()&&t.addFormat(d.Z.formatType,d.Z),t.addFormat(p.formatType,p),t.isInView=!0,t.refreshTimer=null,t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"unitTemplate",get:function(){return xt}},{key:"isMwSticky",get:function(){return i.Z.isXsmall()&&this.context.stickyManager}},{key:"setupSizes",value:function(){(0,r.Vx)(e,"setupSizes",this,3)([]);var t=i.Z.getBreakPoint(),n=this.context.env.adSizes;this.isMwSticky&&this.excludeSize(this.context.env.adSizes.NATIVE,this.context.env.adSizes.FLUID,this.context.env.adSizes.NATIVE_COMPLEX_6),(this.context.env.hasQuiz||"xs"===t||"sm"===t)&&this.excludeSize(n.NATIVE_COMPLEX_RECTANGLE),"sm"===t?this.filterProgrammaticSizes({max:n.PROGRAMMATIC_LEADERBOARD}):"md"!==t&&"lg"!==t||(this.filterProgrammaticSizes({min:n.PROGRAMMATIC_LEADERBOARD}),this.excludeSize(n.NATIVE_COMPLEX_6))}},{key:"setup",value:function(){try{var t=this,n=t.config.wid;t.onPublicEvent("ad-content-ready:".concat(n),(function(e){var n,r;(t.element.classList.add("ad--rendered"),"spotlight"===e.type||"bfp_spotlight_unit"===e.bfpFormatName)&&((e.isJumbo||(null===(n=e.bfpData)||void 0===n||null===(r=n.content)||void 0===r?void 0:r.isJumbo))&&t.element.classList.add("ad-spotlight--jumbo"))})),t.onPublicEvent("ad-wireframe-fadein-finish:".concat(n),(function(){t.eventBus.trigger("ad-reveal:".concat(n),{type:t.context.ad.type,isSticky:t.context.ad.isSticky||!1})})),t.onPublicEvent("ad-refresh:".concat(n),(function(){t.element.classList.contains("ad-jumbotron")&&t.element.classList.remove("ad-jumbotron")}));var o,a,s=i.Z.isXsmall(),c=t.context.env.hasQuiz,u=t.isMwSticky||c&&s;if(t.isMwSticky)t.noLazyRendering=!0,t.config.stickyOptions.stickToBottom="tasty"===t.context.env.destination&&(null===t||void 0===t||null===(o=t.context)||void 0===o||null===(a=o.env)||void 0===a?void 0:a.isVideoRecipePage),(t.context.env.isBPage&&t.context.env.hasConnatixVideo||"buzzfeed"===t.context.env.destination&&"en-us"===t.context.env.localization.edition&&"home"===t.context.env.pageId&&"home"===t.context.env.pageName||"topic"===t.context.env.pageName||"standard_page"===t.context.env.pageName)&&(t.config.stickyOptions.stickToBottom=t.config.stickyOptions.alwaysStickToBottom=!0);(0,l.A)("info","lifecycle","awareness isRefreshable = ".concat(u)),u&&(t.config.refreshOptions=St({},f.i7));var d={"homepage-swap-refresh":void 0,"feed-ui-swap-refresh":void 0};return It(Promise.all(Object.keys(d).map((p=function(e){return It(t.context.abeagle.getExperimentVariant(e,{rejectErrors:!1,defaultVariantIfUnbucketed:"off"}),(function(t){d[e]=t}))},function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];try{return Promise.resolve(p.apply(this,t))}catch(n){return Promise.reject(n)}}))),(function(){var n=Object.values(d).some((function(t){return"on"===t}));return(0,l.A)("info","lifecycle","xxx isSwapRefresh = ".concat(n)),n?(t.config.refreshOptions=St({},f.i7),t.config.refreshOptions.inViewSeconds=15,t.plugins.add(m.Z)):t.plugins.add(h.Z),(0,r.Vx)(e,"setup",t,3)([])}))}catch(v){return Promise.reject(v)}var p}}])}(a.Z.withMixins(c.Z_,u.Q))},44399:function(t,e,n){"use strict";n.d(e,{Z:function(){return h}});var r=n(24027),i=n(39901),o=n(5263),a=n(6888);function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function c(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return s(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t){var e,n,r,a,s,c,u;return n=new o.Z({props:{config:t[0]}}),{c:function(){e=(0,i.bG)("div"),(0,i.YC)(n.$$.fragment),r=(0,i.Dh)(),a=(0,i.bG)("div"),(0,i.Lj)(a,"class","awareness-bg-animator"),(0,i.Lj)(e,"id",s="BF_WIDGET_"+t[0].wid),(0,i.Lj)(e,"data-module","ad-awareness"),(0,i.Lj)(e,"class",c="ad-awareness ad-flexible js-ad ad-awareness--full-width "+(t[1]?"":"ad-fade ad-animated ad-fadedown ad-awareness--legacy-wf")+" "+(t[4]?"ad-awareness--".concat(t[4]):"")+" "+(t[0].zone1?"ad-dest--".concat(t[0].zone1):"")),(0,i.Lj)(e,"data-bfa","@l:Awareness;")},m:function(t,o){(0,i.$T)(t,e,o),(0,i.ye)(n,e,null),(0,i.R3)(e,r),(0,i.R3)(e,a),u=!0},p:function(t,r){var o={};1&r&&(o.config=t[0]),n.$set(o),(!u||1&r&&s!==(s="BF_WIDGET_"+t[0].wid))&&(0,i.Lj)(e,"id",s),(!u||19&r&&c!==(c="ad-awareness ad-flexible js-ad ad-awareness--full-width "+(t[1]?"":"ad-fade ad-animated ad-fadedown ad-awareness--legacy-wf")+" "+(t[4]?"ad-awareness--".concat(t[4]):"")+" "+(t[0].zone1?"ad-dest--".concat(t[0].zone1):"")))&&(0,i.Lj)(e,"class",c)},i:function(t){u||((0,i.Ui)(n.$$.fragment,t),u=!0)},o:function(t){(0,i.et)(n.$$.fragment,t),u=!1},d:function(t){t&&(0,i.og)(e),(0,i.vp)(n)}}}function l(t){var e,n;return e=new a.Z({props:{hasWireframe:t[1],width:t[2],height:t[3],cssClasses:f,$$slots:{default:[u]},$$scope:{ctx:t}}}),{c:function(){(0,i.YC)(e.$$.fragment)},m:function(t,r){(0,i.ye)(e,t,r),n=!0},p:function(t,n){var r=c(n,1)[0],i={};2&r&&(i.hasWireframe=t[1]),4&r&&(i.width=t[2]),8&r&&(i.height=t[3]),51&r&&(i.$$scope={dirty:r,ctx:t}),e.$set(i)},i:function(t){n||((0,i.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,i.et)(e.$$.fragment,t),n=!1},d:function(t){(0,i.vp)(e,t)}}}var f=function(){return"ad-wireframe-wrapper--nostick ad-wireframe-wrapper--awareness"};function d(t,e,n){var r=e.slot,i=e.programmaticWireframes,o=e.wireframeWidth,a=e.wireframeHeight,s=e.page;return t.$$set=function(t){"slot"in t&&n(0,r=t.slot),"programmaticWireframes"in t&&n(1,i=t.programmaticWireframes),"wireframeWidth"in t&&n(2,o=t.wireframeWidth),"wireframeHeight"in t&&n(3,a=t.wireframeHeight),"page"in t&&n(4,s=t.page)},[r,i,o,a,s]}var h=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,d,l,i.N8,{slot:0,programmaticWireframes:1,wireframeWidth:2,wireframeHeight:3,page:4}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},82545:function(t,e,n){"use strict";n.d(e,{C:function(){return A},f:function(){return b}});var r=n(24027),i=n(42235),o=n(20238),a=n(83351),s=n(10533),c=n(32249),u=n(17480),l=n(39901);function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function d(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return f(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return f(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t){var e,n,r;return{c:function(){e=(0,l.bG)("div"),(n=(0,l.bG)("div")).textContent="".concat(u.U6.ADVERTISEMENT),(0,l.Lj)(n,"class","ad__disclosure--bfp js-ad-disclosure"),(0,l.Lj)(e,"id",r="ad-bfp-promo-"+t[0]),(0,l.Lj)(e,"class","ad-bfp-promo-container")},m:function(t,r){(0,l.$T)(t,e,r),(0,l.R3)(e,n)},p:function(t,n){1&d(n,1)[0]&&r!==(r="ad-bfp-promo-"+t[0])&&(0,l.Lj)(e,"id",r)},i:l.ZT,o:l.ZT,d:function(t){t&&(0,l.og)(e)}}}function m(t,e,n){var r=e.creativeId;return t.$$set=function(t){"creativeId"in t&&n(0,r=t.creativeId)},[r]}var p=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,l.S1)(n,t,m,h,l.N8,{creativeId:0}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(l.f_);function v(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function g(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){v(t,e,n[e])}))}return t}var y={loadScript:i.v},b=function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).template=p,t.setDebugSettings(),t.eligibleFormats=["bfp_display_card_quiz","bfp_product_carousel","bfp_product_subbuzz_ad","bfp_shopping_showcase","bfp_spotlight_ad","bfp_spotlight_unit","bfp_spotlight_turnkey","bfp_spotlight_newsletter","bfp_spotlight_v3","bfp_trending_products","bfp_shoppable_video_ad","bfp_bright_playlist"],t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"formatContainer",get:function(){return"bfp_spotlight_unit"===this.context.ad.bfpFormatName||"bfp_spotlight_ad"===this.context.ad.bfpFormatName?this.element.querySelector(".js-format-container"):this.element}},{key:"setDebugSettings",value:function(){if(this.debug={on:/\bads-debug-bfp\b/.test(window.location.search)},this.debug.on){var t=(0,o.jH)(window.location.search);this.debug.format=t["ads-debug-bfp"],this.debug.scriptHash=t.hash}}},{key:"getRenderKitURL",value:function(t,e){if(!this.debug||t!==this.debug.format)return e;if("dev"===this.debug.scriptHash){var n=t.replace(/_/g,"-"),r=t.replace(/^bfp_/,"");return"https://".concat(n,".dev.buzzfeed.io/static-assets/buzz-format-platform/").concat(r,"/js/render_kit.js")}return e.replace(/(render_kit.)([a-f0-9]{10,})(.js)/,"$1".concat(this.debug.scriptHash,"$3"))}},{key:"loadRenderKit",value:function(t,e){return y.loadScript(this.getRenderKitURL(t,e))}},{key:"buildFormat",value:function(){var t=arguments,n=this,i=this.context.ad,o=i.bfpFormatName,a=i.bfpScript,s=this.eligibleFormats.indexOf(o)>-1;return a&&o&&s?(this.bfpFormatName=o,this.loadRenderKit(o,a).then((function(){return(0,r.Vx)(e,"buildFormat",n,3)(t)})).then((function(){return n.initBFP()})).catch((function(t){console.error(t),n.destroy()}))):(this.destroy(),null)}},{key:"initBFP",value:function(){this.element.classList.add("ad-flexible--".concat(this.context.ad.bfpFormatName));var t=window[this.bfpFormatName],e={eventBus:this.context.eventBus,tracking:this.context.tracking,trackingData:this.context.trackingData||{},env:this.context.env||{},abeagle:this.context.abeagle||{}},n=this.bfpInstance=t.init({element:this.element.querySelector("#ad-bfp-promo-".concat(this.context.ad.creativeId)),rootElement:this.element,context:e,layout:this.context.getBFPLayout?this.context.getBFPLayout(this.bfpFormatName,this.config):null,slotConfig:this.config,gamData:this.context.ad,bfpData:this.context.ad.bfpData,config:{data:this.context.ad.bfpData,context:g({ad:this.context.ad},e)}});this.setupDFPClickTracker(),this.eventBus.trigger("ad-content-rendered:".concat(this.config.wid)),this.eventBus.trigger("bfp:init-done:".concat(this.config.wid),n)}},{key:"setupDFPClickTracker",value:function(){var t=this.context.ad,e=t.dfpClickTracker,n=t.clickThroughUrl;this.element.querySelectorAll("a").forEach((function(t){var r=t.getAttribute("href")?t.href:n;r.startsWith(e)||(t.href=(0,u.TV)(r,e),t.target="_blank")}))}},{key:"destroy",value:function(){this.eventBus.trigger("ad-content-error:".concat(this.config.wid)),(0,r.Vx)(e,"destroy",this,3)([]),this.bfpInstance&&this.bfpInstance.destroy&&(this.bfpInstance.destroy(),delete this.bfpInstance)}}])}(a.Z);(0,r._x)(b,"formatType","bfp_promo");var A=function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).nativeVideoEnded=!1,t.eligibleFormats=["bfp_native_instream_video","bfp_spotlight_unit"],t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"buildFormat",value:function(){this.context.ad.type="video",(0,r.Vx)(e,"buildFormat",this,3)([])}},{key:"initBFP",value:function(){(0,r.Vx)(e,"initBFP",this,3)([]),this.eventBus.trigger("ad-tpl:".concat(this.config.wid),{type:s.T.GAM_VIDEO})}},{key:"blocksSidebar",value:function(){var t="bfp_native_instream_video"===this.bfpFormatName,e=this.config.adPos.match(/sidebar/);return t&&e}},{key:"reportQuartile",value:function(t){if(!this.nativeVideoEnded){var e=this.config.wid;this.eventBus.trigger("ad-native-video-quartile:".concat(e)),4===t.quartile&&(this.blocksSidebar()||this.eventBus.trigger("ad-native-video-ended:".concat(e)),this.nativeVideoEnded=!0),this.eventBus.trigger("native-video-embed:playback-quartile:".concat(e),t)}}},{key:"reportPause",value:function(t){this.nativeVideoEnded||(this.blocksSidebar()||this.eventBus.trigger("ad-native-video-pause:".concat(this.config.wid)),this.eventBus.trigger("dfp-native-video:pause:".concat(this.config.wid),t))}}])}(b.withMixins(c.B));(0,r._x)(A,"formatType","bfp_video")},36610:function(t,e,n){"use strict";n.d(e,{Z:function(){return G}});var r=n(24027),i=n(80150),o=n(2023),a=(n(37083),n(99404),n(58451),n(48705),n(63396)),s=n(32249),c=(n(84714),n(76635),n(67419)),u=(n(17807),n(20848)),l=n(17748),f=(n(70833),n(60736)),d=n(34686),h=(n(93557),n(11608),n(81383)),m=n(99945);function p(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}function v(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}}var g=3e3,y=function(t){function e(){var t;return(0,r.PA)(this,e),t=(0,r.$w)(this,e,arguments),(0,r._x)(t,"_handleSlotRenderEnded",v((function(e){return p(c.Z.isProgrammaticSlot(e),(function(n){c.Z.isEmpty(e)?(t.ad.type="empty",t.dimensionsReady.resolve(),t.hasExposed.resolve()):n?(t.ad.type="programmatic",t.onPublicEvent("gpt:slotOnload:".concat(t.config.wid),t._handleSlotOnload),t._programmaticTimer=setTimeout(t._handleSlotOnload,1e3)):(t._thumbnailReady=(0,m.qB)(t.element),t._thumbnailReady.then(t.dimensionsReady.resolve))}))}))),(0,r._x)(t,"_handleSlotOnload",(function(){t._exposureTimer=setTimeout(t.hasExposed.resolve,g)})),t.isActive=!0,t.ad={type:null,height:0,duration:0},t.dimensionsReady=new d.BH,t.hasExposed=new d.BH,t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"isEnabled",value:function(){return Promise.all([(0,r.Vx)(e,"isEnabled",this,3)([]),this.config.observeDimensions])}},{key:"setup",value:function(){var t=this;this.dimensionsReady.then((function(){var e;return(e=t).reportDimensions.apply(e,arguments)})),this.hasExposed.then((function(){var e;return(e=t).reportHasExposed.apply(e,arguments)}));var n=this.config.wid;return this.onPublicEvent("ad-content-ready:".concat(n),(function(e){var n=e.type,r=e.typeHasVideo;("dfp_native_video"===n||r)&&(n="video"),t.ad.type=n})),this.onPublicEvent("ad-content-rendered:".concat(n),(function(){var e=t.ad.type;"video"!==e&&"bfp_promo"!==e&&t.dimensionsReady.resolve()})),this.onPublicEvent("ad-native-video-ready:".concat(n),(function(e){var n=e.duration;t.ad.duration=n,t.dimensionsReady.resolve()})),this.onPublicEvent("ad-native-video-pause:".concat(n),this.hasExposed.resolve),this.onPublicEvent("ad-native-video-ended:".concat(n),this.hasExposed.resolve),this.onPublicEvent("gpt:slotRenderEnded:".concat(n),this._handleSlotRenderEnded),this.onPublicEvent("bfp:init-done:".concat(n),(function(e){t._bfpInstance=e,t.dimensionsReady.resolve()})),(0,r.Vx)(e,"setup",this,3)([])}},{key:"reportDimensions",value:function(){if(this.isActive){var t=this.ad.type,e=this.context.ad,n=(void 0===e?{}:e).bfpFormatName;if("empty"!==t){var r=this.element.getBoundingClientRect().height;this.ad.height=r}"bfp_promo"===t&&this._bfpInstance&&this._bfpInstance.reportExposureEnded?this._bfpInstance.reportExposureEnded().then(this.hasExposed.resolve):"video"===t||"programmatic"===t||["bfp_display_card_quiz","bfp_product_carousel","bfp_product_subbuzz_ad","bfp_shoppable_video_ad","bfp_trending_products","bfp_bright_playlist"].includes(n)||(this._exposureTimer=setTimeout(this.hasExposed.resolve,g)),this.eventBus.trigger("ad-dimensions-ready:".concat(this.config.wid),this.ad)}}},{key:"reportHasExposed",value:function(){this.isActive&&(this.eventBus.trigger("ad-has-exposed:".concat(this.config.wid),this.ad),this.removeListeners())}},{key:"removeListeners",value:function(){this.eventBus.off("gpt:slotRenderEnded:".concat(this.config.wid),this._handleSlotRenderEnded),this.eventBus.off("gpt:slotOnload:".concat(this.config.wid),this._handleSlotOnload),this._thumbnailReady&&this._thumbnailReady.unsubscribe(),clearTimeout(this._programmaticTimer),clearTimeout(this._exposureTimer)}},{key:"destroy",value:function(){this.isActive=!1,this.removeListeners(),(0,r.Vx)(e,"destroy",this,3)([])}}])}(i.ZP),b=n(82545),A=n(29613),w=(n(186),n(39901)),E=n(32766),T=n(62751),R=n(17480);function P(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function L(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function x(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return P(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return P(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function k(t){for(var e,n,r,i,o=[t[0]],a={},s=0;s<o.length;s+=1)a=(0,w.f0)(a,o[s]);e=new E.Z({props:a});for(var c=[t[1]],u={},l=0;l<c.length;l+=1)u=(0,w.f0)(u,c[l]);return r=new T.Z({props:u}),{c:function(){(0,w.YC)(e.$$.fragment),n=(0,w.Dh)(),(0,w.YC)(r.$$.fragment)},m:function(t,o){(0,w.ye)(e,t,o),(0,w.$T)(t,n,o),(0,w.ye)(r,t,o),i=!0},p:function(t,n){var i=x(n,1)[0],a=1&i?(0,w.Lo)(o,[(0,w.gC)(t[0])]):{};e.$set(a);var s=2&i?(0,w.Lo)(c,[(0,w.gC)(t[1])]):{};r.$set(s)},i:function(t){i||((0,w.Ui)(e.$$.fragment,t),(0,w.Ui)(r.$$.fragment,t),i=!0)},o:function(t){(0,w.et)(e.$$.fragment,t),(0,w.et)(r.$$.fragment,t),i=!1},d:function(t){(0,w.vp)(e,t),t&&(0,w.og)(n),(0,w.vp)(r,t)}}}function O(t,e,n){var r=["dfpClickTracker","creativeId","ctaLinkOut","ctaBackground","ctaTextColor"],i=(0,w.q2)(e,r),o=e.dfpClickTracker,a=e.creativeId,s=e.ctaLinkOut,c=e.ctaBackground,u=e.ctaTextColor,l=s.length>0?(0,R.TV)(s,o):null,f={creativeId:a,ctaBackground:c,ctaTextColor:u},d=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){L(t,e,n[e])}))}return t}({creativeId:a,ctaUrl:l},i);return t.$$set=function(t){e=(0,w.f0)((0,w.f0)({},e),(0,w.Jv)(t)),n(8,i=(0,w.q2)(e,r)),"dfpClickTracker"in t&&n(2,o=t.dfpClickTracker),"creativeId"in t&&n(3,a=t.creativeId),"ctaLinkOut"in t&&n(4,s=t.ctaLinkOut),"ctaBackground"in t&&n(5,c=t.ctaBackground),"ctaTextColor"in t&&n(6,u=t.ctaTextColor)},[f,d,o,a,s,c,u]}var C=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,w.S1)(n,t,O,k,w.N8,{dfpClickTracker:2,creativeId:3,ctaLinkOut:4,ctaBackground:5,ctaTextColor:6}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(w.f_),S=function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"template",get:function(){return C}},{key:"getTemplateData",value:function(){var t=(0,r.Vx)(e,"getTemplateData",this,3)([]);return t.adPos=this.config.adPos,t}},{key:"buildFormat",value:function(){(0,r.Vx)(e,"buildFormat",this,3)([]),this.element.classList.add("ad-card"),c.Z.getSlotContainer(this.config.wid).classList.add("ad-display-card__vidslot")}}])}(s.t),I=n(41871),j=n(31675),M=n(66611);function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function D(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return _(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function B(t){for(var e,n,r,i,o,a,s,c,u,l,f,d,h=(0,R.eV)(t[0].title)+"",m=[t[2]],p={},v=0;v<m.length;v+=1)p=(0,w.f0)(p,m[v]);return l=new M.Z({props:p}),{c:function(){e=(0,w.bG)("div"),n=(0,w.bG)("a"),r=(0,w.bG)("div"),i=(0,w.bG)("img"),a=(0,w.Dh)(),s=(0,w.bG)("div"),u=(0,w.Dh)(),(0,w.YC)(l.$$.fragment),(0,w.Jn)(i.src,o=t[0].images.dblwide)||(0,w.Lj)(i,"src",o),(0,w.Lj)(i,"alt","Advertisement"),(0,w.Lj)(i,"class","js-ad-thumbnail"),(0,w.Lj)(r,"class","ad-buzz-thumbnail"),(0,w.Lj)(s,"class","title"),(0,w.Lj)(n,"href",c=t[0].url),(0,w.Lj)(n,"target","_blank"),(0,w.Lj)(n,"class","ad-buzz-title js-bfa-impression"),(0,w.Lj)(n,"data-bfa-impressions","true"),(0,w.Lj)(n,"data-bfa",t[1]),(0,w.Lj)(e,"class",f="ad-card ad-buzz-format ad-animated ad-wireframe--collapse-vertical ad-promotion--"+t[0].promotionType)},m:function(t,o){(0,w.$T)(t,e,o),(0,w.R3)(e,n),(0,w.R3)(n,r),(0,w.R3)(r,i),(0,w.R3)(n,a),(0,w.R3)(n,s),s.innerHTML=h,(0,w.R3)(e,u),(0,w.ye)(l,e,null),d=!0},p:function(t,r){var a=D(r,1)[0];(!d||1&a&&!(0,w.Jn)(i.src,o=t[0].images.dblwide))&&(0,w.Lj)(i,"src",o),(!d||1&a)&&h!==(h=(0,R.eV)(t[0].title)+"")&&(s.innerHTML=h),(!d||1&a&&c!==(c=t[0].url))&&(0,w.Lj)(n,"href",c);var u=4&a?(0,w.Lo)(m,[(0,w.gC)(t[2])]):{};l.$set(u),(!d||1&a&&f!==(f="ad-card ad-buzz-format ad-animated ad-wireframe--collapse-vertical ad-promotion--"+t[0].promotionType))&&(0,w.Lj)(e,"class",f)},i:function(t){d||((0,w.Ui)(l.$$.fragment,t),d=!0)},o:function(t){(0,w.et)(l.$$.fragment,t),d=!1},d:function(t){t&&(0,w.og)(e),(0,w.vp)(l)}}}function z(t,e,n){var r=e.buzz,i=e.slot,o=e.wid,a=(0,R.bb)(r,i.position),s="other"!==r.promotionType,c={wid:o,legacyBFA:a,isBranded:s,advertiser:r.advertiser,position:i.position};return t.$$set=function(t){"buzz"in t&&n(0,r=t.buzz),"slot"in t&&n(3,i=t.slot),"wid"in t&&n(4,o=t.wid)},[r,a,c,i,o]}var N=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,w.S1)(n,t,z,B,w.N8,{buzz:0,slot:3,wid:4}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(w.f_),$=function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"template",get:function(){return N}},{key:"getTemplateData",value:function(){var t=(0,r.Vx)(e,"getTemplateData",this,3)([]);return t.breakpoint=f.Z.getBreakPoint(),t}}])}(a.W);function Z(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function U(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Z(t,e,n[e])}))}return t}var G=function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).plugins.add(y),t.plugins.add(h.Z),t.addFormat(b.f.formatType,b.f),t.addFormat(b.C.formatType,b.C),t.addFormat(A.Z.formatType,A.Z),t.addFormat(S.formatType,S),t.addFormat($.formatType,$),t.addFormat(I.Z.formatType,I.Z),t.addFormat(/^((?!programmatic|impression_pixel|empty).)+$/,j.Z),t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"setup",value:function(){var t=this,n=this.config.wid;return this.onPublicEvent("ad-dimensions-ready:".concat(this.config.wid),(function(e){var r=e.data;t.eventBus.trigger("ad-bigstory-ready",U({},r,{wid:n,element:t.element}))})),this.onPublicEvent("ad-has-exposed:".concat(this.config.wid),(function(e){var r=e.data;t.eventBus.trigger("ad-bigstory-exposed",U({},r,{wid:n,element:t.element})),t.eventBus.trigger("ad-is-seen:".concat(t.config.adPos,"-").concat(t.instanceId))})),this.context.env.cmsTags.includes("commerce-partnership")&&(this.noLazyRendering=!0),(0,r.Vx)(e,"setup",this,3)([])}},{key:"render",value:function(){return(0,r.Vx)(e,"render",this,3)([])}},{key:"handleSlotRenderEnded",value:function(){(0,r.Vx)(e,"handleSlotRenderEnded",this,3)(arguments)}},{key:"handleAdContentLoaded",value:function(t){"programmatic"===t.type&&this.element.classList.remove("ad-card","card"),t.bfpFormatName&&(this.element.classList.remove("ad-card","card"),this.element.classList.add(t.bfpFormatName)),(0,r.Vx)(e,"handleAdContentLoaded",this,3)(arguments)}}])}(o.Z.withMixins(u.Z_,u.E6,l.Q))},54442:function(t,e,n){"use strict";n.d(e,{Z:function(){return d}});var r=n(24027),i=n(39901),o=n(5263);function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t){var e,n,r,a,s,c;return n=new o.Z({props:{config:t[0]}}),{c:function(){e=(0,i.bG)("div"),(0,i.YC)(n.$$.fragment),r=(0,i.Dh)(),(a=(0,i.bG)("div")).innerHTML='<div class="ad-wireframe-image ad-wireframe-image--wide"></div> \n    <div class="ad-wireframe-text xs-col-12 xs-mt1"></div> \n    <div class="ad-wireframe-text xs-col-10 xs-mt1"></div>',(0,i.Lj)(a,"class","xs-p2 ad-wireframe ad__wireframe-container"),(0,i.Lj)(e,"id",s="BF_WIDGET_"+t[0].wid),(0,i.Lj)(e,"data-module","ad-bigstory"),(0,i.Lj)(e,"class","clearfix xs-fit xs-relative card ad-bigstory ad-flexible"),(0,i.Lj)(e,"data-bfa","@l:Big-Story;")},m:function(t,o){(0,i.$T)(t,e,o),(0,i.ye)(n,e,null),(0,i.R3)(e,r),(0,i.R3)(e,a),c=!0},p:function(t,r){var o={};1&r&&(o.config=t[0]),n.$set(o),(!c||1&r&&s!==(s="BF_WIDGET_"+t[0].wid))&&(0,i.Lj)(e,"id",s)},i:function(t){c||((0,i.Ui)(n.$$.fragment,t),c=!0)},o:function(t){(0,i.et)(n.$$.fragment,t),c=!1},d:function(t){t&&(0,i.og)(e),(0,i.vp)(n)}}}function u(t){var e,n;return{c:function(){e=(0,i.bG)("div"),(0,i.Lj)(e,"id",n="BF_WIDGET_"+t[0].wid),(0,i.Lj)(e,"class","clearfix xs-fit xs-relative ad-wireframe ad-bigstory ad-bigstory--simple"),(0,i.Lj)(e,"data-bfa","@l:Big-Story;")},m:function(t,n){(0,i.$T)(t,e,n)},p:function(t,r){1&r&&n!==(n="BF_WIDGET_"+t[0].wid)&&(0,i.Lj)(e,"id",n)},i:i.ZT,o:i.ZT,d:function(t){t&&(0,i.og)(e)}}}function l(t){var e,n,r,o,a=function(t,e){return t[1]?0:1},l=[u,c],f=[];return e=a(t),n=f[e]=l[e](t),{c:function(){n.c(),r=(0,i.cS)()},m:function(t,n){f[e].m(t,n),(0,i.$T)(t,r,n),o=!0},p:function(t,o){var c=s(o,1)[0],u=e;(e=a(t))===u?f[e].p(t,c):((0,i.dv)(),(0,i.et)(f[u],1,1,(function(){f[u]=null})),(0,i.gb)(),(n=f[e])?n.p(t,c):(n=f[e]=l[e](t)).c(),(0,i.Ui)(n,1),n.m(r.parentNode,r))},i:function(t){o||((0,i.Ui)(n),o=!0)},o:function(t){(0,i.et)(n),o=!1},d:function(t){f[e].d(t),t&&(0,i.og)(r)}}}function f(t,e,n){var r=e.slot,i=e.plainWireframe,o=void 0===i||i;return t.$$set=function(t){"slot"in t&&n(0,r=t.slot),"plainWireframe"in t&&n(1,o=t.plainWireframe)},[r,o]}var d=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,f,l,i.N8,{slot:0,plainWireframe:1}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},66611:function(t,e,n){"use strict";n.d(e,{Z:function(){return d}});var r=n(24027),i=n(39901),o=n(17480);function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t){var e,n=(0,o.eV)(t[0].displayName)+"";return{c:function(){e=(0,i.bG)("span"),(0,i.Lj)(e,"class","text-gray-lightest")},m:function(t,r){(0,i.$T)(t,e,r),e.innerHTML=n},p:function(t,r){1&r&&n!==(n=(0,o.eV)(t[0].displayName)+"")&&(e.innerHTML=n)},d:function(t){t&&(0,i.og)(e)}}}function u(t){var e,n,r,a=(0,o.eV)(t[0].displayName)+"";return{c:function(){(e=(0,i.bG)("span")).textContent="".concat((0,o.U6)("PROMOTED_BY","Promoted by")),n=(0,i.Dh)(),r=(0,i.bG)("span"),(0,i.Lj)(e,"class","ad-disclosure-promo-text"),(0,i.Lj)(r,"class","ad-disclosure-name")},m:function(t,o){(0,i.$T)(t,e,o),(0,i.$T)(t,n,o),(0,i.$T)(t,r,o),r.innerHTML=a},p:function(t,e){1&e&&a!==(a=(0,o.eV)(t[0].displayName)+"")&&(r.innerHTML=a)},d:function(t){t&&(0,i.og)(e),t&&(0,i.og)(n),t&&(0,i.og)(r)}}}function l(t){var e,n,r,o,a,l,f,d,h,m,p,v,g=function(t,e){return t[3]?u:c},y=g(t),b=y(t);return{c:function(){e=(0,i.bG)("div"),n=(0,i.bG)("a"),r=(0,i.bG)("img"),f=(0,i.Dh)(),d=(0,i.bG)("div"),b.c(),(0,i.Jn)(r.src,o=t[0].avatar)||(0,i.Lj)(r,"src",o),(0,i.Lj)(r,"alt",a=t[0].displayName+" logo"),(0,i.Lj)(r,"class",l="ad-disclosure-image"+(t[3]?"":" wire-frame__avatar circle")),(0,i.Lj)(d,"id",h="ad-disclosure--"+t[1]),(0,i.Lj)(d,"class","ad-disclosure-text"),(0,i.Lj)(n,"href",m=t[0].url),(0,i.Lj)(n,"aria-labelledby",p="ad-disclosure--"+t[1]),(0,i.Lj)(n,"target","_blank"),(0,i.Lj)(n,"data-bfa-position",t[2]),(0,i.Lj)(n,"data-bfa",t[4]),(0,i.Lj)(n,"class","ad-disclosure-link"),(0,i.Lj)(e,"class",v="ad-disclosure "+(t[5]||""))},m:function(t,o){(0,i.$T)(t,e,o),(0,i.R3)(e,n),(0,i.R3)(n,r),(0,i.R3)(n,f),(0,i.R3)(n,d),b.m(d,null)},p:function(t,c){var u=s(c,1)[0];1&u&&!(0,i.Jn)(r.src,o=t[0].avatar)&&(0,i.Lj)(r,"src",o),1&u&&a!==(a=t[0].displayName+" logo")&&(0,i.Lj)(r,"alt",a),8&u&&l!==(l="ad-disclosure-image"+(t[3]?"":" wire-frame__avatar circle"))&&(0,i.Lj)(r,"class",l),y===(y=g(t))&&b?b.p(t,u):(b.d(1),(b=y(t))&&(b.c(),b.m(d,null))),2&u&&h!==(h="ad-disclosure--"+t[1])&&(0,i.Lj)(d,"id",h),1&u&&m!==(m=t[0].url)&&(0,i.Lj)(n,"href",m),2&u&&p!==(p="ad-disclosure--"+t[1])&&(0,i.Lj)(n,"aria-labelledby",p),4&u&&(0,i.Lj)(n,"data-bfa-position",t[2]),16&u&&(0,i.Lj)(n,"data-bfa",t[4]),32&u&&v!==(v="ad-disclosure "+(t[5]||""))&&(0,i.Lj)(e,"class",v)},i:i.ZT,o:i.ZT,d:function(t){t&&(0,i.og)(e),b.d()}}}function f(t,e,n){var r=e.advertiser,i=e.wid,o=e.position,a=e.isBranded,s=e.legacyBFA,c=e.extraClasses;return t.$$set=function(t){"advertiser"in t&&n(0,r=t.advertiser),"wid"in t&&n(1,i=t.wid),"position"in t&&n(2,o=t.position),"isBranded"in t&&n(3,a=t.isBranded),"legacyBFA"in t&&n(4,s=t.legacyBFA),"extraClasses"in t&&n(5,c=t.extraClasses)},[r,i,o,a,s,c]}var d=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,f,l,i.N8,{advertiser:0,wid:1,position:2,isBranded:3,legacyBFA:4,extraClasses:5}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},32766:function(t,e,n){"use strict";n.d(e,{Z:function(){return c}});var r=n(24027),i=n(39901),o=n(17480);function a(t){var e,n,r=(0,o.eV)(t[0])+"";return{c:function(){e=new i.FW(!1),n=(0,i.cS)(),e.a=n},m:function(t,o){e.m(r,t,o),(0,i.$T)(t,n,o)},p:i.ZT,i:i.ZT,o:i.ZT,d:function(t){t&&(0,i.og)(n),t&&e.d()}}}function s(t,e,n){var r=e.creativeId,i=e.ctaBackground,o=e.ctaTextColor,a=".ad-flexible--".concat(r),s="".concat(a," .ad-display-card__cta"),c=i?"\n      ".concat(s," {\n        background: ").concat(i,";\n      }\n      "):"",u=o?"\n      ".concat(s," {\n        color: ").concat(o,";\n      }\n\n      ").concat(s,":hover {\n        color: ").concat(o,";\n      }\n      "):"",l='\n    <style type="text/css">\n      '.concat(c,"\n      ").concat(u,"\n    </style>\n  ");return t.$$set=function(t){"creativeId"in t&&n(1,r=t.creativeId),"ctaBackground"in t&&n(2,i=t.ctaBackground),"ctaTextColor"in t&&n(3,o=t.ctaTextColor)},[l,r,i,o]}var c=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,s,a,i.N8,{creativeId:1,ctaBackground:2,ctaTextColor:3}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},29613:function(t,e,n){"use strict";n.d(e,{Z:function(){return Y}});var r=n(24027),i=n(20238),o=n(60736),a=(n(80150),n(2023),n(83351)),s=(n(99404),n(39901)),c=n(32766),u=n(62751),l=n(17480),f=n(11734);function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function h(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return d(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(t){var e;return{c:function(){e=(0,s.bi)("path"),(0,s.Lj)(e,"d","M60,6 L60,505.86 L452.747143,255.93 L60,6 L60,6 Z")},m:function(t,n){(0,s.$T)(t,e,n)},p:s.ZT,d:function(t){t&&(0,s.og)(e)}}}function p(t){var e,n;return e=new f.Z({props:{title:"Play",a11yLabel:t[0],$$slots:{default:[m]},$$scope:{ctx:t}}}),{c:function(){(0,s.YC)(e.$$.fragment)},m:function(t,r){(0,s.ye)(e,t,r),n=!0},p:function(t,n){var r=h(n,1)[0],i={};1&r&&(i.a11yLabel=t[0]),2&r&&(i.$$scope={dirty:r,ctx:t}),e.$set(i)},i:function(t){n||((0,s.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,s.et)(e.$$.fragment,t),n=!1},d:function(t){(0,s.vp)(e,t)}}}function v(t,e,n){var r=e.a11yLabel;return t.$$set=function(t){"a11yLabel"in t&&n(0,r=t.a11yLabel)},[r]}var g=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,s.S1)(n,t,v,p,s.N8,{a11yLabel:0}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(s.f_);function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function b(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return y(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return y(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function A(t){var e,n,r;return{c:function(){e=(0,s.bi)("path"),n=(0,s.Dh)(),r=(0,s.bi)("path"),(0,s.Lj)(e,"d","M177.428571,506 L51.7142857,506 C43.0714286,506 36,498.96875 36,490.375 L36,21.625 C36,13.03125 43.0714286,6 51.7142857,6 L177.428571,6 C186.071429,6 193.142857,13.03125 193.142857,21.625 L193.142857,490.375 C193.142857,498.96875 186.071429,506 177.428571,506"),(0,s.Lj)(r,"d","M460.285714,506 L334.571429,506 C325.928571,506 318.857143,498.96875 318.857143,490.375 L318.857143,21.625 C318.857143,13.03125 325.928571,6 334.571429,6 L460.285714,6 C468.928571,6 476,13.03125 476,21.625 L476,490.375 C476,498.96875 468.928571,506 460.285714,506")},m:function(t,i){(0,s.$T)(t,e,i),(0,s.$T)(t,n,i),(0,s.$T)(t,r,i)},p:s.ZT,d:function(t){t&&(0,s.og)(e),t&&(0,s.og)(n),t&&(0,s.og)(r)}}}function w(t){var e,n;return e=new f.Z({props:{title:"Pause",a11yLabel:t[0],$$slots:{default:[A]},$$scope:{ctx:t}}}),{c:function(){(0,s.YC)(e.$$.fragment)},m:function(t,r){(0,s.ye)(e,t,r),n=!0},p:function(t,n){var r=b(n,1)[0],i={};1&r&&(i.a11yLabel=t[0]),2&r&&(i.$$scope={dirty:r,ctx:t}),e.$set(i)},i:function(t){n||((0,s.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,s.et)(e.$$.fragment,t),n=!1},d:function(t){(0,s.vp)(e,t)}}}function E(t,e,n){var r=e.a11yLabel;return t.$$set=function(t){"a11yLabel"in t&&n(0,r=t.a11yLabel)},[r]}var T=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,s.S1)(n,t,E,w,s.N8,{a11yLabel:0}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(s.f_);function R(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function P(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return R(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return R(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function L(t){var e,n;return{c:function(){e=(0,s.bG)("a"),n=(0,s.fL)("\xa0"),(0,s.Lj)(e,"href",t[2]),(0,s.Lj)(e,"target","_blank"),(0,s.Lj)(e,"class","ad-display-card__asset-overlay"),(0,s.Lj)(e,"aria-label",t[7])},m:function(t,r){(0,s.$T)(t,e,r),(0,s.R3)(e,n)},p:function(t,n){4&n&&(0,s.Lj)(e,"href",t[2])},d:function(t){t&&(0,s.og)(e)}}}function x(t){var e,n,r,i,o,a,c,u,l,f,d,h,m,p,v,y,b,A,w,E=t[2]&&L(t);return m=new g({props:{a11yLabel:t[6]+"-play-icon"}}),y=new T({props:{a11yLabel:t[6]+"-pause-icon"}}),{c:function(){e=(0,s.bG)("div"),E&&E.c(),n=(0,s.Dh)(),r=(0,s.bG)("video"),i=(0,s.bG)("source"),a=(0,s.Dh)(),c=(0,s.bG)("img"),l=(0,s.Dh)(),f=(0,s.bG)("div"),d=(0,s.bG)("button"),h=(0,s.bG)("div"),(0,s.YC)(m.$$.fragment),p=(0,s.Dh)(),v=(0,s.bG)("div"),(0,s.YC)(y.$$.fragment),(0,s.Jn)(i.src,o=t[1])||(0,s.Lj)(i,"src",o),(0,s.Lj)(i,"type","video/mp4"),(0,s.Lj)(r,"id",t[6]),r.muted=!0,r.autoplay=!0,(0,s.Lj)(r,"preload",""),r.loop=!0,(0,s.Lj)(r,"width","100%"),(0,s.Lj)(r,"class","ad-display-card__asset-player js-video-player"),(0,s.Jn)(c.src,u=t[0])||(0,s.Lj)(c,"src",u),(0,s.Lj)(c,"alt","Advertisement video preview"),(0,s.Lj)(c,"class","js-ad-thumbnail ad-display-card__asset-cover"),(0,s.Lj)(h,"class",t[4]),(0,s.Lj)(v,"class",t[5]),(0,s.Lj)(d,"class","ad-display-card__button js-control"),(0,s.Lj)(d,"aria-label","Pause animation"),(0,s.Lj)(d,"aria-controls",t[6]),(0,s.Lj)(f,"class","ad-display-card__controls"),(0,s.Lj)(e,"class","ad-display-card__asset-wrapper")},m:function(o,u){(0,s.$T)(o,e,u),E&&E.m(e,null),(0,s.R3)(e,n),(0,s.R3)(e,r),(0,s.R3)(r,i),t[11](r),(0,s.R3)(e,a),(0,s.R3)(e,c),(0,s.R3)(e,l),(0,s.R3)(e,f),(0,s.R3)(f,d),(0,s.R3)(d,h),(0,s.ye)(m,h,null),(0,s.R3)(d,p),(0,s.R3)(d,v),(0,s.ye)(y,v,null),b=!0,A||(w=(0,s.oL)(d,"click",t[8]),A=!0)},p:function(t,r){var a=P(r,1)[0];t[2]?E?E.p(t,a):((E=L(t)).c(),E.m(e,n)):E&&(E.d(1),E=null),(!b||2&a&&!(0,s.Jn)(i.src,o=t[1]))&&(0,s.Lj)(i,"src",o),(!b||1&a&&!(0,s.Jn)(c.src,u=t[0]))&&(0,s.Lj)(c,"src",u),(!b||16&a)&&(0,s.Lj)(h,"class",t[4]),(!b||32&a)&&(0,s.Lj)(v,"class",t[5])},i:function(t){b||((0,s.Ui)(m.$$.fragment,t),(0,s.Ui)(y.$$.fragment,t),b=!0)},o:function(t){(0,s.et)(m.$$.fragment,t),(0,s.et)(y.$$.fragment,t),b=!1},d:function(n){n&&(0,s.og)(e),E&&E.d(),t[11](null),(0,s.vp)(m),(0,s.vp)(y),A=!1,w()}}}function k(t,e,n){var r,i=e.assetCover,o=e.assetImage,a=e.brandName,c=e.ctaUrl,u=e.wid,f="ad-animation--".concat(u),d="Animation ".concat((0,l.U6)("PROMOTED_BY","Promoted By")," ").concat(a),h="js-hidden",m="";return t.$$set=function(t){"assetCover"in t&&n(0,i=t.assetCover),"assetImage"in t&&n(1,o=t.assetImage),"brandName"in t&&n(9,a=t.brandName),"ctaUrl"in t&&n(2,c=t.ctaUrl),"wid"in t&&n(10,u=t.wid)},[i,o,c,r,h,m,f,d,function(){r&&(r.paused?(n(5,m="js-hidden"),n(4,h=""),r.pause()):(n(5,m=""),n(4,h="js-hidden"),r.play()))},a,u,function(t){s.Vn[t?"unshift":"push"]((function(){n(3,r=t)}))}]}var O=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,s.S1)(n,t,k,x,s.N8,{assetCover:0,assetImage:1,brandName:9,ctaUrl:2,wid:10}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(s.f_);function C(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function S(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return C(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return C(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function I(t){var e,n;return{c:function(){e=(0,s.bG)("img"),(0,s.Jn)(e.src,n=t[0])||(0,s.Lj)(e,"src",n),(0,s.Lj)(e,"alt","Advertisement"),(0,s.Lj)(e,"class","ad-display-card__asset-cover js-ad-thumbnail")},m:function(t,n){(0,s.$T)(t,e,n)},p:function(t,r){1&r&&!(0,s.Jn)(e.src,n=t[0])&&(0,s.Lj)(e,"src",n)},i:s.ZT,o:s.ZT,d:function(t){t&&(0,s.og)(e)}}}function j(t){var e,n,r;return{c:function(){e=(0,s.bG)("a"),n=(0,s.bG)("img"),(0,s.Jn)(n.src,r=t[0])||(0,s.Lj)(n,"src",r),(0,s.Lj)(n,"alt","Advertisement"),(0,s.Lj)(n,"class","ad-display-card__asset-cover js-ad-thumbnail"),(0,s.Lj)(e,"href",t[2]),(0,s.Lj)(e,"target","_blank"),(0,s.Lj)(e,"class","ad-display-card__image-link")},m:function(t,r){(0,s.$T)(t,e,r),(0,s.R3)(e,n)},p:function(t,e){1&e&&!(0,s.Jn)(n.src,r=t[0])&&(0,s.Lj)(n,"src",r)},i:s.ZT,o:s.ZT,d:function(t){t&&(0,s.og)(e)}}}function M(t){for(var e,n,r=[t[3]],i={},o=0;o<r.length;o+=1)i=(0,s.f0)(i,r[o]);return e=new O({props:i}),{c:function(){(0,s.YC)(e.$$.fragment)},m:function(t,r){(0,s.ye)(e,t,r),n=!0},p:function(t,n){var i=8&n?(0,s.Lo)(r,[(0,s.gC)(t[3])]):{};e.$set(i)},i:function(t){n||((0,s.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,s.et)(e.$$.fragment,t),n=!1},d:function(t){(0,s.vp)(e,t)}}}function _(t){for(var e,n,r,i,o,a,l,f,d=function(t,e){return t[1]?0:t[2]?1:2},h=[t[5]],m={},p=0;p<h.length;p+=1)m=(0,s.f0)(m,h[p]);e=new c.Z({props:m});var v=[M,j,I],g=[];i=d(t),o=g[i]=v[i](t);for(var y=[t[4]],b={},A=0;A<y.length;A+=1)b=(0,s.f0)(b,y[A]);return l=new u.Z({props:b}),{c:function(){(0,s.YC)(e.$$.fragment),n=(0,s.Dh)(),r=(0,s.bG)("div"),o.c(),a=(0,s.Dh)(),(0,s.YC)(l.$$.fragment),(0,s.Lj)(r,"class","ad-display-card__image ad-animated ad-wireframe--collapse-vertical js-bfa-impression")},m:function(t,o){(0,s.ye)(e,t,o),(0,s.$T)(t,n,o),(0,s.$T)(t,r,o),g[i].m(r,null),(0,s.$T)(t,a,o),(0,s.ye)(l,t,o),f=!0},p:function(t,n){var a=S(n,1)[0],c=32&a?(0,s.Lo)(h,[(0,s.gC)(t[5])]):{};e.$set(c);var u=i;(i=d(t))===u?g[i].p(t,a):((0,s.dv)(),(0,s.et)(g[u],1,1,(function(){g[u]=null})),(0,s.gb)(),(o=g[i])?o.p(t,a):(o=g[i]=v[i](t)).c(),(0,s.Ui)(o,1),o.m(r,null));var f=16&a?(0,s.Lo)(y,[(0,s.gC)(t[4])]):{};l.$set(f)},i:function(t){f||((0,s.Ui)(e.$$.fragment,t),(0,s.Ui)(o),(0,s.Ui)(l.$$.fragment,t),f=!0)},o:function(t){(0,s.et)(e.$$.fragment,t),(0,s.et)(o),(0,s.et)(l.$$.fragment,t),f=!1},d:function(t){(0,s.vp)(e,t),t&&(0,s.og)(n),t&&(0,s.og)(r),g[i].d(),t&&(0,s.og)(a),(0,s.vp)(l,t)}}}function D(t,e,n){var r=e.adPos,i=e.assetCover,o=e.assetImage,a=e.brandAvatar,s=e.brandLinkOut,c=e.brandName,u=e.cardDescription,f=e.creativeId,d=e.ctaBackground,h=e.ctaLinkOut,m=e.ctaText,p=e.ctaTextColor,v=e.dfpClickTracker,g=e.disclosure,y=e.isMp4,b=e.moreClasses,A=e.wid;u=(u||"").trim();var w=s.length>0?s:null,E=h.length>0?(0,l.TV)(h,v):null,T={assetCover:i,assetImage:o,brandName:c,ctaUrl:E,wid:A},R={adPos:r,brandAvatar:a,brandName:c,cardDescription:u,ctaText:m,ctaUrl:E,creativeId:f,disclosure:g,moreClasses:b,url:w,wid:A},P={creativeId:f,ctaBackground:d,ctaTextColor:p};return t.$$set=function(t){"adPos"in t&&n(7,r=t.adPos),"assetCover"in t&&n(8,i=t.assetCover),"assetImage"in t&&n(0,o=t.assetImage),"brandAvatar"in t&&n(9,a=t.brandAvatar),"brandLinkOut"in t&&n(10,s=t.brandLinkOut),"brandName"in t&&n(11,c=t.brandName),"cardDescription"in t&&n(6,u=t.cardDescription),"creativeId"in t&&n(12,f=t.creativeId),"ctaBackground"in t&&n(13,d=t.ctaBackground),"ctaLinkOut"in t&&n(14,h=t.ctaLinkOut),"ctaText"in t&&n(15,m=t.ctaText),"ctaTextColor"in t&&n(16,p=t.ctaTextColor),"dfpClickTracker"in t&&n(17,v=t.dfpClickTracker),"disclosure"in t&&n(18,g=t.disclosure),"isMp4"in t&&n(1,y=t.isMp4),"moreClasses"in t&&n(19,b=t.moreClasses),"wid"in t&&n(20,A=t.wid)},[o,y,E,T,R,P,u,r,i,a,s,c,f,d,h,m,p,v,g,b,A]}var B=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,s.S1)(n,t,D,_,s.N8,{adPos:7,assetCover:8,assetImage:0,brandAvatar:9,brandLinkOut:10,brandName:11,cardDescription:6,creativeId:12,ctaBackground:13,ctaLinkOut:14,ctaText:15,ctaTextColor:16,dfpClickTracker:17,disclosure:18,isMp4:1,moreClasses:19,wid:20}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(s.f_),z=n(60655);function N(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function $(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return N(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return N(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Z(t){for(var e,n,r,i=[t[9]],o={},a=0;a<i.length;a+=1)o=(0,s.f0)(o,i[a]);return n=new z.Z({props:o}),{c:function(){e=(0,s.bG)("div"),(0,s.YC)(n.$$.fragment),(0,s.Lj)(e,"class","ad-disclosure")},m:function(t,i){(0,s.$T)(t,e,i),(0,s.ye)(n,e,null),r=!0},p:function(t,e){var r=512&e?(0,s.Lo)(i,[(0,s.gC)(t[9])]):{};n.$set(r)},i:function(t){r||((0,s.Ui)(n.$$.fragment,t),r=!0)},o:function(t){(0,s.et)(n.$$.fragment,t),r=!1},d:function(t){t&&(0,s.og)(e),(0,s.vp)(n)}}}function U(t){for(var e,n,r,i,o=[t[9]],a={},c=0;c<o.length;c+=1)a=(0,s.f0)(a,o[c]);return n=new z.Z({props:a}),{c:function(){e=(0,s.bG)("a"),(0,s.YC)(n.$$.fragment),(0,s.Lj)(e,"href",t[6]),(0,s.Lj)(e,"aria-labelledby",r="ad-disclosure--"+t[5]),(0,s.Lj)(e,"target","_blank"),(0,s.Lj)(e,"class","ad-disclosure")},m:function(t,r){(0,s.$T)(t,e,r),(0,s.ye)(n,e,null),i=!0},p:function(t,a){var c=512&a?(0,s.Lo)(o,[(0,s.gC)(t[9])]):{};n.$set(c),(!i||32&a&&r!==(r="ad-disclosure--"+t[5]))&&(0,s.Lj)(e,"aria-labelledby",r)},i:function(t){i||((0,s.Ui)(n.$$.fragment,t),i=!0)},o:function(t){(0,s.et)(n.$$.fragment,t),i=!1},d:function(t){t&&(0,s.og)(e),(0,s.vp)(n)}}}function G(t){var e,n;return{c:function(){e=(0,s.bG)("img"),(0,s.Jn)(e.src,n=t[2])||(0,s.Lj)(e,"src",n),(0,s.Lj)(e,"alt","Advertisement"),(0,s.Lj)(e,"class","ad-display-card__asset-cover js-ad-thumbnail")},m:function(t,n){(0,s.$T)(t,e,n)},p:function(t,r){4&r&&!(0,s.Jn)(e.src,n=t[2])&&(0,s.Lj)(e,"src",n)},i:s.ZT,o:s.ZT,d:function(t){t&&(0,s.og)(e)}}}function V(t){var e,n,r;return{c:function(){e=(0,s.bG)("a"),n=(0,s.bG)("img"),(0,s.Jn)(n.src,r=t[2])||(0,s.Lj)(n,"src",r),(0,s.Lj)(n,"alt","Advertisement"),(0,s.Lj)(n,"class","ad-display-card__asset-cover js-ad-thumbnail"),(0,s.Lj)(e,"href",t[7]),(0,s.Lj)(e,"target","_blank"),(0,s.Lj)(e,"class","ad-display-card__image-link")},m:function(t,r){(0,s.$T)(t,e,r),(0,s.R3)(e,n)},p:function(t,e){4&e&&!(0,s.Jn)(n.src,r=t[2])&&(0,s.Lj)(n,"src",r)},i:s.ZT,o:s.ZT,d:function(t){t&&(0,s.og)(e)}}}function F(t){for(var e,n,r=[t[8]],i={},o=0;o<r.length;o+=1)i=(0,s.f0)(i,r[o]);return e=new O({props:i}),{c:function(){(0,s.YC)(e.$$.fragment)},m:function(t,r){(0,s.ye)(e,t,r),n=!0},p:function(t,n){var i=256&n?(0,s.Lo)(r,[(0,s.gC)(t[8])]):{};e.$set(i)},i:function(t){n||((0,s.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,s.et)(e.$$.fragment,t),n=!1},d:function(t){(0,s.vp)(e,t)}}}function J(t){var e,n,r=(0,l.eV)(t[3])+"";return{c:function(){e=(0,s.bG)("div"),n=(0,s.bG)("a"),(0,s.Lj)(n,"href",t[7]),(0,s.Lj)(n,"target","_blank"),(0,s.Lj)(n,"class","ad-button js-bfa-impression"),(0,s.Lj)(n,"data-bfa-impressions","true"),(0,s.Lj)(n,"data-bfa-position",t[1]),(0,s.Lj)(n,"bfa-impression-tracked","true"),(0,s.Lj)(e,"class","ad-feed-story__footer")},m:function(t,i){(0,s.$T)(t,e,i),(0,s.R3)(e,n),n.innerHTML=r},p:function(t,e){8&e&&r!==(r=(0,l.eV)(t[3])+"")&&(n.innerHTML=r),2&e&&(0,s.Lj)(n,"data-bfa-position",t[1])},d:function(t){t&&(0,s.og)(e)}}}function H(t){var e,n,r,i,o,a,c,u,f,d,h,m,p=function(t,e){return t[4]?0:t[7]?1:2},v=(0,l.eV)(t[0])+"",g=[U,Z],y=[];o=t[6]?0:1,a=y[o]=g[o](t);var b=[F,V,G],A=[];f=p(t),d=A[f]=b[f](t);var w=t[3]&&J(t);return{c:function(){e=(0,s.bG)("div"),n=(0,s.bG)("div"),r=(0,s.bG)("div"),i=(0,s.Dh)(),a.c(),c=(0,s.Dh)(),u=(0,s.bG)("div"),d.c(),h=(0,s.Dh)(),w&&w.c(),(0,s.Lj)(r,"class","title"),(0,s.Lj)(n,"class","ad-display-card__dek"),(0,s.Lj)(u,"class","ad-display-card__image ad-animated ad-wireframe--collapse-vertical js-bfa-impression"),(0,s.Lj)(e,"class","ad-display-feed-story clearfix")},m:function(t,a){(0,s.$T)(t,e,a),(0,s.R3)(e,n),(0,s.R3)(n,r),r.innerHTML=v,(0,s.R3)(n,i),y[o].m(n,null),(0,s.R3)(e,c),(0,s.R3)(e,u),A[f].m(u,null),(0,s.R3)(e,h),w&&w.m(e,null),m=!0},p:function(t,n){var i=$(n,1)[0];(!m||1&i)&&v!==(v=(0,l.eV)(t[0])+"")&&(r.innerHTML=v),a.p(t,i);var o=f;(f=p(t))===o?A[f].p(t,i):((0,s.dv)(),(0,s.et)(A[o],1,1,(function(){A[o]=null})),(0,s.gb)(),(d=A[f])?d.p(t,i):(d=A[f]=b[f](t)).c(),(0,s.Ui)(d,1),d.m(u,null)),t[3]?w?w.p(t,i):((w=J(t)).c(),w.m(e,null)):w&&(w.d(1),w=null)},i:function(t){m||((0,s.Ui)(a),(0,s.Ui)(d),m=!0)},o:function(t){(0,s.et)(a),(0,s.et)(d),m=!1},d:function(t){t&&(0,s.og)(e),y[o].d(),A[f].d(),w&&w.d()}}}function W(t,e,n){var r=e.adPos,i=e.assetCover,o=e.assetImage,a=e.brandAvatar,s=e.brandLinkOut,c=e.brandName,u=e.cardDescription,f=e.ctaText,d=e.ctaLinkOut,h=e.dfpClickTracker,m=e.disclosure,p=e.isMp4,v=e.wid;u=(u||"").trim();var g=s.length>0?s:null,y=d.length>0?(0,l.TV)(d,h):null,b={assetCover:i,assetImage:o,brandName:c,ctaUrl:y,wid:v},A={brandAvatar:a,brandName:c,disclosure:m,wid:v};return t.$$set=function(t){"adPos"in t&&n(1,r=t.adPos),"assetCover"in t&&n(10,i=t.assetCover),"assetImage"in t&&n(2,o=t.assetImage),"brandAvatar"in t&&n(11,a=t.brandAvatar),"brandLinkOut"in t&&n(12,s=t.brandLinkOut),"brandName"in t&&n(13,c=t.brandName),"cardDescription"in t&&n(0,u=t.cardDescription),"ctaText"in t&&n(3,f=t.ctaText),"ctaLinkOut"in t&&n(14,d=t.ctaLinkOut),"dfpClickTracker"in t&&n(15,h=t.dfpClickTracker),"disclosure"in t&&n(16,m=t.disclosure),"isMp4"in t&&n(4,p=t.isMp4),"wid"in t&&n(5,v=t.wid)},[u,r,o,f,p,v,g,y,b,A,i,a,s,c,d,h,m]}var q=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,s.S1)(n,t,W,H,s.N8,{adPos:1,assetCover:10,assetImage:2,brandAvatar:11,brandLinkOut:12,brandName:13,cardDescription:0,ctaText:3,ctaLinkOut:14,dfpClickTracker:15,disclosure:16,isMp4:4,wid:5}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(s.f_),Y=function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"template",get:function(){return/^story/.test(this.config.adPos)?q:B}},{key:"buildFormat",value:function(){(0,r.Vx)(e,"buildFormat",this,3)([]),this.element.classList.add("ad-card")}},{key:"getTemplateData",value:function(){var t=Object.assign({},this.context.ad),e=t.assetImage,n={};(t.adPos=this.config.adPos,o.Z.isAny(["xs","sm"])?n.downsize="320:*":n.downsize="400:*",e.indexOf(".gif")>=0)&&(n["output-format"]="mp4",n["resize-quality"]=95,t.isMp4=!0,t.assetCover="".concat(e,"?output-format=jpg&output-quality=90"),(new Image).src=t.assetCover);return t.assetImage=e+decodeURIComponent((0,i.nZ)(n)),t}}])}(a.Z);(0,r._x)(Y,"formatType","display_card")},60655:function(t,e,n){"use strict";n.d(e,{Z:function(){return l}});var r=n(24027),i=n(39901),o=n(17480);function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t){var e,n,r,a,c,u,l,f,d,h,m,p=(0,o.eV)(t[1])+"";return{c:function(){e=(0,i.bG)("div"),n=(0,i.bG)("span"),r=(0,i.fL)(t[0]),a=(0,i.Dh)(),c=(0,i.bG)("span"),f=(0,i.Dh)(),d=(0,i.bG)("img"),(0,i.Lj)(c,"class","ad-disclosure-name"),(0,i.Lj)(e,"id",u="ad-disclosure--"+t[4]),(0,i.Lj)(e,"class",l="ad-disclosure-text "+(t[3]||"")),(0,i.Jn)(d.src,h=t[2])||(0,i.Lj)(d,"src",h),(0,i.Lj)(d,"alt",m=t[1]+" logo"),(0,i.Lj)(d,"class","ad-disclosure-image")},m:function(t,o){(0,i.$T)(t,e,o),(0,i.R3)(e,n),(0,i.R3)(n,r),(0,i.R3)(e,a),(0,i.R3)(e,c),c.innerHTML=p,(0,i.$T)(t,f,o),(0,i.$T)(t,d,o)},p:function(t,n){var a=s(n,1)[0];1&a&&(0,i.rT)(r,t[0]),2&a&&p!==(p=(0,o.eV)(t[1])+"")&&(c.innerHTML=p),16&a&&u!==(u="ad-disclosure--"+t[4])&&(0,i.Lj)(e,"id",u),8&a&&l!==(l="ad-disclosure-text "+(t[3]||""))&&(0,i.Lj)(e,"class",l),4&a&&!(0,i.Jn)(d.src,h=t[2])&&(0,i.Lj)(d,"src",h),2&a&&m!==(m=t[1]+" logo")&&(0,i.Lj)(d,"alt",m)},i:i.ZT,o:i.ZT,d:function(t){t&&(0,i.og)(e),t&&(0,i.og)(f),t&&(0,i.og)(d)}}}function u(t,e,n){var r=e.brandName,i=e.brandAvatar,a=e.disclosure,s=e.moreClasses,c=e.wid;return(!a||a.length<=0)&&(a=(0,o.U6)("PROMOTED_BY","Promoted By")),t.$$set=function(t){"brandName"in t&&n(1,r=t.brandName),"brandAvatar"in t&&n(2,i=t.brandAvatar),"disclosure"in t&&n(0,a=t.disclosure),"moreClasses"in t&&n(3,s=t.moreClasses),"wid"in t&&n(4,c=t.wid)},[a,r,i,s,c]}var l=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,u,c,i.N8,{brandName:1,brandAvatar:2,disclosure:0,moreClasses:3,wid:4}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},62751:function(t,e,n){"use strict";n.d(e,{Z:function(){return A}});var r=n(24027),i=n(39901),o=n(17480),a=n(37101);function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function c(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return s(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t){var e,n,r,o,s=t[5].default,u=(0,i.nu)(s,t,t[4],null);return r=new a.Z({props:{title:"Link Out Arrow",extraClasses:"ad-display-card__cta-arrow"}}),{c:function(){e=(0,i.bG)("a"),u&&u.c(),n=(0,i.Dh)(),(0,i.YC)(r.$$.fragment),(0,i.Lj)(e,"href",t[0]),(0,i.Lj)(e,"target","advertiser"),(0,i.Lj)(e,"class","ad-display-card__cta js-bfa-impression"),(0,i.Lj)(e,"data-bfa-impressions","true"),(0,i.Lj)(e,"data-bfa",t[1])},m:function(t,a){(0,i.$T)(t,e,a),u&&u.m(e,null),(0,i.R3)(e,n),(0,i.ye)(r,e,null),o=!0},p:function(t,n){var r=c(n,1)[0];u&&u.p&&(!o||16&r)&&(0,i.km)(u,s,t,t[4],o?(0,i.u2)(s,t[4],r,null):(0,i.VO)(t[4]),null),(!o||1&r)&&(0,i.Lj)(e,"href",t[0])},i:function(t){o||((0,i.Ui)(u,t),(0,i.Ui)(r.$$.fragment,t),o=!0)},o:function(t){(0,i.et)(u,t),(0,i.et)(r.$$.fragment,t),o=!1},d:function(t){t&&(0,i.og)(e),u&&u.d(t),(0,i.vp)(r)}}}function l(t,e,n){var r=e.$$slots,i=void 0===r?{}:r,o=e.$$scope,a=e.adPos,s=e.creativeId,c=e.ctaUrl,u="@a:".concat(a,"-dfp;@d:mac-cta;@o:{dimension7:").concat(s,",dimension13:").concat(s,"};")+"@e:{obj_id:0,content_id:mac#".concat(s,",creativeId:").concat(s,',post_category:Advertiser};"');return t.$$set=function(t){"adPos"in t&&n(2,a=t.adPos),"creativeId"in t&&n(3,s=t.creativeId),"ctaUrl"in t&&n(0,c=t.ctaUrl),"$$scope"in t&&n(4,o=t.$$scope)},[c,u,a,s,o,i]}var f=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,l,u,i.N8,{adPos:2,creativeId:3,ctaUrl:0}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_),d=n(60655);function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function m(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return h(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return h(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t){for(var e,n,r=[t[3]],o={$$slots:{default:[v]},$$scope:{ctx:t}},a=0;a<r.length;a+=1)o=(0,i.f0)(o,r[a]);return e=new f({props:o}),{c:function(){(0,i.YC)(e.$$.fragment)},m:function(t,r){(0,i.ye)(e,t,r),n=!0},p:function(t,n){var o=8&n?(0,i.Lo)(r,[(0,i.gC)(t[3])]):{};4098&n&&(o.$$scope={dirty:n,ctx:t}),e.$set(o)},i:function(t){n||((0,i.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,i.et)(e.$$.fragment,t),n=!1},d:function(t){(0,i.vp)(e,t)}}}function v(t){var e;return{c:function(){e=(0,i.fL)(t[1])},m:function(t,n){(0,i.$T)(t,e,n)},p:function(t,n){2&n&&(0,i.rT)(e,t[1])},d:function(t){t&&(0,i.og)(e)}}}function g(t){var e,n=(0,o.eV)(t[0])+"";return{c:function(){e=(0,i.bG)("div"),(0,i.Lj)(e,"class","ad-display-card__dek")},m:function(t,r){(0,i.$T)(t,e,r),e.innerHTML=n},p:function(t,r){1&r&&n!==(n=(0,o.eV)(t[0])+"")&&(e.innerHTML=n)},d:function(t){t&&(0,i.og)(e)}}}function y(t){for(var e,n,r,o,a,s,c=t[1].length>0&&null!==t[2]&&p(t),u=t[0].length>0&&g(t),l=[t[4]],f={},h=0;h<l.length;h+=1)f=(0,i.f0)(f,l[h]);return a=new d.Z({props:f}),{c:function(){e=(0,i.bG)("div"),c&&c.c(),n=(0,i.Dh)(),u&&u.c(),r=(0,i.Dh)(),o=(0,i.bG)("div"),(0,i.YC)(a.$$.fragment),(0,i.Lj)(e,"class","ad-animated ad-wireframe--collapse-vertical"),(0,i.Lj)(o,"class","ad-disclosure--horizontal")},m:function(t,l){(0,i.$T)(t,e,l),c&&c.m(e,null),(0,i.R3)(e,n),u&&u.m(e,null),(0,i.$T)(t,r,l),(0,i.$T)(t,o,l),(0,i.ye)(a,o,null),s=!0},p:function(t,r){var o=m(r,1)[0];t[1].length>0&&null!==t[2]?c?(c.p(t,o),6&o&&(0,i.Ui)(c,1)):((c=p(t)).c(),(0,i.Ui)(c,1),c.m(e,n)):c&&((0,i.dv)(),(0,i.et)(c,1,1,(function(){c=null})),(0,i.gb)()),t[0].length>0?u?u.p(t,o):((u=g(t)).c(),u.m(e,null)):u&&(u.d(1),u=null);var s=16&o?(0,i.Lo)(l,[(0,i.gC)(t[4])]):{};a.$set(s)},i:function(t){s||((0,i.Ui)(c),(0,i.Ui)(a.$$.fragment,t),s=!0)},o:function(t){(0,i.et)(c),(0,i.et)(a.$$.fragment,t),s=!1},d:function(t){t&&(0,i.og)(e),c&&c.d(),u&&u.d(),t&&(0,i.og)(r),t&&(0,i.og)(o),(0,i.vp)(a)}}}function b(t,e,n){var r=e.adPos,i=e.brandAvatar,o=e.brandLinkOut,a=e.brandName,s=e.cardDescription,c=e.creativeId,u=e.ctaText,l=e.ctaUrl,f=e.disclosure,d=e.wid,h={adPos:r,creativeId:c,ctaUrl:l},m={brandAvatar:i,brandLinkOut:o,brandName:a,disclosure:f,wid:d};return s=(s||"").trim(),t.$$set=function(t){"adPos"in t&&n(5,r=t.adPos),"brandAvatar"in t&&n(6,i=t.brandAvatar),"brandLinkOut"in t&&n(7,o=t.brandLinkOut),"brandName"in t&&n(8,a=t.brandName),"cardDescription"in t&&n(0,s=t.cardDescription),"creativeId"in t&&n(9,c=t.creativeId),"ctaText"in t&&n(1,u=t.ctaText),"ctaUrl"in t&&n(2,l=t.ctaUrl),"disclosure"in t&&n(10,f=t.disclosure),"wid"in t&&n(11,d=t.wid)},[s,u,l,h,m,r,i,o,a,c,f,d]}var A=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,b,y,i.N8,{adPos:5,brandAvatar:6,brandLinkOut:7,brandName:8,cardDescription:0,creativeId:9,ctaText:1,ctaUrl:2,disclosure:10,wid:11}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},95466:function(t,e,n){"use strict";n.d(e,{S:function(){return l}});var r=n(24027),i=n(60736),o=(n(80150),n(2023)),a=(n(37083),n(99404),n(58451),n(48705),n(63396),n(32249),n(84714),n(76635),n(67419),n(17807),n(20848)),s=n(17748),c=(n(70833),n(34686),n(93557),n(11608),n(81383)),u=n(2282),l=function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e)}(function(t){function e(t){var n;(0,r.PA)(this,e),(n=(0,r.$w)(this,e,[t])).plugins.add(c.Z);var i=n.config.adPos;return/inline/.test(i)||"quiz_ad"===i?n.placement="inline":/wide/.test(i)?(n.placement="wide",n.config.refreshOptions={infinite:!0}):/bottom/.test(i)?n.placement="grid":n.placement="rail",n.element.classList.add("ad-ex--".concat(n.placement)),"rail"===n.placement&&n.element.parentElement.classList.contains("side-bar")&&!/-bp$/.test(i)&&n.element.classList.add("ad-ex--sidebar-top"),n.config.refreshOptions&&n.plugins.add(u.Z),n.setupSizes(),n}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"setupSizes",value:function(){(0,r.Vx)(e,"setupSizes",this,3)([]);var t=i.Z.isAny(["md","lg"]),n="grid"===this.placement,o="inline"===this.placement,a=n||o,s=this.context.env.adSizes;t&&a&&this.filterProgrammaticSizes({min:s.PROGRAMMATIC_LEADERBOARD}),!t&&o&&this.filterProgrammaticSizes({max:s.PROGRAMMATIC_SMARTPHONE_BANNER}),this.context.env.hasQuiz&&this.excludeSize(s.NATIVE_COMPLEX_100)}}])}(o.Z.withMixins(s.Q)).withMixins(a.Z_))},28403:function(t,e,n){"use strict";n.d(e,{Z:function(){return y}});var r=n(24027),i=n(39901),o=n(48267),a=n(5263),s=n(6888),c=n(17480);function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function l(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return u(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return u(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var f=function(t){return{}},d=function(t){return{}};function h(t){var e;return{c:function(){e=(0,i.bG)("div"),(0,i.Lj)(e,"class","ad-wireframe-programmatic")},m:function(t,n){(0,i.$T)(t,e,n)},d:function(t){t&&(0,i.og)(e)}}}function m(t){var e,n,r,o,s,u,l,m,p,v,g,y,b,A=(t[4]||(0,c.U6)("ADVERTISEMENT","Advertisement"))+"";e=new a.Z({props:{config:t[3]}});var w=!t[0]&&h(),E=t[15]["extended-format"],T=(0,i.nu)(E,t,t[16],d);return{c:function(){(0,i.YC)(e.$$.fragment),n=(0,i.Dh)(),r=(0,i.bG)("div"),o=(0,i.bG)("div"),s=(0,i.fL)(A),u=(0,i.Dh)(),w&&w.c(),l=(0,i.Dh)(),m=(0,i.bG)("div"),g=(0,i.Dh)(),T&&T.c(),(0,i.Lj)(o,"class","ad__disclosure--ex js-ad-disclosure"),(0,i.Lj)(m,"id",p="div-gpt-ad-"+t[3].wid),(0,i.Lj)(m,"class",v="xs-text-center "+(t[0]?"":"ad-animated ad-fadedown")+" ad-slot js-ad-slot js-ad-slot-"+t[3].wid),(0,i.Lj)(r,"id",y="bf-item-"+t[3].wid+"-1")},m:function(t,a){(0,i.ye)(e,t,a),(0,i.$T)(t,n,a),(0,i.$T)(t,r,a),(0,i.R3)(r,o),(0,i.R3)(o,s),(0,i.R3)(r,u),w&&w.m(r,null),(0,i.R3)(r,l),(0,i.R3)(r,m),(0,i.R3)(r,g),T&&T.m(r,null),b=!0},p:function(t,n){var o={};8&n&&(o.config=t[3]),e.$set(o),(!b||16&n)&&A!==(A=(t[4]||(0,c.U6)("ADVERTISEMENT","Advertisement"))+"")&&(0,i.rT)(s,A),t[0]?w&&(w.d(1),w=null):w||((w=h()).c(),w.m(r,l)),(!b||8&n&&p!==(p="div-gpt-ad-"+t[3].wid))&&(0,i.Lj)(m,"id",p),(!b||9&n&&v!==(v="xs-text-center "+(t[0]?"":"ad-animated ad-fadedown")+" ad-slot js-ad-slot js-ad-slot-"+t[3].wid))&&(0,i.Lj)(m,"class",v),T&&T.p&&(!b||65536&n)&&(0,i.km)(T,E,t,t[16],b?(0,i.u2)(E,t[16],n,f):(0,i.VO)(t[16]),d),(!b||8&n&&y!==(y="bf-item-"+t[3].wid+"-1"))&&(0,i.Lj)(r,"id",y)},i:function(t){b||((0,i.Ui)(e.$$.fragment,t),(0,i.Ui)(T,t),b=!0)},o:function(t){(0,i.et)(e.$$.fragment,t),(0,i.et)(T,t),b=!1},d:function(t){(0,i.vp)(e,t),t&&(0,i.og)(n),t&&(0,i.og)(r),w&&w.d(),T&&T.d(t)}}}function p(t){var e,n;return e=new o.Z({props:{tag:t[7],attributes:t[8],$$slots:{default:[m]},$$scope:{ctx:t}}}),{c:function(){(0,i.YC)(e.$$.fragment)},m:function(t,r){(0,i.ye)(e,t,r),n=!0},p:function(t,n){var r={};128&n&&(r.tag=t[7]),256&n&&(r.attributes=t[8]),65561&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i:function(t){n||((0,i.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,i.et)(e.$$.fragment,t),n=!1},d:function(t){(0,i.vp)(e,t)}}}function v(t){var e,n;return e=new s.Z({props:{hasWireframe:t[0],width:t[1],height:t[2],isListItem:t[6],cssClasses:t[5],$$slots:{default:[p]},$$scope:{ctx:t}}}),{c:function(){(0,i.YC)(e.$$.fragment)},m:function(t,r){(0,i.ye)(e,t,r),n=!0},p:function(t,n){var r=l(n,1)[0],i={};1&r&&(i.hasWireframe=t[0]),2&r&&(i.width=t[1]),4&r&&(i.height=t[2]),64&r&&(i.isListItem=t[6]),32&r&&(i.cssClasses=t[5]),65945&r&&(i.$$scope={dirty:r,ctx:t}),e.$set(i)},i:function(t){n||((0,i.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,i.et)(e.$$.fragment,t),n=!1},d:function(t){(0,i.vp)(e,t)}}}function g(t,e,n){var r,i,o=e.$$slots,a=void 0===o?{}:o,s=e.$$scope,c=e.slot,u=e.customDisclosure,l=e.programmaticWireframes,f=e.programmatic_wireframes,d=e.wireframeWidth,h=e.wireframe_width,m=e.wireframeHeight,p=e.wireframe_height,v=e.wireframeClasses,g=e.isListItem,y=void 0!==g&&g,b=e.t3ModuleName,A=void 0===b?"ad-ex":b,w=e.cssClasses,E=void 0===w?function(t){return t}:w,T=e.position;return t.$$set=function(t){"slot"in t&&n(3,c=t.slot),"customDisclosure"in t&&n(4,u=t.customDisclosure),"programmaticWireframes"in t&&n(0,l=t.programmaticWireframes),"programmatic_wireframes"in t&&n(9,f=t.programmatic_wireframes),"wireframeWidth"in t&&n(1,d=t.wireframeWidth),"wireframe_width"in t&&n(10,h=t.wireframe_width),"wireframeHeight"in t&&n(2,m=t.wireframeHeight),"wireframe_height"in t&&n(11,p=t.wireframe_height),"wireframeClasses"in t&&n(5,v=t.wireframeClasses),"isListItem"in t&&n(6,y=t.isListItem),"t3ModuleName"in t&&n(12,A=t.t3ModuleName),"cssClasses"in t&&n(13,E=t.cssClasses),"position"in t&&n(14,T=t.position),"$$scope"in t&&n(16,s=t.$$scope)},t.$$.update=function(){3591&t.$$.dirty&&(n(0,l=l||f),n(1,d=d||h),n(2,m=m||p)),28745&t.$$.dirty&&(n(7,r=y&&!l?"li":"div"),n(8,i={id:"BF_WIDGET_".concat(c.wid),"data-module":A,class:E("xs-relative ad-ex")}),T&&n(8,i.position=T,i))},[l,d,m,c,u,v,y,r,i,f,h,p,A,E,T,a,s]}var y=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,g,v,i.N8,{slot:3,customDisclosure:4,programmaticWireframes:0,programmatic_wireframes:9,wireframeWidth:1,wireframe_width:10,wireframeHeight:2,wireframe_height:11,wireframeClasses:5,isListItem:6,t3ModuleName:12,cssClasses:13,position:14}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},31207:function(t,e,n){"use strict";n.d(e,{Z:function(){return Q}});var r=n(24027),i=n(60736),o=(n(80150),n(2023)),a=(n(37083),n(99404),n(58451),n(48705),n(63396)),s=n(32249),c=n(84714),u=(n(76635),n(67419)),l=(n(17807),n(20848)),f=n(17748),d=(n(70833),n(29613)),h=n(41871),m=n(82545),p=n(31675),v=n(93802),g=(n(186),n(39901)),y=n(17480);function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function A(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}function w(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return b(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return b(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(t){var e,n=t[5].default,r=(0,g.nu)(n,t,t[4],null);return{c:function(){r&&r.c()},m:function(t,n){r&&r.m(t,n),e=!0},p:function(t,i){r&&r.p&&(!e||16&i)&&(0,g.km)(r,n,t,t[4],e?(0,g.u2)(n,t[4],i,null):(0,g.VO)(t[4]),null)},i:function(t){e||((0,g.Ui)(r,t),e=!0)},o:function(t){(0,g.et)(r,t),e=!1},d:function(t){r&&r.d(t)}}}function T(t){for(var e,n,r=t[5].default,i=(0,g.nu)(r,t,t[4],null),o=[t[2]],a={},s=0;s<o.length;s+=1)a=(0,g.f0)(a,o[s]);return{c:function(){e=(0,g.bG)("span"),i&&i.c(),(0,g.UF)(e,a)},m:function(t,r){(0,g.$T)(t,e,r),i&&i.m(e,null),n=!0},p:function(t,s){i&&i.p&&(!n||16&s)&&(0,g.km)(i,r,t,t[4],n?(0,g.u2)(r,t[4],s,null):(0,g.VO)(t[4]),null),(0,g.UF)(e,a=(0,g.Lo)(o,[4&s&&t[2]]))},i:function(t){n||((0,g.Ui)(i,t),n=!0)},o:function(t){(0,g.et)(i,t),n=!1},d:function(t){t&&(0,g.og)(e),i&&i.d(t)}}}function R(t){for(var e,n,r=t[5].default,i=(0,g.nu)(r,t,t[4],null),o=[{href:t[1]},t[2]],a={},s=0;s<o.length;s+=1)a=(0,g.f0)(a,o[s]);return{c:function(){e=(0,g.bG)("a"),i&&i.c(),(0,g.UF)(e,a)},m:function(t,r){(0,g.$T)(t,e,r),i&&i.m(e,null),n=!0},p:function(t,s){i&&i.p&&(!n||16&s)&&(0,g.km)(i,r,t,t[4],n?(0,g.u2)(r,t[4],s,null):(0,g.VO)(t[4]),null),(0,g.UF)(e,a=(0,g.Lo)(o,[(!n||2&s)&&{href:t[1]},4&s&&t[2]]))},i:function(t){n||((0,g.Ui)(i,t),n=!0)},o:function(t){(0,g.et)(i,t),n=!1},d:function(t){t&&(0,g.og)(e),i&&i.d(t)}}}function P(t){var e,n,r,i,o=function(t,e){return"a"===t[0]?0:"span"===t[0]?1:"none"===t[0]?2:-1},a=[R,T,E],s=[];return~(e=o(t))&&(n=s[e]=a[e](t)),{c:function(){n&&n.c(),r=(0,g.cS)()},m:function(t,n){~e&&s[e].m(t,n),(0,g.$T)(t,r,n),i=!0},p:function(t,i){var c=w(i,1)[0],u=e;(e=o(t))===u?~e&&s[e].p(t,c):(n&&((0,g.dv)(),(0,g.et)(s[u],1,1,(function(){s[u]=null})),(0,g.gb)()),~e?((n=s[e])?n.p(t,c):(n=s[e]=a[e](t)).c(),(0,g.Ui)(n,1),n.m(r.parentNode,r)):n=null)},i:function(t){i||((0,g.Ui)(n),i=!0)},o:function(t){(0,g.et)(n),i=!1},d:function(t){~e&&s[e].d(t),t&&(0,g.og)(r)}}}function L(t,e,n){var r,i,o,a=e.$$slots,s=void 0===a?{}:a,c=e.$$scope,u=e.tag,l=void 0===u?"a":u,f=e.attributes,d=void 0===f?{}:f;return t.$$set=function(t){"tag"in t&&n(0,l=t.tag),"attributes"in t&&n(3,d=t.attributes),"$$scope"in t&&n(4,c=t.$$scope)},o=function(){var e;8&t.$$.dirty&&n(1,(i=A(e=d,["href"]),r=e.href,e),r,(n(2,i),n(3,d)))},t.$$.update=o,[l,r,i,d,c,s]}var x=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,g.S1)(n,t,L,P,g.N8,{tag:0,attributes:3}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(g.f_);function k(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function O(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return k(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return k(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function C(t){var e,n=(0,y.eV)(t[2])+"";return{c:function(){e=(0,g.bG)("div"),(0,g.Lj)(e,"class","title")},m:function(t,r){(0,g.$T)(t,e,r),e.innerHTML=n},p:function(t,r){4&r&&n!==(n=(0,y.eV)(t[2])+"")&&(e.innerHTML=n)},d:function(t){t&&(0,g.og)(e)}}}function S(t){for(var e,n,r,i,o,a,s,c,u,l,f,d=(0,y.eV)(t[6])+"",h=(0,y.eV)(t[1])+"",m=[t[4].attributes],p={},v=0;v<m.length;v+=1)p=(0,g.f0)(p,m[v]);return{c:function(){e=(0,g.bG)("span"),n=(0,g.bG)("span"),r=new g.FW(!1),i=(0,g.fL)("\xa0"),o=(0,g.Dh)(),a=(0,g.bG)("span"),s=(0,g.Dh)(),c=(0,g.bG)("span"),u=(0,g.bG)("img"),r.a=i,(0,g.Lj)(n,"class","ad-disclosure-name promo-text"),(0,g.Lj)(a,"class","ad-disclosure-name"),(0,g.UF)(e,p),(0,g.Jn)(u.src,l=t[0])||(0,g.Lj)(u,"src",l),(0,g.Lj)(u,"alt",f=t[1]+" logo"),(0,g.Lj)(u,"class","ad-disclosure-image")},m:function(t,l){(0,g.$T)(t,e,l),(0,g.R3)(e,n),r.m(d,n),(0,g.R3)(n,i),(0,g.R3)(e,o),(0,g.R3)(e,a),a.innerHTML=h,(0,g.$T)(t,s,l),(0,g.$T)(t,c,l),(0,g.R3)(c,u)},p:function(t,n){2&n&&h!==(h=(0,y.eV)(t[1])+"")&&(a.innerHTML=h),(0,g.UF)(e,p=(0,g.Lo)(m,[16&n&&t[4].attributes])),1&n&&!(0,g.Jn)(u.src,l=t[0])&&(0,g.Lj)(u,"src",l),2&n&&f!==(f=t[1]+" logo")&&(0,g.Lj)(u,"alt",f)},d:function(t){t&&(0,g.og)(e),t&&(0,g.og)(s),t&&(0,g.og)(c)}}}function I(t){for(var e,n,r,i,o,a,s=[t[3]],c={$$slots:{default:[C]},$$scope:{ctx:t}},u=0;u<s.length;u+=1)c=(0,g.f0)(c,s[u]);n=new x({props:c});for(var l=[t[5]],f={$$slots:{default:[S]},$$scope:{ctx:t}},d=0;d<l.length;d+=1)f=(0,g.f0)(f,l[d]);return o=new x({props:f}),{c:function(){e=(0,g.bG)("div"),(0,g.YC)(n.$$.fragment),r=(0,g.Dh)(),i=(0,g.bG)("div"),(0,g.YC)(o.$$.fragment),(0,g.Lj)(i,"class","ad-story-video__text"),(0,g.Lj)(e,"class","ad-story-video__inner")},m:function(t,s){(0,g.$T)(t,e,s),(0,g.ye)(n,e,null),(0,g.R3)(e,r),(0,g.R3)(e,i),(0,g.ye)(o,i,null),a=!0},p:function(t,e){var r=O(e,1)[0],i=8&r?(0,g.Lo)(s,[(0,g.gC)(t[3])]):{};32772&r&&(i.$$scope={dirty:r,ctx:t}),n.$set(i);var a=32&r?(0,g.Lo)(l,[(0,g.gC)(t[5])]):{};32787&r&&(a.$$scope={dirty:r,ctx:t}),o.$set(a)},i:function(t){a||((0,g.Ui)(n.$$.fragment,t),(0,g.Ui)(o.$$.fragment,t),a=!0)},o:function(t){(0,g.et)(n.$$.fragment,t),(0,g.et)(o.$$.fragment,t),a=!1},d:function(t){t&&(0,g.og)(e),(0,g.vp)(n),(0,g.vp)(o)}}}function j(t,e,n){var r=e.adPos,i=e.bfa,o=e.brandAvatar,a=e.brandName,s=e.brandLinkOut,c=e.ctaLinkOut,u=e.dfpClickTracker,l=e.wid,f=e.videoName,d=(0,y.U6)("PROMOTED_BY","Promoted By"),h=c&&c.length>0?(0,y.TV)(c,u):null,m={tag:"span",attributes:{class:"ad-story-video__title-cta"}},p=s.length>0?s:null,v={tag:"span",attributes:{class:"ad-disclosure-text"}},g={tag:"none",attributes:{}};return h&&(m.tag="a",m.attributes.href=h,m["data-bfa"]=i,m["data-bfa-position"]=r),p&&(v.attributes.id="ad-disclosure-mobile--".concat(l),g.tag="a",g.attributes.href=p),t.$$set=function(t){"adPos"in t&&n(7,r=t.adPos),"bfa"in t&&n(8,i=t.bfa),"brandAvatar"in t&&n(0,o=t.brandAvatar),"brandName"in t&&n(1,a=t.brandName),"brandLinkOut"in t&&n(9,s=t.brandLinkOut),"ctaLinkOut"in t&&n(10,c=t.ctaLinkOut),"dfpClickTracker"in t&&n(11,u=t.dfpClickTracker),"wid"in t&&n(12,l=t.wid),"videoName"in t&&n(2,f=t.videoName)},[o,a,f,m,v,g,d,r,i,s,c,u,l]}var M=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,g.S1)(n,t,j,I,g.N8,{adPos:7,bfa:8,brandAvatar:0,brandName:1,brandLinkOut:9,ctaLinkOut:10,dfpClickTracker:11,wid:12,videoName:2}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(g.f_);function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function D(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return _(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function B(t){var e,n,r=(0,y.eV)(t[1])+"";return{c:function(){e=(0,g.bG)("div"),n=(0,g.bG)("a"),(0,g.Lj)(n,"href",t[3]),(0,g.Lj)(n,"target","_blank"),(0,g.Lj)(n,"class","ad-button js-bfa-impression"),(0,g.Lj)(n,"data-bfa",t[0]),(0,g.Lj)(n,"data-bfa-position",t[2]),(0,g.Lj)(e,"class","ad-story-video__inner ad-story-video__footer")},m:function(t,i){(0,g.$T)(t,e,i),(0,g.R3)(e,n),n.innerHTML=r},p:function(t,e){2&e&&r!==(r=(0,y.eV)(t[1])+"")&&(n.innerHTML=r),1&e&&(0,g.Lj)(n,"data-bfa",t[0]),4&e&&(0,g.Lj)(n,"data-bfa-position",t[2])},d:function(t){t&&(0,g.og)(e)}}}function z(t){var e,n=t[1].length>0&&t[3]&&B(t);return{c:function(){n&&n.c(),e=(0,g.cS)()},m:function(t,r){n&&n.m(t,r),(0,g.$T)(t,e,r)},p:function(t,r){var i=D(r,1)[0];t[1].length>0&&t[3]?n?n.p(t,i):((n=B(t)).c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null)},i:g.ZT,o:g.ZT,d:function(t){n&&n.d(t),t&&(0,g.og)(e)}}}function N(t,e,n){var r=e.bfa,i=e.ctaLinkOut,o=e.ctaText,a=e.dfpClickTracker,s=e.adPos,c=i.length>0?(0,y.TV)(i,a):null;return t.$$set=function(t){"bfa"in t&&n(0,r=t.bfa),"ctaLinkOut"in t&&n(4,i=t.ctaLinkOut),"ctaText"in t&&n(1,o=t.ctaText),"dfpClickTracker"in t&&n(5,a=t.dfpClickTracker),"adPos"in t&&n(2,s=t.adPos)},[r,o,s,c,i,a]}var $=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,g.S1)(n,t,N,z,g.N8,{bfa:0,ctaLinkOut:4,ctaText:1,dfpClickTracker:5,adPos:2}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(g.f_);function Z(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function U(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Z(t,e,n[e])}))}return t}var G={renderTemplate:v.Z},V=function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"buildFormat",value:function(){var t=this,e=u.Z.getSlotContainer(this.config.wid);this.element.classList.add("ad-card"),e.style.width="100%",e.insertAdjacentHTML("beforebegin",'<div class="js-header"></div>'),e.insertAdjacentHTML("afterend",'<div class="js-footer"></div>');var n=this.element.querySelector(".js-header"),r=this.element.querySelector(".js-footer");this.headerTemplate=this.templateRender(n,M),this.footerTemplate=this.templateRender(r,$),this.addDestroyAction((function(){return t.componentDestroy()}))}},{key:"templateRender",value:function(t,e){var n=this.getTemplateData(),r=n.videoId,i=n.videoName;return G.renderTemplate({template:e,target:t,props:U({},n,{context:this.context,bfa:"a:video-dfp; @d:None; @o:{dimension3:".concat(r,",dimension4:").concat(i,"};")+"@e:{obj_id:".concat(r,",post_category:Advertiser,obj_type:video,p:None}")})})}},{key:"componentDestroy",value:function(){this.headerTemplate&&this.headerTemplate.$destroy(),this.footerTemplate&&this.footerTemplate.$destroy()}}])}(s.t),F=n(66611);function J(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function H(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return J(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return J(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function W(t){for(var e,n,r,i,o,a,s,c,u,l,f,d,h,m,p,v,b,A,w,E=(0,y.eV)(t[0].title)+"",T=(0,y.eV)(t[0].description)+"",R=[t[4]],P={},L=0;L<R.length;L+=1)P=(0,g.f0)(P,R[L]);return p=new F.Z({props:P}),{c:function(){e=(0,g.bG)("div"),n=(0,g.bG)("span"),r=(0,g.bG)("img"),o=(0,g.Dh)(),a=(0,g.bG)("div"),s=(0,g.bG)("div"),c=(0,g.bG)("a"),u=(0,g.bG)("div"),d=(0,g.Dh)(),h=(0,g.bG)("p"),m=(0,g.Dh)(),(0,g.YC)(p.$$.fragment),(0,g.Jn)(r.src,i=t[0].images.dblbig)||(0,g.Lj)(r,"src",i),(0,g.Lj)(r,"alt","Advertisement"),(0,g.Lj)(r,"class","ad-buzz-image js-ad-thumbnail"),(0,g.Lj)(n,"class","ad-buzz-thumbnail"),(0,g.Lj)(n,"data-bfa-impressions","true"),(0,g.Lj)(u,"class","title"),(0,g.Lj)(c,"href",l=t[0].url),(0,g.Lj)(c,"target","_blank"),(0,g.Lj)(c,"class","ad-buzz-title"),(0,g.Lj)(c,"data-bfa-position",f=t[1].position),(0,g.Lj)(c,"data-bfa",t[3]),(0,g.Lj)(h,"class","ad-buzz-summary"),(0,g.Lj)(s,"class","ad-buzz-content--inner"),(0,g.Lj)(a,"class","ad-buzz-content"),(0,g.Lj)(e,"class",v="ad-buzz-format ad-animated ad-wireframe--collapse-vertical ad-promotion--"+t[0].promotionType+" clearfix")},m:function(i,l){(0,g.$T)(i,e,l),(0,g.R3)(e,n),(0,g.R3)(n,r),(0,g.R3)(e,o),(0,g.R3)(e,a),(0,g.R3)(a,s),(0,g.R3)(s,c),(0,g.R3)(c,u),u.innerHTML=E,t[7](c),(0,g.R3)(s,d),(0,g.R3)(s,h),h.innerHTML=T,(0,g.R3)(a,m),(0,g.ye)(p,a,null),b=!0,A||(w=(0,g.oL)(n,"click",t[6]),A=!0)},p:function(t,n){var o=H(n,1)[0];(!b||1&o&&!(0,g.Jn)(r.src,i=t[0].images.dblbig))&&(0,g.Lj)(r,"src",i),(!b||1&o)&&E!==(E=(0,y.eV)(t[0].title)+"")&&(u.innerHTML=E),(!b||1&o&&l!==(l=t[0].url))&&(0,g.Lj)(c,"href",l),(!b||2&o&&f!==(f=t[1].position))&&(0,g.Lj)(c,"data-bfa-position",f),(!b||1&o)&&T!==(T=(0,y.eV)(t[0].description)+"")&&(h.innerHTML=T);var a=16&o?(0,g.Lo)(R,[(0,g.gC)(t[4])]):{};p.$set(a),(!b||1&o&&v!==(v="ad-buzz-format ad-animated ad-wireframe--collapse-vertical ad-promotion--"+t[0].promotionType+" clearfix"))&&(0,g.Lj)(e,"class",v)},i:function(t){b||((0,g.Ui)(p.$$.fragment,t),b=!0)},o:function(t){(0,g.et)(p.$$.fragment,t),b=!1},d:function(n){n&&(0,g.og)(e),t[7](null),(0,g.vp)(p),A=!1,w()}}}function q(t,e,n){var r,i=e.buzz,o=e.slot,a=e.wid,s=(0,y.bb)(i,o.position),c="other"!==i.promotionType,u={wid:a,legacyBFA:s,isBranded:c,extraClasses:c?"":"xs-hide sm-block",advertiser:i.advertiser,position:o.position};return t.$$set=function(t){"buzz"in t&&n(0,i=t.buzz),"slot"in t&&n(1,o=t.slot),"wid"in t&&n(5,a=t.wid)},[i,o,r,s,u,a,function(t){return(0,y.KV)(t,r)},function(t){g.Vn[t?"unshift":"push"]((function(){n(2,r=t)}))}]}var Y=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,g.S1)(n,t,q,W,g.N8,{buzz:0,slot:1,wid:5}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(g.f_),X=function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).template=Y,t}return(0,r.XW)(e,t),(0,r.qH)(e)}(a.W),Q=function(t){function e(){var t;(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).addFormat(X.formatType,X),t.addFormat(d.Z.formatType,d.Z),t.addFormat(c.P.formatType,c.P),t.addFormat(V.formatType,V),t.addFormat(m.f.formatType,m.f),t.addFormat(m.C.formatType,m.C),t.context.env.isAdPost()&&t.addFormat(h.Z.formatType,h.Z);var n=document.querySelectorAll(".ad-feed-story")||[];return Array.prototype.indexOf.call(n,t.element)+1===3&&t.addFormat(/^((?!programmatic|impression_pixel|empty).)+$/,p.Z),t.configureSizes(),t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"configureSizes",value:function(){i.Z.isAny(["md","lg"])&&this.filterProgrammaticSizes({min:this.context.env.adSizes.PROGRAMMATIC_LEADERBOARD})}},{key:"handleAdContentLoaded",value:function(t){"programmatic"===t.type&&this.element.classList.remove("card"),(0,r.Vx)(e,"handleAdContentLoaded",this,3)(arguments)}}])}(o.Z.withMixins(l.Z_,f.Q))},76562:function(t,e,n){"use strict";n.d(e,{Z:function(){return l}});var r=n(24027),i=n(39901),o=n(5263);function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t){var e,n,r,a,c,u,l;return n=new o.Z({props:{config:t[0]}}),{c:function(){e=(0,i.bG)("div"),(0,i.YC)(n.$$.fragment),r=(0,i.Dh)(),(a=(0,i.bG)("div")).innerHTML='<div class="xs-col-5 md-col-4"><div class="ad-wireframe-image ad-wireframe-image--big"></div></div> \n    <div class="xs-col-7 md-col-8 xs-p1 sm-p2"><div class="ad-wireframe-text xs-col-10 xs-mx05"></div> \n      <div class="ad-wireframe-text xs-col-11 xs-mt2 xs-mx05"></div> \n      <div class="ad-wireframe-text xs-col-7 xs-mt1 xs-mx05"></div></div>',(0,i.Lj)(a,"class","xs-flex xs-flex-align-stretch xs-flex-row ad-wireframe ad__wireframe-container"),(0,i.Lj)(e,"id",c="BF_WIDGET_"+t[0].wid),(0,i.Lj)(e,"data-module","ad-feed-story"),(0,i.Lj)(e,"class",u="xs-relative card story-card card--article-ad ad-feed-story ad-animated "+t[1])},m:function(t,o){(0,i.$T)(t,e,o),(0,i.ye)(n,e,null),(0,i.R3)(e,r),(0,i.R3)(e,a),l=!0},p:function(t,r){var o=s(r,1)[0],a={};1&o&&(a.config=t[0]),n.$set(a),(!l||1&o&&c!==(c="BF_WIDGET_"+t[0].wid))&&(0,i.Lj)(e,"id",c),(!l||2&o&&u!==(u="xs-relative card story-card card--article-ad ad-feed-story ad-animated "+t[1]))&&(0,i.Lj)(e,"class",u)},i:function(t){l||((0,i.Ui)(n.$$.fragment,t),l=!0)},o:function(t){(0,i.et)(n.$$.fragment,t),l=!1},d:function(t){t&&(0,i.og)(e),(0,i.vp)(n)}}}function u(t,e,n){var r=e.slot,i=e.extraClassNames;return t.$$set=function(t){"slot"in t&&n(0,r=t.slot),"extraClassNames"in t&&n(1,i=t.extraClassNames)},[r,i]}var l=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,u,c,i.N8,{slot:0,extraClassNames:1}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},41871:function(t,e,n){"use strict";n.d(e,{Z:function(){return o}});var r=n(24027),i=(n(80150),n(2023),n(83351)),o=(n(99404),n(58451),n(48705),n(63396),n(32249),n(84714),n(76635),n(67419),n(17807),n(186),n(70833),n(60736),function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).template=null,t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"buildFormat",value:function(){this.element.classList.remove("js-hidden"),this.element.classList.add("ad-flexible--".concat(this.formatType),"ad-flexible--".concat(this.context.ad.creativeId)),this.eventBus.trigger("ad-content-rendered:".concat(this.config.wid))}}])}(i.Z));(0,r._x)(o,"formatType","impression_pixel")},31675:function(t,e,n){"use strict";n.d(e,{Z:function(){return d}});var r=n(24027),i=(n(80150),n(2023),n(83351)),o=(n(99404),n(39901)),a=n(17480);function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function c(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return s(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t){var e,n,r,i=(0,a.U6)("ADVERTISE_WITH_BUZZFEED","Advertise with BuzzFeed")+"";return{c:function(){e=(0,o.bG)("div"),n=(0,o.bG)("a"),r=(0,o.fL)(i),(0,o.Lj)(n,"href",t[0]),(0,o.Lj)(n,"target","_blank"),(0,o.Lj)(n,"class","ad-advertise-link"),(0,o.Lj)(e,"class","ad-advertise-wrapper")},m:function(t,i){(0,o.$T)(t,e,i),(0,o.R3)(e,n),(0,o.R3)(n,r)},p:function(t,e){1&c(e,1)[0]&&(0,o.Lj)(n,"href",t[0])},i:o.ZT,o:o.ZT,d:function(t){t&&(0,o.og)(e)}}}function l(t,e,n){var r=e.country,i=e.advertiseUrl,o=void 0===i?(0,a.U6)("ADVERTISE_WITH_URL","http://advertise.buzzfeed.com"):i;return"en-in"===r&&(o="https://advertise.buzzfeed.com/bfindia"),t.$$set=function(t){"country"in t&&n(1,r=t.country),"advertiseUrl"in t&&n(0,o=t.advertiseUrl)},[o,r]}var f=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,o.S1)(n,t,l,u,o.N8,{country:1,advertiseUrl:0}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(o.f_),d=function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"template",get:function(){return f}},{key:"isEnabled",value:function(){return Promise.all([this.context.abeagle.isOn("advertise_international"),"f_other"!==(this.context.ad.buzz||{}).user_type])}},{key:"getTemplateData",value:function(){return{country:this.context.env.localization.country}}},{key:"buildFormat",value:function(){(0,r.Vx)(e,"buildFormat",this,3)([]);var t=this.element,n=t.querySelector(".ad-advertise-wrapper");if(t.style.visibility="none",t.parentElement.classList.contains("ad-wireframe-wrapper")){var i=parseInt(window.getComputedStyle(t.parentElement)["margin-bottom"],10),o='<div class="js-padding" style="margin-bottom: '.concat(i+19,'px"></div>');t.parentElement.insertAdjacentHTML("beforeend",o)}else{t.insertAdjacentHTML("afterEnd",'<div class="js-padding" style="padding-bottom: 19px; margin: 0;"></div>')}n.style.visibility="visible",this.onDOMEvent(n.querySelector("a"),"click",this.trackClick.bind(this))}},{key:"trackClick",value:function(t){this.eventBus.trigger("advertise-click:".concat(this.config.wid),{url:t.target.href})}}])}(i.Z)},71049:function(t,e,n){"use strict";n.d(e,{Z:function(){return p}});var r=n(24027),i=(n(80150),n(2023)),o=(n(37083),n(99404),n(58451),n(48705),n(63396),n(32249),n(84714),n(76635),n(67419),n(17807),n(20848)),a=(n(186),n(70833)),s=n(60736),c=n(70753),u=n(2282),l=n(54341),f=n(15332);function d(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function h(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){d(t,e,n[e])}))}return t}function m(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}var p=function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).config.refreshOptions=h({},c.y0),t.noLazyRendering=!0,t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"setup",value:function(){try{var t=this,n={"tasty-swap-refresh":void 0,"bf-news-swap-refresh":void 0,"bpage-swap-refresh":void 0,"homepage-swap-refresh":void 0,"feedpage-swap-refresh":void 0};return m(Promise.all(Object.keys(n).map((i=function(e){return m(t.context.abeagle.getExperimentVariant(e,{rejectErrors:!1,defaultVariantIfUnbucketed:"control"}),(function(t){n[e]=t}))},function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];try{return Promise.resolve(i.apply(this,t))}catch(n){return Promise.reject(n)}}))),(function(){return Object.values(n).some((function(t){return"on"===t}))?(t.config.refreshOptions.inViewSeconds=15,t.plugins.add(l.Z)):t.plugins.add(u.Z),(0,r.Vx)(e,"setup",t,3)([])}))}catch(o){return Promise.reject(o)}var i}},{key:"handleSlotRenderEnded",value:function(){var t=this.config.wid;if(this.element&&"object"===typeof this.element){var e=this.element.querySelector(".ad-toolbar.loading"),n=this.element.querySelector(".ad-toolbar.close"),r="string"===typeof t?"open--no-animate":"open";e&&"object"===typeof e&&e.classList.replace("loading",r),e&&"object"===typeof n&&e.classList.replace("close",r)}}},{key:"removeToolbarAd",value:function(){if(this.element&&"object"===typeof this.element){var t=this.element.querySelector(".ad-toolbar");t&&"object"===typeof t&&t.remove()}}},{key:"isEnabled",value:function(){try{var t=this;return m((0,r.Vx)(e,"isEnabled",t,3)([]),(function(e){var n=!1;return e?function(t,e){var n=t();if(n&&n.then)return n.then(e);return e(n)}((function(){if(s.Z.isAny(["md","lg"]))return m(t.context.abeagle.isOn("ads_toolbar_bpages"),(function(e){return m(t.context.abeagle.isOn("ads_toolbar_feeds"),(function(r){return m(t.context.abeagle.isOn("ads_toolbar_tasty"),(function(i){return m(t.context.abeagle.isOn("ads_toolbar_bfn"),(function(o){return e||r||i||o||((0,a.A)("info","lifecycle","toolbar >> disabled on desktop, all flags off"),t.removeToolbarAd()),n=!0,e||r||i||o}))}))}))}))}),(function(e){return n?e:!!(0,f.R)(t.context)||(t.removeToolbarAd(),!1)})):(!0,!1)}))}catch(n){return Promise.reject(n)}}}])}(i.Z.withMixins(o.Z_))},12239:function(t,e,n){"use strict";n.d(e,{Z:function(){return j}});var r=n(24027),i=n(39901),o=n(5263);function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t){var e,n,r,o,a,c,u,l,f;return{c:function(){e=(0,i.bi)("svg"),n=(0,i.bi)("path"),r=(0,i.bi)("path"),o=(0,i.bi)("path"),a=(0,i.bi)("path"),c=(0,i.bi)("path"),u=(0,i.bi)("path"),l=(0,i.bi)("path"),f=(0,i.bi)("path"),(0,i.Lj)(n,"d","M7.35058 6.29136C7.94146 6.33848 8.4378 6.57412 8.86324 7.02182C9.28867 7.46951 9.50139 8.08216 9.50139 8.85974C9.50139 9.80227 9.19413 10.5799 8.55598 11.1454C7.91783 11.7109 7.01968 11.9936 5.81428 11.9936H0V0.824707H5.79065C6.80697 0.824707 7.6342 1.10746 8.24872 1.64942C8.86324 2.21493 9.1705 2.94539 9.1705 3.86435C9.1705 4.57125 8.98141 5.1132 8.57961 5.53734C8.17781 5.96147 7.77601 6.17354 7.32694 6.22067L7.35058 6.29136ZM2.76533 5.44308H5.17613C5.57793 5.44308 5.90882 5.32527 6.16881 5.08964C6.40517 4.85401 6.54698 4.57125 6.54698 4.17067C6.54698 3.81723 6.4288 3.55803 6.19245 3.34596C5.9561 3.1339 5.64884 3.03964 5.29431 3.03964H2.76533V5.44308ZM5.31794 9.7787C5.74338 9.7787 6.09791 9.66089 6.33426 9.44882C6.57061 9.23675 6.68879 8.93043 6.68879 8.52986C6.68879 8.15285 6.57061 7.87009 6.31062 7.65802C6.07427 7.44595 5.74338 7.32814 5.36521 7.32814H2.76533V9.7787H5.31794Z"),(0,i.Lj)(n,"fill","#EE3322"),(0,i.Lj)(r,"d","M19.0737 11.9937H16.4029V11.1926C15.7411 11.8995 14.8666 12.2529 13.8267 12.2529C12.834 12.2529 12.054 11.923 11.4631 11.2868C10.8723 10.6506 10.565 9.80233 10.565 8.76555V3.62878H13.2122V8.20004C13.2122 8.69486 13.354 9.09544 13.614 9.40176C13.8739 9.70808 14.2285 9.84946 14.6539 9.84946C15.1975 9.84946 15.623 9.66095 15.9302 9.28394C16.2375 8.90693 16.3793 8.31785 16.3793 7.5167V3.62878H19.0501L19.0737 11.9937Z"),(0,i.Lj)(r,"fill","#EE3322"),(0,i.Lj)(o,"d","M27.3225 11.9937H20.2319V11.4989L23.5645 5.74947H20.3265V3.62878H27.2752V4.12361L23.9427 9.87302H27.2989V11.9937H27.3225Z"),(0,i.Lj)(o,"fill","#EE3322"),(0,i.Lj)(a,"d","M35.5475 11.9937H28.4805V11.4989L31.7894 5.74947H28.5514V3.62878H35.5002V4.12361L32.1676 9.87302H35.5238L35.5475 11.9937Z"),(0,i.Lj)(a,"fill","#EE3322"),(0,i.Lj)(c,"d","M39.5182 5.60803H44.3634V8.01147H39.5182V12.0172H36.7528V0.824707H44.7888V3.22815H39.5182V5.60803Z"),(0,i.Lj)(c,"fill","#EE3322"),(0,i.Lj)(u,"d","M63.9572 8.50627H57.812C57.9774 9.54305 58.7574 10.1321 59.9155 10.1321C60.8373 10.1321 61.7591 9.77868 62.4445 9.23673L63.4608 11.004C62.5154 11.8051 61.31 12.2528 59.9155 12.2528C57.1029 12.2528 55.1885 10.5563 55.1885 7.82294C55.1885 6.52697 55.6139 5.46662 56.4648 4.64191C57.3156 3.8172 58.3792 3.39307 59.6319 3.39307C60.8373 3.39307 61.8536 3.79364 62.6809 4.61835C63.5081 5.44306 63.9335 6.5034 63.9572 7.8465V8.50627ZM58.5447 5.89076C58.2138 6.12639 57.9774 6.45628 57.8593 6.85685H61.31C61.1918 6.43271 60.9791 6.10283 60.6719 5.8672C60.3646 5.65513 60.0101 5.53731 59.6319 5.53731C59.2301 5.53731 58.8756 5.65513 58.5447 5.89076Z"),(0,i.Lj)(u,"fill","#EE3322"),(0,i.Lj)(l,"d","M54.1721 8.50627H48.0269C48.1923 9.54305 48.9723 10.1321 50.1304 10.1321C51.0522 10.1321 51.974 9.77868 52.6594 9.23673L53.6757 11.004C52.7303 11.8051 51.5249 12.2528 50.1304 12.2528C47.3178 12.2528 45.4034 10.5563 45.4034 7.82294C45.4034 6.52697 45.8288 5.46662 46.6797 4.64191C47.5305 3.8172 48.5941 3.39307 49.8468 3.39307C51.0522 3.39307 52.0685 3.79364 52.8958 4.61835C53.723 5.44306 54.1484 6.5034 54.1721 7.8465V8.50627ZM48.7596 5.89076C48.4287 6.12639 48.1923 6.45628 48.0742 6.85685H51.5249C51.4067 6.43271 51.194 6.10283 50.8868 5.8672C50.5795 5.65513 50.225 5.53731 49.8468 5.53731C49.445 5.53731 49.0905 5.65513 48.7596 5.89076Z"),(0,i.Lj)(l,"fill","#EE3322"),(0,i.Lj)(f,"d","M74.2857 11.9936H71.615V11.3339C70.9295 11.9465 70.0787 12.2528 69.0151 12.2528C67.8806 12.2528 66.9115 11.8287 66.1079 11.004C65.3043 10.1793 64.9025 9.11894 64.9025 7.82297C64.9025 6.52699 65.3043 5.46665 66.1079 4.64194C66.9115 3.81723 67.8806 3.39309 69.0151 3.39309C70.0787 3.39309 70.9532 3.69941 71.615 4.31206V0H74.2857V11.9936ZM71.0241 9.37813C71.4259 8.97756 71.6386 8.45917 71.6386 7.82297C71.6386 7.21032 71.4259 6.69193 71.0241 6.2678C70.6223 5.86722 70.1259 5.65516 69.5823 5.65516C68.9914 5.65516 68.5187 5.86722 68.1406 6.2678C67.7624 6.66837 67.5733 7.18676 67.5733 7.82297C67.5733 8.45917 67.7624 9.00112 68.1406 9.4017C68.5187 9.80227 68.9914 9.99078 69.5823 9.99078C70.1259 9.99078 70.5986 9.77871 71.0241 9.37813Z"),(0,i.Lj)(f,"fill","#EE3322"),(0,i.Lj)(e,"title",t[0]),(0,i.Lj)(e,"extraclasses",t[1]),(0,i.Lj)(e,"width","75"),(0,i.Lj)(e,"height","13"),(0,i.Lj)(e,"viewBox","0 0 75 13"),(0,i.Lj)(e,"fill","none")},m:function(t,s){(0,i.$T)(t,e,s),(0,i.R3)(e,n),(0,i.R3)(e,r),(0,i.R3)(e,o),(0,i.R3)(e,a),(0,i.R3)(e,c),(0,i.R3)(e,u),(0,i.R3)(e,l),(0,i.R3)(e,f)},p:function(t,n){var r=s(n,1)[0];1&r&&(0,i.Lj)(e,"title",t[0]),2&r&&(0,i.Lj)(e,"extraclasses",t[1])},i:i.ZT,o:i.ZT,d:function(t){t&&(0,i.og)(e)}}}function u(t,e,n){var r=e.title,i=e.extraClasses;return t.$$set=function(t){"title"in t&&n(0,r=t.title),"extraClasses"in t&&n(1,i=t.extraClasses)},[r,i]}var l=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,u,c,i.N8,{title:0,extraClasses:1}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_);function f(t){var e,n,r,o,a,s;return{c:function(){e=(0,i.bi)("svg"),n=(0,i.bi)("title"),r=(0,i.fL)("Tasty Logo"),o=(0,i.bi)("g"),a=(0,i.bi)("path"),s=(0,i.bi)("path"),(0,i.Lj)(a,"fill","#fefefe"),(0,i.Lj)(a,"d","M113.9 40.23l-1.44.66-.46.2c-.64.3-1.3.6-1.97.97-.75.42-1.34.88-1.86 1.3-.15.1-.3.2-.45.33-.77.58-2 1.65-2.73 2.45-.03 0-.04.03-.06.05.02-.4.03-.83.02-1.3l-.08-4.83c0-.45-.02-.95-.04-1.42s-.04-.96-.04-1.35c0-.33.02-.74.04-1.18.02-.5.05-1.02.05-1.54v-1.1l.02-1.26c0-.84-.05-3.7-.07-4.53 0-.5-.04-1.13-.06-1.72l-.04-1.06-.07-1.36c-.03-.36-.05-.7-.06-1.07l-.12-2c-.04-.7-.1-1.4-.1-1.77 0-.32-.04-.88-.08-1.53l-.1-1.54v-.34l.7-.08.76-.07c.2 0 .37 0 .55-.02.78 0 1.85-.02 2.94-.5-.22 1.3-.52 3.3-.6 3.93-.1.68-.1 1.3-.08 1.8v.72l-.1.8c-.08.56-.15 1.14-.2 1.72l-.12.9c-.07.5-.15 1.04-.2 1.62-.03.64-.16 4.34-.06 5.42.13 1.46.5 2.76 1.06 3.76.44.8 1 1.7 1.7 2.5.8.87 1.54 1.38 2.4 1.86.28.16.62.34 1 .5l-.1.05zM90.08 21.6c0 .4-.05.88-.1 1.35l-.1 1.26c-.02.4-.13 1.63-.22 2.62l-.17 2.02-.08 1.3-.08 1.2c-.03.5-.04 1-.04 1.42 0 .35 0 .7-.04 1l-.1 1.1c-.08.53-.15 1.08-.18 1.6l-.25 2.85c-.2 2.33-.33 3.77-.35 4.27-.02.48 0 1.33 0 2.66 0 .8.03 1.8 0 2.1 0 .43 0 .9-.02 1.38 0 .43-.02.84-.03 1.22-.02.38-.1 1.56-.18 2.5l-.16 2.35c0 .1 0 .25-.02.43-.05-.4-.1-.82-.17-1.2v-.07c-.13-.85-.4-1.54-.63-2.1-.08-.2-.18-.44-.2-.56-.2-.82-.44-1.66-.72-2.58-.23-.74-.48-1.43-.73-2.1l-.15-.38c-.23-.62-.47-1.2-.7-1.78-.1-.2-.18-.4-.26-.6-.22-.5-1.57-3.76-1.97-4.65l-1.05-2.26-1.06-2.23c-.07-.14-.16-.42-.24-.7-.15-.45-.32-1-.6-1.6l-.48-1-.57-1.2c-.2-.43-.42-.84-.6-1.2-.2-.33-.36-.65-.5-.94l-.4-1c-.13-.38-.3-.8-.47-1.24-.15-.33-.3-.7-.44-1.1.02 0 .03.02.04.03.72.47 1.6 1.05 2.8 1.14h.44c2.03 0 3.4-1.03 3.84-1.37 1.13-.86 1.95-2.04 2.37-3.42l.08-.2c.23-.74.55-1.75.55-2.9v-1.15l.02-1.23c0-.38-.02-.75-.05-1.1.6.17 1.26.26 1.98.26.44 0 .9-.03 1.48-.08l.5-.05c0 .25 0 .53.02.83 0 .16 0 .34.02.5.02.65 0 3.87-.03 4.63zm59.07 10l-.42-2.36c-.46-1.26-1.08-2.3-1.87-3.14-1.15-1.23-2.53-1.85-4.1-1.85-.26 0-.52.02-.8.05-1.44.18-2.62.83-3.5 1.37l-.47.3c-.52.3-1.18.68-1.84 1.23l-.4.32c-.32.26-.78.73-1.1 1 .03-.17.2-.55.24-.74l.4-1.88.16-.73c.13-.63.23-1.2.3-1.64l.16-.85c.06-.23.1-.5.17-.8l.32-1.46.3-1.43.2-1c.2-.77.36-1.62.53-2.52.15-.76.23-1.52.3-2.18l.05-.48c.12-.97.16-1.93.15-2.85v-.52c0-.9 0-2.03-.46-3.15-.8-1.94-2.5-3.3-4.6-3.6-.83-.13-1.5-.2-2.1-.2-.57 0-1.1.06-1.58.17-1.87.4-2.97 1.64-3.55 2.3l-.17.18-.23.22c-.15.14-.34.3-.53.52-.12-.68-.4-1.32-.9-1.9-1.05-1.16-2.65-1.9-4.5-2.1-.2-.02-.42-.03-.63-.03-1.17 0-2.17.33-2.9.57l-.3.1-.15.04c-.83.26-2.08.65-3.07 1.62-.25-.5-.54-.95-.87-1.33-1.35-1.56-3.54-1.9-4.73-1.98-.17-.03-.35-.03-.54-.03-.64 0-1.2.06-1.66.1l-.43.06c-.32 0-.7-.04-1.12-.08-.5-.05-1.06-.1-1.65-.12-.93 0-1.85-.03-2.77-.04-.12-.02-.24-.02-.36-.02-.4 0-.8 0-1.18.02l-1 .02h-.4c-.6 0-1.12.05-1.6.1-.36.03-.7.06-1 .06h-.03l-.66-.05C91.8.9 91.23.83 90.6.83h-.47c-.93 0-1.78.06-2.54.2-.78.13-3.14.54-4.38 2.76l-.08.13c-.14.23-.32.53-.5.9-.75-.76-1.56-1.38-2.44-1.85-.74-.4-1.48-.62-2.06-.8-.24-.06-.47-.12-.65-.2-.44-.15-4.4-1.45-5.76-1.63-.5-.07-1-.1-1.47-.1-.9 0-1.8.12-2.6.35-1.6.46-2.7 1.44-3.56 2.23v.02c-.1.1-.2.17-.3.25-.55.47-1.3 1.12-2 2.32-.5.88-.88 1.8-1.2 2.6l-.1.18c-.32.82-1.55 4.38-1.8 5.53-.17.87-.35 1.8-.44 2.8-.1 1.05-.02 4.55.1 5.56.1.75.26 1.55.4 2.25l.15.6c.23 1.08 1.15 3.82 1.72 5.22.2.5.43.93.65 1.35.2.36.37.7.5 1.03l.35.96c.16.5.34 1.04.57 1.6.2.5.75 1.7 1.28 2.87l.86 1.88c.24.57.5 1.1.73 1.55.16.3.3.6.42.86.2.47.58 1.27 1 2.1-.4-.06-.83-.1-1.24-.1-1 0-1.8.2-2.4.35l-.25.07c-1.14.27-2.82.8-4.1 2.33l-.33.4-.15-.77c-.04-.3-.07-.66-.1-1.03-.04-.44-.08-.9-.15-1.37l-.26-1.6c-.15-1-.42-2.67-.46-3.1-.07-.7-.14-1.46-.2-2.4-.04-.3-.1-.92-.15-1.64l-.28-3.3-.1-1.72c-.1-1.6-.18-2.76-.23-3.28l-.07-.97c-.02-.47-.05-1-.1-1.57-.1-.87-.23-1.72-.35-2.55-.1-.6-.2-1.15-.32-1.65l-.18-.9c-.16-.9-.88-4.2-1.14-5.13-.26-.95-1.5-4.44-2.03-5.45-.5-.97-1.1-2.18-2.24-3.13-1.22-1.02-2.67-1.52-4.45-1.52h-.16c-1.25.02-2.5.34-3.83.98-1.02.5-2.33 1.25-3.35 2.65-.68.9-1 1.85-1.24 2.6l-.2.62-.35.76c-.27.58-.57 1.23-.8 2-.27.87-.53 1.68-.78 2.38l-.16.43c-.2.6-.5 1.4-.77 2.37-.13.5-.24 1.05-.35 1.56l-.25 1.12c-.2.82-.37 1.53-.57 2.45l-.4 1.84-.2.95-.37 1.95-.27 1.45c-.2.95-.72 3.8-.85 4.52l-.24 1.17c-.1.5-.22 1-.3 1.5-.13.76-.3 2.26-.45 3.6-.1.75-.17 1.48-.22 1.78l-.12.92c-.08.63-.17 1.28-.26 1.82l-.2 1.24c-.05.42-.1.8-.18 1.23-.16.87-.77 4.5-.9 5.45-.06.37-.3 1.58-.5 2.47-.24 1.15-.4 1.88-.45 2.3-.1.54-.2 1.26-.34 2l-.3 1.54c-.02-.6-.06-1.16-.1-1.68l-.38-3.32-.17-1.45c-.12-1.08-.38-4.04-.4-4.6 0-.77.08-6.57.1-7.36l.13-2.57c.13-2.57.2-4.42.22-4.96 0-.27 0-.84.02-1.5 0-1.28.03-3.02.05-3.55 0-.55 0-1.06-.03-1.5 0-.4-.03-.75 0-1.05 0-.5.07-1.88.13-3.1.05-.86.1-1.65.1-2 .03-.52.02-1 .02-1.47v-1.1c.03-.6 0-1.33-.03-2.2 0-.05 0-.12-.02-.2l2.13-.12 1.3-.06.4-.04c.9-.07 1.8-.15 2.68-.48 1.28-.5 2.9-1.73 3.42-4.88.18-1.17.18-2.37-.03-3.6-.27-2.3-1.32-3.7-2.16-4.45-.98-.9-2.24-1.48-3.96-1.84-.94-.2-2.05-.42-3.26-.42h-.08c-.8 0-1.56.12-2.23.22-.24.04-.5.08-.74.1-.74.1-4.5.6-5.5.84l-1.6.38c-1.13.27-2.86.7-3.48.8-.63.1-1.2.27-1.72.42-.38.1-.73.22-1.02.27-.68.12-4.85.98-5.9 1.33C2.06 7.52.63 9.37.3 11.87c-.15 1.24-.26 3.1.55 4.9 1.1 2.44 3.58 2.84 4.63 3l.3.05c.6.1 1.35.23 2.2.23.24 0 .46 0 .67-.02.27-.02.6-.03.93-.03h.34c.03 1.3.04 3.78.03 4.2-.02.54-.02 1.8-.02 3v1.97c0 .35-.04.82-.07 1.28-.04.56-.08 1.13-.1 1.67v5.24l-.07 2.58-.03.43L9.55 45c-.02.54-.02 1.85-.03 3.13v1.93l-.04 2.24c-.02.78-.12 3.97-.16 4.72l-.03.66-.1 1.65-.1.53c-.1.5-.2 1.2-.27 1.97-.05.7-.02 1.38 0 1.97l.03.78c0 1.63.4 2.93.76 3.73 1.3 3.02 3.6 3.34 4.54 3.34.43 0 .86-.06 1.3-.18l.37-.1c1.48-.43 2.25-.65 2.8-.94l.18-.08h.46l.44.02c.22.02.42.02.6.02 3.16 0 4.3-2.37 4.93-3.64.13-.26.3-.6.47-1.03-.04.68 0 1.33.17 1.95.5 2 1.25 2.86 1.97 3.58 1.58 1.6 4.1 1.7 4.84 1.7.32 0 .62 0 .9-.05 1.45-.22 6.32-1.14 8.2-3.92.9-1.36 1.18-4.25 1.25-6.44v-.13h.07c.5.03 1.2.08 1.98.1-.02 1.18-.06 4.42-.04 5.18v.45c-.02.73-.04 1.74.33 2.8.54 1.54 1.57 2.76 3 3.53 1.36.73 3.05.8 3.7.8.37 0 .72 0 1.07-.04 1.7-.2 3.42-.87 4.47-1.76 1.37-1.17 1.9-2.4 2.26-3.53.24-.76.47-2.57.6-3.98.4.76.82 1.44 1.12 1.82.64.84 1.3 1.58 2.04 2.26.82.77 1.72 1.35 2.44 1.82.78.5 4.3 2.17 6 2.4.58.06 1.27.13 2.03.13.47 0 .92-.03 1.37-.1 1.25-.15 2.36-.55 3.3-.92 1.22-.5 2.2-1.2 2.94-1.75 1.1-.82 3.55-3.85 4.17-5.13.1-.17.18-.35.27-.52.35-.7.75-1.46 1-2.36.1-.33.27-1.08.44-1.96.04.65.14 1.22.23 1.7.14.76.37 2.03 1.23 3.35.56.85 1.93 2.3 4.84 2.3.34 0 .7-.03 1.04-.07.98-.1 1.87-.37 2.65-.6l.5-.14c.5-.15.93-.3 1.3-.43l.64-.2c1.7-.1 2.9-.68 3.7-1.37l.15.3c.5 1 1.1 2.04 1.68 2.84.66.94 1.38 1.62 2.02 2.22l.15.15c.82.78 1.72 1.58 2.78 2.17 1.68.9 4.74 1.94 6.2 2.06.4.04.85.06 1.33.06 1.64 0 4.02-.24 5.2-.65 2.5-.86 4.33-2.77 5.12-3.6l.07-.07c.45-.47 1.33-1.4 2.03-2.33.8-1.06 2.42-4.28 2.7-4.93.5-1.1.8-2.28 1.05-3.28.27-1.1.45-2.13.55-3.17.1-.9.13-1.84.16-2.97 0-.55 0-1.1-.03-1.6l-.03-1.2c0-.24 0-.48.02-.73.02-.63.05-1.33 0-2.1-.04-.43-.1-.84-.14-1.2-.05-.4-.1-.77-.1-1 .02-.42 0-.87-.02-1.3.02 0 .03 0 .05-.02l.67-.48c.4-.3 2.28-1.36 3.2-1.87.6-.34 1.06-.6 1.33-.78.9-.53 1.6-1.13 2.24-1.65l.37-.3c.13-.12.35-.27.57-.44.42-.3.95-.68 1.48-1.14.26-.23 3.45-3.28 4.2-4.87.2-.48.35-.95.43-1.4V31.6z"),(0,i.Lj)(s,"fill","#333"),(0,i.Lj)(s,"d","M32.46 9.64c-.1-1-.43-1.82-1.04-2.37-.6-.54-1.47-.84-2.4-1.04-.8-.17-1.67-.34-2.6-.34-.83 0-1.7.2-2.6.3-.86.1-4.37.6-5.14.77-1.06.24-4.17 1.02-5.25 1.2-.87.15-1.77.53-2.72.7-.93.16-4.72.98-5.4 1.2-.94.32-1.43 1.08-1.58 2.24-.12.95-.18 2.06.26 3.03.33.73 1.16.9 2.04 1.04.72.1 1.52.3 2.36.22.8-.08 1.66-.02 2.38-.1 1.14-.1 2.22-.34 2.37-.08.15.25.13 1.53.2 2.65.05.84.07 4.64.05 5.22-.02.86 0 4.05 0 4.93-.02.87-.16 2.06-.17 2.94v5.25c-.02.88-.06 2.18-.1 3.05 0 .88-.1 3.82-.1 4.63-.03.8-.04 4.03-.05 4.84l-.03 2.42c-.02.8-.12 4.04-.16 4.84-.05.8-.07 1.62-.14 2.42-.04.55-.28 1.45-.36 2.46-.05.8.04 1.7.04 2.5 0 1 .25 1.84.48 2.37.35.83.9 1.42 1.74 1.2.82-.25 2.2-.62 2.55-.8 1.04-.53 1.4-.46 2.24-.45 1.55.02 1.86.35 2.84-1.68.32-.64.67-1.4.72-2.26.05-.75 0-1.58-.06-2.4-.07-.78-.47-4-.54-4.68-.1-.83-.4-4.1-.4-4.94-.02-.83.06-6.7.1-7.52.02-.83.3-6.6.32-7.47.02-.85.04-4.27.07-5.12.03-.85-.06-1.7-.03-2.57.03-.85.2-4.26.25-5.12.03-.85-.02-1.7.02-2.55.04-1.17-.2-3.9-.03-4.98.08-.5 1.83-.45 2.16-.57.5-.2 3.64-.3 4.34-.35.86-.08 1.68-.12 2.2-.3.7-.28 1.06-1.18 1.23-2.22.12-.73.14-1.6-.04-2.54M57.14 64.2c.04-.84-.23-1.66-.26-2.47-.04-.84 0-1.67-.08-2.46-.1-.86-.35-4.2-.48-4.92-.18-.98-.65-4.03-.75-4.83-.1-.8-.33-1.6-.45-2.4-.12-.8-.14-1.6-.25-2.4-.12-.8-.68-4.02-.75-4.85l-.22-2.47c-.07-.82-.35-4.1-.4-4.93-.07-.82-.25-4.12-.33-4.94-.08-.82-.08-1.65-.17-2.47-.1-.82-.2-1.64-.33-2.46-.12-.82-.33-1.62-.48-2.43-.15-.8-.85-4.03-1.07-4.83-.23-.83-1.4-4.06-1.77-4.78-.43-.82-.82-1.58-1.42-2.08-.65-.55-1.4-.72-2.34-.7-.72 0-1.47.2-2.4.63-.8.4-1.55.87-2.1 1.6-.5.7-.68 1.6-1.02 2.46-.34.82-.78 1.62-1.04 2.47-.27.9-.55 1.76-.83 2.55-.2.6-.56 1.46-.84 2.5-.2.78-.36 1.66-.58 2.6-.2.8-.37 1.5-.57 2.4l-.57 2.76c-.2.87-.46 2.5-.63 3.37-.18.9-.7 3.63-.84 4.46-.17.95-.4 1.8-.53 2.6-.18 1.12-.52 4.4-.64 5.3-.14.9-.26 1.95-.4 2.84-.16.9-.24 1.63-.4 2.5-.16.9-.75 4.46-.88 5.34-.13.9-.85 4.07-.95 4.78-.1.72-.22 1.42-.36 2.13-.12.7-.27 1.4-.42 2.13-.18.9-.63 2.67-.4 3.6.33 1.24.68 1.6 1.1 2 .58.6 2.13.8 2.8.7 1.34-.22 4.9-1.07 5.84-2.45.43-.65.65-3.86.68-4.62.02-.82.13-3.1.28-3.32.2-.26 1.23-.18 2.88-.17.36.02 1.5.15 2.65.14 1.18 0 2.92-.03 3.02.13.14.25.12 2.05.1 3.22-.02 1.18-.06 4.52-.05 5.24.02.77-.07 1.52.15 2.16.24.68.67 1.24 1.38 1.62.53.3 1.66.5 2.76.37 1.08-.13 2.14-.57 2.6-.97.77-.65 1-1.25 1.22-1.95.2-.6.54-3.74.56-4.68m-9.4-11.98c-.77.2-3.8.1-4.6.02-1.12-.1-2.35.05-2.47-.27-.14-.38.26-2.02.3-3 .03-.9.22-3.16.37-4.37.1-.76.1-1.64.22-2.58.1-.8.4-4.3.54-5.16.12-.86.35-1.7.5-2.55.12-.86.26-1.72.4-2.55.15-.9.8-4.25 1-4.98.33-1.38.76-2.8 1.04-2.77.23.02.44 1.43.6 2.96l.68 8.14.4 5.28c.07.87.3 1.75.35 2.63.07.9.2 1.76.26 2.6.07.97.16 1.78.2 2.56.08 1.23.6 3.96.2 4.06m23.7-28.57c-.23-.62-.67-1.76-.8-2.83-.07-.8-.03-3.4.23-4.27.25-.84.83-2.54 1.33-3.28.47-.7.97-1.24 1.73-1.76.87-.58 1.8-.16 2.2.57.53 1 .18 3.83 0 4.67-.17.8-.7 1.55-.78 2.32-.08.87.34 1.62.7 2.3.18.3.7.77 1.38 1.17.64.38 1.26.88 1.67.9.85.08 1.46-.27 1.96-.66.55-.4.95-.97 1.17-1.7.2-.64.46-1.37.46-2.08V16.6c0-.8-.1-1.6-.15-2.37-.05-.8 0-1.62-.14-2.38-.12-.8-.38-1.57-.65-2.3-.32-.84-.8-1.5-1.3-2.03-.55-.6-1.17-1.14-1.87-1.52-.7-.37-1.5-.5-2.27-.8-.73-.26-4.17-1.34-5-1.45-.94-.12-1.86-.1-2.7.15-.83.24-1.5.84-2.2 1.5-.6.53-1.12.87-1.63 1.74-.42.73-.74 1.54-1.07 2.35-.33.8-1.45 4.12-1.62 4.94-.17.8-.32 1.6-.4 2.4-.07.8 0 4.05.1 4.85.1.78.33 1.76.5 2.55.15.8.98 3.27 1.53 4.6.3.72.78 1.48 1.15 2.37.32.77.56 1.7.9 2.53.35.8 1.8 3.95 2.14 4.72.37.87.8 1.65 1.12 2.36.48 1.06 2 4.12 2.43 4.96.46.86.75 1.8 1.12 2.68.38.9.7 1.8.93 2.75.14.6.96 3.75 1.03 4.53.07.8-.1 3.4-.86 4.48-.56.83-.93 1.2-1.86 1.13-1.13-.1-1.83-.94-2.15-1.86-.4-1.12-.25-2.48-.25-2.9 0-.6.13-1.5.1-2.5 0-.8-.23-1.64-.38-2.46-.16-.86-.43-1.63-.85-2.3-.44-.7-.97-1.34-1.85-1.54-1-.23-1.85.05-2.63.23-.94.22-1.7.53-2.27 1.2-.56.66-1.07 1.45-1.2 2.4 0 0 .12 4.05.24 4.97.1.77.4 1.55.6 2.4.18.78.45 1.55.75 2.35.28.75 1.78 3.66 2.3 4.33.48.64 1 1.24 1.63 1.82.6.54 1.26 1 1.97 1.45.67.43 3.66 1.75 4.57 1.86.8.1 1.65.18 2.53.07.83-.1 1.62-.38 2.44-.72.77-.3 1.45-.75 2.16-1.3.65-.5 2.75-3.07 3.15-3.9.37-.75.84-1.55 1.07-2.32.22-.8.8-3.92.77-4.74-.02-.82-.14-1.65-.26-2.48-.1-.8-.58-1.53-.77-2.35-.2-.77-.42-1.55-.66-2.35-.24-.76-.53-1.52-.82-2.3-.27-.76-.6-1.5-.9-2.27-.32-.75-1.57-3.76-1.92-4.52l-1.03-2.22-1.05-2.2c-.35-.74-.5-1.57-.85-2.32-.34-.73-.7-1.46-1.06-2.2-.33-.75-.76-1.45-1.1-2.2-.33-.74-.57-1.53-.9-2.3-.32-.73-.58-1.5-.9-2.27-.3-.75-.67-1.48-.96-2.25M109.2 9.5c.23-.54.5-1.4.44-2.22-.07-.85-.44-1.7-.82-2.14-.4-.46-1.4-.75-2.36-.8-.92-.07-1.86.15-2.43.14-.94 0-1.9-.18-2.8-.2-.94 0-1.86-.03-2.8-.04-.9-.02-1.84.05-2.77.03-.9 0-1.83.17-2.78.16-.54 0-1.42-.13-2.36-.15-.78-.02-1.6 0-2.34.14-.9.16-1.6.43-1.95 1.05-.23.4-.76 1.15-.9 2.07-.1.7.16 1.55.25 2.28.1.8.3 1.47.77 2 .5.58 1.66.56 2.87.44 1.14-.1 3.5-.36 3.84-.14.26.16.13 1.18.32 2.3.12.7.12 1.52.15 2.46.03.74.02 4.04-.02 4.9-.03.78-.14 1.8-.2 2.65-.04.8-.33 3.84-.4 4.67-.05.8-.08 1.67-.14 2.5-.07.8-.03 1.58-.08 2.4-.06.8-.24 1.87-.3 2.7-.04.8-.56 6.2-.6 7.02-.03.82.05 3.93.02 4.72l-.07 2.6c-.03.84-.3 4.1-.33 4.87-.04.87-.32 3.98-.38 4.6-.08.8.05 1.52.17 2.22.1.63.22 1.35.7 2.08.44.67 1.54.82 2.63.7.84-.1 1.73-.4 2.6-.64 1.1-.3 2.03-.7 2.55-.73 1.22-.04 1.92-.47 2.27-1.14.34-.67.45-1.54.4-2.6-.06-.8-.28-1.7-.35-2.64l-.17-2.55-.12-2.54c-.03-.86-.1-1.7-.14-2.56l-.08-2.55c-.03-.85.06-1.7.05-2.56-.02-.85-.1-4.04-.1-4.9 0-.84-.06-1.92-.07-2.8 0-.82.1-1.85.1-2.72l.02-2.36c0-.8-.05-3.66-.07-4.45 0-.8-.06-1.94-.08-2.74-.03-.8-.1-1.6-.12-2.42-.03-.78-.2-2.93-.22-3.74l-.16-3.06c-.04-.76-.26-2.92-.26-3.5 0-.48 1.2-.33 2.6-.36.85-.03 1.65-.18 2.36-.22.9-.05 1.73.05 2.35-.26.65-.32 1.05-1.03 1.33-1.95m36.4 20.94c-.35-.92-.74-1.52-1.16-1.97-.63-.68-1.2-.84-1.97-.74-.68.08-1.33.4-2.08.87-.63.4-1.35.74-2 1.27l-1.8 1.5c-.62.53-1.12 1.2-1.72 1.65-.7.53-1.44.88-2.08 1.16-1.1.48-2.27 1.16-2.27 1.16-.3-.2.02-2.34.13-3.06.1-.62.23-1.68.43-2.76.15-.82.33-1.53.5-2.43l.57-2.65c.22-1.07.34-1.9.48-2.53.13-.6.27-1.42.5-2.35.18-.72.32-1.53.5-2.36.2-.76.35-1.56.5-2.36.15-.8.22-1.63.3-2.4.1-.86.15-1.65.13-2.4-.02-.9.06-1.8-.2-2.44-.33-.82-1.06-1.36-1.92-1.5-.94-.13-1.78-.2-2.45-.06-.87.2-1.38.9-1.9 1.45-.52.56-1.04.82-1.3 1.63-.23.74-.1 1.28-.2 2.1-.1.8-.24 1.58-.28 2.36-.05.88-.17 2.66-.26 3.48-.08.8-.5 3.02-.65 3.82-.13.8-.25 3.24-.44 4.35-.12.64-.2 3.04-.83 3.88-.54.74-1.46 1.1-2.55 1.13-1.04.05-1.8-.5-2.54-1.06-.57-.43-.58-2.2-.63-3.4-.02-.86.18-1.8.3-2.8.1-.94.1-1.92.27-2.78.22-1.2.34-2.23.43-2.77.1-.52.7-3.68.88-4.46.16-.74.52-1.46.66-2.18.16-.8.23-1.63.33-2.3.18-1.16.27-2 .1-2.2-.48-.52-1.35-.88-2.3-.98-.83-.1-1.67.24-2.45.5-.87.27-1.77.5-2.13 1.22-.16.3-.5 1.13-.77 2.34-.15.7-.75 3.96-.9 4.8-.12.78-.63 4.02-.74 4.84-.1.8.02 1.65-.08 2.45-.1.8-.2 1.64-.28 2.43-.07.84-.25 1.63-.3 2.42-.05.85-.15 4.18-.08 4.9.1 1.04.35 1.87.63 2.38.37.67.78 1.35 1.24 1.85.5.56.93.85 1.54 1.2.63.35 1.6.75 2.33.88l2.33.42c.8.13 1.5.14 2.3.27 1.35.22 3.54-.1 3.54-.12.02.13-1.6 1.23-2.75 1.83-.64.33-1.36.7-2.1 1.04-.72.36-1.46.7-2.1 1-1.04.48-1.85.9-2.1 1.02-.74.33-1.48.66-2.2 1-.75.34-1.52.68-2.22 1.07-.72.38-1.28.88-1.93 1.37-.64.48-1.7 1.4-2.25 2-.58.64-1.8 3.14-2.02 4-.22.82-.38 1.67-.45 2.54-.06.86-.27 1.75-.2 2.62.06.86.24 1.73.4 2.58.2.85 1.28 4 1.67 4.76.4.78.92 1.7 1.4 2.38.52.72 1.14 1.3 1.75 1.87.64.62 1.32 1.23 2.06 1.64 1.37.75 3.98 1.58 4.84 1.65 1.7.14 4.35-.15 5.1-.4 1.8-.63 3.28-2.23 3.85-2.82.57-.6 1.26-1.36 1.76-2 .5-.67 1.98-3.52 2.3-4.25.38-.87.64-1.85.85-2.7.22-.87.38-1.75.47-2.7.08-.84.12-1.74.15-2.72.02-.83-.06-1.74-.05-2.72 0-.83.08-1.7.02-2.56-.06-.86-.25-1.72-.23-2.56 0-.9-.18-2.34-.1-2.66.13-.46 1.43-1.25 2.17-1.8.75-.56 4.03-2.37 4.8-2.84.83-.5 1.52-1.1 2.23-1.68.5-.4 1.27-.9 1.98-1.5.6-.53 3-3.02 3.3-3.7.4-.8.17-1.65-.1-2.44m-20.3 25.9c-.1 1.05-.2 1.92-.43 2.68-.25.82-.6 1.6-1.05 2.52-.4.8-.96 1.5-1.55 1.95-.65.5-1.5.66-2.34.75-.73.07-1.53-.05-2.4-.3-.83-.27-1.63-.56-2.12-1.08-.54-.6-.8-1.42-.96-2.2-.15-.74-.34-1.57-.14-2.37.42-1.6 1.65-3.85 2.2-4.42.38-.42 1.04-1.14 1.78-1.72.6-.45 1.12-.87 1.78-1.32.7-.45 1.43-.9 2.06-1.24.9-.5 2.95-2.03 3.1-1.6.32 1-.1 1.16.07 3.6.05.63.05 1.33 0 2.38-.03.72.12 1.4.02 2.35"),(0,i.Lj)(o,"fill","none"),(0,i.Lj)(o,"fillrule","evenodd"),(0,i.Lj)(e,"width","75"),(0,i.Lj)(e,"height","38"),(0,i.Lj)(e,"fill","none"),(0,i.Lj)(e,"viewBox","0 0 150 76")},m:function(t,c){(0,i.$T)(t,e,c),(0,i.R3)(e,n),(0,i.R3)(n,r),(0,i.R3)(e,o),(0,i.R3)(o,a),(0,i.R3)(o,s)},p:i.ZT,i:i.ZT,o:i.ZT,d:function(t){t&&(0,i.og)(e)}}}var d=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,null,f,i.N8,{}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_);function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function m(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return h(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return h(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t){var e,n,r,o,a,s;return{c:function(){e=(0,i.bi)("svg"),n=(0,i.bi)("title"),r=(0,i.fL)("BuzzFeed News"),o=(0,i.bi)("g"),a=(0,i.bi)("path"),s=(0,i.bi)("path"),(0,i.Lj)(a,"d","M1 15.22A1.31 1.31 0 0 0 1.5 14V3A1.31 1.31 0 0 0 1 1.74L0 1V.68h7.08c4.18 0 5.4 1.54 5.4 3.76v.1c0 1.78-1.36 3.2-4.62 3.54 3.86.14 5.42 1.6 5.42 4v.12c0 2.84-2.08 4.08-6 4.08H0V16zm4-7.34h1.68c1.62 0 2.22-.54 2.22-3.1v-.3c0-2.42-.52-3-2.22-3H5zm4.48 4.46v-.42c0-2.48-.72-3.24-2.82-3.24H5v6c0 .56.2.8 1.1.8h.6c2.06 0 2.82-.6 2.82-3.14zm4.92-5.22a1.31 1.31 0 0 0-.52-1.22l-.66-.52v-.3h4.46v7.8c0 .92.36 1.4 1.36 1.4a3.72 3.72 0 0 0 1.46-.34V7.36a1.31 1.31 0 0 0-.5-1.22l-1-.76v-.3h4.76v9.16a1.4 1.4 0 0 0 .52 1.22l.56.52v.3H21l-.26-1.82a4.09 4.09 0 0 1-3.44 2c-1.84 0-2.9-1.28-2.9-3.32zm17-1.24h-1.63c-.8 0-1 .08-1.46.84L26 10.22h-.3V5.08h8.92v1.6l-5.72 8.8h1.52c.8 0 1-.08 1.46-.84l2.4-3.7h.3v5.34h-8.89v-1.6zm10.38 0h-1.63c-.8 0-1 .08-1.46.84l-2.28 3.5h-.3V5.08H45v1.6l-5.72 8.8h1.52c.8 0 1-.08 1.46-.84l2.4-3.7H45v5.34h-8.93v-1.6zm5.16 9.34a1.31 1.31 0 0 0 .53-1.22V3a1.31 1.31 0 0 0-.52-1.22L46 1V.68h12v4.84h-.3L55.31 2.4a1.77 1.77 0 0 0-1.78-.92H51V8h1.48c.38 0 .6-.08 1-.56l2.08-2.7h.3V12h-.3l-2.08-2.7c-.36-.48-.58-.56-1-.56H51V14a1 1 0 0 0 .66 1l1.94 1v.3H46V16zm15.28 1.26c-2.88 0-4.92-2.24-4.92-5.62v-.06c0-3.38 2.44-5.92 5-5.92a3.61 3.61 0 0 1 4 3.9V10h-5.7c.2 3.06 1.6 4.3 3.4 4.3a4.62 4.62 0 0 0 2.22-.66h.3v.88a5.59 5.59 0 0 1-4.29 1.96zm-1.7-7.26h2.94V7.84c0-1.68-.32-2.2-1.22-2.2-1.1 0-1.72 1-1.72 3.2zm11.34 7.26C69 16.48 67 14.24 67 10.86v-.06c0-3.38 2.44-5.92 5-5.92a3.61 3.61 0 0 1 4 3.9V10h-5.7c.2 3.06 1.6 4.3 3.4 4.3a4.62 4.62 0 0 0 2.22-.66h.3v.88a5.59 5.59 0 0 1-4.34 1.96zm-1.7-7.26h2.94V7.84c0-1.68-.32-2.2-1.22-2.2-1.1 0-1.72 1-1.72 3.2zm6.42 1.42C76.6 6.9 79 4.88 82 4.88a6.25 6.25 0 0 1 2.06.4v-2c0-.58-.1-.72-.48-.94L82 1.4v-.3L87 0h.3v14.24a1.27 1.27 0 0 0 .56 1.18l.72.56v.3h-4l-.26-1.82a4.07 4.07 0 0 1-3.52 2c-2.48 0-4.18-2.12-4.18-5.74zm5.86 3.64a3.5 3.5 0 0 0 1.6-.34V9.12c0-2.6-.86-3.44-2.06-3.44s-2.12.92-2.12 3.66v.3c.02 3.48 1.14 4.64 2.6 4.64zM92 1.66l-.52-.48v-.3h4.84L103 11.56V6.22a3.5 3.5 0 0 0-.64-2.06l-1.84-3V.88h4.88v.3l-1.24 3.2a4.13 4.13 0 0 0-.36 1.84v10.06h-2.2L94.1 4.22A11.25 11.25 0 0 0 92 1.66zm18 14.82c-2.88 0-4.92-2.24-4.92-5.62v-.06c0-3.38 2.44-5.92 5-5.92a3.61 3.61 0 0 1 4 3.9V10h-5.7c.2 3.06 1.6 4.3 3.4 4.3a4.62 4.62 0 0 0 2.22-.66h.3v.88a5.59 5.59 0 0 1-4.3 1.96zm-1.7-7.26h2.94V7.84c0-1.68-.32-2.2-1.22-2.2-1.1 0-1.72 1-1.72 3.2zm5.56-3.68l-.16-.16v-.3h5.26v.3l-.42.4a1 1 0 0 0-.32 1.1l1.62 5.86 1.36-5-.72-2.38v-.3h3.32l2.2 7.76.9-3.1a2.51 2.51 0 0 0 0-1.44l-.86-2.9v-.3H130v.3l-.4.52a9.85 9.85 0 0 0-1.86 3.72l-1.92 6.66h-2.06l-2.16-7.2-1.94 7.2h-2.06l-2.4-8.36a4.17 4.17 0 0 0-1.36-2.38zm15.66 6.22h.3l1.76 2.72c.62 1 1.08 1.24 1.86 1.24 1 0 1.64-.5 1.64-1.16a1.86 1.86 0 0 0-1-1.62l-2.34-1.54a3.75 3.75 0 0 1-1.9-3.16v-.06c0-1.8 1.52-3.3 4.1-3.3a14.36 14.36 0 0 1 3.26.4v4h-.3L135.37 7c-.7-1.08-1.2-1.32-1.88-1.32-.92 0-1.44.48-1.44 1.14s.22 1 1 1.44l2.2 1.32c1.74 1 2.28 2.12 2.28 3.34V13c0 2.08-2.08 3.5-4.3 3.5a18.07 18.07 0 0 1-3.72-.44z"),(0,i.Lj)(s,"d","M93.66 16.48a2.67 2.67 0 0 1-2.72-2.74 2.72 2.72 0 1 1 5.44 0 2.67 2.67 0 0 1-2.72 2.74z"),(0,i.Lj)(s,"fill","#f4392f"),(0,i.Lj)(e,"viewBox","0 0 137.53 16.48"),(0,i.Lj)(e,"role","img"),(0,i.Lj)(e,"width",t[0]),(0,i.Lj)(e,"height",t[1])},m:function(t,c){(0,i.$T)(t,e,c),(0,i.R3)(e,n),(0,i.R3)(n,r),(0,i.R3)(e,o),(0,i.R3)(o,a),(0,i.R3)(o,s)},p:function(t,n){var r=m(n,1)[0];1&r&&(0,i.Lj)(e,"width",t[0]),2&r&&(0,i.Lj)(e,"height",t[1])},i:i.ZT,o:i.ZT,d:function(t){t&&(0,i.og)(e)}}}function v(t,e,n){var r=e.width,i=e.height;return t.$$set=function(t){"width"in t&&n(0,r=t.width),"height"in t&&n(1,i=t.height)},[r,i]}var g=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,v,p,i.N8,{width:0,height:1}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_);function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function b(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return y(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return y(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function A(t){var e,n,r,o,a,s,c,u;return{c:function(){e=(0,i.bi)("svg"),n=(0,i.bi)("rect"),o=(0,i.bi)("defs"),a=(0,i.bi)("pattern"),s=(0,i.bi)("use"),u=(0,i.bi)("image"),(0,i.Lj)(n,"width","81"),(0,i.Lj)(n,"height","33"),(0,i.Lj)(n,"fill",r="url(#pattern0"+t[2]+")"),(0,i.G_)(s,"xlink:href","#image0_677_111"),(0,i.Lj)(s,"transform","matrix(0.00378788 0 0 0.00925926 -0.0681818 0)"),(0,i.Lj)(a,"id",c="pattern0"+t[2]),(0,i.Lj)(a,"patternContentUnits","objectBoundingBox"),(0,i.Lj)(a,"width","1"),(0,i.Lj)(a,"height","1"),(0,i.Lj)(u,"id","image0_677_111"),(0,i.Lj)(u,"width","282"),(0,i.Lj)(u,"height","108"),(0,i.G_)(u,"xlink:href","data:image/png;base64,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"),(0,i.Lj)(e,"title",t[0]),(0,i.Lj)(e,"extraclasses",t[1]),(0,i.Lj)(e,"width","81"),(0,i.Lj)(e,"height","33"),(0,i.Lj)(e,"viewBox","0 0 81 33")},m:function(t,r){(0,i.$T)(t,e,r),(0,i.R3)(e,n),(0,i.R3)(e,o),(0,i.R3)(o,a),(0,i.R3)(a,s),(0,i.R3)(o,u)},p:function(t,o){var s=b(o,1)[0];4&s&&r!==(r="url(#pattern0"+t[2]+")")&&(0,i.Lj)(n,"fill",r),4&s&&c!==(c="pattern0"+t[2])&&(0,i.Lj)(a,"id",c),1&s&&(0,i.Lj)(e,"title",t[0]),2&s&&(0,i.Lj)(e,"extraclasses",t[1])},i:i.ZT,o:i.ZT,d:function(t){t&&(0,i.og)(e)}}}function w(t,e,n){var r=e.title,i=e.extraClasses,o=e.wid;return t.$$set=function(t){"title"in t&&n(0,r=t.title),"extraClasses"in t&&n(1,i=t.extraClasses),"wid"in t&&n(2,o=t.wid)},[r,i,o]}var E=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,w,A,i.N8,{title:0,extraClasses:1,wid:2}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_),T=n(93468);function R(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function P(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return R(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return R(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function L(t){(0,i.qO)(t,"svelte-bruinv","@media(min-width: 40rem){.ad-toolbar.loading.svelte-bruinv{position:fixed;right:-500px}}")}function x(t){var e,n,r,o,a,s,c;return r=new l({}),s=new E({props:{wid:t[0].wid}}),{c:function(){e=(0,i.bG)("a"),n=(0,i.bG)("div"),(0,i.YC)(r.$$.fragment),o=(0,i.Dh)(),a=(0,i.bG)("div"),(0,i.YC)(s.$$.fragment),(0,i.Lj)(n,"class","ad-toolbar__bflogo"),(0,i.Lj)(a,"class","ad-toolbar__badges"),(0,i.Lj)(e,"href","https://www.buzzfeed.com?origin=tb"),(0,i.Lj)(e,"class","ad-toolbar__images")},m:function(t,u){(0,i.$T)(t,e,u),(0,i.R3)(e,n),(0,i.ye)(r,n,null),(0,i.R3)(e,o),(0,i.R3)(e,a),(0,i.ye)(s,a,null),c=!0},p:function(t,e){var n={};1&e&&(n.wid=t[0].wid),s.$set(n)},i:function(t){c||((0,i.Ui)(r.$$.fragment,t),(0,i.Ui)(s.$$.fragment,t),c=!0)},o:function(t){(0,i.et)(r.$$.fragment,t),(0,i.et)(s.$$.fragment,t),c=!1},d:function(t){t&&(0,i.og)(e),(0,i.vp)(r),(0,i.vp)(s)}}}function k(t){var e,n,r;return n=new g({props:{width:"82",height:"52"}}),{c:function(){e=(0,i.bG)("a"),(0,i.YC)(n.$$.fragment),(0,i.Lj)(e,"href","https://www.buzzfeednews.com?origin=tb"),(0,i.Lj)(e,"class","ad-toolbar__images--bfn")},m:function(t,o){(0,i.$T)(t,e,o),(0,i.ye)(n,e,null),r=!0},p:i.ZT,i:function(t){r||((0,i.Ui)(n.$$.fragment,t),r=!0)},o:function(t){(0,i.et)(n.$$.fragment,t),r=!1},d:function(t){t&&(0,i.og)(e),(0,i.vp)(n)}}}function O(t){var e,n,r;return n=new d({}),{c:function(){e=(0,i.bG)("a"),(0,i.YC)(n.$$.fragment),(0,i.Lj)(e,"href","https://tasty.co?origin=tb"),(0,i.Lj)(e,"class","ad-toolbar__images--tasty")},m:function(t,o){(0,i.$T)(t,e,o),(0,i.ye)(n,e,null),r=!0},p:i.ZT,i:function(t){r||((0,i.Ui)(n.$$.fragment,t),r=!0)},o:function(t){(0,i.et)(n.$$.fragment,t),r=!1},d:function(t){t&&(0,i.og)(e),(0,i.vp)(n)}}}function C(t){var e,n,r,a,s,c,u,l,f,d,h,m,p,v,g,y,b,A,w,E,R,L=function(t,e){return"tasty"===t[0].zone1?0:"bfnews"===t[0].zone1?1:2};n=new o.Z({props:{config:t[0]}}),c=new T.Z({props:{title:"Dismiss Ad",width:15}});var C=[O,k,x],I=[];return l=L(t),f=I[l]=C[l](t),y=new T.Z({props:{title:"Dismiss Ad"}}),{c:function(){e=(0,i.bG)("div"),(0,i.YC)(n.$$.fragment),r=(0,i.Dh)(),a=(0,i.bG)("div"),s=(0,i.bG)("div"),(0,i.YC)(c.$$.fragment),u=(0,i.Dh)(),f.c(),d=(0,i.Dh)(),h=(0,i.bG)("div"),v=(0,i.Dh)(),g=(0,i.bG)("div"),(0,i.YC)(y.$$.fragment),(0,i.Lj)(s,"class","ad-toolbar__arrow md-caret"),(0,i.Lj)(h,"id",m="div-gpt-ad-"+t[0].wid),(0,i.Lj)(h,"class",p="ad-slot js-ad-slot js-ad-slot-"+t[0].wid+" ad-toolbar__ad svelte-bruinv"),(0,i.Lj)(g,"class","ad-toolbar__arrow xs-caret"),(0,i.Lj)(a,"class",b="ad-toolbar loading "+("tasty"===t[0].zone1?"ad-toolbar--tasty":"")+" svelte-bruinv"),(0,i.Lj)(e,"id",A="BF_WIDGET_"+t[0].wid),(0,i.Lj)(e,"data-module","ad-toolbar"),(0,i.Lj)(e,"data-bfa","@l:Toolbar;")},m:function(t,o){(0,i.$T)(t,e,o),(0,i.ye)(n,e,null),(0,i.R3)(e,r),(0,i.R3)(e,a),(0,i.R3)(a,s),(0,i.ye)(c,s,null),(0,i.R3)(a,u),I[l].m(a,null),(0,i.R3)(a,d),(0,i.R3)(a,h),(0,i.R3)(a,v),(0,i.R3)(a,g),(0,i.ye)(y,g,null),w=!0,E||(R=[(0,i.oL)(s,"click",S),(0,i.oL)(g,"click",S)],E=!0)},p:function(t,r){var o=P(r,1)[0],s={};1&o&&(s.config=t[0]),n.$set(s);var c=l;(l=L(t))===c?I[l].p(t,o):((0,i.dv)(),(0,i.et)(I[c],1,1,(function(){I[c]=null})),(0,i.gb)(),(f=I[l])?f.p(t,o):(f=I[l]=C[l](t)).c(),(0,i.Ui)(f,1),f.m(a,d)),(!w||1&o&&m!==(m="div-gpt-ad-"+t[0].wid))&&(0,i.Lj)(h,"id",m),(!w||1&o&&p!==(p="ad-slot js-ad-slot js-ad-slot-"+t[0].wid+" ad-toolbar__ad svelte-bruinv"))&&(0,i.Lj)(h,"class",p),(!w||1&o&&b!==(b="ad-toolbar loading "+("tasty"===t[0].zone1?"ad-toolbar--tasty":"")+" svelte-bruinv"))&&(0,i.Lj)(a,"class",b),(!w||1&o&&A!==(A="BF_WIDGET_"+t[0].wid))&&(0,i.Lj)(e,"id",A)},i:function(t){w||((0,i.Ui)(n.$$.fragment,t),(0,i.Ui)(c.$$.fragment,t),(0,i.Ui)(f),(0,i.Ui)(y.$$.fragment,t),w=!0)},o:function(t){(0,i.et)(n.$$.fragment,t),(0,i.et)(c.$$.fragment,t),(0,i.et)(f),(0,i.et)(y.$$.fragment,t),w=!1},d:function(t){t&&(0,i.og)(e),(0,i.vp)(n),(0,i.vp)(c),I[l].d(),(0,i.vp)(y),E=!1,(0,i.j7)(R)}}}function S(t){if(t&&t.target){var e=t.target.closest(".ad-toolbar");e.classList.contains("open")?e.classList.replace("open","close"):e.classList.contains("open--no-animate")?e.classList.replace("open--no-animate","close"):e.classList.contains("close")?e.classList.replace("close","open"):e.classList.add("close")}}function I(t,e,n){var r=e.slot;return window.document.body.classList.add("has-tb-ads"),t.$$set=function(t){"slot"in t&&n(0,r=t.slot)},[r]}var j=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,I,C,i.N8,{slot:0},L),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},15332:function(t,e,n){"use strict";n.d(e,{R:function(){return i}});var r=n(70833),i=function(t){var e=t.env;return e.isBPage?((0,r.A)("info","lifecycle","toolbar >> enabled for mweb bpage",{env:e}),!0):e.isFeedpager&&(e.isBFO||e.isBFN)?((0,r.A)("info","lifecycle","toolbar >> enabled for mweb feedpager",{env:e}),!0):e.isFeedPage&&"shopping"===e.pageCategory?((0,r.A)("info","lifecycle","toolbar >> enabled for mweb shopping_ui",{env:e}),!0):"topic"===e.pageName||"standard_page"===e.pageName?((0,r.A)("info","lifecycle","toolbar >> enabled for mweb feed_ui ".concat(e.pageName),{env:e}),!0):"buzzfeed"===e.destination&&"en-us"===e.localization.edition&&"home"===e.pageId&&"home"===e.pageName?((0,r.A)("info","lifecycle","toolbar >> enabled for mweb feed_ui home",{env:e}),!0):((0,r.A)("info","lifecycle","toolbar >> DISABLED",{env:e}),!1)}},70753:function(t,e,n){"use strict";n.d(e,{q$:function(){return r},bB:function(){return i},Ii:function(){return a},nC:function(){return o},q0:function(){return s},i7:function(){return c},y0:function(){return u}});var r=50,i=32,o="stopRefresh",a="pauseRefresh",s={default:{inViewSeconds:32,maxRefreshes:9999,emptyWaitSec:i,infinite:!0},globalOverride:{edition:{"ja-jp":{inViewSeconds:32}}}},c={infinite:!0,inViewSeconds:30},u={maxRefreshes:9999,inViewSeconds:32,emptyWaitSec:15,pages:[{adType:"toolbar",adPos:"tb1",wid:"52-1",targeting:{wid:"52-1",pos:["tb1"]}},{adType:"toolbar",adPos:"tb2",wid:"52-2",targeting:{wid:"52-2",pos:["tb2"]}},{adType:"toolbar",adPos:"tb3",wid:"52-3",targeting:{wid:"52-3",pos:["tb3"]}}]}},67419:function(t,e,n){"use strict";n.d(e,{Z:function(){return re}});var r=n(42235),i=n(3379),o=n(94776),a=n.n(o),s=n(20238),c=n(75951),u=(n(40237),n(71288));function l(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(u){return void n(u)}s.done?e(c):Promise.resolve(c).then(r,i)}function f(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function a(t){l(o,r,i,a,s,"next",t)}function s(t){l(o,r,i,a,s,"throw",t)}a(void 0)}))}}var d="permutive-test"in({queryStringToObject:s.jH}.queryStringToObject(window.location.search)||{}),h=function(){var t,e=(0,s.jH)(null===window||void 0===window||null===(t=window.location)||void 0===t?void 0:t.search);return"sdxa"===(null===e||void 0===e?void 0:e.origin)}();function m(t){return p.apply(this,arguments)}function p(){return(p=f(a().mark((function t(e){var n;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.abeagle,!d){t.next=3;break}return t.abrupt("return",Promise.resolve(!0));case 3:if(!h){t.next=5;break}return t.abrupt("return",Promise.resolve(!1));case 5:if(!c.MS.needsConsent()){t.next=11;break}return t.next=8,c.jQ.hasConsented("permutive");case 8:if(t.sent&&!u.ZP.isMobileApp()){t.next=11;break}return t.abrupt("return",Promise.resolve(!1));case 11:return t.abrupt("return",n.getExperimentVariant("ADSGROUP-442-permutive").catch((function(t){return t&&"ExperimentNotFound"===t.type?"on":null})).then((function(t){return Promise.resolve("on"===t)})));case 12:case"end":return t.stop()}}),t)})))).apply(this,arguments)}var v,g=n(60736),y=n(34686),b=!1;function A(t,e){var n=t.data||{},r=n.source,i=n.action,o=n.data;if("dfpNativeTemplate"===r&&i&&"object"===typeof o&&null!==o){var a=function(t){for(var e in t){if(/Encoded$/.test(e))t[e.replace("Encoded","")]=decodeURIComponent(t[e])}for(var n in t){var r=t[n];"string"!==typeof r||/Encoded$/.test(n)||(t[n]=r.trim().replace(/\\('|\u2019)/g,"$1").replace(/\s*\n\s*/g,"\n").replace(/\s{2,}/g," "))}return t}(o);e.trigger("post-message:".concat(a.wid),{action:i,ad:a})}else"adlibSnippet"===r?e.trigger("post-message:creativeSnippet",t.data):"fan"===r&&"error"===i&&"object"===typeof o&&null!==o?e.trigger("post-message:".concat(o.wid),{action:"error",error:"FAN"}):"jumbotron"===r&&e.trigger("post-message:jumbotron:".concat(o.wid),{action:i,data:o})}b=!1,v=void 0;var w={start:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};b||(b=!0,v=function(e){return w.handleMessageEvent(e,t.eventBus)},window.addEventListener("message",v))},handleMessageEvent:function(t,e){(function(t){var e=t.origin;if(!e)return!1;var n=navigator.userAgent.match(/Edge/i)&&navigator.userAgent.match(/Windows/i);return/(?:buzzfeed(?:news)?\.(?:com|io)|tasty\.co|googlesyndication\.com)/.test(e)||n&&/^javascript:/.test(e)})(t)&&A(t,e)},stop:function(){window.removeEventListener("message",v)}},E=n(18977),T=["2162785669"],R=["10226773"],P=["20072413","102536773","35253013","495329533","12342133","263657533","12340813","135123253","235801453"],L=n(99945),x=n(89809),k=n(58451);var O,C={loadScript:r.v},S={init:function(t){var e=t.env,n=t.abeagle;return O||(O=n.isOn("ads_ad_lightning").then((function(t){return t?Promise.resolve(!0):Promise.reject(new k.x9)})).then((function(){var t=e.isDev||e.isStage?"buzzfeed-staging":"buzzfeed";return C.loadScript("".concat("https://tagan.adlightning.com","/").concat(t,"/").concat("op.js"))})).catch((function(t){return e=t,(null!=(n=k.x9)&&"undefined"!==typeof Symbol&&n[Symbol.hasInstance]?n[Symbol.hasInstance](e):e instanceof n)?null:Promise.reject(t);var e,n})))}},I=n(39782),j=n(22299),M=n(53709),_=n(63375),D=n(70833);function B(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function z(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||$(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(t){return function(t){if(Array.isArray(t))return B(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||$(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(t,e){if(t){if("string"===typeof t)return B(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?B(t,e):void 0}}function Z(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}}var U,G={loadScript:r.v},V=["ids","bsc","vlp","tvp","abs"],F="https://pub.doubleverify.com/signals/pub.js#ctx=".concat("24202625","&cmp=").concat("DV734342"),J=new y.BH,H=new y.BH,W={buzzfeed:_.ds,buzzfeed_news:_.uY,bfnews:_.uY,buzzfeednews:_.uY,tasty:_.aL},q={isOn:Z((function(t){var e=t.abeagle;return Promise.all([e.isOn(at),q.isDisabled()]).then((function(t){var e=z(t,2),n=e[0],r=e[1];return n&&!r}))})),tagReady:U||J,targetingDataReady:H,isDisabled:function(){return c.jQ.hasConsented("doubleverify").then((function(t){return(0,D.A)("info","doubleverify","DoubleVerify signal consented:",t),!t||"sdxa"===(0,M.P$)()}))},init:function(t){var e=t.abeagle,n=t.env;return U||(window.PQ=window.PQ||{cmd:[]},U=q.isOn({abeagle:e}).then((function(t){if(!t)throw new k.x9("DoubleVerify signal is disabled")})).then((function(){return G.loadScript(F)})).then((function(){return new Promise((function(t){window.PQ.cmd.push((function(){J.resolve(window.PQ),t(window.PQ)}));var e="";"object"===typeof(null===n||void 0===n?void 0:n.destination)?e=W[n.destination.find((function(t){return W[t]}))]:"string"===typeof(null===n||void 0===n?void 0:n.destination)&&(e=null===W||void 0===W?void 0:W[null===n||void 0===n?void 0:n.destination]),e||(e=_.ds);var r=(0,M.fd)({});window.PQ.cmd.push((function(){window.PQ.getTargeting({signals:V,adUnits:[{adUnitPath:"/".concat(_.M9,"/").concat(e,".").concat(r,"/connatix"),sizes:"480x360,640x480,640x360"}]},(function(t,e){t?console.error(t,e):H.resolve(window.PQ)}))}))}))})).catch((function(t){return e=t,(null!=(n=k.x9)&&"undefined"!==typeof Symbol&&n[Symbol.hasInstance]?n[Symbol.hasInstance](e):e instanceof n)?null:Promise.reject(t);var e,n})))},loadSignals:Z((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"page",e=arguments.length>1?arguments[1]:void 0;return q.tagReady.then((function(n){try{"page"===t?n.loadSignals(V,e):n.loadSignalsForSlots(t,e)}catch(r){return console.error(r),Promise.reject()}return Promise.resolve()}))})),runOnce:function(t){for(var e=this,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];var o=!1;return function(){o||(o=!0,t.apply(e,N(r)))}}},Y={CLIENT_CODE:"34448457",SETTINGS_CODE:"DV1395182"};function X(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Q(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return X(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return X(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var K,tt,et={loadScript:r.v},nt=new y.BH,rt={isOn:(tt=function(t){var e=t.abeagle;return Promise.all([e.isOn("ads_doubleverify"),rt.isDisabled()]).then((function(t){var e=Q(t,2),n=e[0],r=e[1];return n&&!r}))},function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];try{return Promise.resolve(tt.apply(this,t))}catch(n){return Promise.reject(n)}}),isDisabled:function(){return c.jQ.hasConsented("doubleverify").then((function(t){return(0,D.A)("info","doubleverify","DoubleVerify unity consented:",t),!t||"sdxa"===(0,M.P$)()}))},tagReady:nt||K,init:function(t){var e=t.abeagle;return K=rt.isOn({abeagle:e}).then((function(t){if(!t)throw new k.x9("DoubleVerify is disabled");var e="https://pub.doubleverify.com/dvtag/{CLIENT_CODE}/{SETTINGS_CODE}/pub.js";return e=e.replace("{CLIENT_CODE}",Y.CLIENT_CODE).replace("{SETTINGS_CODE}",Y.SETTINGS_CODE),(0,D.A)("info","thirdparty","doubleverify CODES",Y,e),et.loadScript(e)})).then((function(){return new Promise((function(t){if((window.onDvtagReady=rt.onDvtagReady)((function(){nt.resolve(window.dvtag)})),!window.PQ&&window.dvtag){var e=window.dvtag;window.PQ=window.PQ||{},window.PQ.getTargeting=e.getTargeting,window.PQ.PTS=e.getTargeting(),window.PQ.BFFIX=!0}t()}))})).catch((function(t){var e,n;e=t,n=k.x9,K=(null!=n&&"undefined"!==typeof Symbol&&n[Symbol.hasInstance]?n[Symbol.hasInstance](e):e instanceof n)?null:Promise.reject(t),nt.resolve()}))},onDvtagReady:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:500;(0,D.A)("info","thirdparty","doubleverify UNIT onDvtagReady called",{callback:t,timeout:e});var n=window.dvtag||{};n.cmd=n.cmd||[];var r={callback:t,timeout:e,timestamp:(new Date).getTime()};n.cmd.push((function(){n.queueAdRequest(r)})),setTimeout((function(){var t=r.callback;r.callback=null,t&&t()}),e)},defineDvtagSlot:function(t,e){if(window){(0,D.A)("info","thirdparty","doubleverify > Unity > defineDvtagSlot",{elementId:t,params:e});var n=window.dvtag||{};n.cmd=n.cmd||[],n.cmd.push((function(){n.defineSlot(t,e)}))}},getDvtagTargeting:function(t){if(!window)return{};(0,D.A)("info","thirdparty","doubleverify > Unity > getDvtagTargeting",t);var e=window.dvtag||{};return e.cmd=e.cmd||[],e.getTargeting?e.getTargeting(t):{}},updateDvTagTargeting:function(t){window&&t.forEach((function(t){var e=t.getSlotElementId(),n=rt.getDvtagTargeting(e);t.updateTargetingFromMap(n)}))}};function it(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function ot(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return it(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return it(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var at="ads_doubleverify";var st={isOn:function(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}}((function(t){var e=t.abeagle;return Promise.all([e.isOn(at),st.isDisabled()]).then((function(t){var e=ot(t,2),n=e[0],r=e[1];return n&&!r}))})),isDisabled:function(){return c.jQ.hasConsented("doubleverify").then((function(t){return(0,D.A)("info","doubleverify","DoubleVerify consented:",t),!t||"sdxa"===(0,M.P$)()}))},mapInit:function(t){return"signals"===t?q:"unity"===t?rt:function(){return Promise.resolve()}},isOnAndSignals:function(t,e){return t.abeagle.isOn(at).then((function(t){return t&&"signals"===e&&!q.isDisabled()?[t,q.tagReady]:[!1]}))},isOnAndUnity:function(t,e){return t.abeagle.isOn(at).then((function(t){return t&&"unity"===e&&!rt.isDisabled()?[t,rt.tagReady]:[!1]}))},signals:q,unity:rt};var ct;var ut,lt={init:function(){return ct||(ct=new Promise((function(t,e){var n=document.createElement("script");n.onload=function(){return t(n)},n.onerror=function(){e("Teads: Script failed to load")},n.src="https://a.teads.tv/analytics/tag.js",n.async=!0,document.head.appendChild(n)})).then(function(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}}((function(){return t=new Promise((function(t){window.teads_analytics=window.teads_analytics||{},t(window.teads_analytics)})),e=function(){window.teads_analytics.analytics_tag_id="PUB_14692",window.teads_analytics.share=window.teads_analytics.share||function(){(window.teads_analytics.shared_data=window.teads_analytics.shared_data||[]).push(arguments)}},n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t);var t,e,n})),(function(){return console.error("Teads failed to initialize"),Promise.reject(new k.ZP("Teads failed to initialize"))})))}},ft=n(36606),dt=n(1915),ht=n(77892),mt=n(83509);var pt=function(t){(0,D.A)("info","general","Sending hashed email to LiveRamp","");var e=atob(t.replace(/"/g,""));"undefined"!==typeof atsenvelopemodule&&atsenvelopemodule.setAdditionalData&&atsenvelopemodule.setAdditionalData({type:"emailHashes",id:[e]})},vt={init:function(t){var e=t.env;return ut||(ut=function(t){var e=t.isBFO||t.isBFN,n=i.Z.get("bf2-b_info");return e&&n&&!c.MS.needsGDPRConsent()?Promise.resolve():((0,D.A)("info","general","LiveRamp is not enabled",""),Promise.reject(new k.x9))}(e).then((function(){document.cookie.match(/_lr_env=/)||window.addEventListener("envelopeModuleReady",(function(){var t=i.Z.get("hem");t?pt(t):window.addEventListener("hemCookieSet",(function(t){var e;pt(null===t||void 0===t||null===(e=t.detail)||void 0===e?void 0:e.cookieValue)}))}))})).catch((function(t){return e=t,(null!=(n=k.x9)&&"undefined"!==typeof Symbol&&n[Symbol.hasInstance]?n[Symbol.hasInstance](e):e instanceof n)?null:Promise.reject(t);var e,n})))}},gt=n(48307);var yt=n(88262);function bt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function At(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function wt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){At(t,e,n[e])}))}return t}function Et(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||Rt(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tt(t){return function(t){if(Array.isArray(t))return bt(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||Rt(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Rt(t,e){if(t){if("string"===typeof t)return bt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?bt(t,e):void 0}}function Pt(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}var Lt=xt((function(){return Pt(Zt.get("env","abeagle","gdpr","ccpa"),(function(t){var e=Et(t,4),n=e[0],r=e[1],o=e[2],a=e[3];return Pt(Promise.all([Ot,a.getConsentValue(),o.hasConsentedGoogle(),Nt.checkIsOn({abeagle:r,gdpr:o}),It({env:n,abeagle:r,gdpr:o})]),(function(t){var e=Et(t,5),a=e[0],s=e[1],c=e[2],u=e[3],l=e[4];return o.hasConsentedTracking().then((function(t){if((0,D.A)("info","consent","hasConsentedTracking",t),!t){var e=i.Z.getBuzzfeedSubdomainOrWildcard(window.location.hostname);i.Z.remove("bf-affiliate",e)}})),(0,D.A)("info","consent","_configureGPT -> dependencies RESOLVED"),(0,D.A)("info","consent","ccpaConsent",s),(0,D.A)("info","consent","personalizationConsent",c),(0,D.A)("info","adCall","permutiveOn",u),Pt(jt(u),(function(){a.cmd.push(xt((function(){if(Qt({googletag:a,targeting:l,env:n,abeagle:r}),te({googletag:a,settings:Kt({env:n})}),a.pubads().setPrivacySettings({restrictDataProcessing:Xt(s)}),a.pubads().disableInitialLoad(),a.pubads().enableAsyncRendering(),a.pubads().enableSingleRequest(),a.pubads().addEventListener("slotOnload",_t),a.pubads().addEventListener("slotRenderEnded",Bt),a.pubads().addEventListener("slotRequested",Dt),a.pubads().addEventListener("slotVisibilityChanged",Mt),a.pubads().addEventListener("impressionViewable",zt),u){var t=a.pubads().getTargeting("permutive");if(0===(null===t||void 0===t?void 0:t.length)){var e=localStorage.getItem("_pdfps");a.pubads().setTargeting("permutive",e?JSON.parse(e):[])}}return a.enableServices(),st.isOnAndSignals({abeagle:r},Jt).then((function(t){var e=Et(t,1)[0];(0,D.A)("info","thirdparty","#configureGPT: doubleverify > signals: is dv on?",e),e&&st.signals.loadSignals("page")})),Pt()})))}))}))}))}));function xt(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}}var kt,Ot,Ct,St,It=xt((function(t){var e=t.env,n=t.abeagle,r=t.gdpr;return Pt(E.ZP.getPageTargeting({env:e,abeagle:n,gdpr:r},St.getPageTargeting()),(function(t){return t=wt({},t),Object.keys(t).forEach((function(e){var n=t[e];t[e]=[].concat(n).map((function(t){return"string"===typeof t&&"urlslug"!==e&&0!==e.indexOf("w_")?t.substring(0,40):t}))})),t}))})),jt=xt((function(t){return(0,D.A)("info","adCall","permutiveOn:",t),new Promise((function(e){var n=/\bpermutive_timeout=true\b/.test(window.location.search);if((0,D.A)("info","adCall","permutiveTimeout",n),!t||!n)return(0,D.A)("info","adCall","permutiveTimeout or permutiveOn is false"),void e();var r=0,i=setInterval((function(){if(r>=10||window&&window.permutive)return clearInterval(i),void e();r++}),50);(0,D.A)("info","adCall","permutive defined ,interval:",i)}))})),Mt=xt((function(t){var e=String(t.slot.getTargeting("wid"));return Pt(Zt.get("eventBus"),(function(n){Et(n,1)[0].trigger("gpt:slotVisibilityChanged:".concat(e),t)}))})),_t=xt((function(t){var e=String(t.slot.getTargeting("wid"));if(qt(e)&&e)return Pt(Zt.get("eventBus"),(function(n){Et(n,1)[0].trigger("gpt:slotOnload:".concat(e),t)}));window.raven&&window.raven.captureMessage("onSlotLoad error",{tags:{gptEvent:t}})})),Dt=xt((function(t){var e,n=String(t.slot.getTargeting("wid"));if(null===(e=Ut[n])||void 0===e?void 0:e.getAdUnitPath())return Pt(Zt.get("env"),(function(t){var e=Et(t,1)[0],r=Ut[n].getAdUnitPath(),i=Et(r.split("/"),6),o=i[2],a=i[3],s=i[4],c=i[5];(0,mt.A)(ht.fG,{detail:{wid:n,unitPath:r,zone1:o,edition:a,vertical:s,pos:c}}),(0,mt.l)(ht.fG,e)}))})),Bt=xt((function(t){var e=String(t.slot.getTargeting("wid"));delete Gt[e];var n=qt(e);if((0,D.A)("info","adCall","slot is",n),n&&e)return Pt(Zt.get("eventBus"),(function(n){Et(n,1)[0].trigger("gpt:slotRenderEnded:".concat(e),t)}));window.raven&&window.raven.captureMessage("onSlotRenderEnded error",{tags:{gptEvent:t}})})),zt=xt((function(t){var e=String(t.slot.getTargeting("wid"));if(qt(e)&&e)return Pt(Zt.get("eventBus","env"),(function(n){var r=Et(n,2),i=r[0],o=r[1];i.trigger("gpt:impressionViewable:".concat(e),t);var a=Ut[e].getAdUnitPath(),s=Et(a.split("/"),6),c=s[2],u=s[3],l=s[4],f=s[5];(0,mt.A)(ht.Jg,{detail:{wid:e,unitPath:a,zone1:c,edition:u,vertical:l,pos:f}}),(0,mt.l)(ht.Jg,o)}));window.raven&&window.raven.captureMessage("impressionViewable error",{tags:{gptEvent:t}})})),Nt={checkIsOn:m},$t="https://securepubads.g.doubleclick.net/tag/js/gpt.js",Zt={_eventBusGetter:null,_envGetter:null,_localizationGetter:null,_abeagleGetter:null,_gdprGetter:null,_ccpaGetter:null,_trackingGetter:null,get:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return Promise.all(e.map((function(t){return Zt["_".concat(t,"Getter")]})))}},Ut={},Gt={firstAdCallMade:!1},Vt=function(){},Ft="us",Jt="signals";function Ht(t,e){return t.localization.edition?t.localization.edition:"getEdition"in e?e.getEdition():"en-us"}function Wt(t){return(0,yt.c8)(Ft)(t)}function qt(t){return document.getElementById(Wt(t))}function Yt(){var t=document.querySelector('script[src*="'.concat($t,'"]'));(0,D.A)("info","lifecycle","GPT loaded statically?",!!t);var e=[Zt.get("env")];return c.jQ.hasConsented("gpt").then((function(n){return n?(t||e.push((0,r.v)($t)),Promise.all(e).then((function(t){var e=Et(t,1),n=Et(e[0],1)[0];window.googletag=window.googletag||{},window.googletag.cmd=window.googletag.cmd||[];var r=window.googletag;return new Promise((function(t){r.cmd.push((function(){Ot.resolve(r),t(r),(0,mt.A)(ht.QK),(0,mt.l)(ht.N1,n)}))})).then(Lt)}),(function(t){return(0,D.A)("error","lifecycle",t),console.error("GPT failed to initialize"),Promise.reject(new k.ZP("GPT failed to initialize"))}))):((0,D.A)("info","consent","Ad purpose consent not granted to Google",n),Promise.reject(new k.ZP("Did not consent to Google, not loading gpt.js")))}))}function Xt(t){return!!(t&&t.length>=3)&&"Y"===t.charAt(2)}function Qt(t){var e,n=t.googletag,r=t.targeting,i=void 0===r?{}:r,o=t.env,a=void 0===o?{}:o,s=t.abeagle;n.pubads().clearTargeting(),"function"===typeof(null===(e=Vt())||void 0===e?void 0:e.setPrebidPageTargeting)&&Vt().setPrebidPageTargeting("amazon",a,s),Object.keys(i).forEach((function(t){n.pubads().setTargeting(t,i[t])}))}function Kt(t){var e=t.env;return e.isAdPost()||e.cmsTags.includes("commerce-partnership")?{fetchMarginPercent:-1,renderMarginPercent:-1,mobileScaling:-1}:g.Z.isAny(["md","lg"])?Promise.resolve({fetchMarginPercent:1e3,renderMarginPercent:200}):null}function te(t){var e=t.googletag,n=t.settings;e.pubads().enableLazyLoad(n)}kt=null,Ot=new y.BH,Zt._eventBusGetter=new y.BH,Zt._envGetter=new y.BH,Zt._localizationGetter=new y.BH,Zt._abeagleGetter=new y.BH,Zt._gdprGetter=new y.BH,Zt._ccpaGetter=new y.BH,Zt._trackingGetter=new y.BH,Ut={},Gt={},St={getPageTargeting:function(){return Promise.resolve(null)},getSlotTargeting:function(){return Promise.resolve(null)}};var ee=function(){var t=window;return"function"===typeof t.describe&&"function"===typeof t.expect},ne=function(){return window._isAdsServiceTest};Promise.allSettled=Promise.allSettled||function(t){return Promise.all(t.map((function(t){return t.then((function(t){return{status:"fulfilled",value:t}})).catch((function(t){return{status:"rejected",reason:t}}))})))};var re={AdError:k.ZP,thumbnailReady:L.qB,buildAdCall:dt.C,getAdCallCategory:M.jP,inject:function(t){"eventBus"in t&&Zt._eventBusGetter.resolve(t.eventBus),"env"in t&&Zt._envGetter.resolve(t.env),"localization"in t&&Zt._localizationGetter.resolve(t.localization),"abeagle"in t&&Zt._abeagleGetter.resolve(t.abeagle),"gdpr"in t&&Zt._gdprGetter.resolve(t.gdpr),"ccpa"in t&&Zt._ccpaGetter.resolve(t.ccpa),"tracking"in t&&Zt._trackingGetter.resolve(t.tracking)},configure:function(t){var e=function(t){return function(){var e=arguments;return Promise.resolve().then((function(){return t.apply(void 0,Tt(e))})).catch((function(){return{}}))}},n=t.customTargetingPage,r=t.customTargetingSlot;"function"===typeof n&&(St.getPageTargeting=e(n)),"function"===typeof r&&(St.getSlotTargeting=e(r))},init:function(){var t=function(t,e,n){return n.init(t).then((function(t){return t}),(function(t){return console.error(t),null}))};if(ee()&&!ne())throw new Error("Unit tests should not make real ad calls! Make sure mock `ads` methods");var e=ee()||ne();if(kt)return kt;(0,mt.A)(ht.Ly);var n=(0,M.gZ)();return(n+=(n?" + ":"")+E.ZP.getDFPKeyword())&&document.body.insertAdjacentHTML("beforeend",'\n        <div class="ad-test-label fill-yellow-lighter xs-text-5 bold xs-p1 xs-ml1 xs-t0 xs-z4 xs-fixed">\n          '.concat(n,"\n        </div>\n      ")),kt=Zt.get("eventBus","env","localization","abeagle","gdpr","ccpa","tracking").then((function(n){var r=Et(n,7),i=r[0],o=r[1],a=r[2],s=r[3],c=r[4],u=r[5],l=r[6];w.start({eventBus:i});var f,d={env:o,localization:a,abeagle:s,gdpr:c,ccpa:u,tracking:l,googletagReady:Ot},h=t.bind(null,d);f=o.localization.language?o.localization.language:"getRawPageLanguage"in a?a.getRawPageLanguage():"en";var m=Ht(o,a);(0,D.A)("info","adCall","language set",f),(0,ft.m0)(f),Ft=o.userCountry||"us",Jt=(0,yt.Aw)("doubleverify",Ft,m),(0,D.A)("info","thirdparty","DoubleVerify using",Jt),Vt=function(){return(0,yt.RT)(Ft,m)};var p,v=[Yt(),(p={gdpr:c},p.gdpr.fetchAdPurposeConsent().then((function(t){if(!t)throw new k.ZP("Did not consent, not loading ads.")}))),h("ad_lightning",S)];e||v.push(h("doubleverify",st.mapInit(Jt)),Promise.all([st.isOn({abeagle:s}),s.isOn("ads_bfj_new_magnite")]).then((function(t){var e=Et(t,2),n=e[0],r=e[1],i="prebid";return void 0!==r&&r&&(i="bfj_prebid"),n&&"signals"===Jt?st.signals.targetingDataReady.then((function(){return h("prebid",Vt().getPrebidInitializer(i,d))})):h("prebid",Vt().getPrebidInitializer(i,d))})),Vt().BIDDERS.includes("amazon")?h("amazon",Vt().getPrebidInitializer("amazon",d)):Promise.resolve());var g=Promise.all(v);return h("teads",lt),h("liveRamp",vt),g.then((function(){i.trigger("ads:initialized"),(0,mt.A)(ht.tR),(0,mt.l)(ht.C2,o)}))}))},start:function(){return this.init().catch((function(t){return e=t,(null!=(n=k.ZP)&&"undefined"!==typeof Symbol&&n[Symbol.hasInstance]?n[Symbol.hasInstance](e):e instanceof n)?Promise.resolve():Promise.reject(t);var e,n}))},reset:xt((function(){return(0,D.A)("info","lifecycle","reset ad slots",Ut),Pt(Zt.get("env","abeagle","gdpr"),(function(t){var e=Et(t,3),n=e[0],r=e[1],i=e[2];return Promise.all([Ot,It({env:n,abeagle:r,gdpr:i})]).then((function(t){var e=Et(t,2),i=e[0],o=e[1];i.destroySlots(),Qt({googletag:i,targeting:o,env:n,abeagle:r}),te({googletag:i,settings:Kt({env:n})})}))}))})),clearTargeting:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(Ut[t]){var n=Ut[t];e.forEach((function(t){return n.clearTargeting(t)}))}},renderWidget:function(t){var e=this,n=t.wid;return Gt[n]?Gt[n]:(this.createAdSlotContainer(n),Gt[n]=Zt.get("env","gdpr","ccpa","abeagle","localization").then((function(r){var i,o=Et(r,5),a=o[0],s=o[1],c=o[2],u=o[3],l=o[4],f={env:a,gdpr:s,ccpa:c,abeagle:u,googletagReady:Ot},d=(0,dt.C)(t,{env:a,localization:l});if("/DISABLE/"===d)return e.destroySlot(n);var h=(null===(i=Vt())||void 0===i?void 0:i.getAllBidRequesters(f))||[];return Promise.all([e.init(),e.defineSlot(t)]).then((function(n){var r=Et(n,2)[1];return e.requestHeaderBid(h,wt({},t,{path:d,slot:r}))}))})).then((function(){return e.display(t)})))},refreshWidget:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.wid;if(Gt[r])return Gt[r];if(!Ut[r]||!Ut[r].getSlotElementId())return this.renderWidget(t,!1,n);(0,D.A)("info","lifecycle","renderWidget ".concat(r," refresh"),{slotStatus:Gt[r],slot:Ut[r]});var i=Ut[r].getTargetingKeys().filter((function(t){return/^hb_|amzn/.test(t)}));return re.clearTargeting(r,i),Object.keys(n).forEach((function(t){Ut[r].setTargeting(t,n[t])})),Gt[r]=Zt.get("env","gdpr","ccpa","abeagle","localization").then((function(n){var i,o=Et(n,5),a=o[0],s=o[1],c=o[2],u=o[3],l=o[4],f={env:a,gdpr:s,ccpa:c,abeagle:u,googletagReady:Ot},d=(0,dt.C)(t,{env:a,localization:l});if("/DISABLE/"===d)return e.destroySlot(r);var h=(null===(i=Vt())||void 0===i?void 0:i.getAllBidRequesters(f))||[];return Promise.all([e.requestHeaderBid(h,wt({},t,{path:d,slot:Ut[r]})),Ot,st.isOnAndUnity({abeagle:u},Jt)])})).then((function(t){var e=Et(t,3),n=e[1],i=Et(e[2],2),o=i[0],a=i[1];(0,D.A)("info","thirdparty","doubleverify > Unity > #refreshWidget: DV (Unity) Is On / tagReady?",o,a),o?(st.unity.updateDvTagTargeting([Ut[r]]),a.then((function(){n.pubads().refresh([Ut[r]])}))):n.pubads().refresh([Ut[r]])}))},defineSlot:function(t){var e=t.wid,n=t.size,r=void 0===n?"fluid":n,i=t.targeting,o=void 0===i?{}:i,a=Ut[e];return a?Promise.resolve(a):Promise.all([Zt.get("env","localization"),Ot,St.getSlotTargeting(t),new Promise((function(t){"cookieDeprecationLabel"in navigator?navigator.cookieDeprecationLabel.getValue().then((function(e){t(void 0!==e)})):t(!1)})),Zt.get("abeagle").then((function(t){var e=Et(t,1)[0];return st.isOn({abeagle:e})}))]).then((function(n){var i=Et(n,5),a=Et(i[0],2),s=a[0],c=a[1],u=i[1],l=i[2],f=i[3],d=i[4];Object.assign(o,l);var h=(0,dt.C)(t,{env:s,localization:c});if("/DISABLE/"===h)return null;var m=(s||{}).userCountry,p=void 0===m?"us":m,v=Ht(s,c),g=(0,yt.tb)(p,v),y=(0,yt.RB)(g,t);"bzfd"!==g&&(r=y.size);var b=Wt(e),A=u.defineSlot(h,r,b);if(d&&"unity"===Jt){var w=r.map((function(t){var e=Et(t,2);return{width:e[0],height:e[1]}}));(0,D.A)("info","thirdparty","doubleverify > Unity > #defineSlot: DV (Unity) Is On / sizeRemap",w),st.unity.defineDvtagSlot(b,{id:h,sizes:Jt}),st.unity.onDvtagReady()}Object.keys(o).forEach((function(t){A.setTargeting(t,[].concat(o[t]))}));var E=re.getAdCallCategory(t,{env:s});return E&&A.setTargeting("zone3",E),A.setTargeting("addressable",(0,gt.B)()?"true":"false"),A.setTargeting("cookieDeprecation",f?"true":"false"),A.addService(u.pubads()),Ut[e]=A,A}))},display:xt((function(t){var e=t.wid,n=t.isGAMNativeVideo,r=void 0!==n&&n,i=[Ut[e]];return Promise.all([Zt.get("abeagle").then((function(t){var e=Et(t,1)[0];return st.isOn({abeagle:e})})),Ot]).then((function(t){var n=Et(t,2),o=n[0],a=n[1],s=o&&"signals"===Jt,c=o&&"unity"===Jt;if((0,D.A)("info","thirdparty","doubleverify > #display : what DV is on?",{DV_Signals:s,DV_Unity:c,DV_ON:o,isGAMNativeVideo:r}),!s||r||c)!s&&c?(st.unity.updateDvTagTargeting(i),st.unity.onDvtagReady((function(){a.pubads().refresh(i,{changeCorrelator:!1}),a.display(Wt(e))}))):(a.pubads().refresh(i,{changeCorrelator:!1}),a.display(Wt(e)));else{!function(t,e){var n=st.signals.runOnce(t,e);st.signals.loadSignals(e,n),setTimeout(n,750)}((function(t){return a.pubads().refresh(t,{changeCorrelator:!1})}),i),a.display(Wt(e))}}))})),destroySlot:function(t){var e=t.wid,n=Ut[e];return n?(delete Ut[e],delete Gt[e],Ot.then((function(t){t.destroySlots([n])}))):Promise.resolve()},createAdSlotContainer:function(t){var e="div-gpt-ad-".concat(t),n=Wt(t);document.querySelectorAll(".js-ad-".concat(t)).forEach((function(r){var i=null===r||void 0===r?void 0:r.querySelector("#".concat(e));if(r&&!i){var o='\n        <div id="'.concat(n,'" class="js-ad-slot js-ad-slot-').concat(t,' ad-slot ad-slot-invisible"></div>\n      '),a=r.querySelector("script:first-child");a?a.insertAdjacentHTML("afterend",o):r.insertAdjacentHTML("afterbegin",o)}else i&&n!==e&&(i.id=n)}))},isEmpty:function(t){var e=t.size,n=t.isEmpty,r=t.campaignId,i=t.creativeTemplateId;return n||null===e||void 0===e||T.indexOf(String(r))>-1||R.indexOf(String(i))>-1},isBackfillSlot:function(t){if(re.isEmpty(t))return!1;var e=t.advertiserId;return!(e&&!t.isBackfill)||P.indexOf(String(e))>-1},isProgrammaticSlot:xt((function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(re.isEmpty(t))return!1;var n=t.size,r=Et(n,2),i=r[0],o=r[1];return Pt(Zt.get("env"),(function(t){var r=Et(t,1)[0];return(!x.Z.isEqual(n,r.adSizes.RESEARCH_PIXEL)||!x.Z.contains(e,n))&&(1===i&&1===o||x.Z.isProgrammatic(r.programmaticSizes,n,{strict:!1}))}))})),isIframeContent:xt((function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(re.isEmpty(t))return!1;var n=t.size,r=Et(n,2),i=r[0],o=r[1];return 0===i&&0===o||re.isProgrammaticSlot(t,e)})),getRenderedAdSize:function(t,e){if(re.isEmpty(t))return(0,D.A)("warn","adCall","ad is empty"),{};(0,D.A)("info","adCall","gptEv:",t),(0,D.A)("info","adCall","options",e);var n=Et(t.size||[1,1],2),r=n[0],i=n[1];if(1===r&&r===i){var o=e.wid,a=document.getElementById(Wt(o)).querySelector("iframe");r=a.offsetWidth,i=a.offsetHeight}return{width:r,height:i}},detectAdBlock:function(){return Ct?((0,D.A)("info","adCall","ad block enabled. Please disable to see the ads"),Ct):Ct=new Promise(xt((function(t){return(0,r.v)("//www.buzzfeed.com/static/js/ad-detection/ads.js").then((function(){return t(!1)})).catch((function(){return t(!0)})),Pt()})))},notifyBFA:xt((function(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return Pt(Zt.get("tracking"),(function(r){var i=Et(r,1)[0],o=i&&i.track?i.track:window.bfa;(0,I.i)({trackFn:o,eventName:t,eventData:e,hasEventUrl:n})}))})),requestHeaderBid:function(t,e){var n=t.filter((function(t){return t})).map((function(t){return t.requestBid?t.requestBid(re,e):Promise.resolve()}));return Promise.allSettled(n).then((function(t){return t.filter((function(t){var e=t.status,n=t.value;return"fulfilled"===e&&Boolean(n)})).map((function(t){return t.value})).map((function(t){return t.setTargeting()}))})).then((function(t){return Promise.all(t)}))},getSlotContainer:function(t){return qt(t)},getSlotContainerId:function(t){return Wt(t)},isGPTPrefetch:xt((function(){return Pt(Zt.get("env","abeagle"),(function(t){var e=Et(t,2),n=e[0],r=e[1];return Pt(Promise.all([r.isOn("ADS-1791-new-bpage-gpt-lazyload"),Kt({env:n})]),(function(t){var e=Et(t,2),n=e[0],r=e[1];return(0,D.A)("info","adCall","isGPTLazyLoad",n),(0,D.A)("info","adCall","lazyLoadSettings",r),n&&null!==r}))}))}))};xt((function(){if(!ee())return Pt(Zt.get("abeagle"),(function(t){var e=Et(t,1)[0];re.detectAdBlock().then((function(t){t&&(j.Z.init({abeagle:e}),document.body.classList.add("has-lego"))})),j.Z.detectAcceptableAds()}))}))()},80150:function(t,e,n){"use strict";n.d(e,{ZP:function(){return c},Sx:function(){return a}});var r=n(48705),i=n(58451),o=n(88262),a={CREATED:"CREATED",INITIALIZING:"INITIALIZING",INITIALIZED:"INITIALIZED",DESTROYED:"DESTROYED"};Object.freeze(a);var s=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=t;return function(){return e++}}();function c(t){var e=this;this.context=t,this.eventBus=t.eventBus,this.element=t.element,this.config=t.config,this.id=s(),this.privateEvents=new r.Z,this.plugins=new Set,this.lifecycleState=a.CREATED,this.stack="bzfd";var n=this.destroy;this.destroy=function(){e.lifecycleState!==a.DESTROYED&&n.call(e)}}c.prototype.init=function(){var t=this;return this.initializing?this.initializing:(this.lifecycleState=a.INITIALIZING,this.syncConfig(),this.initializing=Promise.resolve().then((function(){return t.isEnabled()})).then((function(t){if(!t||Array.isArray(t)&&t.some((function(t){return!t})))throw new i.x9})).then((function(){if(t.lifecycleState===a.DESTROYED)throw new i.$j})).then((function(){return t.setup()})).then((function(){return t.lifecycleState=a.INITIALIZED})).catch((function(e){return t.destroy(),Promise.reject(e)})))},c.prototype.syncConfig=function(){var t=this.context.env||{},e=t.isBFN,n=void 0!==e&&e,r=t.userCountry,i=void 0===r?"us":r,a=t.localization,s=(void 0===a?{edition:"en-us"}:a).edition;!n&&(0,o.tV)(i)?this.stack="geo-".concat(i):(0,o.Uy)(s)&&(this.stack="ed-".concat(s)),"bzfd"!==this.stack&&(this.config=(0,o.RB)(this.stack,this.config))},c.prototype.isEnabled=function(){return!0!==this.config.disableUnit},c.prototype.setup=function(){return this.syncConfig(),this.initPlugins()},c.prototype.onPublicEvent=function(){var t;if(!this.eventBus)return function(){};var e=(t=this.eventBus).on.apply(t,arguments);return this.addDestroyAction(e),e},c.prototype.onDOMEvent=function(t,e,n){t.addEventListener(e,n);var r=function(){return t.removeEventListener(e,n)};return this.addDestroyAction(r),r},c.prototype.initPlugins=function(){var t=this,e=[];return this.plugins.forEach((function(n){e.push(new Promise((function(e,r){var i=new n(t.context);t.addDestroyAction((function(){return i.destroy()})),i.init().then(e,r)})).catch((function(t){return t})))})),Promise.all(e)},c.prototype.addDestroyAction=function(t){this.privateEvents.on("destroying",t)},c.prototype.destroy=function(){this.lifecycleState!==a.DESTROYED&&(this.privateEvents.trigger("destroying"),this.privateEvents.destroy(),delete this.privateEvents,delete this.eventBus,this.lifecycleState=a.DESTROYED)},c.withMixins=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.reduce((function(t,e){return e(t)}),this)}},83351:function(t,e,n){"use strict";n.d(e,{Z:function(){return s}});var r=n(24027),i=n(93802);function o(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function a(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){o(t,e,n[e])}))}return t}var s=function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).formatType=t.constructor.formatType,t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"skin",get:function(){return this.config.skin||this.config.zone1||null}},{key:"setup",value:function(){var t=this;return(0,r.Vx)(e,"setup",this,3)([]).then((function(){return t.buildFormat()}))}},{key:"buildFormat",value:function(){var t=this.formatContainer||this.element,e=(0,i.Z)({template:this.template,props:a({},this.getTemplateData(),{context:this.context}),target:t});e&&this.addDestroyAction((function(){return e.$destroy()})),this.element.classList.add("ad-flexible--".concat(this.formatType)),"creativeId"in this.context.ad&&this.element.classList.add("ad-flexible--".concat(this.context.ad.creativeId)),this.context.env.isE2ETest&&(this.element.dataset.dfpClickTracker=this.context.ad.dfpClickTracker)}},{key:"addClassesToParent",value:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];this.eventBus.trigger("ad-unit-tpl:".concat(this.config.wid),{moreClasses:e})}},{key:"getTemplateData",value:function(){return this.context.ad}}],[{key:"getFormatDefinition",value:function(){return[this.formatType,this]}}])}(n(80150).ZP)},99404:function(t,e,n){"use strict";n.d(e,{ZP:function(){return u}});var r=n(24027),i=n(76635),o=n(58451),a=n(80150);var s=(0,r.qH)((function t(){(0,r.PA)(this,t)}));function c(t){return e=t,null!=(n=s)&&"undefined"!==typeof Symbol&&n[Symbol.hasInstance]?n[Symbol.hasInstance](e):e instanceof n;var e,n}var u=function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments))._state={},t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"init",value:function(){return this.resolveConfig(),(0,r.Vx)(e,"init",this,3)([])}},{key:"setup",value:function(){return Promise.all([(0,r.Vx)(e,"setup",this,3)([]),this.configure()])}},{key:"destroy",value:function(){(0,r.Vx)(e,"destroy",this,3)([]),delete this._state}},{key:"isEnabled",value:function(){var t=function(t){return t};return this.units.filter(t).length>0||this.unitsRepeated.filter(t).length>0}},{key:"configure",value:function(){}},{key:"resolveConfig",value:function(){var t,e=this,n=this.context.solid;if(n){var r=Object.keys(n.cssBreakpoints),o=n.getBreakPoint();t=function(t){var n=e["".concat(t,"_").concat(o)]||e.config["".concat(t,"_").concat(o)];return void 0!==n?n:(n=e[t]||e.config[t],(0,i.isObject)(n)&&(r.some((function(t){return t in n}))||"default"in n)?n=o in n?n[o]:n.default:n)}}else t=function(t){return e[t]||e.config[t]};this.units=t("units")||[],this.unitsRepeated=t("unitsRepeated")||[],this.pattern=t("pattern")||[],this.placements=t("placements")||[],this.density=Number(t("density"))||0}},{key:"reset",value:function(){delete this._state.currAd,delete this._state.currPlacement}},{key:"getNextAd",value:function(){var t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{pause:!1}).pause;"currAd"in this._state||(this._state.currAd={idx:-1,usePrev:!1});var e,n=this._state.currAd;return c(n.ad)?n.ad:((n.usePrev||t)&&n.idx>-1?e=n.ad:(n.idx++,e=this.getAd(n.idx)),n.ad=e,n.usePrev=t,e||null)}},{key:"getAd",value:function(t){var e,n=!1;return t<this.units.length?e=this.units[t]:this.unitsRepeated.length>0?(e=this.unitsRepeated[(t-this.units.length)%this.unitsRepeated.length],n=!0):e=new s,!e||c(e)||"slot"in e||(e={slot:e}),n&&(e.slot.isInfinite=!0),e}},{key:"getNextPlacement",value:function(){var t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{pause:!1}).pause;if(this.lifecycleState===a.Sx.DESTROYED)return new s;"currPlacement"in this._state||(this._state.currPlacement={idx:-1,placement:-1,usePrev:!1});var e,n=this._state.currPlacement;if(c(n.placement))return n.placement;if((n.usePrev||t)&&n.idx>-1)e=n.placement;else{var r=this.pattern,i=r.length,o=this.placements,u=this.density,l=++n.idx;if(i>0)if(-1===n.placement)-1===(e=r.findIndex((function(t){return t})))&&(e=new s);else{var f,d=n.placement%i,h=r.findIndex((function(t,e){return t&&e>d}));h>-1?f=h-d:(h=r.findIndex((function(t,e){return t&&e<=d})),f=i-d+h),e=n.placement+f}else e=l<o.length?o[l]-1:u>0?n.placement+u:new s}return n.placement=e,n.usePrev=t,e}},{key:"getAdForPlacement",value:function(t){var e=this.getNextPlacement();return c(e)?new s:t===e?this.getNextAd():(this.getNextPlacement({pause:!0}),null)}},{key:"prepareAd",value:function(t,e){return(t=(0,i.merge)({},t)).slot.position=e+1,t}},{key:"getAdModuleTemplate",value:function(){throw new o.ZP("The method should be implemented in derived classes")}}])}(a.ZP);u.Done=s,u.isDone=c},2023:function(t,e,n){"use strict";n.d(e,{Z:function(){return rt}});var r=n(24027),i=n(76635);function o(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e&&t.setAttribute("aria-hidden",!0),(t.tabIndex>=0||t.shadowRoot)&&(t.tabIndex=-1),Array.prototype.forEach.call(t.children,o)}function a(t){o(t,!0)}var s=function(){if("undefined"===typeof document||!document.body)return 0;var t=document.createElement("div");t.style.overflowY="scroll",t.style.width="100px",t.style.height="100px",t.style.zIndex="-1",t.style.visibility="hidden",document.body.appendChild(t);var e=t.offsetWidth-t.clientWidth;return document.body.removeChild(t),e}();function c(t){t&&(0===s?t.classList.add("scrollbar--overlay"):t.classList.add("scrollbar--".concat(Math.ceil(s),"px")))}c("undefined"!==typeof document?document.documentElement:null);var u=n(60736);function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function f(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||h(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t){return function(t){if(Array.isArray(t))return l(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||h(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){if(t){if("string"===typeof t)return l(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(t,e):void 0}}var m=function(t){var e=t.shareUrl,n=void 0===e?"":e,r=t.platform,i=t.utmCampaign,o=t.utmSource;if(!r)return n;var a=null!==i&&void 0!==i?i:"bfshare".concat(r),s=null!==o&&void 0!==o?o:"dynamic",c=f(n.split("?"),2),u=c[0],l=c[1],h=void 0===l?"":l;return u+"?"+["utm_source=".concat(s),"utm_campaign=".concat(a)].concat(d(h.split("&"))).filter((function(t){return!!t})).join("&")},p=n(71288),v=n(11608),g=n(34686),y=n(46658),b=function(){var t=g.ZP.getQueryParams().sub;return t?parseInt(t.split("_")[1],10):null}();var A={getUrl:function(){var t;"canonical_path"in window.BZFD.Context.page?t=window.BZFD.Context.page.canonical_path:window.BZFD.Context.buzz&&(t=window.BZFD.Context.buzz.canonical_path),t=t||"";var e=location.origin+t,n=function(t){if(""===t)return{};for(var e={},n=0;n<t.length;++n){var r=t[n].split("=",2);1===r.length?e[r[0]]="":e[r[0]]=decodeURIComponent(r[1].replace(/\+/g," "))}return e}(window.location.search.substr(1).split("&"));if(n.bfsource){var r="?";e.includes("?")&&(r="&"),e="".concat(e).concat(r,"bfsource=").concat(n.bfsource)}return e},getCanonicalUrl:function(){var t=document.querySelector('link[rel="canonical"]');return t?t.href:this.getUrl()},getAuthor:function(){return function(t){var e,n="",r=v.ZP.find(document.head,'meta[property="'.concat(t,'"]'));return r.length>0&&(e=v.ZP.getAttr(r[0],"content"))&&(n=e),n}("author").replace(/,$/g,"")},getTitle:function(t){var e=window.BZFD.Context.page.title||document.title;return t&&window.BZFD.Context.page.promotions[t]&&window.BZFD.Context.page.promotions[t].title||e},getDescription:function(t){var e=window.BZFD.Context.page.description;return t&&window.BZFD.Context.page.promotions[t]&&window.BZFD.Context.page.promotions[t].description||e},getCaption:function(){var t=document.domain,e=this.getAuthor();return e&&(t+=" | By ".concat(e)),t},getPicture:function(t){var e=window.BZFD.Context.page.picture;return t&&window.BZFD.Context.page.promotions[t]&&window.BZFD.Context.page.promotions[t].picture||e},getAll:function(){return{url:this.getUrl(),author:this.getAuthor(),title:this.getTitle(),description:this.getDescription(),caption:this.getCaption(),picture:this.getPicture()}},renderString:function(t,e,n){var r=g.ZP.extend({},this.getAll(),e||{});return y.Z.renderString(t,r,n)},isLinkedSubbuzz:function(t){return null!==b&&b===t},getSubbuzzShareData:function(t,e){var n=g.ZP.removeHash(this.getUrl()),r=g.ZP.addQueryParam(n,"sub","0_"+e)+"#"+e,i=v.ZP.getText(t,".js-subbuzz__title-text").trim();return{text:i||this.getTitle().trim(),url:n+"#"+e,shareMetaUpdatedUrl:r,fbUrl:r,media:this.getSubbuzzMedia(t)||this.getPicture(),subbuzzTitle:i}},getQuizResultShareData:function(t,e){var n=g.ZP.removeHash(this.getUrl()),r=g.ZP.addQueryParam(n,"quiz_result",t+"_"+e)+"#"+t;return{url:n+"#"+t,shareMetaUpdatedUrl:r,fbUrl:r}},getSubbuzzMedia:function(t){var e=v.ZP.findOne(t,".js-subbuzz__media");if(!e)return"";var n=v.ZP.getAttr(e,"data-src"),r=v.ZP.getAttr(e,"data-gif-src");return u.Z.isAny(["xs","sm"])&&(n=v.ZP.getAttr(e,"data-mobile-src")||n,r=v.ZP.getAttr(e,"data-mobile-gif-src")||r),r||n||v.ZP.getAttr(e,"src")}},w={},E=function(t){var e=document.createElement("iframe");e.style.cssText="position:absolute; width:1px; height:1px; opacity:0;",e.setAttribute("src",t),document.body.appendChild(e)};w.iosOpenUrl=E;var T=function(t){return encodeURIComponent(JSON.stringify(t))};w.encodePixiedustData=T;var R=function(t){var e="";return Object.keys(t).forEach((function(n){e&&(e+="&"),e+=n+"="+encodeURIComponent(t[n])})),e};w.encodeData=R;w.share=function(t,e){var n=e;if("pinterest"===t&&(n=function(t){return{media:t.media,url:m({shareUrl:t.shareMetaUpdatedUrl||t.url,platform:"pinterest"}),description:t.text}}(e)),"facebook"===t&&(n=function(t){return{name:t.text,caption:A.getCaption(),description:A.getDescription("facebook"),link:m({shareUrl:t.shareMetaUpdatedUrl||t.url,platform:"facebook"}),picture:t.picture}}(e)),"twitter"===t&&(n=function(t){return{text:t.text,url:m({shareUrl:t.shareMetaUpdatedUrl||t.url,platform:"twitter"})}}(e)),"email"===t&&(n=function(t){return{subject:t.text,body:m({shareUrl:t.shareMetaUpdatedUrl||t.url,platform:"email"})}}(e)),"copy"===t&&(n=function(t){return{url:m({shareUrl:t.shareMetaUpdatedUrl||t.url,platform:"copy"})}}(e)),n=function(t,e){return t.quizId&&t.itemId&&(e.ga_label=t.quizId+"="+t.itemId,e.result_id=t.itemId),e}(e,n),p.ZP.isIOS()){var r="bf://share/"+t+"?"+R(n);E(r)}else p.ZP.isAndroid()?window.bf&&window.bf.share&&window.bf.share(t,JSON.stringify(n)):console.log("share() error: cannot determine device os, bailing.")};w.analytics=function(t,e){var n;if(p.ZP.isIOS())"pixiedust"!==t||p.ZP.isNewsApp()?"google"===t?n="bf://analytics?"+R(e):console.log('analytics() ios "platform" is neither "pixiedust" nor "google", is this OK?'):n="bf://pixiedust?event="+T(e),E(n);else if(p.ZP.isAndroid())if("pixiedust"===t)window.bf&&window.bf.pixiedust&&window.bf.pixiedust(JSON.stringify(e));else if("google"===t){window.bf&&window.bf.analytics&&window.bf.analytics("",JSON.stringify(e))}else console.log('analytics() android "platform" is neither "pixiedust" nor "google", is this OK?');else console.log("analytics() error: cannot determine device os, bailing.")};var P=function(){var t=!1;return window.bf&&window.bf.optOut&&(t=window.bf.optOut()),t};w.optOut=P;var L=n(6294),x=n(70833),k=n(89809),O=function(t,e,n){for(var r=n?t:t.parentNode;r&&r!==document;){if(r.matches(e))return r;r=r.parentNode}return null},C=n(67419),S=n(58451),I=n(17807);function j(t,e,n,r){(0,i.forEach)(t,(function(i,o){"wid"===o&&String(i)===String(e)?t[o]=n:"object"===typeof i&&null!==i&&j(i,e,n,r),"targeting"===o&&Object.assign(i,r)}))}function M(t,e,n){t.id=t.id.replace(e,n)}function _(t,e,n,r){e.forEach((function(e){var i=t.classList.contains(e);t.classList.remove(e),i&&t.classList.add(e.replace(n,r))}))}var D=n(52824);function B(t,e){return null!=e&&"undefined"!==typeof Symbol&&e[Symbol.hasInstance]?e[Symbol.hasInstance](t):t instanceof e}n(186),n(84714);var z=n(93557);function N(t,e){var n=e.env,r=e.localization;return(0,z.z)({uri:"branded-buzz",view:function(t){return t.buzzes[0].buzz},edition:function(){return""}},{origin:n.webRoot,extraParams:{u:"buzz-mobile"},localization:r},t)}function $(t){if("Failed to fetch"!==t.message)throw t}var Z=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.buzzId,r=e.buzz,i=void 0===r?null:r,o=e.buzzIds,a=void 0===o?[]:o,s=e.buzzes,c=void 0===s?[]:s,u=e.creativeId,l=arguments.length>1?arguments[1]:void 0,f=l.env,d=l.localization;if(i)t=Promise.resolve([i]);else if(c.length>0)t=Promise.resolve(c);else if(n||0!==a.length){n&&(a=[n]);var h="en-us";d?h=d.edition:f.localization&&(h=f.localization.country);var m=function(t){return N(t,{env:f,localization:{getEdition:function(){return h}}})};t=Promise.all(a.map(m))}else t=Promise.reject(new C.Z.AdError("Missing buzz id for creative #".concat(u)));return t.catch($),t},U=n(80150);function G(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function V(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return G(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return G(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var F=function(t){function e(){var t;(0,r.PA)(this,e);var n=(t=(0,r.$w)(this,e,arguments)).onPublicEvent("ad-native--loaded:".concat(t.config.wid),t.handleNativeAdLoad.bind(t)),i=new g.BH({unsubscribe:n}),o=new g.BH;return t.contentPending={native:i,other:o},t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"handleNativeAdLoad",value:function(t){var e=this;/^buzz/.test(t.type)?Z(t,{env:this.context.env,localization:this.context.localization}).then((function(n){n.length>1?t.buzzes=n:t.buzz=n[0],e.contentPending.native.resolve(t)}),(function(n){return e.contentPending.native.reject([t,n])})):this.contentPending.native.resolve(t),this.stopLoadedListening()}},{key:"setup",value:function(){var t,n=this,o=this,a=this.config.wid;return this._onSlotRenderEnded=(t=function(t){return e=C.Z.isProgrammaticSlot(t,o.config.size),n=function(e){C.Z.isEmpty(t)?o.contentPending.other.resolve({type:"empty"}):e&&o.contentPending.other.resolve({type:"programmatic"})},r?n?n(e):e:(e&&e.then||(e=Promise.resolve(e)),n?e.then(n):e);var e,n,r},function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}),this.onPublicEvent("gpt:slotRenderEnded:".concat(a),this._onSlotRenderEnded),Promise.race((0,i.values)(this.contentPending)).then((function(t){n.lifecycleState!==U.Sx.DESTROYED&&(n.eventBus.trigger("ad-content-ready:".concat(a),t),n.element.classList.add("ad-content-ready","ad-flexible--".concat(t.type)))}),(function(t){var e=V(t,2),r=e[0],i=e[1];n.eventBus.trigger("ad-content-error:".concat(a),[r,i])})).then((function(){return n.stopLoadedListening()})),(0,r.Vx)(e,"setup",this,3)([])}},{key:"stopLoadedListening",value:function(){(0,i.values)(this.contentPending).forEach((function(t){return t.unsubscribe()})),this.eventBus&&this.eventBus.off("gpt:slotRenderEnded:".concat(this.config.wid),this._onSlotRenderEnded)}},{key:"destroy",value:function(){this.stopLoadedListening(),(0,r.Vx)(e,"destroy",this,3)([])}}])}(U.ZP);var J=n(42235),H=n(39782),W=function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).gptEv=null,t.ad=null,t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"setup",value:function(){this.trackingReady=new g.BH;var t=this.config.wid;return this.onPublicEvent("ad-content-ready:".concat(t),this.trackingReady.resolve),this.onPublicEvent("ad-impression:".concat(t),this.trackImpression.bind(this)),this.onPublicEvent("ad-click:".concat(t),this.trackClick.bind(this)),this.onPublicEvent("advertise-click:".concat(t),this.trackAdvertiseClick.bind(this)),this.onPublicEvent("gpt:slotRenderEnded:".concat(t),this.handleSlotRender.bind(this)),(0,r.Vx)(e,"setup",this,3)([])}},{key:"handleSlotRender",value:function(t){var e=this;t.isEmpty||(this.gptEv=t,this.trackingReady.then((function(n){(0,x.A)("info","plugins","core-tracking","#handleSlotRender",{lifecycleState:e.lifecycleState,ad:n,gptEv:t}),e.lifecycleState!==U.Sx.DESTROYED&&(e.ad=n,e.normalize3rdPartyTrackers(),e.setUpImpressionTracking(),e.setUpClickTracking())})))}},{key:"setUpClickTracking",value:function(){var t=this;this._trackClicksBound=function(e){var n=O(e.target,"a",!0);n&&!("adsNoTrack"in n.dataset)&&t.eventBus.trigger("ad-click:".concat(t.config.wid),{link:n})},this.element.addEventListener("click",this._trackClicksBound),this.setUpProgrammaticTracking()}},{key:"setUpProgrammaticTracking",value:function(){var t=this;if("programmatic"===this.ad.type){var e=this.element.querySelector("iframe");this._detectProgrammaticClick=function(){window.setTimeout((function(){document.activeElement===e&&t.eventBus.trigger("ad-click:".concat(t.config.wid))}),0)},window.addEventListener("blur",this._detectProgrammaticClick)}}},{key:"setUpImpressionTracking",value:function(){this.eventBus.trigger("ad-impression:".concat(this.config.wid))}},{key:"trackClick",value:function(){var t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).link;t&&t.dataset.bfa||this.notifyBFA("click"),this.notify3rdparty("click")}},{key:"trackAdvertiseClick",value:function(t){var e={t:"click:advertise",n:"Advertise",l:this.config.adPos,d:"edition",data:{url:t.url},opt:{dimension2:this.context.env.localizationCountry,dimension7:this.config.adPos}};(0,H.i)({trackFn:(this.context.tracking||{}).track,eventName:"click",eventData:e})}},{key:"trackImpression",value:function(){this.notify3rdparty("impression"),this.notifyBFA("scroll")}},{key:"notifyBFA",value:function(t){if(this.ad&&t&&this.config&&this.config.adPos){var e=this.config.adPos,n="programmatic"===this.ad.type,r=this.gptEv.creativeId;!r&&n&&(r="adxbackfill");var i={t:t,n:"".concat(e,"-dfp"),l:e,d:n?"programmatic":"creative",data:{obj_id:0,type:this.ad.type,creativeId:r},opt:{dimension13:r}};(0,H.i)({trackFn:(this.context.tracking||{}).track,eventName:t,eventData:i})}}},{key:"notify3rdparty",value:function(t){var e=this;this.ad&&(this.ad["".concat(t,"Trackers")]||[]).forEach((function(n){return e.create3rdPartyPixel(n,t)}))}},{key:"normalize3rdPartyTrackers",value:function(){var t=this.ad;if(t&&"programmatic"!==t.type){var e=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter((function(t){return!!t}))};t.clickTrackers||(t.clickTrackers=[(window.BF_DFP_CLICKS||{})[this.config.wid]]),t.clickTrackers=e(t.clickTrackers),t.impressionTrackers=e(t.impressionTrackers),t.customTrackers="string"===typeof t.customTrackers?e(t.customTrackers.split(/\s+/)):[],this.init3rdPartyScripts()}}},{key:"create3rdPartyPixel",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=new Image;n.src=t,this.context.env.isE2ETest&&(n.classList.add("ad-slot-invisible"),n.classList.add("ad-e2e--pixel-".concat(e)),this.element.appendChild(n))}},{key:"init3rdPartyScripts",value:function(){var t=this;this.ad.customTrackers.forEach((function(e){return(0,J.v)(e,t.element)}))}},{key:"destroy",value:function(){this.element.removeEventListener("click",this._trackClicksBound),delete this._trackClicksBound,this._detectProgrammaticClick&&(window.removeEventListener("blur",this._detectProgrammaticClick),delete this._detectProgrammaticClick),(0,r.Vx)(e,"destroy",this,3)([])}}])}(U.ZP),q=n(99945),Y=n(22299),X=function(t){function e(){var t;(0,r.PA)(this,e);var n=(t=(0,r.$w)(this,e,arguments)).wid=t.config.wid;if(t.onPublicEvent("ad-wireframe-no-collapse:".concat(n),t._onNoCollapse.bind(t)),t.onPublicEvent("ad-content-rendered:".concat(n),t._loadAd.bind(t)),t.onPublicEvent("ad-content-rendered:".concat(n,"-1"),t._loadAd.bind(t)),t.wireframeElement=(0,v.oq)(t.element,".ad-wireframe-wrapper")||null,(!t.config.platform||"autodetect"===t.config.platform)&&t.wireframeElement){var i=Math.max(document.documentElement.clientWidth,window.innerWidth||0),o=t.config.size.filter((function(t){return Array.isArray(t)&&t[0]<=i}));t._setWireframeSizes({width:Math.max.apply(null,o.map((function(t){return t[0]}))),height:Math.max.apply(null,o.map((function(t){return t[1]})))})}return t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"_setWireframeSizes",value:function(t){var e=t.width,n=t.height;(1!==e||1!==n)&&Number.isInteger(e)&&Number.isInteger(n)&&(this.wireframeElement.dataset.wireframeWidth=e,this.wireframeElement.dataset.wireframeHeight=n)}},{key:"_onNoCollapse",value:function(){var t=this;this._thumbnailReady=(0,q.qB)(this.element),this._thumbnailReady.then((function(){return t._loadAd()})),this.addDestroyAction((function(){return t._thumbnailReady.unsubscribe()}))}},{key:"_loadAd",value:function(){var t=this;this._thumbnailReady&&this._thumbnailReady.unsubscribe(),this._loadAdProgress=(0,v.SU)(this.element),Promise.race([this._loadAdProgress,new Promise((function(t){return setTimeout(t,750)}))]).then((function(){t.lifecycleState!==U.Sx.DESTROYED&&t.eventBus.trigger("ad-wireframe-fadein-finish:".concat(t.wid))})),this.addDestroyAction((function(){return t._loadAdProgress.unsubscribe()})),this._adFadeIn(),this._removeWireframe(),this.element.classList.add("ad--loaded")}},{key:"_adFadeIn",value:function(){this.element.querySelectorAll(".ad-wireframe-text").forEach((function(t){t.style.display="none"}));var t=this.element.querySelectorAll(".ad-fade");t.length<=0&&this.element.classList.contains("ad-fade")&&(t=[this.element]),t.forEach((function(t){if(null===t.offsetParent)t.classList.remove("ad-fade");else{var e=(0,v.SU)(t,{properties:["opacity"]}),n=function(){t.classList.remove("ad-fade"),e.unsubscribe()};e.then(n),t.style.opacity=1,setTimeout(n,500)}}))}},{key:"_detectCollapseEnd",value:function(){var t=this;(0,v.SU)(this.wireframeElement||this.element,{properties:["opacity"]}).then((function(){t._removeWireframeAndHide()}))}},{key:"_removeWireframeAndHide",value:function(){this._removeWireframe(!0)}},{key:"_removeWireframe",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this._removeWireframeClasses(this.element,t),t&&(this.element.classList.add("js-hidden"),this.eventBus.trigger("ad-wireframe-collapse-finish:".concat(this.wid)))}},{key:"_removeWireframeClasses",value:function(t,e){if(t&&"string"===typeof t.className&&(t.className=t.className.replace(/\bad-wireframe[-\S]*/g,""),t.className=t.className.replace(/\bad-fade[-\S]+/g,""),e&&(t.className=t.className.replace(/\bad-fade/g,""))),null===t||void 0===t?void 0:t.children)for(var n=0;n<t.children.length;n++){var r=t.children[n];this._removeWireframeClasses(r,e)}}},{key:"_broadcastOnRender",value:function(t){var e=this;Y.Z.detectAcceptableAds().then((function(n){if(!n){var r=e.wid;t.isEmpty||e.eventBus.trigger("ad-wireframe-no-collapse:".concat(r))}}))}},{key:"setup",value:function(){return this.onPublicEvent("gpt:slotRenderEnded:".concat(this.wid),this._broadcastOnRender.bind(this)),(0,r.Vx)(e,"setup",this,3)([])}},{key:"destroy",value:function(){(0,r.Vx)(e,"destroy",this,3)([]),delete this._thumbnailReady,delete this._loadAdProgress,this.element=null,this.config=null,this.wid=null}}])}(U.ZP);function Q(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function K(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function tt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){K(t,e,n[e])}))}return t}function et(t){return function(t){if(Array.isArray(t))return Q(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return Q(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Q(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var nt=P,rt=function(t){function e(t){var n;(0,r.PA)(this,e);var o=(t=tt({},t,{config:(0,i.cloneDeep)(t.config)})).element;o&&o.classList.contains("ad-wireframe-wrapper")&&(o=o.firstElementChild);var a=(n=(0,r.$w)(this,e,[tt({},t,{element:o})])).config;n.syncConfig(),"advertiserContext"in a||(a.advertiserContext=n.context.env.isAdPost(n.element));var s=a.targeting||{};return"wid"in s||(s.wid=a.wid),"pos"in s||(s.pos=[a.adPos]),a.targeting=s,n.plugins=new Set([F,W]),"defaultWireframes"in t&&!t.defaultWireframes||n.plugins.add(X),n.isRendered=!1,n.instanceId=n.id,a.isInfinite&&n.makeInfiniteWid(),n.setupSizes(),n.addA11yMarkup(),n.addCssClasses(),n.renderQueue=[],n.reset(),n}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"skin",get:function(){return this.config.skin||this.config.zone1||null}},{key:"getEventId",value:function(){return"".concat(this.config.adPos,"-").concat(this.id)}},{key:"setup",value:function(){var t=this;this.syncConfig();var n=this.config.wid;this.element.dataset.instanceId=this.instanceId,this.onPublicEvent("ad-wireframe-collapse-finish:".concat(n),this.destroy.bind(this)),this.onPublicEvent("ad-content-error:".concat(n),this.handleContentError.bind(this)),this.onPublicEvent("gpt:slotRenderEnded:".concat(n),this.handleSlotRenderEnded.bind(this)),this.onPublicEvent("ad-unit-tpl:".concat(n),(function(e){var n=e.moreClasses;return t.addCssClasses(n)}));var i=this.getEventId();return this.onPublicEvent("gpt:slotRenderEnded:".concat(n),(function(e){t.eventBus.trigger("gpt:slotRenderEnded:".concat(i),e)})),this.onPublicEvent("ad-content-ready:".concat(n),(function(e){var n=e.type;t.eventBus.trigger("ad-content-ready:".concat(i),{type:n})})),this.render(),(0,r.Vx)(e,"setup",this,3)([])}},{key:"isFirstInit",value:function(){return!this.element.hasAttribute("data-instance-id")}},{key:"isEnabled",value:function(){return this.syncConfig(),this.config.disableUnit?(this.addDisabledCssClasses(),(0,x.A)("info","lifecycle","core/unit -> isEnabled","unit disabled from config"),Promise.resolve(!1)):Promise.all([this.isFirstInit(),!nt(),this.isPlatformCorrect()])}},{key:"setupSizes",value:function(){var t=u.Z.isXsmall(),e=u.Z.isSmall(),n=(0,L.YL)(),r=this.context.env.adSizes;e?this.filterProgrammaticSizes({max:r.PROGRAMMATIC_LEADERBOARD}):t&&(this.filterProgrammaticSizes({max:r.PROGRAMMATIC_SMARTPHONE_BANNER}),this.excludeSize(r.PROGRAMMATIC_VERTICAL),n&&this.excludeSize(r.FLUID))}},{key:"deferRender",value:function(){var t;this.renderQueue=this.renderQueue||[],(t=this.renderQueue).push.apply(t,arguments)}},{key:"render",value:function(){var t=this;return this.isRendered?Promise.resolve():(this.isRendered=!0,this.init().then((function(){return Promise.all(t.renderQueue).then((function(){return t.requestAd()}))}),(function(){})))}},{key:"requestAd",value:function(){var t=this;return C.Z.renderWidget(this.config).catch((function(e){return t.eventBus&&t.eventBus.trigger("ad-request-failure:".concat(t.config.wid)),n=e,(null!=(r=S.ZP)&&"undefined"!==typeof Symbol&&r[Symbol.hasInstance]?r[Symbol.hasInstance](n):n instanceof r)?Promise.resolve(e):Promise.reject(e);var n,r}))}},{key:"addA11yMarkup",value:function(){this.element.setAttribute("role","complementary"),this.element.setAttribute("aria-label","Advertisement")}},{key:"addCssClasses",value:function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;null!==e&&(t=this.element.classList).add.apply(t,et(e));var n=this.config.wid;this.element.classList.add("js-ad","js-ad-".concat(n));var r=this.skin;r&&this.element.classList.add("ad--".concat(r)),this.config.advertiserContext&&this.element.classList.add("ad--partner");var i=C.Z.getSlotContainer(n);i&&i.classList.add("ad-slot","js-ad-slot","js-ad-slot-".concat(n))}},{key:"addDisabledCssClasses",value:function(){this.element&&(this.element.classList.add("ad--disabled"),O(this.element,".Ad")&&O(this.element,".Ad").classList.add("Ad--disabled"))}},{key:"isPlatformCorrect",value:function(){var t,e=this.config.platform||null,n=this.config.supports||null;return e&&("desktop"===e?t=["md","lg"]:"mobileweb"===e&&(t=["xs","sm"])),n&&(t=n),!Array.isArray(t)||u.Z.isAny(t)}},{key:"handleSlotRenderEnded",value:function(t){try{var e=C.Z.getSlotContainer(this.config.wid);return n=C.Z.isIframeContent(t,this.config.size),r=function(t){t?e.classList.remove("ad-slot-invisible"):(e.classList.add("ad-slot-invisible"),a(e))},i?r?r(n):n:(n&&n.then||(n=Promise.resolve(n)),r?n.then(r):n)}catch(o){return Promise.reject(o)}var n,r,i}},{key:"handleContentError",value:function(){this.destroy()}},{key:"addSize",value:function(){for(var t,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=u.Z.isXsmall(),o=(0,L.YL)();i&&o&&(n=n.filter((function(t){return"fluid"!==t}))),this.config.size=(t=k.Z).add.apply(t,[this.config.size].concat(et(n)))}},{key:"excludeSize",value:function(){for(var t,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];this.config.size=(t=k.Z).exclude.apply(t,[this.config.size].concat(et(n)))}},{key:"filterProgrammaticSizes",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.config.size=k.Z.filterProgrammatic(this.context.env.programmaticSizes,this.config.size,t)}},{key:"reset",value:function(){C.Z.destroySlot(this.config);try{delete this.element.dataset.instanceId}catch(S){console.debug("Unable to delete instanceId from element in destroySlot"),console.debug("Instance ID that was not removed:",this.element.dataset.instanceId)}this.element.classList.remove("js-hidden")}},{key:"destroy",value:function(){C.Z.destroySlot(this.config),(0,r.Vx)(e,"destroy",this,3)([])}}])}(U.ZP.withMixins(D.$,(function(t){return function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).formats=t.formats||new Map,t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"setup",value:function(){return this.element.classList.add("ad-flexible"),this.stopContentListening=this.onPublicEvent("ad-content-ready:".concat(this.config.wid),this.handleAdContentLoaded.bind(this)),this.onPublicEvent("ad-request-failure:".concat(this.config.wid),this.handleAdRequestFailure.bind(this)),(0,r.Vx)(e,"setup",this,3)([])}},{key:"handleSlotRenderEnded",value:function(t){(0,r.Vx)(e,"handleSlotRenderEnded",this,3)(arguments),C.Z.isEmpty(t)&&this.handleAdContentLoaded({type:"empty"})}},{key:"handleAdContentLoaded",value:function(t){var e=this;this.context.ad=t,this.stopContentListening();var n=new Set;this.formats.forEach((function(e,r){("string"===typeof r&&r===t.type||B(r,RegExp)&&r.test(t.type))&&e.forEach((function(t){return n.add(t)}))})),n.forEach((function(t){try{var n=new t(e.context);e.addDestroyAction((function(){return n.destroy()})),n.init()}catch(S){console.error(S)}}))}},{key:"handleAdRequestFailure",value:function(t){B(t,S.ZP)&&this.formats.has("empty")&&(this.context.isAdBlocked=!0,this.handleAdContentLoaded({type:"empty"}))}},{key:"addFormat",value:function(t,e){this.formats.has(t)||this.formats.set(t,new Set),this.formats.get(t).add(e)}}])}(t)}),(function(t){return function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"makeInfiniteWid",value:function(){if(this.config.isInfinite&&this.isFirstInit()){var t=this.config.wid,e="infiniteIdx"in this.element.dataset?Number(this.element.dataset.infiniteIdx):I.Z.getUnique(t);if(0!==e){var n="".concat(t,"-").concat(e),r=(0,i.partialRight)(j,t,n,{infinite_index:e}),o=(0,i.partialRight)(M,t,n),a=(0,i.partialRight)(_,["js-ad-".concat(t),"js-ad-format-".concat(t),"js-ad-slot-".concat(t),"ad-wireframe-".concat(t),"ad-fade-".concat(t)],t,n);r(this.config),this.element.querySelectorAll('script[type="text/x-config"]').forEach((function(t){var e=JSON.parse(t.textContent);r(e),t.textContent=JSON.stringify(e)}));var s=/^BF_WIDGET_/.test(this.element.id)?this.element:this.element.querySelector("#BF_WIDGET_".concat(t));s&&o(s);var c=this.element.querySelector("#".concat(C.Z.getSlotContainerId(t)));c&&o(c);var u=this.element.querySelector("#bf-item-".concat(t,"-1"));u&&o(u),a(this.element),this.element.querySelectorAll("*").forEach((function(t){return a(t)})),this.element.dataset.infiniteIdx=e}}}}])}(t)})))},10533:function(t,e,n){"use strict";n.d(e,{T:function(){return r}});var r={IMAGE:"image",EMBED:"embed",GAM_VIDEO:"video (GAM)"}},48705:function(t,e,n){"use strict";function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function i(t){return function(t){if(Array.isArray(t))return r(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return r(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return r(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(){this._listening={}}n.d(e,{Z:function(){return o}}),o.prototype.on=function(t,e){var n=this;return t in this._listening||(this._listening[t]=new Set),this._listening[t].add(e),function(){return n.off(t,e)}},o.prototype.once=function(t,e){var n=this;return this.on(t,(function r(){n.off(t,r),e.apply(void 0,arguments)}))},o.prototype.off=function(t,e){t in this._listening&&(this._listening[t].delete(e),0===this._listening[t].size&&delete this._listening[t])},o.prototype.trigger=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];t in this._listening&&this._listening[t].forEach((function(t){try{t.apply(void 0,i(n))}catch(e){console.error(e)}}));var o=t.replace(/:[^:]*$/,"");o!==t&&this.trigger.apply(this,[o].concat(i(n)))},o.prototype.fire=o.prototype.trigger,o.prototype.destroy=function(){delete this._listening},o.mixInto=function(t){Object.assign(t,o.prototype),o.call(t)}},63396:function(t,e,n){"use strict";n.d(e,{W:function(){return o}});var r=n(24027),i=n(76635),o=function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"getBuzzData",value:function(){var t=this,e=this.context.ad.buzz,n=e.header.users.filter((function(t){return t.username===e.username})).map((function(e){return{displayName:e.display_name,url:"".concat(t.context.env.webRoot,"/").concat(e.username),avatar:e.user_image}}))[0],r=this.context.ad.clickThroughUrl?this.context.ad.clickThroughUrl:e.url,o=this.context.ad.dfpClickTracker||(window.BF_DFP_REDIRECTS||{})[this.config.wid];o&&(r="".concat(o).concat(encodeURIComponent(r)));var a={id:e.id,title:e.name,titleOriginal:e.name,description:e.ad_blurb,url:r,category:e.category,images:e.images,promotionType:e.user_type.replace(/^f_/,""),advertiser:n},s=this.pickVariation(e.active_experiment);return(0,i.merge)(a,s),a}},{key:"pickVariation",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=(t||{}).variations||[];if(0===e.length)return{};var n=(0,i.sample)(e),r=n.id,o=n.title,a=n.thumbnail,s={};return s.flexproVariation=r,o&&(s.title=o),a&&(s.images={big:a.url,dblbig:a.url,wide:a.wide_url,dblwide:a.wide_url}),s}},{key:"getTemplateData",value:function(){var t=(0,i.merge)({},this.context.ad);return t.buzz=this.getBuzzData(),t.slot=this.config,t}}])}(n(83351).Z);o.formatType="buzz"},32249:function(t,e,n){"use strict";n.d(e,{t:function(){return s},B:function(){return a}});var r=n(24027),i=n(83351),o=n(52824),a=function(t){return function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"setup",value:function(){(0,r.Vx)(e,"setup",this,3)([]);var t=this.config.wid;this.config.isGAMNativeVideo=!0,this.onPublicEvent("ad-native--video-quartile:".concat(t),this.reportQuartile.bind(this)),this.onPublicEvent("ad-native--video-view:".concat(t),this.reportPlayback.bind(this)),this.onPublicEvent("ad-native--video-replay:".concat(t),this.reportReplay.bind(this)),this.onPublicEvent("ad-native--video-pause:".concat(t),this.reportPause.bind(this)),this.onPublicEvent("ad-native--video-volumechange:".concat(t),this.reportVolumeChange.bind(this)),this.onPublicEvent("ad-native--video-loadedmeta:".concat(t),this.reportLoadedMetaData.bind(this))}},{key:"reportLoadedMetaData",value:function(t){var e=t.duration,n=this.config.wid;this.eventBus.trigger("ad-native-video-ready:".concat(n),{duration:e}),this.eventBus.trigger("ad-content-rendered:".concat(n)),this.eventBus.trigger("dfp-native-video:loaded-metadata:".concat(n),t)}},{key:"reportQuartile",value:function(t){var e=this.config.wid;this.eventBus.trigger("ad-native-video-quartile:".concat(e)),4===t.quartile&&this.eventBus.trigger("ad-native-video-ended:".concat(e)),this.eventBus.trigger("native-video-embed:playback-quartile:".concat(e),t)}},{key:"reportReplay",value:function(t){this.eventBus.trigger("dfp-native-video:replay:".concat(this.config.wid),t)}},{key:"reportPlayback",value:function(t){this.eventBus.trigger("dfp-native-video:play:".concat(this.config.wid),t)}},{key:"reportPause",value:function(t){this.eventBus.trigger("ad-native-video-pause:".concat(this.config.wid)),this.eventBus.trigger("dfp-native-video:pause:".concat(this.config.wid),t)}},{key:"reportVolumeChange",value:function(t){this.eventBus.trigger("dfp-native-video:volumechange:".concat(this.config.wid),t)}},{key:"getTemplateData",value:function(){var t=Object.assign({},this.context.ad);return t.adPos=this.config.adPos,t}}])}(t.withMixins(o.$))},s=function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.Z.withMixins(a));(0,r._x)(s,"formatType","dfp_native_video")},84714:function(t,e,n){"use strict";n.d(e,{M:function(){return m},P:function(){return p}});var r=n(24027),i=n(67419),o=n(83351),a=n(39901),s=n(17480);function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function u(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return c(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t){var e;return{c:function(){(e=(0,a.bG)("div")).textContent="".concat(s.U6.ADVERTISEMENT),(0,a.Lj)(e,"class","ad-animated ad-fade ad__disclosure--ex ad__disclosure--programmatic js-ad-disclosure")},m:function(t,n){(0,a.$T)(t,e,n)},p:a.ZT,d:function(t){t&&(0,a.og)(e)}}}function f(t){var e,n=t[0]&&l();return{c:function(){n&&n.c(),e=(0,a.cS)()},m:function(t,r){n&&n.m(t,r),(0,a.$T)(t,e,r)},p:function(t,r){var i=u(r,1)[0];t[0]?n?n.p(t,i):((n=l()).c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null)},i:a.ZT,o:a.ZT,d:function(t){n&&n.d(t),t&&(0,a.og)(e)}}}function d(t,e,n){var r=e.needsDisclosure,i=void 0===r||r;return t.$$set=function(t){"needsDisclosure"in t&&n(0,i=t.needsDisclosure)},[i]}var h=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,a.S1)(n,t,d,f,a.N8,{needsDisclosure:0}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(a.f_),m=function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"template",get:function(){return h}},{key:"getTemplateData",value:function(){return{needsDisclosure:!this.element.querySelector(".js-ad-disclosure")}}},{key:"buildFormat",value:function(){var t=this,n=this.config.wid,o=i.Z.getSlotContainer(n);o?((0,r.Vx)(e,"buildFormat",this,3)([]),o.parentElement.insertBefore(this.element.querySelector(".js-ad-disclosure"),o),requestAnimationFrame((function(){return t.eventBus.trigger("ad-content-rendered:".concat(n))}))):window.raven&&window.raven.captureMessage("buildFormat error: slot does not exist",{tags:{wid:n}})}}])}(o.Z);(0,r._x)(m,"formatType","programmatic");var p=function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e)}(m);(0,r._x)(p,"formatType","programmatic_responsive")},52824:function(t,e,n){"use strict";n.d(e,{$:function(){return o}});var r=n(24027);function i(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function o(t){return function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"setup",value:function(){return this.onPublicEvent("post-message:".concat(this.config.wid),this.onPostMessage.bind(this)),(0,r.Vx)(e,"setup",this,3)([])}},{key:"onPostMessage",value:function(t){var e=t.action,n=t.ad;"error"!==e?"force-collapse"!==e?this.eventBus.trigger("ad-native--".concat(e,":").concat(this.config.wid),n):this.eventBus.trigger("ad-native--loaded:".concat(this.config.wid),function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){i(t,e,n[e])}))}return t}({},n,{type:"empty"})):this.destroy()}}])}(t)}},17748:function(t,e,n){"use strict";n.d(e,{Q:function(){return c}});var r=n(24027),i=n(67419),o=n(186),a=n(84714);function s(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}function c(t){return function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).addFormat(a.M.formatType,a.M),t.wasResizeHidden=!1,t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"destroy",value:function(){this.resizeListenerId&&o.Z.remove(this.resizeListenerId),(0,r.Vx)(e,"destroy",this,3)([])}},{key:"handleSlotRenderEnded",value:function(t){try{var n=this,o=arguments;return(0,r.Vx)(e,"handleSlotRenderEnded",n,3)(o),s(i.Z.isProgrammaticSlot(t,n.config.size),(function(e){e&&(n.eventBus.trigger("ad-content-rendered:".concat(n.config.wid)),n.slotSize=i.Z.getRenderedAdSize(t,{wid:n.config.wid}))}))}catch(a){return Promise.reject(a)}}},{key:"doesAdFit",value:function(){if(!this.slotSize)return!1;var t=(this.parent||this.element).getBoundingClientRect().width;return this.slotSize.width<=t}},{key:"resizeHandler",value:function(){var t=this.element;this.element.parentElement.classList.contains("ad-wireframe-wrapper")&&(t=this.element.parentElement),this.doesAdFit()?(t.classList.remove("ad--collapse-vertical","card"),this.wasResizeHidden&&(this.wasResizeHidden=!1,i.Z.renderWidget(this.config))):(t.classList.add("ad--collapse-vertical"),this.wasResizeHidden=!0)}},{key:"listenForResize",value:function(){var t=this;(this.refreshLayout?this.refreshLayout:Promise.resolve()).then((function(){t._resizeHandlerBound=t.resizeHandler.bind(t),t.resizeListenerId=o.Z.add({throttleTimeout:300,callback:t._resizeHandlerBound})}))}}])}(t)}},20848:function(t,e,n){"use strict";n.d(e,{JB:function(){return s},E6:function(){return u},Z_:function(){return c}});var r=n(24027),i=n(67419);function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function a(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return o(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return o(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t){if(t&&t.renderLookahead&&t.renderLookahead.toString().match(/^x/)){var e=parseFloat(t.renderLookahead.slice(1));return parseInt(window.innerHeight*e,10)}return t&&t.renderLookahead?t.renderLookahead:window.innerHeight}function c(t){return function(t){function e(){return(0,r.PA)(this,e),(0,r.$w)(this,e,arguments)}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"setup",value:function(){var t=this;if(this.noLazyRendering)return(0,r.Vx)(e,"setup",this,3)([]);var n=Promise.all([i.Z.isGPTPrefetch(),i.Z.detectAdBlock()]).then((function(e){var n=a(e,2),r=n[0],i=n[1];if(!r&&!i){var o="viewport-active:".concat(t.config.wid);t.deferRender(new Promise((function(e){t.onPublicEvent(o,e),t.viewable=new IntersectionObserver((function(e,n){e.forEach((function(e){e.isIntersecting&&(n.disconnect(),t.eventBus.trigger(o))}))}),{rootMargin:"".concat(s(t.config),"px"),threshold:0}),t.viewable.observe(t.element)})))}}));return Promise.all([n,(0,r.Vx)(e,"setup",this,3)([])])}},{key:"destroy",value:function(){this.viewable&&(this.viewable.disconnect(),delete this.viewable),(0,r.Vx)(e,"destroy",this,3)([])}}])}(t)}function u(t){return function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).context.env.isAdPost(t.element)&&(t.noLazyRendering=!0),t}return(0,r.XW)(e,t),(0,r.qH)(e)}(t)}},48267:function(t,e,n){"use strict";n.d(e,{Z:function(){return f}});var r=n(24027),i=n(39901);function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function a(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return o(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return o(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t){for(var e,n,r=t[3].default,o=(0,i.nu)(r,t,t[2],null),a=[t[1]],s={},c=0;c<a.length;c+=1)s=(0,i.f0)(s,a[c]);return{c:function(){e=(0,i.bG)("li"),o&&o.c(),(0,i.UF)(e,s)},m:function(t,r){(0,i.$T)(t,e,r),o&&o.m(e,null),n=!0},p:function(t,c){o&&o.p&&(!n||4&c)&&(0,i.km)(o,r,t,t[2],n?(0,i.u2)(r,t[2],c,null):(0,i.VO)(t[2]),null),(0,i.UF)(e,s=(0,i.Lo)(a,[2&c&&t[1]]))},i:function(t){n||((0,i.Ui)(o,t),n=!0)},o:function(t){(0,i.et)(o,t),n=!1},d:function(t){t&&(0,i.og)(e),o&&o.d(t)}}}function c(t){for(var e,n,r=t[3].default,o=(0,i.nu)(r,t,t[2],null),a=[t[1]],s={},c=0;c<a.length;c+=1)s=(0,i.f0)(s,a[c]);return{c:function(){e=(0,i.bG)("div"),o&&o.c(),(0,i.UF)(e,s)},m:function(t,r){(0,i.$T)(t,e,r),o&&o.m(e,null),n=!0},p:function(t,c){o&&o.p&&(!n||4&c)&&(0,i.km)(o,r,t,t[2],n?(0,i.u2)(r,t[2],c,null):(0,i.VO)(t[2]),null),(0,i.UF)(e,s=(0,i.Lo)(a,[2&c&&t[1]]))},i:function(t){n||((0,i.Ui)(o,t),n=!0)},o:function(t){(0,i.et)(o,t),n=!1},d:function(t){t&&(0,i.og)(e),o&&o.d(t)}}}function u(t){var e,n,r,o,u=function(t,e){return"div"===t[0]?0:"li"===t[0]?1:-1},l=[c,s],f=[];return~(e=u(t))&&(n=f[e]=l[e](t)),{c:function(){n&&n.c(),r=(0,i.cS)()},m:function(t,n){~e&&f[e].m(t,n),(0,i.$T)(t,r,n),o=!0},p:function(t,o){var s=a(o,1)[0],c=e;(e=u(t))===c?~e&&f[e].p(t,s):(n&&((0,i.dv)(),(0,i.et)(f[c],1,1,(function(){f[c]=null})),(0,i.gb)()),~e?((n=f[e])?n.p(t,s):(n=f[e]=l[e](t)).c(),(0,i.Ui)(n,1),n.m(r.parentNode,r)):n=null)},i:function(t){o||((0,i.Ui)(n),o=!0)},o:function(t){(0,i.et)(n),o=!1},d:function(t){~e&&f[e].d(t),t&&(0,i.og)(r)}}}function l(t,e,n){var r=e.$$slots,i=void 0===r?{}:r,o=e.$$scope,a=e.tag,s=void 0===a?"div":a,c=e.attributes,u=void 0===c?null:c;return t.$$set=function(t){"tag"in t&&n(0,s=t.tag),"attributes"in t&&n(1,u=t.attributes),"$$scope"in t&&n(2,o=t.$$scope)},[s,u,o,i]}var f=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,l,u,i.N8,{tag:0,attributes:1}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},5263:function(t,e,n){"use strict";n.d(e,{Z:function(){return l}});var r=n(24027),i=n(39901);function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function a(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return o(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return o(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t){var e,n;return{c:function(){e=new i.FW(!1),n=(0,i.cS)(),e.a=n},m:function(r,o){e.m(t[1],r,o),(0,i.$T)(r,n,o)},p:function(t,n){2&n&&e.p(t[1])},d:function(t){t&&(0,i.og)(n),t&&e.d()}}}function c(t){var e,n=t[0]&&s(t);return{c:function(){n&&n.c(),e=(0,i.cS)()},m:function(t,r){n&&n.m(t,r),(0,i.$T)(t,e,r)},p:function(t,r){var i=a(r,1)[0];t[0]?n?n.p(t,i):((n=s(t)).c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null)},i:i.ZT,o:i.ZT,d:function(t){n&&n.d(t),t&&(0,i.og)(e)}}}function u(t,e,n){var r,i=e.isT3Env,o=void 0===i||i,a=e.config,s=void 0===a?{}:a;return t.$$set=function(t){"isT3Env"in t&&n(0,o=t.isT3Env),"config"in t&&n(2,s=t.config)},t.$$.update=function(){4&t.$$.dirty&&n(1,r='<script type="text/x-config">'.concat(JSON.stringify(s),"</")+"script>")},[o,r,s]}var l=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,u,c,i.N8,{isT3Env:0,config:2}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},6888:function(t,e,n){"use strict";n.d(e,{Z:function(){return h}});var r=n(24027),i=n(39901),o=n(48267);function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t){var e,n=t[8].default,r=(0,i.nu)(n,t,t[9],null);return{c:function(){r&&r.c()},m:function(t,n){r&&r.m(t,n),e=!0},p:function(t,o){r&&r.p&&(!e||512&o)&&(0,i.km)(r,n,t,t[9],e?(0,i.u2)(n,t[9],o,null):(0,i.VO)(t[9]),null)},i:function(t){e||((0,i.Ui)(r,t),e=!0)},o:function(t){(0,i.et)(r,t),e=!1},d:function(t){r&&r.d(t)}}}function u(t){var e,n;return e=new o.Z({props:{tag:t[1],attributes:t[2],$$slots:{default:[l]},$$scope:{ctx:t}}}),{c:function(){(0,i.YC)(e.$$.fragment)},m:function(t,r){(0,i.ye)(e,t,r),n=!0},p:function(t,n){var r={};2&n&&(r.tag=t[1]),4&n&&(r.attributes=t[2]),512&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i:function(t){n||((0,i.Ui)(e.$$.fragment,t),n=!0)},o:function(t){(0,i.et)(e.$$.fragment,t),n=!1},d:function(t){(0,i.vp)(e,t)}}}function l(t){var e,n=t[8].default,r=(0,i.nu)(n,t,t[9],null);return{c:function(){r&&r.c()},m:function(t,n){r&&r.m(t,n),e=!0},p:function(t,o){r&&r.p&&(!e||512&o)&&(0,i.km)(r,n,t,t[9],e?(0,i.u2)(n,t[9],o,null):(0,i.VO)(t[9]),null)},i:function(t){e||((0,i.Ui)(r,t),e=!0)},o:function(t){(0,i.et)(r,t),e=!1},d:function(t){r&&r.d(t)}}}function f(t){var e,n,r,o,a=function(t,e){return t[0]?0:1},l=[u,c],f=[];return e=a(t),n=f[e]=l[e](t),{c:function(){n.c(),r=(0,i.cS)()},m:function(t,n){f[e].m(t,n),(0,i.$T)(t,r,n),o=!0},p:function(t,o){var c=s(o,1)[0],u=e;(e=a(t))===u?f[e].p(t,c):((0,i.dv)(),(0,i.et)(f[u],1,1,(function(){f[u]=null})),(0,i.gb)(),(n=f[e])?n.p(t,c):(n=f[e]=l[e](t)).c(),(0,i.Ui)(n,1),n.m(r.parentNode,r))},i:function(t){o||((0,i.Ui)(n),o=!0)},o:function(t){(0,i.et)(n),o=!1},d:function(t){f[e].d(t),t&&(0,i.og)(r)}}}function d(t,e,n){var r,i,o=e.$$slots,a=void 0===o?{}:o,s=e.$$scope,c=e.hasWireframe,u=void 0!==c&&c,l=e.width,f=e.height,d=e.page,h=void 0===d?null:d,m=e.isListItem,p=void 0!==m&&m,v=e.cssClasses,g=void 0===v?function(t){return t}:v;return t.$$set=function(t){"hasWireframe"in t&&n(0,u=t.hasWireframe),"width"in t&&n(3,l=t.width),"height"in t&&n(4,f=t.height),"page"in t&&n(5,h=t.page),"isListItem"in t&&n(6,p=t.isListItem),"cssClasses"in t&&n(7,g=t.cssClasses),"$$scope"in t&&n(9,s=t.$$scope)},t.$$.update=function(){248&t.$$.dirty&&(n(1,r=p?"li":"div"),n(2,i={"data-wireframe-width":l,"data-wireframe-height":f,class:"ad-wireframe-wrapper ".concat(h?"ad-wireframe-wrapper--"+h:""," ").concat(g("ad-wireframe-wrapper--labeled"))}))},[u,r,i,l,f,h,p,g,a,s]}var h=function(t){function e(t){var n;return(0,r.PA)(this,e),n=(0,r.$w)(this,e),(0,i.S1)(n,t,d,f,i.N8,{hasWireframe:0,width:3,height:4,page:5,isListItem:6,cssClasses:7}),n}return(0,r.XW)(e,t),(0,r.qH)(e)}(i.f_)},2282:function(t,e,n){"use strict";n.d(e,{Z:function(){return v}});var r=n(24027),i=n(34686),o=n(67419),a=n(80150),s=n(70833),c=n(70753);function u(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function l(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){u(t,e,n[e])}))}return t}function f(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}var d=c.q0.default;function h(){}var m=c.q0.globalOverride;function p(t){var e=t();if(e&&e.then)return e.then(h)}var v=function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).refreshConfig={},t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"setup",value:function(){try{var t=this;return f(t.isEnabled(),(function(n){if(n){var o,a,c,u=t.config.wid;t.complete=!1,t.creativeId=new i.BH,t.emptyCount=0,t.impressionCount=0,t.impressionViewHit=!1,t.inViewPercent=0,t.isCurrentlyEmpty=!1,t.paused=!1,t.refreshCount=0,t.timeout=null,t.config.targeting.refreshable=!0,t.config.targeting.tbr=0,t.context.element.classList.add("ad--refreshable"),t.refreshConfig=l({},d,t.config.refreshOptions),(0,s.A)("info","lifecycle","InViewRefresh ".concat(u," : AD UNIT OVERRIDES -> updated config ->"),t.refreshConfig);var f=((null===(o=t.context)||void 0===o?void 0:o.localization)||(null===(a=t.context)||void 0===a||null===(c=a.env)||void 0===c?void 0:c.localization)||{}).edition;m.edition[f]&&(t.refreshConfig=l({},t.refreshConfig,m.edition[f]),(0,s.A)("info","lifecycle","InViewRefresh ".concat(u," : EDITION OVERRIDES -> updated config ->"),t.refreshConfig)),t.onPublicEvent("ad-content-ready:".concat(u),t.handleAdContentReadyForRefresh.bind(t)),t.onPublicEvent("post-message:creativeSnippet",t.handleSnippetMessage.bind(t)),t.onPublicEvent("gpt:slotRenderEnded:".concat(u),t.handleSlotRenderEndedRefresh.bind(t)),t.onPublicEvent("gpt:impressionViewable:".concat(u),t.handleImpressionViewableForRefresh.bind(t)),t.onPublicEvent("gpt:slotVisibilityChanged:".concat(u),t.handSlotVisChangeForRefresh.bind(t))}else t.destroy();(0,r.Vx)(e,"setup",t,3)([])}))}catch(n){return Promise.reject(n)}}},{key:"isViewable",value:function(){return this.inViewPercent>=c.q$}},{key:"refreshAd",value:function(){try{var t=this,e=t.config.wid;return f(p((function(){if(t.isViewable())return p((function(){var n;if(!(null===(n=t.scrollFetchInfo)||void 0===n?void 0:n.isFast))return f(o.Z.refreshWidget(t.config,{tbr:1,reload:!0}),(function(){t.eventBus.trigger("ad-refresh:".concat(e)),t.refreshCount++,(0,s.A)("info","lifecycle","InViewRefresh ".concat(e," :"),"Refreshed!",t.debugInfo)}));(0,s.A)("info","lifecycle","InViewRefresh ".concat(e," :"),"Did not refresh (users scroll too fast, speed is ".concat(t.scrollFetchInfo.speed),t.debugInfo)}))})))}catch(n){return Promise.reject(n)}}},{key:"refreshEmptyAd",value:function(){var t,e=this,n=this.config.wid;this.emptyCount++,(0,s.A)("info","lifecycle","InViewRefresh ".concat(n," : ad came up empty"),l({emptyCount:this.emptyCount},this.debugInfo)),this.eventBus.trigger("ad-refresh-reset:".concat(n)),this.timeout=setTimeout((t=function(){return o.Z.refreshWidget(e.config,{reload:!0}),e.timeout=null,f()},function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}),1e3*c.bB)}},{key:"handleAdContentReadyForRefresh",value:function(t){var e=t.type,n=this.config.wid;(0,s.A)("info","lifecycle","InViewRefresh ".concat(n," : type = ").concat(e)),"empty"===e?this.refreshEmptyAd():"programmatic"!==e?((0,s.A)("info","lifecycle","InViewRefresh ".concat(n," : Destroy refresh b/c ad type ").concat(e," is not eligible")),this.destroy()):(0,s.A)("info","lifecycle","InViewRefresh ".concat(n," : enabled!"),l({config:this.refreshConfig},this.debugInfo))}},{key:"handleImpressionViewableForRefresh",value:function(t){var e=this,n=this.config.wid;this.impressionViewHit=!0,this.impressionCount++;var r=this.refreshConfig.inViewSeconds,i=this.refreshConfig.maxRefreshes;this.refreshConfig.infinite&&(i=9999),this.paused?(0,s.A)("info","lifecycle","InViewRefresh ".concat(n," Impression viewed, but paused, will refresh later.")):((0,s.A)("info","lifecycle","InViewRefresh ".concat(n," :"),"Impression Viewable",l({},this.debugInfo,{gptEv:t})),this.refreshCount<i&&!this.timeout?((0,s.A)("info","lifecycle","InViewRefresh ".concat(n," :"),"Timer started... (".concat(r," seconds)")),this.timeout=setTimeout((function(){e.timeout=null,e.refreshAd()}),1e3*r)):this.refreshCount>=i&&(this.complete=!0,(0,s.A)("info","lifecycle","InViewRefresh ".concat(n," :"),"Max refreshes hit. Stopping."),this.destroy()))}},{key:"handleSlotRenderEndedRefresh",value:function(t){var e=this.config.wid;t.isEmpty?((0,s.A)("info","lifecycle","InViewRefresh ".concat(e," : empty ad trying again"),this.debugInfo),this.isCurrentlyEmpty=!0,this.refreshEmptyAd()):this.context.element&&((0,s.A)("info","lifecycle","InViewRefresh ".concat(e," : SlotRenderEnded -> Updating creativeId = ").concat(t.creativeId)),this.creativeId=Promise.resolve(t.creativeId),this.emptyCount=0,this.isCurrentlyEmpty=!1,this.context.element.classList.remove("ad-flexible--empty"),this.context.element.classList.add("ad-flexible--programmatic"),this.context.parentElement&&this.context.element.parentElement.classList.remove("Ad--unfilled"))}},{key:"handSlotVisChangeForRefresh",value:function(t){var e=this.config.wid,n=t.inViewPercentage;this.inViewPercent=n,(0,s.A)("info","lifecycle","InViewRefresh ".concat(e," :"),"SlotVisibilityChanged",this.debugInfo),!this.timeout&&this.isViewable()&&this.impressionCount>this.refreshCount&&!this.isCurrentlyEmpty?((0,s.A)("info","lifecycle","InViewRefresh ".concat(e," :"),"Impression/Refresh Count mismatch due to ad moving out of view. Attempting...",this.debugInfo),this.refreshAd()):!this.timeout&&this.isCurrentlyEmpty&&this.refreshEmptyAd()}},{key:"handleSnippetMessage",value:function(t){try{var e=this,n=e.config.wid,r=t.action,i=t.data.creativeId;return f(e.creativeId,(function(o){"".concat(o)===i&&r===c.nC?((0,s.A)("info","lifecycle","InViewRefresh ".concat(n," -> Disabled by creative: ").concat(i),e.debugInfo),e.destroy()):"".concat(o)===i&&r===c.Ii&&((0,s.A)("info","lifecycle","InViewRefresh ".concat(n," -> Paused by creative; waiting ").concat(t.data.seconds," seconds")),e.paused=!0,e.timeout=setTimeout((function(){e.paused=!1,e.timeout=null,(0,s.A)("info","lifecycle","InViewRefresh ".concat(n," -> Pause ended, refreshing now!")),e.refreshAd()}),1e3*(t.data.seconds||30)))}))}catch(o){return Promise.reject(o)}}},{key:"isEnabled",value:function(){return Promise.all([(0,r.Vx)(e,"isEnabled",this,3)([]),this.config.refreshOptions,!this.complete])}},{key:"removeListeners",value:function(){var t=this.config.wid;this.eventBus.off("ad-content-ready:".concat(t)),this.eventBus.off("gpt:impressionViewable:".concat(t)),this.eventBus.off("gpt:slotRenderEnded:".concat(t)),this.eventBus.off("gpt:slotVisibilityChanged:".concat(t)),this.eventBus.off("post-message:creativeSnippet")}},{key:"destroy",value:function(){var t=this.config.wid;o.Z.clearTargeting(t,["refreshable"]),(0,s.A)("info","lifecycle","InViewRefresh ".concat(t," :"),"Destroying listeners/timers & disabling refresh."),delete this.config.refreshOptions,this.removeListeners(),(0,r.Vx)(e,"destroy",this,3)([])}},{key:"debugInfo",get:function(){return{creativeId:this.creativeId,inViewPercent:this.inViewPercent,impressionCount:this.impressionCount,refreshCount:this.refreshCount,timeout:this.timeout}}}])}(a.ZP)},81383:function(t,e,n){"use strict";n.d(e,{Z:function(){return c}});var r=n(24027),i=n(80150),o=n(6294);function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function s(t){return function(t){if(Array.isArray(t))return a(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var c=function(t){function e(){var t;(0,r.PA)(this,e);for(var n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];return t=(0,r.$w)(this,e,s(i)),(0,r._x)(t,"adjustSticky",(function(){if(t.hasLoaded){var e=t.context.stickyRegistry.getAvailableTop(t.element);e>0&&(e+=10),t.element.style.top="".concat(e,"px")}})),t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"setup",value:function(){var t=this;this.hasLoaded=!1;var n=this.element.parentElement,i=!(0,o.tq)()&&this.config.isRemoveSemiStickyBFN;return i&&n.classList.add("ad-wireframe-wrapper--nostick"),!n.classList.contains("ad-wireframe-wrapper")||n.classList.contains("nostick")||i||(this.onPublicEvent("gpt:slotRenderEnded:".concat(this.config.wid),(function(){t.hasLoaded=!0,t.adjustSticky()})),this.context.stickyRegistry.subscribe(this.adjustSticky),this.addDestroyAction((function(){t.context.stickyRegistry.unsubscribe(t.adjustSticky)}))),(0,r.Vx)(e,"setup",this,3)([])}}])}(i.ZP)},54341:function(t,e,n){"use strict";n.d(e,{Z:function(){return w}});var r=n(24027),i=n(34686),o=n(67419),a=n(80150),s=n(70833),c=n(70753),u=n(93802),l=n(12239),f=n(44399),d=n(71049),h=n(35473);function m(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function p(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){m(t,e,n[e])}))}return t}function v(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}var g={awareness:f.Z,toolbar:l.Z};var y=function(t){return"awareness"===t?h.Z:"toolbar"===t?d.Z:null},b=c.q0.default,A=c.q0.globalOverride,w=function(t){function e(){var t;return(0,r.PA)(this,e),(t=(0,r.$w)(this,e,arguments)).refreshConfig={},t}return(0,r.XW)(e,t),(0,r.qH)(e,[{key:"setup",value:function(){try{var t=this;return v(t.isEnabled(),(function(n){if(n){var o,a,c,u=t.config.wid;t.complete=!1,t.creativeId=new i.BH,t.emptyCount=0,t.impressionCount=0,t.impressionViewHit=!1,t.inViewPercentByPage=m({},u,0),t.isCurrentlyEmpty=!1,t.paused=!1,t.refreshCount=0,t.timeout=null,t.currentPage=-1,t.pages=[],t.currentElement=t.element,t.previousElement=null,t.refreshConfig=p({},b,t.config.refreshOptions);var l=((null===(o=t.context)||void 0===o?void 0:o.localization)||(null===(a=t.context)||void 0===a||null===(c=a.env)||void 0===c?void 0:c.localization)||{}).edition;A.edition[l]&&(t.refreshConfig=p({},t.refreshConfig,A.edition[l]),(0,s.A)("info","lifecycle","SwapRefresh ".concat(u," : EDITION OVERRIDES -> updated config ->"),t.refreshConfig)),t.refreshConfig.pages.forEach((function(e,n){t.pages[n]=p({},t.config,t.config.refreshOptions.pages[n]),"awareness"===t.config.adType&&t.context.env.isBPage&&(t.pages[n].adPos+="-bp",t.pages[n].targeting.pos+="-bp"),t.pages[n].targeting.refreshable=!0,t.pages[n].targeting.tbr=0,t.inViewPercentByPage[e.wid]=0,t.addListeners(e.wid)})),t.addListeners(u),t.onPublicEvent("post-message:creativeSnippet",t.handleSnippetMessage.bind(t)),(0,s.A)("info","lifecycle","SwapRefresh ".concat(u," : Pages:"),t.pages)}else t.destroy();(0,r.Vx)(e,"setup",t,3)([])}))}catch(n){return Promise.reject(n)}}},{key:"isViewable",value:function(){var t=(-1===this.currentPage?this.config:this.pages[this.currentPage]).wid;return this.inViewPercentByPage[t]>=c.q$}},{key:"refreshAd",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.isEmpty,n=void 0!==e&&e;try{var r=this;if(!r.eventBus)return v();r.creativeId=new i.BH;var a=(-1===r.currentPage?r.config:r.pages[r.currentPage]).wid;return v(function(){if(r.isViewable()||n)return function(){var t;if(!(null===(t=r.scrollFetchInfo)||void 0===t?void 0:t.isFast)){var e,n;r.currentPage=r.currentPage===r.pages.length-1?0:r.currentPage+1,(0,s.A)("info","lifecycle","SwapRefresh ".concat(a," :"),"Page ".concat(r.currentPage+1,"/").concat(r.pages.length),r.debugInfo);var i={template:g[r.config.adType],props:{slot:r.pages[r.currentPage],isListItem:!1}};r.previousElement=r.currentElement;var c=null===(e=r.previousElement)||void 0===e?void 0:e.parentElement,l=null===(n=r.previousElement)||void 0===n?void 0:n.nextElementSibling;if(!c||!i.template)return void(0,s.A)("error","render","Missing target or template for SwapRefresh");try{(0,u.Z)(p({},i,{target:c,anchor:l}))}catch(f){(0,s.A)("error","render","renderTemplate failed",f)}return r.currentElement=r.previousElement.nextElementSibling,r.currentElement.classList.add("ad--refreshable"),new(y(r.config.adType))(p({},r.context,{config:r.pages[r.currentPage],element:r.currentElement})).init(),v(o.Z.renderWidget(p({},r.pages[r.currentPage])),(function(){r.eventBus.trigger("ad-refresh:".concat(a)),r.refreshCount++,(0,s.A)("info","lifecycle","SwapRefresh ".concat(a," :"),"Refreshed!",r.debugInfo)}))}(0,s.A)("info","lifecycle","SwapRefresh ".concat(a," :"),"Did not refresh (users scroll too fast, speed is ".concat(r.scrollFetchInfo.speed),r.debugInfo)}()}())}catch(c){return Promise.reject(c)}}},{key:"refreshEmptyAd",value:function(){var t=this;if(this.eventBus){var e=(-1===this.currentPage?this.config:this.pages[this.currentPage]).wid;this.emptyCount++,this.eventBus.trigger("ad-refresh-reset:".concat(e));var n,r=this.emptyCount>2?this.refreshConfig.emptyWaitSec:1;(0,s.A)("info","lifecycle","SwapRefresh ".concat(e," :"),"emptyCount: ".concat(this.emptyCount," - refreshing in ").concat(r,"s"),this.debugInfo),this.timeout=setTimeout((n=function(){return t.refreshAd({isEmpty:!0}),t.timeout=null,v()},function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];try{return Promise.resolve(n.apply(this,t))}catch(r){return Promise.reject(r)}}),1e3*r)}}},{key:"handleAdContentReadyForRefresh",value:function(t){var e=t.type,n=(-1===this.currentPage?this.config:this.pages[this.currentPage]).wid;(0,s.A)("info","lifecycle","SwapRefresh ".concat(n," : type = ").concat(e)),"programmatic"!==e&&"empty"!==e?((0,s.A)("info","lifecycle","SwapRefresh ".concat(n," : Destroy refresh b/c ad type ").concat(e," is not eligible")),this.destroy()):(0,s.A)("info","lifecycle","SwapRefresh ".concat(n," : enabled!"),p({config:this.refreshConfig},this.debugInfo))}},{key:"handleImpressionViewableForRefresh",value:function(t){var e=this,n=(-1===this.currentPage?this.config:this.pages[this.currentPage]).wid;this.impressionViewHit=!0,this.impressionCount++;var r=this.refreshConfig.inViewSeconds,i=this.refreshConfig.maxRefreshes;this.refreshConfig.infinite&&(i=9999),this.paused?(0,s.A)("info","lifecycle","SwapRefresh ".concat(n," Impression viewed, but paused, will refresh later.")):((0,s.A)("info","lifecycle","SwapRefresh ".concat(n," :"),"Impression Viewable",p({},this.debugInfo,{gptEv:t})),this.refreshCount<i&&!this.timeout?((0,s.A)("info","lifecycle","SwapRefresh ".concat(n," :"),"Timer started... (".concat(r," seconds)")),this.timeout=setTimeout((function(){e.timeout=null,e.refreshAd()}),1e3*r)):this.refreshCount>=i&&(this.complete=!0,(0,s.A)("info","lifecycle","SwapRefresh ".concat(n," :"),"Max refreshes hit. Stopping."),this.destroy()))}},{key:"handleSlotRenderEndedRefresh",value:function(t){var e=(-1===this.currentPage?this.config:this.pages[this.currentPage]).wid;this.previousElement&&((0,s.A)("info","lifecycle","SwapRefresh ".concat(e," : removing ad"),this.previousElement),this.previousElement.remove()),t.isEmpty?(this.isCurrentlyEmpty=!0,this.creativeId=Promise.resolve(null),(0,s.A)("info","lifecycle","SwapRefresh ".concat(e," :"),"handleSlotRenderEndedRefresh: refreshEmptyAd()",this.debugInfo),this.refreshEmptyAd()):this.context.element&&((0,s.A)("info","lifecycle","SwapRefresh ".concat(e," : SlotRenderEnded -> Updating creativeId = ").concat(t.creativeId)),this.creativeId=Promise.resolve(t.creativeId),this.emptyCount=0,this.isCurrentlyEmpty=!1,this.context.element.classList.remove("ad-flexible--empty"),this.context.element.classList.add("ad-flexible--programmatic"),this.context.parentElement&&this.context.element.parentElement.classList.remove("Ad--unfilled"))}},{key:"handSlotVisChangeForRefresh",value:function(t){var e=(-1===this.currentPage?this.config:this.pages[this.currentPage]).wid,n=t.inViewPercentage,r=t.slot.getSlotElementId().match(/(\d+-?\d*)$/),i=r?r[1]:null;i&&(this.inViewPercentByPage[i]=n),(0,s.A)("info","lifecycle","SwapRefresh ".concat(e," :"),"SlotVisibilityChanged",this.debugInfo),!this.timeout&&this.isViewable()&&this.impressionCount>this.refreshCount&&!this.isCurrentlyEmpty?((0,s.A)("info","lifecycle","SwapRefresh ".concat(e," :"),"Impression/Refresh Count mismatch due to ad moving out of view. Attempting...",this.debugInfo),this.refreshAd()):!this.timeout&&this.isCurrentlyEmpty&&((0,s.A)("info","lifecycle","SwapRefresh ".concat(e," :"),"SlotVisibilityChanged: refreshEmptyAd()",this.debugInfo),this.refreshEmptyAd())}},{key:"handleSnippetMessage",value:function(t){try{var e=this,n=(-1===e.currentPage?e.config:e.pages[e.currentPage]).wid,r=t.action,i=t.data.creativeId;return v(e.creativeId,(function(o){"".concat(o)===i&&r===c.nC?((0,s.A)("info","lifecycle","SwapRefresh ".concat(n," -> Disabled by creative: ").concat(i),e.debugInfo),e.destroy()):"".concat(o)===i&&r===c.Ii&&((0,s.A)("info","lifecycle","SwapRefresh ".concat(n," -> Paused by creative; waiting ").concat(t.data.seconds," seconds")),e.paused=!0,e.timeout=setTimeout((function(){e.paused=!1,e.timeout=null,(0,s.A)("info","lifecycle","SwapRefresh ".concat(n," -> Pause ended, refreshing now!")),e.refreshAd()}),1e3*(t.data.seconds||30)))}))}catch(o){return Promise.reject(o)}}},{key:"isEnabled",value:function(){var t=this,n=!0;return this.config.refreshOptions.pages.forEach((function(e){var r=e.wid;t.config.wid===r&&(n=!1)})),Promise.all([n,(0,r.Vx)(e,"isEnabled",this,3)([]),this.config.refreshOptions,!this.complete])}},{key:"addListeners",value:function(t){this.onPublicEvent("ad-content-ready:".concat(t),this.handleAdContentReadyForRefresh.bind(this)),this.onPublicEvent("gpt:slotRenderEnded:".concat(t),this.handleSlotRenderEndedRefresh.bind(this)),this.onPublicEvent("gpt:impressionViewable:".concat(t),this.handleImpressionViewableForRefresh.bind(this)),this.onPublicEvent("gpt:slotVisibilityChanged:".concat(t),this.handSlotVisChangeForRefresh.bind(this))}},{key:"removeListeners",value:function(t){this.eventBus.off("ad-content-ready:".concat(t)),this.eventBus.off("gpt:impressionViewable:".concat(t)),this.eventBus.off("gpt:slotRenderEnded:".concat(t)),this.eventBus.off("gpt:slotVisibilityChanged:".concat(t))}},{key:"destroy",value:function(){var t=this,n=this.config.wid;o.Z.clearTargeting(n,["refreshable"]),(0,s.A)("info","lifecycle","SwapRefresh ".concat(n," :"),"Destroying listeners/timers & disabling refresh."),this.removeListeners(n),this.pages.forEach((function(e){t.removeListeners(e.wid)})),this.eventBus.off("post-message:creativeSnippet"),delete this.config.refreshOptions,(0,r.Vx)(e,"destroy",this,3)([])}},{key:"debugInfo",get:function(){return{currentPage:"".concat(this.currentPage+1),creativeId:this.creativeId,emptyCount:this.emptyCount,inViewPercentByPage:this.inViewPercentByPage,impressionCount:this.impressionCount,refreshCount:this.refreshCount,timeout:this.timeout}}}])}(a.ZP)},39782:function(t,e,n){"use strict";function r(t){var e=t.trackFn,n=t.eventName,r=t.eventData,i=t.hasEventUrl,o=void 0!==i&&i;if((e=e||window.bfa)&&n&&r&&"object"===typeof r&&r.l){var a;if(o)a=n;else if("click"===n)a="track/click/".concat(r.l);else{if("scroll"!==n)return;a="track/scroll/impression"}e(a,r)}}n.d(e,{i:function(){return r}})},22299:function(t,e,n){"use strict";n.d(e,{Z:function(){return a}});var r=n(58451);var i,o;var a={init:function(t){var e=t.abeagle;return i||(i=e.isOn("ads_blockthrough").then((function(t){return t?Promise.resolve(!0):Promise.reject(new r.x9)})).then((function(){return new Promise((function(t,e){var n=document.createElement("script");n.onload=function(){return t(n)},n.onerror=function(){e("Blockthrough: Script failed to load")},n.src="//buzzfeed-com.videoplayerhub.com/galleryplayer.js",n.async=!0,n.dataset.domain="buzzfeed.com",n.id="BLOCKTHROUGH",document.head.appendChild(n)}))})).catch((function(t){return e=t,(null!=(n=r.x9)&&"undefined"!==typeof Symbol&&n[Symbol.hasInstance]?n[Symbol.hasInstance](e):e instanceof n)?null:Promise.reject(t);var e,n})))},detectAcceptableAds:function(){return o||(o=new Promise((function(t){var e=setTimeout((function(){return t(!1)}),3e3);window.addEventListener("AcceptableAdsInit",(function(n){clearTimeout(e),n.detail?t(!0):t(!1)}))})))}}},36606:function(t,e,n){"use strict";n.d(e,{ZP:function(){return s},m0:function(){return a}});var r={de:{ADVERTISE_WITH_BUZZFEED:"Wirb mit BuzzFeed",ADVERTISE_WITH_URL:"https://advertise.buzzfeed.com/bfdeutschland",ADVERTISEMENT:"Anzeige",PAID_POST:"Anzeige",PROMOTED_BY:"Pr\xe4sentiert von",PROMOTED:"Anzeige",SPONSORED_BY:""},en:{ADVERTISE_WITH_BUZZFEED:"Advertise with BuzzFeed",ADVERTISE_WITH_URL:"https://advertise.buzzfeed.com",ADVERTISEMENT:"Advertisement",PAID_POST:"Paid Post",PROMOTED_BY:"Promoted By",PROMOTED:"Promoted",SPONSORED_BY:"Sponsored By"},es:{ADVERTISE_WITH_BUZZFEED:"An\xfanciate en BuzzFeed",ADVERTISE_WITH_URL:"https://advertise.buzzfeed.com/bfespana",ADVERTISEMENT:"Publicidad",PAID_POST:"Patrocinado",PROMOTED_BY:"Patrocinado por",PROMOTED:"Patrocinado",SPONSORED_BY:"Patrocinado por"},fr:{ADVERTISE_WITH_BUZZFEED:"Annoncer sur BuzzFeed",ADVERTISE_WITH_URL:"https://advertise.buzzfeed.com/bffrance",ADVERTISEMENT:"Publicit\xe9",PAID_POST:"Sponsoris\xe9",PROMOTED_BY:"Sponsoris\xe9 par",PROMOTED:"Sponsoris\xe9",SPONSORED_BY:""},ja:{ADVERTISE_WITH_BUZZFEED:"",ADVERTISE_WITH_URL:"",ADVERTISEMENT:"\u5e83\u544a",PAID_POST:"Sponsored",PROMOTED_BY:"Sponsored by",PROMOTED:"Sponsored",SPONSORED_BY:"Sponsored by"},pt:{ADVERTISE_WITH_BUZZFEED:"",ADVERTISE_WITH_URL:"",ADVERTISEMENT:"Publicidade",PAID_POST:"Conte\xfado Pago",PROMOTED_BY:"Em parceria com",PROMOTED:"Patrocinado",SPONSORED_BY:""},test:{FOO:"Localized foo",ADVERTISEMENT:"Test Advertisement",PROMOTED_BY:"Test Promoted By"}},i="en",o={_curr:i,_translation:{},set translation(t){"string"!==typeof t?(this._translation=t,this._curr=""):r[t]?(this._translation=r[t],this._curr=t):(this._translation=r.en||{},this._curr=t)},get translation(){return this._translation}};function a(t){o.translation=t||i}var s={getTranslationStr:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return o.translation[t]||e||r.en[t]||t}}},186:function(t,e,n){"use strict";n.d(e,{Z:function(){return c}});var r=n(24027),i=n(76635),o=0,a={};a={};var s=function(){return(0,r.qH)((function t(e){var n=this,o=e.throttleTimeout,a=e.parent;(0,r.PA)(this,t),this.callbacks={},this.target=a||window,this._triggerThrottled=(0,i.throttle)((function(){return n.trigger()}),o),this.target.addEventListener("resize",this._triggerThrottled)}),[{key:"add",value:function(t){return this.callbacks[++o]=t,o}},{key:"remove",value:function(t){delete this.callbacks[t]}},{key:"trigger",value:function(t){if(t in this.callbacks)this.callbacks[t]();else for(var e in this.callbacks)e in this.callbacks&&this.callbacks[e]()}},{key:"destroy",value:function(){this.target.removeEventListener("resize",this._triggerThrottled),delete this.target,delete this.callbacks,delete this._triggerThrottled}},{key:"isEmpty",get:function(){return 0===Object.keys(this.callbacks).length}}])}(),c={add:function(t){var e=t.throttleTimeout,n=void 0===e?350:e,r=t.parent,i=t.callback,o=a[n];o||(o=a[n]=new s({throttleTimeout:n,parent:r}));var c=o.add(i);return o.trigger(c),c},remove:function(t){Object.keys(a).forEach((function(e){var n=a[e];n.remove(t),n.isEmpty&&(n.destroy(),delete a[e])}))}}},17480:function(t,e,n){"use strict";n.d(e,{U6:function(){return a},bb:function(){return s},KV:function(){return u},xw:function(){return c},TV:function(){return i},eV:function(){return o}});var r=n(36606);function i(t,e){return!e||e.length<0?t:e+encodeURIComponent(t)}function o(t){var e=(new DOMParser).parseFromString('<div id="root">'.concat(t,"</div>"),"text/html").getElementById("root");return e.querySelectorAll(["script","img","picture","video","audio","embed","object"].join(",")).forEach((function(t){return t.parentElement.removeChild(t)})),e.innerHTML}function a(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return r.ZP.getTranslationStr(t,e)}function s(t,e){var n="@o:{dimension2:".concat(t.id,",dimension4:").concat(t.titleOriginal,"}"),r="@e:{obj_type:post,obj_id:".concat(t.id,",post_category:").concat(t.category)+(t.flexproVariation?",variation_id:".concat(t.flexproVariation):"")+(e?",p:".concat(e):"")+"}";return"".concat("@a:click:post|dfp",";").concat(n,";").concat(r,";")}Object.defineProperties(a,{ADVERTISEMENT:{get:function(){return a("ADVERTISEMENT","Advertisement")}},PROMOTED_BY:{get:function(){return a("PROMOTED_BY","Promoted by")}}});var c={"1:1":"data:image/gif;base64,R0lGODlhAQABAIAAAP///////yH5BAEAAAEALAAAAAABAAEAAAICTAEAOw==","4:3":"data:image/gif;base64,R0lGODlhBAADAIAAAP///////yH5BAEAAAEALAAAAAAEAAMAAAIDjI9WADs=","16:9":"data:image/gif;base64,R0lGODlhEAAJAIAAAP///////yH5BAEAAAEALAAAAAAQAAkAAAIKjI+py+0Po5yUFQA7"};function u(t,e){try{var n=new MouseEvent(t.type,t);e.dispatchEvent(n)}catch(r){e.click()}}},93802:function(t,e,n){"use strict";n.d(e,{Z:function(){return i}});var r=n(37083);function i(t){var e,n,i=t.template,o=t.props,a=t.target,s=t.anchor;if("function"===typeof i&&"$set"in i.prototype)return new i({props:o,target:a,anchor:s});if("env"in i){var c=(0,r.Z)(i).render(o);if(s){if(e=s,!(null!=(n=Node)&&"undefined"!==typeof Symbol&&n[Symbol.hasInstance]?n[Symbol.hasInstance](e):e instanceof n))throw new TypeError("`anchor` is not of type `Node`");if(s.parentElement!==a)throw new Error("`anchor` is not a child of `target`");s.insertAdjacentHTML("beforebegin",c)}else a.insertAdjacentHTML("beforeend",c);return null}return null}},99945:function(t,e,n){"use strict";n.d(e,{qB:function(){return s}});var r=n(20238),i=[".js-ad-thumbnail",".bf-image",".bf-image-big",".bf-image-dblbig",".bf-mobile-image",".bf-bg-image",".bf-image-bigstory",".bf-image-dblwidestory",".bf-image-dblbigstory",".bf-image-widestory"].join(",");function o(){var t=Promise.resolve();return t.unsubscribe=function(){},t}function a(t,e){if(t.querySelectorAll(i).forEach((function(t){if(!(e.thumbnails.indexOf(t)>-1)){var n;if("IMG"===t.nodeName)n=function(t){if(!t.src||/^data:image/.test(t.src))return null;var e,n=[],i=(0,r.SV)(t.src).split(".").pop(),o=new Promise((function r(o){if(t.complete)o();else if(/^gif$/i.test(i))if(t.naturalHeight)o();else{var a=setTimeout((function(){r(o)}),100);n.push((function(){return clearTimeout(a)}))}else e||(e=function(){o(),t.removeEventListener("load",e),t.removeEventListener("error",e)},t.addEventListener("load",e),t.addEventListener("error",e),n.push((function(){t.removeEventListener("load",e),t.removeEventListener("error",e)})))}));return o.unsubscribe=function(){n.forEach((function(t){return t()})),n=[]},o}(t);else if("VIDEO"===t.nodeName)n=function(t){var e,n=new Promise((function(n){t.readyState>=HTMLVideoElement.HAVE_METADATA?n():(e=function(){n(),t.removeEventListener("loadedmetadata",e)},t.addEventListener("loadedmetadata",e))}));return n.unsubscribe=function(){return t.removeEventListener("loadedmetadata",e)},n}(t);else if("IFRAME"===t.nodeName)n=o();else{var i=getComputedStyle(t).backgroundImage;/^url/.test(i)&&(n=o())}n&&(e.thumbnails.push(t),e.thumbnailsReady.push(n))}})),0===e.thumbnailsReady.length)return null;var n=Promise.all(e.thumbnailsReady);return n.unsubscribe=function(){e.thumbnails=[],e.thumbnailsReady.forEach((function(t){t.unsubscribe()})),e.thumbnailsReady=[]},n}function s(t){var e,n={thumbnails:[],thumbnailsReady:[]},r=new Promise((function(r){var i,o;e=function(){i&&i.disconnect(),o&&o.unsubscribe()},(o=a(t,n))?o.then(r):(i=new MutationObserver((function(){(o=a(t,n))&&(o.then(r),i.disconnect())}))).observe(t,{subtree:!0,attributes:!0,childList:!0})}));return r.unsubscribe=e,r.then(r.unsubscribe),r}},17807:function(t,e,n){"use strict";n.d(e,{Z:function(){return o}});var r,i=n(34686);r={};var o={getUnique:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.startFrom,o=void 0===n?1:n;if(null===t||void 0===t||""===String(t)||/\[object Object\]/.test(String(t)))throw new TypeError("The argument should be a primitive non-empty value");t in r||(r[t]=(0,i.Ul)(o));var a=r[t];return a()},purge:function(t){delete r[t]}}},90093:function(t,e,n){"use strict";n.d(e,{p:function(){return r}});var r=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"buzzfeed.com";try{var n=new URL(t);if(n.hostname.includes(e))return!0}catch(r){if("/"===t[0])return!0}return!1}},40237:function(t,e,n){"use strict";n.d(e,{Z:function(){return S}});var r=n(94776),i=n.n(r),o=n(9845),a=n(20238),s="track/abtest",c="track/abeagle/request";var u=n(80352),l=n(18215),f=n(76635);function d(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(u){return void n(u)}s.done?e(c):Promise.resolve(c).then(r,i)}function h(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function a(t){d(o,r,i,a,s,"next",t)}function s(t){d(o,r,i,a,s,"throw",t)}a(void 0)}))}}var m,p=[],v={},g={},y={},b=[],A=function(){return l.Z.getBuzzfeedSubdomainOrWildcard(window.location.hostname)},w=null;function E(t,e){g[t]=e,v[t]&&v[t].forEach((function(t){return t.resolve(e)}))}function T(t,e){return R.apply(this,arguments)}function R(){return(R=h(i().mark((function t(e,n){return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.asyncIsEligible){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,e.asyncIsEligible();case 4:if(!t.sent){t.next=6;break}E(e.name,n);case 6:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function P(){p.forEach((function(t){t.asyncIsEligible?T(t,w):t.isEligible()&&E(t.name,w)}))}function L(t,e){var n=window.location.hostname.replace("www","");l.Z.remove(t,".".concat(n));var r=A(),i=r==="www.".concat(n)?14:1;l.Z.set({name:t,value:e,days:i,domain:r})}function x(t,e){window.bfa(t,{data:e})}function k(t){return Object.keys(t).forEach((function(e){var n="".concat(e,"_version"),r=t[e],i=r.value,o=r.version,a=r.error,c=r.resolved;if(a){var u={type:"ExperimentServerError",name:e,error:a};return y[e]=u,void(v[e]&&v[e].forEach((function(t){return t.resolve(w)})))}if(c&&(i=i||"control"),E(e,i),null===i){var f=A();return l.Z.remove(e,f),void l.Z.remove(n,f)}b.push([e,r.id,r.version,r.value,r.variant_id].join("|"));var d=l.Z.get(e)===String(i),h=l.Z.get(n)===String(o);d&&h||(L(e,i),L(n,o),x("".concat(s,"/").concat(e),{experiment:e,variation:r}))})),b}function O(t,e){var n=[];p.forEach((function(t){"boolean"===typeof t.storeResponse&&t.storeResponse&&n.push(t.name)})),0!==n.length&&t.forEach((function(t){if(-1!==n.indexOf(t)){var r=e[t];if(!r||r.error||null===r.value)E(t,w);else{var i=t+"_metadata",o=r;o=JSON.stringify(o),o=escape(o),l.Z.get(i)!==o&&L(i,o)}}}))}function C(t){return(0,a.jH)(window.location.search)["abeagle_".concat(t)]}var S={registerExperiments:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];p=t,m=null},getExperimentMetaData:function(t){var e={};return t.forEach((function(t){var n=l.Z.get(t+"_metadata");try{n=unescape(n),n=JSON.parse(n),e[t]=n}catch(r){return}})),e},getExperimentVariant:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.rejectErrors,r=void 0===n||n,i=e.defaultVariantIfUnbucketed,o=void 0===i?"control":i,a=e.errorVariant,s=void 0===a?w:a,c=C(t);if(c)return Promise.resolve(c);var u,l=p.filter((function(e){return e.name===t}))[0],f=y[t];return l?l.isEligible()||l.asyncIsEligible||(f={type:"ExperimentNotEligible",name:t}):f={type:"ExperimentNotFound",name:t},f?!1===r?(this.defaultErrorHandler(f),u=Promise.resolve(s)):u=Promise.reject(f):u=t in g?Promise.resolve(g[t]):new Promise((function(e,n){v[t]?v[t].push({resolve:e,reject:n}):v[t]=[{resolve:e,reject:n}]})),o!==w&&(u=u.then((function(t){return t===w?o:t}))),u},isOn:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"on";return(0,f.isNil)(e)?Promise.reject("onValue cannot be null or undefined"):this.getExperimentVariant(t,{rejectErrors:!1}).then((function(t){return t===e}))},defaultErrorHandler:function(t){if(e=t,null!=(n=Error)&&"undefined"!==typeof Symbol&&n[Symbol.hasInstance]?n[Symbol.hasInstance](e):e instanceof n)throw t;var e,n;if("prod"!==window.BZFD.Config.env)switch(t.type){case"ExperimentNotFound":console.log('Experiment "'.concat(t.name,'" is not registered'));break;case"ExperimentNotEligible":console.log('Experiment "'.concat(t.name,'" is not eligible'));break;case"ExperimentServerError":console.log("Experiment ".concat(t.name," error: ").concat(t.error))}},getVariants:function(t){if(t)return p.forEach((function(t){var e=l.Z.get(t.name);t.asyncIsEligible?T(t,e):t.isEligible()&&E(t.name,e)})),Promise.resolve();if(m)return Promise.resolve();var e=p.filter((function(t){return t.isEligible()})).map((function(t){return t.name}));return e.length?this.requestVariants(e):Promise.resolve()},requestVariants:function(t){var e=(new Date).getTime(),n=(0,o.TQ)();return u.Z.get("".concat(window.BZFD.Config.abeagle.url,"/public/v2/experiment_variants"),{data:{experiment_names:t.join(";"),user_id:n,client_id:"buzz_web"},timeout:1500}).then((function(n){x(c,{responseTime:Date.now()-e}),k(n),O(t,n)})).catch(P)},requestVariantsAsync:function(t){return h(i().mark((function e(){var n,r;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.asyncIsEligible){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,t.asyncIsEligible();case 4:n=e.sent,delete t.asyncIsEligible,n?(t.isEligible=function(){return!0},(r=l.Z.get(t.name))?E(t.name,r):this.requestVariants([t.name])):E(t.name,w);case 7:case"end":return e.stop()}}),e,this)}))).apply(this)},saveExperiments:function(t){"undefined"!==typeof t.experimentsMap&&"undefined"!==typeof t.experimentNames&&(k(t.experimentsMap),O(t.experimentNames,t.experimentsMap))},start:function(t){var e=this;this.getVariants(t),t?t.start(p):p.forEach((function(t){if(t.asyncIsEligible)e.requestVariantsAsync(t);else if(t.isEligible()){var n=l.Z.get(t.name);n&&E(t.name,n)}else E(t.name,w)}))},reset:function(){p=[],v={},y={},g={},m=null,b=[]}}},80352:function(t,e,n){"use strict";var r=n(26528),i=n.n(r),o=n(41559),a=n(2601),s=n.n(a);function c(t){var e=[];for(var n in t)if(t.hasOwnProperty(n)){var r=t[n];null!==r&&""!==r&&"undefined"!==typeof r&&e.push(n+"="+r)}return e.join("&")}function u(t){return new Promise((function(e,n){setTimeout((function(){return n({type:"timeout",msg:"".concat(t,"ms timeout exceeded")})}),t)}))}function l(t){var e=t.url;return function(t){return(0,o.x)("xhr",t.type||"error",{url:e,status:t.status||0}),Promise.reject(t)}}function f(t){return new Promise((function(e,n){s()(t,(function(t,r){return t?n(t):e(r)}))}))}function d(t){return t.ok?Promise.resolve(t):Promise.reject({type:"error",status:t.status,statusText:t.statusText,response:t})}function h(t){return t.json()}function m(t){return t.text()}function p(t){if("dev"!==BZFD.Config.env)return t;var e=t.indexOf("?")>-1?"&":"?";return t+e+"aW50ZWdyYWxpc3RfZGlkX3RoaXNfb25fMjAxOV8xMV8xMQ=="}e.Z={get:function(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.type,o=void 0===r?"json":r,a=n.data,s=void 0===a?{}:a,v=n.params,g=void 0===v?{}:v,y=n.skipAuth,b=n.timeout;if(!t)return Promise.reject("URL parameter is required");var A=Object.assign({credentials:"same-origin"},g),w=t,E=c(s);switch(E&&(w+=(w.indexOf("?")>-1?"&":"?")+E),y&&(w=p(w)),o){case"json":e=i()(w,A).then(d).then(h);break;case"text":e=i()(w,A).then(d).then(m);break;case"jsonp":e=f(w);break;default:e=Promise.reject("Unsupported type ".concat(o))}return(b?Promise.race([u(b),e]):e).catch(l({url:t}))},post:function(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.data,o=n.type,a=void 0===o?"json":o,s=n.params,f=void 0===s?{}:s,v=n.skipAuth,g=void 0!==v&&v,y=n.raw,b=void 0!==y&&y,A=n.timeout;if(!t)return Promise.reject("URL parameter is required");if(!r)return Promise.reject("Can not send POST request without data");g&&(t=p(t));var w=Object.assign({headers:{"Content-type":"application/x-www-form-urlencoded"},credentials:"same-origin"},f);switch(w.method="POST",w.body=b?r:c(r),a){case"json":e=i()(t,w).then(d).then(h);break;case"text":e=i()(t,w).then(d).then(m);break;default:e=Promise.reject("Unsupported type ".concat(a))}return(A?Promise.race([u(A),e]):e).catch(l({url:t}))}}},95609:function(t,e,n){"use strict";function r(t,e,n){for(var r=n?t:t.parentNode;r&&r!==document;){if(r.matches(e))return r;r=r.parentNode}return null}n.d(e,{Z:function(){return r}})},93557:function(t,e,n){"use strict";n.d(e,{z:function(){return d}});var r=n(20238),i=(n(40237),n(80352)),o=n(76635),a=n(95609);var s={get isBPage(){try{return"buzz"in window.BZFD.Context}catch(t){return!1}},get hasQuizBadge(){try{return window.BZFD.Context.buzz.badges.filter((function(t){return"quiz"===t.badge_type})).length>0}catch(t){return!1}},get hasQuiz(){try{return window.BZFD.Context.page.hasQuiz||this.hasQuizBadge}catch(t){return!1}},get hasList(){try{return window.BZFD.Context.buzz.format.type.match(/list/)}catch(t){return!1}},get isFeedPage(){return!!window.FEEDPAGER||!!window.BZFD.Context.feedpager},get isFeedpager(){return s.isFeedPage},get isFeed(){return!!window.BZFD.Context.isFeed},get isHomePage(){return!!BZFD.Context.homepage||"home"===s.pageName},get isBuzzblocks(){try{return"buzzblocks"===BZFD.Config.service}catch(t){return!1}},get isDev(){try{return"dev"===BZFD.Config.env}catch(t){return!1}},get isStage(){try{return"stage"===BZFD.Config.env}catch(t){return!1}},get isProd(){try{return"prod"===BZFD.Config.env}catch(t){return!1}},get isPharmaceutical(){return"emdserono"===s.author},get author(){try{return s.isBPage?BZFD.Context.buzz.username:BZFD.Context.page.username}catch(t){return null}},get pageName(){try{return BZFD.Context.page.name}catch(t){return null}},get pageCategory(){try{return BZFD.Context.page.category}catch(t){return null}},get pageVertical(){try{return BZFD.Context.page.vertical}catch(t){return null}},get pageClassification(){try{return BZFD.Context.page.classification||{}}catch(t){return{}}},get localization(){try{return(0,o.pick)(BZFD.Context.page.localization,["country","language","locale"])}catch(t){return{}}},get allPageClassifications(){try{return BZFD.Context.page.allClassifications||{}}catch(t){return{}}},get allPageSections(){try{return BZFD.Context.page.allClassifications.sections||[]}catch(t){}try{return BZFD.Context.page.sections||[]}catch(e){return[]}},get allPageEditions(){try{return BZFD.Context.page.allClassifications.editions||[]}catch(t){return[]}},get pageFilter(){try{return BZFD.Context.page.filter}catch(t){return null}},get pageFilters(){try{return BZFD.Context.page.filters||{}}catch(t){return{}}},get pageMainFilter(){try{var t,e=BZFD.Context.page.filters;for(var n in e)if(e[n].is_main){t=n;break}return t||null}catch(r){return null}},get isWidePost(){try{return"buzz"in BZFD.Context&&"wide"===BZFD.Context.page.width}catch(t){return!1}},get facebookApiAppId(){try{return BZFD.Config.facebookApi.appId}catch(t){return null}},get facebookApiVersion(){try{return BZFD.Config.facebookApi.version}catch(t){return"v2.9"}},get isNewsPost(){try{return"news"===BZFD.Context.page.vertical}catch(t){return!1}},get buzzType(){var t="article";return s.hasQuiz?t="quiz":s.hasList&&(t="list"),t},get buzzTags(){try{return BZFD.Context.buzz.tags}catch(t){return[]}},get buzzFlags(){try{return BZFD.Context.buzz.flags}catch(t){return{}}},get locale(){try{return BZFD.Context.page.localization.locale}catch(t){return"en_US"}},get hasTranslations(){try{return!!BZFD.Context.page.localization.translations}catch(t){return!1}},get webRoot(){try{return BZFD.Config.webRoot}catch(t){return""}},get prebidUnits(){try{BZFD.Config.ads.prebidUnits.length}catch(t){(0,o.set)(window,"BZFD.Config.ads.prebidUnits",[])}return BZFD.Config.ads.prebidUnits},get programmaticSizes(){try{return BZFD.Config.ads.programmaticSizes}catch(t){throw new Error("`BZFD.Config.ads` is not defined, which may cause ads to work incorrectly; use `bf_ads.get_ads_config` to make the config")}},get contextDestination(){var t,e;try{t=BZFD.Context.buzz.destination}catch(n){t=null}try{e=BZFD.Context.page.destination}catch(r){e=null}return t||e||"buzzfeed"},get isShopping(){try{return"Shopping"===s.pageCategory||-1!==s.allPageSections.indexOf("Shopping")}catch(t){return!1}},get isCommerce(){try{return!(!this.isShopping&&!s.buzzTags.some((function(t){return"intlcomtent"===t||"comtent"===t})))}catch(t){return!1}},get isCommunity(){return"Community"===s.pageCategory},get isAsIs(){try{return s.isBPage&&/^(As\/?Is)/im.test(s.pageClassification.section)||"asis"===s.pageName}catch(t){return!1}},get isBFO(){try{return"buzzfeed"===s.contextDestination}catch(t){return!1}},get isBFN(){try{return"buzzfeed_news"===s.contextDestination}catch(t){return!1}},get buzzId(){try{return BZFD.Context.buzz.id}catch(t){return""}},isAdPost:function(t){try{var e=function(t){var e=null;try{var n=(0,a.Z)(t,".js-buzz",!0);n&&(e=JSON.parse(n.querySelector("script").textContent).context)}catch(r){}return e}(t);return!!e.buzz.flags.ad}catch(n){}try{return!!BZFD.Context.buzz.flags.ad}catch(r){return!1}}},c=s,u=(n(43790),n(42252)),l="/site-component",f=function(t){var e={buzzIds:"buzz_ids",page:"page",pageSize:"page_size",imageCrop:"image_crop",advertiserId:"advertiser_id",showName:"show_name",keywords:"keywords",fields:"fields",q:"q",qFuzzy:"q__fuzzy",username:"username",areaId:"area_id",startId:"start_id",startFrom:"start_from",convertLinks:"convert_links",platform:"platform"};return Object.keys(e).reduce((function(n,r){var i=e[r],o=t[r];return o&&(n[i]=o),n}),{})};function d(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"v1",a=n&&"control"!==n?"".concat(t.uri,"/").concat(n):t.uri,s=e.origin||c.webRoot,d="".concat(s).concat(l,"/").concat(o),h=e.localization&&e.localization.getEdition?e.localization.getEdition():u.ZP.getEdition(e.service),m=t.edition(h,n);m&&(d="".concat(d,"/").concat(m));var p=f(e);Object.assign(p,e.extraParams);var v=Object.keys(p).length>0?(0,r.nZ)(p):"";return i.Z.get("".concat(d,"/").concat(a).concat(v),{skipAuth:!0}).then((function(e){return Promise.resolve(t.view(e))}))}},18215:function(t,e){"use strict";function n(t,e){var n=t.match(e);return n&&n.length?n[0]:null}function r(){return"prod"===window.BZFD.Config.env?"buzzfeed.com":window.location.hostname}e.Z={getBuzzfeedSubdomainOrWildcard:function(t){var e=n(t,"(dev|stage|www).(buzzfeed|buzzfeednews).(com|io)$");return e||n(t,".?[a-z]+.[a-z]+$")},get:function(t){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n="".concat(t,"="),r=document.cookie.split(";"),i=0;i<r.length;i++){for(var o=r[i];" "===o.charAt(0);)o=o.substring(1,o.length);if(0===o.indexOf(n))return o.substring(n.length,o.length)}return e},set:function(t){var e=t.name,n=t.value,i=t.days,o=t.domain;o=o||r();var a="";if(i){var s=new Date;s.setTime(s.getTime()+24*i*60*60*1e3),a="; expires=".concat(s.toGMTString())}return document.cookie="".concat(e,"=").concat(n).concat(a,"; path=/; domain=").concat(o)},remove:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r();return this.set({name:t,value:"",days:-1,domain:e})}}},11608:function(t,e,n){"use strict";n.d(e,{oq:function(){return a.Z},ZP:function(){return u},SU:function(){return c}});var r="undefined"!==typeof document?document.documentElement:null,i="undefined"!==typeof document&&!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect;!function(){try{localStorage.setItem("ls_test",!0),localStorage.removeItem("ls_test")}catch(t){return!1}}();r&&!i&&r.classList.add("no-svg"),"undefined"!==typeof document&&(navigator.userAgent.match(/Pinterest/i)||document.referrer.match(/pinterest.com/i))&&r.classList.add("pinterest");(function(){var t="ontouchstart"in window;t&&r.classList.add("has-touch")})(),"undefined"===typeof document||(navigator.userAgent.match(/android/i)?r.classList.add("is-android"):navigator.userAgent.match(/iphone|ipad|ipod/i)&&r.classList.add("is-ios")),function(){var t=document.referrer,e=window.location.search,n=navigator.userAgent.match(/fban|twitter|pinterest/i),r=n?n[0]:"";e.match("referrer=pinterest")||t.match("pinterest")||"pinterest"===r||(e.match("referrer=twitter")||t.match("t.co")||"twitter"===r||(e.match("referrer=facebook")||t.match("m.facebook")))}();var o=function(){return window.innerHeight||r.clientHeight},a=n(95609);function s(t,e){return null!=e&&"undefined"!==typeof Symbol&&e[Symbol.hasInstance]?e[Symbol.hasInstance](t):t instanceof e}function c(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.properties,r=void 0===n?["any"]:n,i=function(t){"any"in a?a.any(t):t.propertyName in a&&a[t.propertyName](t)},o=["transitionend","webkitTransitionEnd","mozTransitionEnd","oTransitionEnd"],a={},s=[];r.forEach((function(t){var e,n=new Promise((function(t){return e=t}));s.push(n),a[t]=e})),o.forEach((function(e){return t.addEventListener(e,i)}));var c=Promise.all(s);return c.unsubscribe=function(){o.forEach((function(e){return t.removeEventListener(e,i)}))},c.then(c.unsubscribe),c}"undefined"!==typeof document&&(function(){if(!s(document.createElementNS("http://www.w3.org/2000/svg","g").classList,DOMTokenList))try{Object.defineProperty(SVGElement.prototype,"classList",Object.getOwnPropertyDescriptor(HTMLElement.prototype,"classList")||Object.getOwnPropertyDescriptor(Element.prototype,"classList"))}catch(t){}}(),function(){try{var t=document.createElement("div");if(t.classList.add("foo","bar"),t.classList.contains("bar"))return;var e=DOMTokenList.prototype.add,n=DOMTokenList.prototype.remove;DOMTokenList.prototype.add=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var i=this;n.forEach((function(t){return e.call(i,t)}))},DOMTokenList.prototype.remove=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var i=this;e.forEach((function(t){return n.call(i,t)}))}}catch(r){}}()),"undefined"!==typeof NodeList&&(NodeList.prototype.forEach||(NodeList.prototype.forEach=Array.prototype.forEach));var u={stringToHTML:function(t){var e=document.createElement("div");return e.innerHTML=t,e.childNodes},arrayFromNodeList:function(t){return[].slice.call(t)},withNodeList:function(t,e){return"string"===typeof t&&(t=this.stringToHTML(t)),s(t,HTMLElement)&&(t=[t]),(s(t,NodeList)||Array.isArray(t))&&Array.prototype.slice.call(t).forEach((function(t){return e(t)})),this},append:function(t,e){"undefined"===typeof e&&(e=t,t=document.body);return this.withNodeList(e,(function(e){return t.appendChild(e)}))},prepend:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;"undefined"===typeof e&&(e=t,t=document.body);var r=t.children[n],i=function(e){return t.insertBefore(e,r)};return this.withNodeList(e,i)},remove:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=[];return e.forEach((function(t){s(t,Element)&&t.parentNode&&r.push(t.parentNode.removeChild(t))})),r},find:function(t,e){return Array.prototype.slice.call(t.querySelectorAll(e))},findOne:function(t,e){return t.querySelector(e)},closest:function(t,e){return(0,a.Z)(t,e,!0)},addClass:function(t,e){Array.isArray(t)||(t=[t]);var n=e.split(" ");t.forEach((function(t){return n.forEach((function(e){return t.classList.add(e)}))}))},removeClass:function(t,e){Array.isArray(t)||(t=[t]);var n=e.split(" ");t.forEach((function(t){return n.forEach((function(e){return t.classList.remove(e)}))}))},hasClass:function(t,e){return Array.isArray(t)||(t=[t]),t.some((function(t){return t.classList.contains(e)}))},toggleClass:function(t,e){Array.isArray(t)||(t=[t]),"string"===typeof t&&(t=this.find(t)),e.split(" ").forEach((function(e){return t.forEach((function(t){return t.classList.toggle(e)}))}))},matches:function(){if("undefined"===typeof Element)return function(){return!1};var t=Element.prototype,e=t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.msMatchesSelector||function(t){return[].indexOf.call(document.querySelectorAll(t),this)>-1};return function(t,n){return e.call(t,n)}}(),getViewPosition:function(t,e,n){var r=t.getBoundingClientRect(),i=o();n=n||0;var a=0-(e=e||0),s=i+e,c={top:r.top-n,bottom:r.bottom-n};return{isHidden:function(){return!t.offsetParent},isWithinViewport:function(){return c.top>=0&&c.bottom<=i},isWithinLookahead:function(){return c.top>=a&&c.bottom<=s},isPartiallyInViewport:function(){return c.top>=0&&c.top<=i||c.bottom>0&&c.bottom<i},isPartiallyInLookahead:function(){return c.top>0?c.top<e+i:c.bottom>0||Math.abs(c.bottom)<e},isNearBottom:function(){return parseInt(r.bottom,10)<=e}}},on:function(t,e,n,r){"function"===typeof n&&"undefined"===typeof r&&(r=n,n=null),e.split(" ").forEach((function(e){return t.addEventListener(e,r)}))},one:function(t,e,n,r){var i=this;"function"===typeof n&&(r=n,n=null);var o=function(n){r.call(this,n),i.off(t,e,o)};return i.on(t,e,o),o},off:function(t,e,n){e.split(" ").forEach((function(e){return t.removeEventListener(e,n)}))},trigger:function(t,e){document.createEvent&&e.split(" ").forEach((function(e){var n=document.createEvent("Event");n.initEvent&&(n.initEvent(e,!1,!0),t.dispatchEvent(n))}))},hide:function(t,e){var n=e?this.find(t,e):t;return this.addClass(n,"js-hidden"),this},show:function(t,e){var n=e?this.find(t,e):t;if(this.hasClass(n,"js-hidden"))this.removeClass(n,"js-hidden");else{var r=this;Array.isArray(n)||(n=[n]),n.forEach((function(t){return r.setStyle(t,{display:"block"})}))}return this},getData:function(t,e){return"string"===typeof t&&(t=this.stringToHTML(t)[0]),t.getAttribute("data-".concat(e))},setData:function(t,e,n){return"string"===typeof t&&(t=this.stringToHTML(t)[0]),t.setAttribute("data-".concat(e),n),!0},getStyle:function(t,e){return t.style[e]},setStyle:function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t.style[n]=e[n]);return this},getAttr:function(t,e){return t.getAttribute(e)},setAttr:function(t,e){for(var n in e)e.hasOwnProperty(n)&&t.setAttribute(n,e[n]);return this},removeAttr:function(t,e){return t.removeAttribute(e),this},hasAttr:function(t,e){return t.hasAttribute(e)},getText:function(t,e){var n=e?this.findOne(t,e):t;return n?n.textContent:""},setText:function(t,e,n){var r=n?this.findOne(t,n):t;return r&&(r.textContent=e),this},setHTML:function(t,e,n){var r=n?this.findOne(t,n):t;return r&&(r.innerHTML=e),this},parent:function(t,e){return(0,a.Z)(t,e)},getModuleConfig:function(t,e){var n=t.querySelector("script");return e?n:n?JSON.parse(n.text):null},createElement:function(t,e){var n=document.createElement(t);return this.setAttr(n,e),n},scrollIntoView:function(t,e){var n=this;e?setTimeout((function(){n.scrollIntoView(t)}),e):t.scrollIntoView?t.scrollIntoView():window.scrollTo(0,t.offsetTop)},offset:function(t,e){var n=e?this.findOne(t,e):t;return n?{top:n.offsetTop,left:n.offsetLeft}:{}},size:function(t){return{width:t.offsetWidth,height:t.offsetHeight}},detectTransitionEnd:c}},42252:function(t,e,n){"use strict";n.d(e,{ME:function(){return f}});var r=n(76635),i=n(18215),o=n(46658);function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],a=!0,s=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(c){s=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var c=function(){try{return window.BZFD.Context.page.localization}catch(t){return{}}},u=function(){return c().translations||{}},l={de:["de"],en:["au","ca","in","uk","us"],es:["es","mx"],fr:["fr"],ja:["jp"],pt:["br"]};function f(t){return u()[t]||""}e.ZP={getEdition:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={useCountryCookie:!0};(0,r.isString)(t)&&(t="feedpager"===t?{useCountryCookie:!1}:{}),t=Object.assign({},e,t);var n=c().country,o=s(n.split("-"),2),a=o[0],u=o[1],f=void 0===u?"":u;if("en"===c().language&&t.useCountryCookie){var d,h=i.Z.get("country");h||(h=f||"us"),d=s(h.split("-"),2),a=d[0],(f=d[1])||"es"===a||(f=a,a=Object.keys(l).filter((function(t){return-1!==l[t].indexOf(a)}))[0])}return l[a]&&-1!==l[a].indexOf(f)?"".concat(a,"-").concat(f):"es"===a?"es":"en-us"},getRawPageLanguage:function(){return-1!==Object.keys(l).indexOf(c().language)?c().language:"en"},getPageLocale:function(){return c().locale},getUserCountry:function(){return i.Z.get("country")||"us"},getUserGeoCountry:function(){return i.Z.get("bf-geo-country")||"US"},getTranslation:function(t,e,n){if(!u()[t])throw new Error("Missing translation "+t+" for "+this.getRawPageLanguage());var r=n?{delimiters:n}:{};return o.Z.renderString(u()[t],e,r)},getTranslationStr:f,getDateFormatTemplate:function(){switch(c().language){case"de":return"{{D}}. {{MM}} {{Y}}, {{H}}:{{m}} Uhr";case"es":case"pt":return"{{D}} de {{MM}} de {{Y}}, {{h}}:{{m}} {{a}}";case"fr":return"{{D}} {{MM}}, {{Y}} &agrave; {{H}} h {{m}}";case"ja":return"{{Y}}/{{M}}/{{DD}} {{H}}:{{m}}";default:return"{{MM}} {{D}}, {{Y}}, at {{h}}:{{m}} {{a}}"}}}},37083:function(t,e,n){"use strict";n.d(e,{Z:function(){return o}});var r=n(42252);function i(t){return t.env?(t.env.addFilter("tojson",t.env.filters.dump),t.env.addFilter("prependClickTracker",(function(t,e){return function(t,e){return!t||t.length<0?e:t+encodeURIComponent(e)}(e,t)})),t.env.addFilter("l10n",(function(t,e){return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";try{var n=(0,r.ME)(t);return n.length<=0?e:n}catch(i){console.error("error fetching translation: "+i)}return e}(e,t)})),t.env.addFilter("regex_match",(function(t,e){return t.match(new RegExp(e))})),t):t}function o(t){return i(t),function(t){t.env.addGlobal("i18n",window.BZFD.Context.page.localization.translations),t.env.addGlobal("utils",{transGif1x1Base64:"data:image/gif;base64,R0lGODlhAQABAPAAAAAAAAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==",transGif3x2Base64:"data:image/gif;base64,R0lGODlhAwACAPAAAAAAAAAAACH5BAEAAAAALAAAAAADAAIAAAIChF8AOw=="}),t.env.addGlobal("config",window.BZFD.Config)}(t),t}},46658:function(t,e,n){"use strict";var r=n(50112),i=n.n(r);e.Z={renderString:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return i().compile(t,n).render(e)}}},49277:function(t,e,n){"use strict";n.d(e,{Z:function(){return f}});var r=n(94776),i=n.n(r),o=n(2784),a=n(75951);function s(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(u){return void n(u)}s.done?e(c):Promise.resolve(c).then(r,i)}function c(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function a(t){s(o,r,i,a,c,"next",t)}function c(t){s(o,r,i,a,c,"throw",t)}a(void 0)}))}}var u=a.ZP.framework,l=a.ZP.rules;function f(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"tracking",e=u.needsConsent(),n=(0,o.useState)(!e||u.isConsentStringCookieSet()),r=n[0],a=n[1],s=(0,o.useState)(!e),f=s[0],d=s[1],h=(0,o.useState)(!e),m=h[0],p=h[1];return(0,o.useEffect)((function(){var n=function(){var e=c(i().mark((function e(){var n;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,l.hasConsented(t);case 2:n=e.sent,p(n),a(!0),d(!0);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();e&&n()}),[t,e]),{consentValue:m,isConsentKnown:r,isConsentReady:f}}},98286:function(t,e,n){"use strict";n.d(e,{T:function(){return r},n:function(){return i}});var r=["\ud83d\udc4d","\u2764\ufe0f","\ud83d\ude02","\ud83d\ude2d","\ud83e\udd2f"],i=["\ud83d\ude21","\ud83d\ude44","\ud83d\ude2c"]},45847:function(t,e,n){t.exports=n(20747)},26893:function(t,e,n){"use strict";function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}n.d(e,{Z:function(){return r}})}}]);
//# sourceMappingURL=407-bbbaf25b258e070e.js.map