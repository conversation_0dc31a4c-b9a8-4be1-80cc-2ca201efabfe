(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[542],{71031:function(t,e,n){"use strict";var i=n(52322),r=n(2784),u=n(13980),a=n.n(u),o=n(89966),c=n.n(o),s=n(56828),M=n.n(s),l=n(41332),j=n(46811),N=n(22813),g=function(t){var e=t.className,n=void 0===e?"":e,u=t.isFloatingShareButtonEnd,a=(0,r.useState)(!1),o=a[0],s=a[1],g=(0,r.useState)(!1),d=g[0],y=g[1],L=(0,r.useState)(!1),T=L[0],z=L[1],m=(0,r.useState)(!1),p=m[0],I=m[1],f=(0,r.useState)(!1),D=f[0],x=f[1],b=(0,r.useState)(null),O=b[0],A=b[1],h=(0,r.useState)(0),_=h[0],S=h[1],w=(0,r.useContext)(l.Z).id,E=(0,r.useRef)(null);return(0,r.useEffect)((function(){(0,j.Hd)()&&s(!0)}),[]),(0,r.useEffect)((function(){y(!T&&p&&o&&!D)}),[T,p,D]),(0,r.useEffect)((function(){var t=window;if(T||z(!0),!p){var e=t.scrollTop>20||t.scrollY>20||t.pageYOffset>20;I(e)}if(O){var n=t.scrollTop||t.scrollY||t.pageYOffset;x(n-O>50)}M()((function(){clearTimeout(E.current),E.current=setTimeout((function(){z(!1)}),250)}),60)()}),[_]),(0,r.useEffect)((function(){var t=window,e=function(){return S(Date.now())};return t.addEventListener("scroll",e),function(){t.removeEventListener("scroll",e)}}),[]),(0,r.useEffect)((function(){if(u){var t=window,e=t.scrollTop||t.scrollY||t.pageYOffset;A(e)}}),[u]),(0,i.jsx)("div",{className:"".concat(c().floatingShareButton," ").concat(n," \n      ").concat(d?c().visible:"","\n      ").concat(p?"":c().inactive),children:(0,i.jsx)(N.Z,{type:"page_level",variant:"icon_only",includeShortTitle:!0,trackingData:{unit_type:"modal",unit_name:w,subunit_type:"component",subunit_name:"floating_share"}})})};g.propTypes={className:a().string,isFloatingShareButtonEnd:a().bool},e.Z=g},83661:function(t,e,n){"use strict";n.d(e,{Z:function(){return it}});var i={};n.r(i),n.d(i,{getComponent:function(){return G},getMaxSizes:function(){return F},getTemplate:function(){return W},getWireframeOptions:function(){return H},isInline:function(){return B},isWideInline:function(){return R}});var r=n(52322),u=n(2784),a=n(13980),o=n.n(a),c=n(35167),s=n(14007),M=n(6294),l=n(93002),j=n(89809),N=n(93176),g=n(21576),d=n(82984),y=n(50320),L=n(17481),T=n(3873),z=n(95466),m=n(21764),p=n(65104),I=n(80226),f=n(65211),D=n(71049),x=n(22606),b=n(44399),O=n(54442),A=n(10918),h=n(66892),_=n(28403),S=n(53205),w=n(43517),E=n(27363),v=n(9895),C=n(12239);n(98254);N.lT.prototype.getEventId=function(){return"".concat(this.config.adPos,"-").concat(this.instanceId)};var k={AdAffiliatePixel:g.Z,AdAwareness:d.Z,AdBigstory:y.Z,AdBpageStory:L.Z,AdComment:T.Z,AdExBase:z.u,AdEx:z.S,AdExGrid:m.Z,AdGridStory:p.Z,AdPromoInline:I.Z,AdSubbuzz:f.Z,AdToolbar:D.Z},Q={AdAffiliatePixel:x.Z,AdAwareness:b.Z,AdBigstory:O.Z,AdBpageStory:A.Z,AdComment:h.Z,AdEx:_.Z,AdExGrid:S.Z,AdGridStory:w.Z,AdPromoInline:E.Z,AdSubbuzz:v.Z,AdToolbar:C.Z};function U(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function Y(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var i,r,u=[],a=!0,o=!1;try{for(n=n.call(t);!(a=(i=n.next()).done)&&(u.push(i.value),!e||u.length!==e);a=!0);}catch(c){o=!0,r=c}finally{try{a||null==n.return||n.return()}finally{if(o)throw r}}return u}}(t,e)||Z(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function P(t){return function(t){if(Array.isArray(t))return U(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||Z(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Z(t,e){if(t){if("string"===typeof t)return U(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?U(t,e):void 0}}function B(t){var e=t.adPos;return/^promo-inline/.test(e)}function R(t){var e=t.adPos;return/^promo\d*-wide/.test(e)}function W(t){var e=t.adPos;t.size;return/^awareness/.test(e)?Q.AdAwareness:/^(sidebar|bigstory)/.test(e)?Q.AdBigstory:B(t)?Q.AdPromoInline:R(t)||"aiquizzes"===e?Q.AdEx:"story-bpage"===e?t.isMobile?Q.AdGridStory:Q.AdBpageStory:/^promo-bottom/.test(e)?Q.AdExGrid:/^subbuzz/.test(e)?Q.AdSubbuzz:/pixel$/.test(e)?Q.AdAffiliatePixel:/tb$/.test(e)?Q.AdToolbar:/^comment/.test(e)?Q.AdComment:void 0}function G(t){var e=t.adPos;return/^awareness/.test(e)?k.AdAwareness:/^(sidebar|bigstory)/.test(e)?k.AdBigstory:B(t)?k.AdPromoInline:"story-bpage"===e?t.isMobile?k.AdGridStory:k.AdBpageStory:/^promo-bottom/.test(e)?k.AdExGrid:/^subbuzz/.test(e)?k.AdSubbuzz:/pixel$/.test(e)?k.AdAffiliatePixel:/tb/.test(e)?k.AdToolbar:/^comment/.test(e)?k.AdComment:k.AdEx}function F(t){var e,n,i=j.Z.getProgrammatic(c.PP,t.size),r=(e=Math).max.apply(e,[0].concat(P(i.map((function(t){return Y(t,2)[1]})))));return[(n=Math).max.apply(n,[0].concat(P(i.map((function(t){return Y(t,1)[0]}))))),r]}function H(t){if(!B(t)&&!R(t))return{};var e=F(t);return{programmaticWireframes:!0,wireframeWidth:e[0],wireframeHeight:e[1],wireframeClasses:function(t){return"".concat(t," ad-wireframe-wrapper--inline")}}}var J=n(41332),V=n(36001),q=n(81550),K=n(24678),X=n(3695),$=n(32966),tt=n(9772);function et(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function nt(t){var e,n=t.type,a=t.config,o=t.mode,j=t.onAdViewed,N=t.onAdRender,g=t.buzzFormat,d=void 0===g?"standard":g,y=t.className,L=void 0===y?"":y,T=t.pixiedust,z=(0,u.useContext)(J.Z).buzz,m=void 0===z?{}:z,p=(0,u.useContext)(V.Z),I=(0,u.useState)("loading"),f=I[0],D=I[1],x=!(0,l.s)()&&(0,M.tq)()?"x0.5":"x0.25",b=(0,X.G)(m),O=(0,u.useContext)($.Z).getFeatureFlagValue,A=(0,u.useContext)(tt.$),h=null===A||void 0===A||null===(e=A.env)||void 0===e?void 0:e.userCountry,_="au"===h||"nz"===h,S=O("ads_destroy_ads_on_scroll_mobile")||!1,w=(0,u.useMemo)((function(){var t=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){et(t,e,n[e])}))}return t}({},a||c.vc[n]);return b&&(t.zone1="bfnews"),"renderLookahead"in t||(t.renderLookahead=x),t}),[a,n,x,b]),E=(0,u.useCallback)((function(t){t.type&&D("empty"===t.type?"unfilled":"loaded"),"function"===typeof N&&N(t)}),[w,N,m.id]);return(0,u.useEffect)((function(){D("loading")}),[w,m.id]),(0,r.jsx)(s.Z,{adUnitUtils:i,config:w,pageId:m.id,mode:o,onAdViewed:j,onAdRender:E,stickyManager:p,isWidePage:"wide"===d,className:"".concat(L," Ad--").concat(m.isAd?"partner":"edit"," Ad--").concat(f),pixiedust:T,destroyAdsOnScroll:!_&&S})}nt.propTypes={type:o().string,config:o().object,mode:o().oneOf(["active","preload"]),onAdViewed:o().func,onAdRender:o().func,buzzFormat:o().string,buzzLanguage:o().string,className:o().string,pixiedust:o().object};var it=(0,q.Z)(nt,{onError:K.Tb})},10097:function(t,e,n){"use strict";n.d(e,{Z:function(){return G}});var i=n(52322),r=n(2784),u=n(12524),a=n.n(u),o=n(30353),c=n(49101),s=n(99112),M=function(t){return t.tags.includes("--add-yours")},l=n(27374),j=n(3695),N=n(36491),g=n(13980),d=n.n(g),y=n(90253),L=n.n(y),T=n(4366);function z(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function m(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){z(t,e,n[e])}))}return t}function p(t,e){if(null==t)return{};var n,i,r=function(t,e){if(null==t)return{};var n,i,r={},u=Object.keys(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var I=n(70028),f=n(28316),D=n(32966),x=n(99404),b=n(35167);function O(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function A(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function h(t){return h=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},h(t)}function _(t,e){return!e||"object"!==w(e)&&"function"!==typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function S(t,e){return S=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},S(t,e)}var w=function(t){return t&&"undefined"!==typeof Symbol&&t.constructor===Symbol?"symbol":typeof t};function E(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,i=h(t);if(e){var r=h(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return _(this,n)}}var v=function(t){!function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&S(t,e)}(a,t);var e,n,r,u=E(a);function a(t){var e,n=t.placeholders,i=t.onAdsReady;return O(this,a),(e=u.call.apply(u,[this].concat(Array.prototype.slice.call(arguments)))).config.injectMethod="inside",e.placeholders=n,e.placeholders=e.placeholders.filter((function(t,e){return(e+1)%5===0})),"function"===typeof i&&e.privateEvents.on("ads-ready",i),e}return e=a,n=[{key:"configure",value:function(){var t=this;this.placements=[1],this.density=1;var e=Array.prototype.map.call(this.placeholders,(function(e){var n=t.config.AdUnit,r=t.getNextAd(),u=t.language,a=t.pixiedust;return function(){return f.createPortal((0,i.jsx)(n,{config:r.slot,buzzLanguage:u,pixiedust:a,className:"Ad--comment"}),e)}}));this.privateEvents.trigger("ads-ready",e)}}],n&&A(e.prototype,n),r&&A(e,r),a}(x.ZP);var C=n(83661);function k(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function Q(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function U(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Q(t,e,n[e])}))}return t}function Y(t){return function(t){if(Array.isArray(t))return k(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return k(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return k(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var P=function(t){return function(e){var n=e.buzz,u=(0,r.useContext)(D.Z),a=u.experiments,o=(0,u.getExperimentValue)("ads_in_comments",{rejectErrors:!1}),c=(0,r.useRef)(null),s=(0,r.useState)(null),M=s[0],l=s[1],j=(0,r.useState)(null),N=j[0],g=j[1],d=(0,r.useState)([]),y=d[0],L=d[1],T=(0,r.useRef)(null);return(0,r.useEffect)((function(){return"on"!==o?function(){}:M?(c.current&&c.current.destroy(),L([]),c.current=function(t){var e=t.AdUnit,n=t.element,i=t.onAdsReady,r=t.pixiedust,u=t.placeholders,a=t.language,o=(0,b.kE)(9,2).map((function(t){return b.vc["comment".concat(t)]})),c=[b.vc["comment-infinite"]];return new v({config:{AdUnit:e,units:o,unitsRepeated:c},element:n,onAdsReady:i,pixiedust:r,placeholders:u,language:a})}({AdUnit:C.Z,element:T.current,onAdsReady:function(t){L(t)},pixiedust:{},placeholders:M,language:n.language}),c.current.init(),function(){c.current&&c.current.destroy()}):function(){}}),[M,a.loaded]),(0,i.jsxs)("div",{ref:T,children:["on"===o&&N&&f.createPortal((0,i.jsx)(C.Z,{config:b.vc.comment1,buzzLanguage:n.language,pixiedust:{},className:"Ad--comment"}),N),y.map((function(t,e){return(0,i.jsx)(t,{},e)})),(0,i.jsx)(t,U({},e,{onAdPlaceholderRefsReady:function(t){(!M||t.length!==M.length||t.some((function(t,e){return!t.isSameNode(M[e])})))&&l(Y(t))},onFormAdPlaceholderRefReady:function(t){g(t)}}))]})}};var Z=(0,N.Z)((function(){return Promise.all([n.e(470),n.e(65),n.e(324)]).then(n.bind(n,26065))}),{ssr:!1}),B=(0,N.Z)((function(){return n.e(581).then(n.bind(n,67434))}),{ssr:!1}),R=(0,N.Z)((function(){return Promise.all([n.e(553),n.e(220)]).then(n.bind(n,17910))}),{ssr:!1});function W(t){var e,n,u,N=t.buzz,g=t.commentCount,d=t.firstPage,y=t.repliesFirstPage,z=t.perPage,f=t.onAdPlaceholderRefsReady,D=t.onFormAdPlaceholderRefReady,x=t.onCommentsLoaded,b=(0,r.useContext)(s.Z),O=b.base_url,A=b.destination,h=(0,r.useContext)(c.Z),_=h.isCommentsPanelOpen,S=h.toggleCommentsPanel,w=(0,r.useState)({ctaCommentText:"",syncCtaToSidebar:!1,syncSidebarToCta:!1}),E=w[0],v=w[1],C=!(0,l._)(N)&&!N.isCommentsPage&&"Japan"!==N.classification.edition,k=o.destinations.buzzfeed.base_url,Q=function(t){var e=(0,r.useContext)(s.Z).destination;return(0,r.useMemo)((function(){return{internalLink:function(e){e.type&&"login"===e.type&&(0,T.TW)(t,{item_name:"sign_in_to_comment",item_type:"text",subunit_name:"comments",subunit_type:"component",unit_name:t.id,unit_type:"buzz_bottom",target_content_type:"auth",target_content_id:"sign_in"}),e.type&&"comment"===e.type&&(0,T.TW)(t,{item_name:e.itemName||"",item_type:e.itemType||"comment",subunit_name:"comments",subunit_type:"component",unit_name:t.id,unit_type:"buzz_bottom",target_content_type:e.targetContentType||"url",target_content_id:e.targetContentId||""})},externalLink:function(e){var n=e||{},i=n.type,r=p(n,["type"]);"signin_modal"!==i||(0,T.nz)(t,r)},contentAction:function(e){if(t&&t.id){var n=e||{},i=n.type,r=p(n,["type"]);if("signin_modal"!==i){var u="comment",a=String(e.comment_id)||null,o="comment",c="submission";e.parent_id?(u="reply",o=String(e.parent_id),c="comment"):"image"===e.form&&(u="upload",a=e.image_url,o="image"),"reaction"===e.type&&(u="react",a=e.form),(0,T.bC)(t,{action_type:e.actionType?e.actionType:u,action_value:e.actionValue?e.actionValue:a,item_name:e.itemName?e.itemName:o,item_type:e.itemType?e.itemType:c,subunit_name:e.subunitName?e.subunitName:"comments",subunit_type:"component",unit_name:t.id,unit_type:"buzz_bottom"})}else(0,T.bC)(t,m({unit_name:t.id,unit_type:"buzz_bottom",subunit_name:"comments",subunit_type:"component",item_name:"sign_in_to_comment",item_type:"text"},r))}},bylineLink:function(e){var n=e.type,i=e.id,r=e.link;if(n&&"byline"===n){var u={item_name:"byline",item_type:"profile",subunit_name:"comments",subunit_type:"component",unit_name:t.id,unit_type:"buzz_bottom"};(0,j.G)(t)?(0,T.nz)(t,m({},u,{target_content_url:r})):(0,T.TW)(t,m({},u,{target_content_type:"user",target_content_id:i}))}},impressionUnit:function(e){var n=e.element;"loadMore"===e.type&&(0,j.G)(t)&&(0,T.aF)(n,t,{unit_name:t.id,unit_type:"buzz_bottom",subunit_name:"comments",subunit_type:"component",item_type:"button",item_name:"load_more",target_content_type:"buzz",target_content_id:t.id})}}}),[t,e])}(N),U=(0,I.i)(N,{unit_type:"buzz_bottom",subunit_name:"comments_cta_section"}),Y=(0,j.G)(N)?"".concat(O,"/auth/sso/signin?redirect=").concat(N.canonical_url):void 0,P=N.language||"en",W=(0,r.useCallback)((function(){S(!1)}),[S]),G=(0,r.useCallback)((function(){return S(!0)}),[S]),F="dev"===o.CLUSTER?k:O,H=(0,i.jsx)(Z,{apiRoot:F,profileRoot:k,imageRoot:o.image_service_url,buzzId:+N.id,firstPage:d,repliesFirstPage:y,perPage:z,track:Q,locale:P,loginRedirectUrl:Y,onLoaded:x,allowImages:M(N),className:a()({themeNews:(0,j.G)(N),condensedComments:C,discussionPage:(0,l._)(N)}),showCloseButton:C,onAdPlaceholderRefsReady:f,onFormAdPlaceholderRefReady:D,onCloseButtonClick:W,onOpenComments:G,shouldSortByDate:N.tags.includes("--sort-comments-latest"),ctaState:E});return(0,i.jsxs)("div",{className:a()(L().commentsWrapper,(e={},n=L().condensedComments,u=C&&!(0,j.G)(N),n in e?Object.defineProperty(e,n,{value:u,enumerable:!0,configurable:!0,writable:!0}):e[n]=u,e)),children:[!C&&(0,i.jsx)("div",{id:"comments",className:L().comments,children:H}),C&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(B,{buzz:N,profileRoot:k,commentCount:g,commentsApiRoot:F,locale:P,destination:A,ctaState:E,setCtaState:v,onOpenComments:G,toggleCommentsPanel:S,isCommentsPanelOpen:_,commentsBottomCtaButtonTrackingFn:U,isNews:(0,j.G)(N),track:Q}),(0,i.jsx)(R,{buzz:N,isCommentsPanelOpen:_,children:H})]})]})}W.propTypes={buzz:d().object,firstPage:d().number,repliesFirstPage:d().number,perPage:d().number};var G=P(W)},51340:function(t,e,n){"use strict";n.d(e,{Z:function(){return y}});var i=n(52322),r=n(2784),u=n(93002),a="1bf35e8b-3d55-48a6-aef8-0ebfdd277df7",o=n(9772),c=n(18977),s=n(71520),M=n(49101),l=n(3695),j=n(14125);var N=n(29014),g=n.n(N),d=n(21038);function y(t){var e,n=t.buzz,N=void 0===n?{}:n,y=t.showOnTopQuiz,L=void 0!==y&&y,T=(0,r.useState)(null),z=T[0],m=T[1],p=(0,r.useContext)(o.$),I=(0,r.useState)(!1),f=I[0],D=I[1],x=(0,r.useState)(null),b=x[0],O=x[1],A=(0,r.useState)(null),h=A[0],_=A[1],S=(0,r.useRef)(null),w=(0,r.useRef)(!1),E=(0,j.yR)(N)||!(0,j.rR)(N)&&L,v=(0,r.useContext)(M.Z).isCommentsPanelOpen,C=null===p||void 0===p||null===(e=p.env)||void 0===e?void 0:e.userCountry,k=p.gdpr,Q=(0,l.G)(N)?"buzzfeed_news":"buzzfeed",U=(0,s.Q)("backfill",Q,L),Y=U.embedId,P=U.playerId,Z="player-".concat(Y);return(0,r.useEffect)((function(){d.jQ.hasConsented("connatix").then((function(t){t&&(document.querySelector('script[src*="connatix.player.js"]')||function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.head;(0,u.s)()?Promise.reject("Cannot load Connatix player on server"):new Promise((function(e,n){window.cnx&&e(window.cnx),window.cnx={},window.cnx.cmd=[];var i="//cd.connatix.com/connatix.player.js?cid=".concat(a),r=document.createElement("script");r.onload=function(){return e(window.cnx)},r.onerror=function(){n("Script at url ".concat(i," failed to load"))},r.src=i,r.async=!0,r.type="text/javascript",t.appendChild(r)}))}(),D(!0))}))}),[]),(0,r.useEffect)((function(){k&&(0,c.HU)({gdpr:k}).then((function(t){_(t)}))}),[k]),(0,r.useEffect)((function(){if(f&&E&&C&&!w.current&&h){if(w.current=!0,!z){var t="https://capi.connatix.com/tr/si?token=".concat(P,"&cid=").concat(s.k),e=new Image;e.src=t,m(e)}window.cnx.cmd.push((function(){var t={playerId:P,customParam1:h,settings:{rendering:{insertPosition:window.cnx.configEnums.InsertPositionEnum.WithinContainer}}};["uk","gb","ie"].includes(C)&&(t.playlistId=Y),window.cnx(t).render(Z,(function(t,e){t?console.error("Player rendering failed:",t):O(e)}))}))}}),[E,C,P,Z,z,h,f]),function(t,e,n){var i=(0,r.useRef)(!1);(0,r.useEffect)((function(){if(!n)return null===t||void 0===t||t.disableFloatingMode(),void(i.current=!1);if(n&&e.current&&"function"===typeof t.enableFloatingMode&&"function"===typeof t.disableFloatingMode&&"undefined"!==typeof IntersectionObserver){var r=e.current;r&&(r.style.minHeight="100px");var u=new IntersectionObserver((function(e){e.forEach((function(e){var n=e.boundingClientRect,r=window.innerHeight||document.documentElement.clientHeight;!e.isIntersecting&&n.top<0&&!i.current?(t.enableFloatingMode(),i.current=!0):e.isIntersecting&&n.bottom<=r&&i.current&&(t.disableFloatingMode(),i.current=!1)}))}),{threshold:[0,.1],rootMargin:"0px"});return r&&u.observe(r),function(){r&&u.unobserve(r)}}}),[n,t,e])}(b,S,b&&window.innerWidth<=1025&&(0,j.$0)(N)&&!v||!1),E&&C?(0,i.jsx)("div",{className:"bf-cnx-override ".concat(L?g().bfElementsWrapperQuiz:g().bfElementsWrapper),children:(0,i.jsx)("div",{id:Z,ref:S,className:"bf-cnx-player"})}):""}},52592:function(t,e,n){"use strict";var i=n(52322),r=n(2784),u=n(88163),a=n(81550),o=n(4366),c=n(42719),s=n(36001),M=n(57673),l=n(24678);e.Z=(0,a.Z)((function(t){var e=t.buzz,n=(0,r.useRef)(),a=(0,u.Z)({once:!0,threshold:1}),l=a.isIntersecting,j=a.setObservable,N=(0,r.useContext)(s.Z).observeArticleBottom;return(0,r.useEffect)((function(){if(n.current&&l){var t=n.current.getBoundingClientRect().top;(0,o.Y3)(e,{pixel_depth:parseInt((0,M.l)()+t,10),marker:"content_bottom"})}}),[e,l]),(0,r.useEffect)((function(){N(n.current)}),[N]),(0,i.jsx)("div",{ref:(0,c.Z)(n,j),style:{width:"100%",height:"1px",clear:"both"}})}),{onError:l.Tb})},63871:function(t,e,n){"use strict";n.d(e,{n:function(){return M}});var i=n(52322),r=n(99550),u=n.n(r),a=n(99112),o=n(30353),c=n(24678);function s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var M=function(t){var e=t.destination,n=t.children;c.YA("destination",e);var r=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){s(t,e,n[e])}))}return t}({destination:e,destinationHost:["buzzfeed_news","buzzfeed"].includes(e)?"buzzfeed":e},o.destinations[e]);return(0,i.jsx)(a.Z.Provider,{value:r,children:(0,i.jsx)("div",{className:u()[e],children:n})})}},28182:function(t,e,n){"use strict";n.d(e,{Z:function(){return T}});var i=n(52322),r=n(2784),u=n(57483),a=n.n(u),o=n(5409),c=n(41332),s=n(32617),M=n(5103),l=n(4366);function j(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function N(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){j(t,e,n[e])}))}return t}var g=n(64435),d=n(11839),y=n.n(d),L=n(70448);var T=(0,M.withTranslation)("common")((function(){var t=(0,r.useContext)(c.Z).buzz,e=void 0===t?{}:t,n=(0,r.useState)(!1),u=n[0],M=n[1],j=(0,r.useState)(!1),d=j[0],T=j[1];(0,r.useEffect)((function(){(0,L.zk)()&&M(!0)}),[]);var z=(0,r.useMemo)((function(){return{unit_type:"buzz_bottom",unit_name:e.id,item_name:"more_community",item_type:"button",target_content_type:"feed",target_content_id:"community_about"}}),[e.id]),m=(0,g.R)(z),p=(0,r.useMemo)((function(){return{unit_type:"buzz_bottom",unit_name:e.id,item_name:"community_footer_sign_up",item_type:"button",target_content_type:"auth",target_content_id:"sign_in"}}),[e.id]),I=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,r.useMemo)((function(){return{externalLink:function(n){(0,l.nz)(t,N({},e,n))},contentAction:function(n){(0,l.bC)(t,N({},e,n))}}}),[t,e])}(e,p),f=[s.MODERATED_COMMUNITY,s.UNMODERATED_COMMUNITY].includes(e.editorial_status),D=e.isEnglish&&(e.isQuiz||f);return(0,i.jsx)(i.Fragment,{children:D&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:a().footerCommunityBanner,children:[(0,i.jsxs)("div",{className:a().flexContainer,children:[(0,i.jsxs)("div",{className:a().flexContainerTitle,children:["Want to get your very own quizzes and posts featured on BuzzFeed\u2019s homepage and app?",(0,i.jsx)("br",{}),(0,i.jsx)("br",{}),"Become a Community Contributor."]}),(0,i.jsx)("div",{className:a().flexContainerImgWrapper,children:(0,i.jsx)("img",{className:a().flexContainerImg,src:y(),alt:"promo"})})]}),(0,i.jsxs)("div",{className:a().buttonsContainer,children:[!u&&(0,i.jsx)("a",{className:a().loginBtn,role:"button",onClick:function(){return T(!0)},children:(0,i.jsx)("p",{children:"Sign up to get started"})}),(0,i.jsx)("a",{className:u?a().learnMoreBtn:a().promoLink,target:"_blank",href:"https://www.buzzfeed.com/community/about",rel:"noreferrer",ref:m,children:"Learn more about Community"})]})]}),(0,i.jsx)(o.f,{isOpen:d,onClose:function(){return T(!1)},track:I})]})})}))},70211:function(t,e,n){"use strict";n.d(e,{Z:function(){return b}});var i=n(52322),r=n(2784),u=n(13980),a=n.n(u),o=n(39252),c=n(64435),s=n(41332),M=n(36491),l=n(30729),j=n(76139),N=n.n(j),g=function(t){var e=t.dateline,n="https://img.buzzfeed.com/buzzfeed-static".concat(e.dateline_image);return(0,i.jsxs)("div",{className:N().dateline,children:[(0,i.jsx)(o.lS,{src:(0,l.Z)(n),className:N().dateline_map,alt:"Map of ".concat(e.name)}),(0,i.jsxs)("div",{className:"xs-flex-grow-1",children:[(0,i.jsx)("p",{className:N().datelineTitle,children:"Reporting From"}),(0,i.jsx)("strong",{className:N().datelineName,children:e.name})]})]})};g.propTypes={dateline:a().shape({name:a().string.isRequired,dateline_image:a().string.isRequired})};var d=g,y=n(50381),L=n.n(y),T=n(3695),z=n(27374),m=(0,M.Z)((function(){return n.e(33).then(n.bind(n,67740))}),{ssr:!1});function p(t){var e=t.byline,n=t.advertiserClass,u=t.index,a=t.isSingle,M=void 0!==a&&a,j=((0,r.useContext)(s.Z)||{}).buzz,N=void 0===j?{}:j,g="1"===e.description_id,d=(0,T.G)(N)?"/author/".concat(e.username):"/".concat(e.username),y=(0,r.useMemo)((function(){return{eventCategory:"Header",eventAction:"link",eventLabel:d,unit_type:"buzz_head",unit_name:N.id,item_name:"byline",item_type:"profile",position_in_unit:u,target_content_type:"user",target_content_id:e.id}}),[N,e.id,d,u]),z=(0,c.R)(y);return e?(0,i.jsxs)("div",{className:"".concat((0,T.G)(N)?L().newsByline:""," ").concat((0,T.G)(N)&&M?L().singleByline:""),children:[(0,i.jsx)("a",{ref:z,href:d,className:L().headlineByline,children:(0,i.jsxs)("div",{className:L().bylineContainer,children:[(0,i.jsx)(o.lS,{src:(0,l.Z)(e.avatar),className:"".concat(L().avatar," ").concat(n," embed-byline-avatar"),alt:e.display_name}),(0,i.jsxs)("div",{className:"".concat(L().bylineText," embed-byline-text"),children:[(0,i.jsx)("span",{className:"".concat(L().text," ").concat(n),children:"by "}),(0,i.jsx)("span",{className:"metadata-link ".concat(L().bylineName),dangerouslySetInnerHTML:{__html:e.display_name}}),(0,i.jsx)("p",{className:L().position,children:e.title})]})]})}),g&&(0,i.jsx)("div",{className:L().internetPointsContainer,children:(0,i.jsx)("div",{className:L().internetPointsContainerInner,children:(0,i.jsx)(m,{className:L().desktopShow,userId:e.id})})})]}):null}function I(t){var e=t.byline,n=t.advertiserClass,u=t.isLastByline,a=t.index,M=((0,r.useContext)(s.Z)||{}).buzz,j=void 0===M?{}:M,N=(0,r.useMemo)((function(){return{eventCategory:"Header",eventAction:"link",eventLabel:"/".concat(e.username),unit_type:"buzz_head",unit_name:j.id,item_name:"byline",item_type:"profile",position_in_unit:a,target_content_type:"user",target_content_id:e.id}}),[j,e.id,e.username,a]),g=(0,c.R)(N);return(0,i.jsxs)("a",{ref:g,href:"/".concat(e.username),className:"".concat(L().headlineByline," ").concat(L().twoAuthors),children:[(0,i.jsxs)("div",{className:L().bylineContainer,children:[(0,i.jsx)(o.lS,{src:(0,l.Z)(e.avatar),className:"".concat(L().avatar," ").concat(L().desktopShow," ").concat(n),alt:e.display_name}),(0,i.jsxs)("div",{className:L().bylineText,children:[(0,i.jsxs)("span",{className:"".concat(L().desktopShow," ").concat(L().text," ").concat(n),children:["by"," "]}),(0,i.jsx)("span",{className:"metadata-link ".concat(L().bylineName),dangerouslySetInnerHTML:{__html:e.display_name}}),(0,i.jsx)("p",{className:L().desktopShow,children:e.title})]})]}),!u&&(0,i.jsx)("span",{className:L().desktopHide,children:", "})]})}function f(){var t=((0,r.useContext)(s.Z)||{}).buzz,e=void 0===t?{}:t,n=(0,r.useMemo)((function(){return{eventCategory:"Header",eventAction:"link",eventLabel:"/community/about",unit_type:"buzz_head",unit_name:e.id,item_name:"byline",item_type:"profile",position_in_unit:1,target_content_type:"feed",target_content_id:"community_about"}}),[e.id]),u=(0,c.R)(n);return(0,i.jsx)("a",{ref:u,href:"/community/about",className:"".concat(L().headlineByline," ").concat(L().communityTeamByline),children:(0,i.jsxs)("div",{className:L().bylineContainer,children:[(0,i.jsx)(o.lS,{src:(0,l.Z)("https://img.buzzfeed.com/store-an-image-prod-us-east-1/p9aVli648.png"),className:"".concat(L().avatar," ").concat(L().desktopShow),alt:"BuzzFeed Community Team"}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("span",{className:"".concat(L().text," ").concat(L().communityTeamBy),children:["Approved and edited by"," "]}),(0,i.jsx)("span",{className:"metadata-link ".concat(L().bylineName),children:"BuzzFeed Community Team"})]})]})})}function D(t){var e=t.byline,n=t.className,u=void 0===n?"":n,a=t.isLastByline,o=t.index,M=((0,r.useContext)(s.Z)||{}).buzz,l=void 0===M?{}:M,j=(0,r.useMemo)((function(){return{eventCategory:"Header",eventAction:"link",eventLabel:"/".concat(e.username),unit_type:"buzz_head",unit_name:l.id,item_name:"byline",item_type:"profile",position_in_unit:o,target_content_type:"user",target_content_id:e.id}}),[l,e.id,e.username,o]),N=(0,c.R)(j);return(0,i.jsxs)("span",{className:"".concat(L().textByline," ").concat(u),children:[(0,i.jsx)("a",{className:L().headlineByline,ref:N,href:"/".concat(e.username),children:(0,i.jsx)("span",{className:"metadata-link ".concat(L().bylineName),dangerouslySetInnerHTML:{__html:e.display_name}})}),!a&&", "]})}function x(t){var e=t.bylines,n=void 0===e?[]:e,r=t.isAd,u=void 0!==r&&r,a=t.buzz,o=void 0===a?{}:a,c=u?L().isAdvertiser:"",s=o.isCommunity&&o.isModerated,M=o.datelines;if((0,z._)(o)){if("discuss"===(null===o||void 0===o?void 0:o.username))return null;var l=n[0];return(0,i.jsx)(D,{byline:l,className:L().discussionByline,isLastByline:!0,index:0},l.id)}return(0,T.G)(o)?(0,i.jsxs)("div",{className:"".concat(L().newsHeadlineByline," ").concat(1!==n.length||M.length?"":[L().singleHeadlineByline]," embed-byline"),children:[(0,i.jsx)("span",{className:L().text,children:"By "}),n.map((function(t,e){return(0,i.jsxs)("span",{className:L().bylineWrap,children:[(0,i.jsx)(p,{byline:t,advertiserClass:c,index:e,isSingle:1===n.length&&!M.length},t.id),e!==n.length-1&&(0,i.jsx)("span",{className:L().text,children:" and "})]},t.id)})),!!M.length&&M.map((function(t){return(0,i.jsx)(d,{dateline:t},t.name)}))]}):n.length>2?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("span",{className:L().text,children:"by "}),n.map((function(t,e){var r=n.length===e+1;return(0,i.jsx)(D,{byline:t,isLastByline:r,index:e},t.id)}))]}):2===n.length?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("span",{className:"".concat(L().desktopHide," ").concat(L().text," ").concat(c),children:["by"," "]}),n.map((function(t,e){var n=1===e;return(0,i.jsx)(I,{byline:t,advertiserClass:c,isLastByline:n,index:e},t.id)}))]}):n.map((function(t,e){return(0,i.jsxs)("div",{className:L().adaptiveBylineContainer,children:[(0,i.jsx)(p,{byline:t,advertiserClass:c,index:e},t.id),s&&(0,i.jsx)(f,{})]},t.id)}))}x.propTypes={bylines:a().arrayOf(a().object).isRequired,isAd:a().bool.isRequired};var b=x},15185:function(t,e,n){"use strict";var i=n(52322),r=(n(2784),n(13980)),u=n.n(r),a=n(84225),o=n(5103),c=n(38362),s=n.n(c);function M(t){return new Date(1e3*t).toISOString()}function l(t){var e=t.timestampPublishedUnix,n=t.timestampUpdatedUnix,r=t.showUpdatedTimestampOnly,u=t.countryCode,o=t.t,c=t.destination;if(!e||!u)return null;var l,j,N,g,d="buzzfeed_news"===c,y=M(e),L=M(n);return d?(r||(l=(0,a.i$)(e,u,{month:"long",hour:"numeric",minute:"numeric"})).hasTimestampString||(l.formattedTimestamp=l.formattedTimestamp.replace(" AM"," am").replace(" PM"," pm")),(n||r)&&((j=(0,a.i$)(n||e,u,{month:"long",hour:"numeric",minute:"numeric"})).hasTimestampString||(j.formattedTimestamp=j.formattedTimestamp.replace(" AM"," am").replace(" PM"," pm")))):n>e?j=(0,a.i$)(n,u):l=(0,a.i$)(e,u),l&&(N=l.hasTimestampString?o("posted"):o("posted_on")),j&&(g=j.hasTimestampString?o("updated"):o("updated_on")),(0,i.jsxs)("span",{className:"".concat(d?s().timestampNews:s().timestamp," embed-timestamp"),children:[!d&&j&&(0,i.jsxs)("time",{dateTime:L,children:[g," ",j.formattedTimestamp]}),!d&&l&&(0,i.jsxs)("time",{dateTime:y,children:[N," ",l.formattedTimestamp]}),d&&(0,i.jsx)("span",{className:"".concat(s().timestampNews," embed-timestamp"),children:(0,i.jsxs)("time",{dateTime:j?L:y,children:[j&&(0,i.jsxs)("span",{className:s().timeDisplay,children:[g," ",j.formattedTimestamp]}),l&&(0,i.jsxs)("span",{className:s().timeDisplay,children:[N," ",l.formattedTimestamp]})]})})]})}l.defaultProps={timestampPublishedUnix:0,timestampUpdatedUnix:0,showUpdatedTimestampOnly:!1,countryCode:"en-us",destination:"buzzfeed"},l.propTypes={timestampPublishedUnix:u().number.isRequired,timestampUpdatedUnix:u().number.isRequired,showUpdatedTimestampOnly:u().bool,countryCode:u().string.isRequired,destination:u().string},e.Z=(0,o.withTranslation)("common")(l)},27681:function(t,e,n){"use strict";n.d(e,{Z:function(){return on}});var i=n(52322),r=n(2784),u=n(13980),a=n.n(u),o=["trending","viral"],c=["news"],s=["1999","2018","2018midterms","2020election","2020protests","amtodm","areaofexpertise","blackfutures","bodyweek2021","bodyweek2022","climate","climatechange","coronavirus","emergingwriters","endgame","fincen-files","followthis","futurefood","futurehistory","goodnews","hopesweek","impeachingtrump","investigatingtonyrobbins","itsbrutalouthere","jpg","letsgetphysical","loveyourlibrary","mentalhealthweek","moneyweek","newyearsrevolution","opinion","opportunitycosts","outsideyourbubble","parentingweek2020","popculturenostalgia","robertdurstontrial","securethebag","sex","sexweek","sexweek2021","sneakylinks","stanweek","thatwas2020","thefacebookleaks","themoneytrail","themuellermemos","theroyaltea","thingsweloved","travelweek2021","unpacking","weinsteinontrial","whatnow","whatweowe","wwfsecretwar","ukraine"],M=n(3695),l=n(30353);function j(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function N(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var i,r,u=[],a=!0,o=!1;try{for(n=n.call(t);!(a=(i=n.next()).done)&&(u.push(i.value),!e||u.length!==e);a=!0);}catch(c){o=!0,r=c}finally{try{a||null==n.return||n.return()}finally{if(o)throw r}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return j(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return j(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(t){var e=[],n=!0,i=!1,r=void 0;try{for(var u,a=Object.entries(l.QUIZ_TYPES)[Symbol.iterator]();!(n=(u=a.next()).done);n=!0){var o=N(u.value,2),c=o[0],s=o[1],M=s.tags,j=void 0===M?[]:M,g=s.title,d=s.url;j.some((function(e){return t.metadata.tags.includes(e)}))&&e.push({tags:j,title:g,type:c,url:d})}}catch(y){i=!0,r=y}finally{try{n||null==a.return||a.return()}finally{if(i)throw r}}return e}function d(t){var e=[];return l.QUIZ_CATEGORIES.forEach((function(n){n.feed_tags.some((function(e){return t.metadata.tags.includes(e)}))&&e.push(n)})),e}var y=n(84952),L=n(4726);function T(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function z(t){return function(t){if(Array.isArray(t))return T(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return T(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return T(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var m=(0,y.Js)(),p=L[m]?L[m].COMMERCE_CATEGORIES:[];var I={tvandmovies:{text:"TV and Movies",url:"/tvandmovies"},ukpolitics:{text:"Politics",url:"/politics"},longform:{text:"Big Stories",url:"/bigstories"},"as/is":{text:"As/Is",url:"/asis"},lgbt:{text:"LGBTQ",url:"/lgbt"},"internet finds":{text:"Internet Finds",url:"/bestoftheinternet"},"arts & entertainment":{text:"Arts & Entertainment",url:"/arts-entertainment"},culture:{text:"Culture & Criticism",url:"/culture"}},f=["uktvandmovies","ukcelebrity","ukmusic"];function D(t,e){if(t.isQuiz&&!t.isNews){var n=function(t){var e=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).max,n=void 0===e?4:e,i=[],r=g(t),u=d(t);return r.filter((function(t){return!t.tags.some((function(t){return u.find((function(e){return e.feed_tags.includes(t)}))}))})).forEach((function(t){i.push({text:t.title,url:t.url})})),u.forEach((function(t){i.push({text:"".concat(t.name," Quiz"),url:"/quizzes".concat(t.url)})})),i.splice(n),i}(t);if(n.length)return{primary:n,secondary:[]}}var i,r=t.classification.section||t.category,u=t.all_classifications.sections,a=(t.laser_tags||{}).subbuzz,j=(void 0===a?{}:a).commerce_category,N=(void 0===j?[]:j).map((function(t){return t.tag_name}))||[],y=[],L=[],T=r,m=r.toLowerCase();if("buzzfeed"===m&&(m=(T=u&&"BuzzFeed"!==u[0]?u[0]:"Buzz").toLowerCase()),m in I){var D=I[m];T=D.text,i=D.url}else f.includes(m)&&(T="Entertainment",i="/entertainment");if(y.push({text:T,url:i||"/".concat(T.toLowerCase().replace(/&/g,"and").replace(/ /g,"-"))}),"Shopping"===r&&(L=z(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(!t.length)return e;var n,i=p.find((function(e){return t.includes(e.laser_tag)}))||"";if(i&&(e.push({text:i.name,url:"/shopping/".concat(i.id)}),null===(n=i.children)||void 0===n?void 0:n.length)){var r,u=i.children.find((function(e){return t.includes(e.laser_tag)}))||"";if(u&&(e.push({text:u.name,url:"/shopping/".concat(i.id,"/").concat(u.id)}),null===(r=u.children)||void 0===r?void 0:r.length)){var a=u.children.find((function(e){return t.includes(e.laser_tag)}))||"";e.push({text:a.name,url:"/shopping/".concat(i.id,"/").concat(u.id,"/").concat(a.id)})}}return e}(N)).concat(z(L))),(0,M.G)(t)){if(e.length){var x=l.destinations.buzzfeed.base_url,b=e[0];o.includes(b.badge_type)||("opinion"===b.type?L.push({text:"opinion",url:"/collection/opinion"}):s.includes(b.link_to)?L.push({text:b.name,url:"/collection/".concat(b.link_to)}):L.push({text:b.name,url:"".concat(x,"/badge/").concat(b.link_to||b.type)}))}if(!L.length){var O,A,h=null===(O=t.laser_tags)||void 0===O||null===(A=O.bf_content_description)||void 0===A?void 0:A.topic;h&&h.length&&(L=z(function(t){return t&&t.length?[{text:t[0].tag_display_name,url:"/topic/".concat(t[0].tag_name.replace(/_/g,"-"))}]:[]}(h)).concat(z(L)))}(y=y.filter((function(t){return!c.includes(t.text.toLowerCase())}))).forEach((function(t){t.url="/section".concat(t.url)}))}return{primary:y,secondary:L}}var x=n(81298),b=n(67654),O=n.n(b),A=n(64435),h=n(41332),_=n(3207),S={comma:(0,i.jsxs)("span",{className:O().comma,"aria-hidden":"true",children:[","," "]}),caret:(0,i.jsx)(x.QU,{className:"caret-separator ".concat(O().caret),"aria-hidden":"true"}),midot:(0,i.jsx)("span",{className:"bold ".concat(O().midot),children:"\xb7"})};function w(t){var e=t.buzz,n=t.text,u=t.url,a=t.index,o=(0,r.useMemo)((function(){return{eventCategory:"Header",eventAction:"link",eventLabel:u,unit_type:"buzz_head",unit_name:e.id,item_name:(0,M.G)(e)?n:(0,_.z)(u),item_type:"text",position_in_unit:(0,M.G)(e)?null:a,position_in_subunit:(0,M.G)(e)?a:null,subunit_name:(0,M.G)(e)?"breadcrumbs":"",subunit_type:(0,M.G)(e)?"component":"",target_content_type:"feed",target_content_id:(0,_.z)(u)}}),[e.id,a,u]),c=(0,A.R)(o);return(0,i.jsx)("a",{ref:c,className:"metadata-link",href:u,children:n})}function E(t){var e=t.breadcrumbs,n=t.hasSecondaryBreadcrumbs,u=void 0!==n&&n,a=((0,r.useContext)(h.Z)||{}).buzz,o=void 0===a?{}:a,c=function(t){var e;switch(!0){case t.isNews:e="default";break;case t.isQuiz:e="midot";break;default:e="caret"}return S[e]}(o);return(0,i.jsx)(i.Fragment,{children:e.map((function(t,n){var r=e.length===n+1,a=o.isNews?O().breadcrumbNewsItem:O().breadcrumbItem;return(0,i.jsxs)("li",{className:a,children:[(0,i.jsx)(w,{buzz:o,text:t.text,url:t.url,index:n}),(!r||u)&&c]},"".concat(t,"--").concat(n))}))})}function v(t){var e=t.breadcrumbs,n=t.secondaryBreadcrumbs,r=void 0===n?[]:n,u=Boolean(r.length);return(0,i.jsxs)("ol",{children:[(0,i.jsx)(E,{breadcrumbs:e,hasSecondaryBreadcrumbs:u}),u&&(0,i.jsx)(E,{breadcrumbs:r})]})}v.propTypes={breadcrumbs:a().array.isRequired,secondaryBreadcrumbs:a().array};var C=n(15185),k=n(70211),Q=n(5103),U=n(81677),Y=n.n(U);var P=(0,Q.withTranslation)("common")((function(t){var e=t.t,n=(0,r.useContext)(h.Z).buzz,u=void 0===n?{}:n,a=(0,r.useMemo)((function(){return{unit_type:"buzz_head",unit_name:u.id,item_name:"return_to_article",item_type:"text",target_content_type:"buzz",target_content_id:u.id}}),[u]),o=(0,A.R)(a);return(0,i.jsxs)("div",{className:Y().headlineComments,children:[(0,i.jsxs)("a",{ref:o,href:u.canonical_url,className:"blue-link ".concat(Y().returnLink),children:[(0,i.jsx)(x.BN,{className:Y().returnIcon}),(0,i.jsx)("span",{children:e("return_to_article")})]}),(0,i.jsx)("div",{className:Y().commentOn,children:e("comments_on")})]})}));function Z(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function B(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var i,r,u=[],a=!0,o=!1;try{for(n=n.call(t);!(a=(i=n.next()).done)&&(u.push(i.value),!e||u.length!==e);a=!0);}catch(c){o=!0,r=c}finally{try{a||null==n.return||n.return()}finally{if(o)throw r}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return Z(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Z(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var R=["de","en","es","ja","pt"],W=["trending","ridealong","pick","fail"];var G=n(10432),F=n(12383),H=n(99112),J=n(12063),V=n.n(J);function q(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function K(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var i,r,u=[],a=!0,o=!1;try{for(n=n.call(t);!(a=(i=n.next()).done)&&(u.push(i.value),!e||u.length!==e);a=!0);}catch(c){o=!0,r=c}finally{try{a||null==n.return||n.return()}finally{if(o)throw r}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return q(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return q(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function X(t){var e=t.buzzId,n=t.t,u=(0,r.useState)(0),a=u[0],o=u[1],c=(0,r.useContext)(H.Z).base_url,s=K((0,G.Z)("".concat(c,"/site-component/v1/en-us/buzzstats?buzz_ids=").concat(e),{results:[]}),1)[0],M=s.error,l=s.data,j=(0,r.useMemo)((function(){return{eventCategory:"Buzz:content",eventAction:"trending",eventLabel:null,unit_type:"buzz_head",unit_name:e,item_name:"trending",item_type:"badge",position_in_unit:0,target_content_type:"feed",target_content_id:"trending"}}),[e]),N=(0,A.R)(j);return(0,r.useEffect)((function(){!M&&l.results[0]&&o(parseInt(l.results[0].total,10))}),[l,M]),M?null:(0,i.jsxs)("div",{className:V().trendingBadgeContainer,children:[(0,i.jsx)("div",{className:V().badge,children:(0,i.jsx)("a",{ref:N,className:V().trendingLink,href:"/badge/trending",children:(0,i.jsxs)("svg",{viewBox:"0 0 512 512",role:"img","aria-labelledby":"buzz-trending-badge",children:[(0,i.jsx)("title",{id:"buzz-trending-badge",children:n("trending_badge")}),(0,i.jsx)("circle",{cx:"250",cy:"250",r:"250",fill:"#E32"}),(0,i.jsx)("path",{d:"M383.7767,251.45855 L367.5385,136.3119 L259.69355,179.82015 L302.4393,204.4977 L262.06035,274.4342 L192.1208,234.05525 L123.2152,353.4048 L161.8099,375.6881 L208.4322,294.93325 L278.37175,335.3122 L341.034,226.781 L383.7767,251.45855 Z",fill:"#FFF"})]})})}),(0,i.jsxs)("div",{className:V().trendingText,children:[(0,i.jsx)("div",{className:V().trendingTitle,children:n("trending")}),a>=5e4&&(0,i.jsxs)("div",{className:"".concat(V().trendingTitle," ").concat(V().trendingViewCount),children:[(0,F.x)(a)," ",n("views")]})]})]})}X.propTypes={buzzId:a().string.isRequired};var $=(0,Q.withTranslation)("common")(X),tt=n(39252),et=n(14257),nt=n.n(et),it=n(89732),rt=n.n(it),ut=n(23679),at=n.n(ut),ot=n(71962),ct=n.n(ot),st=n(74756),Mt=n.n(st),lt=n(58724),jt=n.n(lt),Nt=n(24958),gt=n.n(Nt),dt=n(39751),yt=n.n(dt),Lt=n(84818),Tt=n.n(Lt),zt=n(62660),mt=n.n(zt),pt=n(63847),It=n.n(pt),ft=n(99139),Dt=n.n(ft),xt=n(33175),bt=n.n(xt),Ot=n(37943),At=n.n(Ot),ht=n(55805),_t=n.n(ht),St=n(24560),wt=n.n(St),Et=n(19519),vt=n.n(Et),Ct=n(84006),kt=n.n(Ct),Qt=n(14446),Ut=n.n(Qt),Yt=n(63453),Pt=n.n(Yt),Zt=n(628),Bt=n.n(Zt),Rt=n(12803),Wt=n.n(Rt),Gt=n(51326),Ft=n.n(Gt),Ht=n(89089),Jt=n.n(Ht),Vt=n(49432),qt=n.n(Vt),Kt=n(2915),Xt=n.n(Kt),$t=n(54116),te=n.n($t),ee=n(33455),ne=n.n(ee),ie=n(64742),re=n.n(ie),ue=n(40073),ae=n.n(ue),oe=n(61623),ce=n.n(oe),se=n(59922),Me=n.n(se),le=n(40583),je=n.n(le),Ne=n(98217),ge=n.n(Ne),de=n(8150),ye=n.n(de),Le=n(7870),Te=n.n(Le),ze=n(15700),me=n.n(ze),pe=n(64651),Ie=n.n(pe),fe=n(51716),De=n.n(fe),xe=n(12371),be=n.n(xe),Oe=n(60937),Ae=n.n(Oe),he=n(48061),_e=n.n(he),Se=n(42340),we=n.n(Se),Ee=n(87351),ve=n.n(Ee),Ce=n(71529),ke=n.n(Ce),Qe=n(47756),Ue=n.n(Qe),Ye={de:{cute:nt(),fail:rt(),lol:at(),omg:ct(),win:Mt(),wtf:jt()},en:{cute:gt(),fail:yt(),lol:Tt(),omg:mt(),win:It(),wtf:Dt()},es:{cute:bt(),fail:At(),lol:_t(),omg:wt(),win:vt(),wtf:kt()},"es-es":{cute:Ut(),fail:Pt(),lol:Bt(),omg:Wt(),win:Ft(),wtf:Jt()},fr:{cute:qt(),fail:Xt(),lol:te(),omg:ne(),win:re(),wtf:ae()},general:{fail:Me(),quiz:ce(),trending:je(),viral:ge()},ja:{cute:ye(),fail:Te(),lol:me(),omg:Ie(),win:De(),wtf:be()},pt:{cute:Ae(),fail:_e(),lol:we(),omg:ve(),win:ke(),wtf:Ue()}};function Pe(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Ze={Triggered:{priority:1},Editorial:{priority:2},Reaction:{priority:3},Others:{priority:4}},Be=["cute","omg","win","fail","wtf","lol","trending","viral","quiz"],Re=["cute","omg","win","fail","wtf","lol"];function We(t){var e=t.index,n=t.badge,u=t.buzzId,a=(0,r.useMemo)((function(){return{eventCategory:"Buzz:content",eventAction:n.badge_type,eventLabel:null,unit_type:"buzz_head",unit_name:u,item_name:n.link_to,item_type:"badge",position_in_unit:e,target_content_type:"feed",target_content_id:n.link_to}}),[n.badge_type,u,e]),o=(0,A.R)(a);return(0,i.jsx)("a",{ref:o,className:V().badge,href:"/badge/".concat(n.link_to),children:n.has_svg&&n.badge_language?(0,i.jsx)("img",{src:Ye[n.badge_language][n.badge_type],alt:"".concat(n.name," badge")}):(0,i.jsx)(tt.lS,{src:"".concat(l.image_service_url).concat(n.path),alt:"".concat(n.name," badge")})},e)}function Ge(t){var e=t.badges,n=t.countryCode,r=t.flags,u=t.buzzId;if(!e)return null;var a=e.find((function(t){return"trending"===t.badge_type})),o=e.filter((function(t){return!W.includes(t.badge_type)&&!function(t,e){var n=0===e.sensitive,i=0===e.sensitive&&0===e.nsfw&&1===e.brand_safe,r=["Editorial","Reaction"].includes(t.category),u=["Custom","Thematic"].includes(t.category),a=!t.path;return!n&&r||!i&&u||a}(t,r)&&(!a||a&&"viral"!==t.badge_type)})).map((function(t){var e=t.badge_type;return t.badge_language=Re.includes(e)?function(t){if("es-es"===t)return t;var e=B((t||"").split("-"),1)[0];return R.includes(e)?e:"en"}(n):"general",t.has_svg=Be.includes(e),t.link_to=t.link_to||e,t.category in Ze||(t.category="Others"),function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Pe(t,e,n[e])}))}return t}({},t,Ze[t.category])})),c=function(t){return t.sort((function(t,e){return t.priority>e.priority?1:t.priority<e.priority?-1:t.price>e.price?1:t.price<e.price?-1:0}))}(o).slice(0,3);return(0,i.jsxs)("ul",{className:V().badgeList,children:[c.map((function(t,e){return(0,i.jsx)("li",{children:(0,i.jsx)(We,{index:e,badge:t,buzzId:u})},e)})),a&&(0,i.jsx)("li",{children:(0,i.jsx)($,{buzzId:u})})]})}Ge.defaultProps={badges:[],countryCode:"en-us",flags:{},buzzId:"0"},Ge.propTypes={badges:a().array.isRequired,countryCode:a().string.isRequired,flags:a().object.isRequired,buzzId:a().string.isRequired};var Fe=Ge,He=n(80266),Je=n.n(He),Ve=n(81550),qe=n(24678),Ke=n(20420);var Xe=function(t){var e=t.buzz,n=(0,Q.useTranslation)("common").t,r="commerce_disclaimer";return e.isShopping||e.tags.some((function(t){return t.match("--commerce-disclaimer")}))?((0,M.G)(e)&&"en"===e.language&&(r="commerce_disclaimer_news"),(0,i.jsx)("div",{className:Ke.container,children:(0,i.jsx)("p",{className:"".concat(Ke.text," ").concat((0,M.G)(e)?Ke.buzzfeed_news:""),children:n(r)})})):null},$e=n(83343),tn={taylor_swift:{topic:"Taylor Swift",subscriptions:[{subscriptionId:"buzzfeed_email_taylor-swift-weekly-rundown",title:"Weekly Rundown",description:"Our weekly Taylor Swift rundown right to your inbox.",isSubscribed:!0},{subscriptionId:"buzzfeed_email_taylor-swift-daily-rundown",title:"Daily Rundown",description:"Can't wait a whole week? Get updated on all things Taylor Swift daily.",isSubscribed:!1}]},economy_hate_watch:{topic:"Economy Hate Watch",description:"Trump promised to bring prices way, way down. So, as a pro-hater, I'm keeping track of Trump's economy right here. Every month. Enjoy!",descriptionLoggedOut:"Trump promised to bring prices way, way down. So, as a pro-hater, I'm keeping track of Trump's economy right here. Every month. Enjoy!",subscriptions:[{subscriptionId:"buzzfeed_email_economy_hate_watch",title:"Economy Hate Watch",description:"",isSubscribed:!0}]},arcade:{topic:"Arcade",description:"Be notified on the launch of new games, AI image generators, filters, and experiments \u2014 no more than once a week!",descriptionLoggedOut:"Be notified on the launch of new games, AI image generators, filters, and experiments \u2014 no more than once a week!",subscriptions:[{subscriptionId:"buzzfeed_email_arcade-highlights-weekly",title:"Arcade Highlights",description:"",isSubscribed:!0}]},that_got_dark:{topic:"That Got Dark",description:"Grab your flashlight, it's time to dig into the weirdest, creepiest things on the internet (and beyond). \u2014 By Crystal Ro",descriptionLoggedOut:"Grab your flashlight, it's time to dig into the weirdest, creepiest things on the internet (and beyond). \u2014 By Crystal Ro",subscriptions:[{subscriptionId:"buzzfeed_email_that-got-dark",title:"That Got Dark",description:"",isSubscribed:!0}]},screen_time:{topic:"Screen Time",description:"Your insider guide on what to watch next \u2014 from someone who watches everything. \u2014 By Nora Dominick",descriptionLoggedOut:"Your insider guide on what to watch next \u2014 from someone who watches everything. \u2014 By Nora Dominick",subscriptions:[{subscriptionId:"buzzfeed_email_screen-time",title:"Screen Time",description:"",isSubscribed:!0}]},quizzes:{topic:"Quizzes",description:"The quizzes you know and love delivered straight to your inbox!",descriptionLoggedOut:"The quizzes you know and love delivered straight to your inbox!",subscriptions:[{subscriptionId:"buzzfeed_email_quizzes",title:"Quizzes",description:"",isSubscribed:!0}]},shopping:{topic:"Shopping",description:"Find great products - plus cool stuff you didn't even know you needed.",descriptionLoggedOut:"Find great products - plus cool stuff you didn't even know you needed.",subscriptions:[{subscriptionId:"buzzfeed_email_shopping",title:"Shopping",description:"",isSubscribed:!0}]},daily:{topic:"BuzzFeed Daily",description:"Keep up with the latest daily buzz with the BuzzFeed Daily newsletter!",descriptionLoggedOut:"Keep up with the latest daily buzz with the BuzzFeed Daily newsletter!",subscriptions:[{subscriptionId:"buzzfeed_email_daily",title:"BuzzFeed Daily",description:"",isSubscribed:!0}]}},en=n(70448),nn=n(4366);function rn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function un(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){rn(t,e,n[e])}))}return t}function an(t){var e=t.buzz,n=t.badges,u=t.t,a=(0,r.useContext)(H.Z).destination,o=e.isCommentsPage,c=Number(e.published),s="edit_updated"in e?Number(e.edit_updated):0,j=e.tags.includes("--updated-timestamp-only"),N="en"===e.language,g=null,d=function(t){var n,i,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"topic",u=Array.isArray(t)?t:[t],a=(null===e||void 0===e||null===(n=e.laser_tags)||void 0===n||null===(i=n.bf_content_description)||void 0===i?void 0:i[r])||[];return u.every((function(t){return a.some((function(e){return e.tag_name===t}))}))};e.isAd||e.isCommunity&&!e.isModerated?g=null:d("unsolved_ops","workflow")&&N?g="that_got_dark":(d("tv_shows")||d("movies"))&&N?g="screen_time":d("taylor_swift")&&N?g="taylor_swift":d("economy_hate_watch")&&N?g="economy_hate_watch":d("arcade")&&N?g="arcade":e.isShopping&&N?g="shopping":e.isQuiz&&N?g="quizzes":N&&(g="daily");var y=1===e.flags.ad,L=e.country_code,T=D(e,n),z=T.primary,m=T.secondary,p=e.bylines.some((function(t){return"6"===t.description_id}))&&"Japan"!==e.classification.edition,I=e.longform_custom_header&&!(0,M.G)(e)?e.longform_custom_header:e,f=I.title,x=I.description,b=!(0,M.G)(e)||(0,M.G)(e)&&(z.length||m.length)>0,O={unit_type:"buzz_head",unit_name:e.id,item_type:"button"},A=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,r.useMemo)((function(){return{internalLink:function(n){(0,nn.TW)(t,un({},e,n))},contentAction:function(n){(0,nn.bC)(t,un({},e,n))},impression:function(n){(0,nn.Oz)(t,un({},e,n))}}}),[t,e])}(e,O),h=!!(0,en.zk)(),_=tn[g];g&&!_&&console.warn("Unknown subscription offering: ".concat(g));var S=e.longform_custom_header&&e.longform_custom_header.style&&e.longform_custom_header.style.text_over;if(e.hasQuizSharedResult){var w=n[0];return w.has_svg=!0,w.badge_language="general",(0,i.jsxs)("div",{className:Je().sharedResultHeadlineWrapper,children:[(0,i.jsxs)("div",{className:Je().sharedResultBadge,children:[(0,i.jsx)(We,{index:0,badge:w,buzzId:e.buzzId}),(0,i.jsx)("p",{children:"Result from"})]}),(0,i.jsx)("h1",{className:Je().sharedResultTitle,children:(0,i.jsx)("a",{href:e.canonical_url,children:e.title})})]})}var E=(0,i.jsxs)("div",{className:"".concat(Je().container," embed-breadcrumbs"),children:[y?(0,i.jsx)("span",{className:Je().paidPostBreadcrumb,children:u("paid_post")}):(0,i.jsxs)("nav",{"aria-label":"Breadcrumb",className:(0,M.G)(e)?Je().breadcrumbContainerNews:Je().breadcrumbContainer,children:[(0,i.jsx)(v,{breadcrumbs:z,secondaryBreadcrumbs:m}),"buzzfeed"===a&&0!==z.length&&c>0&&(0,i.jsx)("span",{className:"bold ".concat(Je().middot),children:"\xb7"})]}),"buzzfeed"===a&&(0,i.jsx)(C.Z,{timestampPublishedUnix:c,timestampUpdatedUnix:s,countryCode:L,destination:a})]}),Q={adDisclaimer:!o&&p,badges:!o&&!e.isAd&&"buzzfeed"===a,breadcrumbAboveTitle:!o&&b,breadcrumbBelowTitle:o&&b,byline:!o,commerceDisclaimer:!o,communityDisclaimer:!o&&!e.isModerated,timestamp:!o&&"buzzfeed_news"===a};return(0,i.jsxs)("div",{className:"".concat(Je().headline," ").concat(Je()[e.bfpTheme]||""," ").concat(Je()[a]," embed-buzz-header"),children:[Q.badges&&(0,i.jsx)("div",{className:Je().container,children:(0,i.jsx)(Fe,{badges:n,buzzId:e.id,countryCode:L,flags:e.flags})}),o&&(0,i.jsx)(P,{}),Q.commerceDisclaimer&&(0,i.jsx)(Xe,{buzz:e}),Q.communityDisclaimer&&(0,i.jsxs)("div",{className:"".concat(Je().disclaimer," ").concat(Je().container),children:[u("community_disclaimer"),(0,i.jsxs)("a",{className:"blue-link",href:"http://community.buzzfeed.com/",children:[" ",u("community_try")]})]}),Q.adDisclaimer&&(0,i.jsx)("div",{className:"".concat(Je().disclaimer," ").concat(Je().container),children:u("ad_disclaimer")}),Q.breadcrumbAboveTitle&&E,_&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)($e.Z,{topic:_.topic,description:_.description,descriptionLoggedOut:_.descriptionLoggedOut,subscriptions:_.subscriptions,isSignedIn:h,siteCaptchaKey:l.site_captcha_key,track:A}),(0,i.jsx)("br",{})]}),(0,i.jsxs)("div",{className:"".concat(Je().container," ").concat(S?Je().hideTitle:""," embed-headline"),children:[(0,i.jsx)("h1",{className:"".concat(Je().title," embed-headline-title"),dangerouslySetInnerHTML:{__html:f}}),(0,i.jsx)("p",{className:"".concat(Je().description," embed-headline-description"),dangerouslySetInnerHTML:{__html:x}})]}),Q.breadcrumbBelowTitle&&E,Q.byline&&(0,i.jsx)(k.Z,{bylines:e.bylines,isAd:y,buzz:e}),Q.timestamp&&(0,i.jsx)(C.Z,{timestampPublishedUnix:c,timestampUpdatedUnix:s,showUpdatedTimestampOnly:j,countryCode:L,destination:a})]})}an.propTypes={buzz:a().object.isRequired,badges:a().array};var on=(0,Ve.Z)((0,Q.withTranslation)("common")(an),{onError:qe.Tb})},54761:function(t,e,n){"use strict";n.d(e,{Z:function(){return c}});var i=n(52322),r=n(2784),u=n(88163),a=n(41332);function o(t,e){if(null==t)return{};var n,i,r=function(t,e){if(null==t)return{};var n,i,r={},u=Object.keys(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function c(t){var e=t.children,n=t.lazy,c=void 0===n||n,s=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.preloadDistance,n=void 0===e?2:e,i=t.rootMargin,o=(0,r.useContext)(a.Z).id;if(void 0===i){var c="undefined"!==typeof document?document.documentElement.clientHeight:0;i="".concat(c*n,"px 0px")}var s=(0,u.Z)({rootMargin:i,once:!0,pageId:o}),M=s.isIntersecting,l=s.setObservable;return{canLoad:M,setObservable:l}}(o(t,["children","lazy"])),M=s.canLoad,l=s.setObservable;return(0,i.jsx)("div",{ref:c?l:null,children:!c||M?e:null})}},22813:function(t,e,n){"use strict";var i=n(94776),r=n.n(i),u=n(52322),a=n(13980),o=n.n(a),c=n(2784),s=n(6294),M=n(73931),l=n(81298),j=n(5103),N=n(41332),g=n(46811),d=n(4366),y=n(16179),L=n.n(y);function T(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function z(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function m(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){z(t,e,n[e])}))}return t}function p(t){var e=t.type,n=void 0===e?"page_level":e,i=t.variant,a=t.includeTitle,o=void 0!==a&&a,y=t.includeShortTitle,z=void 0!==y&&y,p=t.includeImage,I=void 0!==p&&p,f=t.subbuzzId,D=void 0===f?"":f,x=t.trackingData,b=void 0===x?{}:x,O=(0,j.useTranslation)("common").t,A=(0,c.useState)(null),h=A[0],_=A[1],S=(0,c.useContext)(N.Z).buzz,w=void 0===S?{}:S,E="".concat(w.canonical_url).concat(D?"?sub="+D:""),v=(0,M.X)({shareUrl:E,platform:"native".concat(h)});if((0,c.useEffect)((function(){(0,g.Hd)()&&_((0,s.gc)()?"android":"ios")}),[]),!i||"page_level"===n&&w.shouldHideBuzzSharing||!h)return null;var C=function(){var t,e=(t=r().mark((function t(){var e,n,i,u,a,c,s,M,l,j;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e={url:v.replace("https://stage","https://www")},n=w.title,z){c=null===w||void 0===w||null===(i=w.socialpromotions)||void 0===i||null===(u=i.homepage)||void 0===u||null===(a=u[0])||void 0===a?void 0:a.extra_fields;try{s=JSON.parse(c),n=(null===s||void 0===s?void 0:s.short_headline)||n}catch(r){console.error(r)}}if((z||o)&&(e.text=n),!I||!w.picture){t.next=12;break}return t.next=7,fetch("".concat(w.picture,"?output-quality=auto&output-format=auto"));case 7:return M=t.sent,t.next=10,M.blob();case 10:l=t.sent,e.files=[new File([l],"buzzfeed-".concat(w.title.replace(/\W/g,"-"),".jpg"),{type:l.type})];case 12:j=m({action_type:"share",action_value:"native_share",item_type:"button",item_name:"native_share"},b),(0,d.bC)(w,j),(0,g.Om)(e);case 15:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){T(u,i,r,a,o,"next",t)}function o(t){T(u,i,r,a,o,"throw",t)}a(void 0)}))});return function(){return e.apply(this,arguments)}}();return(0,u.jsxs)("button",{className:"".concat(L().nativeShareButton," ").concat(L()[i]," ").concat(L()[h]),onClick:C,children:[(0,u.jsx)("div",{className:L().icon,children:"android"===h?(0,u.jsx)(l.tm,{}):(0,u.jsx)(l.r6,{})}),"icon_only"!==i&&O("share")]})}p.propTypes={variant:o().oneOf(["standard","standard_small","dark","icon_only"]).isRequired,type:o().string,includeTitle:o().bool,includeShortTitle:o().bool,includeImage:o().bool,subbuzzId:o().string},e.Z=p},29583:function(t,e,n){"use strict";n.d(e,{Z:function(){return j}});var i=n(52322),r=n(2784),u=n(41332),a=n(36491),o=n(54761),c=n(61784);function s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function M(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){s(t,e,n[e])}))}return t}var l=(0,a.Z)((function(){return Promise.all([n.e(548),n.e(403),n.e(794)]).then(n.bind(n,21954))}),{ssr:!1});function j(t){var e=(0,r.useContext)(u.Z).buzz,n=void 0===e?{}:e;return(0,c.h)(n)?(0,i.jsx)(o.Z,{children:(0,i.jsx)(l,M({},t))}):null}},49671:function(t,e,n){"use strict";n.d(e,{Z:function(){return mt}});var r=n(52322),u=n(54761),a=n(2784),o=n(99523),c=n.n(o),s=n(41332),M=n(99112),l=n(4366);function j(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function N(t){var e=(0,a.useRef)(null),n=(0,a.useContext)(s.Z).buzz,i=void 0===n?{}:n,r=(0,a.useContext)(M.Z).destination;return(0,a.useEffect)((function(){var n=e.current;if(!n)return function(){};var r=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){j(t,e,n[e])}))}return t}({},t,{action:"post/package/"+t.index,position_in_unit:0,unit_name:"package",unit_type:"buzz_bottom",subunit_type:"package",subunit_name:t.category,label:t.category});"cta"===t.index&&(r.action="cta/package");var u=(0,l.aF)(n,i,r),a=r.target_content_url?(0,l.W3)(n,i,r):(0,l.Ev)(n,i,r);return function(){u(),a()}}),[i,r,t]),e}var g=n(94776),d=n.n(g),y=n(30353),L=n(32966),T=n(15382),z=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=document.createElement("div");return e.innerHTML=t,e.textContent};function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function p(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function I(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){I(t,e,n[e])}))}return t}function D(t){return function(t){if(Array.isArray(t))return m(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return m(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return m(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var x=function(){var t=function(t){var n=e[t];return(0,T.Z)(n,{mode:"mobile"})},e={control:"shopping-package-internal"},n={aria_title:"Shopping",component_name:"packages",countdown:0,cta:{text:"See more Shopping",url:"https://www.buzzfeed.com/shopping"},data_source:"recsys_api",package_id:"",results:[],template:"default",theme:"blue",title:"",treatments:["shopping","visual"]},i=function(){var e,i=(e=d().mark((function e(i,r,u){var a,o,c,s,M,l,j,N,g,L,T=arguments;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=T.length>3&&void 0!==T[3]&&T[3],o=u||t(i||"control"),c=null,e.prev=3,e.next=6,fetch(o,{});case 6:return s=e.sent,e.next=9,s.json();case 9:if(M=e.sent,l=M.results||[],j=[],a&&((j=l.filter((function(t){return Array.isArray(t.laser_tags)&&t.laser_tags.some((function(t){return"prime_day"===t.tag_name&&"bf_content_description"===t.namespace_name}))}))).length<6&&(N=6-j.length,g=l.filter((function(t){return!j.some((function(e){return e.id===t.id}))})),L=g.slice(-N),j=j.concat(L)),l=j),l.length){e.next=15;break}return e.abrupt("return",null);case 15:(c=n).package_id=l[0].id,c.results=l.map((function(t){var e=null===t||void 0===t?void 0:t.image,n=null===t||void 0===t?void 0:t.image_alt;if(Array.isArray(t.thumbnails)&&t.thumbnails.length){var i=t.thumbnails[0];if(Array.isArray(i.sizes)&&i.sizes.length){var u=i.sizes.find((function(t){return"big"===t.size}))||i.sizes[0];u&&(e=u.url,n=u.alt_text||"")}}return{author:"",canonical_path:t.canonical_path,created_at:"",data_source:t.data_source_name,data_source_name:t.data_source_name,description:z(t.description),id:t.id,image:e,image_alt_text:n,name:t.name,url:t.url?"".concat(t.url,"?origin=").concat(r):"https://www.buzzfeed.com".concat(t.canonical_path,"?origin=").concat(r)}})),c.cta&&c.cta.url&&(c.cta.url+="?origin=".concat(r)),e.next=24;break;case 21:e.prev=21,e.t0=e.catch(3),"dev"===y.CLUSTER&&console.log("error fetching data",e.t0);case 24:return e.abrupt("return",c);case 25:case"end":return e.stop()}}),e,null,[[3,21]])})),function(){var t=this,n=arguments;return new Promise((function(i,r){var u=e.apply(t,n);function a(t){p(u,i,r,a,o,"next",t)}function o(t){p(u,i,r,a,o,"throw",t)}a(void 0)}))});return function(t,e,n){return i.apply(this,arguments)}}();return{fetchData:i}}();function b(t){var e=t.buzz,n=(0,a.useState)(null),i=n[0],r=n[1],u=(0,a.useContext)(L.Z),o=u.getFeatureFlagValue,c=u.getExperimentValue,s=u.experiments,l=e.country_code,j=e.isUS,N=(e.classification.section||e.category).toLowerCase(),g=e.laser_tags&&e.laser_tags.bf_content_description&&e.laser_tags.bf_content_description.topic||[],d=D(e.tags.filter((function(t){return!t.includes("--")}))).concat(D(g.map((function(t){return t.tag_name}))));(0,a.useContext)(M.Z).base_url;return(0,a.useEffect)((function(){var t;if(s.loaded){var n=(null===e||void 0===e||null===(t=e.flags)||void 0===t?void 0:t.sensitive)||!1,i=e.isUS&&!e.isAd&&!n&&!d.includes("ai quiz")&&!e.shouldHideRecircSection,u=c("shopping-package-v4-ml-1499",{rejectErrors:!1}),a=o("prime-day-bpage-recirc")||!1,M="";if((i||("nifty"===N||"shopping"===N)&&j)&&(M="shopping"),j&&"shopping"===M)if(a){x.fetchData(u,"bprime_pkg","https://weaver-api.buzzfeed.com/v1/feeds/shopping-package-prime-day",!0).then((function(t){t&&r(f({},t,{category:M}))}))}else{var l=function(t,e){var n={shopping:{shopping:"bshp",nifty:"nifty_pkg_bpage"}},i={giftguide:"bgg"};return n[t]&&n[t][e]?n[t][e]:i[t]||"shp"}(M,N);x.fetchData(u,l).then((function(t){t&&r(f({},t,{category:M}))}))}}}),[e.tags,N,j,l,s]),i}var O=n(39252),A=n(81298),h=n(52547),_=n(3207),S=n(90093),w=n(33859),E=n.n(w),v=function(t){var e=t.className;return(0,r.jsxs)("svg",{role:"img","aria-label":"BuzzFeed Shopping logo",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 535.7 153.8",className:e,children:[(0,r.jsx)("title",{children:"Shopping on BuzzFeed"}),(0,r.jsxs)("g",{children:[(0,r.jsx)("path",{d:"M85.4 33v-9.5C85.4 9.6 72.9.4 57.6 2.8S29.7 18.6 29.7 32.5v11.7",fill:"none",stroke:"#000",strokeWidth:4.92,strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{fill:"#fff",stroke:"#000",strokeWidth:4.92,strokeLinecap:"round",strokeLinejoin:"round",d:"M2.5 51.2L11.8 56.4 21.1 47.5 30.4 52.7 39.7 43.8 49 49 58.3 40 67.6 45.3 76.9 36.3 86.2 41.6 95.6 32.7 102.7 37.9 102.7 144.1 3.6 135.6z"}),(0,r.jsx)("path",{fill:"#f37a7b",stroke:"#000",strokeWidth:4.92,strokeLinecap:"round",strokeLinejoin:"round",d:"M129.2 38.9L121.7 43.5 118.8 35.9 112.6 40.8 108.8 32.7 102.7 37.4 102.7 144.1 129.2 134z"}),(0,r.jsx)("path",{id:"Arrow",d:"M85.4 90.4L81.1 60.1 52.7 71.6 64 78.1 53.3 96.5 35 85.9 16.8 117.3 27 123.1 39.2 101.9 57.6 112.5 74.1 83.9z"}),(0,r.jsx)("path",{d:"M155.6 80.9V38.1h19c7.4 0 12 4.5 12 10.9 0 5.4-3.6 9-7.5 9.8 4.6.7 8.3 5.4 8.3 10.5 0 6.9-4.6 11.5-12.4 11.5h-19.4zm25.5-31.1c0-3.8-2.6-6.9-7.4-6.9h-12.8v13.8h12.8c4.8-.1 7.4-3 7.4-6.9zm.8 18.9c0-3.9-2.7-7.4-8-7.4h-13.1V76H174c5 .1 7.9-2.7 7.9-7.3zM204.8 64.3V38.1h5.3v26c0 7.8 4.2 12.8 12.1 12.8 7.9 0 12.1-4.9 12.1-12.8v-26h5.3v26.1c0 10.6-5.8 17.4-17.4 17.4-11.6 0-17.4-6.8-17.4-17.3zM256.7 80.9v-4.4l24-33.6h-24v-4.7h30.8v4.4l-24 33.6H288v4.7h-31.3zM303.1 80.9v-4.4l24-33.6h-24v-4.7h30.8v4.4l-24 33.6h24.5v4.7h-31.3zM351.4 80.9V38.1h28v4.7h-22.7v13.8H379v4.7h-22.2v19.5h-5.4zM395.6 80.9V38.1h28v4.7h-22.7v13.8h22.2v4.7h-22.2V76h22.7v4.7h-28zM441 80.9V38.1h28v4.7h-22.7v13.8h22.2v4.7h-22.2V76H469v4.7h-28zM486.4 80.9V38.1H501c13.3 0 21.9 9.3 21.9 21.4 0 12.2-8.7 21.4-21.9 21.4h-14.6zm31-21.4c0-9.2-5.8-16.7-16.4-16.7h-9.3v33.3h9.3c10.4 0 16.4-7.4 16.4-16.6z"}),(0,r.jsx)("path",{d:"M154.5 129.3l4.9-6.9c3 3.1 7.7 5.8 13.6 5.8 5 0 7.4-2.3 7.4-4.7 0-7.6-24.6-2.4-24.6-18.5 0-7.1 6.2-13 16.2-13 6.8 0 12.4 2.1 16.7 6l-5.1 6.7c-3.5-3.2-8.1-4.7-12.4-4.7-3.9 0-6.1 1.7-6.1 4.3 0 6.8 24.6 2.2 24.6 18.2 0 7.8-5.6 13.7-17.1 13.7-8.3-.1-14.2-2.8-18.1-6.9zM232.2 135.4v-18H212v18h-9.1V92.6h9.1v16.8h20.2V92.6h9.2v42.8h-9.2zM254.7 114c0-12.9 9.4-22.1 22.3-22.1 13 0 22.4 9.2 22.4 22.1 0 12.9-9.4 22.1-22.4 22.1-12.8 0-22.3-9.2-22.3-22.1zm35.4 0c0-8-5.1-14-13-14s-13 6-13 14 5.1 14 13 14 13-6 13-14zM312.9 135.4V92.6h20c9.3 0 14.4 6.3 14.4 13.8 0 7.4-5.1 13.8-14.4 13.8H322v15.2h-9.1zm25.1-29c0-3.6-2.7-5.8-6.3-5.8H322v11.5h9.7c3.6.1 6.3-2.2 6.3-5.7zM360 135.4V92.6h20c9.3 0 14.4 6.3 14.4 13.8 0 7.4-5.1 13.8-14.4 13.8h-10.9v15.2H360zm25-29c0-3.6-2.7-5.8-6.3-5.8H369v11.5h9.7c3.6.1 6.3-2.2 6.3-5.7zM407 135.4V92.6h9.1v42.8H407zM461.1 135.4l-20.4-27.9v27.9h-9.1V92.6h9.4l19.8 26.9V92.6h9.1v42.8h-8.8zM483.4 114c0-13.5 10.3-22.1 22.8-22.1 8.9 0 14.5 4.5 17.7 9.6l-7.6 4.1c-1.9-3-5.6-5.6-10.1-5.6-7.8 0-13.4 6-13.4 14 0 8.1 5.6 14 13.4 14 3.8 0 7.4-1.7 9.1-3.3v-5.1H504v-8h20.4V128c-4.4 4.9-10.5 8.1-18.2 8.1-12.5.1-22.8-8.6-22.8-22.1z"})]})]})},C=function(t){var e=t.children;return(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:E().packageWrapper,children:[(0,r.jsx)("div",{className:E().heading,children:(0,r.jsx)(v,{})}),e]})})},k=function(t){var e=t.className;return(0,r.jsx)("svg",{className:e,width:"11",height:"10",viewBox:"0 0 11 10",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.3146 4.99927L5.36504 0.0497586L4.12766 1.28714L7.84027 4.99976L4.12766 8.71237L5.36504 9.94975L10.3146 5.00024L10.3141 4.99976L10.3146 4.99927Z"})})};function Q(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function U(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Y(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){U(t,e,n[e])}))}return t}function P(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return Q(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Q(t,e)}(t,i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Z(t){var e=t.item,n=void 0===e?{}:e,i=t.category,u=(0,a.useRef)(null);return(0,a.useEffect)((function(){var t=u.current;t&&(0,h.t)(t,180)})),(0,r.jsx)("div",{className:c().splash,children:(0,r.jsx)(R,{item:n,index:0,category:i,children:(0,r.jsxs)("div",{className:c().textWrap,children:[(0,r.jsx)("h3",{className:c().text,children:n.name}),(0,r.jsx)("p",{className:c().dek,ref:u,children:n.description})]})})})}function B(t){var e=t.item,n=void 0===e?{}:e,i=t.index,u=t.category;return(0,r.jsx)("li",{className:c().item,children:(0,r.jsx)(R,{item:n,index:i,category:u,children:(0,r.jsx)("div",{className:c().text,children:n.name})})})}function R(t){var e=t.item,n=t.children,i=t.index,u=t.category,o={};(0,S.p)(e.url)?(o.target_content_type="buzz",o.target_content_id=e.id):(o.target_content_url=e.url,o.target_content_type="url",o.target_content_id=e.url);var s=N((0,a.useMemo)((function(){return Y({},e,{item_name:e.id,item_type:0===i?"splash":"card",position_in_subunit:i,index:i,category:u},o,{data_source_name:"site_component_api"})}),[e,i,u,o]));return(0,r.jsx)("article",{children:(0,r.jsxs)("a",{href:e.url,className:c().link,ref:s,children:[(0,r.jsx)(O.lS,{className:c().img,src:e.image,alt:e.image_alt_text}),n]})})}function W(t){var e=t.item,n=t.category,i=t.position,u={item_name:(0,_.z)(e.url),position_in_subunit:i,target_content_type:"feed",target_content_id:(0,_.z)(e.url)},o=N((0,a.useMemo)((function(){return Y({},e,{item_type:"text",index:"cta",category:n,data_source_name:"site_component_api"},u)}),[e,n,u]));return(0,r.jsxs)("a",{className:c().ctaBottom,href:e.url,ref:o,children:[e.text,(0,r.jsx)(k,{className:c().ctaIcon})]})}function G(t){var e=t.title,n=t.template;return(0,r.jsxs)("div",{className:c().headerWrap,children:[(0,r.jsx)(F,{title:e,children:(0,r.jsx)(H,{template:n})}),(0,r.jsx)(J,{template:n})]})}function F(t){var e=t.title,n=t.children;return(0,r.jsxs)("h2",{className:c().headerText,children:[n,(0,r.jsx)("span",{dangerouslySetInnerHTML:{__html:e}})]})}function H(t){var e=t.template;return"countdown"!==e&&"megasplash"!==e?null:(0,r.jsx)("img",{className:c().fireIcon,src:n(54712),alt:"Fire"})}function J(t){var e=t.template;return"countdown"!==e&&"megasplash"!==e?null:(0,r.jsx)("div",{className:c().packageSubTitle,children:"THE BEST DEALS ON THE INTERNET"})}function V(t){var e=t.item,n=t.category,i=t.template,u=N((0,a.useMemo)((function(){return Y({},e,{item_type:"text",item_name:(0,_.z)(e.url),target_content_type:"feed",target_content_id:(0,_.z)(e.url),index:"cta",category:n,data_source_name:"site_component_api"})}),[e,n]));return"themed"!==i?null:(0,r.jsx)("div",{className:c().ctaTopWrap,children:(0,r.jsxs)("a",{className:c().ctaTop,href:e.url,ref:u,children:[e.text,(0,r.jsx)(A.QU,{className:c().rightArrow,height:14,width:14})]})})}function q(t){var e=t.endTime,n=t.template,i=(0,a.useState)(1e3*parseInt(e,10)-Date.now()),u=i[0],o=i[1];if((0,a.useEffect)((function(){var t=setTimeout((function(){o(u-1e3)}),1e3);return function(){return clearTimeout(t)}}),[u]),"countdown"!==n&&"megasplash"!==n)return null;if(u<=0)return null;var s={seconds:Math.floor(u/1e3%60),minutes:Math.floor(u/1e3/60%60),hours:Math.floor(u/36e5%24),days:Math.floor(u/864e5)};return(0,r.jsxs)("div",{className:c().countdown,"aria-label":"Time Left in Countdown",children:[(0,r.jsxs)("div",{className:c().countdownItem,children:[(0,r.jsx)("span",{className:c().countdownNum,children:s.days}),(0,r.jsx)("span",{className:c().countdownUnit,children:"DAYS"})]}),(0,r.jsxs)("div",{className:c().countdownItem,children:[(0,r.jsx)("span",{className:c().countdownNum,children:s.hours}),(0,r.jsx)("span",{className:c().countdownUnit,children:"HRS"})]}),(0,r.jsxs)("div",{className:c().countdownItem,children:[(0,r.jsx)("span",{className:c().countdownNum,children:s.minutes}),(0,r.jsx)("span",{className:c().countdownUnit,children:"MINS"})]}),(0,r.jsxs)("div",{className:c().countdownItem,children:[(0,r.jsx)("span",{className:c().countdownNum,children:s.seconds}),(0,r.jsx)("span",{className:c().countdownUnit,children:"SECS"})]})]})}var K=function(){var t=(0,a.useContext)(s.Z).buzz,e=b({buzz:void 0===t?{}:t}),n=e?e.category:"";if(!e)return null;var i=P(e.results.slice(0,5)),u=i[0],o=i.slice(1),M="default"!==e.template&&e.theme&&c()[e.theme]?c()[e.theme]:c().default;return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(C,{role:"region","aria-label":e.title,children:(0,r.jsxs)("div",{className:M,role:"region","aria-label":e.title,children:[(0,r.jsxs)("div",{className:c().header,children:[(0,r.jsx)(G,{template:e.template,title:e.title}),(0,r.jsx)(V,{item:e.cta,category:n,template:e.template}),(0,r.jsx)(q,{endTime:e.countdown,template:e.template})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(Z,{item:u,category:n},u.id),(0,r.jsx)("ul",{className:c().items,children:o.map((function(t,e){return(0,r.jsx)(B,{item:t,index:e+1,category:n},t.id)}))})]}),(0,r.jsx)(W,{item:e.cta,category:n,position:o.length+1})]})})})},X=n(6277),$=n(58183),tt=n.n($),et=n(59882),nt=n.n(et),it=n(82924),rt=n(3695),ut=function(t){var e=t.className,n=t.title;return(0,r.jsxs)("svg",{className:e,width:"70",height:"110",viewBox:"0 0 70 110",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("title",{children:n}),(0,r.jsx)("path",{d:"M16.588 26.4197C16.3097 25.5489 16.7033 24.6989 17.4663 24.523L24.0372 23.0084C32.5086 21.0557 43.3848 27.8246 48.2901 38.0947C50.8458 43.4667 51.4041 49.0667 49.8103 53.4629C48.4056 57.3169 45.4852 59.9393 41.5825 60.8389L36.2462 62.0689C35.4833 62.2448 34.6376 61.6804 34.3594 60.8096C34.0811 59.9388 34.4747 59.0887 35.2377 58.9129L40.5739 57.6828C43.6536 56.973 45.9559 54.918 47.0536 51.9055C48.3509 48.3369 47.8549 43.7085 45.7255 39.2355C41.4954 30.3794 32.2241 24.5155 25.0474 26.1697L18.4765 27.6843C17.7136 27.8602 16.8679 27.2958 16.5897 26.425L16.588 26.4197Z",fill:"#222222"}),(0,r.jsx)("path",{d:"M3.41035 11.072L2.70878 17.922L9.82641 23.1112L9.12485 29.9612L16.2626 35.1521L15.5332 41.9784L22.6664 47.1703L21.9447 54.0186L29.0824 59.2095L28.3627 66.0632L35.4985 71.2487L34.2855 76.6377L-24.5796 89.0953L-43.1005 21.7587L3.41035 11.072Z",fill:"#F9F7F7"}),(0,r.jsx)("path",{d:"M-25.9307 89.3664C-25.948 89.317 -25.9634 89.273 -25.9761 89.2227L-44.49 21.9067C-44.7251 21.0541 -44.3749 20.2841 -43.6792 20.1251L2.81619 9.44714C3.30035 9.33877 3.8558 9.54557 4.28248 9.99782C4.70461 10.451 4.93281 11.075 4.87533 11.6357L4.2937 17.32L10.408 21.7818C11.0085 22.2149 11.3606 22.9837 11.289 23.6712L10.7074 29.3555L16.8398 33.8134C17.4358 34.2475 17.7989 35.0199 17.7201 35.7148L17.1133 41.3691L23.2393 45.8225C23.8353 46.2566 24.1984 47.029 24.1222 47.7174L23.527 53.4046L29.6549 57.8635C30.2509 58.2976 30.614 59.07 30.5378 59.7584L29.9426 65.4456L36.0686 69.899C36.7292 70.3784 37.0841 71.2528 36.9228 71.9769L35.7129 77.3578C35.6019 77.8354 35.286 78.1735 34.8499 78.2658L-23.9932 90.7196C-24.3748 90.8004 -24.8009 90.6842 -25.179 90.3986C-25.5164 90.1456 -25.7839 89.7717 -25.9261 89.3654L-25.9307 89.3664ZM-41.2746 23.1192L-23.6706 87.1247L33.001 75.1305L33.7589 71.7619L27.7622 67.3991C27.1662 66.965 26.805 66.1981 26.8793 65.5042L27.4745 59.817L21.3466 55.3581C20.7506 54.924 20.3875 54.1516 20.4637 53.4632L21.059 47.776L14.9329 43.3226C14.337 42.8885 13.9739 42.1161 14.0527 41.4212L14.6575 35.7614L8.53152 31.308C7.93554 30.8739 7.57893 30.1061 7.64864 29.4131L8.23026 23.7288L2.11595 19.267C1.51997 18.8329 1.16335 18.0651 1.23498 17.3776L1.65569 13.2548L-41.2791 23.1201L-41.2746 23.1192Z",fill:"#222222"}),(0,r.jsx)("path",{d:"M37.8053 91.2761L33.193 86.3418L37.0017 83.0926L32.5986 79.1681L36.5063 75.2663L32.1717 71.3264L-31.5272 85.6963L-18.9132 104.071L37.8053 91.2761Z",fill:"#FF9C9F"}),(0,r.jsx)("path",{d:"M-32.834 86.0523C-32.9551 85.6902 -32.9865 85.3145 -32.9168 84.9745C-32.8072 84.4246 -32.4576 84.0284 -31.9797 83.9215L31.4682 69.7229C31.9217 69.6214 32.4396 69.791 32.86 70.1704L37.1775 74.0694C37.591 74.444 37.8742 74.9774 37.9466 75.5385C38.017 76.0936 37.8718 76.5997 37.5487 76.9185L35.0018 79.441L37.652 81.7865C38.089 82.1688 38.3844 82.7384 38.4413 83.316C38.5031 83.8924 38.3307 84.4111 37.9674 84.7194L35.5395 86.7677L38.5941 90.0087C39.0606 90.4946 39.3114 91.1975 39.2448 91.8287C39.1781 92.4598 38.8097 92.925 38.2831 93.0429L-18.2121 105.686C-18.8167 105.821 -19.5133 105.477 -19.96 104.831L-32.5176 86.6691C-32.6517 86.4721 -32.7585 86.2624 -32.8312 86.0452L-32.834 86.0523ZM31.9538 73.4934L-28.6565 87.057L-18.4197 101.853L34.4528 90.0208L32.1613 87.5885C31.7759 87.1883 31.5463 86.6429 31.5064 86.1004C31.4733 85.5629 31.6481 85.0827 31.9882 84.799L34.2513 82.8849L31.7195 80.6426C31.3011 80.2692 31.002 79.7198 30.9337 79.1708C30.8564 78.6107 31.0044 78.0976 31.3275 77.7787L33.89 75.2398L31.9518 73.4874L31.9538 73.4934Z",fill:"#222222"})]})},at=function(t){var e=t.className,n=t.title;return(0,r.jsxs)("svg",{className:e,width:"86",height:"123",viewBox:"0 0 86 123",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("title",{children:n}),(0,r.jsx)("path",{d:"M82.2148 53.3186C79.1806 51.4317 75.9115 49.9746 72.5452 48.9813C78.6368 46.6095 80.9036 40.9363 78.465 33.6684C77.1565 29.7685 73.9359 24.3128 65.94 20.6325C62.592 19.1288 58.7545 18.4257 54.3291 18.6072L53.4567 16.0073C52.9606 14.3403 52.0124 12.8327 50.7109 11.5902C48.6798 9.67984 45.9435 8.86909 43.4074 9.40848C39.8098 10.1737 37.0651 13.6695 38.8098 18.8693L39.8606 22.001C35.2241 24.1899 31.5154 27.6376 29.0092 32.0323C25.7397 37.9187 25.384 44.7681 27.9217 52.3315C30.0233 58.5949 33.7544 63.6888 38.9377 67.4609C41.736 69.398 44.7102 70.9178 47.8213 72.092L47.7623 72.1045C43.8499 72.8734 40.6894 75.3181 39.0856 78.8245C37.58 82.2467 37.4725 86.2578 38.8202 90.0861C40.2477 94.3405 43.8039 100.231 52.626 103.926C57.1356 105.879 62.3699 106.601 68.1126 106.203L69.5797 110.575L69.7378 110.858C71.4399 114.801 75.9699 117.003 79.9204 115.783C83.4581 114.651 85.2978 111.094 84.1667 107.346L82.422 102.147C87.8435 99.2842 91.7088 95.5501 93.8998 90.9692C96.756 84.9808 96.8747 77.802 94.0793 69.4704C91.6015 62.2741 87.6335 56.8508 82.2148 53.3186Z",fill:"#222222",stroke:"#222222",strokeWidth:"0.54",strokeMiterlimit:"10"}),(0,r.jsx)("path",{d:"M80.3618 50.6047C77.3274 48.7178 74.0581 47.2607 70.7506 46.2548C76.8426 43.8829 79.1096 38.2096 76.6708 30.9414C75.3622 27.0414 72.1413 21.5856 64.1449 17.9052C60.7968 16.4015 56.9589 15.6983 52.5332 15.8799L51.6608 13.2799C51.1647 11.6128 50.2164 10.1052 48.9148 8.86261C46.9034 7.01132 44.0881 6.154 41.6307 6.73995C38.0328 7.50515 35.2879 11.0011 37.0327 16.201L38.0836 19.3328C33.4468 21.5218 29.7379 24.9695 27.2905 29.3518C23.9816 35.31 23.6259 42.1596 26.144 49.6641C28.2457 55.9277 31.977 61.0217 37.1606 64.794C39.9591 66.7311 42.9336 68.2509 46.0448 69.4252L45.9858 69.4377C42.0732 70.2065 38.9124 72.6514 37.3086 76.1578C35.8029 79.5801 35.6954 83.5914 37.0823 87.3481C38.5297 91.6617 42.0861 97.5526 50.889 101.188C55.399 103.141 60.5746 103.876 66.3767 103.465L67.8439 107.838L68.002 108.121C69.7041 112.064 74.2344 114.266 78.1853 113.046C81.7232 111.913 83.563 108.357 82.4318 104.609L80.687 99.4089C86.1089 96.5465 89.9744 92.8123 92.1656 88.2313C95.0809 82.2301 95.1209 75.0046 92.3451 66.7319C89.7492 59.5604 85.7809 54.137 80.3618 50.6047Z",fill:"#F0CBA3",stroke:"#222222",strokeWidth:"0.93",strokeMiterlimit:"10"})]})},ot=function(t){var e=t.className,n=t.title;return(0,r.jsxs)("svg",{width:91,height:105,viewBox:"0 0 91 105",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,r.jsx)("title",{children:n}),(0,r.jsxs)("g",{clipPath:"url(#clip0_5173_27913)",children:[(0,r.jsx)("path",{d:"M78.022 72.07l2.85 5.23-5.833 3.178-2.767-5.078c-5.027 2.15-9.473 2.41-13.628 1.235l.49-9.31a14.3 14.3 0 0011.2-.944c2.672-1.456 3.526-3.74 2.514-5.596-1.218-2.236-3.98-2.108-7.743-1.58-5.708.9-13.241 2.302-17.558-5.618-2.85-5.229-1.952-11.91 3.847-16.496l-2.891-5.305 5.832-3.179 2.85 5.23c3.614-1.43 7.598-1.733 11.413-.913l-.447 8.943a13.28 13.28 0 00-9.538 1.217c-2.559 1.394-3.098 3.36-2.19 5.027 1.095 2.008 3.76 1.883 7.636 1.294 5.73-.862 13.332-1.958 17.462 5.62 3.305 6.064 2.183 12.425-3.499 17.045z",fill:"#222"}),(0,r.jsx)("path",{d:"M74.874 71.132l2.85 5.23-5.833 3.178-2.767-5.077c-5.027 2.15-9.473 2.41-13.628 1.235l.51-9.273a14.3 14.3 0 0011.2-.943c2.672-1.456 3.526-3.74 2.514-5.597-1.218-2.236-3.979-2.107-7.742-1.58-5.73.862-13.28 2.323-17.596-5.597-2.85-5.23-1.952-11.91 3.884-16.517l-2.912-5.343 5.833-3.178 2.85 5.229c3.614-1.43 7.597-1.733 11.412-.912l-.446 8.942a13.28 13.28 0 00-9.538 1.217c-2.559 1.395-3.099 3.36-2.19 5.027 1.095 2.009 3.76 1.883 7.636 1.294 5.692-.84 13.332-1.957 17.462 5.621 3.342 6.042 2.183 12.424-3.499 17.044z",fill:"#6EA28F",stroke:"#222",strokeWidth:.93,strokeMiterlimit:10})]}),(0,r.jsxs)("g",{clipPath:"url(#clip1_5173_27913)",children:[(0,r.jsx)("path",{d:"M108.022 51.07l2.85 5.23-5.833 3.178-2.767-5.078c-5.027 2.15-9.473 2.41-13.628 1.235l.49-9.31a14.3 14.3 0 0011.2-.944c2.672-1.456 3.526-3.74 2.514-5.596-1.218-2.236-3.98-2.108-7.743-1.58-5.708.9-13.241 2.302-17.558-5.618-2.85-5.229-1.952-11.91 3.847-16.496l-2.891-5.305 5.832-3.179 2.85 5.23c3.614-1.43 7.598-1.733 11.413-.913l-.447 8.943a13.28 13.28 0 00-9.538 1.217c-2.558 1.395-3.098 3.36-2.19 5.027 1.095 2.008 3.76 1.883 7.636 1.294 5.73-.862 13.332-1.958 17.462 5.62 3.304 6.064 2.183 12.425-3.499 17.045z",fill:"#222"}),(0,r.jsx)("path",{d:"M104.874 50.132l2.85 5.23-5.833 3.178-2.767-5.077c-5.027 2.15-9.473 2.41-13.628 1.234l.51-9.272a14.3 14.3 0 0011.2-.943c2.672-1.456 3.526-3.74 2.514-5.597-1.218-2.236-3.979-2.107-7.742-1.58-5.73.862-13.28 2.323-17.596-5.597-2.85-5.23-1.952-11.91 3.884-16.517l-2.912-5.343 5.833-3.179 2.85 5.23c3.614-1.43 7.597-1.733 11.412-.912l-.446 8.942a13.28 13.28 0 00-9.538 1.217c-2.559 1.395-3.099 3.36-2.19 5.027 1.094 2.009 3.76 1.883 7.636 1.294 5.692-.84 13.332-1.957 17.462 5.621 3.342 6.042 2.183 12.424-3.499 17.044z",fill:"#6EA28F",stroke:"#222",strokeWidth:.93,strokeMiterlimit:10})]}),(0,r.jsxs)("g",{clipPath:"url(#clip2_5173_27913)",children:[(0,r.jsx)("path",{d:"M44.022 89.07l2.85 5.23-5.833 3.178-2.767-5.078c-5.027 2.15-9.473 2.41-13.628 1.235l.49-9.31a14.3 14.3 0 0011.2-.944c2.672-1.456 3.526-3.74 2.514-5.597-1.218-2.235-3.98-2.107-7.743-1.58-5.708.9-13.241 2.303-17.558-5.617-2.85-5.229-1.952-11.91 3.847-16.496l-2.891-5.305 5.832-3.179 2.85 5.23c3.614-1.43 7.598-1.733 11.413-.913l-.447 8.943a13.28 13.28 0 00-9.538 1.217c-2.558 1.394-3.098 3.36-2.19 5.027 1.095 2.008 3.76 1.883 7.636 1.294 5.73-.862 13.332-1.958 17.462 5.62 3.304 6.063 2.183 12.425-3.499 17.045z",fill:"#222"}),(0,r.jsx)("path",{d:"M40.874 88.132l2.85 5.23-5.833 3.178-2.767-5.077c-5.027 2.15-9.473 2.41-13.628 1.235l.51-9.273a14.3 14.3 0 0011.2-.943c2.672-1.456 3.526-3.74 2.514-5.597-1.218-2.236-3.979-2.107-7.742-1.58-5.73.862-13.28 2.323-17.596-5.597-2.85-5.23-1.952-11.91 3.884-16.517l-2.912-5.343 5.833-3.178 2.85 5.229c3.614-1.43 7.597-1.733 11.412-.912l-.446 8.942a13.28 13.28 0 00-9.538 1.217c-2.559 1.395-3.099 3.36-2.19 5.028 1.095 2.008 3.76 1.882 7.636 1.293 5.691-.84 13.332-1.957 17.462 5.621 3.342 6.043 2.183 12.424-3.499 17.044z",fill:"#6EA28F",stroke:"#222",strokeWidth:.93,strokeMiterlimit:10})]}),(0,r.jsxs)("defs",{children:[(0,r.jsx)("clipPath",{id:"clip0_5173_27913",children:(0,r.jsx)("path",{fill:"#fff",transform:"rotate(-28.59 89.195 -48.322)",d:"M0 0H33V58H0z"})}),(0,r.jsx)("clipPath",{id:"clip1_5173_27913",children:(0,r.jsx)("path",{fill:"#fff",transform:"rotate(-28.59 62.987 -117.691)",d:"M0 0H33V58H0z"})}),(0,r.jsx)("clipPath",{id:"clip2_5173_27913",children:(0,r.jsx)("path",{fill:"#fff",transform:"rotate(-28.59 105.554 26.895)",d:"M0 0H33V58H0z"})})]})]})},ct=function(t){var e=t.className,n=t.title;return(0,r.jsxs)("svg",{width:49,height:231,viewBox:"0 0 49 231",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,r.jsx)("title",{children:n}),(0,r.jsx)("path",{d:"M-22.09 118.223l-3.12-16.053c-1.258-6.615 2.795-12.9 9.13-13.957C-9.747 87.156-3.534 91.6-2.276 98.215L.75 113.866",stroke:"#222",strokeWidth:1.45,strokeMiterlimit:10}),(0,r.jsx)("path",{d:"M27.545 112.716l-78.013 13.407 19.994 101.122 78.012-13.407-19.993-101.122zM18.113 24.282l-79.77 5.646 5.75 55.955 79.77-5.646-5.75-55.955z",fill:"#000",stroke:"#222",strokeWidth:1.45,strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M22.314 108.376l-78.012 13.407 19.993 101.122 78.013-13.406-19.994-101.123z",fill:"#F4A2A2",stroke:"#222",strokeWidth:1.45,strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M12.89 20.263l-79.77 5.645 5.751 55.955 79.77-5.645-5.75-55.955z",fill:"#79A191",stroke:"#222",strokeWidth:1.45,strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M16.403 109.199L34.41 209.794M3.509 111.461l18.006 100.595M-9.056 114.109L8.948 214.625",stroke:"#fff",strokeWidth:4.31,strokeMiterlimit:10}),(0,r.jsx)("path",{d:"M22.314 108.376l-78.012 13.407 19.993 101.122 78.013-13.406-19.994-101.123z",stroke:"#222",strokeWidth:1.45,strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M-18.765 34.946c-3.344 2.708-4.421 7.563-2.596 11.732 2.052 4.085 6.412 6.364 10.618 5.5-.984 5.175-3.304 9.886-6.829 13.645 6.62-.495 12.367-4.607 14.995-10.935C-.37 49.453-.766 41.312-4.71 36.446c-3.2-3.827-10.483-4.291-14.055-1.5z",fill:"#B4DBC8"})]})},st=function(t){var e=t.className,n=t.title;return(0,r.jsxs)("svg",{className:e,width:"71",height:"80",viewBox:"0 0 71 80",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("title",{children:n}),(0,r.jsx)("path",{d:"M57.5738 34.8247L-1.13379 19.4531L-15.9616 63.7258L42.746 79.0974L57.5738 34.8247Z",fill:"black",stroke:"#222222",strokeWidth:"1.45",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M10.2451 26.601L14.5039 14.0005C16.2926 8.80714 21.5116 5.57132 26.2181 6.83241C30.9246 8.0935 33.3414 13.2701 31.6082 18.4817L27.4706 30.7737",stroke:"#222222",strokeWidth:"1.45",strokeMiterlimit:"10"}),(0,r.jsx)("path",{d:"M16.9894 28.9971L21.2476 16.3979C23.0361 11.2051 28.2301 8.03131 32.9046 9.21242C37.6105 10.4734 40.027 15.6495 38.294 20.8605L34.1569 33.1512",stroke:"#222222",strokeWidth:"1.45",strokeMiterlimit:"10"}),(0,r.jsx)("path",{d:"M54.8833 30.3951L-3.82422 15.0234L-18.652 59.2961L40.0556 74.6677L54.8833 30.3951Z",fill:"#79A191",stroke:"#222222",strokeWidth:"1.45",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M9.44595 29.3386C6.1632 30.6084 3.79429 33.9741 3.70173 37.5352C3.7977 41.0891 6.145 43.7941 9.40191 44.1734C6.99212 47.8019 3.79326 50.7569 0.0586811 52.707C4.87276 54.0123 10.3181 52.2788 14.27 48.19C17.6294 44.6662 20.0471 38.4848 18.8737 33.8863C17.9378 30.2636 12.8933 28.1229 9.44595 29.3386Z",fill:"#B4DBC8"}),(0,r.jsx)("path",{d:"M27.4967 34.6277C24.2379 35.8363 21.869 39.202 21.7764 42.763C21.8724 46.3169 24.1958 49.0831 27.4527 49.4624C25.0429 53.091 21.8441 56.0459 18.1095 57.9961C22.9475 59.2401 28.3689 57.5679 32.3208 53.479C35.6802 49.9553 38.0978 43.7738 36.9245 39.1753C35.9338 35.5347 30.9441 33.412 27.4967 34.6277Z",fill:"#B4DBC8"})]})},Mt=function(t){var e=t.className,n=t.title;return(0,r.jsxs)("svg",{width:96,height:107,viewBox:"0 0 96 107",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,r.jsx)("title",{children:n}),(0,r.jsxs)("g",{clipPath:"url(#clip0_5082_28265)",children:[(0,r.jsx)("path",{d:"M68.43 72.602l1.239 4.716-5.26 1.383-1.204-4.58c-4.418.723-8.003.057-11.076-1.693l2.217-7.305a11.71 11.71 0 009.089 1.448c2.41-.633 3.536-2.28 3.096-3.955-.53-2.017-2.75-2.456-5.845-2.776-4.714-.405-10.977-.769-12.854-7.911-1.24-4.717.786-9.852 6.295-12.359l-1.257-4.784 5.26-1.383 1.24 4.717c3.153-.427 6.38.114 9.25 1.515l-2.11 7.02a10.873 10.873 0 00-7.82-.904c-2.308.607-3.123 2.063-2.727 3.567.475 1.81 2.618 2.234 5.816 2.527 4.723.44 10.981 1.06 12.777 7.895 1.437 5.468-.703 10.305-6.126 12.862z",fill:"#222"}),(0,r.jsx)("path",{d:"M66.111 71.239l1.24 4.716-5.261 1.382-1.203-4.58c-4.418.723-8.004.058-11.076-1.693l2.226-7.27a11.71 11.71 0 009.088 1.448c2.41-.633 3.537-2.28 3.097-3.955-.53-2.017-2.75-2.456-5.845-2.776-4.723-.44-11.012-.76-12.888-7.903-1.24-4.716.786-9.851 6.329-12.367l-1.266-4.819 5.26-1.382 1.24 4.716c3.153-.426 6.379.114 9.25 1.516l-2.11 7.02a10.873 10.873 0 00-7.82-.904c-2.308.606-3.123 2.063-2.728 3.566.476 1.812 2.619 2.235 5.816 2.527 4.69.449 10.982 1.06 12.778 7.896 1.47 5.459-.703 10.304-6.127 12.862z",fill:"#6EA28F",stroke:"#222",strokeWidth:.93,strokeMiterlimit:10})]}),(0,r.jsxs)("g",{clipPath:"url(#clip1_5082_28265)",children:[(0,r.jsx)("path",{d:"M96.398 61.798l1.24 4.716-5.261 1.382-1.203-4.58c-4.418.723-8.004.057-11.076-1.693l2.217-7.305a11.71 11.71 0 009.088 1.449c2.41-.634 3.537-2.281 3.097-3.956-.53-2.016-2.75-2.456-5.845-2.775-4.714-.406-10.978-.77-12.854-7.912-1.24-4.716.785-9.852 6.295-12.359l-1.257-4.784 5.26-1.382 1.24 4.716c3.153-.427 6.379.114 9.25 1.515l-2.11 7.021a10.873 10.873 0 00-7.82-.904c-2.308.606-3.123 2.062-2.728 3.566.476 1.811 2.62 2.235 5.816 2.527 4.724.44 10.982 1.06 12.778 7.895 1.437 5.468-.703 10.305-6.127 12.863z",fill:"#222"}),(0,r.jsx)("path",{d:"M94.08 60.434l1.24 4.716-5.261 1.383-1.204-4.58c-4.418.722-8.003.057-11.075-1.693l2.226-7.27a11.709 11.709 0 009.088 1.447c2.41-.633 3.537-2.28 3.097-3.955-.53-2.016-2.75-2.456-5.845-2.775-4.724-.44-11.012-.76-12.889-7.903-1.239-4.716.786-9.852 6.33-12.368l-1.267-4.818 5.261-1.382 1.24 4.716c3.152-.427 6.379.114 9.25 1.515l-2.11 7.02a10.873 10.873 0 00-7.82-.904c-2.308.607-3.123 2.063-2.728 3.567.476 1.81 2.619 2.234 5.816 2.527 4.69.448 10.982 1.06 12.778 7.895 1.47 5.46-.703 10.305-6.127 12.862z",fill:"#6EA28F",stroke:"#222",strokeWidth:.93,strokeMiterlimit:10})]}),(0,r.jsxs)("g",{clipPath:"url(#clip2_5082_28265)",children:[(0,r.jsx)("path",{d:"M38.065 79.442l1.24 4.716-5.261 1.382-1.203-4.58c-4.418.723-8.004.058-11.076-1.692l2.217-7.305a11.709 11.709 0 009.088 1.448c2.41-.633 3.537-2.28 3.097-3.956-.53-2.016-2.75-2.456-5.845-2.775-4.714-.405-10.978-.769-12.854-7.912-1.24-4.716.785-9.851 6.295-12.358l-1.257-4.784 5.26-1.383 1.24 4.716c3.153-.426 6.379.115 9.25 1.516l-2.11 7.02a10.873 10.873 0 00-7.82-.904c-2.308.606-3.123 2.063-2.728 3.566.476 1.812 2.62 2.235 5.816 2.528 4.724.44 10.982 1.06 12.778 7.895 1.437 5.468-.703 10.305-6.127 12.862z",fill:"#222"}),(0,r.jsx)("path",{d:"M35.747 78.079l1.24 4.716-5.261 1.382-1.204-4.58c-4.418.723-8.003.058-11.075-1.693l2.226-7.27a11.709 11.709 0 009.088 1.448c2.41-.633 3.537-2.281 3.097-3.956-.53-2.016-2.75-2.456-5.845-2.775-4.724-.44-11.012-.76-12.889-7.903-1.239-4.716.786-9.851 6.33-12.367l-1.267-4.819 5.261-1.382 1.24 4.716c3.152-.426 6.378.114 9.25 1.515l-2.11 7.021a10.873 10.873 0 00-7.82-.904c-2.308.606-3.123 2.062-2.728 3.566.476 1.811 2.619 2.235 5.816 2.527 4.69.449 10.982 1.06 12.778 7.896 1.47 5.459-.704 10.304-6.127 12.862z",fill:"#6EA28F",stroke:"#222",strokeWidth:.93,strokeMiterlimit:10})]}),(0,r.jsxs)("defs",{children:[(0,r.jsx)("clipPath",{id:"clip0_5082_28265",children:(0,r.jsx)("path",{fill:"#fff",transform:"rotate(-14.721 159.205 -138.257)",d:"M0 0H27.0202V47.4901H0z"})}),(0,r.jsx)("clipPath",{id:"clip1_5082_28265",children:(0,r.jsx)("path",{fill:"#fff",transform:"rotate(-14.721 131.368 -251.916)",d:"M0 0H27.0202V47.4901H0z"})}),(0,r.jsx)("clipPath",{id:"clip2_5082_28265",children:(0,r.jsx)("path",{fill:"#fff",transform:"rotate(-14.721 170.497 -17.308)",d:"M0 0H27.0202V47.4901H0z"})})]})]})},lt=function(t){var e=t.className,n=t.title;return(0,r.jsxs)("svg",{width:70,height:74,viewBox:"0 0 70 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,r.jsx)("title",{children:n}),(0,r.jsx)("path",{d:"M18.865 23.168c-.616-.043-1.061-.599-.993-1.24l.588-5.526c.758-7.125 7.6-13.524 15.25-14.272 4-.383 7.675.844 10.08 3.378 2.108 2.227 3.08 5.26 2.731 8.543l-.477 4.488c-.069.641-.625 1.128-1.241 1.085-.616-.043-1.061-.6-.993-1.24l.477-4.489c.276-2.59-.483-4.978-2.13-6.718-1.952-2.06-4.997-3.048-8.328-2.727-6.598.645-12.49 6.072-13.131 12.107l-.588 5.527c-.069.641-.625 1.128-1.241 1.085h-.004z",fill:"#222"}),(0,r.jsx)("path",{d:"M7.942 23.952l3.8 2.72 4.643-4.003 3.8 2.72 4.648-4.017 3.78 2.735 4.648-4.015 3.795 2.736 4.648-4.019 3.799 2.736 4.644-4.018 2.837 2.664-6.356 50.603L3.408 64.2l4.534-40.249z",fill:"#F9F7F7"}),(0,r.jsx)("path",{d:"M46.481 71.965a.753.753 0 01-.093-.01l-43.23-6.593c-.547-.083-.912-.604-.844-1.206l4.54-40.254a1.3 1.3 0 01.656-.989c.36-.191.773-.175 1.085.048l3.155 2.258 3.992-3.44c.39-.339.916-.375 1.297-.101l3.156 2.258 3.994-3.455c.389-.335.92-.38 1.303-.097l3.133 2.268 3.99-3.45c.389-.336.92-.38 1.3-.102l3.155 2.27 3.993-3.451c.389-.336.92-.38 1.3-.102l3.155 2.27 3.99-3.45c.43-.373 1.017-.378 1.4-.022l2.835 2.66c.25.24.373.597.326.972l-6.358 50.609c-.04.328-.206.63-.459.839a1.11 1.11 0 01-.77.266v.004zm-41.88-8.77l41.104 6.269 6.123-48.741-1.775-1.666-3.907 3.377c-.389.336-.916.38-1.3.101l-3.155-2.27-3.993 3.452c-.39.335-.92.379-1.3.101l-3.155-2.27L29.253 25c-.389.336-.92.38-1.303.098l-3.137-2.27-3.99 3.452c-.388.335-.915.376-1.3.101l-3.156-2.258-3.992 3.44c-.388.335-.915.375-1.296.101l-2.29-1.637L4.601 63.2v-.004z",fill:"#222"}),(0,r.jsx)("path",{d:"M65.248 22.745l-3.276 1.968-.803-3.636-2.75 2.095-1.096-3.886-2.745 2.04-5.722 49.832 11.297-4.043 5.095-44.37z",fill:"#FF9C9F"}),(0,r.jsx)("path",{d:"M48.636 72.327a.868.868 0 01-.545-.24c-.248-.232-.372-.595-.328-.97l5.776-49.88a1.29 1.29 0 01.499-.882l2.78-2.043c.266-.195.585-.252.874-.148a.978.978 0 01.597.671l.726 2.545 1.683-1.267c.275-.21.614-.266.908-.147.296.116.515.385.594.733l.517 2.327 2.203-1.31a.898.898 0 011.023.034c.296.224.45.62.403 1.034l-5.144 44.412c-.055.475-.363.884-.77 1.028l-11.43 4.045a.95.95 0 01-.369.054l.003.004zm6.85-50.393l-5.518 47.648 9.313-3.3 4.813-41.565-1.653.983a.915.915 0 01-.861.06 1.003 1.003 0 01-.542-.71l-.484-2.17-1.609 1.21c-.267.199-.596.263-.88.16-.29-.1-.511-.35-.603-.672l-.73-2.56-1.25.916h.004z",fill:"#222"})]})};function jt(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Nt(t){var e=t.styles;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(st,{className:e.mobileBottomLeft,title:"Shopping Bag"}),(0,r.jsx)(Mt,{className:e.mobileBottomRight,title:"Money Signs"}),(0,r.jsx)(lt,{className:e.mobileTopRight,title:"Money Signs"}),(0,r.jsx)(ut,{className:e.desktopTopLeft,title:"Top Left Decoration"}),(0,r.jsx)(ct,{className:e.desktopBottomLeft,title:"Bottom Left Decoration"}),(0,r.jsx)(at,{className:e.desktopTopRight,title:"Top Right Decoration"}),(0,r.jsx)(ot,{className:e.desktopBottomRight,title:"Bottom Right Decoration"})]})}function gt(t){var e=t.item,n=t.index,i=t.category,u=t.styles,o=t.imageLayout,c=void 0===o?"stacked":o,s=t.imageSize,l=void 0===s?"big":s,j=(0,a.useContext)(M.Z).destination,g=(0,S.p)(e.url)?{target_content_type:"buzz",target_content_id:e.id}:{target_content_url:e.url,target_content_type:"url",target_content_id:e.url},d=(0,a.useMemo)((function(){return function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){jt(t,e,n[e])}))}return t}({},e,{item_name:e.id,item_type:0===n?"splash":"card",position_in_subunit:n,index:n,category:i},g,{data_source_name:"site_component_api"})}),[e,n,i,g]),y=N(d);return(0,r.jsx)("article",{children:(0,r.jsxs)("a",{href:e.url,ref:y,className:(0,X.Z)(u[c],u.shoppingItemLink,u[j]),children:[(0,r.jsx)("div",{className:(0,X.Z)(u.imageWrap,u[l]),children:(0,r.jsx)(O.lS,{src:e.image,alt:e.image_alt_text})}),(0,r.jsx)("span",{className:u.text,children:e.name})]})})}function dt(t){var e=t.item,n=t.index,i=t.category,u=t.styles;return(0,r.jsx)("li",{children:(0,r.jsx)(gt,{item:e,index:n,category:i,styles:u})})}var yt=function(t){var e=t.isShoppingDesign,n=void 0!==e&&e,i=(0,a.useContext)(s.Z).buzz,u=void 0===i?{}:i,o=(0,a.useContext)(L.Z).getFeatureFlagValue,c=b({buzz:u}),M=(null===c||void 0===c?void 0:c.category)||"",l=o("prime-day-bpage-recirc")||!1?"Shop More Prime Day":"Your New Favorite Thing",j=u.isShopping||n?tt():nt();return c?(0,r.jsxs)("div",{className:"".concat(j.fullWidth," ").concat(n?j.shoppingStyle:""),children:[(u.isShopping||n)&&(0,r.jsx)(Nt,{styles:j}),(0,r.jsxs)("section",{className:(0,X.Z)(j.shoppingWrapper,(0,rt.G)(u)&&j.bottomShoppingList),children:[(0,r.jsx)(it.Z,{className:"new-shopping-package-title",id:"new-shopping-package-title",children:l}),(0,r.jsx)("ul",{className:j.gridNarrow,children:c.results.slice(0,6).map((function(t,e){return(0,r.jsx)(dt,{item:t,index:e+1,category:M,styles:j},t.id)}))})]})]}):null};function Lt(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Tt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Lt(t,e,n[e])}))}return t}function zt(t,e){if(null==t)return{};var n,i,r=function(t,e){if(null==t)return{};var n,i,r={},u=Object.keys(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function mt(t){var e=t.isNewDesign,n=t.isShoppingDesign,i=void 0!==n&&n,a=zt(t,["isNewDesign","isShoppingDesign"]);return(0,r.jsx)(u.Z,{children:e?(0,r.jsx)(yt,{isShoppingDesign:i}):(0,r.jsx)(K,Tt({},a))})}},61813:function(t,e,n){"use strict";n.d(e,{Z:function(){return X}});var i=n(52322),r=n(88163),u=n(16938),a=n(21871),o=n(24678),c=n(13980),s=n.n(c),M=n(2784),l=n(83661),j=n(10097),N=n(52592),g=n(99112),d=n(71031),y=function(t){var e=t.className,n=t.title;return(0,i.jsxs)("svg",{className:e,width:"15",height:"20",viewBox:"0 0 15 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsx)("title",{children:n}),(0,i.jsx)("path",{d:"M11.6364 6.90906H12.8182C13.1316 6.90906 13.4322 7.03357 13.6539 7.2552C13.8755 7.47684 14 7.77744 14 8.09088V17.5454C14 17.8589 13.8755 18.1595 13.6539 18.3811C13.4322 18.6027 13.1316 18.7272 12.8182 18.7272H2.18182C1.86838 18.7272 1.56778 18.6027 1.34615 18.3811C1.12451 18.1595 1 17.8589 1 17.5454V8.09088C1 7.77744 1.12451 7.47684 1.34615 7.2552C1.56778 7.03357 1.86838 6.90906 2.18182 6.90906H3.36364",stroke:"#5246F5",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,i.jsx)("path",{d:"M7.5 1V9.27273",stroke:"#5246F5",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,i.jsx)("path",{d:"M4.54541 3.95455L7.49996 1L10.4545 3.95455",stroke:"#5246F5",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})},L=n(29583),T=function(t){var e=t.className,n=void 0===e?"":e;return(0,i.jsx)("svg",{className:n,fill:"#EB5369",width:"15",height:"15",viewBox:"0 0 15 15",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)("path",{d:"M0 2.7907C0 1.24944 1.22104 0 2.72727 0H12.2727C13.779 0 15 1.24944 15 2.7907L15 8.37209C15 9.91335 13.779 11.1628 12.2727 11.1628H10.9091V15L4.77274 11.1628H2.72727C1.22104 11.1628 0 9.91335 0 8.37209C0 6.51163 0 4.65116 0 2.7907Z",fill:"#EB5369"})})},z=n(42494),m=n(66376),p=n(81298),I=n(30353),f=n(41332),D=n(64435),x=n(98195),b=n.n(x),O=function(t){var e,n=t.display_name,r=t.position,u=t.slug,a=(0,M.useContext)(f.Z).buzz,o=void 0===a?{}:a,c=(0,D.R)({unit_type:"top"===r?"buzz_head":"buzz_bottom",unit_name:o.id,item_type:"text",item_name:u,target_content_type:"feed",target_content_id:8}),s="".concat(null===(e=I.destinations.buzzfeed)||void 0===e?void 0:e.base_url,"/topic/").concat(u);return(0,i.jsx)("div",{className:"".concat(b().topicPageLink," ").concat(b()[r]),children:(0,i.jsxs)("a",{ref:c,href:s,children:[(0,i.jsx)(p.BN,{className:b().caret}),(0,i.jsxs)("div",{children:["Go Back to",(0,i.jsx)("span",{className:b().pageName,children:n})]})]})})},A=n(53407),h=n(4366),_=n(9100),S=n.n(_);function w(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function E(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){w(t,e,n[e])}))}return t}var v=function(){var t=(0,M.useContext)(f.Z);return(0,i.jsx)("button",{className:S().ctaButton,onClick:function(){var e={unit_name:t.id,unit_type:"buzz_head",item_type:"button"};if((0,A.bG)()){var n=document.getElementById("comments");if(n){window.scrollTo({top:n.getBoundingClientRect().top+scrollY-110,left:0,behavior:"smooth"});var i=document.getElementById("comment-form-textarea");i&&i.focus(),(0,h.bC)(t,E({},e,{item_name:"comment-cta",action_value:"comment-cta",action_type:"navigate"}))}}else(0,h.TW)(t,E({},e,{item_name:"sign_in_to_comment",target_content_id:"sign_in",target_content_type:"auth"})),(0,A.nH)()},children:"Start The Discussion"})},C=n(30729),k=n(91904),Q=n(98570),U=n.n(Q);function Y(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function P(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Y(t,e,n[e])}))}return t}var Z=function(t){var e,n=t.alt_text,r=t.attribution,u=t.buzz,a=t.id,o=t.index,c=t.images,s=function(t){return P({},t,{url:(0,C.Z)(t.url,700)})},M=function(t){return{width:t.width,height:t.height}},l=s(null!==(e=c.standard)&&void 0!==e?e:c.original),j=c.mobile?s(c.mobile):l,N=(0,k.J)(u,o);return(0,i.jsxs)("figure",{className:U().discussionImage,ref:N,children:[(0,i.jsx)("div",{id:a,className:"subbuzz-anchor"}),(0,i.jsxs)("picture",{children:[(0,i.jsx)("source",P({media:"(min-width: 52rem)",srcSet:"".concat(l.url," 1600w")},M(l))),(0,i.jsx)("source",P({srcSet:"".concat(j.url," 800w")},M(j))),(0,i.jsx)("img",P({src:l.url,alt:n},M(l)))]}),(0,i.jsx)("div",{className:U().attribution,dangerouslySetInnerHTML:{__html:r}})]})},B=n(70211),R=n(64994),W=n(22813),G=n(46811),F=n(48492),H=n(70755),J=n(70733),V=n.n(J);function q(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function K(t){var e,n,c,s,p,I,f=t.buzz,D=t.subbuzzData,x=(0,M.useRef)(null),b=(0,r.Z)({once:!0}),A=b.isIntersecting,_=b.setObservable,S=(0,M.useContext)(g.Z).destination,w=(0,u.Z)("(max-width:500px)"),E=(0,M.useState)(null),C=E[0],k=E[1],Q=(0,M.useState)(),U=Q[0],Y=Q[1],P=(0,M.useState)(!1),J=P[0],K=P[1],X=null!==(I=f.longform_custom_header)&&void 0!==I?I:f,$=X.title,tt=X.description,et=null===(e=f.sub_buzzes)||void 0===e?void 0:e.filter((function(t){return"image"===t.form})),nt=Number.isInteger(null===D||void 0===D?void 0:D.gateIndex),it=(null===(n=f.relatedTopicFeeds)||void 0===n||null===(c=n.topicPageTagFeeds)||void 0===c?void 0:c.find((function(t){return t.edition===f.country_code&&"bf"===t.domain})))||null;!it&&(null===(s=f.relatedTopicFeeds)||void 0===s||null===(p=s.remainingTagFeeds)||void 0===p?void 0:p.length)&&(it=f.relatedTopicFeeds.remainingTagFeeds.find((function(t){return t.edition===f.country_code&&"bf"===t.domain}))||null),(0,M.useEffect)((function(){var t=window.location.href;(null===navigator||void 0===navigator?void 0:navigator.share)&&navigator.canShare({url:t})&&Y(!0)}),[]);var rt;return(0,M.useEffect)((function(){(0,G.Hd)()&&K(!0)}),[]),rt=J?(0,i.jsx)("div",{className:V().discussionShareNewButton,children:(0,i.jsx)(W.Z,{variant:"standard_small",includeShortTitle:!0,trackingData:{unit_type:"buzz_head",unit_name:f.id,subunit_type:"component",subunit_name:"discussion_question"}})}):w?U&&(0,i.jsx)("button",{onClick:function(){(0,h.bC)(f,{action_type:"share",action_value:"native_share",item_type:"button",item_name:"native_share",unit_type:"buzz_head",unit_name:f.id,subunit_type:"component",subunit_name:"discussion_question"}),navigator.share({title:$,url:window.location.href})},className:V().nativeShare,children:(0,i.jsx)(y,{title:"Share",className:V().nativeShareIcon})}):(0,i.jsx)(F.WX,{buzz:f,type:"pageLevelOutlineThick",position:"top_share_bar"}),(0,i.jsxs)("main",{id:"buzz-content",className:"".concat(V().main," ").concat(V()[S]," ").concat(V().commentsMain," embed-content"),ref:x,children:[(0,i.jsxs)("div",{className:"".concat(V().article," ").concat(V().default," ").concat(V()[S]," embed-post"),children:[(0,i.jsxs)("div",{className:V().content,children:[it&&(0,i.jsx)(O,{display_name:it.display_name,position:"top",slug:it.slug}),!1,(0,i.jsxs)("article",{className:V().discussionTitleWrapper,children:[(0,i.jsx)(B.Z,{bylines:f.bylines,isAd:1===f.flags.ad,buzz:f}),(0,i.jsx)("h1",{dangerouslySetInnerHTML:{__html:$}}),(0,i.jsx)("p",{dangerouslySetInnerHTML:{__html:tt}}),et.length>0&&et.map((function(t,e){return(0,i.jsx)(Z,function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){q(t,e,n[e])}))}return t}({},t,{buzz:f,index:e}),t.id)})),(0,i.jsxs)("p",{className:V().commentsAndShare,children:[C?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(T,{className:V().commentsBubble}),(0,i.jsx)("span",{className:V().numberOfComments,children:C})]}):(0,i.jsx)(v,{}),rt]})]}),nt&&(0,i.jsx)(H.b,{buzz:f,subbuzzData:{assets:null===D||void 0===D?void 0:D.assets,gateIndex:0,subbuzzes:[null===D||void 0===D?void 0:D.subbuzzes[null===D||void 0===D?void 0:D.gateIndex]]},pixiedust:{unit_type:"buzz_body",unit_name:f.id,position_in_unit:0}},f.id),(0,i.jsxs)(a.Z,{onError:o.Tb,children:[!nt&&(0,i.jsx)("div",{className:V().discussionCommentsWrapper,children:(0,i.jsx)(j.Z,{buzz:f,commentCount:C,onCommentsLoaded:k,firstPage:9,repliesFirstPage:2})}),it&&(0,i.jsx)(O,{display_name:it.display_name,position:"bottom",slug:it.slug}),!nt&&(0,i.jsxs)("div",{className:V().discussionShare,children:[(0,i.jsx)(R.Z,{buzz:f}),(0,i.jsx)(d.Z,{isFloatingShareButtonEnd:A})]}),(0,i.jsx)(l.Z,{type:"promo-inline1",pixiedust:{unit_type:"buzz_bottom",unit_name:f.id,position_in_unit:0}}),(0,i.jsx)(N.Z,{buzz:f}),(0,i.jsx)("div",{ref:_}),(0,i.jsx)(z.d$,{}),(0,i.jsx)(L.Z,{}),w&&(0,i.jsx)(l.Z,{type:"bigstory",pixiedust:{unit_type:"buzz_bottom",unit_name:f.id,position_in_unit:1}})]})]}),(0,i.jsx)(m.Z,{pixiedust:{unit_type:"sidebar",unit_name:"right",position_in_unit:0}})]}),(0,i.jsx)(a.Z,{onError:o.Tb,children:(0,i.jsx)(z.cM,{className:V().discussionBottomUnit})})]})}K.propTypes={buzz:s().object.isRequired,badges:s().array,subbuzzData:s().shape({assets:s().object,subbuzzes:s().array,gateIndex:s().number})};var X=K},50283:function(t,e,n){"use strict";n.d(e,{Z:function(){return Gi}});var r=n(52322),u=n(2784),a=n(13980),o=n.n(a),c=n(9772),s=n(24678),M=n(88163),l=n(16938),j=n(21871),N=n(32561),g=n(3695),d=n(19844),y=n(91136),L=n.n(y),T=n(70733),z=n.n(T),m=n(48492),p=n(49101),I=n(70028),f=n(4366),D=n(36491),x=n(22813),b=n(46811),O=n(71031),A=(0,D.Z)((function(){return Promise.resolve().then(n.bind(n,64762))}),{ssr:!1});function h(t){var e=t.buzz,n=t.commentCount,i=t.showShareBar,a=t.className,o=void 0===a?"":a,c=t.destination,s=void 0===c?"buzzfeed":c,M=t.isFloatingShareButtonEnd,l=void 0!==M&&M,j=t.showCommentButton,N=void 0===j||j,d=(0,I.i)(e),y=(0,u.useContext)(p.Z),T=y.commentsEnabled,D=y.isCommentsPanelOpen,h=y.toggleCommentsPanel,_=(0,u.useState)(!1),S=_[0],w=_[1],E=(0,g.G)(e)?function(t){var n=t.element,i=t.index,r=t.platform,u=t.shareUrl;(0,f.aF)(n,e,{subunit_name:"share",subunit_type:"component",unit_type:"buzz_head",unit_name:e.id,item_name:r,item_type:"button",position_in_subunit:i,target_content_type:"url",target_content_id:u})}:void 0;(0,u.useEffect)((function(){var t=window.localStorage.getItem("COMMENT_"+window.location.host+window.location.pathname),e=new URLSearchParams(window.location.search).has("open_comments");null!==n&&(t||e)&&h(!0)}),[n]),(0,u.useEffect)((function(){(0,b.Hd)()&&w(!0)}),[]);var v=T&&N&&(0,r.jsx)(A,{commentCount:n,track:d,locale:e.language||"en",destination:s,onCtaClick:function(){return h(!0)},disabled:D}),C=i&&(0,r.jsx)(m.ZP,{buzz:e,type:"pageLevelOutlineThick",position:"top_share_bar",className:z().shareTop,onLoaded:E}),k=S&&(0,r.jsx)(x.Z,{variant:"standard",includeShortTitle:!0,trackingData:{unit_name:e.id,unit_type:"buzz_head"}});return e.hasQuizSharedResult?null:(0,r.jsxs)("div",{className:"".concat(L().actionBar," ").concat(L()[s]," ").concat(o," embed-action-bar"),children:[!e.shouldHideBuzzSharing&&!e.isQuiz&&(0,r.jsx)(O.Z,{isFloatingShareButtonEnd:l}),(0,g.G)(e)?(0,r.jsxs)(r.Fragment,{children:[C,v]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:L().commentsCtaWrapper,children:[v,k]}),C]})]})}h.propTypes={buzz:o().object.isRequired,isMobile:o().bool,isFloatingShareButtonEnd:o().bool};var _=h,S=n(32966),w=n(41332),E=n(17111);var v=function(){var t=(0,u.useContext)(w.Z).buzz,e=void 0===t?{}:t,n=(0,u.useContext)(S.Z),i=n.getExperimentValue,a=n.getFeatureFlagValue,o="adshield"===i("RT-1710-adshield-experiment",{rejectErrors:!1})||a("RT-1559-AdShield-script-on-BFN"),c=(0,u.useState)(!1),s=c[0],M=c[1];return(0,u.useEffect)((function(){o&&!s&&(M(!0),E._c.init({isShopping:e.isShopping,destination:e.destination_name}))}),[o]),(0,r.jsx)(r.Fragment,{})},C=n(83661),k=n(5103),Q=n(13715),U=n.n(Q),Y=n(39252),P=n(30729);function Z(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function B(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Z(t,e,n[e])}))}return t}var R=function(t){var e=t.buzz,n=t.index,i=(0,k.useTranslation)("common").t,a=(0,u.useRef)(null),o=(0,u.useRef)(null),c=(0,u.useRef)(null),s=e.bylines[n],M=s.id,l=s.bio,j=s.display_name,N=s.email,d=s.username,y=s.avatar,L={username:d,title:s.title,display_name:j||d,id:M,email:N,bio:l,avatar:y},T=(0,g.G)(e)?"/author/".concat(L.username):"/".concat(L.username);L.contact=i("contact_at_email",{authorLink:T,email:N,display_name:j});var z=(0,u.useMemo)((function(){return{unit_type:"buzz_body",unit_name:e.id,subunit_name:"author",subunit_type:"component",position_in_subunit:n,item_type:"profile",item_name:"byline"}}),[e.id,n]),m=(0,u.useCallback)((function(){(0,f.bC)(e,B({},z,{action_type:"reply",action_value:L.id}))}),[z,L.id]);return(0,u.useEffect)((function(){var t;return a&&a.current&&(t=(0,f.W3)(a.current,e,B({},z,{item_name:"send_tips",item_type:"text",position_in_subunit:null,target_content_url:"https://tips.buzzfeed.com"}))),function(){"function"===typeof t&&t()}}),[a]),(0,u.useEffect)((function(){var t,n,i;if(o&&o.current){var r=o.current.querySelectorAll("a"),u=r[0];if(i=r[1],u){var a=B({},z,{target_content_type:"user",target_content_id:L.id});n=(0,f.aF)(u,e,a),t=(0,f.Ev)(u,e,a)}i&&i.addEventListener("click",m)}return function(){"function"===typeof t&&t(),"function"===typeof n&&n(),i&&i.removeEventListener("click",m)}}),[o]),(0,u.useEffect)((function(){var t;return c&&c.current&&(t=(0,f.Ev)(c.current,e,B({},z,{target_content_type:"user",target_content_id:L.id}))),function(){"function"===typeof t&&t()}}),[c]),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:U().newsByline,children:(0,r.jsx)("a",{ref:c,href:T,className:U().headlineByline,children:(0,r.jsxs)("div",{className:U().bylineContainer,children:[(0,r.jsx)(Y.lS,{src:(0,P.Z)(L.avatar),className:U().avatar,alt:L.display_name}),(0,r.jsxs)("div",{className:U().bylineText,children:[(0,r.jsx)("span",{className:U().bylineName,dangerouslySetInnerHTML:{__html:L.display_name}}),(0,r.jsx)("p",{className:U().position,children:L.title})]})]})})})})},W=function(t){var e=t.buzz,n=void 0===e?{}:e,i=n.bylines;return"news"!==n.metavertical||n.tags.includes("--no-bio")||!i.length?null:(0,r.jsx)("div",{className:"".concat(U().newsFooter," embed-reporter-cards"),children:(0,r.jsx)("ul",{className:U().reporterCardList,children:i.map((function(t,e){return(0,r.jsx)("li",{className:U().reporterCardItem,children:(0,r.jsx)(R,{index:e,buzz:n})},e)}))})})},G=n(10097),F=n(52592),H=n(99112),J=n(98286),V=n(42630),q=n.n(V);function K(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function X(t){return function(t){if(Array.isArray(t))return K(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return K(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return K(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var $=function(t,e){var n;t>e&&(t=(n=[e,t])[0],e=n[1]);return Math.floor(Math.random()*(e-t+1))+t},tt=function(t){var e=t.className,n=void 0===e?"":e,i=t.emoji,a=void 0===i?"\ud83e\udd2f":i,o=t.onTransitionEndComplete,c=t.range,s=void 0===c?[10,15]:c,M=(0,u.useRef)($.apply(void 0,X(s))),l=(0,u.useRef)(function(t){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.degreeMax,i=void 0===n?45:n,r=e.degreeMin,u=void 0===r?-45:r,a=e.distributionFactor,o=void 0===a?1:a,c=[],s=i-u,M=0;M<t;M++){var l=Math.floor((Math.random()*s*o+M*s/t)%s)+u;c.push(l)}return c}(M.current)),j=(0,u.useRef)(null),N=(0,u.useState)(""),g=N[0],d=N[1],y=(0,u.useState)(0),L=y[0],T=y[1];(0,u.useEffect)((function(){window.matchMedia("(prefers-reduced-motion: reduce)").matches?"function"===typeof o&&o():d(q().animate)}),[o]),(0,u.useEffect)((function(){L===M.current&&"function"===typeof o&&requestIdleCallback(o)}),[L,o]),(0,u.useEffect)((function(){return function(){j.current&&cancelIdleCallback(j.current)}}),[]);return(0,r.jsx)("div",{className:"".concat(n," ").concat(q().container," ").concat(g),"aria-hidden":"true",children:(0,r.jsx)("ul",{className:q().emojis,onAnimationEnd:function(t){j.current=requestIdleCallback((function(){L<M.current&&t.animationName===q().fadeInOut&&T((function(t){return t+1}))}))},children:Array.from({length:M.current}).map((function(t,e){var n=$(25,25*M.current),i=$(25,100)/100,u=$(10,85),o=u/85,c=Math.pow(1+o,1.5);return(0,r.jsx)("li",{className:q().emoji,style:{"--confetti-rotate":"".concat(l.current[e],"deg"),"--confetti-distance-horizontal":"".concat(u,"px"),"--confetti-scaling-factor":c,"--confetti-delay":"".concat(n,"ms"),"--confetti-scale":i,zIndex:u},children:(0,r.jsx)("div",{className:q().verticalLayer,children:(0,r.jsx)("div",{className:q().scaleLayer,children:(0,r.jsx)("div",{className:q().opacityLayer,children:a})})})},e)}))})})};tt.propTypes={className:o().string,emoji:o().string.isRequired,onTransitionEndComplete:o().func,range:o().arrayOf(o().number)};var et=tt,nt=n(81985),it=n(82924),rt=n(93935),ut=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;if(!t)return"0";var n={billion:"B",million:"M",thousand:"K"};return t>=1e9?(t/1e9).toFixed(e).replace(/\.0+$/,"")+n.billion:t>=1e6?(t/1e6).toFixed(e).replace(/\.0+$/,"")+n.million:t>=1e3?(t/1e3).toFixed(e).replace(/\.0+$/,"")+n.thousand:t.toString()},at="user-entries",ot=n(83816),ct=n.n(ot);function st(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function Mt(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function lt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Mt(t,e,n[e])}))}return t}function jt(t){return function(t){if(Array.isArray(t))return st(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return st(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return st(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Nt=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.namespace,n=void 0===e?at:e,i=t.limit,r=void 0===i?100:i;return{getAll:function(){try{return JSON.parse(window.localStorage.getItem(n))||[]}catch(t){return[]}},get:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!t)throw new Error("An `entry` value is required.");var e=this.getAll();return e.find((function(e){return e.startsWith(t)}))||""},set:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!t)throw new Error("An `entry` value is required.");var e=this.getAll();e.includes(t)?e.splice(e.indexOf(t),1):e.unshift(t),r&&e.length>r&&(e=e.slice(0,r)),window.localStorage.setItem(n,JSON.stringify(e))}}}({namespace:"localUserEmojis"}),gt=function(t){var e=t.name,n=t.localEmojiName,i=t.count,a=void 0===i?0:i,o=t.onClick,c=void 0===o?function(){}:o,s=t.isClicked,M=void 0!==s&&s,l=(0,u.useState)(!1),j=l[0],N=l[1];return(0,r.jsxs)("li",{className:"".concat(ct().reactionsItem," ").concat(!M&&ct().reactionsItemActive),children:[(0,r.jsx)("button",{className:ct().reactionBtn,disabled:M,onClick:function(){Nt.set(n),c(e),N(!0)},children:e}),0!==a&&(0,r.jsx)("span",{className:ct().count,children:ut(a)}),j&&(0,r.jsx)(et,{className:ct().confetti,emoji:e,onTransitionEndComplete:function(){return N(!1)}})]},e)};gt.propTypes={count:o().number.isRequired,onClick:o().func.isRequired,name:o().string.isRequired,localEmojiName:o().string.isRequired};var dt=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.className,n=void 0===e?"":e,i=t.contentId,a=t.contentType,o=void 0===a?"content-object":a,c=t.isSponsored,s=(0,k.useTranslation)("common").t,M=(0,u.useState)(null),l=M[0],j=M[1],N=(0,u.useState)(null),g=N[0],d=N[1],y=(0,u.useState)(null),L=y[0],T=y[1],z=jt(J.T).concat(jt(J.n));(0,u.useEffect)((function(){var t=Nt.get("".concat(o,":").concat(i));if(t){var e=t.split(":").pop();(!c&&z.includes(e)||c&&J.T.includes(e))&&j(e)}}),[i,o]);var m=function(t){g||d(t.name),t.increment();var e=L.map((function(e){return e.name===t.name?e.count++:e.name===l&&e.count--,e}));T(e),j(t.name)},p=(0,u.useContext)(w.Z),I=p.buzz,f=void 0===I?{}:I,D=(0,nt.z)((0,rt.h)(f),!0),x=D.reactions,b=D.serverError;if(!x.length)return null;g&&g!=l&&x.forEach((function(t){t.name==g&&(t.count--,d(l))}));var O=[];x.forEach((function(t){t.displayPriority=J.n.includes(t.name)?1/(J.n.indexOf(t.name)+J.T.length+1):1/(J.T.indexOf(t.name)+1),c&&J.n.includes(t.name)||O.push(t)}));var A=O.sort((function(t,e){return e.count+e.displayPriority-(t.count+t.displayPriority)}));return(null===L||void 0===L?void 0:L.length)||T(A),(0,r.jsxs)("section",{className:"".concat(n," ").concat(ct().reactions),children:[(0,r.jsx)(it.Z,{id:"reactions-title",children:s("what_do_you_think")}),b&&(0,r.jsx)("p",{className:ct().error,children:s("server_error")}),(0,r.jsx)("ul",{"aria-label":"Emoji Reactions","aria-controls":"dialog",className:"".concat(ct().reactionsList," ").concat(x.some((function(t){return t.count>0}))&&ct().hasReactions," ").concat((null===L||void 0===L?void 0:L.length)<8&&ct().restrictedReactions),children:L&&L.map((function(t){return(0,r.jsx)(gt,lt({isClicked:l==t.name,localEmojiName:"".concat(o,":").concat(i,":").concat(t.name),onClick:function(){m(t)}},t),t.name)}))})]})};dt.propTypes={className:o().string,contentId:o().string,contentType:o().string};var yt=dt,Lt=n(45243),Tt=n.n(Lt),zt=n(81550),mt=(0,D.Z)((function(){return n.e(36).then(n.bind(n,10305))}),{ssr:!1});function pt(t){var e=t.data,n=void 0===e?{}:e,i=n.images,a=(0,u.useContext)(w.Z).buzz,o=void 0===a?{}:a,c=(0,u.useContext)(H.Z).destination,s=o.longform_custom_header.style&&o.longform_custom_header.style.text_over,M=s&&o.longform_custom_header.style.text_over.gradient&&"no_gradient"!==o.longform_custom_header.style.text_over.gradient,l=o.longform_custom_header,j=l.title,N=l.description;if(!i)return null;var g=i.mobile||i.standard||{width:1,height:1},d=g.width,y=g.height,L=parseInt(y/d*100,10);return(0,r.jsxs)("div",{className:"".concat(Tt().featureImageWrapper," ").concat(Tt()["featureImage-".concat(c)]," embed-feature-image"),children:[(0,r.jsx)("div",{style:{paddingBottom:"".concat(L,"%")},className:Tt().featureImagePlaceholder,children:(0,r.jsx)(mt,{data:n})}),s&&(0,r.jsx)("div",{className:"".concat(Tt().featureText," ").concat(M?Tt().featureImageShadow:""),children:(0,r.jsxs)("div",{className:Tt().featureTextInner,children:[(0,r.jsx)("div",{className:Tt().title,dangerouslySetInnerHTML:{__html:j}}),(0,r.jsx)("p",{className:Tt().description,dangerouslySetInnerHTML:{__html:N}})]})})]})}pt.propTypes={data:o().object};var It=(0,zt.Z)(pt,{onError:s.Tb}),ft=n(28182),Dt=n(27681),xt=n(64762),bt=n(80266),Ot=n.n(bt),At=n(26349),ht=n.n(At),_t=n(15185),St=n(70211);function wt(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Et=function(t){var e,n=t.primaryColor,i=t.textColor,r=void 0===i?"#000":i,u=t.subbuzzHeaderBackgroundColor,a=void 0===u?n:u,o=t.subbuzzHeaderOpacity,c=void 0===o?.3:o;return wt(e={},ht().primaryColor,n),wt(e,ht().textColor,r),wt(e,ht().subbuzzHeaderBackground,function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=parseInt(t.slice(1,3),16),i=parseInt(t.slice(3,5),16),r=parseInt(t.slice(5,7),16);return"rgba(".concat(n,",").concat(i,",").concat(r,",").concat(e,")")}(a,c)),e},vt={core_peach:Et({primaryColor:"#FFE3D4",subbuzzHeaderOpacity:1}),rose:Et({primaryColor:"#FFC0C5"}),watermelon:Et({primaryColor:"#F4A2A2"}),sand:Et({primaryColor:"#F7C597"}),hunter:Et({primaryColor:"#587E6F",textColor:"#fff"}),mint:Et({primaryColor:"#B4DBC8"}),periwinkle:Et({primaryColor:"#6772E5",textColor:"#fff",subbuzzHeaderBackgroundColor:"#8CA2E9"}),stone:Et({primaryColor:"#B0C1D1"}),black:Et({primaryColor:"#2B3249",textColor:"#fff"}),orange:Et({primaryColor:"#BB6719"})},Ct={6213166:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/2wr8eZ7PB.png",alt:"foo bar",caption:"Image caption here"},{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/mrut2VKAq.png",alt:"foo bar",caption:"Image caption here"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/2wr8eZ7PB.png",alt:"foo bar",caption:"Image caption here"}]},6318306:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/Kkep1TlKv.png",alt:": A woman in a vibrant floral suit and a woman in a mid-length lilac shirt dress",caption:"via Nordstrom"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/m4991T6wJ.png",alt:": A woman in a vibrant floral suit and a woman in a mid-length lilac shirt dress",caption:"via Nordstrom"}]},6317663:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/ojJJkGRzs.png",alt:"smiling young woman holding gift",caption:"via Nordstrom Rack"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/EFWAnH_vV.png",alt:"smiling young woman holding gift",caption:"via Nordstrom Rack"}]},6328731:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/_urLEETyh.png",alt:"GUESS Originals - Betty Boop Capsule",caption:"via GUESS Originals"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/Vnh84j7By.png",alt:"GUESS Originals - Betty Boop Capsule",caption:"via GUESS Originals"}]},6338670:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/YgB-ffkzG.png",alt:"woman wearing a purple suit jacket",caption:"via Express"},{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/QqTTkj6UI.png",alt:"two women wearing grey outfits",caption:"via Express"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/YgB-ffkzG.png",alt:"woman wearing a purple suit jacket",caption:"via Express"}]},6343747:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/_LYFS3vSe.png",alt:"Inga hosting a potluck party",caption:"Shannon Soule & Morgan Demeter / BuzzFeed"},{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/M3zC6OHNV.png",alt:"Jasmine hosting a cocktail party",caption:"Morgan Demeter / BuzzFeed"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/pMMRuj_bo.png",alt:"Inga hosting a potluck party",caption:"Shannon Soule / BuzzFeed"}]},6345368:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/DS46CxGjP.png",alt:"Elise sits on couch to wrap presents",caption:"BuzzFeed"},{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/bOUr4sHDm.png",alt:"Elise writes holiday cards",caption:"BuzzFeed"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/wmD5lwXnY.png",alt:"Elise sits on couch to wrap presents",caption:"BuzzFeed"}]},6345370:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/mVAhnRJRK.png",alt:"Rie puts flannels into boxes lined with tissue paper",caption:"BuzzFeed"},{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/Qsxen0jb1.png",alt:"Rie holds out ribbon above table with wrapping",caption:"BuzzFeed"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/AqNNrXv8f.png",alt:"Rie puts flannels into boxes lined with tissue paper",caption:"BuzzFeed"}]},6356153:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/BROOyjyP9.png",alt:"Krista wrapping a present",caption:"Shannon Soule / BuzzFeed"},{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/SpwcsDXYJ.png",alt:"Krista opening a present",caption:"Shannon Soule / BuzzFeed"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/vPCt23PGQ.png",alt:"Krista wrapping a present",caption:"Shannon Soule / BuzzFeed"}]},6356259:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/WALG2BALK.png",alt:"Woman models silver sequin one-shouldered top and skirt",caption:"Express"},{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/6addLd8V9.png",alt:"Woman walks down city crosswalk wearing long black sequin dress and open-toed black heels",caption:"Express"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/mKXQxIrY-.png",alt:"Woman models silver sequin one-shouldered top and skirt",caption:"Express"}]},7372811:{desktop:[{src:"https://img.buzzfeed.com/thumbnailer-prod-us-east-1/hive/binaries/491935.jpg",alt:"Models lounge in leggings, bras, shorts, and shirts from the Hanes Originals Collection",caption:"Hanes"},{src:"https://img.buzzfeed.com/thumbnailer-prod-us-east-1/hive/binaries/491939.jpg",alt:"Man wears boxers and tank while woman wears bra and underwear from the Hanes Originals Collection",caption:"Hanes"}],mobile:[{src:"https://img.buzzfeed.com/thumbnailer-prod-us-east-1/hive/binaries/491937.jpg",alt:"Models lounge in leggings, bras, shorts, and shirts from the Hanes Originals Collection",caption:"Hanes"}]},7538615:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/v6ckgPVfn.png",alt:"Two women dressed in fairy halloween costumes taking a selfie outside",caption:"Party City"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/v6ckgPVfn.png",alt:"Two women dressed in fairy halloween costumes taking a selfie outside",caption:"Party City"}]},7562607:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/DyHTTsHRn.png",alt:"Two dogs lying in front of a christmas tree and fireplace",caption:"Petsmart"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/DyHTTsHRn.png",alt:"Two dogs lying in front of a christmas tree and fireplace",caption:"Petsmart"}]},7633098:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/dl0YcJ-Rt.png",alt:"Two women standing in a field against a cloud back drop",caption:"via Walmart Canada"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/dl0YcJ-Rt.png",alt:"Two women standing in a field against a cloud back drop",caption:"via Walmart Canada"}]},7727906:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/8C5A6X2CW.png?output-format=jpg&output-quality=95",alt:'dog in front of a "happy halloween" sign, jack-o-lanterns and decorative bats.',caption:"via Getty Images"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/8C5A6X2CW.png?output-format=jpg&output-quality=95",alt:'dog in front of a "happy halloween" sign, jack-o-lanterns and decorative bats.',caption:"via Getty Images"}]},7757785:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/eA2VM7NFv.png?output-format=jpg&output-quality=95",alt:"Two kids opening presents from Dick's Sporting Good by a Christmas tree",caption:"DICK'S Sporting Goods"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/eA2VM7NFv.png?output-format=jpg&output-quality=95",alt:"Two kids opening presents from Dick's Sporting Good by a Christmas tree",caption:"DICK'S Sporting Goods"}]},7765663:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/GVKPPABas.png?output-format=jpg&output-quality=95",alt:"a tin of cookies, a vanilla scented candkle, and two holiday themed mugs",caption:"Walmart Canada"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/GVKPPABas.png?output-format=jpg&output-quality=95",alt:"a tin of cookies, a vanilla scented candkle, and two holiday themed mugs",caption:"Walmart Canada"}]},7765768:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/tONV_zSyk.png?output-format=jpg&output-quality=95",alt:'table setting with a champagne flute, and pink and green plates that say "noel" with cupcakes on it. table is decorated with a pink and green tree and a disco ball',caption:"Walmart Canada"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/tONV_zSyk.png?output-format=jpg&output-quality=95",alt:'table setting with a champagne flute, and pink and green plates that say "noel" with cupcakes on it. table is decorated with a pink and green tree and a disco ball',caption:"Walmart Canada"}]},7829855:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/Aay1XOftY.png?output-format=jpg&output-quality=95",alt:"one child and one woman in denim shorts",caption:"Walmart Canada"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/Aay1XOftY.png?output-format=jpg&output-quality=95",alt:"one child and one woman in denim shorts",caption:"Walmart Canada"}]},7838751:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/h71dtWwyX.png?output-format=jpg&output-quality=95",alt:"family sitting on a patio with patio furniture",caption:"Walmart Canada"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/h71dtWwyX.png?output-format=jpg&output-quality=95",alt:"family sitting on a patio with patio furniture",caption:"Walmart Canada"}]},7859887:{desktop:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/A_3KF9JWS.png?output-format=jpg&output-quality=95",alt:"woman up against a sky background, wearing a denim skirt and top",caption:"Walmart Canada"}],mobile:[{src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/A_3KF9JWS.png?output-format=jpg&output-quality=95",alt:"woman up against a sky background, wearing a denim skirt and top",caption:"Walmart Canada"}]}};function kt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function Qt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var i,r,u=[],a=!0,o=!1;try{for(n=n.call(t);!(a=(i=n.next()).done)&&(u.push(i.value),!e||u.length!==e);a=!0);}catch(c){o=!0,r=c}finally{try{a||null==n.return||n.return()}finally{if(o)throw r}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return kt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return kt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ut(t){var e=t.images,n=t.className,i=t.maxWidth;return e?(0,r.jsx)("div",{className:"".concat(ht().imagesContainer," ").concat(n),children:e.map((function(t){return(0,r.jsxs)("div",{className:ht().imageWrapper,children:[(0,r.jsx)("img",{src:"".concat(t.src,"?output-quality=100&output-format=auto&resize=").concat(i,":*"),alt:t.alt}),(0,r.jsx)("p",{className:ht().imageCaption,children:t.caption})]},t.src)}))}):null}var Yt=(0,zt.Z)((0,k.withTranslation)("common")((function(t){var e,n,i,a=t.buzz,o=t.commentsWrapperId,c=t.commentCount,s=t.showShareBar,M=t.t,l=(0,I.i)(a),j=(0,u.useContext)(p.Z),N=j.commentsEnabled,g=j.isCommentsPanelOpen,d=j.toggleCommentsPanel,y=(0,u.useContext)(H.Z).destination,L=Number(a.published),T="edit_updated"in a?Number(a.edit_updated):0;!function(t){var e=t.paletteName;(0,u.useEffect)((function(){var t=vt[e];if(!t)return function(){};var n=!0,i=!1,r=void 0;try{for(var u,a=Object.entries(t)[Symbol.iterator]();!(n=(u=a.next()).done);n=!0){var o=Qt(u.value,2),c=o[0],s=o[1];document.documentElement.style.setProperty(c,s)}}catch(M){i=!0,r=M}finally{try{n||null==a.return||a.return()}finally{if(i)throw r}}return document.documentElement.style.overflowX="hidden",function(){return document.documentElement.style.overflowX=null}}),[e])}({paletteName:a.lookbookPalette});var z=null===(e=Ct[a.id])||void 0===e?void 0:e.desktop.length;return(0,r.jsx)("div",{className:ht().container,children:(0,r.jsxs)("div",{className:"".concat(ht().headline," ").concat(Ot().headline),children:[(0,r.jsxs)("div",{className:"".concat(ht().timestamp," ").concat(Ot().container),children:[(0,r.jsx)("span",{className:"".concat(ht().paidPostBreadcrumb," ").concat(Ot().paidPostBreadcrumb),children:M("paid_post")}),(0,r.jsx)(_t.Z,{timestampPublishedUnix:L,timestampUpdatedUnix:T,countryCode:a.country_code,destination:y})]}),(0,r.jsx)("div",{className:Ot().container,children:(0,r.jsx)("h1",{className:"".concat(Ot().title," ").concat(ht().title),dangerouslySetInnerHTML:{__html:a.title}})}),(0,r.jsxs)("div",{className:ht().imageFullWidthContainer,children:[(0,r.jsx)(Ut,{className:ht().desktop,images:null===(n=Ct[a.id])||void 0===n?void 0:n.desktop,maxWidth:1===z?1130:551}),(0,r.jsx)(Ut,{className:ht().mobile,images:null===(i=Ct[a.id])||void 0===i?void 0:i.mobile,maxWidth:600}),(0,r.jsx)("div",{className:ht().imageBackgroundContainer})]}),(0,r.jsxs)("div",{className:ht().contentWidth,children:[(0,r.jsxs)("div",{className:ht().bylineContainer,children:[(0,r.jsx)(St.Z,{bylines:a.bylines,buzz:a}),(0,r.jsx)(_,{buzz:a,commentCount:c,className:ht().actionBar,showShareBar:s,destination:y,showCommentButton:!1})]}),(0,r.jsx)("p",{className:"".concat(Ot().description," ").concat(ht().description),dangerouslySetInnerHTML:{__html:a.description}}),N&&(0,r.jsx)(xt.default,{commentCount:c,track:l,locale:a.language||"en",commentsWrapperId:o,destination:y,onCtaClick:function(){return d(!0)},disabled:g})]})]})})})),{onError:s.Tb}),Pt=n(54761),Zt=n(29583),Bt=n(49671),Rt=n(12764),Wt=n.n(Rt);function Gt(t){var e=t.onOverlayClick,n=function(){e(!0)};return(0,r.jsx)("div",{role:"presentation",className:Wt().pageOverlay,onClick:n,onKeyDown:n})}function Ft(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Ht(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Ft(t,e,n[e])}))}return t}var Jt=(0,D.Z)((function(){return Promise.all([n.e(893),n.e(226)]).then(n.bind(n,41234))}),{ssr:!1});function Vt(t){var e=(0,u.useContext)(w.Z).buzz,n=void 0===e?{}:e;return(0,rt.h)(n)?(0,r.jsx)(Pt.Z,{children:(0,r.jsx)(Jt,Ht({},t))}):null}var qt=n(11455);function Kt(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Xt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Kt(t,e,n[e])}))}return t}var $t=(0,D.Z)((function(){return Promise.all([n.e(893),n.e(226)]).then(n.bind(n,1004))}),{ssr:!1});function te(t){var e=(0,u.useContext)(w.Z).buzz,n=void 0===e?{}:e;return(0,qt.h)(n)?(0,r.jsx)(Pt.Z,{children:(0,r.jsx)($t,Xt({},t))}):null}var ee=n(42494),ne=n(39097),ie=n(12430),re=n.n(ie),ue=n(94479),ae=n.n(ue),oe=n(91872);function ce(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function se(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){ce(t,e,n[e])}))}return t}var Me="https://tv.apple.com/us/movie/f-marry-kill/umc.cmc.2l224j2927seo3rujqf8h245d",le=function(t){var e=t.buzz,n=(0,u.useRef)(null),i=(0,u.useRef)(null),a=(0,u.useRef)(null),o=e&&e.sub_buzzes?e.sub_buzzes.length:0,c=(0,oe.J)({buzz:e});return(0,u.useEffect)((function(){var t,r,u={item_name:"primevideo",item_type:"text",position_in_unit:o,unit_type:"buzz_body",unit_name:e.id,subunit_type:"component",subunit_name:"studios_promo",target_content_type:"url",target_content_id:Me};return n&&n.current&&(0,f.aF)(n.current,e,se({},u,{position_in_subunit:0})),a&&a.current&&(0,f.aF)(a.current,e,se({},u,{item_type:"image",position_in_subunit:1})),i&&i.current&&(t=(0,f.W3)(i.current,e,se({},u,{position_in_subunit:0,target_content_url:Me}))),a&&a.current&&(r=(0,f.W3)(a.current,e,se({},u,{item_type:"image",position_in_subunit:1,target_content_url:Me}))),function(){"function"===typeof t&&t(),"function"===typeof r&&r()}}),[e.id,o,i,a,n]),(0,r.jsxs)("div",{className:re().promoUnit,ref:c,children:[(0,r.jsx)("h2",{className:re().promoUnitTitle,ref:n,children:(0,r.jsxs)("i",{children:["A little serial dating never killed anyone. \ud83d\udcf1\ud83d\udd2a F Marry Kill starring Lucy Hale and Virginia Gardner is now playing in select theaters and on digital. ",(0,r.jsx)(ne.default,{href:Me,children:(0,r.jsx)("a",{"data-affiliate":!0,target:"_blank",rel:"sponsored",ref:i,children:"Watch the Trailer Now!"})})]})}),(0,r.jsx)("div",{className:re().promoUnitImage,children:(0,r.jsx)("a",{"data-affiliate":!0,target:"_blank",rel:"sponsored",ref:a,href:Me,children:(0,r.jsx)("img",{src:ae(),alt:"F Marry Kill Movie"})})})]})},je=n(70755),Ne=n(60319),ge=n.n(Ne),de=n(21038);function ye(t){var e=t.isMobile,n=t.placement,i=void 0===n?1:n,a=(0,u.useState)(),o=a[0],c=a[1],s=(0,u.useState)(),M=s[0],l=s[1],j=(0,u.useState)(),N=j[0],g=j[1],d=(0,u.useState)(),y=d[0],L=d[1],T=(0,u.useContext)(w.Z).buzz,z=T.classification,m=T.flags,p=T.isPreview,I=T.tags;(0,u.useEffect)((function(){var t=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=t?"SP":"Desktop",i="taboola-below-article-thumbnails-".concat(t?"sp":"desktop").concat(e),r=1==e?"thumbnails-a":"alternating-thumbnails-a",u="\n    window._taboola = window._taboola || [];\n    if (publishedDate > firstDecDate) {\n      // MG code\n      _taboola.push({\n        mode: '".concat(r,"',\n        container: '").concat(i,"',\n        placement: 'Below Article Thumbnails ").concat(n).concat(e,"',\n        target_type: 'mix'\n      });\n    } else {\n      //Rev share code\n      _taboola.push({\n        mode: '").concat(r,"',\n        container: '").concat(i,"',\n        placement: 'Below Article Thumbnails ").concat(n).concat(e," Past',\n        target_type: 'mix'\n      });\n    }\n  ");return{id:i,inline_js:u}}(e,i),n=t.id,r=t.inline_js;g(n),L(r)}),[e]);var f=!m.sensitive&&!m.nsfw&&!m.ad&&!I.includes("--noads")&&!p&&"Japan"===z.edition;return(0,u.useEffect)((function(){de.hi.getTCData().then((function(t){var e=t.tcData,n=e.gdprApplies,i=e.eventStatus;if(n&&!["useractioncomplete","tcloaded"].includes(i)||document.getElementById("tb_loader_script"))M||o||(de.hi.setTCFListener((function(t){var e=t.tcData;c("useractioncomplete"===e.eventStatus||"tcloaded"===e.eventStatus)})),l(!0));else{var r=document.createElement("script");r.type="text/javascript",r.innerHTML="\n  window._taboola = window._taboola || [];\n  _taboola.push({article:'auto'});\n  !function (e, f, u, i) {\n    if (!document.getElementById(i)){\n      e.async = 1;\n      e.src = u;\n      e.id = i;\n      f.parentNode.insertBefore(e, f);\n    }\n  }(document.createElement('script'),\n  document.getElementsByTagName('script')[0],\n  '//cdn.taboola.com/libtrc/buzzfeedjapan/loader.js',\n  'tb_loader_script');\n  if(window.performance && typeof window.performance.mark == 'function')\n    {window.performance.mark('tbl_ic');}\n",document.head.appendChild(r);var u=document.createElement("script");u.type="text/javascript",u.innerHTML="\n  window._taboola = window._taboola || [];\n  _taboola.push({flush: true});\n",document.body.appendChild(u),o||c(!0)}}))}),[M,o]),f&&(0,r.jsxs)("div",{className:ge().taboolaWrapper,children:[1==i&&(0,r.jsx)("script",{type:"text/javascript",dangerouslySetInnerHTML:{__html:'\n  var docString = document.documentElement.innerHTML.toString();\n  var arrPublished = docString.split("datePublished");\n  var publishedDateString = arrPublished[1].split(\'"\')[2];\n  var arrModified = docString.split("dateModified");\n  var modifiedDateString = arrModified[1].split(\'"\')[2];\n  if(modifiedDateString){\n    // replacing publishedDate if dateModified has value\n    publishedDateString = modifiedDateString;\n  }\n  var publishedDate = new Date(publishedDateString);\n  var firstDecDate = new Date("12/01/2022");\n'}}),(0,r.jsx)("div",{id:N}),(0,r.jsx)("script",{type:"text/javascript",dangerouslySetInnerHTML:{__html:y}})]})}ye.propTypes={isMobile:o().bool,placement:o().number};var Le=ye,Te=n(94200),ze=n.n(Te),me=n(84952),pe=["IN","PH","SG","ID","MY","PK","HK","TH","KR","BD","VN","LK","TW","NP","KH","BN","PG","MO","LA","BT","TL","MN"];var Ie=function(){var t=(0,u.useState)(),e=t[0],n=t[1],i=(0,u.useState)(),a=i[0],o=i[1],c=(0,u.useRef)(null),s=(0,u.useContext)(w.Z).buzz,M=s.classification,l=s.flags,j=s.isPreview,N=s.tags,g=!l.sensitive&&!l.nsfw&&!l.ad&&!N.includes("--noads")&&!j&&"Japan"!==M.edition&&-1!==pe.indexOf((0,me.pP)());return(0,u.useEffect)((function(){g&&de.hi.getTCData().then((function(t){var i=t.tcData,r=i.gdprApplies,u=i.eventStatus;if(r&&!["useractioncomplete","tcloaded"].includes(u)||document.getElementById("tb_loader_script"))a||e||(de.hi.setTCFListener((function(t){var e=t.tcData;n("useractioncomplete"===e.eventStatus||"tcloaded"===e.eventStatus)})),o(!0));else{var s=document.createElement("script");s.type="text/javascript",s.innerHTML="\n  window._taboola = window._taboola || [];\n  _taboola.push({article:'auto'});\n  !function (e, f, u, i) {\n    if (!document.getElementById(i)){\n      e.async = 1;\n      e.src = u;\n      e.id = i;\n      f.parentNode.insertBefore(e, f);\n    }\n  }(document.createElement('script'),\n  document.getElementsByTagName('script')[0],\n  '//cdn.taboola.com/libtrc/buzzfeed-international/loader.js',\n  'tb_loader_script');\n  if(window.performance && typeof window.performance.mark == 'function')\n    {window.performance.mark('tbl_ic');}\n",document.head.appendChild(s);var M=document.createElement("script");M.type="text/javascript",M.innerHTML="\n  window._taboola = window._taboola || [];\n  _taboola.push({\n    mode: 'alternating-thumbnails-a',\n    container: 'taboola-below-article-thumbnails',\n    placement: 'Below Article Thumbnails',\n    target_type: 'mix'\n  });\n",c.current.appendChild(M);var l=document.createElement("script");l.type="text/javascript",l.innerHTML="\n  window._taboola = window._taboola || [];\n  _taboola.push({flush: true});\n",document.body.appendChild(l),e||n(!0)}}))}),[a,e]),g&&(0,r.jsx)("div",{className:ze().taboolaWrapper,ref:c,children:(0,r.jsx)("div",{id:"taboola-below-article-thumbnails"})})},fe=n(66330),De=n(31284),xe=n.n(De),be=n(64435),Oe=function(t){var e=t.topic,n=t.buzz,i=t.index,a=e.tag_name.replace(/_/g,"-"),o=(0,u.useMemo)((function(){return{item_type:"button",item_name:e.tag_display_name,subunit_name:"topic_in_article",subunit_type:"component",unit_type:"buzz_body",unit_name:n.id,target_content_type:"feed",target_content_id:"topic_".concat(a),position_in_subunit:i}}),[n.id,e.tag_name,i]),c=(0,be.R)(o);return(0,r.jsx)("a",{ref:c,href:"/topic/".concat(a),children:e.tag_display_name})},Ae=function(t){var e,n,i=t.buzz,u=(null===(e=i.laser_tags)||void 0===e||null===(n=e.bf_content_description)||void 0===n?void 0:n.topic)||[];return u.length?(0,r.jsxs)("div",{className:xe().topics,children:[(0,r.jsx)("h2",{className:xe().heading,children:"Topics in this article"}),(0,r.jsx)("ul",{children:u.slice(0,3).map((function(t,e){return(0,r.jsx)("li",{children:(0,r.jsx)(Oe,{topic:t,buzz:i,index:e})},e)}))})]}):null},he=n(48121),_e=n.n(he);function Se(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var we=function(t){return(0,r.jsx)("svg",function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Se(t,e,n[e])}))}return t}({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,viewBox:"0 0 16 16",fill:"none"},t,{children:(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.945 9.048l-4.95 4.95-4.95-4.95L4.46 7.634 7 10.174V1.999h2v8.165l2.53-2.53 1.415 1.414z"})}))},Ee=n(16981),ve=n(48882),Ce=n(67176),ke=n(6294),Qe=n(52547),Ue=n(42719),Ye=n(91904);function Pe(t){var e=t.product,n=void 0===e?{}:e,i=t.postRef,a=t.buzz,o=t.index,c=(0,k.useTranslation)("common").t,s=n.subbuzz_id,M=n.subbuzz_text,l=n.rank_in_post;return(0,u.useEffect)((function(){var t=a.sub_buzzes.some((function(t){var e,n,i;return t.id===s&&"product_subbuzz"===(null===(e=t.bfp_data)||void 0===e?void 0:e.format_name)&&!!(null===(n=t.bfp_data)||void 0===n||null===(i=n.data)||void 0===i?void 0:i.heading)})),e=!!i.current.querySelector('[id="'.concat(s,'"] ~ .trending-product-label'));if(!t&&!e){var n=i.current.querySelector('[id="'.concat(s,'"]'));if(n&&n.parentNode){var r=n.parentNode.querySelector("[class*=media]");if(r){var u=document.createElement("div"),o=document.createElement("div");u.classList.add("trending-product-label-wrapper"),o.innerHTML="".concat(c("popular")),o.classList.add("trending-product-label"),u.appendChild(o),r.parentNode.insertBefore(u,r)}}}}),[s,i,c,a.sub_buzzes]),(0,r.jsxs)("li",{className:_e().trendingProduct,children:[(0,r.jsx)(Ze,{buzz:a,subbuzz_text:M,index:o}),(0,r.jsx)("div",{className:_e().seeInList,children:(0,r.jsxs)("a",{className:_e().viewInListBtn,href:"#".concat(s),onClick:function(){var t,e=i.current.querySelector('[id="'.concat(s,'"]'));e&&(t=e.closest(".subbuzz")),t&&(t.querySelectorAll("a[data-affiliate]").forEach((function(t){t.setAttribute("data-origin","tp-subbuzz"),(0,Ce.aj)(t,{origin:"tp-subbuzz"})})),(0,f.TW)(a,{data_source_name:"site_component_api",item_name:s,item_type:"subbuzz",position_in_unit:l,target_content_id:a.id,target_content_type:"buzz",unit_name:a.id,unit_type:"buzz_body"}))},children:[(0,r.jsx)("span",{children:"View in list"}),(0,r.jsx)(we,{className:_e().downArrow,"aria-hidden":"true"})]})})]})}function Ze(t){var e=t.buzz,n=t.subbuzz_text,i=t.index,a=(0,ve.m)({buzz:e}),o=(0,oe.J)({buzz:e}),c=(0,Ee.B)({buzz:e}),s=(0,Ye.J)(e,i);return(0,u.useEffect)((function(){var t=o.current;t&&((0,Qe.t)(t,(0,ke.tq)()?90:150),t.querySelectorAll("a[href]").forEach((function(t){(0,Ce.aj)(t,{origin:"tp"})})))})),(0,r.jsx)("p",{dangerouslySetInnerHTML:{__html:n},ref:(0,Ue.Z)(a,o,s,c)})}var Be=(0,zt.Z)((0,k.withTranslation)("common")((function(t){var e=t.t,n=t.buzz,i=void 0===n?{}:n,u=t.postRef,a=i.trending_products,o="trending-products";return!a||"object"!==typeof a||!Array.isArray(a)||a.length<2?null:(0,r.jsx)("section",{className:"\n          ".concat(_e().trendingProducts,"\n        "),"aria-labelledby":o,children:(0,r.jsxs)("div",{className:_e().contentWrapper,children:[(0,r.jsx)("h2",{id:o,className:_e().title,children:e("popular_products")}),(0,r.jsx)("ul",{className:_e().productList,children:a.map((function(t,e){return(0,r.jsx)(r.Fragment,{children:t.subbuzz_title&&(0,r.jsx)(Pe,{product:t,buzz:i,postRef:u,index:e},t.subbuzz_id)})}))})]})})})),{onError:s.Tb}),Re=n(48185);function We(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Ge(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){We(t,e,n[e])}))}return t}var Fe=(0,D.Z)((function(){return n.e(246).then(n.bind(n,96661))}),{ssr:!1});function He(t){var e=(0,u.useContext)(w.Z).buzz,n=void 0===e?{}:e,i=(0,l.Z)("(min-width: 64rem)");return(0,Re.h)(n,i)?(0,r.jsx)(Pt.Z,{children:(0,r.jsx)(Fe,Ge({},t))}):null}var Je=n(64994),Ve=n(81298),qe=n(29489),Ke=n.n(qe);function Xe(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function $e(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Xe(t,e,n[e])}))}return t}function tn(t){var e,n=t.buzz,i=t.isOpen,a=t.onRequestClose,o=t.subbuzzData,c=void 0===o?{}:o,s=t.subbuzzId,M=(0,u.useRef)(null),l=(0,k.useTranslation)("common").t,j=n.sub_buzzes.map((function(t){return t.id})).indexOf(s),N=-1!==j?null===c||void 0===c||null===(e=c.subbuzzes)||void 0===e?void 0:e[j]:"",g=(0,u.useMemo)((function(){return{subunit_type:"component",subunit_name:"affiliate_link_modal",unit_type:"modal",unit_name:n.id}}),[n.id,j]),d=(0,u.useCallback)((function(){(0,f.bC)(n,$e({},g,{action_type:"close",action_value:"subbuzz_modal",item_type:"button",item_name:"close_modal"}))}),[g]),y=(0,u.useCallback)((function(){(0,f.bC)(n,$e({},g,{action_type:"close",action_value:"subbuzz_modal",item_type:"button",item_name:"read_article"}))}),[g]),L=(0,u.useRef)(null);(0,u.useEffect)((function(){var t;return L.current&&(t=(0,f.aF)(L.current,n,$e({},g,{item_type:"button",item_name:"read_article",target_content_type:"buzz",target_content_id:n.id}))),function(){"function"===typeof t&&t()}}),[]);return(0,u.useEffect)((function(){var t="overflow-hidden",e=i&&-1!==j;return e&&M.current.focus(),document.body.classList.toggle(t,e),function(){document.body.classList.remove(t)}}),[i]),(0,u.useEffect)((function(){-1==j&&a()}),[j]),-1==j?(0,r.jsx)(r.Fragment,{}):(0,r.jsx)("div",{className:"".concat(Ke().subbuzzShareModalOverlay," ").concat(i?Ke().open:Ke().closed),onClick:a,"aria-labelledby":n.title,role:"dialog","aria-modal":"true","aria-hidden":"false",id:"subbuzz-shared-modal",children:(0,r.jsxs)("div",{className:Ke().subbuzzShareModalModal,onClick:function(t){t.stopPropagation()},children:[(0,r.jsxs)("div",{className:"".concat(Ke().header),children:[(0,r.jsx)("div",{}),(0,r.jsx)("div",{className:Ke().title,children:n.title}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{ref:M,className:Ke().closeButton,onClick:function(){d(),a()},"data-dismiss":"modal","aria-label":"Close",children:(0,r.jsx)(Ve.b0,{})})})]}),(0,r.jsx)("div",{className:Ke().content,children:(0,r.jsx)(je.NE,{html:N,buzz:n,index:j},"subbuzz-share-modal-".concat(s))}),(0,r.jsx)("div",{className:Ke().footer,children:(0,r.jsx)("button",{ref:L,"aria-label":"Read Article",className:"".concat(Ke().readArticleButton," ").concat(Ke().standard),onClick:function(){y(),a()},children:l("read_article")})})]})})}tn.propTypes={buzz:o().object,isOpen:o().bool,onRequestClose:o().func,subbuzzData:o().shape({assets:o().object,subbuzzes:o().array})},tn.defaultProps={isOpen:!1,onRequestClose:function(){}};var en=tn,nn=n(38668),rn=n.n(nn),un=[{heading:(0,r.jsxs)(r.Fragment,{children:["It\u2019s more than a tote \u2014 it\u2019s a thank-you. ",(0,r.jsx)("a",{href:"https://www.buzzfeed.com/member-center/signup/products?utm_source=buzzfeed&utm_medium=web&utm_campaign=bpage_t2_v1",children:"Join BuzzFeed+"})," for no ads, exclusive content, and a little something we made just for you."]}),image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/3KV8NMHQh.png?output-format=auto&output-quality=80&downsize=700%3A%2A",link:"https://www.buzzfeed.com/member-center/signup/products?utm_source=buzzfeed&utm_medium=web&utm_campaign=bpage_t2_v1",experimentName:"variant_1",index:1,version:2},{heading:(0,r.jsxs)(r.Fragment,{children:["Love BuzzFeed? You\u2019re exactly who we made this for. ",(0,r.jsx)("a",{href:"https://www.buzzfeed.com/member-center/signup/products?utm_source=buzzfeed&utm_medium=web&utm_campaign=bpage_t2_v2",children:"Join BuzzFeed+"})," for member-only posts, a clean, ad-free scroll, and a free tote you didn\u2019t know you needed."]}),image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/tMriU2v9F.png?output-format=auto&output-quality=80&downsize=700%3A%2A",link:"https://www.buzzfeed.com/member-center/signup/products?utm_source=buzzfeed&utm_medium=web&utm_campaign=bpage_t2_v2",experimentName:"variant_2",index:2,version:2},{heading:(0,r.jsxs)(r.Fragment,{children:["Free tote. No ads. More BuzzFeed. ",(0,r.jsx)("a",{href:"https://www.buzzfeed.com/member-center/signup/products?utm_source=buzzfeed&utm_medium=web&utm_campaign=bpage_t2_v3",children:"Join BuzzFeed+"})," to get exclusive content and your choice of two staff-designed bags."]}),image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/tMriU2v9F.png?output-format=auto&output-quality=80&downsize=700%3A%2A",link:"https://www.buzzfeed.com/member-center/signup/products?utm_source=buzzfeed&utm_medium=web&utm_campaign=bpage_t2_v3",experimentName:"variant_3",index:3,version:2}],an=[{image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/u7A8H0Hrg.png?output-format=auto&output-quality=80&downsize=700%3A%2A",link:"https://www.buzzfeed.com/member-center/signup/products?utm_source=buzzfeed&utm_medium=web&utm_campaign=bpage_t3_v1",experimentName:"variant_1",index:1,version:3},{image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/SABNDRir_.png?output-format=auto&output-quality=80&downsize=700%3A%2A",link:"https://www.buzzfeed.com/member-center/signup/products?utm_source=buzzfeed&utm_medium=web&utm_campaign=bpage_t3_v2",experimentName:"variant_2",index:2,version:3},{image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/_N7FVygeM.png?output-format=auto&output-quality=80&downsize=700%3A%2A",link:"https://www.buzzfeed.com/member-center/signup/products?utm_source=buzzfeed&utm_medium=web&utm_campaign=bpage_t3_v3",experimentName:"variant_3",index:3,version:3}],on=function(t){var e=t.buzz,n=(0,u.useRef)(null),i=(0,u.useContext)(S.Z),a=i.experiments,o=i.getExperimentValue,c=(0,u.useState)(null),s=c[0],M=c[1];return(0,u.useEffect)((function(){if(a&&a.loaded){var t=o("membership-bpage-promo",{rejectErrors:!1}),e=o("membership-bpage-politics-promo",{rejectErrors:!1}),n=null;t&&"control"!==t?n=un.find((function(e){return e.experimentName===t})):e&&"control"!==e&&(n=an.find((function(t){return t.experimentName===e}))),M(n)}}),[a,o]),(0,u.useEffect)((function(){var t,i=null===n||void 0===n?void 0:n.current;if(i&&s){var r={unit_type:"buzz_body",unit_name:e.id,item_type:"subbuzz",item_name:"membership_promo_t".concat(s.version,"_v").concat(s.index),target_content_type:"utility",target_content_id:"membership"},u=function(){(0,f.TW)(e,r)};t=(0,f.zq)(i,e,r);var a=i.querySelectorAll("a[href]");return a.forEach((function(t){t.addEventListener("click",u)})),function(){"function"===typeof t&&t(),i&&a.forEach((function(t){t.removeEventListener("click",u)}))}}}),[n,e.id,s]),s?(0,r.jsxs)("div",{className:rn().promoUnit,ref:n,children:[s.heading&&(0,r.jsx)("h2",{className:"subbuzz__header subbuzz__title",children:s.heading}),(0,r.jsx)("div",{className:rn().promoUnitImage,children:(0,r.jsx)("a",{href:s.link,children:(0,r.jsx)("img",{src:s.image,alt:"Membership Promo"})})})]}):null};function cn(t){var e,n=t.buzz,i=t.subbuzzData,a=t.badges,o=t.showOverlay,c=t.setShowOverlay,y=(0,u.useRef)(null),L=(0,M.Z)({once:!0}),T=L.isIntersecting,m=L.setObservable,I=(0,u.useContext)(H.Z).destination,f=(0,u.useContext)(p.Z).commentsEnabled,D=(0,l.Z)("(max-width:500px)"),x=(0,u.useState)(null),b=x[0],O=x[1],A=(0,u.useContext)(S.Z),_=A.experiments,w=A.getExperimentValue,E=A.getFeatureFlagValue,k=(0,u.useState)(!1),Q=k[0],U=k[1],Y=(0,u.useState)(!1),P=Y[0],Z=Y[1],B=n.isQuiz,R=!(n.shouldHideBuzzSharing||n.shouldShowPostContentOnly)&&!D&&!B||(0,g.G)(n),J=n.isTrivia||n.isPersonality,V=!!(n.published&&parseInt(n.published,10)>1710959400),q=(0,u.useState)(!1),K=q[0],X=q[1],$=(0,u.useState)(null),tt=$[0],et=$[1],nt=null===n||void 0===n||null===(e=n.metadata)||void 0===e?void 0:e.subbuzzId;(0,u.useEffect)((function(){nt&&X(!0)}),[]),(0,u.useEffect)((function(){(0,g.G)(n)&&(document.querySelector("body").style.overflowX="hidden")}),[n,n.format.page_width]),(0,u.useEffect)((function(){var t=w("RT-946-shopping-recirc-bfn",{rejectErrors:!1});(0,g.G)(n)&&"var1"===t&&U(!0)}),[w]),(0,u.useEffect)((function(){if(_&&_.loaded){var t=E("taboola_apac");Z(t)}}),[_,E]),(0,u.useEffect)((function(){_&&_.loaded&&et(E("RT-1749-move-package-above-comments"))}),[_,E]);var it=(0,u.useCallback)((function(){c(!1)}),[c]);return(0,r.jsxs)("main",{"aria-hidden":K,className:"".concat(z().main," ").concat(z()[I]," embed-content"),id:"buzz-content",ref:y,children:[!n.disableAds&&(0,r.jsx)(v,{}),(0,r.jsx)(en,{buzz:n,isOpen:K,onRequestClose:function(){return X(!1)},subbuzzData:i,subbuzzId:nt},"shared-modal-".concat(n.id)),n.longform_custom_header&&(0,r.jsx)(It,{data:n.longform_custom_header}),(0,r.jsx)("div",{className:"".concat(z().article," ").concat(z()[I]," ").concat(z().wide," ").concat(z()[n.bfpTheme]||""," embed-post"),children:(0,r.jsxs)("div",{className:z().content,children:[(0,r.jsxs)("article",{className:"featured-post",children:[n.lookbookPalette?(0,r.jsx)(Yt,{buzz:n,commentCount:b,showShareBar:R}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Dt.Z,{buzz:n,badges:a}),(0,r.jsx)(h,{buzz:n,commentCount:b,showShareBar:R,destination:I,isFloatingShareButtonEnd:T})]}),(0,r.jsx)(Be,{buzz:n,postRef:y}),(0,r.jsx)(je.ZP,{buzz:n,subbuzzData:i,pixiedust:{unit_type:"buzz_body",unit_name:n.id,position_in_unit:0}}),n.hasStudiosPromoUnit&&(0,r.jsx)(le,{buzz:n}),(0,g.G)(n)&&(0,r.jsx)(Ae,{buzz:n}),(0,r.jsx)(W,{buzz:n}),(0,r.jsx)(F.Z,{buzz:n})]}),(0,r.jsx)(on,{buzz:n}),(0,r.jsxs)(j.Z,{onError:s.Tb,children:[(0,r.jsx)(C.Z,{type:"subbuzz",pixiedust:{unit_type:"buzz_bottom",unit_name:n.id,position_in_unit:0}}),P&&(0,r.jsx)(Pt.Z,{children:(0,r.jsx)(Ie,{})}),"Japan"===n.classification.edition&&(0,r.jsx)(Pt.Z,{children:(0,r.jsx)(Le,{isMobile:D})}),(0,r.jsx)(Je.Z,{buzz:n}),!V&&(J?(0,r.jsx)(te,{}):(0,r.jsx)(Vt,{})),V&&(0,r.jsx)(yt,{contentId:n.id,isSponsored:(0,d.f)(n)}),!n.isShopping&&!0===tt&&(0,r.jsx)(Bt.Z,{style:{marginBottom:"3rem"},isNewDesign:!0,isShoppingDesign:!0}),f&&(0,r.jsx)(G.Z,{buzz:n,commentCount:b,onCommentsLoaded:O,firstPage:100,repliesFirstPage:2,perPage:100}),f&&(0,r.jsx)(fe.Z,{buzz:n,subunitName:"comments",subunitType:"component",unitType:"buzz_bottom"}),"buzzfeed"===I&&!n.isShopping&&!(0,g.G)(n)&&(0,r.jsxs)(r.Fragment,{children:[!1===tt&&(0,r.jsx)(Bt.Z,{style:{marginBottom:"3rem"},isNewDesign:!0}),(0,r.jsx)(C.Z,{type:"story-bpage-desktop",pixiedust:{unit_type:"buzz_bottom",unit_name:n.id,position_in_unit:1},style:{marginTop:"-2rem"}})]}),(0,r.jsx)(ee.d$,{}),"buzzfeed"===I&&n.isShopping&&!(0,g.G)(n)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(C.Z,{type:"story-bpage-desktop",pixiedust:{unit_type:"buzz_bottom",unit_name:n.id,position_in_unit:1}}),(0,r.jsx)(Bt.Z,{style:{marginTop:D?"1rem":"-2rem"},isNewDesign:!0})]}),(0,r.jsx)(Zt.Z,{}),(0,r.jsx)(ft.Z,{}),D&&(0,r.jsx)(C.Z,{type:"bigstory",pixiedust:{unit_type:"buzz_bottom",unit_name:n.id,position_in_unit:1}})]})]})}),(0,r.jsxs)(j.Z,{onError:s.Tb,children:[(0,r.jsx)(He,{}),"Japan"===n.classification.edition&&(0,r.jsx)(Pt.Z,{children:(0,r.jsx)(Le,{placement:2,isMobile:D})}),(0,g.G)(n)&&(0,r.jsx)(N.Z,{jumpTo:"news-footer",className:"skipToFooter",label:"Skip to footer"}),(0,g.G)(n)&&Q&&(0,r.jsx)(Bt.Z,{style:{marginBottom:"3rem",marginTop:"-2rem"},isNewDesign:!1}),(0,r.jsx)("div",{ref:m}),(0,r.jsx)(ee.cM,{})]}),o&&(0,r.jsx)(Gt,{onOverlayClick:it})]})}cn.propTypes={buzz:o().object.isRequired,badges:o().array,subbuzzData:o().shape({assets:o().object,subbuzzes:o().array})};var sn=cn,Mn=n(14125),ln=n(35167);function jn(t){var e=t.pixiedust;return(0,r.jsx)(C.Z,{config:{adType:"ex",adPos:"promo-inline-trending-ad-unit-slot",size:[ln.J7.PROGRAMMATIC_SMARTPHONE_BANNER,ln.J7.PROGRAMMATIC_MEDIUM_RECTANGLE,ln.J7.PROGRAMMATIC_LEADERBOARD,ln.J7.NATIVE,ln.J7.FLUID],viewability:"high"},pixiedust:e})}var Nn=n(42235),gn=n(13607),dn=n(50946);var yn=function(){var t=(0,u.useContext)(w.Z).buzz,e=(0,u.useContext)(gn.Z).tracking,n=e.consentValue,i=e.isConsentReady,a=!t.isAd&&"news"!==t.metavertical&&!t.flags.sensitive&&!t.flags.nsfw&&t.isUS&&-1!==dn.u.indexOf(t.category.toLowerCase());return(0,u.useEffect)((function(){a&&n&&i&&(0,Nn.v)("//z-na.amazon-adsystem.com/widgets/onejs?MarketPlace=US&adInstanceId=8e4d6210-350e-4d17-b9c5-70937cc906ff")}),[a,n,i]),a?(0,r.jsx)("div",{id:"amzn-assoc-ad-8e4d6210-350e-4d17-b9c5-70937cc906ff"}):null};var Ln=function(t){var e,n=t.buzz,i=(0,k.useTranslation)("common").t;return n.tags.forEach((function(t){t.includes("--commerce-seo-links-")&&(e=t.replace("--commerce-seo-links-",""))})),e&&""!==e&&"Choose"!==e&&["US"].some((function(t){return t===n.classification.edition}))&&["en"].some((function(t){return t===n.language}))?(0,r.jsx)("div",{className:"subbuzz subbuzz-text xs-mb4 xs-relative",children:(0,r.jsx)("h2",{dangerouslySetInnerHTML:{__html:i("COMMERCE_SEO_"+e.toUpperCase())}})}):null},Tn=n(30353);var zn=function(){var t,e=(0,u.useContext)(w.Z).buzz,n=void 0===e?{}:e,i=(0,u.useContext)(c.$),a=null===i||void 0===i||null===(t=i.env)||void 0===t?void 0:t.userCountry,o=!(0,g.G)(n)&&"Japan"!==n.classification.edition&&!n.isAd&&("au"===a||"nz"===a),s="dev"!==Tn.CLUSTER&&"stage"!==Tn.CLUSTER,M="\n    window.PubFeedHeader = true;\n    var script = document.createElement('script');\n    script.src = \"https://".concat(s?"pubfeed":"staging-pubfeed",".linkby.com/widget.js\";\n    document.getElementsByTagName('head')[0].appendChild(script);\n  ");if(!o)return"";if(o&&!window.PubFeedHeader){var l=document.createElement("script");l.type="text/javascript",l.innerHTML=M,document.head.appendChild(l)}return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"linkby-widget","data-type":"listicle"})})},mn=n(99377),pn=n(95705),In=n.n(pn);function fn(t){var e=t.content,n=t.onClick,i=t.onClose,u=e.btnText;return(0,r.jsx)("div",{className:In().container,children:(0,r.jsxs)("div",{className:In().button,children:[(0,r.jsx)("div",{className:In().link,onClick:function(){return n()},children:u}),(0,r.jsx)("div",{className:In().close,onClick:function(){return i()},children:"\xd7"})]})})}var Dn=n(35346),xn=n.n(Dn);function bn(t){var e=t.content,n=t.onClick,i=t.onClose,u=e.titleText,a=e.descText,o=e.btnText,c=e.closeText;return(0,r.jsxs)("div",{className:xn().container,children:[(0,r.jsx)("div",{className:xn().icon,children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 38 38",children:[(0,r.jsx)("title",{children:"BuzzFeed Trending Arrow"}),(0,r.jsx)("path",{d:"M19 38C8.5 38 0 29.5 0 19S8.5 0 19 0s19 8.5 19 19-8.5 19-19 19zm9.9-19.4l-1.3-9-8.4 3.4 3.3 1.9-3.1 5.4-5.4-3-5.4 9.3 3 1.7 3.6-6.3 5.4 3.1 4.9-8.5 3.4 2z"})]})}),(0,r.jsx)("div",{className:xn().title,children:u}),(0,r.jsx)("div",{className:xn().desc,children:a}),(0,r.jsx)("div",{className:xn().button,onClick:function(){return n()},children:o}),(0,r.jsx)("div",{className:xn().close,onClick:function(){return i()},children:c})]})}var On={buzzfeed:{ios:"https://apps.apple.com/app/apple-store/id352969997?pt=329394&&mt=8&ct=",android:"https://play.google.com/store/apps/details?id=com.buzzfeed.android&utm_campaign=",desktop:"https://www.buzzfeed.com/app?c="},tasty:{ios:"https://apps.apple.com/us/app/tasty-recipes-cooking-videos/id1217456898?pt=xxxx&&mt=8&ct=",android:"https://play.google.com/store/apps/details?id=com.buzzfeed.tasty&utm_source=buzzfeed&utm_medium=web&utm_campaign=",desktop:"https://www.tasty.co/download?c="}},An=[{name:"tasty",enabled:!0,match:function(t){return"Tasty"===t.category},content:{promoType:"button",btnText:"7,500+ recipes, 1 free app",campaignId:"bftasty",storeLinks:On.tasty,unit_name:"tasty",subunit_name:"tasty_mobile_app_promo"}},{name:"facebook",enabled:!1,match:function(t,e){return"facebook"==e},content:{promoType:"card",titleText:"Life is better in the app!",descText:"What\u2019s trending, funny tweets, quizzes, games, and more.",btnText:"Get the BuzzFeed app",closeText:"Not now",campaignId:"post-fb",storeLinks:On.buzzfeed}},{name:"quiz",enabled:!0,match:function(t){return t.isQuiz},content:{promoType:"button",btnText:"More Quizzes in the App!",campaignId:"quiz",storeLinks:On.buzzfeed}},{name:"default",enabled:!1,match:function(t){return!t.isQuiz},content:{promoType:"button",btnText:"Get the App!",campaignId:"post",storeLinks:On.buzzfeed}},{name:"quiz-complete",enabled:!0,match:function(t){return t.isQuiz},content:{promoType:"card",titleText:"Quizzes are better in the app!",descText:"What\u2019s trending, funny tweets, quizzes, games, and more.",btnText:"Get the BuzzFeed app",closeText:"Not now",campaignId:"quiz-complete",storeLinks:On.buzzfeed}}],hn=n(96547),_n=n.n(hn);function Sn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function wn(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Sn(t,e,n[e])}))}return t}var En="MobileAppPromo_closed_at";var vn=function(){var t=(0,u.useContext)(w.Z).buzz,e=void 0===t?{}:t,n=(0,u.useContext)(p.Z).isCommentsPanelOpen,i=(0,u.useState)(void 0),a=i[0],o=i[1],c=(0,u.useState)(void 0),s=c[0],M=c[1],l=(0,u.useState)(void 0),j=l[0],N=l[1],g={addListener:function(){return!0},track:function(){return!0},logEvent:function(){return!0},closeJourney:function(){var t=An.filter((function(t){return"quiz-complete"===t.name})),e=t?t[0].content:{};return N(e),!0}},d={unit_type:"modal",subunit_type:"component",position_in_unit:0,position_in_subunit:0,item_type:"button",item_name:"cta"};(0,u.useEffect)((function(){try{if(void 0===a){window.branch=g;var t=window.navigator.userAgent,n=/iP(hone|ad|od)/i.test(t)?"ios":/Android/i.test(t)?"android":"desktop";o(n)}if(void 0===j){var i=(0,mn.an)(),r=An.find((function(t){return t.match(e,i)}));(null===r||void 0===r?void 0:r.enabled)?N(r.content):N(!1)}if(void 0===s){var u=localStorage.getItem(En),c=(new Date).getTime();M(!(!!u&&c-u<6048e5))}}catch(l){return console.error(l),l}}),[]),(0,u.useEffect)((function(){if(j&&(null===j||void 0===j?void 0:j.promoType)&&s){var t=j.storeLinks[a]+j.campaignId;(0,f.Oz)(e,wn({target_content_id:t,target_content_type:"url",unit_name:(null===j||void 0===j?void 0:j.unit_name)||j.campaignId,subunit_name:(null===j||void 0===j?void 0:j.subunit_name)||"mobile_app_promo"},d))}}),[j]);var y=function(t){return!t.isShopping&&"en"==t.language&&"buzzfeed"===t.destination_name&&["Moderated Community","Editorial"].includes(t.editorial_status)}(e);if(!y||!s||!j||n||"desktop"===a)return null;var L=function(t){switch(t.content.promoType){case"button":return(0,r.jsx)(fn,wn({},t));case"card":return(0,r.jsx)(bn,wn({},t));default:return null}};return(0,r.jsx)("section",{id:"mobile-app-promo",className:"".concat(_n().mobileAppPromo," ").concat("button"===j.promoType?_n().promoButton:_n().promoBanner),children:(0,r.jsx)(L,{content:j,onClick:function(){var t=j.storeLinks[a]+j.campaignId;(0,f.nz)(e,wn({target_content_url:t,unit_name:(null===j||void 0===j?void 0:j.unit_name)||j.campaignId,subunit_name:(null===j||void 0===j?void 0:j.subunit_name)||"mobile_app_promo"},d)),window.location=t},onClose:function(){(0,f.bC)(e,wn({action_type:"close",action_value:"close_promo",unit_name:(null===j||void 0===j?void 0:j.unit_name)||j.campaignId,subunit_name:(null===j||void 0===j?void 0:j.subunit_name)||"mobile_app_promo"},d));var t=(new Date).getTime();localStorage.setItem(En,t),M(!1)}})})},Cn=n(94776),kn=n.n(Cn),Qn=n(62430),Un=n(24823),Yn=n(50923),Pn=n.n(Yn);function Zn(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function Bn(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){Zn(u,i,r,a,o,"next",t)}function o(t){Zn(u,i,r,a,o,"throw",t)}a(void 0)}))}}function Rn(t){var e,n,i,a,o=t.buzz,c=void 0===o?{}:o,s=(null===c||void 0===c||null===(e=c.laser_tags)||void 0===e||null===(n=e.watson)||void 0===n?void 0:n.category)||[],M=(null===c||void 0===c||null===(i=c.laser_tags)||void 0===i||null===(a=i.watson)||void 0===a?void 0:a.keyword)||[],l=(M.length>0?M:s).map((function(t){return t.tag_name.replaceAll("_"," ")})),j=(0,u.useState)([]),N=j[0],g=j[1],d=(0,u.useContext)(H.Z).base_url;return(0,u.useEffect)((function(){function t(){return(t=Bn(kn().mark((function t(){var e,n,i;return kn().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=4,fetch("".concat(d,"/site-component/v1/commerce-product-search?q=retailer:amazon AND ").concat(encodeURIComponent(l.join(" OR "))));case 4:return n=t.sent,t.next=7,n.json();case 7:i=t.sent,g((null===i||void 0===i||null===(e=i.items)||void 0===e?void 0:e.slice(0,4))||[]),t.next=14;break;case 11:t.prev=11,t.t0=t.catch(0),console.error("Error fetching products:",t.t0);case 14:case"end":return t.stop()}}),t,null,[[0,11]])})))).apply(this,arguments)}l.length>0&&function(){t.apply(this,arguments)}()}),[]),N&&N.forEach((function(t,e){delete N[e].latest_subbuzz.id,N[e].latest_subbuzz.parent_buzz_canonical_url=N[e].detail_page_url})),0===N.length?null:(0,r.jsx)(Wn,{buzz:c,products:N})}var Wn=function(t){var e=t.buzz,n=t.products,i=(0,oe.J)({buzz:e}),a=(0,Qn.P)({buzz:e}),o=(0,u.useMemo)((function(){return{unit_type:"buzz_bottom",unit_name:e.id}}),[e.id]);return(0,r.jsxs)("div",{className:Pn().retailProductsGridWrapper,children:[(0,r.jsx)(it.Z,{id:"reactions-title",children:"Our Bestselling Products"}),(0,r.jsx)("div",{className:Pn().retailProductsGrid,ref:(0,Ue.Z)(i,a),children:(0,r.jsx)(Un.Z,{offset:0,len:4,products:n,useUnitTracking:be.R,tracking:o})})]})},Gn=n(66376),Fn=n(19925),Hn=n.n(Fn);var Jn=function(t){var e=t.buzz,n=(0,u.useMemo)((function(){return{position_in_unit:0,unit_type:"buzz_body",unit_name:"quiz_result",subunit_type:"component",subunit_name:"quiz_result_links",target_content_id:e.id,target_content_type:"buzz",item_name:e.id,item_type:"card"}}),[e.id]),i=(0,be.R)(n);return e.hasQuizSharedResult?(0,r.jsxs)("section",{className:Hn().container,children:[(0,r.jsx)(it.Z,{id:"takeQuizYourself",children:"Take this Quiz Yourself"}),(0,r.jsxs)("a",{className:Hn().quizLink,href:e.canonical_url,ref:i,children:[(0,r.jsx)("img",{src:e.picture,className:Hn().quizImg,alt:e.pictureAlt||""}),(0,r.jsx)("span",{className:Hn().quizTitle,children:e.title})]})]}):null},Vn=n(19103),qn=n.n(Vn);function Kn(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}var Xn=function(t){var e=new Date(t),n=new Date,i=Math.floor((n-e)/1e3),r=!0,u=!1,a=void 0;try{for(var o,c=[{label:"year",seconds:31536e3},{label:"month",seconds:2592e3},{label:"week",seconds:604800},{label:"day",seconds:86400},{label:"hour",seconds:3600},{label:"minute",seconds:60}][Symbol.iterator]();!(r=(o=c.next()).done);r=!0){var s=o.value,M=Math.floor(i/s.seconds);if(M>=1)return"".concat(M," ").concat(s.label).concat(M>1?"s":""," ago")}}catch(l){u=!0,a=l}finally{try{r||null==c.return||c.return()}finally{if(u)throw a}}return"Just now"},$n=function(t){var e=t.article,n=t.index,i=(0,u.useMemo)((function(){return{unit_name:"trending_posts",unit_type:"buzz_bottom",item_type:"card",item_name:e.id,position_in_unit:n+1,target_content_url:e.url,target_content_type:"buzz",target_content_id:e.id,data_source_name:"recsys_api",eventCategory:"list:bottom-feed"}}),[e,n]),a=(0,be.R)(i,!1,!1);return(0,r.jsx)("a",{href:"".concat(e.url,"?origin=btrend"),className:qn().link,ref:a,children:(0,r.jsxs)("div",{className:qn().cardWrapper,children:[(0,r.jsxs)("div",{className:qn().imageWrapper,children:[(0,r.jsx)("div",{className:qn().number,children:n+1}),(0,r.jsx)("img",{src:e.image,alt:e.title,className:qn().image})]}),(0,r.jsx)("div",{className:qn().timeAgo,children:Xn(e.created_at)}),(0,r.jsx)("div",{className:qn().category,children:"TVAndMovies"===e.category?"TV & MOVIES":e.category}),(0,r.jsx)("h2",{className:qn().title,children:e.name})]})})},ti=function(){var t=(0,u.useContext)(H.Z).base_url,e=(0,u.useState)([]),n=e[0],i=e[1];return(0,u.useEffect)((function(){var e=new AbortController,n=function(){var n,r=(n=kn().mark((function n(){var r,u;return kn().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,fetch("".concat(t,"/site-component/v1/en-us/trending"),{signal:e.signal});case 3:return r=n.sent,n.next=6,r.json();case 6:u=n.sent,i(u.results||[]),n.next=13;break;case 10:n.prev=10,n.t0=n.catch(0),e.signal.aborted||console.error("Fetch error:",n.t0);case 13:case"end":return n.stop()}}),n,null,[[0,10]])})),function(){var t=this,e=arguments;return new Promise((function(i,r){var u=n.apply(t,e);function a(t){Kn(u,i,r,a,o,"next",t)}function o(t){Kn(u,i,r,a,o,"throw",t)}a(void 0)}))});return function(){return r.apply(this,arguments)}}();return n(),function(){return e.abort()}}),[t]),n.length?(0,r.jsxs)("div",{className:qn().trending,children:[(0,r.jsx)("h1",{className:qn().header,children:"Trending on BuzzFeed"}),(0,r.jsx)("div",{className:qn().grid,children:n.slice(0,10).map((function(t,e){return(0,r.jsx)($n,{article:t,index:e},t.id)}))})]}):null},ei=n(51340),ni=n(6277),ii=function(t){var e=t.width,n=t.height;return(0,r.jsx)("svg",{width:e,height:n,viewBox:"0 0 12 8",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{d:"M1 1L6 6L11 1",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})},ri=n(82536),ui=n.n(ri),ai=n(4042),oi=n.n(ai),ci=120,si=200,Mi=function(t){var e=t.previousPath,n=(0,u.useState)(!1),i=n[0],a=n[1];return(0,r.jsxs)("button",{className:ui().returnButton,type:"button",onMouseEnter:function(){return a(!0)},onMouseLeave:function(){return a(!1)},onClick:function(){e&&(window.location.href=e)},style:{display:"inline-flex",alignItems:"center"},children:[(0,r.jsx)("span",{className:ui().returnButton__icon,style:{marginRight:"12px",display:"inline-flex",alignItems:"center",transform:i?"rotate(90deg)":"rotate(180deg)",transition:"transform 0.2s ease"},children:(0,r.jsx)(ii,{width:16,height:16})}),(0,r.jsx)("span",{className:ui().returnButton__text,children:"Return to Previous Article"})]})},li=function(t){var e=t.previousPath;return(0,r.jsx)("div",{style:{position:"relative",height:"".concat(ci,"px")},children:(0,r.jsx)("div",{className:(0,ni.Z)(ui().scrollCueBox,ui().isReturning),style:{position:"relative",height:"".concat(ci,"px"),backgroundImage:"url(".concat(oi(),")")},children:(0,r.jsx)("div",{className:ui().overlayHolder,children:(0,r.jsx)(Mi,{previousPath:e})})})})},ji=function(t){var e=t.previousPath,n=void 0===e?null:e,i=t.onExpandComplete,a=void 0===i?function(){}:i,o=t.onOffscreenScrollDown,c=void 0===o?function(){}:o,s=(0,l.Z)("(max-width: 600px)")?1:3,M=(0,u.useRef)(null),j=(0,u.useRef)(null),N=(0,u.useRef)(!1),g=(0,u.useRef)(null),d=(0,u.useRef)(!1),y=(0,u.useState)(3),L=y[0],T=y[1],z=(0,u.useState)(!1),m=z[0],p=z[1],I=(0,u.useState)(!1),f=I[0],D=I[1],x=(0,u.useState)(!1),b=x[0],O=x[1],A=(0,u.useState)(ci),h=A[0],_=A[1],S=(0,u.useState)(!1),w=S[0],E=S[1],v=function(){var t,e=null===(t=M.current)||void 0===t?void 0:t.getBoundingClientRect();E(e&&e.top>=0&&e.bottom<=window.innerHeight),e&&e.top<=40&&!f&&(_(ci),T(3))},C=function(){var t=M.current;if(t&&!f){var e=t.getBoundingClientRect(),n=window.innerHeight-e.bottom,i=Math.min(1,n/1200);t.style.backgroundPosition="center ".concat(100-40*i,"%"),t.style.backgroundImage="url(".concat(oi(),")"),e.bottom>-160?(window.scrollBy(0,15),g.current=requestAnimationFrame(C)):(cancelAnimationFrame(g.current),t.style.backgroundPosition="center top",t.style.height="".concat(ci,"px"),setTimeout((function(){T(3),_(ci),D(!0),O(!0),c()}),50))}};(0,u.useEffect)((function(){var t=null,e=function(t){d.current||(d.current=!0,function(t){var e=t>0;if(!(f||N.current&&e)){var n=t/(e?s:1);_((function(t){var i=e?Math.min(si,t+Math.abs(n)):Math.max(ci,t-Math.abs(n));return T(i<140?1:i<180?2:3),!N.current&&i>=190&&(N.current=!0,p(!0),a(),requestAnimationFrame(C)),i}))}}(t),setTimeout((function(){d.current=!1}),16))},n=function(t){v(),w&&e(t.deltaY)},i=function(n){if(w&&1===n.touches.length){var i=n.touches[0].clientY;if(null!==t)e(t-i);t=i}},r=function(){t=null};return window.addEventListener("wheel",n,{passive:!0}),window.addEventListener("touchmove",i,{passive:!0}),window.addEventListener("touchend",r),window.addEventListener("touchcancel",r),window.addEventListener("scroll",v,{passive:!0}),function(){window.removeEventListener("wheel",n),window.removeEventListener("touchmove",i),window.removeEventListener("touchend",r),window.removeEventListener("touchcancel",r),window.removeEventListener("scroll",v)}}),[w,f]),(0,u.useEffect)((function(){f&&(_(ci),T(3),p(!1),N.current=!1)}),[f]);var k=(h-ci)/80,Q=40+20*k,U=(0,u.useMemo)((function(){return{position:"relative",height:"".concat(h,"px"),transition:"none",willChange:"height, background-position",backgroundImage:m||f?"url(".concat(oi(),")"):"linear-gradient(rgba(140,162,233,0.9), rgba(140,162,233,0.9)), url(".concat(oi(),")"),backgroundPosition:"center top",backgroundRepeat:"no-repeat"}}),[h,m,f]);return(0,r.jsx)("div",{ref:j,style:{position:"relative",height:"".concat(f?ci:si,"px")},children:(0,r.jsxs)("div",{ref:M,tabIndex:0,className:(0,ni.Z)(ui().scrollCueBox,b&&ui().isReturning),style:U,children:[!m&&!f&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:ui().cueText,style:{position:"absolute",top:"".concat(Q,"px")},children:"KEEP SCROLLING FOR NEXT ARTICLE"}),(0,r.jsx)("div",{className:ui().cueArrows,children:[{width:36,height:62},{width:32,height:54},{width:24,height:42}].slice(0,L).map((function(t,e){var n=N.current?0:20*k*e;return 0===n&&e>0?null:(0,r.jsx)("div",{className:ui().cueArrowItem,style:{position:"absolute",left:"50%",transform:"translateX(-50%)",top:"".concat(Q+24+n,"px"),zIndex:L-e},children:(0,r.jsx)(ii,{width:t.width,height:t.height})},e)}))})]}),f&&(0,r.jsx)("div",{className:ui().overlayHolder,children:(0,r.jsx)(Mi,{previousPath:n})})]})})},Ni=n(42457),gi=n(84361),di=n(81374),yi=n(44381),Li=n(50896),Ti=n(29572),zi=n(27354),mi=n(99151),pi=(0,zt.Z)((function(t){var e=t.buzz,n=void 0===e?{}:e;return(0,gi.k)(n),(0,di.L)(n),(0,yi.y)(n),(0,Li.s)(n),(0,Ti.mH)(n.id),(0,zi.l)(n.id),(0,mi.Z)(n.id),null}),{onError:s.Tb}),Ii=function(t){var e=t.buzz,n=t.subbuzzData,i=(0,u.useRef)(),a=(0,u.useContext)(w.Z).setValue;return(0,u.useEffect)((function(){var t="".concat((null===e||void 0===e?void 0:e.canonical_path)||(null===e||void 0===e?void 0:e.canonical_url),"?origin=shp-is");window.location.pathname+window.location.search!==t&&window.history.pushState(null,"",t),null===a||void 0===a||a(e)}),[null===e||void 0===e?void 0:e.id]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Ni.default,{buzz:e,subbuzzData:n,badges:e.badges||[]}),(0,r.jsx)(pi,{buzz:e}),(0,r.jsxs)("div",{ref:i,style:{margin:"40px 0"},children:[(0,r.jsx)(Dt.Z,{buzz:e,badges:[]}),(0,r.jsx)(je.ZP,{buzz:e,subbuzzData:n,pixiedust:{unit_type:"buzz_body",unit_name:null===e||void 0===e?void 0:e.id,position_in_unit:0}},e.id)]})]})},fi=u.memo(Ii);fi.displayName="Post";var Di=fi;function xi(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function bi(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function Oi(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){bi(u,i,r,a,o,"next",t)}function o(t){bi(u,i,r,a,o,"throw",t)}a(void 0)}))}}function Ai(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function hi(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function _i(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Ai(t,e,n[e])}))}return t}function Si(t){return function(t){if(Array.isArray(t))return t}(t)||hi(t)||Ei(t,i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wi(t){return function(t){if(Array.isArray(t))return xi(t)}(t)||hi(t)||Ei(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ei(t,e){if(t){if("string"===typeof t)return xi(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?xi(t,e):void 0}}var vi=function(t){var e=t.previousPath,n=void 0===e?"":e,i=t.disableDefaultIS,a=t.setActiveBuzz,o=(0,u.useState)([]),c=o[0],s=o[1],M=(0,u.useState)([]),l=M[0],N=M[1],g=(0,u.useState)(null),d=g[0],y=g[1],L=(0,u.useState)(null),T=L[0],z=L[1],m=(0,u.useState)(!1),p=m[0],I=m[1],f=(0,u.useState)(n),D=f[0],x=f[1],b=(0,u.useRef)(!0),O=/Safari/.test(navigator.userAgent)&&!/Chrome|Chromium|CriOS|FxiOS|Edg|OPR/.test(navigator.userAgent);(0,u.useEffect)((function(){return function(){b.current=!1}}),[]);var A=(0,u.useCallback)(function(){var t=Oi(kn().mark((function t(e){var n,i,r,u,a,o,c=arguments;return kn().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=c.length>1&&void 0!==c[1]?c[1]:"",t.prev=1,"string"===typeof e){t.next=5;break}return t.abrupt("return",null);case 5:return t.next=7,fetch("".concat(e,".json"));case 7:if((r=t.sent).ok){t.next=10;break}return t.abrupt("return",null);case 10:return t.next=12,r.json();case 12:if(u=t.sent,(null===(a=u.buzz||u)||void 0===a?void 0:a.id)&&(null===a||void 0===a||null===(i=a.metadata)||void 0===i?void 0:i.title)){t.next=16;break}return t.abrupt("return",null);case 16:return t.abrupt("return",{id:a.id,buzz:_i({},a,{referrer:n}),subbuzzData:null!==(o=u.subbuzzes)&&void 0!==o?o:{subbuzzes:[],assets:{}},visible:!0});case 20:return t.prev=20,t.t0=t.catch(1),t.abrupt("return",null);case 23:case"end":return t.stop()}}),t,null,[[1,20]])})));return function(e){return t.apply(this,arguments)}}(),[]),h=(0,u.useCallback)(Oi(kn().mark((function t(){var e,i,r,u,a,o;return kn().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!p&&b.current){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,fetch("https://recsys-api.buzzfeed.com/web/shopping-package-internal?page_size=10");case 4:return e=t.sent,t.next=7,e.json();case 7:return i=t.sent,r=(i.results||[]).map((function(t){return t.url})).filter((function(t){return"string"===typeof t})),s(r.slice(1)),t.next=12,A(r[0],n);case 12:(u=t.sent)&&b.current&&(x((null===(a=u.buzz)||void 0===a?void 0:a.canonical_path)||(null===(o=u.buzz)||void 0===o?void 0:o.canonical_url)||""),y(u));case 14:case"end":return t.stop()}}),t)}))),[A,n,p]),_=(0,u.useCallback)(Oi(kn().mark((function t(){var e,n,i,r,u,a;return kn().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(0!==c.length){t.next=2;break}return t.abrupt("return");case 2:return e=Si(c),n=e[0],i=e.slice(1),b.current&&s(i),t.next=6,A(n,D);case 6:(r=t.sent)&&b.current&&(x((null===(u=r.buzz)||void 0===u?void 0:u.canonical_path)||(null===(a=r.buzz)||void 0===a?void 0:a.canonical_url)||""),y(r));case 8:case"end":return t.stop()}}),t)}))),[c,A,D]);(0,u.useEffect)((function(){if(d&&b.current){var t=requestAnimationFrame((function(){b.current&&(N((function(t){return t.find((function(t){var e,n;return(null===(e=t.buzz)||void 0===e?void 0:e.id)===(null===(n=d.buzz)||void 0===n?void 0:n.id)}))?t:wi(t).concat([d])})),I(!0),y(null))}));return function(){return cancelAnimationFrame(t)}}}),[d]),(0,u.useEffect)((function(){Oi(kn().mark((function t(){return kn().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,h();case 3:t.next=8;break;case 5:t.prev=5,t.t0=t.catch(0),console.error("loadInitial failed:",t.t0);case 8:case"end":return t.stop()}}),t,null,[[0,5]])})))()}),[h]);var S=function(t){N((function(e){if(t<0||t>=e.length)return e;var n=wi(e);return n[t]=_i({},n[t],{visible:!1}),n}))},w=(0,u.useCallback)(function(){var t=Oi(kn().mark((function t(e){var n;return kn().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=l[e],O&&n&&b.current&&(z(_i({},n,{previousPath:D})),S(e)),t.next=4,_();case 4:O&&b.current&&requestAnimationFrame((function(){setTimeout((function(){window.scrollTo({top:800,behavior:"auto"})}),50)})),"function"===typeof i&&i(!0);case 6:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),[O,l,_,i,D]);return(0,u.useEffect)((function(){var t=Node.prototype.removeChild;return Node.prototype.removeChild=function(e){try{return t.call(this,e)}catch(n){return null}},function(){Node.prototype.removeChild=t}}),[]),(0,r.jsxs)("div",{children:[T&&(0,r.jsx)(li,{previousPath:T.previousPath},"final-".concat(T.id)),l.map((function(t,e){var n,i;if(!t.visible)return null;var o="".concat(null===(n=t.buzz)||void 0===n?void 0:n.id,"-").concat((null===(i=t.buzz)||void 0===i?void 0:i.canonical_path)||"");return(0,r.jsxs)(u.Fragment,{children:[(0,r.jsx)(j.Z,{fallback:null,children:(0,r.jsx)(Di,{buzz:t.buzz,subbuzzData:t.subbuzzData,index:e,canActivate:!0,setActiveIndex:function(){},setActivePost:function(){},setActiveBuzz:a})}),(0,r.jsx)(ji,{onExpandComplete:function(){return w(e)},onOffscreenScrollDown:function(){if(!O){var t=l[e];t&&b.current&&(z(_i({},t,{previousPath:D})),S(e))}}})]},o)}))]})},Ci=(0,D.Z)((function(){return n.e(520).then(n.bind(n,51336))}),{ssr:!1}),ki=(0,D.Z)((function(){return n.e(562).then(n.bind(n,4195))}),{ssr:!0}),Qi=(0,D.Z)((function(){return Promise.all([n.e(893),n.e(969)]).then(n.bind(n,77402))}),{ssr:!1}),Ui=(0,D.Z)((function(){return n.e(84).then(n.bind(n,89813))}),{ssr:!1});function Yi(t){var e,n,i=t.buzz,a=t.subbuzzData,o=t.badges,c=t.showOverlay,y=t.setShowOverlay,L=t.giftguideHeader,T=(0,u.useState)(i),m=T[0],I=T[1],f=(0,u.useRef)(null),D=(0,M.Z)({once:!0}),x=D.isIntersecting,b=D.setObservable,O=(0,u.useContext)(H.Z).destination,A=(0,u.useContext)(p.Z).commentsEnabled,_=(0,l.Z)("(max-width:500px)"),E=(0,l.Z)("(min-width: 64rem)"),k=i.isTrivia||i.isPersonality,Q=!(i.shouldHideBuzzSharing||i.shouldShowPostContentOnly)&&!_||(0,g.G)(i),U=(0,u.useState)(null),Y=U[0],P=U[1],Z=(0,u.useContext)(S.Z),B=Z.getFeatureFlagValue,R=Z.experiments,J=Z.getExperimentValue,V=(0,u.useState)(!1),q=V[0],K=V[1],X=(0,u.useState)(!1),$=X[0],tt=X[1],et=(0,u.useState)(!1),nt=et[0],it=et[1],rt=(0,u.useState)(!1),ut=rt[0],at=rt[1],ot=(0,u.useState)("control"),ct=ot[0],st=ot[1],Mt=(0,u.useState)(!1),lt=Mt[0],jt=Mt[1],Nt=(0,u.useState)(null),gt=Nt[0],dt=Nt[1],Lt=!!(i.published&&parseInt(i.published,10)>1710959400),Tt=(0,u.useState)(!1),zt=Tt[0],mt=Tt[1],pt=null===i||void 0===i||null===(e=i.metadata)||void 0===e?void 0:e.subbuzzId;(0,u.useEffect)((function(){pt&&mt(!0)}),[]),(0,u.useEffect)((function(){var t=B("shoppybot_bpage_promo"),e=J("shoppy-bpage-test",{rejectErrors:!1});tt(!("chat"!==e&&!t))}),[B,J]),(0,u.useEffect)((function(){"is"===J("ads_shopping_infinite_scroll",{rejectErrors:!1})&&K(!0)}),[J,i]),(0,u.useEffect)((function(){var t=J("RT-946-shopping-recirc-bfn",{rejectErrors:!1});(0,g.G)(i)&&"var1"===t&&it(!0)}),[J]),(0,u.useEffect)((function(){R&&R.loaded&&st(J("ads_retail_media_network",{rejectErrors:!1}))}),[R,J]),(0,u.useEffect)((function(){R&&R.loaded&&!(0,Mn.rK)(i)&&at(B("RT-1378-us-quiz-top-sticky-desktop")||B("ads-quiz-top-player-mobile"))}),[R,B,J]),(0,u.useEffect)((function(){if(R&&R.loaded){var t=B("taboola_apac");jt(t)}}),[R,B]),(0,u.useEffect)((function(){R&&R.loaded&&dt(B("RT-1749-move-package-above-comments"))}),[R,B]);var It=(0,u.useCallback)((function(){y(!1)}),[y]),xt=/Safari/.test(navigator.userAgent)&&!/Chrome|Chromium|CriOS|FxiOS|Edg|OPR/.test(navigator.userAgent),bt=(0,u.useState)(!1),Ot=bt[0],At=bt[1],ht=(0,u.useState)(!1),_t=ht[0],St=ht[1],wt=(0,u.useState)(!1),Et=wt[0],vt=wt[1],Ct=(0,u.useRef)(!0);(0,u.useEffect)((function(){return Ct.current=!0,function(){Ct.current=!1}}),[]);var kt=(0,u.useCallback)((function(){_t||St(!0)}),[_t]),Qt=(0,u.useCallback)((function(){Ot||requestAnimationFrame((function(){requestAnimationFrame((function(){Ct.current&&At(!0)}))}))}),[Ot]);return(0,r.jsxs)("main",{"aria-hidden":zt,id:"buzz-content",className:"".concat(z().main," ").concat(z()[O]," embed-content"),ref:f,children:[(0,r.jsxs)(j.Z,{onError:s.Tb,children:[!i.disableAds&&(0,r.jsx)(v,{}),(0,r.jsx)(yn,{buzz:i})]}),(0,r.jsx)(en,{buzz:i,isOpen:zt,onRequestClose:function(){return mt(!1)},subbuzzData:a,subbuzzId:pt},"shared-modal-".concat(i.id)),(0,r.jsxs)("div",{className:"".concat(z().article," ").concat(z().default," ").concat(z()[O]," embed-post"),children:[(0,r.jsxs)("div",{className:z().content,children:[(0,r.jsxs)("article",{children:[!Ot&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Dt.Z,{buzz:i,badges:o}),(0,r.jsx)(h,{buzz:i,commentCount:Y,showShareBar:Q,destination:O,isFloatingShareButtonEnd:x}),(0,r.jsx)(Be,{buzz:i,postRef:f}),i.tags.some((function(t){return["etsy-strategy","etsy_strategy","etsy strategy"].includes(t)}))&&(0,r.jsx)(jn,{pixiedust:{unit_type:"buzz_body",unit_name:i.id,position_in_unit:0}}),(0,r.jsx)(vn,{}),ut&&(0,r.jsx)(ei.Z,{buzz:i,showOnTopQuiz:!0}),(0,r.jsx)(je.ZP,{buzz:i,subbuzzData:a,pixiedust:{unit_type:"buzz_body",unit_name:i.id,position_in_unit:0}},i.id),i.hasStudiosPromoUnit&&(0,r.jsx)(le,{buzz:i}),(0,g.G)(i)&&(0,r.jsx)(Ae,{buzz:i}),(0,r.jsx)(Ln,{buzz:i}),(0,r.jsx)(W,{buzz:i}),(0,r.jsx)(F.Z,{buzz:i}),!E&&i.walmartBundle&&(0,r.jsx)(Ci,{buzz:i})]}),i.isShopping&&q&&(0,r.jsxs)(r.Fragment,{children:[!Et&&(0,r.jsx)(ji,{onExpandComplete:function(){kt(),xt&&Qt()},onOffscreenScrollDown:function(){xt||Qt()},previousPath:i.canonical_path||i.canonical_url}),_t&&(0,r.jsx)(vi,{disableDefaultIS:vt,setActiveBuzz:I})]})]}),(0,r.jsx)(on,{buzz:i}),(0,r.jsxs)(j.Z,{onError:s.Tb,children:[(0,r.jsx)(Jn,{buzz:i}),(0,r.jsx)(C.Z,{type:"subbuzz",pixiedust:{unit_type:"buzz_bottom",unit_name:i.id,position_in_unit:0}}),"Japan"===i.classification.edition&&(0,r.jsx)(Le,{isMobile:_}),lt&&(0,r.jsx)(Ie,{}),"Shopping"===(null===i||void 0===i||null===(n=i.classification)||void 0===n?void 0:n.section)&&(0,r.jsx)(Ui,{buzz:i,giftguideHeader:L}),(0,r.jsx)(Je.Z,{buzz:i}),!i.isShopping&&(0,r.jsxs)(r.Fragment,{children:[!Lt&&(k?(0,r.jsx)(te,{}):(0,r.jsx)(Vt,{})),Lt&&(0,r.jsx)(yt,{contentId:i.id,isSponsored:(0,d.f)(i)}),!0===gt&&(0,r.jsx)(Bt.Z,{style:{marginBottom:"3rem"},isNewDesign:!0,isShoppingDesign:!0}),A&&(0,r.jsx)(G.Z,{buzz:i,commentCount:Y,onCommentsLoaded:P,firstPage:100,repliesFirstPage:2,perPage:100}),A&&(0,r.jsx)(fe.Z,{buzz:i,subunitName:"comments",subunitType:"component",unitType:"buzz_bottom"}),"var1"===ct&&(0,r.jsx)(Rn,{version:ct,buzz:i})]}),(0,r.jsx)(zn,{}),i.tags.some((function(t){return"--commerce-user-reviews"===t}))&&(0,r.jsx)(ki,{buzz:i}),"buzzfeed"===O&&!i.isShopping&&!(0,g.G)(i)&&(0,r.jsxs)(r.Fragment,{children:[!1===gt&&(0,r.jsx)(Bt.Z,{style:{marginBottom:"3rem"},isNewDesign:!0}),"var2"===ct&&(0,r.jsx)(Rn,{version:ct,buzz:i}),(0,r.jsx)(C.Z,{type:"story-bpage-desktop",pixiedust:{unit_type:"buzz_bottom",unit_name:i.id,position_in_unit:1},style:{marginTop:"-2rem"}})]})||"var2"===ct&&(0,r.jsx)(Rn,{version:ct,buzz:i}),!i.isShopping&&(0,r.jsx)(ee.d$,{}),i.isShopping&&!q&&"var1"===ct&&(0,r.jsx)(Rn,{version:ct,buzz:i}),$&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{style:{height:_?"575px":"700px",margin:"40px 0"},children:(0,r.jsx)("iframe",{border:"0",height:"100%",id:"chatframe",scrolling:"no",src:"/ai-ui/games/shoppy-chat?page=".concat(i.id),width:"100%"})})}),(0,r.jsx)(Zt.Z,{}),(0,r.jsx)(ft.Z,{}),_&&!q&&(0,r.jsx)(C.Z,{type:"bigstory",pixiedust:{unit_type:"buzz_bottom",unit_name:i.id,position_in_unit:1}}),k&&(0,r.jsx)(Qi,{})]})]}),(0,r.jsx)(w.E,{value:m,children:(0,r.jsx)(Gn.Z,{pixiedust:{unit_type:"sidebar",unit_name:"right",position_in_unit:0}},null===m||void 0===m?void 0:m.id)})]}),(0,r.jsxs)(j.Z,{onError:s.Tb,children:["buzzfeed"===O&&i.isShopping&&!(0,g.G)(i)&&!q&&(0,r.jsx)(Bt.Z,{style:{marginTop:_?"1rem":"-2rem"},isNewDesign:!0}),i.isShopping&&"Japan"!==i.classification.edition&&!q&&(0,r.jsx)(ti,{}),!i.isShopping&&(0,r.jsx)(He,{}),"Japan"===i.classification.edition&&(0,r.jsx)(Le,{placement:2,isMobile:_}),(0,g.G)(i)&&(0,r.jsx)(N.Z,{jumpTo:"news-footer",className:"skipToFooter",label:"Skip to footer"}),(0,g.G)(i)&&nt&&(0,r.jsx)(Bt.Z,{style:{marginBottom:"3rem",marginTop:"-2rem"},isNewDesign:!0}),(0,r.jsx)("div",{ref:b}),!q&&(0,r.jsx)(ee.cM,{})]}),c&&(0,r.jsx)(Gt,{onOverlayClick:It})]})}Yi.propTypes={buzz:o().object.isRequired,badges:o().array,subbuzzData:o().shape({assets:o().object,subbuzzes:o().array,gateIndex:o().number})};var Pi=Yi;function Zi(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Bi(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Zi(t,e,n[e])}))}return t}function Ri(t,e){if(null==t)return{};var n,i,r=function(t,e){if(null==t)return{};var n,i,r={},u=Object.keys(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Wi(t){var e=t.buzz,n=Ri(t,["buzz"]);return function(t,e){var n=(0,u.useContext)(S.Z),i=n.experiments,r=n.getFeatureFlagValue;(0,u.useEffect)((function(){e&&e.eventBus&&i&&i.loaded&&(r("RT-1510-ButtonPostTap-Non-Amazon")||e.eventBus.on("ads:initialized",(function(){(0,Nn.v)("https://www.dwin2.com/pub.304459.min.js")})))}),[e,t,i,r])}(e,(0,u.useContext)(c.$)),"wide"===e.format.page_width?(0,r.jsx)(sn,Bi({buzz:e},n)):(0,r.jsx)(Pi,Bi({buzz:e},n))}Wi.propTypes={buzz:o().object.isRequired};var Gi=Wi},93935:function(t,e,n){"use strict";function i(t){return!(t.flags.sensitive||"news"===t.metavertical||"Japan"===t.classification.edition||"France"===t.classification.edition||t.tags.includes("--add-yours")||t.tags.includes("--post-content-only"))}n.d(e,{h:function(){return i}})},11455:function(t,e,n){"use strict";function i(t){return!(t.flags.sensitive||"news"===t.metavertical||"Japan"===t.classification.edition||"France"===t.classification.edition||t.tags.includes("--add-yours")||t.tags.includes("--post-content-only"))}n.d(e,{h:function(){return i}})},15208:function(t,e,n){"use strict";function i(t){return![t.flags.sensitive,t.shouldHideRecircSection].some((function(t){return t}))}n.d(e,{h:function(){return i}})},60382:function(t){"use strict";t.exports={isEligible:function(t){return![t.isAd,t.isShopping,t.bfpFormats.includes("related_links"),"long"===t.format.page_type,t.tags.includes("coronavirus")||t.tags.includes("Coronavirus"),!t.isEnglish,!t.isModerated,t.shouldHideRecircSection].some((function(t){return t}))}}},42494:function(t,e,n){"use strict";n.d(e,{cM:function(){return L},d$:function(){return j},ru:function(){return T}});var i=n(36491),r=n(52322),u=n(2784),a=n(41332),o=n(54761),c=n(60382);function s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function M(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){s(t,e,n[e])}))}return t}var l=(0,i.Z)((function(){return Promise.all([n.e(863),n.e(54)]).then(n.bind(n,51425))}),{ssr:!1});function j(t){var e=(0,u.useContext)(a.Z).buzz,n=void 0===e?{}:e;return(0,c.isEligible)(n)?(0,r.jsx)(o.Z,{children:(0,r.jsx)(l,M({},t))}):null}var N=n(15208);function g(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function d(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){g(t,e,n[e])}))}return t}var y=(0,i.Z)((function(){return Promise.all([n.e(863),n.e(853)]).then(n.bind(n,26176))}),{ssr:!1});function L(t){var e=(0,u.useContext)(a.Z).buzz,n=void 0===e?{}:e;return(0,N.h)(n)?(0,r.jsx)(o.Z,{lazy:!n.isAd,children:(0,r.jsx)(y,d({},t))}):null}var T=(0,i.Z)((function(){return Promise.all([n.e(863),n.e(38)]).then(n.bind(n,5478))}),{ssr:!1});(0,i.Z)((function(){return Promise.all([n.e(863),n.e(461)]).then(n.bind(n,26265))}),{ssr:!1})},53365:function(t,e,n){"use strict";n.d(e,{Z:function(){return M}});var i=n(52322),r=n(2784),u=n(13980),a=n.n(u),o=n(16938),c=r.createContext({}),s=c.Provider;function M(t){var e=t.children,n={isMobile:(0,o.Z)("(max-width:500px)")};return(0,i.jsx)(s,{value:n,children:e})}M.propTypes={children:a().oneOfType([a().array,a().object])}},82924:function(t,e,n){"use strict";n.d(e,{N:function(){return M}});var i=n(52322),r=n(2784),u=n(13980),a=n.n(u),o=n(99112),c=n(62245),s=n.n(c);function M(t){var e=t.children,n=t.id,u=(0,r.useContext)(o.Z).destination;return e?(0,i.jsx)("h2",{id:n,className:"".concat(s().sectionTitle," ").concat(s()[u]),children:e}):null}M.propTypes={children:a().oneOfType([a().arrayOf(a().node),a().node]),id:a().string},e.Z=M},48492:function(t,e,n){"use strict";n.d(e,{WX:function(){return b},ZP:function(){return O}});var i=n(52322),r=n(2784),u=n(13980),a=n.n(u),o=n(5103),c=n(15404),s=n(17160),M=n.n(s),l=n(16938),j=n(17967),N=n(81550),g=n(73931),d=n(46811),y=n(30353),L=n(88163);function T(t){var e=t.platformOptions,n=t.url,r=(0,L.Z)({rootMargin:"0px 0px -50% 0px"}),u=r.isIntersecting,a=r.setObservable;return(0,i.jsx)("li",{ref:a,children:(0,i.jsx)(j.T,{className:"".concat(M().shareButton," ").concat(M().pinterestOverlay," ").concat(u?M().show:""),label:e.savePinText||"Save",platform:"pinterest",platformOptions:e,showLabel:!0,url:n,variant:"small"})})}T.propTypes={platformOptions:a().object,url:a().string};var z=T,m=n(63778),p=n(24678);function I(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){I(t,e,n[e])}))}return t}var D={copy:{copyFailText:"unable_to_copy_link",copySuccessText:"link_copied",shareOnText:"copy_link"},email:{shareOnText:"email"},bluesky:{shareOnText:"share_on_x"},facebook:{shareOnText:"share_on_x"},lineapp:{shareOnText:"share_on_x"},more:{shareOnText:"more_sharing_options"},pinterest:{savePinText:"save_pin",shareOnText:"share_on_x"},sms:{shareOnText:"share_on_x"},snapchat:{shareOnText:"share_on_x"},twitter:{shareOnText:"share_on_x"},vk:{shareOnText:"share_on_x"},whatsapp:{shareOnText:"share_on_x"}},x={bluesky:"Bluesky",facebook:"Facebook",pinterest:"Pinterest",twitter:"Twitter",vk:"VK"};function b(t){var e=t.buzz,n=t.heading,u=t.children,a=t.position,s=void 0===a?"":a,N=t.platformOverrides,L=void 0===N?{}:N,T=t.showLabel,p=void 0!==T&&T,I=t.type,b=void 0===I?"pagelevel":I,O=t.url,A=void 0===O?"":O,h=t.className,_=void 0===h?"":h,S=t.subbuzzId,w=void 0===S?void 0:S,E=t.onLoaded,v=void 0===E?function(){}:E,C=(0,o.useTranslation)("common",{i18n:o.i18n}).t,k=["pagelevel","pagelevelList","pagelevelSticky","pageLevelOutlineThick"].includes(b),Q=k?"500px":"51.9rem",U=(0,l.Z)("(max-width:".concat(Q,")")),Y=(0,c.f)(e,w),P=(0,d.Bd)(b,e,U,s),Z=(0,r.useState)(!1),B=Z[0],R=Z[1],W=e.destination_name;(0,r.useEffect)((function(){R(!0)}),[]);var G={className:"".concat(M().shareButton," ").concat(M()[b]||""," ").concat(M()[s]||""),type:b,showLabel:p,url:A||e.canonical_url};k?G.variant=U?"small":"large":"subbuzz"===b&&(G.variant="outline"),"pageLevelOutlineThick"===b&&(G.variant="outlineThick"),"buzzfeed_news"===W&&(G.variant=["pagelevel","pageLevelOutlineThick"].includes(b)?"outlineMonochrome":"small");var F=P&&1===P.length&&"pinterest"===P[0]&&"subbuzz"===b,H=[];return P.forEach((function(t,n){if(e.shareData[t]||"more"===t){var u=D[t],a={};Object.keys(u).forEach((function(e){a[e]=C(u[e],{name:x[t]})}));var o=(0,m.decodeHtmlEntities)(e.shareData[t].title),c=f({},e.shareData[t],a,{title:o});if(L&&L[t]&&Object.keys(L[t]).forEach((function(e){c[e]=L[t][e]})),"facebook"===t)c.fbAppId=y.destinations[W].facebook_app_id;else if("email"===t){var M=(0,g.X)({shareUrl:G.url,platform:t});c.body="".concat(M,"\n\nGet the BuzzFeed App: https://bzfd.it/bfmobileapps")}if("pinterest"===t&&F)H.push((0,r.createElement)(z,f({platformOptions:c},G,{key:"pinterestShare"})));else{var l=s.replace(/_/g,"-");H.push((0,i.jsx)("li",{children:(0,i.jsx)(j.T,f({platform:t,platformOptions:c,tracking:Y({platform:t,position:l}),onLoaded:function(e,i){return v({element:e,index:n,platform:t,shareUrl:i})}},G))},n))}}})),(0,i.jsxs)("div",{role:"group","aria-label":n||"Share",className:F?M().shareBarContainer:_,children:[n&&(0,i.jsx)("h2",{className:M().heading,children:n}),(0,i.jsxs)("ul",{className:"".concat(M().shareBar," ").concat(M()[b]||""," ").concat(B?M().loaded:""," ").concat(M()[W]),children:[H,u]})]})}b.propTypes={buzz:a().object,className:a().string,position:a().oneOf(["top_share_bar","bottom_share_bar","subbuzz","quiz_results"]),platformOverrides:a().object,showLabel:a().bool,type:a().oneOf(["pagelevel","subbuzz","pagelevelList","pagelevelSticky","pageLevelOutlineThick"]),url:a().string};var O=(0,N.Z)(b,{onError:p.Tb})},64994:function(t,e,n){"use strict";var i=n(52322),r=n(2784),u=n(22813),a=n(48492),o=n(6652),c=n.n(o),s=n(46811),M=n(5103),l=n(27374);e.Z=function(t){var e=t.buzz,n=(0,r.useState)(!1),o=n[0],j=n[1],N=(0,M.useTranslation)("common").t;(0,r.useEffect)((function(){(0,s.Hd)()&&j(!0)}),[]);var g="standard";(0,l._)(e)?g="discussion":e.isShopping?g="shopping":e.isQuiz&&(g="quiz");var d=["quiz","discussion"].includes(g),y=d&&!o&&"shopping"!==g,L=d&&o;return(y||L)&&(0,i.jsxs)("div",{className:c().shareBottom,children:[(0,i.jsx)("h3",{className:c().shareCta,children:N({standard:"share_article",shopping:"share_article",discussion:"share_discussion",quiz:"share_quiz"}[g])}),(0,i.jsxs)("div",{className:c().shareBar,children:[y&&(0,i.jsx)(a.ZP,{buzz:e,type:"pageLevelOutlineThick",position:"bottom_share_bar"}),L&&(0,i.jsx)("div",{className:c().nativeShareButton,children:(0,i.jsx)(u.Z,{variant:"standard",includeShortTitle:!0,trackingData:{unit_name:e.id,unit_type:"buzz_bottom",subunit_type:"component",subunit_name:"share_buttons"}})})]})]})}},66376:function(t,e,n){"use strict";n.d(e,{Z:function(){return ut}});var i=n(52322),r=n(16938),u=n(81550),a=n(24678),o=n(2784),c=n(41332),s=n(99112),M=n(5103),l=n(36491),j=n(86415),N=n.n(j),g=n(31178),d=n(94776),y=n.n(d),L=n(81298),T=n(13980),z=n.n(T),m=n(42235),p=n(31314),I=n.n(p);function f(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function D(t){var e=t.dataSource,n=t.options,r=void 0===n?{}:n,u=t.displayName,a=void 0===u?"":u,c=(0,o.useRef)(),s=(0,o.useRef)(),M=(0,o.useState)(!1),l=M[0],j=M[1];return(0,o.useEffect)((function(){c.current&&function(){var t,n=(t=y().mark((function t(){return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(window.twttr){t.next=3;break}return t.next=3,(0,m.v)("https://platform.twitter.com/widgets.js");case 3:s.current&&c.current.removeChild(s.current),s.current=document.createElement("div"),c.current.appendChild(s.current);try{window.twttr.widgets.createTimeline(e,s.current,r)}catch(n){j(!0)}case 7:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){f(u,i,r,a,o,"next",t)}function o(t){f(u,i,r,a,o,"throw",t)}a(void 0)}))});return function(){return n.apply(this,arguments)}}()()}),[e,r]),(0,i.jsxs)(i.Fragment,{children:[l&&(0,i.jsxs)("a",{href:e.url,className:I().twitterTimelineFallback,children:[(0,i.jsx)(L.Zm,{width:16,height:16}),"Tweets by ",a]}),(0,i.jsx)("div",{ref:c,style:{height:null===r||void 0===r?void 0:r.height}})]})}D.propTypes={dataSource:z().object.isRequired,options:z().object};var x=D;function b(t){var e=t.url,n=(0,o.useRef)(),r=(0,o.useContext)(s.Z).facebook_app_id;return(0,o.useEffect)((function(){n.current&&("undefined"===typeof window.FB?(window.fbAsyncInit=function(){window.FB.init({appId:r,xfbml:!1,version:"v2.9"}),window.FB.XFBML.parse(n.current)},(0,m.v)("https://connect.facebook.net/en_US/sdk.js")):window.FB.XFBML.parse(n.current))}),[e,r]),(0,i.jsx)("div",{ref:n,children:(0,i.jsx)("div",{className:"fb-page","data-href":e,"data-show-facepile":!0,"data-tabs":"timeline","data-width":"500","data-adapt-container-width":"true",style:{width:"100%"}})})}b.propTypes={url:z().string.isRequired};var O=b;var A=function(t){var e=t.twitterPageUrl,n=t.facebookPageUrl,r=t.displayName,u=void 0===r?"":r,a=(0,o.useMemo)((function(){return{sourceType:"url",url:e}}),[e]),c=(0,o.useMemo)((function(){return{height:600}}),[]);return e||n?(0,i.jsxs)("div",{className:g.container,children:[n&&(0,i.jsx)(O,{url:n}),e&&(0,i.jsx)(x,{dataSource:a,options:c,displayName:u})]}):null},h=n(42494),_=n(83661);var S=function(t){var e=t.buzz,n=t.containerRef,r=t.pixiedust,u=e.bylines.find((function(t){return t.username===e.username}))||{},a=["twitter_page_url","facebook_page_url"].reduce((function(t,e){return u[e]&&t++,t}),0);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(_.Z,{type:"bigstory",pixiedust:r}),(0,i.jsx)(A,{twitterPageUrl:u.twitter_page_url,facebookPageUrl:u.facebook_page_url,displayName:u.display_name}),(0,i.jsx)(h.ru,{containerRef:n,displayName:u.display_name,widgetCount:a})]})},w=n(59855),E=n(16693),v=n(88163),C=n(9772);var k=function(t){var e=t.adManager,n=t.sidebarPage,r=t.mode,u=t.acquireLock,a=t.releaseLock,c=t.pixiedust,s=(0,o.useState)(null),M=s[0],l=s[1],j=(0,o.useState)(!1),N=j[0],g=j[1],d=(0,o.useCallback)((function(t){g(!1),a(t)}),[a]);return(0,o.useEffect)((function(){u(),g(!0)}),[u]),(0,o.useEffect)((function(){if(e){var t=e.getAdForPlacement(n);t&&(t.slot.targeting||(t.slot.targeting={}),0!==n&&(t.slot.targeting.reload="true"),t.slot.targeting.sidebarPage=n+1),l(t)}else l(null)}),[e,n]),e&&M?(0,i.jsx)(_.Z,{config:M.slot,onAdRender:N&&"preload"===r?d:null,onAdViewed:N&&"active"===r?d:null,pixiedust:c}):null},Q=n(99404),U=n(35167);function Y(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function P(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){Y(t,e,n[e])}))}return t}function Z(t,e){if(t.isNews){var n=U.vc[e].size.filter((function(t){return JSON.stringify(t)!==JSON.stringify([300,600])}));U.vc[e].size=n}return P({},U.vc[e],{observeDimensions:!0,loopVideo:!1})}function B(t){var e=t.isNews?[U.J7.NATIVE,U.J7.PROGRAMMATIC_MEDIUM_RECTANGLE,U.J7.AMAZON_OUTSTREAM]:[U.J7.NATIVE,U.J7.PROGRAMMATIC_MEDIUM_RECTANGLE,U.J7.PROGRAMMATIC_VERTICAL,U.J7.AMAZON_OUTSTREAM],n=P({},Z(t,"sidebar1-bp"),{size:e});return new Q.ZP({config:{units:[n].concat((0,U.kE)(9,2).map((function(e){return Z(t,"sidebar".concat(e,"-bp"))}))),unitsRepeated:[Z(t,"sidebar-bp-infinite")],pattern:[!0]}})}var R=n(70833);var W=n(32966),G=n(49101);function F(t){var e=t.mode,n=t.children,r=t.sidebarPage,u=t.acquireLock,a=t.releaseLock;if("inactive"===e)return null;var c=o.cloneElement(n,{mode:e,sidebarPage:r,acquireLock:u,releaseLock:a});return(0,i.jsx)("div",{className:"preload"===e?"js-hidden":"",children:c})}function H(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function J(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function V(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var i,r,u=[],a=!0,o=!1;try{for(n=n.call(t);!(a=(i=n.next()).done)&&(u.push(i.value),!e||u.length!==e);a=!0);}catch(c){o=!0,r=c}finally{try{a||null==n.return||n.return()}finally{if(o)throw r}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return H(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return H(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}F.propTypes={mode:z().oneOf(["active","inactive","preload"])};var q=function(t){var e=t.sidebarPage,n=t.children,r=t.onNextPageReady,u=t.isIntersecting,a=t.adManager,c=(0,o.useContext)(W.Z),s=c.experiments,M=c.getFeatureFlagValue,l=(0,o.useContext)(G.Z).isCommentsPanelOpen,j=V(function(t){var e=(0,o.useRef)(new Set),n=(0,o.useState)(!1),i=n[0],r=n[1],u=(0,o.useCallback)((function(t){return e.current.add(t)}),[]),a=(0,o.useCallback)((function(t){e.current.has(t)?(e.current.delete(t),0===e.current.size&&r(!0)):console.warn("Attempted to release a free '".concat(t,"' lock"))}),[]);return(0,o.useEffect)((function(){if(i)return t(),void r(!1)}),[t,i]),[u,a]}(r),2),N=j[0],g=j[1],d=o.Children.count(n),y=(0,o.useState)(null),L=y[0],T=y[1],z=(0,o.useState)(null),m=z[0],p=z[1],I=(0,o.useState)(!0),f=I[0],D=I[1],x=(0,o.useState)(!0),b=x[0],O=x[1],A=(0,o.useState)(!1),h=A[0],_=A[1],S=(0,o.useState)(!0),w=S[0],E=S[1],v=(0,o.useRef)(null),C=(0,o.useRef)(null),Q=M("RT-1522-sidebar-15s-refresh")?15e3:32e3;return(0,o.useEffect)((function(){u?(g("sidebar:visibility"),(0,R.A)("info","lifecycle","Sidebar Refresh: Release visibility lock")):(N("sidebar:visibility"),(0,R.A)("info","lifecycle","Sidebar Refresh: Acquire visibility lock"))}),[u,N,g]),(0,o.useEffect)((function(){l?N("sidebar:condensed-comments"):g("sidebar:condensed-comments")}),[l,N,g]),(0,o.useEffect)((function(){var t=function(){return E(!0)},e=function(){return E(!1)},n=function(){return E("visible"===document.visibilityState)};return window.addEventListener("focus",t),window.addEventListener("blur",e),window.addEventListener("visibilitychange",n),function(){window.removeEventListener("focus",t),window.removeEventListener("blur",e),window.removeEventListener("visibilitychange",n)}}),[]),(0,o.useEffect)((function(){w?(g("sidebar:page-focus"),(0,R.A)("info","lifecycle","Sidebar Refresh: Release page-focus lock")):(N("sidebar:page-focus"),(0,R.A)("info","lifecycle","Sidebar Refresh: Acquire page-focus lock"))}),[w,N,g]),(0,o.useEffect)((function(){var t=new URL(location).searchParams.get("sidebar-debug");null!==t&&e>=t-1?N("sidebar:debug"):g("sidebar:debug")}),[e,N,g]),(0,o.useEffect)((function(){s&&s.loaded&&(0,R.A)("info","lifecycle","Sidebar Refresh: Refresh Interval:",Q)}),[s,Q]),(0,o.useEffect)((function(){(null===s||void 0===s?void 0:s.loaded)&&e!==v.current&&(v.current=e,_(!1),N("sidebar:preload-item"),clearTimeout(C.current),N("sidebar:timer"),D(!0),C.current=setTimeout((function(){(0,R.A)("info","lifecycle","Sidebar Refresh: Starting timer... (page: ".concat(e,")")),C.current=null,g("sidebar:timer"),D(!1)}),Q))}),[e,N,g,s,Q]),(0,o.useEffect)((function(){return function(){return clearTimeout(C.current)}}),[]),(0,o.useEffect)((function(){f||b||_(!0)}),[m,f,b]),(0,o.useEffect)((function(){T({idx:e%d,props:{mode:"active",sidebarPage:e,acquireLock:function(){N("sidebar:active-item"),O(!0)},releaseLock:function(){g("sidebar:active-item"),O(!1)}}});var t=e+1;p({idx:t%d,props:{mode:"preload",sidebarPage:t,acquireLock:function(){return N("sidebar:preload-item")},releaseLock:function(){return g("sidebar:preload-item")}}})}),[e,d,N,g]),(0,o.useEffect)((function(){a&&(a.pattern=o.Children.map(n,(function(t){return t.type===k})))}),[n,a]),L&&m&&o.Children.map(n,(function(t,n){var r=n===L.idx,u=h&&n===m.idx,a=r?L.props:u?m.props:{mode:"inactive"};return(0,i.jsx)(F,function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){J(t,e,n[e])}))}return t}({sidebarPage:e},a,{children:t}))}))},K=n(28442);function X(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function $(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var i,r,u=[],a=!0,o=!1;try{for(n=n.call(t);!(a=(i=n.next()).done)&&(u.push(i.value),!e||u.length!==e);a=!0);}catch(c){o=!0,r=c}finally{try{a||null==n.return||n.return()}finally{if(o)throw r}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return X(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return X(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tt(t){var e=t.buzz,n=t.containerRef,r=t.pixiedust,u=(0,o.useContext)(C.$),a=$(function(t){var e=(0,o.useState)(0),n=e[0],i=e[1],r=(0,o.useState)(!1),u=r[0],a=r[1];return[n,(0,o.useCallback)((function(t){0!==t?(a(!0),setTimeout((function(){i(t),a(!1)}),parseInt(K.transitionDuration,10))):i(0)}),[t.id]),u]}(e),3),c=a[0],s=a[1],M=a[2],l=(0,E.Z)(n).height,j=(0,v.Z)({threshold:.5,pageId:e.id}),N=j.isIntersecting,g=j.setObservable;(0,o.useEffect)((function(){s(0)}),[s]);var d=(0,o.useState)(null),y=d[0],L=d[1];(0,o.useEffect)((function(){if("loaded"!==u.status)return function(){};var t=B(e);return t.init().then((function(){L(t)})),function(){L(null),t.destroy()}}),[u]);var T=(0,o.useState)(0),z=T[0],m=T[1],p=(0,o.useCallback)((function(){var t=w.Z.getAvailableTop(n.current);z!==t&&m(t)}),[e.id,n]);return(0,o.useEffect)((function(){return p(),w.Z.subscribe(p),function(){return w.Z.unsubscribe(p)}}),[e.id,p]),(0,i.jsx)("div",{ref:g,className:"".concat(K.container," ").concat(M?K.transitioning:""),style:{top:"".concat(z,"px")},children:(0,i.jsxs)(q,{sidebarPage:c,onNextPageReady:function(){return s((function(t){return t+1}))},maxHeight:l,isIntersecting:N,buzzId:e.id,adManager:y,children:[(0,i.jsx)(k,{adManager:y,pixiedust:r}),(0,i.jsx)(k,{adManager:y,pixiedust:r})]})})}tt.propTypes={buzz:z().object,containerRef:z().shape({current:z().object})};var et=tt;function nt(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function it(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){nt(t,e,n[e])}))}return t}var rt=(0,l.Z)((function(){return n.e(570).then(n.bind(n,73))}),{ssr:!1});var ut=(0,u.Z)((function(t){var e=t.pixiedust,n=(0,o.useContext)(c.Z).buzz,u=(0,o.useContext)(s.Z).destination,a=(0,r.Z)("(min-width: 64rem)"),l=(0,o.useRef)(null),j=(0,M.useTranslation)("common").t;if(!a)return null;var g={buzz:n,containerRef:l,pixiedust:e};return(0,i.jsx)("section",{className:"".concat(N().sidebar," ").concat(N()[u]," ").concat(n.isAd?N().sidebarBranded:""),"aria-label":j("promoted_content"),ref:l,children:n.walmartBundle?(0,i.jsx)(rt,it({},g,{buzz:n})):n.isAd?(0,i.jsx)(S,it({},g)):(0,i.jsx)(et,it({},g))})}),{onError:a.Tb})},31066:function(t,e,n){"use strict";var i=n(52322),r=n(2784),u=n(52114),a=n(41332),o=n(36001);function c(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){c(t,e,n[e])}))}return t}e.Z=function(t){var e=(0,r.useContext)(a.Z).buzz.destination_name,n=(0,r.useContext)(o.Z),c=s({},t);return"buzzfeed_news"===e&&(c.stickyRegistryClass=""),(0,i.jsx)(u.Z,s({},c,{stickyRegistry:n}))}},70755:function(t,e,n){"use strict";n.d(e,{NE:function(){return Tt},b:function(){return zt},ZP:function(){return mt}});var i=n(52322),r=n(2784),u=n(13980),a=n.n(u),o=n(42719),c=n(5103),s=n(94776),M=n.n(s),l=n(24678),j=n(88641),N=n(84225),g=n(46811);function d(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var y=function(t,e){var n=function(t){var n=e.getConfig("id"),i=t.network;delete t.network,!t.element&&t.event&&(t.element=t.event.target),delete t.event,(0,g.BN)(i,{canonical_url:o,destination_name:c,shareData:s,title:a},function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){d(t,e,n[e])}))}return t}({},t,{subbuzzId:n}))},i={},r=e.getElement(),u={},a=t.title,o=t.canonical_url,c=t.destination_name,s=t.shareData,M="share-".concat(r.id);return i[M]=n,u.messages=[M],u.onclick=function(t,e,i){"sharing-button"===i&&n({network:e.getAttribute("data-bfa-network"),element:e})},u.onmessage=function(t,e){var n=i[t];"function"===typeof n&&n.call(this,e)},u},L=n(30353),T=n(99112),z=n(30332),m=n(41332),p=n(5875);function I(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var f=function(t){return function(e){return(0,N.i$)(e,t.language,{month:"long",hour:"numeric",minute:"numeric"}).formattedTimestamp}},D=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return c.i18n.t(t.toLowerCase(),e)},x=function(){function t(e,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.baseUrl=e,this.abeagle=n,this.addBehavior=j.Application.addBehavior.bind(j.Application),this.addModule=j.Application.addModule.bind(j.Application),this.broadcast=j.Application.broadcast.bind(j.Application),this.on=j.Application.on.bind(j.Application),this.off=j.Application.off.bind(j.Application),this.start=j.Application.start.bind(j.Application),this.startAll=j.Application.startAll.bind(j.Application),this.stop=j.Application.stop.bind(j.Application),this.getGlobalConfig=j.Application.getGlobalConfig.bind(j.Application)}var e,n,i;return e=t,(n=[{key:"init",value:function(t){var e={abeagle:this.abeagle,bf_url:this.baseUrl,canonical_path:t.canonical_path,category:t.category,country:t.country_code,edition:t.classification.edition,debug:"dev"===L.ENV||"stage"===L.ENV,description:t.description,formatDate:f(t),isAd:t.isAd,language:t.language,mango_url:L.mango_url,picture:t.picture,shareBehavior:"share-behavior",tags:t.tags,laser_tags:t.laser_tags,title:t.title,tracking:this.tracking,translate:D};j.Application.init(e),j.Application.addBehavior("share-behavior",y.bind(null,t))}},{key:"destroy",value:function(){j.Application.destroy()}},{key:"_setTrackingFns",value:function(t){this.tracking=t}}])&&I(e.prototype,n),i&&I(e,i),t}();function b(){var t=(0,r.useState)(null),e=t[0],n=t[1],i=(0,r.useContext)(m.Z).buzz,u=void 0===i?{}:i,a=(0,r.useContext)(T.Z).base_url,o=(0,z.Z)(u),c=(0,p.Z)();return(0,r.useEffect)((function(){window.BZFD.App=window.BZFD.App||new x(a,c),window.BZFD.App._setTrackingFns(o),n(window.BZFD.App)}),[u.id,o]),e}var O=n(42235);function A(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function h(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){A(u,i,r,a,o,"next",t)}function o(t){A(u,i,r,a,o,"throw",t)}a(void 0)}))}}var _=n(67176),S=n(73035),w=n(32966);function E(t){var e=t.buzz,n=void 0===e?{}:e,i=t.index,u=(0,r.useRef)(null),a=(0,r.useContext)(w.Z),o=a.experiments,c=a.getFeatureFlagValue;return(0,r.useEffect)((function(){var t=u.current,e=c("ads_amazon_nca");t&&o.loaded&&e&&t.querySelectorAll("a[href]").forEach((function(t){(0,_.D1)(t.href)&&(t.classList.contains("js-aagc-modified")||(t.classList.add("js-aagc-modified"),function(t){var e=t.element;if(e){var n=e.href.match("/([a-zA-Z0-9]{10})(?:[/?]|$)"),i=new URL(e.href);e.setAttribute("data-aps-asin",n&&!(0,S.B$)(e.href)?n[1]:null),e.setAttribute("data-aps-asc-tag",i.searchParams.get("tag")),e.setAttribute("data-aps-asc-subtag",i.searchParams.get("ascsubtag"))}}({element:t,buzz:n,index:i})))}))}),[o]),u}var v=n(91872);var C=n(16981),k=n(88163),Q=n(4366);var U=n(62430),Y=n(48882),P=n(91904);function Z(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}var B=function(){var t,e=(t=M().mark((function t(e){var n,i,r,u,a,o;return M().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:(n=e.element).hasAttribute("data-affiliate")&&n.hasAttribute("data-vars-product-id")&&(i=(0,_.nC)(n),r=i["product-id"],u=JSON.parse(localStorage.getItem("clickedProducts")||"[]"),a={id:i["product-id"],timestamp:Date.now()},o=u.some((function(t){return t.id===r})),r&&!o&&u.push(a),window.localStorage.setItem("clickedProducts",JSON.stringify(u)));case 2:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){Z(u,i,r,a,o,"next",t)}function o(t){Z(u,i,r,a,o,"throw",t)}a(void 0)}))});return function(t){return e.apply(this,arguments)}}();var R=n(3695);function W(t){var e=t.buzz,n=t.subbuzzEl;return(0,r.useEffect)((function(){n.current&&(0,R.G)(e)&&e.tags.includes("comtent")&&n.current.querySelectorAll("a[href]").forEach((function(t){var e;"italic"!==getComputedStyle(t).fontStyle&&"I"!==(null===t||void 0===t||null===(e=t.firstElementChild)||void 0===e?void 0:e.tagName)||t.classList.add("js-news-product")}))}),[n]),(0,i.jsx)("div",{})}var G=n(66330),F=n(92929),H=n.n(F),J=function(){var t=(0,r.useContext)(w.Z),e=t.experiments,n=t.getExperimentValue,u=(0,r.useState)(!1),a=u[0],o=u[1],c=(0,r.useState)(!1),s=c[0],M=c[1];return(0,r.useEffect)((function(){e&&e.loaded&&o("show"===n("RT-1712-shopsense-embed",{rejectErrors:!1}))}),[e,n]),(0,r.useEffect)((function(){if(!s&&a){var t=document.createElement("script");t.src="https://embeds.prod.shopsense.ai/v2/shopsense-embed-injector.min.js",t.async=!0,t.onload=function(){window.ShopsenseEmbeds.EmbedInjector.loadContextualEmbed({container_id:"shopsense-embed",publisher:"buzzfeed",meta:{}})},document.body.appendChild(t),M(!0)}}),[s,a]),a?(0,i.jsx)("div",{id:"shopsense-embed",className:H().shopsenseWidget}):null},V=n(9772),q=n(35167),K=n(15449),X=n(48570),$=n(51171),tt=n(83661),et=n(81550),nt=n(51340),it=n(89013),rt=n.n(it);function ut(){var t=(0,r.useRef)(null);return(0,r.useEffect)((function(){var e=document.createElement("script");e.type="text/javascript",e.innerHTML='(apscustom = window.apscustom || []).push({ id: "93b7dd52-a8ce-11ed-afa1-0242ac120002:1"});',t.current.appendChild(e)}),[t]),(0,i.jsx)("div",{className:rt().amazonAiWrapper,children:(0,i.jsx)("div",{ref:t,className:"apscustom",id:"93b7dd52-a8ce-11ed-afa1-0242ac120002:1"})})}var at=n(14125);function ot(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var ct=(0,et.Z)((function(t){var e,n=t.config.adPos,u=(0,r.useContext)(m.Z).buzz,a=void 0===u?{}:u,o=(0,r.useContext)(V.$),c=null===o||void 0===o||null===(e=o.env)||void 0===e?void 0:e.userCountry,s=(0,at.yR)(a),M=(0,r.useState)("control"),l=M[0],j=M[1],N=(0,r.useContext)(w.Z),g=N.experiments,d=N.getExperimentValue;return(0,r.useEffect)((function(){g&&g.loaded&&j(d("RT-583-amazon-recommendation-abtest",{rejectErrors:!1}))}),[g,d]),g.loaded?"on"===l&&"promo-inline2"===n?(0,i.jsx)(ut,{}):s&&"promo-inline2"===n&&"jp"===c?(0,i.jsx)(nt.Z,{buzz:a}):(0,i.jsx)(tt.Z,function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){ot(t,e,n[e])}))}return t}({},t)):""}),{onError:l.Tb});function st(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function Mt(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function lt(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function jt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){lt(t,e,n[e])}))}return t}function Nt(t){return function(t){if(Array.isArray(t))return st(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return st(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return st(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var gt=["ai_quiz","scale_quiz","tap_on_image","quiz","ai_generator"],dt=function(t){return function(e){var n,u=(0,r.useContext)(m.Z).buzz,a=void 0===u?{}:u,o=(0,r.useContext)(V.$),c=null===o||void 0===o||null===(n=o.env)||void 0===n?void 0:n.userCountry,s="au"===c||"nz"===c,l=(0,r.useRef)(null),j=(0,r.useRef)(null),N=(0,r.useState)([]),g=N[0],d=N[1],y=b(),T=e?e.pixiedust:{},z=(0,r.useContext)(w.Z),p=z.experiments,I=z.getFeatureFlagValue,f=z.getExperimentValue,D=I("ads_mweb_quiz_promo-inline1"),x=I("ads_high_density_bpage_non_quiz"),A=I("ads_high_density_bpage_quiz"),h=f("ads_shopping_density_test",{rejectErrors:!1}),_="control";try{_=f("RT-1520-density")}catch(Y){_="not-eligible"}var S="control";try{S=f("RT-1473-bfj-increase-density-ads")}catch(P){S="not-eligible"}var E=(0,r.useState)([]),v=E[0],C=E[1],k=(0,r.useState)(!1),Q=k[0],U=k[1];return(0,r.useEffect)((function(){d([]),j.current=null}),[a.id]),(0,r.useEffect)((function(){var t=[],e=[],n=function(n){var i=n.data;if("ukEngineQuiz_Ready"===i.message&&i.messageData)e=(0,q.kE)(i.messageData.totalQuestions,0).map((function(t){return"bfp-ad-placeholder-".concat(t,"-ready")}));else{if("bfp-ad-placeholders-ready"!==i.message&&!e.includes(i.message))return;C((function(t){var e=Nt(t);return e.push(i),e}))}j.current&&(t.forEach((function(t){j.current.onBFPReady(t.messageData)})),t=[])};return y&&"function"===typeof y.on&&(y.on("message",n),a.bfpFormats.includes("tap_on_image")&&(window.BfpT3MessageReady=!0,document.dispatchEvent(new Event("BfpT3MessageReady")))),function(){y&&"function"===typeof y.off&&y.off("message",n)}}),[y,j]),(0,r.useEffect)((function(){var t=10,e=setInterval((function(){--t<=0&&(clearInterval(e),U(!0))}),1e3);return function(){return clearInterval(e)}}),[v.length]),(0,r.useEffect)((function(){var t=a.sub_buzzes.filter((function(t){return"bfp"===t.form&&-1!==gt.indexOf(t.format_name)})).length;(v.length>=t||Q)&&j.current&&v.sort((function(t,e){var n,i;return((null===t||void 0===t||null===(n=t.messageData)||void 0===n?void 0:n.subbuzzIndex)||0)-((null===e||void 0===e||null===(i=e.messageData)||void 0===i?void 0:i.subbuzzIndex)||0)})).forEach((function(t){j.current.onBFPReady(t.messageData)}))}),[v,j,a.id,Q]),(0,r.useEffect)((function(){if(!p.loaded)return function(){};if("loaded"!==o.status)return function(){};var t,e,n=2;return a.is_quiz&&!a.isBFPQuiz&&((s||A)&&(n=1),(t=(0,X.Z)({AdUnit:tt.Z,buzz:a,disablePromo1Native:D,element:l.current,onAdsReady:function(t){return d(g.concat(t))},pixiedust:T,presetDensity:n,skipNUnits:!1})).init()),(e=(0,K.Z)({AdUnit:ct,buzz:a,element:l.current,onAdsReady:function(t){d(g.concat(t))},pixiedust:T,skipNUnits:!1,presetDensity:"control"!==_?"density_"+_:"not-eligible"===_&&"control"!==S&&"not-eligible"!==S?"density_bfj_"+S:s||x||"variant1"===h?"density_200":"variant2"===h&&"density_275",presetPlacements:"control"!==_?"placements_"+_:"not-eligible"===_&&"control"!==S&&"not-eligible"!==S?"placements_bfj_"+S:s||x||"variant1"===h?"placements_1":"variant2"===h&&"placements_2",looseTitles:"increase"===_})).init(),function(){e&&e.destroy(),t&&t.destroy()}}),[a.id,o,p.loaded]),(0,r.useEffect)((function(){if(!p.loaded)return function(){};if("loaded"!==o.status)return function(){};var t=2;return a.bfpFormats.length>0&&((s||A||"increase"===_)&&(t=1),j.current=(0,$.Z)({AdUnit:tt.Z,disablePromo1Native:D,element:l.current,onAdsReady:function(t){return d(g.concat(t))},pixiedust:T,presetDensity:t,skipNUnits:!1}),j.current.init()),function(){j.current&&j.current.destroy()}}),[a.id,o,p.loaded]),(0,r.useEffect)((function(){if((0,R.G)(a)&&(null===l||void 0===l?void 0:l.current)&&!window.pgmApi&&g.length&&"disabled"!==o.status){var t=document.createElement("div");t.setAttribute("id","publicGood"),t.classList.add("loading","clearfix","ad-ex");var e={partnerId:"buzzfeed-buzzfeed-news"};"dev"!==L.CLUSTER&&"stage"!==L.CLUSTER||(e.attributes={targetId:"2d3d74ba-3807-4e28-875a-dfc5d67706f8",targetType:"campaign"}),(0,O.v)("https://assets.publicgood.com/pgm/v1/pgm-api.js",document.body).then(function(){var n,i=(n=M().mark((function n(i){var r;return M().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,i.onload();case 2:r=Math.floor(g.length/2),l.current.querySelectorAll(".js-ad-placement")[r].insertAdjacentElement("afterend",t),window.pgmApi.create(t,jt({},e,{onShow:function(){t.classList.remove("loading");var e=document.createElement("h2");e.classList.add("ad__disclosure--ex"),e.innerText="ARTICLE CONTINUES BELOW",t.insertAdjacentElement("afterbegin",e)},onHide:function(){window.pgmApi.remove(),t.remove()}}));case 6:case"end":return n.stop()}}),n)})),function(){var t=this,e=arguments;return new Promise((function(i,r){var u=n.apply(t,e);function a(t){Mt(u,i,r,a,o,"next",t)}function o(t){Mt(u,i,r,a,o,"throw",t)}a(void 0)}))});return function(t){return i.apply(this,arguments)}}())}}),[a,o,g,l,O.v]),(0,i.jsxs)("div",{ref:l,children:["jp"!==c&&(0,i.jsx)(nt.Z,{buzz:a}),(0,i.jsx)(t,jt({},e)),g.map((function(t,e){return(0,i.jsx)(t,{},e)}))]})}};function yt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function Lt(t){return function(t){if(Array.isArray(t))return yt(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return yt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return yt(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Tt=function(t){var e=t.html,n=void 0===e?"":e,u=t.buzz,a=t.index,c=t.isContentGate,s=void 0!==c&&c,M=(0,Y.m)({buzz:u}),l=(0,v.J)({buzz:u}),j=E({buzz:u,index:a}),N=(0,C.B)({buzz:u}),g=(0,U.P)({buzz:u}),d=(0,P.J)(u,a),y=function(t){var e=t.buzz,n=void 0===e?{}:e,i=(0,r.useRef)(null);return(0,r.useEffect)((function(){var t=i.current;t&&t.querySelectorAll("a[href]").forEach((function(t){(0,S.Uz)(t)&&t.addEventListener("click",(function(t){B({element:t.target.closest("a")})}))}))}),[n]),i}({buzz:u}),L=function(){var t=(0,r.useState)(!1),e=t[0],n=t[1],i=(0,r.useContext)(w.Z),u=i.experiments,a=i.getFeatureFlagValue;return(0,r.useEffect)((function(){u&&u.loaded&&n(a("ads_bullwhip"))}),[u,a]),(0,r.useEffect)((function(){e&&(document.querySelector('script[src*="cdn.bullwhip.cloud"]')||"h5d"==new URLSearchParams(window.location.search).get("utm_medium")&&(0,O.v)("https://cdn.bullwhip.cloud/sonar/bf-bf.umd.js"))}),[e]),null}(),T=function(t,e){var n=(0,r.useRef)(null),i=(0,r.useState)(!1),u=i[0],a=i[1],o=(0,k.Z)({once:!0,threshold:.1,pageId:"content_gate",defaultValue:u}),c=o.isIntersecting,s=o.setObservable;return(0,r.useEffect)((function(){var n=t.bfpFormats.includes("content_gate")&&e;n&&!t.disableAds&&a(n)}),[t,e]),(0,r.useEffect)((function(){if(c&&(null===n||void 0===n?void 0:n.current)&&u){var e=null===n||void 0===n?void 0:n.current.querySelector(".subbuzz-bfp--content_gate");if(e){var i=document.getElementById("mobile-app-promo");i&&(i.style.zIndex="497"),document.documentElement.style.overflow="hidden",document.body.classList.add("content-gate-open"),e.classList.add("isActive"),(0,Q.Oz)(t,{target_content_id:"membership_products",target_content_type:"utility",unit_name:"membership_gate",unit_type:"modal",item_name:"get_started",item_type:"button"})}}}),[c,u,n]),{contentGateRef:n,gateObservable:u?s:null}}(u,s),z=T.contentGateRef,m=T.gateObservable;return(0,r.useEffect)((function(){var t;if(M&&M.current){var e=M.current.querySelector("div.subbuzz-anchor"),n=(0,R.G)(u)&&M.current.querySelector(".subbuzz-bfp--related_links"),i=e&&e.id?e.id:null;if(n)M.current.querySelectorAll(".bfp-related-links__link").forEach((function(t,e){var n=t.href.match(/^(http:\/\/|https:\/\/)?(www|stage)?.buzzfeednews\.com/g);(0,Q.aF)(t,u,{subunit_name:"more_on_this",subunit_type:"package",unit_type:"buzz_body",unit_name:u.id,item_name:t.href,item_type:"card",position_in_unit:Number(a),position_in_subunit:Number(e),target_content_type:n?"buzz":"url",target_content_id:t.href})}));else t=(0,Q.zq)(M.current,u,{item_name:i,item_type:"subbuzz",position_in_unit:Number(a),unit_type:"buzz_body",unit_name:u.id,subunit_name:"",subunit_type:""})}return function(){"function"===typeof t&&t()}}),[u,a,M]),(0,i.jsxs)("div",{className:"js-subbuzz-wrapper",children:[(0,i.jsx)("div",{dangerouslySetInnerHTML:{__html:n},ref:(0,o.Z)(M,l,j,g,d,y,N,L,z,m)}),(0,i.jsx)(W,{buzz:u,subbuzzEl:M,index:a})]})},zt=function(t){var e=t.buzz,n=t.subbuzzData,u=void 0===n?{}:n,a=(0,r.useContext)(T.Z).destination,o=(0,r.useRef)(null),s=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=b(),n=(0,r.useState)(!1),i=n[0],u=n[1],a=(0,r.useRef)(t).current;return(0,r.useEffect)((function(){if(!i){if(t.inline_js){var e=document.body,n=document.createElement("script");n.innerHTML=t.inline_js,e.appendChild(n)}t.js&&function(){r.apply(this,arguments)}()}function r(){return r=h(M().mark((function e(){return M().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Promise.all(t.js.map(function(){var t=h(M().mark((function t(e){return M().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,(0,O.v)(e);case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()));case 3:u(!0),e.next=9;break;case 6:e.prev=6,e.t0=e.catch(0),(0,l.Tb)(e.t0);case 9:case"end":return e.stop()}}),e,null,[[0,6]])}))),r.apply(this,arguments)}}),[a,i]),e&&i?e:null}(u.assets);!function(){var t=(0,r.useContext)(m.Z).buzz,e=void 0===t?{}:t;(0,r.useEffect)((function(){window.clientEventTracking={getPageContextLayer:function(){return(0,Q.z4)(e)},env:L.CLUSTER}}),[e])}();var j=(0,c.useTranslation)("common").t,N=u.subbuzzes?Lt(u.subbuzzes):[],g=["list","long"].reduce((function(t,n){return e.format.page_type.includes(n)&&(t+="buzz--".concat(n)),t}),"");if("breaking"===e.format.page_type&&N.length){var d=N.find((function(t){return t.includes("buzz__update")}));if(d){var y=N.indexOf(d);N.splice(y,0,'<h2 class="buzz__update-title">'.concat(j("updates"),"</h2>"))}}return(0,r.useEffect)((function(){return s&&s.init&&s.init(e),function(){s&&s.destroy&&s.destroy()}}),[e,s]),(0,i.jsx)("div",{className:"\n        ".concat(g,"\n        buzz--").concat(e.category,"\n        subbuzzes-wrapper\n        subbuzzes--").concat(a,"\n      "),ref:o,children:N.map((function(t,n){var a,o;return(0,i.jsxs)(r.Fragment,{children:[0===n&&"connatix_video"!==(null===(a=null===e||void 0===e?void 0:e.sub_buzzes[0])||void 0===a?void 0:a.format_name)&&(0,i.jsx)(G.W,{buzz:e,itemType:"text",positionInUnit:n}),1===n&&"connatix_video"===(null===(o=null===e||void 0===e?void 0:e.sub_buzzes[0])||void 0===o?void 0:o.format_name)&&(0,i.jsx)(G.W,{buzz:e,itemType:"text",positionInUnit:n}),(0,i.jsx)(Tt,{html:t,buzz:e,index:n,isContentGate:n===(null===u||void 0===u?void 0:u.gateIndex)},n),3===n&&(0,i.jsx)(J,{}),(3===n||n===(null===N||void 0===N?void 0:N.length)-1&&(null===N||void 0===N?void 0:N.length)<4)&&(0,i.jsx)(G.W,{buzz:e,itemType:"card",positionInUnit:n,unitType:(null===N||void 0===N?void 0:N.length)>=4?"buzz_body":"buzz_bottom"})]},n)}))})};zt.propTypes={subbuzzData:a().shape({assets:a().object,subbuzzes:a().array,gateIndex:a().number})};var mt=dt(zt)},66330:function(t,e,n){"use strict";n.d(e,{W:function(){return g},Z:function(){return d}});var i=n(52322),r=(n(2784),n(13980)),u=n.n(r),a=n(30353),o=n(33528),c=n.n(o),s=function(t){var e=t.className,n=t.fill,r=void 0===n?"none":n,u=t.title,a=void 0===u?"":u,o=t.stroke,c=void 0===o?"":o;return(0,i.jsxs)("svg",{className:e,fill:r,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",children:[(0,i.jsx)("title",{children:a}),(0,i.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)("path",{d:"M5.5 3L10.5 8L5.5 13",stroke:c||"#5246f5",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})]})};function M(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function l(t,e){if(null==t)return{};var n,i,r=function(t,e){if(null==t)return{};var n,i,r={},u=Object.keys(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var j=function(t){var e=t.className,n=t.title,r=void 0===n?"BuzzFeed Trending":n,u=l(t,["className","title"]);return(0,i.jsxs)("svg",function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){M(t,e,n[e])}))}return t}({className:e,xmlns:"http://www.w3.org/2000/svg",width:"17",height:"20",viewBox:"0 0 17 20",fill:"none"},u,{children:[(0,i.jsx)("title",{children:r}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 16.875C10.797 16.875 13.875 13.797 13.875 10C13.875 6.20304 10.797 3.125 7 3.125C3.20304 3.125 0.125 6.20304 0.125 10C0.125 13.797 3.20304 16.875 7 16.875ZM10.6004 9.80556L10.1101 6.5625L7.08176 7.74923L8.28984 8.45212L7.15325 10.4262L5.20161 9.32699L3.25 12.6695L4.32678 13.3051L5.63531 11.0094L7.58695 12.1386L9.35913 9.05032L10.6004 9.80556Z"})]}))},N=n(64435),g=function(t){var e,n,r,u,o,M,l,g,d,y=t.buzz,L=t.itemType,T=void 0===L?"card":L,z=t.positionInUnit,m=void 0===z?0:z,p=t.subunitName,I=t.subunitType,f=t.unitType,D=void 0===f?"buzz_body":f,x=y.topicPagePromo,b=(0,N.R)({item_type:T,item_name:null===x||void 0===x?void 0:x.slug,subunit_name:p,subunit_type:I,unit_type:D,unit_name:y.id,target_content_type:"feed",target_content_id:8,position_in_unit:m,target_content_url:(null===y||void 0===y?void 0:y.isNews)?null===y||void 0===y?void 0:y.canonical_url:null},!1,null===y||void 0===y?void 0:y.isNews);if(!x)return null;var O=null===(e=x.metadata)||void 0===e?void 0:e.promos,A=null===(n=x.metadata)||void 0===n?void 0:n.images,h="games-hub"===(null===x||void 0===x?void 0:x.slug)?"/arcade":"/topic/".concat(null===x||void 0===x?void 0:x.slug),_="".concat(null===(r=a.destinations.buzzfeed)||void 0===r?void 0:r.base_url).concat(h);if("text"===T){var S,w=(null===O||void 0===O||null===(S=O.tertiary)||void 0===S?void 0:S.title)||"Full coverage and conversation on";return(0,i.jsxs)("div",{ref:b,className:c().textPromo,children:[(0,i.jsx)("div",{className:c().hotTopic,children:"Hot Topic"}),(0,i.jsxs)("div",{className:c().textPromoTitle,children:["\ud83d\udd25 ",w," ",(0,i.jsxs)("a",{href:_,className:c().topicLink,children:[x.display_name,(0,i.jsx)(s,{className:c().carret,fill:"#5246F5"})]})]})]})}if("comments"===p){var E,v,C=(null===O||void 0===O||null===(E=O.secondary)||void 0===E?void 0:E.title)||"We see you lurking \ud83d\udc40",k=(null===O||void 0===O||null===(v=O.secondary)||void 0===v?void 0:v.description)||"",Q=null===A||void 0===A?void 0:A.wide,U=null===A||void 0===A?void 0:A.narrow;return(0,i.jsxs)("a",{ref:b,href:_,className:"".concat(c().commentsPromo," ").concat(Q&&U?"":c().noImage),children:[Q&&U&&(0,i.jsxs)("picture",{children:[(0,i.jsx)("source",{srcSet:Q,media:"(min-width: 640px)"}),(0,i.jsx)("img",{src:U,"aria-hidden":!0})]}),(0,i.jsxs)("div",{className:c().commentsPromoContent,children:[(0,i.jsxs)("div",{className:c().commentsPromoCopy,children:[(0,i.jsx)("div",{className:c().commentsPromoTitle,children:C}),(0,i.jsx)("div",{className:c().commentsPromoDescription,children:k})]}),(0,i.jsxs)("div",{className:c().commentsPromoCta,children:["See the Discussions",(0,i.jsx)(s,{className:c().carret,fill:"#5246F5"})]})]})]})}var Y=(null===O||void 0===O||null===(u=O.primary)||void 0===u?void 0:u.title)||"",P=(null===O||void 0===O||null===(o=O.primary)||void 0===o?void 0:o.cta)||"Join a Discussion",Z=null===A||void 0===A?void 0:A.wide,B=(null===x||void 0===x||null===(M=x.metadata)||void 0===M?void 0:M.theme)&&(null===x||void 0===x||null===(l=x.metadata)||void 0===l?void 0:l.theme.color1)&&(null===x||void 0===x||null===(g=x.metadata)||void 0===g?void 0:g.theme.color2)&&(null===x||void 0===x||null===(d=x.metadata)||void 0===d?void 0:d.theme.textColor)?x.metadata.theme:{color1:"#8DAAF8",color2:"#C6D4FB",textColor:"#222"};return(0,i.jsxs)("a",{ref:b,href:_,className:"".concat(c().visualPromo," ").concat(Z?"":c().noImage,"\n        ").concat(Y.length>90&&Z?c().longVisualPromoTitle:""),style:{"--promoThemeColor1":B.color1,"--promoThemeColor2":B.color2,"--promoThemeTextColor":B.textColor},children:[Z&&(0,i.jsx)("img",{alt:"",className:c().visualPromoImage,src:Z,"aria-hidden":!0}),(0,i.jsxs)("div",{className:c().visualPromoContent,children:[(0,i.jsxs)("div",{className:c().visualPromoCopy,children:[(0,i.jsxs)("div",{className:c().visualPromoPill,children:[(0,i.jsx)(j,{className:c().visualPromoIcon,"aria-hidden":!0}),"Hot Topic"]}),(0,i.jsx)("div",{className:c().visualPromoTitle,children:Y})]}),(0,i.jsxs)("div",{className:c().visualPromoCta,children:[P,(0,i.jsx)(s,{className:c().carret,stroke:B.textColor})]})]})]})};g.propTypes={buzz:u().object.isRequired,itemType:u().oneOf(["text","card"]),positionInUnit:u().number,subunitName:u().string,subunitType:u().string};var d=g},48185:function(t,e,n){"use strict";function i(t,e){return!(!t.isShopping||e||t.isAd||!["US","UK"].includes(t.classification.edition)||!t.isEnglish)}n.d(e,{h:function(){return i}})},50946:function(t,e,n){"use strict";n.d(e,{wo:function(){return i},gK:function(){return r},u:function(){return u}});var i=["japan"],r=["--disable-skimlinks-lib","noskimlinks"],u=["advertiser","aunews","business","canadanews","community","francenews","japannews","lgbt","politics","quickpost","science","tech","uknews","ukpolitics","usnews","world"]},32617:function(t){"use strict";t.exports={ADVERTISING:"Advertising",EDITORIAL:"Editorial",MODERATED_COMMUNITY:"Moderated Community",UNMODERATED_COMMUNITY:"Unmoderated Community"}},32966:function(t,e,n){"use strict";var i=n(27625);e.Z=i.Z},41332:function(t,e,n){"use strict";n.d(e,{E:function(){return a}});var i=n(52322),r=n(2784),u=r.createContext({});e.Z=u;var a=function(t){var e=t.value,n=t.children,a=(0,r.useState)(e),o=a[0],c=a[1];return(0,i.jsx)(u.Provider,{value:{buzz:o,setValue:c},children:n})}},49101:function(t,e,n){"use strict";n.d(e,{C:function(){return r}});var i=n(2784).createContext({}),r=i.Provider;e.Z=i},13607:function(t,e,n){"use strict";n.d(e,{x:function(){return r}});var i=n(2784).createContext({}),r=i.Provider;e.Z=i},99112:function(t,e,n){"use strict";var i=n(2784).createContext({});i.Provider;e.Z=i},36001:function(t,e,n){"use strict";var i=n(98638);e.Z=i.Z},33402:function(t,e,n){"use strict";n.d(e,{j2:function(){return j},iZ:function(){return g},Ey:function(){return y},rb:function(){return T},Sy:function(){return m},Lk:function(){return I},_l:function(){return D}});var i=n(94776),r=n.n(i),u=n(30353),a=n(20237),o=n(3379);function c(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function s(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){c(u,i,r,a,o,"next",t)}function o(t){c(u,i,r,a,o,"throw",t)}a(void 0)}))}}var M={},l=function(t,e){if(null===e||void 0===e?void 0:e.hasVideoSubbuzz)return console.info("Disabled the Facebook Pixel on posts that include a video"),{trackEvent:function(){}};if(!(null===e||void 0===e?void 0:e.isShopping))return console.info("Disabled the Facebook Pixel on non-shopping posts"),{trackEvent:function(){}};if(!M[t]){var n=u.destinations[t].facebook_tracking_id;M[t]=new a.mS({trackingId:n,cluster:u.CLUSTER})}return M[t]};function j(t,e,n){return N.apply(this,arguments)}function N(){return(N=s(r().mark((function t(e,n,i){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,l(e,n);case 2:t.sent.trackEvent({eventType:"PageView",eventData:i});case 4:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function g(t,e,n){return d.apply(this,arguments)}function d(){return(d=s(r().mark((function t(e,n,i){var u,a;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,l(e,n);case 2:u=t.sent,(a=o.Z.get("hem"))&&(i.em=atob(a.replace(/"/g,""))),u.trackEvent({eventType:"AddToCart",eventData:i}),u.trackEvent({eventType:"Purchase",eventData:i});case 7:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function y(t,e,n){return L.apply(this,arguments)}function L(){return(L=s(r().mark((function t(e,n,i){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,l(e,n);case 2:t.sent.trackEvent({eventType:"newsletterSignup",eventData:i,options:{custom:!0}});case 4:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function T(t,e,n){return z.apply(this,arguments)}function z(){return(z=s(r().mark((function t(e,n,i){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,l(e,n);case 2:t.sent.trackEvent({eventType:"Share",eventData:i,options:{custom:!0}});case 4:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function m(t,e,n){return p.apply(this,arguments)}function p(){return(p=s(r().mark((function t(e,n,i){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,l(e,n);case 2:t.sent.trackEvent({eventType:"Lead",eventData:i});case 4:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function I(t,e,n){return f.apply(this,arguments)}function f(){return(f=s(r().mark((function t(e,n,i){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,l(e,n);case 2:t.sent.trackEvent({eventType:"CompletedQuiz",eventData:i,options:{custom:!0}});case 4:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function D(t,e,n){return x.apply(this,arguments)}function x(){return(x=s(r().mark((function t(e,n,i){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,l(e,n);case 2:t.sent.trackEvent({eventType:"QuizAnswer",eventData:i,options:{custom:!0}});case 4:case"end":return t.stop()}}),t)})))).apply(this,arguments)}},5291:function(t,e,n){"use strict";n.d(e,{p:function(){return j},w:function(){return g}});var i,r=n(94776),u=n.n(r),a=n(20237),o=n(30353),c=n(18215);function s(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function M(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){s(u,i,r,a,o,"next",t)}function o(t){s(u,i,r,a,o,"throw",t)}a(void 0)}))}}var l=function(){return i||(i=new a.FC({cluster:o.CLUSTER,ids:o.outbrain_ids})),i};function j(){return N.apply(this,arguments)}function N(){return(N=M(u().mark((function t(){return u().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:"JP"===c.Z.get("bf-geo-country")&&l().trackPageView();case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function g(){return d.apply(this,arguments)}function d(){return(d=M(u().mark((function t(){return u().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:"JP"===c.Z.get("bf-geo-country")&&l().trackAffiliateClick();case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}},95829:function(t,e,n){"use strict";n.d(e,{k:function(){return y},m:function(){return T}});var i=n(94776),r=n.n(i),u=n(5229),a=n(20237),o=n(30353);function c(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function s(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){c(u,i,r,a,o,"next",t)}function o(t){c(u,i,r,a,o,"throw",t)}a(void 0)}))}}function M(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var l={},j=function(t){if(!t||!t.destination_name||!t.classification)throw new Error("Could not initialize Permutive because no buzz data was passed to establish the edition for a new instance.");var e=function(t){var e=t.classification,n=t.destination_name,i=e.edition,r=o.destinations[n].permutive_creds,u=r[i]||r.default,a=u.api_key;return{projectId:u.project_id,apiKey:a}}(t),n=t.destination_name,i=t.classification.edition;return l["".concat(n,"_").concat(i)]||(l["".concat(n,"_").concat(i)]=new a.TZ(function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){M(t,e,n[e])}))}return t}({cluster:o.CLUSTER},e)),l["".concat(n,"_").concat(i)].identify()),l["".concat(n,"_").concat(i)]},N=function(){var t=(0,u.Y)();return document.querySelectorAll("a[data-vars-retailers]").forEach((function(e){var n=e.getAttribute("data-vars-retailers").split(","),i=!0,r=!1,u=void 0;try{for(var a,o=n[Symbol.iterator]();!(i=(a=o.next()).done);i=!0){var c=a.value;t.includes(c)||t.push(c)}}catch(s){r=!0,u=s}finally{try{i||null==o.return||o.return()}finally{if(r)throw u}}})),t.filter(Boolean)},g=function(t){var e;return Object.values((null===t||void 0===t||null===(e=t.laser_tags)||void 0===e?void 0:e.watson)||[]).reduce((function(t,e){return t.concat(e.map((function(t){return t.tag_display_name})))}),[])},d=function(t){var e;return Object.values((null===t||void 0===t||null===(e=t.laser_tags)||void 0===e?void 0:e.bf_content_description)||[]).reduce((function(t,e){return t.concat(e.map((function(t){return t.tag_display_name})))}),[])};function y(t){return L.apply(this,arguments)}function L(){return(L=s(r().mark((function t(e){var n,i;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=j(e),i={page:{meta:{bf_buzzid:e.id,bf_userid:e.bylines[0].id,type:e.is_quiz?"quiz":e.format.type,section:e.category,publisher:"buzzfeed",title:e.title,description:e.description,author:e.bylines[0].username,tags:e.tags,platform:"web",edition:(e.country_code||"").split("-")[1]||"",categories:e.sections,badges:e.badges?e.badges.map((function(t){return t.name})):void 0,keywords:(0,u.$)(),retailers:N(),watson_tags:g(e),bf_content_description:d(e)}}},n.trackPageView(i);case 3:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function T(t,e){return z.apply(this,arguments)}function z(){return(z=s(r().mark((function t(e,n){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:j(n).trackAffiliateClick(e);case 2:case"end":return t.stop()}}),t)})))).apply(this,arguments)}},48882:function(t,e,n){"use strict";n.d(e,{m:function(){return A}});var i=n(94776),r=n.n(i),u=n(2784),a=n(3379),o=n(33402),c=n(5291),s=n(95829),M=n(20237),l=n(30353);function j(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function N(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){j(u,i,r,a,o,"next",t)}function o(t){j(u,i,r,a,o,"throw",t)}a(void 0)}))}}var g=!1,d=function(){return g||(g=new M.$P({cluster:l.CLUSTER})),g};function y(){return L.apply(this,arguments)}function L(){return(L=N(r().mark((function t(){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,d();case 2:t.sent.trackEvent();case 4:case"end":return t.stop()}}),t)})))).apply(this,arguments)}var T=n(67176),z=n(73035),m=n(32966),p=n(13607);function I(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function f(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function D(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var i,r,u=[],a=!0,o=!1;try{for(n=n.call(t);!(a=(i=n.next()).done)&&(u.push(i.value),!e||u.length!==e);a=!0);}catch(c){o=!0,r=c}finally{try{a||null==n.return||n.return()}finally{if(o)throw r}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return I(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return I(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var x={amazon:["amazon"],target:["target"],walmart:["walmart"]},b=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n="bf-affiliate",i=e.toLowerCase(),r=a.Z.get(n),u=new Set;r&&(u=new Set(r.split(",")));var o=(null===(t=Object.entries(x).find((function(t){var e=D(t,2);e[0];return e[1].some((function(t){return-1!==i.indexOf(t)}))})))||void 0===t?void 0:t[0])||"other";u.add(o);var c=a.Z.getBuzzfeedSubdomainOrWildcard(window.location.hostname);a.Z.set({name:n,value:Array.from(u).join(","),days:365,domain:c})},O=function(){var t,e=(t=r().mark((function t(e){var n,i,u,a,M,l,j;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=e.subbuzzElement,i=e.element,u=e.buzz,a=e.isPermutiveEnabled,i.hasAttribute("data-affiliate")&&(M=(0,T.nC)(i),l=n?n.querySelector("div.subbuzz-anchor"):null,j=l&&l.id?l.id:null,b(null===M||void 0===M?void 0:M.affiliate),(0,o.iZ)(u.destination_name,u,{section:u.classification.section,currency:M["price.currency"],contents:M.href,value:M["price.value"],linkId:M["link-id"],"subbuzz-id":j}),a&&(0,s.m)({affiliate:M.affiliate,campaign:M.campaign,href:M.href,name:M.name,price:{value:Number(parseFloat(M["price.value"]||0).toFixed(2)),currency:M["price.currency"]||""},redirectUrl:M.redirecturl,keywords:"string"===typeof M.keywords?M.keywords.split(","):[],retailers:"string"===typeof M.retailers?M.retailers.split(","):[],affiliateLinkId:M["link-id"]},u),(0,c.w)(),y());case 2:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){f(u,i,r,a,o,"next",t)}function o(t){f(u,i,r,a,o,"throw",t)}a(void 0)}))});return function(t){return e.apply(this,arguments)}}();function A(t){var e=t.buzz,n=void 0===e?{}:e,i=(0,u.useRef)(null),r=(0,u.useContext)(p.Z).tracking,a=r.consentValue,o=r.isConsentReady,c=(0,(0,u.useContext)(m.Z).getFeatureFlagValue)("ADSGROUP-442-permutive");return(0,u.useEffect)((function(){var t=i.current;t&&o&&(!o||a)&&null!==c&&t.querySelectorAll("a[href]").forEach((function(e){(0,z.Uz)(e)&&e.addEventListener("click",(function(e){O({subbuzzElement:t,element:e.target.closest("a"),buzz:n,isPermutiveEnabled:c})}))}))}),[n,o,a,c]),i}},73385:function(t,e,n){"use strict";n.d(e,{s:function(){return c}});var i=n(2784),r=n(90093),u=n(4366);function a(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function o(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){a(t,e,n[e])}))}return t}function c(t,e){var n=new URL(e).hostname,a={subunit_name:"breaking_bar",subunit_type:"component",item_type:"button",unit_type:"nav",unit_name:"main"};return(0,i.useCallback)((function(e){(0,r.p)(e.breaking_url,n)?(0,u.TW)(t,o({},a,{item_name:e.label.toLocaleLowerCase(),target_content_type:"url",target_content_id:e.breaking_url})):(0,u.nz)(t,o({},a,{item_name:e.label.toLocaleLowerCase(),target_content_url:e.breaking_url}))}),[t])}},70028:function(t,e,n){"use strict";n.d(e,{i:function(){return c}});var i=n(2784),r=n(4366),u=n(3695);function a(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function o(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){a(t,e,n[e])}))}return t}function c(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,i.useMemo)((function(){return{trackClick:function(){t&&t.id&&(0,r.bC)(t,o({item_name:"comment-cta",item_type:"button",action_value:"comment-cta",action_type:"navigate",unit_name:t.id,unit_type:"buzz_head"},e))},trackImpression:function(n){(0,u.G)(t)&&(0,r.aF)(n,t,o({unit_type:"buzz_head",unit_name:t.id,item_name:"comment-cta",item_type:"button",target_content_type:"buzz",target_content_id:t.id},e))}}}),[t])}},30332:function(t,e,n){"use strict";n.d(e,{Z:function(){return M}});var i=n(2784),r=n(15404),u=n(4366);var a=n(33402);function o(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){o(t,e,n[e])}))}return t}var s=function(t){var e=t;try{var n=Math.abs(parseInt(t,10));isNaN(n)||(e=n)}catch(i){console.error(i)}return e};function M(t){var e=(0,r.f)(t);return(0,i.useMemo)((function(){var n={trackingPathsMap:{},track:function(t,e){n.trackingPathsMap[t]&&n.trackingPathsMap[t](e)}};return function(t,e){var n=e.buzz,i=e.createShareTrackingFn,r={trackSraQuizAnswer:function(t){var e=t.data;(0,u.u3)(n,{action_type:"quiz_answer",action_value:"".concat(e.answerId),item_name:"".concat(e.answerId),item_type:"quiz_answer",subunit_name:"".concat(e.questionId),subunit_type:"quiz_question",unit_name:n.id,unit_type:"buzz_body"}),(0,a._l)(n.destination_name,n,{buzz_id:n.id,quiz_id:e.quizId,quiz_question_id:e.questionId,quiz_answer_id:e.answerId})},trackSraQuizComplete:function(t){var e=t.data;(0,u.Hk)(n,{action_type:"quiz_result",action_value:"".concat(e.itemId||e.resultId)}),(0,a.Lk)(n.destination_name,n,{buzz_id:n.id,quiz_id:e.quizId,quiz_result:e.itemId||"".concat(e.current," out of ").concat(e.total)})},trackSraQuizRetake:function(t){var e=t.data;(0,u.bC)(n,{action_type:"quiz_retake",action_value:n.id,item_name:e.itemId||e.resultId||e.quizId,item_type:"quiz_result",subunit_name:e.questionId||e.quizId||"",subunit_type:"quiz_question",unit_name:n.id,unit_type:"buzz_body"})},trackSraQuizShare:function(t){var e=t;t.data&&(e=c({},t,t.data)),i({platform:e.d,position:"quiz_results",item_type:"quiz_result",item_name:e.itemId||e.resultId||e.quizId})()},trackBfpQuizAnswer:function(e){t.trackSraQuizAnswer({data:c({},e.data,{questionId:s(e.data.questionId),answerId:s(e.data.answerId)})})},trackBfpQuizComplete:function(e){var n=e.data;t.trackSraQuizComplete({data:c({},n,{itemId:s(n.resultId)})})},trackBfpQuizResultSignup:function(){(0,u.TW)(n,{item_name:"sign_up",item_type:"button",subunit_name:"quiz_result",subunit_type:"component",target_content_id:"sign_in",target_content_type:"auth",unit_name:n.id,unit_type:"buzz_bottom"})},trackBfpQuizPin:function(t){var e=t.data;(0,u.bC)(n,{action_type:"save",action_value:e.resultId||"",item_name:"pin_to_my_profile",item_type:"button",subunit_name:"quiz_result",subunit_type:"component",unit_name:n.id,unit_type:"buzz_bottom"})},trackBfpQuizPinReplace:function(t){var e=t.data;(0,u.bC)(n,{action_type:"save",action_value:e.resultId||"",item_name:"pin_new_result",item_type:"button",subunit_name:"quiz_result",subunit_type:"component",unit_name:n.id,unit_type:"buzz_bottom"})},trackBfpQuizPinModal:function(t){var e=t.data;(0,u.TW)(n,{item_name:"visit_profile",item_type:"button",subunit_name:"quiz_result",subunit_type:"component",target_content_id:e.uuid||"",target_content_type:"user",unit_name:n.id,unit_type:"buzz_bottom"})},trackBfpQuizLoginPrompt:function(){(0,u.TW)(n,{item_name:"log_in",item_type:"text",subunit_name:"quiz_result",subunit_type:"component",target_content_id:"sign_in",target_content_type:"auth",unit_name:n.id,unit_type:"buzz_bottom"})}};Object.keys(r).forEach((function(e){return t[e]=r[e]})),t.trackingPathsMap=c({},t.trackingPathsMap,{"quiz/answer":t.trackSraQuizAnswer,"quiz/complete":t.trackSraQuizComplete,"quiz/retake":t.trackSraQuizRetake,"quiz/share":t.trackSraQuizShare})}(n,{buzz:t,createShareTrackingFn:e}),function(t,e){var n=e.buzz,i=function(t){var e=t.element,i=t.data,r=t.isWithImpression;e&&i&&((void 0===r||r)&&(0,u.aF)(e,n,i),i.target_content_url?(0,u.W3)(e,n,i):(0,u.Ev)(e,n,i))},r={trackBfpClientContentAction:function(t){var e=t.data;(0,u.bC)(n,e)},trackBfpClientLinkClick:function(t){var e=t.data;e.target_content_url?(0,u.nz)(n,e):(0,u.TW)(n,e)},bindBfpClientContentAction:function(t){var e=t.element,i=t.data;e&&i&&(0,u.g1)(e,n,i)},bindBfpProductTracking:i,bindUnitTracking:i,trackMinibuzzInteraction:function(){},trackMinibuzzImpression:function(){},trackBfpNewsletterSignup:function(t,e){var i=e.data;(0,u.bC)(n,{action_type:"signup",action_value:i.newsletter,item_name:"email",item_type:"submission",position_in_subunit:0,position_in_unit:0,subunit_name:"newsletter_signup",subunit_type:"component",unit_name:n.id,unit_type:"buzz_bottom"})}};Object.keys(r).forEach((function(e){return t[e]=r[e]}))}(n,{buzz:t}),function(t,e){var n=e.buzz,i={trackVideoEvents:function(t){(0,u.Pq)(n,{item_name:t.item_name||t.youtube_id||t.video_id,item_type:"video",subunit_name:0,subunit_type:"subbuzz",unit_name:n.id,unit_type:"buzz"})}};Object.keys(i).forEach((function(e){return t[e]=i[e]}))}(n,{buzz:t}),n}),[t.id,e])}},91904:function(t,e,n){"use strict";n.d(e,{J:function(){return l}});var i=n(2784),r=n(67176),u=n(90093),a=n(3695),o=n(4366),c=n(99112);function s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function M(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){s(t,e,n[e])}))}return t}function l(t,e){var n=(0,i.useRef)(null),s=(0,i.useContext)(c.Z),l=s.destination,j=s.base_url,N=new URL(j).hostname;return(0,i.useEffect)((function(){var i=n.current;if(i){var c=Boolean((0,a.G)(t)&&i.querySelector("div.subbuzz-bfp--related_links"));i.querySelectorAll("a[href]").forEach((function(n){n.addEventListener("click",(function(){var a,s=(0,r.nC)(n),l=i.querySelector("div.subbuzz-anchor"),j=l&&l.id?l.id:null,g=(null===t||void 0===t||null===(a=t.metadata)||void 0===a?void 0:a.subbuzzId)&&null!==n.closest("#subbuzz-shared-modal"),d={position_in_unit:Number(e),unit_type:"buzz_body",unit_name:t.id,item_type:"subbuzz",item_name:j,subunit_name:"",subunit_type:"",link_id:s["link-id"]};if(c)d.item_type="card",d.subunit_name="more_on_this",d.subunit_type="package";else if(g){d.position_in_unit=null,d.subunit_name="affiliate_link_modal",d.subunit_type="component"}(0,u.p)(s.href,N)?(0,o.TW)(t,M({},d,{target_content_type:"url",target_content_id:s.href})):(0,o.nz)(t,M({},d,{target_content_url:s.href}))}))}))}}),[t,l,e]),n}},84361:function(t,e,n){"use strict";n.d(e,{k:function(){return f}});var i=n(2784),r=n(4366),u=n(94776),a=n.n(u),o=n(30353),c=n(20237);function s(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function M(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){s(u,i,r,a,o,"next",t)}function o(t){s(u,i,r,a,o,"throw",t)}a(void 0)}))}}function l(){return(l=M(a().mark((function t(){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:new c.BE({cluster:o.CLUSTER});case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}var j=n(33402),N=n(5291),g=n(95829);function d(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function y(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){d(u,i,r,a,o,"next",t)}function o(t){d(u,i,r,a,o,"throw",t)}a(void 0)}))}}function L(){return(L=y(a().mark((function t(){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:new c.P0({client:"bpage",cluster:o.CLUSTER});case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}var T=n(99377),z=n(32966),m=n(99112),p=n(13607),I=n(17730);function f(t){var e=(0,i.useContext)(z.Z).getFeatureFlagValue,n=(0,i.useContext)(m.Z).destination,u=e("ADSGROUP-442-permutive")&&"sdxa"!==(0,I.P)(),a=(0,i.useContext)(p.Z).tracking,o=a.consentValue,c=a.isConsentReady;(0,i.useEffect)((function(){(0,r.Yq)(t),(0,j.j2)(n,t,{section:t.classification.section,Edition:t.classification.edition,username:t.bylines[0].username,tags:t.tags,SocialReferral:(0,T.an)()})}),[t,n]),(0,i.useEffect)((function(){u&&(0,g.k)(t)}),[t,u]),(0,i.useEffect)((function(){c&&o&&((0,N.p)(),"Japan"===t.classification.edition&&function(){L.apply(this,arguments)}())}),[t,o,n,c]),(0,i.useEffect)((function(){!function(){l.apply(this,arguments)}()}),[t])}},50896:function(t,e,n){"use strict";n.d(e,{s:function(){return u}});var i=n(2784),r=n(4366);function u(t){(0,i.useEffect)((function(){(0,r.AG)(t)}),[])}},44381:function(t,e,n){"use strict";n.d(e,{y:function(){return a}});var i=n(2784),r=n(4366),u=n(57673);function a(t){var e=(0,i.useRef)(!1);(0,i.useEffect)((function(){var n=function(e){(0,r.Y3)(t,{pixel_depth:parseInt((0,u.l)()+window.innerHeight,10),marker:e})},i=function(){e.current||"hidden"!==document.visibilityState||n("distraction_depth")},a=function(){n("exit_depth"),e.current=!0};return window.addEventListener("beforeunload",a),document.addEventListener("visibilitychange",i),function(){window.removeEventListener("beforeunload",a),document.removeEventListener("visibilitychange",i)}}),[t,e])}},15404:function(t,e,n){"use strict";n.d(e,{f:function(){return s}});var i=n(2784),r=n(33402),u=n(4366),a=n(13607);function o(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function c(t,e){if(null==t)return{};var n,i,r=function(t,e){if(null==t)return{};var n,i,r={},u=Object.keys(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function s(t,e){var n=(0,i.useContext)(a.Z).tracking,s=n.consentValue,M=n.isConsentReady,l={subbuzz:{item_name:e,item_type:"subbuzz",subunit_name:"",subunit_type:"",unit_type:"buzz_body"},bottom_share_bar:{subunit_name:"share_buttons",subunit_type:"component",unit_type:"buzz_bottom"},top_share_bar:{subunit_name:"",subunit_type:"",unit_type:"buzz_head"},quiz_results:{subunit_name:"",subunit_type:"",unit_type:"buzz_body"}};return(0,i.useCallback)((function(e){var n=e.platform,i=e.position,a=c(e,["platform","position"]);return function(){if(t&&t.id){var e=l[i.replace(/-/g,"_")]||{};(0,u.bC)(t,function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){o(t,e,n[e])}))}return t}({action_type:"share",action_value:n,item_name:n,item_type:"button",unit_name:t.id},e,a)),M&&s&&((0,r.rb)(t.destination_name,t,{platform:n,section:t.classification.section}),t.isAd&&(0,r.Sy)(t.destination_name,t,{content_category:t.bylines[0].username}))}}}),[t.id,t,s,M])}},81374:function(t,e,n){"use strict";n.d(e,{L:function(){return o}});var i=n(2784),r=n(32966),u=n(99112),a=n(4366);function o(t){var e=(0,i.useContext)(r.Z).getExperimentValue,n=(0,i.useContext)(u.Z).destination,o=e("TimeSpentRO_4",{rejectErrors:!1});(0,i.useEffect)((function(){var e=function(){};return"on"===o&&(e=(0,a.ff)(t)),e}),[t,n,o])}},64435:function(t,e,n){"use strict";n.d(e,{R:function(){return s}});var i=n(2784),r=n(41332),u=n(99112),a=n(4366),o=n(67176),c=n(17730);function s(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],s=(0,i.useRef)(null),M=(0,i.useContext)(r.Z),l=M.buzz,j=void 0===l?{}:l,N=(0,i.useContext)(u.Z).destination;return(0,i.useEffect)((function(){var i=function(n){if(e){var i=n.target.closest("a");if(i){var r=(0,o.nC)(i);t.eventLabel=r.href?(0,c.Q)(r.href):void 0}}},r=s.current;if(!r)return function(){};var u=(0,a.aF)(r,j,t),M=n?(0,a.W3)(r,j,t):(0,a.Ev)(r,j,t);return r.addEventListener("click",i),function(){u(),M(),r.removeEventListener("click",i)}}),[j,N,t,e,n]),s}},17730:function(t,e,n){"use strict";n.d(e,{Q:function(){return r},P:function(){return u}});var i=n(20238);function r(t){return t.replace("http://","").replace("https://","").replace("www.","")}function u(){var t,e=(0,i.jH)(null===window||void 0===window||null===(t=window.location)||void 0===t?void 0:t.search);return(null===e||void 0===e?void 0:e.origin)||""}},5875:function(t,e,n){"use strict";n.d(e,{Z:function(){return s}});var i=n(2784),r=n(74967),u=n(32966),a=n(41332);function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function c(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var i,r,u=[],a=!0,o=!1;try{for(n=n.call(t);!(a=(i=n.next()).done)&&(u.push(i.value),!e||u.length!==e);a=!0);}catch(c){o=!0,r=c}finally{try{a||null==n.return||n.return()}finally{if(o)throw r}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return o(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return o(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(){var t,e=(0,i.useContext)(u.Z).experiments,n=(0,i.useContext)(a.Z).buzz,o=void 0===n?{}:n,s=(0,i.useRef)(),M=(0,i.useRef)({});s.current&&o.id===s.current||(t=c(function(){var t;return[new Promise((function(e){return t=e})),t]}(),2),M.current.loaded=t[0],M.current.resolve=t[1]);s.current=o.id;var l=(0,i.useRef)({experiments:M.current.loaded,isOn:function(){var t=arguments;return M.current.loaded.then((function(e){return r.F7.apply(void 0,[e].concat(Array.prototype.slice.call(t)))}))},getExperimentVariant:function(){var t=arguments;return M.current.loaded.then((function(e){return r.ts.apply(void 0,[e].concat(Array.prototype.slice.call(t)))}))}});return(0,i.useEffect)((function(){e&&e.loaded&&!e.stale&&M.current.resolve(e)}),[e]),l.current}},91872:function(t,e,n){"use strict";n.d(e,{J:function(){return o}});var i=n(2784),r=n(32966),u=n(67176),a=n(73502);function o(t){var e=t.buzz,n=void 0===e?{}:e,o=(0,a.J)({buzz:n,ABeagleContext:r.Z}),c=null===o||void 0===o?void 0:o.current;return(0,i.useEffect)((function(){n.is_convert_enabled&&c&&!c.querySelector("a[data-affiliate]")&&c.querySelectorAll('a[href*="amazon"]:not([data-affiliate])').forEach((function(t){(0,u.D1)(t.href)&&t.setAttribute("data-affiliate","true")}))}),[c,n]),o}},16981:function(t,e,n){"use strict";n.d(e,{B:function(){return c}});var i=n(2784),r=n(73035),u=n(67176),a=n(32966),o=function(t){var e=t.element,n=t.experiments;if(e){var i=e.href;if((0,u.D1)(i)){var a={platform:(0,r.b5)(),origin:(0,r.P$)({origin:(0,r.wH)(e)}),experiment:(0,r.wp)(n),pubhubId:(0,r.hp)(),bioId:(0,r.pw)(),linkId:e.getAttribute("data-vars-link-id")||"0",module:"0",rank:"0"},o=!0,c=!1,s=void 0;try{for(var M,l=["origin","linkId"][Symbol.iterator]();!(o=(M=l.next()).done);o=!0){var j=M.value;a[j]&&"0"!==a[j]||delete a[j]}}catch(d){c=!0,s=d}finally{try{o||null==l.return||l.return()}finally{if(c)throw s}}i=(0,u.mL)(i,a)+"%2C".concat((0,r.o6)()||"0")}else{var N=new URL(i),g=decodeURIComponent(N.search);N.search=new URLSearchParams(g.toString()),i=N.toString()}e.href="https://r.bttn.io?btn_ref=org-21949126c81417e9&btn_url="+encodeURIComponent(i)}};function c(t){t.buzz;var e=(0,i.useRef)(null),n=(0,i.useContext)(a.Z),r=n.experiments,c=n.getExperimentValue,M=n.getFeatureFlagValue,l=(0,i.useState)(!1),j=l[0],N=l[1],g=(0,i.useState)(!1),d=g[0],y=g[1];return(0,i.useEffect)((function(){r&&r.loaded&&(N("control"===c("RT-1150-ButtonPostTap-abtest",{rejectErrors:!1})),y(M("RT-1510-ButtonPostTap-Non-Amazon")))}),[r,c]),(0,i.useEffect)((function(){var t=e.current;t&&(j||d)&&t.querySelectorAll("a[data-affiliate]").forEach((function(t){(0,u.D1)(t.href)&&!j||((0,u.D1)(t.href)||d)&&(t.classList.contains("js-buttonposttap-modified")||(t.classList.add("js-buttonposttap-modified"),t.classList.add("noskim"),(0,u.D1)(t.href)?o({element:t,experiments:r}):s(t,r)))}))}),[j,r]),e}var s=function(t,e){var n=new MutationObserver((function(i){i.forEach((function(i){"attributes"===i.type&&"data-xid"===i.attributeName&&t.getAttribute("data-xid")&&(o({element:t,experiments:e}),n.disconnect())}))}));n.observe(t,{attributes:!0})}},81985:function(t,e,n){"use strict";n.d(e,{z:function(){return T}});var i=n(94776),r=n.n(i),u=n(2784),a=n(41332),o=n(99112),c=n(10432),s=n(8698),M=n(4366);function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function j(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function N(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){j(u,i,r,a,o,"next",t)}function o(t){j(u,i,r,a,o,"throw",t)}a(void 0)}))}}function g(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function d(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){g(t,e,n[e])}))}return t}function y(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var i,r,u=[],a=!0,o=!1;try{for(n=n.call(t);!(a=(i=n.next()).done)&&(u.push(i.value),!e||u.length!==e);a=!0);}catch(c){o=!0,r=c}finally{try{a||null==n.return||n.return()}finally{if(o)throw r}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return l(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var L=function(t,e){return e.count-t.count};function T(t,e){var n=(0,u.useContext)(a.Z).buzz,i=void 0===n?{}:n,l=(0,u.useContext)(o.Z),j=l.base_url,g=l.destination,T=(0,u.useState)([]),z=T[0],m=T[1],p=(0,u.useState)(null),I=p[0],f=p[1],D=(0,u.useState)(null),x=D[0],b=D[1],O=(0,s.I)(),A=y((0,c.Z)(null),2),h=A[0],_=h.error,S=h.data,w=A[1],E=(0,u.useCallback)((function(t){f(t),m((function(e){return e.map(function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return function(n){return n.name===t?d({},n,{count:n.count+e}):n}}(t))}))}),[]);return(0,u.useEffect)((function(){if(i&&t){var n="edition=".concat(i.country_code);w(e?"".concat(j,"/content-reactions-api/v2/emoji/buzz/").concat(i.id,"?").concat(n):"".concat(j,"/content-reactions-api/v1/buzz/").concat(i.id,"?").concat(n))}}),[j,i,t,w]),(0,u.useEffect)((function(){_?b(_):S&&S.reactions&&m((function(){var t=S.reactions.sort(L),e=y(t,1)[0],n=(void 0===e?{}:e).count;return t.map((function(t){return d({},t,{increment:function(){return E(t.name)},height:function(t){return function(t,e){return 3*t<90?3*e:e>t?93:Math.round(90*e/t)}(n,t)}})}))}))}),[_,S,E]),(0,u.useEffect)((function(){function t(){return(t=N(r().mark((function t(n){var u,a,o,c;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,u=["reaction=".concat(n),"client_uuid=".concat(O),"edition=".concat(i.country_code)].join("&"),a=e?"".concat(j,"/content-reactions-api/v2/emoji/buzz/").concat(i.id,"?").concat(u,"&page_type=buzz&page_id=").concat(i.id):"".concat(j,"/content-reactions-api/v1/buzz/").concat(i.id,"?").concat(u),t.next=5,fetch(a,{method:"POST"});case 5:return o=t.sent,t.next=8,o.json();case 8:if((c=t.sent)&&c.success){t.next=12;break}return b("request failed"),t.abrupt("return");case 12:(0,M.bC)(i,{action_type:"react",action_value:n,item_name:n,item_type:"button",subunit_name:"reactions",subunit_type:"component",unit_name:i.id,unit_type:"buzz_bottom"}),t.next=18;break;case 15:t.prev=15,t.t0=t.catch(0),b(t.t0);case 18:case"end":return t.stop()}}),t,null,[[0,15]])})))).apply(this,arguments)}I&&function(e){t.apply(this,arguments)}(I)}),[j,i,g,I,O]),{reactions:z,lastReacted:I,serverError:x}}},27354:function(t,e,n){"use strict";n.d(e,{l:function(){return l}});var i=n(94776),r=n.n(i),u=n(2784);function a(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function o(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function o(t){a(u,i,r,o,c,"next",t)}function c(t){a(u,i,r,o,c,"throw",t)}o(void 0)}))}}function c(){return s.apply(this,arguments)}function s(){return(s=o(r().mark((function t(){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,n.e(507).then(n.bind(n,43462));case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function M(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function l(t){(0,u.useEffect)((function(){if(t){var e=function(){var e,n=(e=r().mark((function e(){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,c();case 2:e.sent.buzzes.add(t);case 4:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(i,r){var u=e.apply(t,n);function a(t){M(u,i,r,a,o,"next",t)}function o(t){M(u,i,r,a,o,"throw",t)}a(void 0)}))});return function(){return n.apply(this,arguments)}}();e()}}),[t])}},62430:function(t,e,n){"use strict";n.d(e,{P:function(){return s}});var i=n(2784),r=n(67176),u=n(42235),a=n(73035),o=n(50946),c=n(32966);function s(t){var e=t.buzz,n=void 0===e?{}:e,s=(0,i.useRef)(null),M=(0,i.useContext)(c.Z).experiments;return(0,i.useEffect)((function(){var t=s.current,e=-1!==o.wo.indexOf(n.classification.edition.toLowerCase()),i=-1!==o.u.indexOf(n.classification.section.toLowerCase()),c=o.gK.some((function(t){return n.tags.includes(t)})),l="uk"===n.classification.edition.toLowerCase()&&"shopping"!==n.classification.section.toLowerCase();if(!(!t||e||i||c||n.isAd||0!==n.flags.sensitive||0!==n.flags.nsfw||l)){var j=(0,a.wp)(M);t.querySelectorAll("a[href]").forEach((function(t){t.classList.contains("js-skimlink-subtag-modified")||(t.classList.add("js-skimlink-subtag-modified"),t.hasAttribute("data-skimlinks-tracking")||t.setAttribute("data-skimlinks-tracking",n.id||""),t.addEventListener("click",(function(t){!function(t){var e=t.element,n=t.experimentSubtags;if(e){var i="https://go.redirectingat.com/".replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),u=i+"/?\\?id=(.*)&sref=(.*)&url=(http.*)&xs=(.*)",o=new RegExp(i+"/?\\?id=(.*)&site=(.*)&xs=(.*)&url=(http.*)"),c=new RegExp(u),s=(0,r.nC)(e)["link-id"];if(o.test(e.href)||c.test(e.href)){var M=decodeURIComponent((0,r.Wy)(e).xcust||"").split("|")||"";M[1]=(0,a.b5)(),M[2]=(0,a.o6)(),M[3]=n,M[4]=s,(0,r.tF)(e,{xcust:encodeURIComponent(M.join("|"))})}}}({element:t.target.closest("a"),experimentSubtags:j})}),{once:!0}))})),document.querySelector('script[src*="skimresources"]')||(0,u.v)("https://s.skimresources.com/js/74679X1524629.skimlinks.js")}}),[n.id,M.loaded]),s}},8698:function(t,e,n){"use strict";n.d(e,{I:function(){return s}});var i=n(94776),r=n.n(i),u=n(30353),a=n(2784),o=n(9845);function c(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}function s(){var t=(0,a.useState)(null),e=t[0],n=t[1];return(0,a.useEffect)((function(){var t;(t=r().mark((function t(){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.t0=n,t.next=3,(0,o.n5)({env:u.CLUSTER,legacy:!1});case 3:t.t1=t.sent,(0,t.t0)(t.t1);case 5:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){c(u,i,r,a,o,"next",t)}function o(t){c(u,i,r,a,o,"throw",t)}a(void 0)}))})()}),[]),e}},15382:function(t,e,n){"use strict";n.d(e,{Z:function(){return s}});var i=n(29572),r=n(20238),u=n(30353);function a(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function o(t,e){if(null==t)return{};var n,i,r=function(t,e){if(null==t)return{};var n,i,r={},u=Object.keys(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var c={edition:"en-us",page_size:30,buzz_id:"",page:1,lang:"en",country:"us"};function s(t,e){var n=e.pageSize,s=e.buzzId,M=e.language,l=o(e,["pageSize","buzzId","language"]);if(!t)throw new Error("endpoint is required");var j=i.Nl.getIds(),N=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){a(t,e,n[e])}))}return t}({page_size:n,buzz_id:s,lang:M},l);return Object.keys(c).forEach((function(t){void 0===N[t]&&(N[t]=c[t])})),j.length&&!N.filter&&(N.filter=j),"".concat(u.recsys_api_origin,"/").concat(t).concat((0,r.nZ)(N))}},30729:function(t,e){"use strict";e.Z=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:120;if(!t)return"";var n=t.indexOf("?")>=0?"&":"?",i=t.includes("downsize");return i?t:"".concat(t).concat(n,"downsize=").concat(e,":*")}},36491:function(t,e,n){"use strict";var i=n(52322),r=n(25237);e.Z=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.ssr,u=void 0===n||n,a=e.suspense,o=void 0!==a&&a,c=e.loading,s=void 0===c?function(){return null}:c;return window.location.search.match(/(\?|&)layer=content/)?function(){return(0,i.jsx)("div",{})}:(0,r.default)(t,{loading:s,ssr:u,suspense:o})}},32527:function(t,e,n){"use strict";n.d(e,{I:function(){return s}});var i=n(94776),r=n.n(i),u=n(26528),a=n.n(u),o=n(34406);function c(t,e,n,i,r,u,a){try{var o=t[u](a),c=o.value}catch(s){return void n(s)}o.done?e(c):Promise.resolve(c).then(i,r)}var s=function(){var t,e=(t=r().mark((function t(e,n){var i,u,c,s,M;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=n.req,u=i?"http://localhost:".concat(o.env.PORT):"",c=i?{headers:i.headers}:{},t.prev=3,t.next=6,a()("".concat(u).concat(e),c);case 6:if((s=t.sent).ok){t.next=13;break}return t.next=10,s.text();case 10:M=t.sent;try{M=JSON.parse(M).message}catch(r){}return t.abrupt("return",{error:{error:M,statusCode:s.status}});case 13:return t.abrupt("return",s.json());case 16:return t.prev=16,t.t0=t.catch(3),t.abrupt("return",{error:{error:t.t0.message||"Error fetching API data",statusCode:500}});case 19:case"end":return t.stop()}}),t,null,[[3,16]])})),function(){var e=this,n=arguments;return new Promise((function(i,r){var u=t.apply(e,n);function a(t){c(u,i,r,a,o,"next",t)}function o(t){c(u,i,r,a,o,"throw",t)}a(void 0)}))});return function(t,n){return e.apply(this,arguments)}}()},27374:function(t,e,n){"use strict";n.d(e,{_:function(){return i}});var i=function(t){var e;return"Discussion"===(null===t||void 0===t||null===(e=t.classification)||void 0===e?void 0:e.section)}},19844:function(t,e,n){"use strict";n.d(e,{f:function(){return i}});var i=function(t){var e;return(null===t||void 0===t||null===(e=t.bylines)||void 0===e?void 0:e.length)&&t.bylines.some((function(t){return"Brand Publisher"===(null===t||void 0===t?void 0:t.title)}))}},29572:function(t,e,n){"use strict";n.d(e,{Nl:function(){return c},mH:function(){return s}});var i=n(93002),r=n(2784);function u(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var o=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.max_size,r=void 0===n?20:n,a=e.storage_key,o=void 0===a?"buzz_history":a;u(this,t),this.storage=!(0,i.s)()&&localStorage||{},this.max_size=r,this.storage_key=o,this.ids=this.read()}var e,n,r;return e=t,(n=[{key:"getIds",value:function(){return this.ids}},{key:"add",value:function(t){if(t){var e=this.ids.filter((function(e){return e!==t}));e.push(t),this.ids=e.slice(-this.max_size),this.write()}}},{key:"read",value:function(){var t=this.storage[this.storage_key];if(!t)return[];var e=[];try{e=JSON.parse(t)}catch(n){}return e}},{key:"write",value:function(){this.storage[this.storage_key]=JSON.stringify(this.ids)}}])&&a(e.prototype,n),r&&a(e,r),t}(),c=new o;function s(t){(0,r.useEffect)((function(){c.add(t)}),[t])}},61784:function(t,e,n){"use strict";n.d(e,{j:function(){return u},h:function(){return a}});var i=function(t,e,n,i){return{category:t,title:e,description:n,newsletterId:i}},r={BuzzFeedDaily:i("daily","BuzzFeed Daily","Keep up with the latest daily buzz with the BuzzFeed Daily newsletter!","buzzfeed_email_daily"),DIY:i("DIY","Want awesome DIY tips in your inbox three times a week? Sign up for the BuzzFeed DIY newsletter!","","buzzfeed_email_nifty"),Style:i("Style","Want amazing beauty and style tips twice a week? Sign up for the As/Is newsletter!","","buzzfeed_email_asis"),Tasty:i("tasty",'<svg aria-labelledby="tasty-newsletter-logo-title" viewBox="0 0 150 76" width="80" height="41"><title id="tasty-newsletter-logo-title">Tasty</title><g fill="none" fill-rule="evenodd"><path fill="#fefefe" d="M113.9 40.23l-1.44.66-.46.2c-.64.3-1.3.6-1.97.97-.75.42-1.34.88-1.86 1.3-.15.1-.3.2-.45.33-.77.58-2 1.65-2.73 2.45-.03 0-.04.03-.06.05.02-.4.03-.83.02-1.3l-.08-4.83c0-.45-.02-.95-.04-1.42s-.04-.96-.04-1.35c0-.33.02-.74.04-1.18.02-.5.05-1.02.05-1.54v-1.1l.02-1.26c0-.84-.05-3.7-.07-4.53 0-.5-.04-1.13-.06-1.72l-.04-1.06-.07-1.36c-.03-.36-.05-.7-.06-1.07l-.12-2c-.04-.7-.1-1.4-.1-1.77 0-.32-.04-.88-.08-1.53l-.1-1.54v-.34l.7-.08.76-.07c.2 0 .37 0 .55-.02.78 0 1.85-.02 2.94-.5-.22 1.3-.52 3.3-.6 3.93-.1.68-.1 1.3-.08 1.8v.72l-.1.8c-.08.56-.15 1.14-.2 1.72l-.12.9c-.07.5-.15 1.04-.2 1.62-.03.64-.16 4.34-.06 5.42.13 1.46.5 2.76 1.06 ********* 1 1.7 1.7 ******** 1.54 1.38 2.4 **********.62.34 1 .5l-.1.05zM90.08 21.6c0 .4-.05.88-.1 1.35l-.1 1.26c-.02.4-.13 1.63-.22 2.62l-.17 2.02-.08 1.3-.08 1.2c-.03.5-.04 1-.04 1.42 0 .35 0 .7-.04 1l-.1 1.1c-.08.53-.15 1.08-.18 1.6l-.25 2.85c-.2 2.33-.33 3.77-.35 4.27-.02.48 0 1.33 0 2.66 0 .8.03 1.8 0 2.1 0 .43 0 .9-.02 1.38 0 .43-.02.84-.03 1.22-.02.38-.1 1.56-.18 2.5l-.16 2.35c0 .1 0 .25-.02.43-.05-.4-.1-.82-.17-1.2v-.07c-.13-.85-.4-1.54-.63-2.1-.08-.2-.18-.44-.2-.56-.2-.82-.44-1.66-.72-2.58-.23-.74-.48-1.43-.73-2.1l-.15-.38c-.23-.62-.47-1.2-.7-1.78-.1-.2-.18-.4-.26-.6-.22-.5-1.57-3.76-1.97-4.65l-1.05-2.26-1.06-2.23c-.07-.14-.16-.42-.24-.7-.15-.45-.32-1-.6-1.6l-.48-1-.57-1.2c-.2-.43-.42-.84-.6-1.2-.2-.33-.36-.65-.5-.94l-.4-1c-.13-.38-.3-.8-.47-1.24-.15-.33-.3-.7-.44-1.1.02 0 .03.02.04.03.72.47 1.6 1.05 2.8 1.14h.44c2.03 0 3.4-1.03 3.84-1.37 1.13-.86 1.95-2.04 2.37-3.42l.08-.2c.23-.74.55-1.75.55-2.9v-1.15l.02-1.23c0-.38-.02-.75-.05-1.1.6.17 1.26.26 1.98.26.44 0 .9-.03 1.48-.08l.5-.05c0 .25 0 .53.02.83 0 .16 0 .34.02.5.02.65 0 3.87-.03 4.63zm59.07 10l-.42-2.36c-.46-1.26-1.08-2.3-1.87-3.14-1.15-1.23-2.53-1.85-4.1-1.85-.26 0-.52.02-.8.05-1.44.18-2.62.83-3.5 1.37l-.47.3c-.52.3-1.18.68-1.84 1.23l-.4.32c-.32.26-.78.73-1.1 1 .03-.17.2-.55.24-.74l.4-1.88.16-.73c.13-.63.23-1.2.3-1.64l.16-.85c.06-.23.1-.5.17-.8l.32-1.46.3-1.43.2-1c.2-.77.36-1.62.53-2.52.15-.76.23-1.52.3-2.18l.05-.48c.12-.97.16-1.93.15-2.85v-.52c0-.9 0-2.03-.46-3.15-.8-1.94-2.5-3.3-4.6-3.6-.83-.13-1.5-.2-2.1-.2-.57 0-1.1.06-1.58.17-1.87.4-2.97 1.64-3.55 2.3l-.17.18-.23.22c-.15.14-.34.3-.53.52-.12-.68-.4-1.32-.9-1.9-1.05-1.16-2.65-1.9-4.5-2.1-.2-.02-.42-.03-.63-.03-1.17 0-2.17.33-2.9.57l-.3.1-.15.04c-.83.26-2.08.65-3.07 1.62-.25-.5-.54-.95-.87-1.33-1.35-1.56-3.54-1.9-4.73-1.98-.17-.03-.35-.03-.54-.03-.64 0-1.2.06-1.66.1l-.43.06c-.32 0-.7-.04-1.12-.08-.5-.05-1.06-.1-1.65-.12-.93 0-1.85-.03-2.77-.04-.12-.02-.24-.02-.36-.02-.4 0-.8 0-1.18.02l-1 .02h-.4c-.6 0-1.12.05-1.6.1-.36.03-.7.06-1 .06h-.03l-.66-.05C91.8.9 91.23.83 90.6.83h-.47c-.93 0-1.78.06-2.54.2-.78.13-3.14.54-4.38 2.76l-.08.13c-.14.23-.32.53-.5.9-.75-.76-1.56-1.38-2.44-1.85-.74-.4-1.48-.62-2.06-.8-.24-.06-.47-.12-.65-.2-.44-.15-4.4-1.45-5.76-1.63-.5-.07-1-.1-1.47-.1-.9 0-1.8.12-2.6.35-1.6.46-2.7 1.44-3.56 2.23v.02c-.1.1-.2.17-.3.25-.55.47-1.3 1.12-2 2.32-.5.88-.88 1.8-1.2 2.6l-.1.18c-.32.82-1.55 4.38-1.8 5.53-.17.87-.35 1.8-.44 2.8-.1 1.05-.02 4.55.1 5.56.1.75.26 1.55.4 2.25l.15.6c.23 1.08 1.15 3.82 1.72 5.22.2.5.43.93.65 1.35.2.36.37.7.5 1.03l.35.96c.16.5.34 1.04.57 1.6.2.5.75 1.7 1.28 2.87l.86 1.88c.24.57.5 1.1.73 1.55.16.3.3.6.42.86.2.47.58 1.27 1 2.1-.4-.06-.83-.1-1.24-.1-1 0-1.8.2-2.4.35l-.25.07c-1.14.27-2.82.8-4.1 2.33l-.33.4-.15-.77c-.04-.3-.07-.66-.1-1.03-.04-.44-.08-.9-.15-1.37l-.26-1.6c-.15-1-.42-2.67-.46-3.1-.07-.7-.14-1.46-.2-2.4-.04-.3-.1-.92-.15-1.64l-.28-3.3-.1-1.72c-.1-1.6-.18-2.76-.23-3.28l-.07-.97c-.02-.47-.05-1-.1-1.57-.1-.87-.23-1.72-.35-2.55-.1-.6-.2-1.15-.32-1.65l-.18-.9c-.16-.9-.88-4.2-1.14-5.13-.26-.95-1.5-4.44-2.03-5.45-.5-.97-1.1-2.18-2.24-3.13-1.22-1.02-2.67-1.52-4.45-1.52h-.16c-1.25.02-2.5.34-3.83.98-1.02.5-2.33 1.25-3.35 2.65-.68.9-1 1.85-1.24 2.6l-.2.62-.35.76c-.27.58-.57 1.23-.8 2-.27.87-.53 1.68-.78 2.38l-.16.43c-.2.6-.5 1.4-.77 2.37-.13.5-.24 1.05-.35 1.56l-.25 1.12c-.2.82-.37 1.53-.57 2.45l-.4 1.84-.2.95-.37 1.95-.27 1.45c-.2.95-.72 3.8-.85 4.52l-.24 1.17c-.1.5-.22 1-.3 1.5-.13.76-.3 2.26-.45 3.6-.1.75-.17 1.48-.22 1.78l-.12.92c-.08.63-.17 1.28-.26 1.82l-.2 1.24c-.05.42-.1.8-.18 1.23-.16.87-.77 4.5-.9 5.45-.06.37-.3 1.58-.5 2.47-.24 1.15-.4 1.88-.45 2.3-.1.54-.2 1.26-.34 2l-.3 1.54c-.02-.6-.06-1.16-.1-1.68l-.38-3.32-.17-1.45c-.12-1.08-.38-4.04-.4-4.6 0-.77.08-6.57.1-7.36l.13-2.57c.13-2.57.2-4.42.22-4.96 0-.27 0-.84.02-1.5 0-1.28.03-3.02.05-3.55 0-.55 0-1.06-.03-1.5 0-.4-.03-.75 0-1.05 0-.5.07-1.88.13-3.1.05-.86.1-1.65.1-2 .03-.52.02-1 .02-1.47v-1.1c.03-.6 0-1.33-.03-2.2 0-.05 0-.12-.02-.2l2.13-.12 1.3-.06.4-.04c.9-.07 1.8-.15 2.68-.48 1.28-.5 2.9-1.73 3.42-4.88.18-1.17.18-2.37-.03-3.6-.27-2.3-1.32-3.7-2.16-4.45-.98-.9-2.24-1.48-3.96-1.84-.94-.2-2.05-.42-3.26-.42h-.08c-.8 0-1.56.12-2.23.22-.24.04-.5.08-.74.1-.74.1-4.5.6-5.5.84l-1.6.38c-1.13.27-2.86.7-3.48.8-.63.1-1.2.27-1.72.42-.38.1-.73.22-1.02.27-.68.12-4.85.98-5.9 1.33C2.06 7.52.63 9.37.3 11.87c-.15 1.24-.26 3.1.55 4.9 1.1 2.44 3.58 2.84 4.63 3l.3.05c.6.1 1.35.23 2.2.23.24 0 .46 0 .67-.02.27-.02.6-.03.93-.03h.34c.03 1.3.04 3.78.03 4.2-.02.54-.02 1.8-.02 3v1.97c0 .35-.04.82-.07 1.28-.04.56-.08 1.13-.1 1.67v5.24l-.07 2.58-.03.43L9.55 45c-.02.54-.02 1.85-.03 3.13v1.93l-.04 2.24c-.02.78-.12 3.97-.16 4.72l-.03.66-.1 1.65-.1.53c-.1.5-.2 1.2-.27 1.97-.05.7-.02 1.38 0 1.97l.03.78c0 1.63.4 2.93.76 3.73 1.3 3.02 3.6 3.34 4.54 3.34.43 0 .86-.06 1.3-.18l.37-.1c1.48-.43 2.25-.65 2.8-.94l.18-.08h.46l.44.02c.22.02.42.02.6.02 3.16 0 4.3-2.37 4.93-3.64.13-.26.3-.6.47-1.03-.04.68 0 1.33.17 1.95.5 2 1.25 2.86 1.97 3.58 1.58 1.6 4.1 1.7 4.84 1.7.32 0 .62 0 .9-.05 1.45-.22 6.32-1.14 8.2-3.92.9-1.36 1.18-4.25 1.25-6.44v-.13h.07c.5.03 1.2.08 1.98.1-.02 1.18-.06 4.42-.04 5.18v.45c-.02.73-.04 1.74.33 2.8.54 1.54 1.57 2.76 3 3.53 1.36.73 3.05.8 3.7.8.37 0 .72 0 1.07-.04 1.7-.2 3.42-.87 4.47-1.76 1.37-1.17 1.9-2.4 2.26-3.53.24-.76.47-2.57.6-3.98.4.76.82 1.44 1.12 1.82.64.84 1.3 1.58 2.04 2.26.82.77 1.72 1.35 2.44 1.82.78.5 4.3 2.17 6 2.4.58.06 1.27.13 2.03.13.47 0 .92-.03 1.37-.1 1.25-.15 2.36-.55 3.3-.92 1.22-.5 2.2-1.2 2.94-1.75 1.1-.82 3.55-3.85 4.17-5.13.1-.17.18-.35.27-.52.35-.7.75-1.46 1-2.36.1-.33.27-1.08.44-1.96.04.65.14 1.22.23 1.7.14.76.37 2.03 1.23 3.35.56.85 1.93 2.3 4.84 2.3.34 0 .7-.03 1.04-.07.98-.1 1.87-.37 2.65-.6l.5-.14c.5-.15.93-.3 1.3-.43l.64-.2c1.7-.1 2.9-.68 3.7-1.37l.15.3c.5 1 1.1 2.04 1.68 2.84.66.94 1.38 1.62 2.02 2.22l.15.15c.82.78 1.72 1.58 2.78 2.17 1.68.9 4.74 1.94 6.2 2.06.4.04.85.06 1.33.06 1.64 0 4.02-.24 5.2-.65 2.5-.86 4.33-2.77 5.12-3.6l.07-.07c.45-.47 1.33-1.4 2.03-2.33.8-1.06 2.42-4.28 2.7-4.93.5-1.1.8-2.28 1.05-3.28.27-1.1.45-2.13.55-3.17.1-.9.13-1.84.16-2.97 0-.55 0-1.1-.03-1.6l-.03-1.2c0-.24 0-.48.02-.73.02-.63.05-1.33 0-2.1-.04-.43-.1-.84-.14-1.2-.05-.4-.1-.77-.1-1 .02-.42 0-.87-.02-1.3.02 0 .03 0 .05-.02l.67-.48c.4-.3 2.28-1.36 3.2-1.87.6-.34 1.06-.6 1.33-.78.9-.53 1.6-1.13 2.24-1.65l.37-.3c.13-.12.35-.27.57-.44.42-.3.95-.68 1.48-1.14.26-.23 3.45-3.28 4.2-4.87.2-.48.35-.95.43-1.4V31.6z"></path><path fill="#333" d="M32.46 9.64c-.1-1-.43-1.82-1.04-2.37-.6-.54-1.47-.84-2.4-1.04-.8-.17-1.67-.34-2.6-.34-.83 0-1.7.2-2.6.3-.86.1-4.37.6-5.14.77-1.06.24-4.17 1.02-5.25 1.2-.87.15-1.77.53-2.72.7-.93.16-4.72.98-5.4 1.2-.94.32-1.43 1.08-1.58 2.24-.12.95-.18 2.06.26 3.03.33.73 1.16.9 2.04 1.04.72.1 1.52.3 2.36.22.8-.08 1.66-.02 2.38-.1 1.14-.1 2.22-.34 2.37-.08.15.25.13 1.53.2 2.65.05.84.07 4.64.05 5.22-.02.86 0 4.05 0 4.93-.02.87-.16 2.06-.17 2.94v5.25c-.02.88-.06 2.18-.1 3.05 0 .88-.1 3.82-.1 4.63-.03.8-.04 4.03-.05 4.84l-.03 2.42c-.02.8-.12 4.04-.16 4.84-.05.8-.07 1.62-.14 2.42-.04.55-.28 1.45-.36 2.46-.05.8.04 1.7.04 2.5 0 1 .25 1.84.48 2.37.35.83.9 1.42 1.74 1.2.82-.25 2.2-.62 2.55-.8 1.04-.53 1.4-.46 2.24-.45 1.55.02 1.86.35 2.84-1.68.32-.64.67-1.4.72-2.26.05-.75 0-1.58-.06-2.4-.07-.78-.47-4-.54-4.68-.1-.83-.4-4.1-.4-4.94-.02-.83.06-6.7.1-7.52.02-.83.3-6.6.32-7.47.02-.85.04-4.27.07-5.12.03-.85-.06-1.7-.03-2.57.03-.85.2-4.26.25-5.12.03-.85-.02-1.7.02-2.55.04-1.17-.2-3.9-.03-4.98.08-.5 1.83-.45 2.16-.57.5-.2 3.64-.3 4.34-.35.86-.08 1.68-.12 2.2-.3.7-.28 1.06-1.18 1.23-2.22.12-.73.14-1.6-.04-2.54M57.14 64.2c.04-.84-.23-1.66-.26-2.47-.04-.84 0-1.67-.08-2.46-.1-.86-.35-4.2-.48-4.92-.18-.98-.65-4.03-.75-4.83-.1-.8-.33-1.6-.45-2.4-.12-.8-.14-1.6-.25-2.4-.12-.8-.68-4.02-.75-4.85l-.22-2.47c-.07-.82-.35-4.1-.4-4.93-.07-.82-.25-4.12-.33-4.94-.08-.82-.08-1.65-.17-2.47-.1-.82-.2-1.64-.33-2.46-.12-.82-.33-1.62-.48-2.43-.15-.8-.85-4.03-1.07-4.83-.23-.83-1.4-4.06-1.77-4.78-.43-.82-.82-1.58-1.42-2.08-.65-.55-1.4-.72-2.34-.7-.72 0-1.47.2-2.4.63-.8.4-1.55.87-2.1 1.6-.5.7-.68 1.6-1.02 2.46-.34.82-.78 1.62-1.04 2.47-.27.9-.55 1.76-.83 2.55-.2.6-.56 1.46-.84 2.5-.2.78-.36 1.66-.58 2.6-.2.8-.37 1.5-.57 2.4l-.57 2.76c-.2.87-.46 2.5-.63 3.37-.18.9-.7 3.63-.84 4.46-.17.95-.4 1.8-.53 2.6-.18 1.12-.52 4.4-.64 5.3-.14.9-.26 1.95-.4 2.84-.16.9-.24 1.63-.4 2.5-.16.9-.75 4.46-.88 5.34-.13.9-.85 4.07-.95 4.78-.1.72-.22 1.42-.36 2.13-.12.7-.27 1.4-.42 2.13-.18.9-.63 2.67-.4 3.6.33 1.24.68 1.6 1.1 2 .58.6 2.13.8 2.8.7 1.34-.22 4.9-1.07 5.84-2.45.43-.65.65-3.86.68-4.62.02-.82.13-3.1.28-3.32.2-.26 1.23-.18 2.88-.17.36.02 1.5.15 2.65.14 1.18 0 2.92-.03 3.02.13.14.25.12 2.05.1 3.22-.02 1.18-.06 4.52-.05 5.24.02.77-.07 1.52.15 2.16.24.68.67 1.24 1.38 1.62.53.3 1.66.5 2.76.37 1.08-.13 2.14-.57 2.6-.97.77-.65 1-1.25 1.22-1.95.2-.6.54-3.74.56-4.68m-9.4-11.98c-.77.2-3.8.1-4.6.02-1.12-.1-2.35.05-2.47-.27-.14-.38.26-2.02.3-3 .03-.9.22-3.16.37-4.37.1-.76.1-1.64.22-2.58.1-.8.4-4.3.54-5.16.12-.86.35-1.7.5-2.55.12-.86.26-1.72.4-2.55.15-.9.8-4.25 1-4.98.33-1.38.76-2.8 1.04-2.77.23.02.44 1.43.6 2.96l.68 8.14.4 5.28c.07.87.3 1.75.35 2.63.07.9.2 1.76.26 2.6.07.97.16 1.78.2 2.56.08 1.23.6 3.96.2 4.06m23.7-28.57c-.23-.62-.67-1.76-.8-2.83-.07-.8-.03-3.4.23-4.27.25-.84.83-2.54 1.33-3.28.47-.7.97-1.24 1.73-1.76.87-.58 1.8-.16 2.2.57.53 1 .18 3.83 0 4.67-.17.8-.7 1.55-.78 2.32-.08.87.34 1.62.7 2.3.18.3.7.77 1.38 1.17.64.38 1.26.88 1.67.9.85.08 1.46-.27 1.96-.66.55-.4.95-.97 1.17-1.7.2-.64.46-1.37.46-2.08V16.6c0-.8-.1-1.6-.15-2.37-.05-.8 0-1.62-.14-2.38-.12-.8-.38-1.57-.65-2.3-.32-.84-.8-1.5-1.3-2.03-.55-.6-1.17-1.14-1.87-1.52-.7-.37-1.5-.5-2.27-.8-.73-.26-4.17-1.34-5-1.45-.94-.12-1.86-.1-2.7.15-.83.24-1.5.84-2.2 1.5-.6.53-1.12.87-1.63 1.74-.42.73-.74 1.54-1.07 2.35-.33.8-1.45 4.12-1.62 4.94-.17.8-.32 1.6-.4 2.4-.07.8 0 4.05.1 4.85.1.78.33 1.76.5 2.55.15.8.98 3.27 1.53 4.6.3.72.78 1.48 1.15 2.37.32.77.56 1.7.9 2.53.35.8 1.8 3.95 2.14 4.72.37.87.8 1.65 1.12 2.36.48 1.06 2 4.12 2.43 4.96.46.86.75 1.8 1.12 2.68.38.9.7 1.8.93 2.75.14.6.96 3.75 1.03 4.53.07.8-.1 3.4-.86 4.48-.56.83-.93 1.2-1.86 1.13-1.13-.1-1.83-.94-2.15-1.86-.4-1.12-.25-2.48-.25-2.9 0-.6.13-1.5.1-2.5 0-.8-.23-1.64-.38-2.46-.16-.86-.43-1.63-.85-2.3-.44-.7-.97-1.34-1.85-1.54-1-.23-1.85.05-2.63.23-.94.22-1.7.53-2.27 1.2-.56.66-1.07 1.45-1.2 2.4 0 0 .12 4.05.24 4.97.1.77.4 1.55.6 2.4.18.78.45 1.55.75 2.35.28.75 1.78 3.66 2.3 4.33.48.64 1 1.24 1.63 1.82.6.54 1.26 1 1.97 1.45.67.43 3.66 1.75 4.57 1.86.8.1 1.65.18 2.53.07.83-.1 1.62-.38 2.44-.72.77-.3 1.45-.75 2.16-1.3.65-.5 2.75-3.07 3.15-3.9.37-.75.84-1.55 1.07-2.32.22-.8.8-3.92.77-4.74-.02-.82-.14-1.65-.26-2.48-.1-.8-.58-1.53-.77-2.35-.2-.77-.42-1.55-.66-2.35-.24-.76-.53-1.52-.82-2.3-.27-.76-.6-1.5-.9-2.27-.32-.75-1.57-3.76-1.92-4.52l-1.03-2.22-1.05-2.2c-.35-.74-.5-1.57-.85-2.32-.34-.73-.7-1.46-1.06-2.2-.33-.75-.76-1.45-1.1-2.2-.33-.74-.57-1.53-.9-2.3-.32-.73-.58-1.5-.9-2.27-.3-.75-.67-1.48-.96-2.25M109.2 9.5c.23-.54.5-1.4.44-2.22-.07-.85-.44-1.7-.82-2.14-.4-.46-1.4-.75-2.36-.8-.92-.07-1.86.15-2.43.14-.94 0-1.9-.18-2.8-.2-.94 0-1.86-.03-2.8-.04-.9-.02-1.84.05-2.77.03-.9 0-1.83.17-2.78.16-.54 0-1.42-.13-2.36-.15-.78-.02-1.6 0-2.34.14-.9.16-1.6.43-1.95 1.05-.23.4-.76 1.15-.9 2.07-.1.7.16 1.55.25 2.28.1.8.3 1.47.77 2 .5.58 1.66.56 2.87.44 1.14-.1 3.5-.36 3.84-.14.26.16.13 1.18.32 2.3.12.7.12 1.52.15 2.46.03.74.02 4.04-.02 4.9-.03.78-.14 1.8-.2 2.65-.04.8-.33 3.84-.4 4.67-.05.8-.08 1.67-.14 2.5-.07.8-.03 1.58-.08 2.4-.06.8-.24 1.87-.3 2.7-.04.8-.56 6.2-.6 7.02-.03.82.05 3.93.02 4.72l-.07 2.6c-.03.84-.3 4.1-.33 4.87-.04.87-.32 3.98-.38 4.6-.08.8.05 1.52.17 2.22.1.63.22 1.35.7 2.08.44.67 1.54.82 2.63.7.84-.1 1.73-.4 2.6-.64 1.1-.3 2.03-.7 2.55-.73 1.22-.04 1.92-.47 2.27-1.14.34-.67.45-1.54.4-2.6-.06-.8-.28-1.7-.35-2.64l-.17-2.55-.12-2.54c-.03-.86-.1-1.7-.14-2.56l-.08-2.55c-.03-.85.06-1.7.05-2.56-.02-.85-.1-4.04-.1-4.9 0-.84-.06-1.92-.07-2.8 0-.82.1-1.85.1-2.72l.02-2.36c0-.8-.05-3.66-.07-4.45 0-.8-.06-1.94-.08-2.74-.03-.8-.1-1.6-.12-2.42-.03-.78-.2-2.93-.22-3.74l-.16-3.06c-.04-.76-.26-2.92-.26-3.5 0-.48 1.2-.33 2.6-.36.85-.03 1.65-.18 2.36-.22.9-.05 1.73.05 2.35-.26.65-.32 1.05-1.03 1.33-1.95m36.4 20.94c-.35-.92-.74-1.52-1.16-1.97-.63-.68-1.2-.84-1.97-.74-.68.08-1.33.4-2.08.87-.63.4-1.35.74-2 1.27l-1.8 1.5c-.62.53-1.12 1.2-1.72 1.65-.7.53-1.44.88-2.08 1.16-1.1.48-2.27 1.16-2.27 1.16-.3-.2.02-2.34.13-3.06.1-.62.23-1.68.43-2.76.15-.82.33-1.53.5-2.43l.57-2.65c.22-1.07.34-1.9.48-2.53.13-.6.27-1.42.5-2.35.18-.72.32-1.53.5-2.36.2-.76.35-1.56.5-2.36.15-.8.22-1.63.3-2.4.1-.86.15-1.65.13-2.4-.02-.9.06-1.8-.2-2.44-.33-.82-1.06-1.36-1.92-1.5-.94-.13-1.78-.2-2.45-.06-.87.2-1.38.9-1.9 1.45-.52.56-1.04.82-1.3 1.63-.23.74-.1 1.28-.2 2.1-.1.8-.24 1.58-.28 2.36-.05.88-.17 2.66-.26 3.48-.08.8-.5 3.02-.65 3.82-.13.8-.25 3.24-.44 4.35-.12.64-.2 3.04-.83 3.88-.54.74-1.46 1.1-2.55 1.13-1.04.05-1.8-.5-2.54-1.06-.57-.43-.58-2.2-.63-3.4-.02-.86.18-1.8.3-2.8.1-.94.1-1.92.27-2.78.22-1.2.34-2.23.43-2.77.1-.52.7-3.68.88-4.46.16-.74.52-1.46.66-2.18.16-.8.23-1.63.33-2.3.18-1.16.27-2 .1-2.2-.48-.52-1.35-.88-2.3-.98-.83-.1-1.67.24-2.45.5-.87.27-1.77.5-2.13 1.22-.16.3-.5 1.13-.77 2.34-.15.7-.75 3.96-.9 4.8-.12.78-.63 4.02-.74 4.84-.1.8.02 1.65-.08 2.45-.1.8-.2 1.64-.28 2.43-.07.84-.25 1.63-.3 2.42-.05.85-.15 4.18-.08 4.9.1 1.04.35 1.87.63 2.38.37.67.78 1.35 1.24 1.85.5.56.93.85 1.54 1.2.63.35 1.6.75 2.33.88l2.33.42c.8.13 1.5.14 2.3.27 1.35.22 3.54-.1 3.54-.12.02.13-1.6 1.23-2.75 1.83-.64.33-1.36.7-2.1 1.04-.72.36-1.46.7-2.1 1-1.04.48-1.85.9-2.1 1.02-.74.33-1.48.66-2.2 1-.75.34-1.52.68-2.22 1.07-.72.38-1.28.88-1.93 1.37-.64.48-1.7 1.4-2.25 2-.58.64-1.8 3.14-2.02 4-.22.82-.38 1.67-.45 2.54-.06.86-.27 1.75-.2 2.62.06.86.24 1.73.4 2.58.2.85 1.28 4 1.67 4.76.4.78.92 1.7 1.4 2.38.52.72 1.14 1.3 1.75 1.87.64.62 1.32 1.23 2.06 1.64 1.37.75 3.98 1.58 4.84 1.65 1.7.14 4.35-.15 5.1-.4 1.8-.63 3.28-2.23 3.85-2.82.57-.6 1.26-1.36 1.76-2 .5-.67 1.98-3.52 2.3-4.25.38-.87.64-1.85.85-2.7.22-.87.38-1.75.47-2.7.08-.84.12-1.74.15-2.72.02-.83-.06-1.74-.05-2.72 0-.83.08-1.7.02-2.56-.06-.86-.25-1.72-.23-2.56 0-.9-.18-2.34-.1-2.66.13-.46 1.43-1.25 2.17-1.8.75-.56 4.03-2.37 4.8-2.84.83-.5 1.52-1.1 2.23-1.68.5-.4 1.27-.9 1.98-1.5.6-.53 3-3.02 3.3-3.7.4-.8.17-1.65-.1-2.44m-20.3 25.9c-.1 1.05-.2 1.92-.43 2.68-.25.82-.6 1.6-1.05 2.52-.4.8-.96 1.5-1.55 1.95-.65.5-1.5.66-2.34.75-.73.07-1.53-.05-2.4-.3-.83-.27-1.63-.56-2.12-1.08-.54-.6-.8-1.42-.96-2.2-.15-.74-.34-1.57-.14-2.37.42-1.6 1.65-3.85 2.2-4.42.38-.42 1.04-1.14 1.78-1.72.6-.45 1.12-.87 1.78-1.32.7-.45 1.43-.9 2.06-1.24.9-.5 2.95-2.03 3.1-1.6.32 1-.1 1.16.07 *********.05 1.33 0 2.38-.03.72.12 1.4.02 2.35"></path></g></svg>',"Get all the best Tasty recipes in your inbox! Sign up for the Tasty newsletter today!","buzzfeed_email_tasty"),TvAndMovies:i("tvandmovies","TV and Movies","Get all the best moments in pop culture & entertainment delivered to your inbox.","buzzfeed_email_tvandmovies")};function u(t){var e=t.edition,n=t.section,i=t.isNews;return"US"===e&&i?r.BuzzFeedDaily:function(t){return{Animals:r.BuzzFeedDaily,BuzzFeed:r.BuzzFeedDaily,Celebrity:r.BuzzFeedDaily,DIY:r.DIY,Food:r.Tasty,Nifty:r.DIY,Parents:r.BuzzFeedDaily,"As/Is":r.Style,Tasty:r.Tasty,Travel:r.BuzzFeedDaily,TVAndMovies:r.TvAndMovies}[t]}(n)}var a=function(t){return![t.tags.includes("--no-newsletter-signup"),t.bfpFormats.includes("newsletter_signup"),t.flags.nsfw,t.isAd,t.isShopping,t.isQuiz&&t.isUS,"Japan"===t.classification.edition].some((function(t){return t}))}},12383:function(t,e,n){"use strict";function i(t){return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}n.d(e,{x:function(){return i}})},57673:function(t,e,n){"use strict";n.d(e,{l:function(){return i}});var i=function(){return window.scrollY||window.pageYOffset}},46811:function(t,e,n){"use strict";n.d(e,{Bd:function(){return z},Hd:function(){return p},BN:function(){return m},Om:function(){return I}});var i=n(52322),r=n(2784),u=n(28316),a=n(99036),o=n.n(a),c=n(5103),s=n(13681),M=n(20238),l=n(30353),j=n(6294),N=function(){return(0,j.tq)()},g=n(31781);var d=function(t){var e=t.containerEl,n=t.contentEl,u=t.position,a=void 0===u?"top":u,o=t.text,c=t.themes,s=(0,r.useState)(!0),M=s[0],l=s[1],j=r.createRef();return j.current=e,(0,r.useEffect)((function(){setTimeout((function(){l(!1),setTimeout((function(){n&&n.removeChild(e)}),1e3)}),3e3)}),[e,n]),(0,i.jsx)(g.Z,{container:j,position:a,shouldShow:M,themes:c,text:o})},y=n(94470),L=n.n(y),T=n(63778);function z(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",r=["twitter","facebook","copy"];return["pagelevel","pagelevelSticky"].includes(t)?"buzzfeed_news"===e.destination_name?r:n&&"bottom_share_bar"!==i?"Japan"===e.classification.edition?["facebook","twitter"]:["facebook","pinterest","copy"]:"Russia"===e.category?["facebook","vk","twitter","email","pinterest","copy"]:["copy","facebook","pinterest","twitter","email"]:"pagelevelList"===t?"Japan"===e.classification.edition?["lineapp","sms","email","pinterest","snapchat","copy"]:["twitter","snapchat","whatsapp","sms","email","copy"]:"pageLevelOutlineThick"===t?"buzzfeed_news"===e.destination_name?r:"Japan"===e.classification.edition?["twitter","bluesky","facebook","pinterest","copy"]:["bluesky","facebook","pinterest","copy"]:[]}function m(t,e,n){var r={utm_source:"dynamic",utm_campaign:"bfshare".concat(t)},a=e.canonical_url,j=e.shareData,g=n.itemId,y=n.quizId,z=n.subbuzzId,m=n.getCopyText,p="";g&&y?(r.quiz_result="".concat(y,"_").concat(g),r.quote=o()(n.text||n.title),p=y,"twitter"===t&&(r.rid=g)):z&&(r.sub=z);var I="".concat(a).concat((0,M.nZ)(r)).concat(p?"#".concat(p):""),f={};"twitter"===t?f.title=o()(n.text||n.title||j.twitter.title||e.title):"facebook"===t?f.fbAppId=l.destinations[e.destination_name].facebook_app_id:["lineapp","whatsapp"].includes(t)?f.title=o()(n.title||e.title):"copy"===t&&m&&(f.getCopyText=m),f.title=(0,T.decodeHtmlEntities)(f.title);var D=(0,s.BN)(I,t,f,!N());if("copy"===t&&n.element){var x=document.createElement("div");x.className=L().tooltipContainer,n.element.appendChild(x),u.render((0,i.jsx)(d,{containerEl:x,contentEl:n.element,text:D?c.i18n.t("link_copied"):c.i18n.t("unable_to_copy_link"),themes:D?["dark","success"]:["error"]}),x)}}function p(){return!!(N()&&function(t){var e=t.includeTablets;return void 0!==e&&e?(0,j.OK)()||(0,j.gc)():(0,j.DN)()||(0,j.gc)()}({includeTablets:!1})&&navigator.canShare)}function I(t){var e=t.url,n=t.text,i=t.title,r=t.files,u={};e&&(u.url=e),n&&(u.text=n),i&&(u.title=i),(null===r||void 0===r?void 0:r.length)&&(u.files=r);try{navigator.share(u)}catch(a){console.error(a)}}},3207:function(t,e,n){"use strict";n.d(e,{C:function(){return i},z:function(){return r}});var i=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.url,n=void 0===e?"":e,i=t.canonical_path,r=void 0===i?"":i,u=t.destination_name,a=void 0===u?"":u,o=t.is_external,c=void 0!==o&&o,s=r.includes("/watch/video/")||c,M=r;return{href:!s&&n.length&&a.length?"/[...slug]":n,hrefAs:r.length?M:n,url:n}},r=function(t){if(!t)return"";var e=t;try{e=new URL(t).pathname}catch(n){}return"/"===e.charAt(0)&&(e=e.slice(1,e.length)),e.replace(/\//g,"_")}},70448:function(t,e,n){"use strict";n.d(e,{zk:function(){return o}});var r=n(3379);function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function a(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return u(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return u(t,e)}(t,i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(){return function(t){if(!t)return null;var e=decodeURIComponent(t).split("&"),n=e.findIndex((function(t){return t.match("^image=")}));if(-1!==n&&e[n+1]&&e[n+1].match("^crop=")){var i=e.splice(n+1,1);e[n]+="&"+i}return e.reduce((function(t,e){var n=a(e.split("=")),i=n[0],r=n.slice(1).join("=");return t[i]=decodeURIComponent(r),t}),{})}(r.Z.get("bf2-b_info"))}},89966:function(t){t.exports={floatingShareButton:"floating-share-button_floatingShareButton__zoj_a",inactive:"floating-share-button_inactive__IOXiB",visible:"floating-share-button_visible__4i2QN"}},91136:function(t){t.exports={actionBar:"action-bar_actionBar__3gHbY",buzzfeed:"action-bar_buzzfeed__uteSM",commentsCtaWrapper:"action-bar_commentsCtaWrapper__5y7Kb",buzzfeed_news:"action-bar_buzzfeed_news__nobM0"}},89013:function(t){t.exports={amazonAiWrapper:"styles_amazonAiWrapper__b50d_"}},90253:function(t){t.exports={comments:"CommentsWrapper_comments__blzpo",commentsHeader:"CommentsWrapper_commentsHeader__4gODy",commentsWrapper:"CommentsWrapper_commentsWrapper__Qe9Al",condensedComments:"CommentsWrapper_condensedComments__N5mnj"}},20420:function(t){t.exports={container:"commerce-disclaimer_container__F2BuI",text:"commerce-disclaimer_text__NLh_M"}},29014:function(t){t.exports={bfElementsWrapper:"elements_bfElementsWrapper__dkV4g",bfElementsWrapperQuiz:"elements_bfElementsWrapperQuiz__Hd0Ft"}},99550:function(t){t.exports={buzzfeed:"DestinationWrapper_buzzfeed__LdORv",buzzfeed_news:"DestinationWrapper_buzzfeed_news__45LO2","xs-font-serif":"DestinationWrapper_xs-font-serif__qx3dS","xs-font-sans":"DestinationWrapper_xs-font-sans__GDoxO","sm-font-serif":"DestinationWrapper_sm-font-serif__UX3D_","sm-font-sans":"DestinationWrapper_sm-font-sans__bhx2N","md-font-serif":"DestinationWrapper_md-font-serif__m7k6A","md-font-sans":"DestinationWrapper_md-font-sans__THDE3","lg-font-serif":"DestinationWrapper_lg-font-serif__4DyEm","lg-font-sans":"DestinationWrapper_lg-font-sans___BmXC","xl-font-serif":"DestinationWrapper_xl-font-serif__gi1vE","xl-font-sans":"DestinationWrapper_xl-font-sans__ZFyl_","stickyHeader-font-serif":"DestinationWrapper_stickyHeader-font-serif__tSykv","stickyHeader-font-sans":"DestinationWrapper_stickyHeader-font-sans__8KKYL"}},9100:function(t){t.exports={ctaButton:"discussionCtaButton_ctaButton__h1dS_"}},98570:function(t){t.exports={discussionImage:"discussionImage_discussionImage__Nb2QY",attribution:"discussionImage_attribution__7C1wW"}},42630:function(t){t.exports={container:"confetti_container__Fh3oR",emojis:"confetti_emojis__1pg_f",emoji:"confetti_emoji__8_nbH",verticalLayer:"confetti_verticalLayer__5gzHh",scaleLayer:"confetti_scaleLayer__q7z_w",opacityLayer:"confetti_opacityLayer__GZPeQ",animate:"confetti_animate__YW9ip",fadeInOut:"confetti_fadeInOut__0cAP8"}},83816:function(t){t.exports={reactions:"emojiReactions_reactions__vzAf7",reactionsList:"emojiReactions_reactionsList__2q7A_",restrictedReactions:"emojiReactions_restrictedReactions__EXjHM",hasReactions:"emojiReactions_hasReactions__gMbUL",reactionsItem:"emojiReactions_reactionsItem__MInzZ",reactionsItemActive:"emojiReactions_reactionsItemActive__mNlVX",count:"emojiReactions_count__NVrl3",error:"emojiReactions_error____BXx",confetti:"emojiReactions_confetti__7cYwh"}},57483:function(t){t.exports={footerCommunityBanner:"footer-community_footerCommunityBanner__h_N_r",flexContainer:"footer-community_flexContainer__VeCma",flexContainerTitle:"footer-community_flexContainerTitle__eJrN_",flexContainerImgWrapper:"footer-community_flexContainerImgWrapper__x6MZ7",flexContainerImg:"footer-community_flexContainerImg__2SIG_",buttonsContainer:"footer-community_buttonsContainer__7ET3f",loginBtn:"footer-community_loginBtn__D4WsJ",learnMoreBtn:"footer-community_learnMoreBtn__kICA1",promoLink:"footer-community_promoLink__8jMwF"}},12063:function(t){t.exports={badge:"headline-badges_badge__7Xx0q",badgeList:"headline-badges_badgeList__aEnF8",trendingBadgeContainer:"headline-badges_trendingBadgeContainer__sKi4j",trendingText:"headline-badges_trendingText__tiGpf",trendingTitle:"headline-badges_trendingTitle__i1ZTH",trendingViewCount:"headline-badges_trendingViewCount__tr4vq",trendingLink:"headline-badges_trendingLink__WARVA"}},67654:function(t){t.exports={caret:"headline-breadcrumb_caret__ShBbr",comma:"headline-breadcrumb_comma__QyLvR",midot:"headline-breadcrumb_midot__Y036H",breadcrumbItem:"headline-breadcrumb_breadcrumbItem__qbSnI",breadcrumbNewsItem:"headline-breadcrumb_breadcrumbNewsItem__X4Dgp","metadata-link":"headline-breadcrumb_metadata-link__kkpzA"}},50381:function(t){t.exports={headlineByline:"headline-byline_headlineByline__qjInt",bylineName:"headline-byline_bylineName__D9j7i",twoAuthors: <AUTHORS>
//# sourceMappingURL=542-2f96d7037a2bf128.js.map