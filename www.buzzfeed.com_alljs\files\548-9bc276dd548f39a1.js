(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[548],{27912:function(e,t,n){"use strict";n.d(t,{Z:function(){return w}});var r=n(2784),o=n(13980),i=n.n(o),c=(n(88282),"subscribeModal__1iYyf"),a="container__3OWEG",s="closeButton__34sfr",u=(n(78839),"content__3GDbx"),p="hr__1pRc4",l="button__2GfA4",d="openEnvelopeWrapper__2GJ3O",h=n(81298);function f(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e){var t=e.email,n=e.topic,o=e.title,i=void 0===o?"You have been subscribed!":o,c=e.track;return r.createElement("div",{className:u},r.createElement("h4",null,i),r.createElement("div",{className:d},r.createElement(h.$K,{width:34})),n&&r.createElement("p",null,"You will now receive updates for ",n,"."),r.createElement("svg",{className:p,height:"1",width:"100%"},r.createElement("line",{x1:"0",x2:"100%",y1:"0",y2:"0",style:{stroke:"#EDEDED",strokeWidth:2}})),r.createElement("p",{dangerouslySetInnerHTML:{__html:"<b>Set up your free account</b> to engage with the community, create your own content and get a more personalized feed."}}),r.createElement("a",{className:l,onClick:function(e){e.preventDefault(),c&&"function"===typeof c.internalLink&&c.internalLink(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){f(e,t,n[t])}))}return e}({},c.commonTrackingData)),window.location.href="/auth/csrf?provider=auth0&connection=email&login_hint=".concat(t,"&redirect=").concat(encodeURIComponent(window.location.href))},href:"/auth/csrf?provider=auth0&connection=email&login_hint=".concat(t,"&redirect=").concat(window.location.href)},"Create an account"))}m.propTypes={email:i().string,topic:i().string,track:i().object};var g=n(88163);function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var v=function(e){var t=e.trackingData,n=void 0===t?{}:t,o=e.trackImpression,i=e.options,c=void 0===i?{}:i,a=e.condition,s=void 0===a||a,u=(0,g.Z)(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){b(e,t,n[t])}))}return e}({threshold:.5,once:!0},c)),p=u.isIntersecting,l=u.setObservable;return(0,r.useEffect)((function(){"function"===typeof o&&p&&s&&o(n)}),[p]),{isIntersecting:p,setObservable:l}};function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){y(e,t,n[t])}))}return e}function w(e){var t=e.email,n=e.topic,o=void 0===n?"":n,i=e.track,u=void 0===i?{commonTrackingData:{}}:i,p=e.onClose,l=o.replace(/'/g,"").replace(/\W+/g,"_").toLowerCase(),d=v({trackingData:_({},u.commonTrackingData,{subunit_type:"component",subunit_name:"newsletter_account_signup",item_type:"button",item_name:"create_account",target_content_type:"auth",target_content_id:"sign_in"}),trackImpression:u.impression}).setObservable;(0,r.useEffect)((function(){var e=function(e){27===e.keyCode&&p()};return document.addEventListener("keydown",e),function(){document.removeEventListener("keydown",e)}}),[p]),(0,r.useEffect)((function(){var e=function(e){"modal-backdrop"===e.target.id&&p()};return document.addEventListener("mousedown",e),function(){document.removeEventListener("mousedown",e)}}),[p]);return t?r.createElement("div",{id:"modal-backdrop",className:c,ref:function(e){return d(e)}},r.createElement("div",{className:a},r.createElement("button",{className:s,onClick:function(){u&&"function"===typeof u.contentAction&&u.contentAction(_({},u.commonTrackingData,{subunit_type:"component",subunit_name:"newsletter_account_signup",item_type:"button",item_name:"close_modal",action_type:"close",action_value:"signup_modal"})),p()},"aria-label":"Close subscription confirmation"},r.createElement(h.b0,null)),r.createElement(m,{email:t,topic:o,topicName:l,title:"You\u2019re subscribed!",track:_({},u,{commonTrackingData:_({},u.commonTrackingData,{subunit_type:"component",subunit_name:"newsletter_account_signup",item_type:"button",item_name:"create_account",target_content_type:"auth",target_content_id:"sign_in"})})}))):null}w.propTypes={email:i().string.isRequired,onClose:i().func.isRequired,topic:i().string,track:i().object}},65831:function(e,t,n){"use strict";n.d(t,{P:function(){return s}});var r=n(94776),o=n.n(r),i=n(21038),c=n(59095);function a(e,t,n,r,o,i,c){try{var a=e[i](c),s=a.value}catch(u){return void n(u)}a.done?t(s):Promise.resolve(s).then(r,o)}var s=function(){var e,t=(e=o().mark((function e(t){var n,r;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=!1,r=!1,i.MS.needsConsent()){e.next=7;break}n=!0,r=!0,e.next=11;break;case 7:return e.next=9,i.jQ.hasConsented("tracking");case 9:r=e.sent,n=!0;case 11:(0,c.AZ)({email:t,isConsentReady:n,consentValue:r});case 12:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function c(e){a(i,r,o,c,s,"next",e)}function s(e){a(i,r,o,c,s,"throw",e)}c(void 0)}))});return function(e){return t.apply(this,arguments)}}()},88282:function(){},78839:function(){},77749:function(e,t,n){"use strict";n.d(t,{Z:function(){return l}});var r=n(2784),o=n(13980),i=n.n(o);function c(){return c=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},c.apply(this,arguments)}function a(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var s=function(e){var t,n;function o(){var t;return(t=e.call(this)||this).handleExpired=t.handleExpired.bind(a(t)),t.handleErrored=t.handleErrored.bind(a(t)),t.handleChange=t.handleChange.bind(a(t)),t.handleRecaptchaRef=t.handleRecaptchaRef.bind(a(t)),t}n=e,(t=o).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n;var i=o.prototype;return i.getValue=function(){return this.props.grecaptcha&&void 0!==this._widgetId?this.props.grecaptcha.getResponse(this._widgetId):null},i.getWidgetId=function(){return this.props.grecaptcha&&void 0!==this._widgetId?this._widgetId:null},i.execute=function(){var e=this.props.grecaptcha;if(e&&void 0!==this._widgetId)return e.execute(this._widgetId);this._executeRequested=!0},i.executeAsync=function(){var e=this;return new Promise((function(t,n){e.executionResolve=t,e.executionReject=n,e.execute()}))},i.reset=function(){this.props.grecaptcha&&void 0!==this._widgetId&&this.props.grecaptcha.reset(this._widgetId)},i.handleExpired=function(){this.props.onExpired?this.props.onExpired():this.handleChange(null)},i.handleErrored=function(){this.props.onErrored&&this.props.onErrored(),this.executionReject&&(this.executionReject(),delete this.executionResolve,delete this.executionReject)},i.handleChange=function(e){this.props.onChange&&this.props.onChange(e),this.executionResolve&&(this.executionResolve(e),delete this.executionReject,delete this.executionResolve)},i.explicitRender=function(){if(this.props.grecaptcha&&this.props.grecaptcha.render&&void 0===this._widgetId){var e=document.createElement("div");this._widgetId=this.props.grecaptcha.render(e,{sitekey:this.props.sitekey,callback:this.handleChange,theme:this.props.theme,type:this.props.type,tabindex:this.props.tabindex,"expired-callback":this.handleExpired,"error-callback":this.handleErrored,size:this.props.size,stoken:this.props.stoken,hl:this.props.hl,badge:this.props.badge}),this.captcha.appendChild(e)}this._executeRequested&&this.props.grecaptcha&&void 0!==this._widgetId&&(this._executeRequested=!1,this.execute())},i.componentDidMount=function(){this.explicitRender()},i.componentDidUpdate=function(){this.explicitRender()},i.componentWillUnmount=function(){void 0!==this._widgetId&&(this.delayOfCaptchaIframeRemoving(),this.reset())},i.delayOfCaptchaIframeRemoving=function(){var e=document.createElement("div");for(document.body.appendChild(e),e.style.display="none";this.captcha.firstChild;)e.appendChild(this.captcha.firstChild);setTimeout((function(){document.body.removeChild(e)}),5e3)},i.handleRecaptchaRef=function(e){this.captcha=e},i.render=function(){var e=this.props,t=(e.sitekey,e.onChange,e.theme,e.type,e.tabindex,e.onExpired,e.onErrored,e.size,e.stoken,e.grecaptcha,e.badge,e.hl,function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,["sitekey","onChange","theme","type","tabindex","onExpired","onErrored","size","stoken","grecaptcha","badge","hl"]));return r.createElement("div",c({},t,{ref:this.handleRecaptchaRef}))},o}(r.Component);s.displayName="ReCAPTCHA",s.propTypes={sitekey:i().string.isRequired,onChange:i().func,grecaptcha:i().object,theme:i().oneOf(["dark","light"]),type:i().oneOf(["image","audio"]),tabindex:i().number,onExpired:i().func,onErrored:i().func,size:i().oneOf(["compact","normal","invisible"]),stoken:i().string,hl:i().string,badge:i().oneOf(["bottomright","bottomleft","inline"])},s.defaultProps={onChange:function(){},theme:"light",type:"image",tabindex:0,size:"normal",badge:"bottomright"};var u=n(42158),p="onloadcallback";var l=(0,u.Z)((function(){return"https://"+(("undefined"!==typeof window&&window.recaptchaOptions||{}).useRecaptchaNet?"recaptcha.net":"www.google.com")+"/recaptcha/api.js?onload="+p+"&render=explicit"}),{callbackName:p,globalName:"grecaptcha"})(s)}}]);
//# sourceMappingURL=548-9bc276dd548f39a1.js.map