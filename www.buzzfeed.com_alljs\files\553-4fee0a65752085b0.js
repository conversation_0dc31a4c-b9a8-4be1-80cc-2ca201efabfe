"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[553],{79553:function(e,t,n){function o(e){return o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function r(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function a(e,t){return a=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},a(e,t)}function i(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,o=u(e);if(t){var r=u(this).constructor;n=Reflect.construct(o,arguments,r)}else n=o.apply(this,arguments);return c(this,n)}}function c(e,t){return!t||"object"!==o(t)&&"function"!==typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function u(e){return u=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},u(e)}var s=n(2784),l=n(28316),f=n(13980),p=n(63338).createFocusTrap,d=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&a(e,t)}(u,e);var t,n,o,c=i(u);function u(e){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),(t=c.call(this,e)).tailoredFocusTrapOptions={returnFocusOnDeactivate:!1},t.returnFocusOnDeactivate=!0;var n=e.focusTrapOptions;for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&("returnFocusOnDeactivate"!==o?t.tailoredFocusTrapOptions[o]=n[o]:t.returnFocusOnDeactivate=!!n[o]);return t.focusTrapElements=e.containerElements||[],t.updatePreviousElement(),t}return t=u,(n=[{key:"updatePreviousElement",value:function(){"undefined"!==typeof document&&(this.previouslyFocusedElement=document.activeElement)}},{key:"returnFocus",value:function(){this.previouslyFocusedElement&&this.previouslyFocusedElement.focus&&this.previouslyFocusedElement.focus()}},{key:"setupFocusTrap",value:function(){if(!this.focusTrap){var e=this.focusTrapElements.map(l.findDOMNode);e.some(Boolean)&&(this.focusTrap=this.props._createFocusTrap(e,this.tailoredFocusTrapOptions),this.props.active&&this.focusTrap.activate(),this.props.paused&&this.focusTrap.pause())}}},{key:"componentDidMount",value:function(){this.setupFocusTrap()}},{key:"componentDidUpdate",value:function(e){if(this.focusTrap){if(e.containerElements!==this.props.containerElements&&this.focusTrap.updateContainerElements(this.props.containerElements),e.active&&!this.props.active)return this.focusTrap.deactivate({returnFocus:!1}),void(this.returnFocusOnDeactivate&&this.returnFocus());!e.active&&this.props.active&&(this.updatePreviousElement(),this.focusTrap.activate()),e.paused&&!this.props.paused?this.focusTrap.unpause():!e.paused&&this.props.paused&&this.focusTrap.pause()}else e.containerElements!==this.props.containerElements&&(this.focusTrapElements=this.props.containerElements,this.setupFocusTrap())}},{key:"componentWillUnmount",value:function(){this.focusTrap&&this.focusTrap.deactivate({returnFocus:!1}),this.returnFocusOnDeactivate&&this.returnFocus()}},{key:"render",value:function(){var e=this,t=this.props.children?s.Children.only(this.props.children):void 0;if(t){if(t.type&&t.type===s.Fragment)throw new Error("A focus-trap cannot use a Fragment as its child container. Try replacing it with a <div> element.");return s.cloneElement(t,{ref:function(n){var o=e.props.containerElements;t&&("function"===typeof t.ref?t.ref(n):t.ref&&(t.ref.current=n)),e.focusTrapElements=o||[n]}})}return null}}])&&r(t.prototype,n),o&&r(t,o),u}(s.Component),v="undefined"===typeof Element?Function:Element;d.propTypes={active:f.bool,paused:f.bool,focusTrapOptions:f.shape({onActivate:f.func,onDeactivate:f.func,initialFocus:f.oneOfType([f.instanceOf(v),f.string,f.func]),fallbackFocus:f.oneOfType([f.instanceOf(v),f.string,f.func]),escapeDeactivates:f.bool,clickOutsideDeactivates:f.oneOfType([f.bool,f.func]),returnFocusOnDeactivate:f.bool,setReturnFocus:f.oneOfType([f.instanceOf(v),f.string,f.func]),allowOutsideClick:f.oneOfType([f.bool,f.func]),preventScroll:f.bool}),containerElements:f.arrayOf(f.instanceOf(v)),children:f.oneOfType([f.element,f.instanceOf(v)])},d.defaultProps={active:!0,paused:!1,focusTrapOptions:{},_createFocusTrap:p},e.exports=d},63338:function(e,t,n){n.r(t),n.d(t,{createFocusTrap:function(){return P}});var o=["input","select","textarea","a[href]","button","[tabindex]:not(slot)","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])',"details>summary:first-of-type","details"],r=o.join(","),a="undefined"===typeof Element,i=a?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,c=!a&&Element.prototype.getRootNode?function(e){return e.getRootNode()}:function(e){return e.ownerDocument},u=function(e,t,n){var o=Array.prototype.slice.apply(e.querySelectorAll(r));return t&&i.call(e,r)&&o.unshift(e),o=o.filter(n)},s=function e(t,n,o){for(var a=[],c=Array.from(t);c.length;){var u=c.shift();if("SLOT"===u.tagName){var s=u.assignedElements(),l=e(s.length?s:u.children,!0,o);o.flatten?a.push.apply(a,l):a.push({scope:u,candidates:l})}else{i.call(u,r)&&o.filter(u)&&(n||!t.includes(u))&&a.push(u);var f=u.shadowRoot||"function"===typeof o.getShadowRoot&&o.getShadowRoot(u),p=!o.shadowRootFilter||o.shadowRootFilter(u);if(f&&p){var d=e(!0===f?u.children:f.children,!0,o);o.flatten?a.push.apply(a,d):a.push({scope:u,candidates:d})}else c.unshift.apply(c,u.children)}}return a},l=function(e,t){return e.tabIndex<0&&(t||/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||e.isContentEditable)&&isNaN(parseInt(e.getAttribute("tabindex"),10))?0:e.tabIndex},f=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},p=function(e){return"INPUT"===e.tagName},d=function(e){return function(e){return p(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||c(e),o=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!==typeof window&&"undefined"!==typeof window.CSS&&"function"===typeof window.CSS.escape)t=o(window.CSS.escape(e.name));else try{t=o(e.name)}catch(a){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",a.message),!1}var r=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!r||r===e}(e)},v=function(e){var t=e.getBoundingClientRect(),n=t.width,o=t.height;return 0===n&&0===o},b=function(e,t){return!(t.disabled||function(e){return p(e)&&"hidden"===e.type}(t)||function(e,t){var n=t.displayCheck,o=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var r=i.call(e,"details>summary:first-of-type")?e.parentElement:e;if(i.call(r,"details:not([open]) *"))return!0;var a=c(e).host,u=(null===a||void 0===a?void 0:a.ownerDocument.contains(a))||e.ownerDocument.contains(e);if(n&&"full"!==n){if("non-zero-area"===n)return v(e)}else{if("function"===typeof o){for(var s=e;e;){var l=e.parentElement,f=c(e);if(l&&!l.shadowRoot&&!0===o(l))return v(e);e=e.assignedSlot?e.assignedSlot:l||f===e.ownerDocument?l:f.host}e=s}if(u)return!e.getClientRects().length}return!1}(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var o=t.children.item(n);if("LEGEND"===o.tagName)return!!i.call(t,"fieldset[disabled] *")||!o.contains(e)}return!0}t=t.parentElement}return!1}(t))},h=function(e,t){return!(d(t)||l(t)<0||!b(e,t))},y=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},m=function e(t){var n=[],o=[];return t.forEach((function(t,r){var a=!!t.scope,i=a?t.scope:t,c=l(i,a),u=a?e(t.candidates):i;0===c?a?n.push.apply(n,u):n.push(i):o.push({documentOrder:r,tabIndex:c,item:t,isScope:a,content:u})})),o.sort(f).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)},O=function(e,t){var n;return n=(t=t||{}).getShadowRoot?s([e],t.includeContainer,{filter:h.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:y}):u(e,t.includeContainer,h.bind(null,t)),m(n)},g=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==i.call(e,r)&&h(t,e)},E=o.concat("iframe").join(","),w=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==i.call(e,E)&&b(t,e)};function T(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function F(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?T(Object(n),!0).forEach((function(t){S(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):T(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var D=function(){var e=[];return{activateTrap:function(t){if(e.length>0){var n=e[e.length-1];n!==t&&n.pause()}var o=e.indexOf(t);-1===o||e.splice(o,1),e.push(t)},deactivateTrap:function(t){var n=e.indexOf(t);-1!==n&&e.splice(n,1),e.length>0&&e[e.length-1].unpause()}}}(),N=function(e){return setTimeout(e,0)},k=function(e,t){var n=-1;return e.every((function(e,o){return!t(e)||(n=o,!1)})),n},R=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return"function"===typeof e?e.apply(void 0,n):e},C=function(e){return e.target.shadowRoot&&"function"===typeof e.composedPath?e.composedPath()[0]:e.target},P=function(e,t){var n,o=(null===t||void 0===t?void 0:t.document)||document,r=F({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0},t),a={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0},i=function(e,t,n){return e&&void 0!==e[t]?e[t]:r[n||t]},c=function(e){return a.containerGroups.findIndex((function(t){var n=t.container,o=t.tabbableNodes;return n.contains(e)||o.find((function(t){return t===e}))}))},l=function(e){var t=r[e];if("function"===typeof t){for(var n=arguments.length,a=new Array(n>1?n-1:0),i=1;i<n;i++)a[i-1]=arguments[i];t=t.apply(void 0,a)}if(!0===t&&(t=void 0),!t){if(void 0===t||!1===t)return t;throw new Error("`".concat(e,"` was specified but was not a node, or did not return a node"))}var c=t;if("string"===typeof t&&!(c=o.querySelector(t)))throw new Error("`".concat(e,"` as selector refers to no known node"));return c},f=function(){var e=l("initialFocus");if(!1===e)return!1;if(void 0===e)if(c(o.activeElement)>=0)e=o.activeElement;else{var t=a.tabbableGroups[0];e=t&&t.firstTabbableNode||l("fallbackFocus")}if(!e)throw new Error("Your focus-trap needs to have at least one focusable element");return e},p=function(){if(a.containerGroups=a.containers.map((function(e){var t,n,o=O(e,r.tabbableOptions),a=(t=e,(n=(n=r.tabbableOptions)||{}).getShadowRoot?s([t],n.includeContainer,{filter:b.bind(null,n),flatten:!0,getShadowRoot:n.getShadowRoot}):u(t,n.includeContainer,b.bind(null,n)));return{container:e,tabbableNodes:o,focusableNodes:a,firstTabbableNode:o.length>0?o[0]:null,lastTabbableNode:o.length>0?o[o.length-1]:null,nextTabbableNode:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=a.findIndex((function(t){return t===e}));if(!(n<0))return t?a.slice(n+1).find((function(e){return g(e,r.tabbableOptions)})):a.slice(0,n).reverse().find((function(e){return g(e,r.tabbableOptions)}))}}})),a.tabbableGroups=a.containerGroups.filter((function(e){return e.tabbableNodes.length>0})),a.tabbableGroups.length<=0&&!l("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times")},d=function e(t){!1!==t&&t!==o.activeElement&&(t&&t.focus?(t.focus({preventScroll:!!r.preventScroll}),a.mostRecentlyFocusedNode=t,function(e){return e.tagName&&"input"===e.tagName.toLowerCase()&&"function"===typeof e.select}(t)&&t.select()):e(f()))},v=function(e){var t=l("setReturnFocus",e);return t||!1!==t&&e},h=function(e){var t=C(e);c(t)>=0||(R(r.clickOutsideDeactivates,e)?n.deactivate({returnFocus:r.returnFocusOnDeactivate&&!w(t,r.tabbableOptions)}):R(r.allowOutsideClick,e)||e.preventDefault())},y=function(e){var t=C(e),n=c(t)>=0;n||t instanceof Document?n&&(a.mostRecentlyFocusedNode=t):(e.stopImmediatePropagation(),d(a.mostRecentlyFocusedNode||f()))},m=function(e){if(function(e){return"Escape"===e.key||"Esc"===e.key||27===e.keyCode}(e)&&!1!==R(r.escapeDeactivates,e))return e.preventDefault(),void n.deactivate();(function(e){return"Tab"===e.key||9===e.keyCode})(e)&&function(e){var t=C(e);p();var n=null;if(a.tabbableGroups.length>0){var o=c(t),i=o>=0?a.containerGroups[o]:void 0;if(o<0)n=e.shiftKey?a.tabbableGroups[a.tabbableGroups.length-1].lastTabbableNode:a.tabbableGroups[0].firstTabbableNode;else if(e.shiftKey){var u=k(a.tabbableGroups,(function(e){var n=e.firstTabbableNode;return t===n}));if(u<0&&(i.container===t||w(t,r.tabbableOptions)&&!g(t,r.tabbableOptions)&&!i.nextTabbableNode(t,!1))&&(u=o),u>=0){var s=0===u?a.tabbableGroups.length-1:u-1;n=a.tabbableGroups[s].lastTabbableNode}}else{var f=k(a.tabbableGroups,(function(e){var n=e.lastTabbableNode;return t===n}));if(f<0&&(i.container===t||w(t,r.tabbableOptions)&&!g(t,r.tabbableOptions)&&!i.nextTabbableNode(t))&&(f=o),f>=0){var v=f===a.tabbableGroups.length-1?0:f+1;n=a.tabbableGroups[v].firstTabbableNode}}}else n=l("fallbackFocus");n&&(e.preventDefault(),d(n))}(e)},E=function(e){var t=C(e);c(t)>=0||R(r.clickOutsideDeactivates,e)||R(r.allowOutsideClick,e)||(e.preventDefault(),e.stopImmediatePropagation())},T=function(){if(a.active)return D.activateTrap(n),a.delayInitialFocusTimer=r.delayInitialFocus?N((function(){d(f())})):d(f()),o.addEventListener("focusin",y,!0),o.addEventListener("mousedown",h,{capture:!0,passive:!1}),o.addEventListener("touchstart",h,{capture:!0,passive:!1}),o.addEventListener("click",E,{capture:!0,passive:!1}),o.addEventListener("keydown",m,{capture:!0,passive:!1}),n},S=function(){if(a.active)return o.removeEventListener("focusin",y,!0),o.removeEventListener("mousedown",h,!0),o.removeEventListener("touchstart",h,!0),o.removeEventListener("click",E,!0),o.removeEventListener("keydown",m,!0),n};return(n={get active(){return a.active},get paused(){return a.paused},activate:function(e){if(a.active)return this;var t=i(e,"onActivate"),n=i(e,"onPostActivate"),r=i(e,"checkCanFocusTrap");r||p(),a.active=!0,a.paused=!1,a.nodeFocusedBeforeActivation=o.activeElement,t&&t();var c=function(){r&&p(),T(),n&&n()};return r?(r(a.containers.concat()).then(c,c),this):(c(),this)},deactivate:function(e){if(!a.active)return this;var t=F({onDeactivate:r.onDeactivate,onPostDeactivate:r.onPostDeactivate,checkCanReturnFocus:r.checkCanReturnFocus},e);clearTimeout(a.delayInitialFocusTimer),a.delayInitialFocusTimer=void 0,S(),a.active=!1,a.paused=!1,D.deactivateTrap(n);var o=i(t,"onDeactivate"),c=i(t,"onPostDeactivate"),u=i(t,"checkCanReturnFocus"),s=i(t,"returnFocus","returnFocusOnDeactivate");o&&o();var l=function(){N((function(){s&&d(v(a.nodeFocusedBeforeActivation)),c&&c()}))};return s&&u?(u(v(a.nodeFocusedBeforeActivation)).then(l,l),this):(l(),this)},pause:function(){return a.paused||!a.active||(a.paused=!0,S()),this},unpause:function(){return a.paused&&a.active?(a.paused=!1,p(),T(),this):this},updateContainerElements:function(e){var t=[].concat(e).filter(Boolean);return a.containers=t.map((function(e){return"string"===typeof e?o.querySelector(e):e})),a.active&&p(),this}}).updateContainerElements(e),n}}}]);
//# sourceMappingURL=553-4fee0a65752085b0.js.map