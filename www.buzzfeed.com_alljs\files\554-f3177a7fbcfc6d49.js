(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[554],{68758:function(e,t,n){"use strict";n.d(t,{Z:function(){return g}});var r=n(52322),i=n(2784),a=n(13980),o=n.n(a);function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function u(e){return function(e){if(Array.isArray(e))return s(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var c=[n(21038).Yx].concat(u([]),u([])),l=n(30353),f=n(45201),d=n(27625),_=n(74967),m=n(26002);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){p(e,t,n[t])}))}return e}function b(e){var t=e.children,n=e.pageProps,a=void 0===n?{}:n,o=(0,m.I)({cluster:l.ov}),s=(0,i.useMemo)((function(){return{userId:o,data:{}}}),[o]),u=(0,f.Z)(h({abeagleHost:l.b7,experimentConfig:c,source:"buzz_web"},s)),p=(0,i.useRef)(null),b=a.pagePath;p.current&&p.current.loaded===u.loaded||(p.current=h({},u,{pagePath:b})),p.current.stale=p.current.pagePath!==b;var g=p.current.loaded,v=u.eligible?Object.keys(u.eligible).join("|"):"";(0,i.useEffect)((function(){if(g&&v.length){var e=[];Object.keys(u.eligible).forEach((function(t){var n=u.eligible[t];n&&n.value&&e.push([t,n.id,n.version,n.value,n.variant_id].join("|"))}))}}),[g,v]);return(0,r.jsx)(d.Z.Provider,{value:{experiments:u,getFeatureFlagValue:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"on";return(0,_.F7)(u,e,t)},getExperimentValue:function(e,t){return(0,_.ts)(u,e,t)}},children:t})}b.propTypes={children:o().oneOfType([o().arrayOf(o().node),o().node])};var g=b},29199:function(e,t,n){"use strict";var r=n(52322),i=n(12772),a=n.n(i),o=n(36469),s=n(86);function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){u(e,t,n[t])}))}return e}t.Z=function(e){var t=e.brand,n=e.children,i=e.components,u=e.featuredNewsletter,l=e.hasSiteHeader,f=void 0===l||l,d=(i||{}).header,_=void 0===d?{}:d;return"buzzfeed"===t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(s.default,{featuredNewsletter:u}),f&&(0,r.jsx)(o.Z,c({},_)),(0,r.jsx)("main",{className:"".concat(a().layout," ").concat(a()[t]),children:n})]}):(0,r.jsxs)("main",{className:a().layout,children:[(0,r.jsx)(s.default,{featuredNewsletter:u}),f&&(0,r.jsx)(o.Z,c({},_)),n]})}},19414:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});var r=n(2784),i=n(21038),a=n(74967),o=n(27625);function s(){var e=(0,r.useContext)(o.Z).experiments;return(0,r.useEffect)((function(){if(e.loaded&&!e.stale){var t=(0,a.F7)(e,i.Yx.name);i.hi.configure({useFallback:!t})}}),[e.loaded,e.stale]),(0,r.useEffect)((function(){i.hi.init()}),[]),null}},56273:function(e,t,n){"use strict";var r=n(52322),i=n(2784),a=n(3176),o=n(30353),s=n(5103),u=n(83589),c=n(58019),l=n.n(c),f=n(52390),d=(0,i.forwardRef)((function(e,t){var n=e.brand,i=e.edition,c=e.handleSubmit,d=e.onEmailInputChange,_=e.disableSubscribe,m=e.showEmailError,p=e.emailInput,h=e.userEmailState,b=e.type,g=(0,s.useTranslation)("common").t,v="individual"===b&&"buzzfeed"===n,x={buzzfeed:{title:"",description:"",termsLink:"https://www.buzzfeed.com/about/useragreement?country="+i,privacyLink:"https://www.buzzfeed.com/about/privacy?country="+i}},y=function(){return(0,r.jsxs)("div",{className:"".concat(l().formSmallprint," ").concat(l()[n]),children:[g("disclaimer_text")," You are also agreeing to our"," ",(0,r.jsx)("a",{className:l().signupLearnMore,target:"_blank",href:x[n].termsLink,rel:"noreferrer",children:"Terms of Service"})," ","and"," ",(0,r.jsx)("a",{className:l().signupLearnMore,target:"_blank",href:x[n].privacyLink,rel:"noreferrer",children:"Privacy Policy."})]})};return(0,r.jsxs)("div",{className:l().formContainer,children:["manage"!==b?(0,r.jsx)(r.Fragment,{children:!v&&y()}):(0,r.jsx)("div",{className:l().formTitleManageWrapper,children:(0,r.jsx)("p",{className:l().titleManage,children:"Enter your email address and we\u2019ll send you an email with a link to manage your newsletter subscriptions"})}),(0,r.jsxs)("form",{className:"".concat(l().formWrapper," ").concat(m&&l().formErrorWrapper),onSubmit:c,name:"form",children:[(0,r.jsx)(a.Z,{ref:t,size:"invisible",badge:"bottomright",hl:(0,u.detectRecaptchaCode)(i),sitekey:o.A3}),(0,r.jsxs)("div",{className:"manage"===b?l().emailWrapperManage:l().emailWrapper,children:[v&&(0,r.jsx)("label",{children:g("label_email")}),(0,r.jsx)("div",{className:"".concat(l().inputContainer," ").concat(l()[n]),children:(0,r.jsx)("input",{value:p,"data-testid":"email-input",placeholder:g("sample_email"),onChange:d,className:"".concat(l().signupInput," ").concat(l()[n]),"aria-invalid":m,"aria-describedby":"emailError",disabled:h})})]}),(0,r.jsx)("button",{type:"submit",className:"".concat(l().subscribeButton," ").concat(l()[n]),disabled:_,"aria-disabled":_,children:g("subscribe_cta")}),v&&y()]}),m&&(0,r.jsx)(f.Z,{notificationText:{title:"",description:g("error_invalid_email"),error:!0},brand:n,page:b})]})}));d.displayName="Form",t.Z=d},15596:function(e,t,n){"use strict";var r=n(52322),i=n(95486),a=n(79915),o=n.n(a),s=n(52390);t.Z=function(e){var t=e.title,n=e.notificationText,a=e.onCloseNotification,u=e.children,c=e.edition,l=e.type,f=e.brand;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"".concat(o().headerWrapper," ").concat(o()[f]," ").concat(o()[l]),children:[n&&(0,r.jsx)(s.Z,{notificationText:n,onCloseHandle:a,brand:f,page:l}),"individual"!==l&&(0,r.jsx)("h1",{className:o().headerTitle,children:t})]}),(0,r.jsx)("div",{className:"".concat(o().headerContainer," ").concat(o()[f]," ").concat(o()[l]),children:(0,r.jsx)("div",{className:"".concat(o()[f]," ").concat(o().header," ").concat(o()[l]),children:(0,r.jsxs)("div",{className:"".concat(o().headerInputContainer," ").concat(o()[l]),children:[u,!("individual"===l||"webview"===l||"manage"===l||"authUser"===l)&&"en-us"===c&&(0,r.jsx)(i.Z,{brand:f})]})})})]})}},95486:function(e,t,n){"use strict";n.d(t,{Z:function(){return y}});var r=n(52322),i=n(2784),a=n(62646);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){o(e,t,n[t])}))}return e}var u=n(4298),c=n(12524),l=n.n(c),f=n(32671),d=n.n(f);function _(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var m=(0,i.forwardRef)((function(e,t){var n,i=e.type,a=e.hrefValue,o=l()((_(n={},d().manageSubsContainer,!0),_(n,d().individualManageSubsContainer,"individual"===i),n));return(0,r.jsx)("div",{className:o,children:(0,r.jsxs)("p",{className:d().manageSubsText,children:["Need to manage your current newsletter subscriptions? ",(0,r.jsx)("a",{ref:t,className:d().manageSubsLinkHuffpost,href:a,children:"Manage Subscriptions"})]})})}));m.displayName="HuffpostManageText";var p=m,h=n(63161),b=n.n(h),g=n(30353);function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var x=(0,i.forwardRef)((function(e,t){var n,i=e.type,a=e.hrefValue,o=e.brand,s=l()((v(n={},b().individualManageSubsContainer,"individual"===i),v(n,b().manageSubsContainer,!0),v(n,b()[o],!0),n));return(0,r.jsx)("div",{className:s,children:(0,r.jsxs)("p",{className:b().manageSubsText,children:["Need to manage your current Subscriptions? ",(0,r.jsx)("a",{ref:t,className:b().manageSubsLink,href:a,children:"Manage Subscriptions"})]})})})),y=function(e){var t,n=e.type,o=e.brand,c=e.edition,l=function(e){var t=e.mode;return(0,i.useMemo)((function(){return{unit_type:"feed",unit_name:"main",item_type:"text",item_name:"manage_subscriptions",target_content_type:"feed",target_content_id:"newsletters_manage",position_in_unit:1,mode:t}}),[t])}({mode:(0,u.y)()}),f=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=(0,i.useRef)(null);return(0,i.useEffect)((function(){var i=s({},e),o=r.current;if(!o)return function(){};var u=n?(0,a.y1)(o,t,i):function(){};return function(){u()}}),[e,t,n]),{ref:r}}(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){v(e,t,n[t])}))}return e}({},l),{type:"feed",id:"newsletters",brand:o},!0),d=f.ref,_=c?c.split("-")[1]:null;return t=g.hv.includes(_)&&"buzzfeed"===o?"/".concat(_,"/newsletters/manage?subhub=true"):"/newsletters/manage?subhub=true",(0,r.jsx)(r.Fragment,{children:"huffpost"===o?(0,r.jsx)(p,{type:n,hrefValue:t,ref:d}):(0,r.jsx)(x,{type:n,hrefValue:t,brand:o,ref:d})})}},52390:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(52322),i=(n(2784),n(53625)),a=function(e){var t=e.className,n=e.width,i=void 0===n?16:n,a=e.height,o=void 0===a?16:a;return(0,r.jsx)("svg",{"aria-label":"success",className:t,width:i,height:o,viewBox:"0 0 16 16",fill:"none",role:"img",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8 1.75C4.54822 1.75 1.75 4.54822 1.75 8C1.75 11.4518 4.54822 14.25 8 14.25C11.4518 14.25 14.25 11.4518 14.25 8C14.25 4.54822 11.4518 1.75 8 1.75ZM0.5 8C0.5 3.85786 3.85786 0.5 8 0.5C12.1421 0.5 15.5 3.85786 15.5 8C15.5 12.1421 12.1421 15.5 8 15.5C3.85786 15.5 0.5 12.1421 0.5 8ZM11.1836 4.63677C11.4722 4.82616 11.5526 5.21363 11.3632 5.50221L7.50881 11.3756C7.39845 11.5438 7.21388 11.6486 7.0129 11.6571C6.81193 11.6657 6.61911 11.577 6.49483 11.4188L4.47585 8.84923C4.26259 8.57781 4.30974 8.1849 4.58116 7.97165C4.85258 7.75839 5.24549 7.80554 5.45875 8.07696L6.94068 9.96305L10.3182 4.81639C10.5076 4.52781 10.895 4.44739 11.1836 4.63677Z"})})},o=function(e){var t=e.className,n=e.width,i=void 0===n?16:n,a=e.height,o=void 0===a?16:a;return(0,r.jsxs)("svg",{"aria-label":"error",className:t,width:i,height:o,viewBox:"0 0 24 24",fill:"none",role:"img",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsxs)("g",{clipPath:"url(#clip0_957_1723)",children:[(0,r.jsx)("path",{d:"M22 12C22 17.5228 17.5228 22 12 22V24C18.6274 24 24 18.6274 24 12H22ZM12 22C6.47715 22 2 17.5228 2 12H0C0 18.6274 5.37258 24 12 24V22ZM2 12C2 6.47715 6.47715 2 12 2V0C5.37258 0 0 5.37258 0 12H2ZM12 2C17.5228 2 22 6.47715 22 12H24C24 5.37258 18.6274 0 12 0V2Z"}),(0,r.jsx)("path",{d:"M10.9729 6.56737C10.9505 5.98463 11.4169 5.5 12.0001 5.5C12.5832 5.5 13.0496 5.98463 13.0272 6.56737L12.7395 14.0458C12.7242 14.4432 12.3977 14.7574 12.0001 14.7574C11.6024 14.7574 11.2759 14.4432 11.2606 14.0458L10.9729 6.56737Z"}),(0,r.jsx)("path",{d:"M10.7997 17.5636C10.7997 16.9007 11.3371 16.3633 12 16.3633C12.6629 16.3633 13.2003 16.9007 13.2003 17.5636C13.2003 18.2265 12.6629 18.7639 12 18.7639C11.3371 18.7639 10.7997 18.2265 10.7997 17.5636Z"})]}),(0,r.jsx)("defs",{children:(0,r.jsx)("clipPath",{id:"clip0_957_1723",children:(0,r.jsx)("rect",{width:"24",height:"24",fill:"white"})})})]})},s=n(47452),u=n.n(s),c=function(e){var t=e.notificationText,n=e.onCloseHandle,s=void 0===n?null:n,c=e.brand,l=e.page,f=void 0===l?"list":l,d=t.title,_=t.description,m=t.error,p="huffpost"===c,h=m?"error":"success",b="function"===typeof s;return(0,r.jsx)("div",{"aria-live":"assertive",className:"".concat(u().container," ").concat(u()[c]),children:(0,r.jsxs)("div",{className:"".concat(u().notification," ").concat(u()[c]," ").concat(u()[h]," ").concat(u()[f]),children:[(0,r.jsxs)("div",{className:u().wrapper,children:[m?(0,r.jsx)("div",{className:u().iconWrapper,children:(0,r.jsx)(o,{"data-testid":"errorIcon",className:u().svgIcon,width:16,height:16})}):(0,r.jsx)("div",{className:p?u().iconWrapper:void 0,children:(0,r.jsx)(a,{className:u().svgIcon,width:16,height:16})}),(0,r.jsxs)("div",{className:u().textContainer,children:[(0,r.jsx)("p",{className:u().notificationTitle,children:d}),!!_.length&&(0,r.jsx)("p",{className:u().notificationMessage,children:_})]})]}),b&&(0,r.jsx)("span",{className:u().closeButtonWrapper,children:(0,r.jsx)("button",{"aria-label":"Dismiss message",onClick:s,className:u().closeButton,children:(0,r.jsx)(i.X,{className:u().svgIcon})})})]})})}},90:function(e,t,n){"use strict";n.d(t,{Z:function(){return y}});var r=n(52322),i=n(2784),a=n(3176),o=n(5103),s=n(30353),u=n(83589),c=n(64673),l=n.n(c),f=n(12524),d=n.n(f),_=n(13738),m=n.n(_);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var h=function(e,t){var n;return d()((p(n={},m().huffpostSignupInput,"huffpost"===t),p(n,m().signupInputError,e&&"huffpost"===t),n))},b=function(e,t){var n;return d()((p(n={},m().huffpostEmailErrorLabel,"huffpost"===t),p(n,m().active,e),n))},g=function(e,t){var n;return d()((p(n={},m().huffpostInputContainer,"huffpost"===t),p(n,m()["mt-1"],e&&"huffpost"===t),n))},v=function(e){var t=e.edition,n=e.t,i=e.type;return"en-us"!==t&&"en-gb"!==t?(0,r.jsxs)("div",{className:l().signupSmallprint,children:[n("disclaimer_text"),"webview"!==i&&(0,r.jsx)("a",{className:l().signupLearnMore,target:"_blank",href:n("hp_disclaimer_link"),rel:"noreferrer",children:n("disclaimer_cta")})]}):(0,r.jsxs)("div",{className:l().usSignupSmallprint,children:[n("disclaimer_text")," ","webview"!==i&&(0,r.jsxs)(r.Fragment,{children:["You are also agreeing to our"," ",(0,r.jsx)("a",{className:l().signupLearnMore,target:"_blank",href:n("hp_disclaimer_link"),rel:"noreferrer",children:"Terms of Service"})," ","and"," ",(0,r.jsx)("a",{className:l().signupLearnMore,target:"_blank",href:n("hp_disclaimer_second_link"),rel:"noreferrer",children:"Privacy Policy."})]})]})},x=(0,i.forwardRef)((function(e,t){var n=e.edition,i=e.handleSubmit,c=e.onEmailInputChange,f=e.disableSubscribe,d=e.showEmailError,_=e.emailInput,m=e.type,p=e.userEmailState,x=(0,o.useTranslation)("common").t;return(0,r.jsxs)(r.Fragment,{children:["manage"!==m&&(0,r.jsx)(v,{edition:n,t:x,type:m}),(0,r.jsxs)("form",{className:g(d,"huffpost"),onSubmit:i,name:"form",children:[(0,r.jsx)(a.Z,{ref:t,size:"invisible",badge:"bottomright",hl:(0,u.detectRecaptchaCode)(n),sitekey:s.A3}),(0,r.jsxs)("div",{className:l().emailWrapper,children:[(0,r.jsx)("div",{className:b(d,"huffpost"),children:x("error_invalid_email")}),(0,r.jsx)("input",{value:_,"data-testid":"email-input",placeholder:x("sample_email"),onChange:c,className:h(d,"huffpost"),"aria-label":"email input field",disabled:p})]}),(0,r.jsx)("button",{type:"submit",className:l().subscribeButton,disabled:f,"aria-disabled":f,children:x("subscribe_cta")})]})]})}));x.displayName="HuffpostForm";var y=x},42008:function(e,t,n){"use strict";var r=n(52322),i=(n(2784),n(82568)),a=n.n(i),o=n(52390),s=n(95486);t.Z=function(e){var t=e.title,n=e.edition,i=e.subtitle,u=e.hideSubOnMobile,c=e.notificationText,l=e.onCloseNotification,f=e.children,d=e.type,_=e.brand;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:function(){switch(d){case"webview":return a().webviewSignupHeader;case"manage":return a().manageSignupHeader;case"individual":return a().individualSignupHeader;default:return a().signupHeader}}(),children:[c&&(0,r.jsx)(o.Z,{notificationText:c,onCloseHandle:l,brand:_}),(0,r.jsx)("div",{className:"".concat(a().signupTitle," ").concat(u&&a().marginBottom),children:t}),(0,r.jsx)("h2",{className:"".concat(a().signupSubtitle," ").concat(u&&a().hideOnMobile),children:i}),f]}),!("webview"===d||"manage"===d)&&"en-us"===n&&(0,r.jsx)(s.Z,{brand:"huffpost"}),"individual"===d&&(0,r.jsx)("h3",{className:a().individualSubtitle,children:"Check out some of our other newsletters"})]})}},50214:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(52322),react__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(2784),_pages_head__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(86),_huffpost_layout_module_scss__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(11103),_huffpost_layout_module_scss__WEBPACK_IMPORTED_MODULE_3___default=__webpack_require__.n(_huffpost_layout_module_scss__WEBPACK_IMPORTED_MODULE_3__),RenderHtml=function(e){var t=e.html;return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{dangerouslySetInnerHTML:{__html:t}})},runJS=function(){var components=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return components.filter((function(e){return e.js})).map((function(param){var js=param.js;return eval(js)}))},getCSS=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment,{children:e.filter((function(e){return e.css})).map((function(e,t){var n=e.css;return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("style",{type:"text/css",children:n},t)}))})},HuffpostLayout=function(e){var t=e.components,n=e.children,r=e.featuredNewsletter,i=void 0===r?{}:r,a=t||{},o=a.header,s=void 0===o?{}:o,u=a.leftnav,c=void 0===u?{}:u,l=a.footer,f=void 0===l?{}:l;return(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)((function(){runJS([s,c,f])})),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment,{children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_pages_head__WEBPACK_IMPORTED_MODULE_2__.default,{featuredNewsletter:i,children:getCSS([s,c,f])}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("main",{className:_huffpost_layout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().main,children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:_huffpost_layout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().header,children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(RenderHtml,{html:null===s||void 0===s?void 0:s.html}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(RenderHtml,{html:null===c||void 0===c?void 0:c.html})]}),n,(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(RenderHtml,{html:null===f||void 0===f?void 0:f.html})]})]})};__webpack_exports__.Z=HuffpostLayout},4298:function(e,t,n){"use strict";n.d(t,{y:function(){return i}});var r=n(2784),i=function(){var e=(0,r.useState)(!1),t=e[0],n=e[1];return(0,r.useEffect)((function(){n(window.matchMedia("(max-width: 640px)").matches)}),[]),t?"mobile":"desktop"}},73178:function(e,t,n){"use strict";n.d(t,{L:function(){return a}});var r=n(2784),i=n(30353),a=function(e,t){var n=(0,r.useState)(!1),a=n[0],o=n[1],s={huffpost:i.DJ,buzzfeed:i.qD}[t];switch((0,r.useEffect)((function(){o(window.matchMedia("(max-width: 640px)").matches)}),[]),e){case"individual":return s.signup;case"webview":return"buzzfeed"===t?i.qD.webview:i.DJ.webview;default:return a?s.mobile:s.desktop}}},86:function(e,t,n){"use strict";n.r(t);var r=n(52322),i=n(97729),a=n(5632),o=n(49215),s=n(5103),u=n(43997),c=n(67644),l=n.n(c),f=n(42483),d=n.n(f),_=n(54700),m=n.n(_),p=n(58914),h=n.n(p),b=n(33983),g=n(65667);t.default=function(e){var t=e.children,n=e.featuredNewsletter,c=void 0===n?{}:n,f=(0,s.useTranslation)("common").t,_=(0,a.useRouter)().asPath,p=(0,u.v9)(b.Or),v=(0,u.v9)(b.cM),x=(0,u.v9)(b.gI),y={huffpost:{favicon:l(),ogImage:d(),title:f("hp_hub_title"),description:f("hub_description"),siteName:f("en-gb"===v?"hp_uk_site_name":"hp_site_name"),canonicalUrl:"".concat(x).concat(_)},buzzfeed:{favicon:m(),ogImage:h(),title:f("bf_hub_title"),description:"BuzzFeed has breaking news, vital journalism, quizzes, videos, celeb news, Tasty food videos, recipes, DIY hacks, and all the trending buzz you\u2019ll want to share with your friends. Copyright BuzzFeed, Inc. All rights reserved.",siteName:f("en-uk"===v?"bf_uk_site_name":"bf_site_name"),canonicalUrl:"https://www.buzzfeed.com/newsletters"}},w=y[p],j=w.favicon,C=w.siteName,N=y[p].canonicalUrl+(c.slug?"/".concat(c.slug):""),M=y[p].title+(c.title?": ".concat(c.title):""),E=c.description||y[p].description,A=(0,g.F)(c.image,{fit:"1200:630"})||y[p].ogImage;return(0,r.jsxs)(i.default,{children:[(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, minimum-scale=1"},"viewport"),(0,r.jsx)("meta",{charSet:"utf-8"},"charset"),(0,r.jsx)("title",{children:M}),(0,r.jsx)("meta",{name:"title",content:M},"title"),(0,r.jsx)("meta",{name:"description",content:E},"description"),(0,r.jsx)("link",{rel:"icon",href:j}),(0,r.jsx)("link",{rel:"canonical",href:N},"canonical"),(0,r.jsx)("meta",{property:"og:title",content:M},"og:title"),(0,r.jsx)("meta",{property:"og:description",content:E},"og:description"),(0,r.jsx)("meta",{property:"og:url",content:N},"og:url"),(0,r.jsx)("meta",{property:"og:site_name",content:C},"og:site_name"),(0,r.jsx)("meta",{property:"og:image",content:A},"og:image"),(0,r.jsx)("meta",{property:"og:image:alt",content:"".concat(C," logo")},"og:image:alt"),(0,r.jsx)("meta",{property:"og:type",content:"website"},"og:type"),"buzzfeed"===p&&(0,r.jsx)("script",{dangerouslySetInnerHTML:{__html:"\n    window.BZFD = {\n      Config: {\n        bfwInfoCookie: 'bf2-b_info'\n      }\n    };\n  "}},"window-globals"),(0,r.jsx)("script",{dangerouslySetInnerHTML:{__html:(0,o.oO)()}},"js-profiling"),t]})}},33983:function(e,t,n){"use strict";n.d(t,{Or:function(){return r},cM:function(){return i},gI:function(){return a}});var r=function(e){return e.page.brand},i=function(e){return e.page.edition},a=function(e){return e.page.host}},86596:function(e,t,n){"use strict";n.d(t,{R:function(){return r}});var r=function(e){return e.userSubscriptions}},83589:function(e){"use strict";e.exports={detectRecaptchaCode:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";switch(e){case"es-es":return"es";case"jp-ja":return"ja";case"en-gb":return"en-GB";case"gr-gr":return"el";default:return"en"}}}},65667:function(e,t,n){"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,s=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(u){s=!0,i=u}finally{try{o||null==n.return||n.return()}finally{if(s)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return r(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}n.d(t,{F:function(){return a}});var a=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{var n=new URL(e),r=["img.buzzfeed.com","img-stage.buzzfeed.com"];return r.includes(n.host)?(n.searchParams.set("output-format","auto"),n.searchParams.set("output-quality","auto"),Object.entries(t).forEach((function(e){var t=i(e,2),r=t[0],a=t[1];n.searchParams.set(r,a)})),n.href):e}catch(a){return console.error("Error processing image: ".concat(e)),e}}},50425:function(e,t,n){"use strict";n.d(t,{v:function(){return i}});var r=/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/i,i=function(e){return r.test(e)}},68590:function(e,t,n){"use strict";n.d(t,{ou:function(){return r},A:function(){return i},lh:function(){return a},uy:function(){return o},wv:function(){return s}});var r=function(){return{title:"We sent an email to the email address provided.",description:"",error:!1}},i=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return{title:e,description:"",error:!0}},a=function(e,t){return{title:e,description:t,error:!1}},o=function(){return{title:"You have unsubscribed from all newsletters.",description:"",error:!1}},s=function(){return{title:"Changes saved.",description:"",error:!1}}},12772:function(e){e.exports={layout:"brand-layout_layout__umqpg",buzzfeed:"brand-layout_buzzfeed__tHpZe"}},58019:function(e){e.exports={formWrapper:"form_formWrapper__SHzh0",emailWrapper:"form_emailWrapper__Gwh3I",emailWrapperManage:"form_emailWrapperManage__cU5qW",formSmallprint:"form_formSmallprint__q7Fn9",buzzfeed:"form_buzzfeed__e4jQ4",signupLearnMore:"form_signupLearnMore__8WJR0",formContainer:"form_formContainer__7A3PJ",formTitle:"form_formTitle__r9fit",formDesc:"form_formDesc__ElPGC",formTitleManageWrapper:"form_formTitleManageWrapper__1xwsS",titleManage:"form_titleManage__ebLrO",subscribeButton:"form_subscribeButton__D_dcH",inputContainer:"form_inputContainer___BPTh",signupInput:"form_signupInput__dqGOR",emailErrorLabel:"form_emailErrorLabel__7Xqhh"}},79915:function(e){e.exports={headerWrapper:"header_headerWrapper__xk9VQ",buzzfeed:"header_buzzfeed__ai2e_",individual:"header_individual__ZrqEu",header:"header_header__D3viy",headerInputContainer:"header_headerInputContainer__esufi",headerContainer:"header_headerContainer___wona",headerTitle:"header_headerTitle__ET3HE",individualSubtitle:"header_individualSubtitle__L22Gn"}},63161:function(e){e.exports={individualManageSubsContainer:"manage-text_individualManageSubsContainer__p_x4_",manageSubsContainer:"manage-text_manageSubsContainer__HDt7U",buzzfeed:"manage-text_buzzfeed__XyIQC",manageSubsText:"manage-text_manageSubsText__EbCEP",manageSubsLink:"manage-text_manageSubsLink__sCpYh"}},47452:function(e){e.exports={container:"notification_container__sSe8R",huffpost:"notification_huffpost__Cc2wx",notification:"notification_notification__QKqIc","fade-in":"notification_fade-in__lmvZR",individual:"notification_individual__B_g6E",notificationTitle:"notification_notificationTitle__Yb_nN",notificationMessage:"notification_notificationMessage__wIS8h",textContainer:"notification_textContainer__M_Isd",buzzfeed:"notification_buzzfeed__buYvF",success:"notification_success__CgX3J",svgIcon:"notification_svgIcon__2g6p7",error:"notification_error__Dy9N_",wrapper:"notification_wrapper__6vRbb",iconWrapper:"notification_iconWrapper__53hSj",huffpostContentContainer:"notification_huffpostContentContainer__B8N5M",closeButtonWrapper:"notification_closeButtonWrapper__qj1ds",closeButton:"notification_closeButton__r_fEG"}},64673:function(e){e.exports={emailWrapper:"huffpost-form_emailWrapper__STjWE",subscribeButton:"huffpost-form_subscribeButton__HlV9G",signupSmallprint:"huffpost-form_signupSmallprint__TJFRy",usSignupSmallprint:"huffpost-form_usSignupSmallprint__EjSmj",signupLearnMore:"huffpost-form_signupLearnMore__IswM0"}},82568:function(e){e.exports={signupHeader:"huffpost-header_signupHeader__m32uR",individualSignupHeader:"huffpost-header_individualSignupHeader__y_72O",webviewSignupHeader:"huffpost-header_webviewSignupHeader__inghK",manageSignupHeader:"huffpost-header_manageSignupHeader__PRI1Q",signupTitle:"huffpost-header_signupTitle___gSof",marginBottom:"huffpost-header_marginBottom__Zw84F",signupSubtitle:"huffpost-header_signupSubtitle__pUT1e",subscribeButton:"huffpost-header_subscribeButton__6zDJm",signupLearnMore:"huffpost-header_signupLearnMore__XYgMx",hideOnMobile:"huffpost-header_hideOnMobile__dBtOL",individualSubtitle:"huffpost-header_individualSubtitle__WScAh"}},11103:function(e){e.exports={main:"huffpost-layout_main__ZGTjB",header:"huffpost-layout_header__bY_hC",footer:"huffpost-layout_footer__ymyNH",title:"huffpost-layout_title__aIVRf",description:"huffpost-layout_description__gAOok",code:"huffpost-layout_code___zX32",grid:"huffpost-layout_grid__cXM9a",card:"huffpost-layout_card__lHjJF",logo:"huffpost-layout_logo__ryAdK"}},32671:function(e){e.exports={manageSubsContainer:"huffpost-manage-text_manageSubsContainer__mWEyZ",individualManageSubsContainer:"huffpost-manage-text_individualManageSubsContainer__AQRgF",manageSubsText:"huffpost-manage-text_manageSubsText__TAoox",manageSubsLinkHuffpost:"huffpost-manage-text_manageSubsLinkHuffpost__Hw9BT"}},36595:function(e){e.exports={mainContent:"index_mainContent___73Z2",huffpost:"index_huffpost__oyqkJ",webviewLayout:"index_webviewLayout__UH50z"}},13738:function(e){e.exports={"mt-1":"utils_mt-1__jn_A6",huffpostSignupInput:"utils_huffpostSignupInput__hM_cz",huffpostEmailErrorLabel:"utils_huffpostEmailErrorLabel__1tsyy",huffpostInputContainer:"utils_huffpostInputContainer__Jv__5",active:"utils_active__G8bkP",signupInputError:"utils_signupInputError__pUNgH"}},54700:function(e){e.exports="/static-assets/_next/static/images/favicon-496b7cee633e6a7dca162654e1bb39c9.ico"},58914:function(e){e.exports="data:image/png;base64,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"},67644:function(e){e.exports="/static-assets/_next/static/images/favicon-49d968d73480ca9a4068ad104af94a00.ico"},42483:function(e){e.exports="/static-assets/_next/static/images/og-image-d1af1f41909755af8254bdc90e00c62d.png"}}]);
//# sourceMappingURL=554-f3177a7fbcfc6d49.js.map