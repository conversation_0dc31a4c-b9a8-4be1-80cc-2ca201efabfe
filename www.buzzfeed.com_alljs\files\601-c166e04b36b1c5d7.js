(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[601],{36864:function(t,r,e){"use strict";var n=e(76635);function i(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=new Array(r);e<r;e++)n[e]=t[e];return n}function o(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,r){var e=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,i,o=[],a=!0,u=!1;try{for(e=e.call(t);!(a=(n=e.next()).done)&&(o.push(n.value),!r||o.length!==r);a=!0);}catch(l){u=!0,i=l}finally{try{a||null==e.return||e.return()}finally{if(u)throw i}}return o}}(t,r)||function(t,r){if(!t)return;if("string"===typeof t)return i(t,r);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return i(t,r)}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}r.Z={add:function(t){for(var r=arguments.length,e=new Array(r>1?r-1:0),i=1;i<r;i++)e[i-1]=arguments[i];return(0,n.unionBy)(t,e,JSON.stringify)},exclude:function(t){for(var r=arguments.length,e=new Array(r>1?r-1:0),i=1;i<r;i++)e[i-1]=arguments[i];return(0,n.differenceBy)(t,e,JSON.stringify)},_filterProgrammatic:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return!0};return t=t.map(JSON.stringify),r.filter((function(r){return-1===t.indexOf(JSON.stringify(r))||e(r)}))},filterProgrammatic:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=e.min,i=void 0===n?null:n,o=e.max,a=void 0===o?null:o;return this._filterProgrammatic(t,r,(function(t){return!!(i&&t[0]>=i[0]||a&&t[0]<=a[0])}))},excludeProgrammatic:function(t,r){return this._filterProgrammatic(t,r,(function(){return!1}))},getProgrammatic:function(t,r){return(0,n.differenceBy)(r,this.excludeProgrammatic(t,r),JSON.stringify)},isProgrammatic:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=e.strict,i=void 0===n||n,a=this.getProgrammatic(t,[r]),u=1===a.length;if(u)return!0;if(!i)try{var l=o(r,2),c=l[0],s=l[1];return c>15&&s>15}catch(f){return!1}return!1},isEqual:function(t,r){return t===r||JSON.stringify(t)===JSON.stringify(r)},contains:function(t,r){var e=this;return t.filter((function(t){return e.isEqual(t,r)})).length>0}}},34783:function(t,r,e){"use strict";e.d(r,{lS:function(){return O}});var n=e(2784),i=e(13980),o=e.n(i),a=(e(62863),"cdnImage__3mJlv"),u="gif__playing__UA_gM",l="gifPlayButton__2SCJp",c="gifPlayButton_cta__2JyMD",s="gifPlayButton_bg__FCgUQ",f="gifPlayButton_circle__2oy9F",d="gifPlayButton_pauseCta__3gQC2",g="gif__loading__mFPFP",y="gifPlaceholder__2Z0Xs",h="gifPlayButton_icon__1yKof",v=e(85953);function m(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=new Array(r);e<r;e++)n[e]=t[e];return n}function p(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,r){var e=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,i,o=[],a=!0,u=!1;try{for(e=e.call(t);!(a=(n=e.next()).done)&&(o.push(n.value),!r||o.length!==r);a=!0);}catch(l){u=!0,i=l}finally{try{a||null==e.return||e.return()}finally{if(u)throw i}}return o}}(t,r)||function(t,r){if(!t)return;if("string"===typeof t)return m(t,r);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return m(t,r)}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var b=function(t){var r=function(){S(!1)},e=t.alt,i=void 0===e?"":e,o=t.height,a=void 0===o?"":o,u=t.id,g=void 0===u?Math.random().toString(36).replace(/[^a-z]+/g,""):u,m=t.isPlaying,b=void 0===m||m,A=t.setIsLoading,S=void 0===A?function(){}:A,_=t.setIsPlaying,E=void 0===_?function(){}:_,w=t.src,P=void 0===w?"":w,I=t.width,O=void 0===I?"":I,N=(0,n.useRef)(null),z="".concat(b?"Pause":"Play"," GIF"),j="gif-play-button-".concat(g);if((0,n.useEffect)((function(){N&&N.current&&P&&(N.current.setAttribute("muted",""),N.current.readyState>=2?S(!1):N.current.addEventListener("loadeddata",r))}),[P]),!P)return n.createElement("div",{className:y});var x=p((0,v.O2)(P),2),B=x[0],M=x[1].filter((function(t){return!t.includes("output")})).concat(["output-format=mp4","output-quality=auto"]);return n.createElement(n.Fragment,null,n.createElement("button",{type:"button",className:l,onClick:function(){if(N&&N.current){var t=N.current.paused,r=t?"play":"pause";"function"===typeof N.current[r]&&(N.current[r](),E(t))}}},n.createElement("svg",{"aria-labelledby":j,viewBox:"0 0 48 48",className:h,fill:"none",role:"img"},n.createElement("title",{id:j},z),n.createElement("circle",{className:s,cx:"24",cy:"24",r:"20",fill:"rgba(0, 0, 0, .25)"}),n.createElement("circle",{className:f,cx:"24",cy:"24",r:"18",stroke:"rgba(255, 255, 255, 1)",strokeWidth:"4"}),n.createElement("path",{className:d,d:"M23.25,30V19.33H20.5V30ZM28,19.33H25.17V30h2.76Zm-.07,2.4,0,1.66"}),n.createElement("path",{className:c,d:"M17.416 30.192c2.016 0 3.584-.816 4.72-2.08v-4.24h-5.152v2.416h2.4v.832c-.384.32-1.184.64-1.968.64-1.744 0-2.976-1.344-2.976-3.088 0-1.76 1.232-3.088 2.976-3.088 1.04 0 1.856.656 2.256 1.36l2.304-1.2c-.736-1.328-2.176-2.592-4.56-2.592-3.184 0-5.776 2.128-5.776 5.52 0 3.392 2.592 5.52 5.776 5.52zM26.504 30V19.328h-2.752V30h2.752zm4.672 0v-4.208h4.96v-2.4h-4.96v-1.664h5.072v-2.4h-7.824V30h2.752z"}))),n.createElement("video",{"aria-label":"GIF: ".concat(i),autoPlay:!0,height:a,loop:!0,muted:!0,playsInline:!0,ref:N,width:O},n.createElement("source",{type:"video/mp4",src:(0,v.tY)(B,M)})))};function A(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=new Array(r);e<r;e++)n[e]=t[e];return n}function S(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,r){var e=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,i,o=[],a=!0,u=!1;try{for(e=e.call(t);!(a=(n=e.next()).done)&&(o.push(n.value),!r||o.length!==r);a=!0);}catch(l){u=!0,i=l}finally{try{a||null==e.return||e.return()}finally{if(u)throw i}}return o}}(t,r)||function(t,r){if(!t)return;if("string"===typeof t)return A(t,r);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return A(t,r)}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}b.propTypes={alt:o().string,height:o().string,id:o().string,isPlaying:o().bool,setIsLoading:o().func,setIsPlaying:o().func,src:o().string,width:o().string};var _=/data:image\/\w+;base64/g,E=/\S+(?:\s?\S+)?/g;function w(t){var r=t.match(E)||[],e=[],n=[];return r.forEach((function(t){var r=S(t.split(" "),2),i=r[0],o=r[1],a=void 0===o?"":o,u=(0,v.Kl)(i),l=i&&i.match(_),c=S((0,v.O2)(i),2),s=c[0],f=c[1].filter((function(t){return!t.includes("output")})),d=l?f:f.concat(["output-format=jpg","output-quality=auto"]),g=f.concat(["output-format=auto","output-quality=auto"]);n.push("".concat((0,v.tY)(s,d)," ").concat(a).trim()),u||l||e.push("".concat((0,v.tY)(s,g)," ").concat(a).trim())})),[n.join(" "),e.join(" ")]}var P=function(t){var r=t.alt,e=void 0===r?"":r,i=t.fetchpriority,o=void 0===i?"auto":i,a=t.height,u=void 0===a?"":a,l=t.loading,c=void 0===l?"eager":l,s=t.sizes,f=void 0===s?"":s,d=t.src,g=void 0===d?"":d,y=t.srcSet,h=void 0===y?"":y,v=t.width,m=void 0===v?"":v,p=(0,n.useRef)(null),b=S(w(g),1)[0],A=S(w(h||g),2),_=A[0],E=A[1];return n.createElement("picture",null,E&&n.createElement("source",{srcSet:E,sizes:f||null}),n.createElement("img",{alt:e,height:u,loading:c,fetchpriority:o,ref:p,sizes:f||null,src:b,srcSet:h?_:null,width:m}))};P.propTypes={alt:o().string,fetchpriority:o().oneOf(["high","low","auto"]),height:o().string,loading:o().oneOf(["eager","lazy"]),sizes:o().string,src:o().string,srcset:o().string,width:o().string};var I="data:image/gif;base64,R0lGODlhAQABAIAAAP///////yH5BAEAAAEALAAAAAABAAEAAAICTAEAOw==",O=function(t){var r=t.alt,e=void 0===r?"":r,i=t.className,o=void 0===i?"":i,l=t.height,c=void 0===l?"":l,s=t.id,f=void 0===s?"":s,d=t.lazy,y=void 0!==d&&d,h=t.loading,m=void 0===h?"eager":h,p=t.fetchpriority,A=void 0===p?"auto":p,S=t.sizes,_=void 0===S?"":S,E=t.src,w=void 0===E?"":E,O=t.srcSet,N=void 0===O?"":O,z=t.width,j=void 0===z?"":z,x=(0,n.useState)(!0),B=x[0],M=x[1],C=(0,n.useState)(!0),J=C[0],T=C[1],k=(0,v.Kl)(w),F=k&&!(0,v.nk)(),V=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.pageId,e=t.once,i=void 0!==e&&e,o=t.root,a=void 0===o?null:o,u=t.rootMargin,l=void 0===u?"0px":u,c=t.threshold,s=void 0===c?0:c,f=t.defaultValue,d=void 0!==f&&f,g=(0,n.useState)(null),y=g[0],h=g[1],v=(0,n.useState)(d),m=v[0],p=v[1],b=(0,n.useRef)(null);return(0,n.useEffect)((function(){return y?(b.current?b.current.disconnect():b.current=new IntersectionObserver((function(t){var r;null!==(r=t[0])&&void 0!==r&&r.isIntersecting?(p(!0),i&&b.current&&b.current.disconnect()):m&&p(!1)}),{root:a,rootMargin:l,threshold:s}),b.current.observe(y),function(){b.current.disconnect()}):function(){}}),[y,r]),{isIntersecting:m,setObservable:h}}({rootMargin:"300px",once:!0}),q=V.isIntersecting,L=V.setObservable,U="lazy"===m&&(F||!(0,v.Dn)())||y&&(F||"lazy"!==m);if(!w)return null;var R=o?o.split(" "):[];k&&(R.push("js-gif-container",a),J&&F&&R.push(B?g:u));var Z=U?q?w:I:w,D=Z===I;return n.createElement("div",{className:R.join(" "),ref:U?L:null},F&&n.createElement(b,{alt:e,height:"".concat(c),id:f,isPlaying:J,setIsLoading:M,setIsPlaying:T,src:D?"":Z,width:"".concat(j)}),!F&&n.createElement(P,{alt:e,height:"".concat(c),loading:U?null:m,fetchpriority:A,sizes:D?null:_,src:Z,srcSet:D?null:N,width:"".concat(j)}))};O.propTypes={alt:o().string,className:o().string,height:o().oneOfType([o().string,o().number]),lazy:o().bool,loading:o().oneOf(["eager","lazy"]),src:o().string,width:o().oneOfType([o().string,o().number])}},62863:function(){}}]);
//# sourceMappingURL=601-c166e04b36b1c5d7.js.map