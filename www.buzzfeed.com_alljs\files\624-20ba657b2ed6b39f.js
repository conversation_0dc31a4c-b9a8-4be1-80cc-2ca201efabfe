(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[624],{19856:function(e,t,n){"use strict";var r=n(52322),a=n(60565),i=n(65246),o=n.n(i);function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){c(e,t,n[t])}))}return e}t.Z=function(e){var t=e.name,n=e.url,i=e.btnStyles,c=e.icon,l=void 0===c?"":c,u=e.trackingInfo,m=void 0===u?{}:u,d=e.trackingInfoExternalLink,_=void 0===d?{}:d,f=e.target,p=void 0===f?"":f,g=e.onSignInClick,h=(0,a.E)(),b=h.trackInternalLink,y=h.trackExternalLink,v="function"===typeof g;return(0,r.jsxs)("a",s({onClick:function(){v?g(s({item_type:"button"},m)):0!==Object.keys(_).length?y(s({context_page_type:"feed",item_type:"button",unit_name:"main"},_)):b(s({context_page_type:"feed",item_type:"button"},m))},className:"".concat(o().loginBtn," ").concat(o()[i])},!v&&{target:p,href:n,rel:"noreferrer"},{children:[l&&(0,r.jsx)("div",{className:o().loginIcon,children:l}),(0,r.jsx)("p",{className:o().loginBtnText,children:t})]}))}},41407:function(e,t,n){"use strict";var r=n(52322),a=n(11289),i=n.n(a),o=n(60565),c=n(30353);t.Z=function(e){var t=e.data,n=e.hasNumberedPosts,a=e.hasSplash,s=e.layout,l=void 0===s?"list":s,u=e.pageId,m=(0,o.E)().trackInternalLink,d=function(e,t,n){m({context_page_type:"feed",context_page_id:"community_"+u,unit_type:"feed",unit_name:"main",position_in_unit:n,item_type:"card",item_name:t,target_content_type:e,target_content_id:t})},_="list"===l?i().list:i().grid,f="list"===l?i().bigRankBadge:"";return(0,r.jsx)("div",{className:"".concat(i().feedContainer," ").concat(_),children:t&&t.map((function(e,t){return(0,r.jsxs)("div",{className:"".concat(i().item," ").concat(a&&0==t?i().splash:""),children:[(0,r.jsx)("div",{className:i().imageWrapper,children:(0,r.jsxs)("a",{onClick:function(){return d("buzz",e.buzz_id,t+1)},href:"".concat(c._H).concat(e.canonical_path),rel:"noreferrer",children:[(0,r.jsx)("img",{className:i().image,src:a&&0==t?e.thumbnailWide:e.thumbnail,alt:e.thumbnailAlt}),n&&(0,r.jsx)("div",{className:"".concat(i().rankBadge," ").concat(f),children:t+1})]})}),(0,r.jsxs)("div",{className:i().titleWrapper,children:[(0,r.jsx)("a",{onClick:function(){return d("buzz",e.buzz_id,t+1)},href:"".concat(c._H).concat(e.canonical_path),rel:"noreferrer",children:(0,r.jsx)("p",{className:i().title,children:(o=e.title,o.replace(/\b'/g,"\u2019").replace(/"\b/g,"\u201c").replace(/\b"/g,"\u201d"))})}),!(a&&0==t)&&(0,r.jsx)("div",{className:i().avatarWrapper,children:(0,r.jsxs)("a",{onClick:function(){return d("user",e.username,t+1)},href:"https://www.buzzfeed.com/".concat(e.username),rel:"noreferrer",children:[(0,r.jsx)("img",{className:i().avatar,src:e.avatar,alt:"avatar"}),(0,r.jsx)("span",{className:i().avatarName,children:e.display_name})]})})]})]},e.buzz_id);var o}))})}},91340:function(e,t,n){"use strict";var r=n(52322),a=n(20760),i=n.n(a),o=n(60565),c=n(34369),s=n(91987);t.Z=function(e){var t=e.data,n=e.pageId,a=(0,o.E)().trackInternalLink,l=function(e,t){a({context_page_type:"feed",context_page_id:"community_"+n,unit_name:"main",unit_type:"feed",subunit_name:"contributors",subunit_type:"package",position_in_unit:1,position_in_subunit:t,item_type:"card",item_name:e,target_content_type:"user",target_content_id:e})};return(0,r.jsxs)("div",{className:i().topContributors,children:[(0,r.jsx)(s.Z,{title:"Top Contributors"}),(0,r.jsx)("p",{className:i().subTitle,children:"Based on number of posts promoted in the last 30 days by the Community team."}),(0,r.jsx)("div",{className:i().itemsContainer,children:t&&t.map((function(e,t){return(0,r.jsxs)("div",{className:"".concat(i().item),children:[(0,r.jsx)("div",{className:i().avatarWrapper,children:(0,r.jsxs)("a",{onClick:function(){return l(e.username,t+1)},href:"https://www.buzzfeed.com/".concat(e.username),rel:"noreferrer",children:[(0,r.jsx)("img",{className:i().avatar,src:e.avatar,alt:"".concat(e.display_name," avatar")}),(0,r.jsx)("div",{className:i().rankBadge,children:t+1})]})}),(0,r.jsxs)("div",{className:i().detailsWrapper,children:[(0,r.jsx)("a",{className:i().displayName,onClick:function(){return l(e.username,t+1)},href:"https://www.buzzfeed.com/".concat(e.username),rel:"noreferrer",children:e.display_name}),(0,r.jsxs)("div",{className:i().numPosts,children:["\u2605 ",e.num_posts," Posts"]})]})]},e.id)}))}),"leaderboard"!==n&&(0,r.jsx)("div",{className:i().actions,children:(0,r.jsx)(c.Z,{link:"/community/leaderboard",name:"View the leaderboard \u203a"})})]})}},80684:function(e,t,n){"use strict";n.d(t,{Z:function(){return j}});var r=n(52322),a=n(2784),i=n(94776),o=n.n(i);function c(e,t,n,r,a,i,o){try{var c=e[i](o),s=c.value}catch(l){return void n(l)}c.done?t(s):Promise.resolve(s).then(r,a)}function s(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(e){c(i,r,a,o,s,"next",e)}function s(e){c(i,r,a,o,s,"throw",e)}o(void 0)}))}}function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){l(e,t,n[t])}))}return e}var m={credentials:"same-origin"},d=function(e,t){var n=t.type,r=t.data,a=t.error;switch(n){case"START":return u({},e,{isLoading:!0,error:null});case"SUCCESS":return u({},e,{isLoading:!1,error:null,data:r});case"FAIL":return u({},e,{isLoading:!1,error:a})}return e},_=n(30353),f=n(71700),p=n(69930),g=n.n(p),h=n(91987),b=function(){return(0,r.jsxs)("svg",{width:"18",height:"14",viewBox:"0 0 18 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("title",{children:"Email"}),(0,r.jsx)("path",{d:"M0.75 3.25V11.2292V11.5833C0.75 12.3657 1.42157 13 2.25 13H15.75C16.5784 13 17.25 12.3657 17.25 11.5833V11.2292V3.25M0.75 3.25V2.41667C0.75 1.63426 1.42157 1 2.25 1H15.75C16.5784 1 17.25 1.63426 17.25 2.41667V3.25M0.75 3.25L9 7.375L17.25 3.25",stroke:"black",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})},y=function(){return(0,r.jsxs)("svg",{width:"18",height:"14",viewBox:"0 0 18 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("title",{children:"Twitter"}),(0,r.jsx)("path",{d:"M5.92688 13.7082C12.1574 13.7082 15.5642 8.54538 15.5642 4.07092C15.5642 3.92341 15.5642 3.7759 15.5572 3.63542C16.2174 3.15777 16.7934 2.56071 17.25 1.87935C16.6459 2.14628 15.9927 2.32891 15.3043 2.4132C16.0067 1.99174 16.5406 1.33146 16.7934 0.537719C16.1402 0.924054 15.4167 1.20502 14.644 1.35956C14.0259 0.699277 13.1478 0.29187 12.1715 0.29187C10.303 0.29187 8.78576 1.80911 8.78576 3.67756C8.78576 3.94449 8.81386 4.20438 8.87708 4.45023C6.06034 4.30975 3.56673 2.96109 1.89496 0.910005C1.60696 1.40873 1.43838 1.99174 1.43838 2.60988C1.43838 3.78293 2.03544 4.82252 2.9486 5.42661C2.39368 5.41256 1.87388 5.25802 1.41731 5.00515C1.41731 5.0192 1.41731 5.03325 1.41731 5.0473C1.41731 6.69097 2.58333 8.05368 4.1357 8.36977C3.85473 8.44704 3.55268 8.48918 3.24361 8.48918C3.02586 8.48918 2.81513 8.46811 2.60441 8.42597C3.03289 9.77462 4.28321 10.751 5.76533 10.7791C4.60632 11.6852 3.14527 12.2261 1.55779 12.2261C1.28384 12.2261 1.01692 12.212 0.75 12.1769C2.23212 13.1463 4.01628 13.7082 5.92688 13.7082Z",fill:"#222222"})]})};function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function x(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,c=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(s){c=!0,a=s}finally{try{o||null==n.return||n.return()}finally{if(c)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return v(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return v(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var j=function(){var e=(0,a.useState)([]),t=e[0],n=e[1],i=x(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=(0,a.useState)(e),r=n[0],i=n[1],c=(0,a.useReducer)(d,{isLoading:!1,error:null,data:t}),l=c[0],u=c[1];return(0,a.useEffect)((function(){if(!r)return u({type:"SUCCESS",data:l.data}),function(){};var e=!0,t=function(){var t=s(o().mark((function t(){var n,a;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return u({type:"START"}),t.prev=1,t.next=4,fetch(r,m);case 4:return n=t.sent,t.next=7,n.json();case 7:a=t.sent,e&&u({type:"SUCCESS",data:a}),t.next=14;break;case 11:t.prev=11,t.t0=t.catch(1),e&&(window.raven&&window.raven.captureException(t.t0),u({type:"FAIL",error:t.t0}));case 14:case"end":return t.stop()}}),t,null,[[1,11]])})));return function(){return t.apply(this,arguments)}}();return t(),function(){e=!1}}),[r]),[l,i]}("".concat(f.SITE_COMPONENT_API,"/en-us/masthead/community")),1),c=i[0],l=c.error,u=c.data;return(0,a.useEffect)((function(){var e;!l&&(null===u||void 0===u||null===(e=u.data)||void 0===e?void 0:e.length)&&u.data[0].contacts&&n(u.data[0].contacts)}),[u,l]),l||!t.length?null:(0,r.jsxs)("div",{className:g().guidelinesOurTeamWrapper,children:[(0,r.jsx)(h.Z,{title:"Our Team"}),t.map((function(e){return(0,r.jsxs)("div",{className:g().guidelinesOurTeamPersonWrapper,children:[(0,r.jsxs)("div",{className:g().guidelinesOurTeamPerson,children:[(0,r.jsx)("a",{className:g().guidelinesOurTeamName,target:"_blank",href:"".concat(_._H,"/").concat(e.username),rel:"noreferrer",children:e.display_name}),(0,r.jsx)("p",{className:g().guidelinesOurTeamPosition,children:e.byline_description})]}),(0,r.jsxs)("div",{className:g().guidelinesOurTeamIconWrapper,children:[e.twitter&&(0,r.jsx)("a",{className:g().guidelinesOurTeamIcon,target:"_blank",href:e.twitter,rel:"noreferrer",children:(0,r.jsx)(y,{})}),(0,r.jsx)("a",{className:g().guidelinesOurTeamIcon,target:"_blank",href:"mailto: ".concat(e.email),rel:"noreferrer",children:(0,r.jsx)(b,{})})]})]},e.display_name)}))]})}},59424:function(e,t,n){"use strict";var r=n(52322),a=n(63953),i=n.n(a),o=n(65267),c=n.n(o),s=n(19856),l=n(34369),u=n(51383),m=n(91987),d=n(10467);function _(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){_(e,t,n[t])}))}return e}t.Z=function(e){var t=e.isUserAuth;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:i().footerWrapper,children:(0,r.jsxs)("div",{className:i().footer,children:[!t&&(0,r.jsxs)("div",{className:i().loginCol,children:[(0,r.jsx)("h2",{className:i().sectionTitle,children:"Sign up and start creating"}),(0,r.jsx)("div",{className:i().loginBtns,children:d.f$.map((function(e){return(0,r.jsx)("div",{className:i().btnSize,children:(0,r.jsx)(s.Z,{name:e.name,url:e.url,btnStyles:e.btnStyle,icon:e.icon,trackingInfo:f({},e.trackingInfo,{unit_name:"bottom",target_content_type:"auth",target_content_id:"sign_in"})})},e.name)}))})]}),(0,r.jsxs)("div",{className:i().FAQCol,children:[(0,r.jsxs)("div",{className:i().FAQSection,children:[(0,r.jsx)(m.Z,{title:"FAQs",titleSmall:!0}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:(0,r.jsx)(l.Z,{link:"https://www.buzzfeed.com/annakopsky/internet-points-2019",name:"What are Internet Points?"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l.Z,{link:"https://www.buzzfeed.com/annakopsky/a-teachers-guide-to-using-buzzfeed-community-cmbif6i95p",name:"What if I\u2019m a professor/teacher/student that wants to use Community for educational purposes?"})})]})]}),(0,r.jsxs)("div",{className:i().FAQSection,children:[(0,r.jsx)(m.Z,{title:"Contact us",titleSmall:!0}),(0,r.jsx)(l.Z,{link:"mailto: <EMAIL>",name:"<EMAIL>"}),(0,r.jsx)("br",{}),(0,r.jsx)("div",{className:i().googleFormLink,children:(0,r.jsx)(l.Z,{link:"https://forms.gle/vD3MT2vRdPb7B62b6",name:"Join our user research program to provide your feedback and earn gift cards!"})})]}),(0,r.jsx)("div",{className:i().FAQSection,children:(0,r.jsx)(u.Z,{titleSmall:!0,fillColor:"#C6C8EE",twitterColorDefault:!1})})]})]})}),(0,r.jsx)("div",{className:i().wrapperBg,children:(0,r.jsx)("div",{className:i().background,style:{backgroundImage:"url(".concat(c(),")")}})})]})}},71700:function(e){"use strict";function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function n(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function r(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,c=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(s){c=!0,a=s}finally{try{o||null==n.return||n.return()}finally{if(c)throw a}}return i}}(e,n)||function(e,n){if(!e)return;if("string"===typeof e)return t(e,n);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return t(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var a={SITE_COMPONENT_API:"/site-component/v1"};if(["development","stage"].includes("production")){var i=!0,o=!1,c=void 0;try{for(var s,l=Object.entries(a)[Symbol.iterator]();!(i=(s=l.next()).done);i=!0){var u=r(s.value,2),m=u[0],d=u[1];a[m]="".concat("https://stage.buzzfeed.com").concat(d)}}catch(_){o=!0,c=_}finally{try{i||null==l.return||l.return()}finally{if(o)throw c}}}e.exports=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},a=Object.keys(r);"function"===typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),a.forEach((function(t){n(e,t,r[t])}))}return e}({},{BF_HEADER_UI:"/bf-header-ui",CONTENT_METRICS_API:"/content-metrics",FEED_API:"/feed-api/v1",LEADERBOARD_API:"/leaderboard",TASTY_TRENDING:"/search/trending",WEAVER_API:"/weaver/v1"},a)},92893:function(e,t,n){"use strict";n.d(t,{L:function(){return i}});var r=n(2784),a=n(60565);function i(e){var t=(0,a.E)().trackTimeSpent;(0,r.useEffect)((function(){return t(e)}),[])}},65246:function(e){e.exports={loginBtn:"loginBtn_loginBtn__QeO57",loginBtnText:"loginBtn_loginBtnText__oOXQH",loginIcon:"loginBtn_loginIcon__As08U",loginGoogleBtn:"loginBtn_loginGoogleBtn__8_6gi",loginFbBtn:"loginBtn_loginFbBtn__FM1WB",loginAppleBtn:"loginBtn_loginAppleBtn__8FORH",loginEmailBtn:"loginBtn_loginEmailBtn__1cSkq",loginEmailBtnGuidelines:"loginBtn_loginEmailBtnGuidelines__m_UyT",loginEmailBtnCreate:"loginBtn_loginEmailBtnCreate__zsmsn",loginEmailBtnStep:"loginBtn_loginEmailBtnStep__vZxgz"}},11289:function(e){e.exports={rankBadge:"communityFeed_rankBadge__TPd7l",feedContainer:"communityFeed_feedContainer__TEWGF",item:"communityFeed_item__z7Yyf",imageWrapper:"communityFeed_imageWrapper__9PWQ0",list:"communityFeed_list__IFieF",splash:"communityFeed_splash__0VNZx",grid:"communityFeed_grid__cvdQu",image:"communityFeed_image__kLo3i",titleWrapper:"communityFeed_titleWrapper__9Rlu8",title:"communityFeed_title__73iEL",bigRankBadge:"communityFeed_bigRankBadge__7p7m_",avatarWrapper:"communityFeed_avatarWrapper__yQ0lA",avatar:"communityFeed_avatar__v_hoR",avatarName:"communityFeed_avatarName__3cjB5"}},20760:function(e){e.exports={rankBadge:"contributors_rankBadge__pttwI",topContributors:"contributors_topContributors__wUXox",subTitle:"contributors_subTitle__fjQz6",actions:"contributors_actions__lynyz",itemsContainer:"contributors_itemsContainer___L2nZ",item:"contributors_item__YA6lw",avatarWrapper:"contributors_avatarWrapper__cwvLF",avatar:"contributors_avatar__5XENV",detailsWrapper:"contributors_detailsWrapper__sOG7z",displayName:"contributors_displayName__8CRH3",numPosts:"contributors_numPosts__0EQnT"}},69930:function(e){e.exports={guidelinesOurTeamWrapper:"ourTeam_guidelinesOurTeamWrapper__8xR0y",guidelinesOurTeamPersonWrapper:"ourTeam_guidelinesOurTeamPersonWrapper__jgbnB",guidelinesOurTeamPerson:"ourTeam_guidelinesOurTeamPerson__tgd3p",guidelinesOurTeamName:"ourTeam_guidelinesOurTeamName__LLgWR",guidelinesOurTeamPosition:"ourTeam_guidelinesOurTeamPosition__sx0On",guidelinesOurTeamIconWrapper:"ourTeam_guidelinesOurTeamIconWrapper__czxBq",guidelinesOurTeamIcon:"ourTeam_guidelinesOurTeamIcon__2EWJV"}},63953:function(e){e.exports={footerWrapper:"footer_footerWrapper__yHY_b",footer:"footer_footer__oomSf",loginCol:"footer_loginCol__p_mMO",sectionTitle:"footer_sectionTitle__Zc9c4",loginBtns:"footer_loginBtns__5C5GK",btnSize:"footer_btnSize__uRtCv",FAQCol:"footer_FAQCol__asESL",FAQSection:"footer_FAQSection__S14ye",googleFormLink:"footer_googleFormLink__Qen2y",wrapperBg:"footer_wrapperBg__WO_FM",background:"footer_background__CLkD8"}}}]);
//# sourceMappingURL=624-20ba657b2ed6b39f.js.map