(window.webpackJsonp_N_E=window.webpackJsonp_N_E||[]).push([[7],{"+5gW":function(t,e,n){"use strict";var r=n("MrsR"),o=n("GUVy"),i=n("XWg/"),a=n("ZpqU"),c=n("Tjm3"),u=Array.prototype.slice,s=Function.prototype.apply,f=Object.create;n("pmLj").async=function(t,e){var n,l,p,h=f(null),d=f(null),v=e.memoized,y=e.original;e.memoized=a((function(t){var e=arguments,r=e[e.length-1];return"function"===typeof r&&(n=r,e=u.call(e,0,-1)),v.apply(l=this,p=e)}),v);try{i(e.memoized,v)}catch(g){}e.on("get",(function(t){var r,o,i;if(n){if(h[t])return"function"===typeof h[t]?h[t]=[h[t],n]:h[t].push(n),void(n=null);r=n,o=l,i=p,n=l=p=null,c((function(){var a;hasOwnProperty.call(d,t)?(a=d[t],e.emit("getasync",t,i,o),s.call(r,a.context,a.args)):(n=r,l=o,p=i,v.apply(o,i))}))}})),e.original=function(){var t,o,i,a;return n?(t=r(arguments),o=function t(n){var o,i,u=t.id;if(null!=u){if(delete t.id,o=h[u],delete h[u],o)return i=r(arguments),e.has(u)&&(n?e.delete(u):(d[u]={context:this,args:i},e.emit("setasync",u,"function"===typeof o?1:o.length))),"function"===typeof o?a=s.call(o,this,i):o.forEach((function(t){a=s.call(t,this,i)}),this),a}else c(s.bind(t,this,arguments))},i=n,n=l=p=null,t.push(o),a=s.call(y,this,t),o.cb=i,n=o,a):s.call(y,this,arguments)},e.on("set",(function(t){n?(h[t]?"function"===typeof h[t]?h[t]=[h[t],n.cb]:h[t].push(n.cb):h[t]=n.cb,delete n.cb,n.id=t,n=null):e.delete(t)})),e.on("delete",(function(t){var n;hasOwnProperty.call(h,t)||d[t]&&(n=d[t],delete d[t],e.emit("deleteasync",t,u.call(n.args,1)))})),e.on("clear",(function(){var t=d;d=f(null),e.emit("clearasync",o(t,(function(t){return u.call(t.args,1)})))}))}},"+dLj":function(t,e,n){"use strict";var r=n("UBbi"),o=Object.keys;t.exports=function(t){return o(r(t)?Object(t):t)}},"/Qos":function(t,e,n){(function(t){var r="undefined"!==typeof t&&t||"undefined"!==typeof self&&self||window,o=Function.prototype.apply;function i(t,e){this._id=t,this._clearFn=e}e.setTimeout=function(){return new i(o.call(setTimeout,r,arguments),clearTimeout)},e.setInterval=function(){return new i(o.call(setInterval,r,arguments),clearInterval)},e.clearTimeout=e.clearInterval=function(t){t&&t.close()},i.prototype.unref=i.prototype.ref=function(){},i.prototype.close=function(){this._clearFn.call(r,this._id)},e.enroll=function(t,e){clearTimeout(t._idleTimeoutId),t._idleTimeout=e},e.unenroll=function(t){clearTimeout(t._idleTimeoutId),t._idleTimeout=-1},e._unrefActive=e.active=function(t){clearTimeout(t._idleTimeoutId);var e=t._idleTimeout;e>=0&&(t._idleTimeoutId=setTimeout((function(){t._onTimeout&&t._onTimeout()}),e))},n("gIIS"),e.setImmediate="undefined"!==typeof self&&self.setImmediate||"undefined"!==typeof t&&t.setImmediate||this&&this.setImmediate,e.clearImmediate="undefined"!==typeof self&&self.clearImmediate||"undefined"!==typeof t&&t.clearImmediate||this&&this.clearImmediate}).call(this,n("fRV1"))},"/eHx":function(t,e,n){"use strict";var r,o=n("nETP"),i=n("UBbi"),a=n("U4eW"),c=Array.prototype.slice;r=function(t){return this.map((function(e,n){return e?e(t[n]):t[n]})).concat(c.call(t,this.length))},t.exports=function(t){return(t=o(t)).forEach((function(t){i(t)&&a(t)})),r.bind(t)}},"/nJV":function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));function r(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(function(t){return(t^function(){try{return crypto.getRandomValues(new Uint8Array(1))[0]}catch(t){return 255*Math.random()}}()&15>>t/4).toString(16)}))}},"/vbP":function(t,e,n){"use strict";var r=n("U4eW"),o=n("J4Mq"),i=n("pmLj"),a=n("6jaE"),c=n("WHdK");t.exports=function t(e){var n,u,s;if(r(e),(n=Object(arguments[1])).async&&n.promise)throw new Error("Options 'async' and 'promise' cannot be used together");return hasOwnProperty.call(e,"__memoized__")&&!n.force?e:(u=c(n.length,e.length,n.async&&i.async),s=a(e,u,n),o(i,(function(t,e){n[e]&&t(n[e],s,n)})),t.__profiler__&&t.__profiler__(s),s.updateEnv(),s.memoized)}},"0tOi":function(t,e,n){"use strict";t.exports=function(){var t,e,n=Array.from;return"function"===typeof n&&(e=n(t=["raz","dwa"]),Boolean(e&&e!==t&&"dwa"===e[1]))}},"1cz4":function(t,e,n){"use strict";t.exports=n("e8ka")()?Math.sign:n("vncP")},"23+d":function(t,e,n){"use strict";t.exports=function(){var t,e=Object.assign;return"function"===typeof e&&(e(t={foo:"raz"},{bar:"dwa"},{trzy:"trzy"}),t.foo+t.bar+t.trzy==="razdwatrzy")}},"266R":function(t,e,n){"use strict";function r(t,e,n,r,o,i,a){try{var c=t[i](a),u=c.value}catch(s){return void n(s)}c.done?e(u):Promise.resolve(u).then(r,o)}function o(t){return function(){var e=this,n=arguments;return new Promise((function(o,i){var a=t.apply(e,n);function c(t){r(a,o,i,c,u,"next",t)}function u(t){r(a,o,i,c,u,"throw",t)}c(void 0)}))}}n.d(e,"a",(function(){return o}))},"29HP":function(t,e,n){"use strict";t.exports=function(t){var e,n,r=t.length;if(!r)return"\x02";for(e=String(t[n=0]);--r;)e+="\x01"+t[++n];return e}},"2JES":function(t,e,n){"use strict";function r(t,e){var n=t.match(e);return n&&n.length?n[0]:null}var o={getBuzzfeedSubdomainOrWildcard:function(t){var e=r(t,"(dev|stage|www).(buzzfeed|buzzfeednews).(com|io)$");return e||r(t,".?[a-z]+.[a-z]+$")},get:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n="".concat(t,"=");if("undefined"===typeof document)return e;for(var r=document.cookie.split(";"),o=0;o<r.length;o++){for(var i=r[o];" "===i.charAt(0);)i=i.substring(1,i.length);if(0===i.indexOf(n))return i.substring(n.length,i.length)}return e},set:function(t){var e=t.name,n=t.value,r=t.days,o=t.domain,i="";if(r){var a=new Date;a.setTime(a.getTime()+24*r*60*60*1e3),i="; expires=".concat(a.toGMTString())}var c="";return void 0!==o&&(c="; domain=".concat(o)),document.cookie="".concat(e,"=").concat(n).concat(i).concat(c,"; path=/")},remove:function(t,e){return this.set({name:t,value:"",days:-1,domain:e})}};e.a=o},"44S2":function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));var r=n("ERkP"),o=n("O6my"),i=n("kZhk");function a(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var a=Object(o.a)();Object(r.useEffect)((function(){i.j.apply(void 0,[a()].concat(e))}),[])}},"4HaG":function(t,e,n){"use strict";function r(t){return!!(t?t.toLowerCase():navigator.userAgent.toLowerCase()).match(/gsa/)}function o(t){return!!(t?t.toLowerCase():navigator.userAgent.toLowerCase()).match(/android/)}n.d(e,"b",(function(){return r})),n.d(e,"a",(function(){return o}))},"4dDg":function(t,e,n){t.exports={aBeagleFlipper__1Sq4F:"aBeagleFlipper__1Sq4F",aBeagleFlipperTitle__2tWV1:"aBeagleFlipperTitle__2tWV1",toggleOpen__2iMAk:"toggleOpen__2iMAk","toggleClosed__11-zU":"toggleClosed__11-zU",visuallyHidden__TCHEd:"visuallyHidden__TCHEd",panel__3Hima:"panel__3Hima",controls__20xVx:"controls__20xVx",experimentList__EunKK:"experimentList__EunKK","experimentListItem__-wLwY":"experimentListItem__-wLwY",actions__1Anym:"actions__1Anym",primary__3JC83:"primary__3JC83",empty__27yvl:"empty__27yvl"}},"4h0E":function(t,e,n){"use strict";n.d(e,"a",(function(){return c}));var r=n("VX0Q"),o=n("ChH/"),i=n("PMPH"),a=function(t){return t.match(/^stage\./)?"https://".concat(t):"https://www.".concat(t)},c=function(){return Object(o.a)((function t(e){var n=this,o=e.sourceOfTruthDomain,i=e.localDomain,a=e.namespace,c=e.env,u=void 0===c?"dev":c,s=e.updateInterval,f=void 0===s?3e5:s,l=e.iframeTimeout,p=void 0===l?3e3:l;Object(r.a)(this,t),this.sourceOfTruthDomain=o,this.localDomain=i,this.env=u,this.namespace=a,this.iframeTimeout=p,this.cookies={},o!==i&&this.initIframe().then((function(){setInterval(n.updateFromIframe.bind(n),f)})).catch((function(){}))}),[{key:"get",value:function(t){var e=this;return this.sourceOfTruthDomain===this.localDomain?Promise.resolve(i.a.get(t)):this.initIframe().then((function(){return e.cookies[t]||i.a.get(t)})).catch((function(){return i.a.get(t)}))}},{key:"set",value:function(t){var e=this,n=t.name,r=t.value,o=t.days,c=t.secureOnly,u=void 0===c||c;i.a.set({name:n,value:r,days:o,domain:this.localDomain}),this.sourceOfTruthDomain!==this.localDomain&&this.initIframe().then((function(){var t={namespace:e.namespace,msgType:"destination-sync-write",cookieName:n,cookieVal:r,expiresDays:o,secureOnly:u},i=a(e.sourceOfTruthDomain);e.iframe.contentWindow.postMessage(JSON.stringify(t),i)})).catch((function(){return i.a.set({name:n,value:r,days:o,domain:e.localDomain})}))}},{key:"cleanup",value:function(){if(this.boundOnMessage&&window.removeEventListener("message",this.boundOnMessage),this.iframe){var t=new ErrorEvent({message:"XDomainCookies were cleaned up before ready"});this.iframe.dispatchEvent(t),this.iframe.remove()}this.iframeReady=null}},{key:"initIframe",value:function(){var t=this;if(this.iframeReady)return this.iframeReady;var e,n=new Promise((function(e,n){t.boundOnMessage=function(n){t.onMessage(n,e)},window.addEventListener("message",t.boundOnMessage),t.createIframe(n)}));return this.iframeReady=Promise.race([(e=this.iframeTimeout,new Promise((function(t,n){var r={type:"timeout",msg:"".concat(e,"ms timeout exceeded")};setTimeout((function(){return n(r)}),e)}))),n]).catch((function(e){throw"prod"===t.env&&window.raven&&window.raven.captureException("timeout"===e.type?new Error("Destination Sync: ".concat(e.msg)):e),console.error(e),e})),this.iframeReady}},{key:"createIframe",value:function(t){var e="xdomaincookies-".concat(this.namespace),n=document.getElementById(e);if(n)return n.addEventListener("error",(function(e){t(e)})),this.iframe=n,void(this.iframe.dataset.loaded&&this.updateFromIframe());var r=JSON.stringify({namespace:this.namespace,windowOrigin:window.location.origin}),o=document.createElement("iframe");o.style.display="none",o.addEventListener("error",(function(e){t(e)})),o.id=e,o.src=function(t,e){return"".concat(a(t),"/").concat("destination-sync.html","#").concat(encodeURIComponent(e))}(this.sourceOfTruthDomain,r),this.iframe=o,document.body.appendChild(o)}},{key:"updateFromIframe",value:function(){var t={namespace:this.namespace,msgType:"destination-sync-read"},e=a(this.sourceOfTruthDomain);this.iframe.contentWindow.postMessage(JSON.stringify(t),e)}},{key:"onMessage",value:function(t,e){var n={};try{n=JSON.parse(t.data)}catch(r){}n.namespace===this.namespace&&("destination-sync-init"===n.msgType&&(this.iframe.dataset.loaded=!0),"destination-sync-init"!==n.msgType&&"destination-sync-read"!==n.msgType||(this.cookies=n.cookies),e())}}])}()},"5/Fh":function(t,e,n){"use strict";var r=n("pVAg");t.exports=function(t){if(!r(t))return!1;try{return!!t.constructor&&t.constructor.prototype===t}catch(e){return!1}}},"5u1z":function(t,e,n){"use strict";t.exports=n("C5t7")()?Object.keys:n("+dLj")},"6+02":function(t,e,n){"use strict";t.exports=n("xdv7")()?Number.isNaN:n("zlHb")},"6S4M":function(t,e,n){"use strict";n.d(e,"a",(function(){return s})),n.d(e,"b",(function(){return f}));var r=n("zygG"),o=n("xwdf"),i={de:["de"],en:["au","ca","in","uk","us"],es:["es","mx"],fr:["fr"],ja:["jp"],pt:["br"]},a={useCountryCookie:!0},c={country:"en-us",language:"en"};function u(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return i[t]&&i[t].includes(e)?"".concat(t,"-").concat(e):"es"===t?"es":"en-us"}function s(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object.assign({},a,e);var n=(t=Object.assign({},c,t)).country,s=n.split("-"),f=Object(r.a)(s,2),l=f[0],p=f[1],h=void 0===p?"":p;if("en"!==t.language||"en"===t.language&&!e.useCountryCookie)return u(l,h);var d=o.a.get("country");d||(d=h||"us");var v=d.split("-"),y=Object(r.a)(v,2);return l=y[0],(h=y[1])||"es"===l||(h=l,l=Object.keys(i).filter((function(t){return i[t].includes(l)}))[0]),u(l,h)}function f(){return o.a.get("bf-geo-country")||"US"}},"6jaE":function(t,e,n){"use strict";var r=n("ky1M"),o=n("ZpqU"),i=n("HX/o"),a=n("8Ul1").methods,c=n("/eHx"),u=n("DyUT"),s=Function.prototype.apply,f=Function.prototype.call,l=Object.create,p=Object.defineProperties,h=a.on,d=a.emit;t.exports=function(t,e,n){var a,v,y,g,m,b,_,w,O,E,j,x,S,P,L,k=l(null);return v=!1!==e?e:isNaN(t.length)?1:t.length,n.normalizer&&(E=u(n.normalizer),y=E.get,g=E.set,m=E.delete,b=E.clear),null!=n.resolvers&&(L=c(n.resolvers)),P=y?o((function(e){var n,o,i=arguments;if(L&&(i=L(i)),null!==(n=y(i))&&hasOwnProperty.call(k,n))return j&&a.emit("get",n,i,this),k[n];if(o=1===i.length?f.call(t,this,i[0]):s.call(t,this,i),null===n){if(null!==(n=y(i)))throw r("Circular invocation","CIRCULAR_INVOCATION");n=g(i)}else if(hasOwnProperty.call(k,n))throw r("Circular invocation","CIRCULAR_INVOCATION");return k[n]=o,x&&a.emit("set",n,null,o),o}),v):0===e?function(){var e;if(hasOwnProperty.call(k,"data"))return j&&a.emit("get","data",arguments,this),k.data;if(e=arguments.length?s.call(t,this,arguments):f.call(t,this),hasOwnProperty.call(k,"data"))throw r("Circular invocation","CIRCULAR_INVOCATION");return k.data=e,x&&a.emit("set","data",null,e),e}:function(e){var n,o,i=arguments;if(L&&(i=L(arguments)),o=String(i[0]),hasOwnProperty.call(k,o))return j&&a.emit("get",o,i,this),k[o];if(n=1===i.length?f.call(t,this,i[0]):s.call(t,this,i),hasOwnProperty.call(k,o))throw r("Circular invocation","CIRCULAR_INVOCATION");return k[o]=n,x&&a.emit("set",o,null,n),n},a={original:t,memoized:P,profileName:n.profileName,get:function(t){return L&&(t=L(t)),y?y(t):String(t[0])},has:function(t){return hasOwnProperty.call(k,t)},delete:function(t){var e;hasOwnProperty.call(k,t)&&(m&&m(t),e=k[t],delete k[t],S&&a.emit("delete",t,e))},clear:function(){var t=k;b&&b(),k=l(null),a.emit("clear",t)},on:function(t,e){return"get"===t?j=!0:"set"===t?x=!0:"delete"===t&&(S=!0),h.call(this,t,e)},emit:d,updateEnv:function(){t=a.original}},_=y?o((function(t){var e,n=arguments;L&&(n=L(n)),null!==(e=y(n))&&a.delete(e)}),v):0===e?function(){return a.delete("data")}:function(t){return L&&(t=L(arguments)[0]),a.delete(t)},w=o((function(){var t,n=arguments;return 0===e?k.data:(L&&(n=L(n)),t=y?y(n):String(n[0]),k[t])})),O=o((function(){var t,n=arguments;return 0===e?a.has("data"):(L&&(n=L(n)),null!==(t=y?y(n):String(n[0]))&&a.has(t))})),p(P,{__memoized__:i(!0),delete:i(_),clear:i(a.clear),_get:i(w),_has:i(O)}),a}},"7JvT":function(t,e,n){"use strict";var r=n("fGyu"),o="".concat("sticky:","members-update"),i={normal:1,medium:2,high:3},a=new Map,c=new Map,u={};function s(t,e){var n={priority:t},r=new Set;a.forEach((function(e,n){e.priority>t||r.add(n)})),c.forEach((function(t,i){i!==e&&(a.has(i)&&!r.has(i)||t.forEach((function(t){try{"function"===typeof t?t(n):"fire"in t&&t.fire(o,n)}catch(e){console.error(e)}})))}))}function f(t){return"fixed"===getComputedStyle(t).position}function l(t,e){void 0===e&&(e=f(t));var n=t.getBoundingClientRect(),r=n.top,o=n.right,i=n.bottom,a=n.left,c=n.width,u=n.height,s=window.pageXOffset;return e||(a+=s,o+=s),{top:r,right:o,bottom:i,left:a,width:c,height:u}}var p={get defaultPriorities(){return i},MEMBERS_UPDATE:o,validatePriority:function(t){if(isNaN(Number(t))){if("string"!==typeof t)throw new TypeError("Unrecognized priority, should be a number or a name");if(void 0===(t=i[t]))throw new TypeError("Unknown priority name, should be one of ".concat(Object.keys(i)))}return t},isFixed:f,getFixedRect:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.priority,r=void 0===n?i.normal:n,o=e.requestedTop,a=void 0===o?"auto":o;r=p.validatePriority(r);var c,u=l(t);return c="auto"===a?p.getAvailableTop(t,{priority:r,boundingRect:u}):a,u.top=c,u.bottom=c+u.height,u},subscribe:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;c.has(e)||c.set(e,new Set);var n=c.get(e);n.add(t)},unsubscribe:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u,n=c.get(e);n&&(n.delete(t),e!==u&&0===n.size&&c.delete(e))},add:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.priority,r=void 0===n?i.normal:n,o=e.requestedTop,c=void 0===o?"auto":o;if(a.has(t))return p.update(t);r=p.validatePriority(r);var u=p.getFixedRect(t,{priority:r,requestedTop:c});return a.set(t,{rect:u,priority:r,requestedTop:c}),s(r,t),u.top},update:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.forceNotify,r=void 0!==n&&n,o=a.get(t);if(!o)throw new Error("The element is not in the registry");var i=o.priority,c=o.requestedTop,u=o.rect,f=p.getFixedRect(t,{priority:i,requestedTop:c});return o.rect=f,a.set(t,o),(r||f.top!==u.top||f.bottom!==u.bottom||f.left!==u.left||f.right!==u.right)&&s(i,t),f.top},remove:function(t){var e=a.get(t);e&&(t.className.includes("sticky--fixed sticky--show")||a.delete(t),s(e.priority,t))},has:function(t){return a.has(t)},getAvailableTop:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.priority,o=void 0===n?i.normal:n,c=e.boundingRect;o=p.validatePriority(o);var u=[];if(a.forEach((function(e,n){n!==t&&e.priority>=o&&u.push(e)})),0===u.length)return 0;if(!c){var s=a.get(t);c=s?s.rect:l(t)}var f=[];return u.forEach((function(t){var e=t.rect;(e.right>=c.left||e.left<=c.right)&&f.push(e)})),Math.max.apply(Math,Object(r.a)(f.map((function(t){return t.bottom}))))},getTopmostPosition:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i.normal;t=p.validatePriority(t);var e=[];return a.forEach((function(n){n.priority>t&&e.push(n.rect.bottom)})),Math.max.apply(Math,e)},reset:function(){a.clear(),c.clear()}};e.a=p},"7YTw":function(t,e,n){"use strict";var r=n("5/Fh");t.exports=function(t){if("function"!==typeof t)return!1;if(!hasOwnProperty.call(t,"length"))return!1;try{if("number"!==typeof t.length)return!1;if("function"!==typeof t.call)return!1;if("function"!==typeof t.apply)return!1}catch(e){return!1}return!r(t)}},"7kdo":function(t,e,n){"use strict";n.d(e,"a",(function(){return f}));var r=n("zjfJ"),o=n("ERkP"),i=n("lOqH"),a=n("v0uu"),c=n("O6my");function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(Object(n),!0).forEach((function(e){Object(r.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function f(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Object(i.a)("tracking");var e=Object(c.a)(),n=s(s({},e()),t);return Object(o.useEffect)((function(){window.clientEventTracking={getPageContextLayer:function(){return function(){return n}},env:a.CLUSTER}}),[n]),function(){return window.clientEventTracking}}},"7poC":function(t,e,n){"use strict";t.exports=function(t){return"function"===typeof t}},"7px9":function(t,e,n){"use strict";t.exports=function(t){return undefined!==t&&null!==t}},"8Ul1":function(t,e,n){"use strict";var r,o,i,a,c,u,s,f=n("HX/o"),l=n("U4eW"),p=Function.prototype.apply,h=Function.prototype.call,d=Object.create,v=Object.defineProperty,y=Object.defineProperties,g=Object.prototype.hasOwnProperty,m={configurable:!0,enumerable:!1,writable:!0};o=function(t,e){var n,o;return l(e),o=this,r.call(this,t,n=function(){i.call(o,t,n),p.call(e,this,arguments)}),n.__eeOnceListener__=e,this},c={on:r=function(t,e){var n;return l(e),g.call(this,"__ee__")?n=this.__ee__:(n=m.value=d(null),v(this,"__ee__",m),m.value=null),n[t]?"object"===typeof n[t]?n[t].push(e):n[t]=[n[t],e]:n[t]=e,this},once:o,off:i=function(t,e){var n,r,o,i;if(l(e),!g.call(this,"__ee__"))return this;if(!(n=this.__ee__)[t])return this;if("object"===typeof(r=n[t]))for(i=0;o=r[i];++i)o!==e&&o.__eeOnceListener__!==e||(2===r.length?n[t]=r[i?0:1]:r.splice(i,1));else r!==e&&r.__eeOnceListener__!==e||delete n[t];return this},emit:a=function(t){var e,n,r,o,i;if(g.call(this,"__ee__")&&(o=this.__ee__[t]))if("object"===typeof o){for(n=arguments.length,i=new Array(n-1),e=1;e<n;++e)i[e-1]=arguments[e];for(o=o.slice(),e=0;r=o[e];++e)p.call(r,this,i)}else switch(arguments.length){case 1:h.call(o,this);break;case 2:h.call(o,this,arguments[1]);break;case 3:h.call(o,this,arguments[1],arguments[2]);break;default:for(n=arguments.length,i=new Array(n-1),e=1;e<n;++e)i[e-1]=arguments[e];p.call(o,this,i)}}},u={on:f(r),once:f(o),off:f(i),emit:f(a)},s=y({},u),t.exports=e=function(t){return null==t?d(s):y(Object(t),u)},e.methods=c},"8Ync":function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.CDN_URL?window.CDN_URL:window.location.href;return-1!==t.search("[?&]?s=mobile_app([&#]|$)")}},"8o0J":function(t,e,n){"use strict";n.d(e,"a",(function(){return c}));var r=n("9fIP"),o=n("MMYH"),i=n("R/i5"),a=n("gYXA"),c=function(){return Object(o.a)((function t(e){var n=e.cookieName,o=e.daysExpiry,c=e.env,u=e.namespace,s=e.sourceOfTruthDomain,f=e.throttleTimer,l=void 0===f?null:f,p=e.secureOnly,h=void 0===p||p,d=e.localDomain,v=void 0===d?Object(a.a)():d;Object(r.a)(this,t),c="live"===c?"prod":c,this.xDomainCookies=new i.a({sourceOfTruthDomain:s||Object(a.b)(c),namespace:u,localDomain:v,env:c}),this.cookieName=n,this.daysExpiry=o,this.secureOnly=h,this.throttleTimer=l,this.inMemoryValue=null}),[{key:"get",value:function(){var t=this;return this.throttle?Promise.resolve(this.inMemoryValue):this.xDomainCookies.get(this.cookieName).then((function(e){return t.inMemoryValue=e,t.resetThrottle(),e}))}},{key:"set",value:function(t){return this.inMemoryValue=t,this.xDomainCookies.set({name:this.cookieName,value:t,days:this.daysExpiry,secureOnly:this.secureOnly})}},{key:"resetThrottle",value:function(){var t=this;this.throttleTimer&&(this.throttle=setTimeout((function(){t.throttle=null}),this.throttleTimer))}}])}()},"9rgT":function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return o}));var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window,e=t.location.hostname,n=["buzzfeed","buzzfeednews","tasty","huffpost","huffingtonpost"],r=e.split(".")[0];return r&&"stage"!==r&&-1===n.indexOf(r)&&e.split(".").length>=3&&(e=e.substring(r.length+1)),e},o=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"dev";return"dev"===t?"dev.buzzfeed.io":"prod"===t||"app-west"===t?"buzzfeed.com":"stage.buzzfeed.com"}},A4TU:function(t,e){function n(t){return!!t&&("object"===typeof t||"function"===typeof t)&&"function"===typeof t.then}t.exports=n,t.exports.default=n},A8CH:function(t,e,n){"use strict";n.d(e,"a",(function(){return xt}));var r=n("ERkP"),o=n.n(r);n("PMPH");!function(){try{localStorage.setItem("ls_test",!0),localStorage.removeItem("ls_test")}catch(t){return!1}}();var i=n("Q92E");function a(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}var c=n("ODk3");function u(t){return function(t){if(Array.isArray(t))return Object(i.a)(t)}(t)||a(t)||Object(c.a)(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}n("266R");n("O3Et"),n("Q1AV"),n("YPse");n("myWV"),n("dKMb"),n("4h0E"),n("CZvW");var s=n("wope");n("Q1+2");n("E7fa");n("ISyl");n("fW8w");var f="".concat("sticky:","members-update"),l={normal:1,medium:2,high:3},p=new Map,h=new Map,d={};function v(t,e){var n={priority:t},r=new Set;p.forEach((function(e,n){e.priority>t||r.add(n)})),h.forEach((function(t,o){o!==e&&(p.has(o)&&!r.has(o)||t.forEach((function(t){try{"function"===typeof t?t(n):"fire"in t&&t.fire(f,n)}catch(e){console.error(e)}})))}))}function y(t){return"fixed"===getComputedStyle(t).position}function g(t,e){void 0===e&&(e=y(t));var n=t.getBoundingClientRect(),r=n.top,o=n.right,i=n.bottom,a=n.left,c=n.width,u=n.height,s=window.pageXOffset;return e||(a+=s,o+=s),{top:r,right:o,bottom:i,left:a,width:c,height:u}}var m={get defaultPriorities(){return l},MEMBERS_UPDATE:f,validatePriority:function(t){if(isNaN(Number(t))){if("string"!==typeof t)throw new TypeError("Unrecognized priority, should be a number or a name");if(void 0===(t=l[t]))throw new TypeError("Unknown priority name, should be one of ".concat(Object.keys(l)))}return t},isFixed:y,getFixedRect:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.priority,r=void 0===n?l.normal:n,o=e.requestedTop,i=void 0===o?"auto":o;r=m.validatePriority(r);var a,c=g(t);return a="auto"===i?m.getAvailableTop(t,{priority:r,boundingRect:c}):i,c.top=a,c.bottom=a+c.height,c},subscribe:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:d;h.has(e)||h.set(e,new Set);var n=h.get(e);n.add(t)},unsubscribe:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:d,n=h.get(e);n&&(n.delete(t),e!==d&&0===n.size&&h.delete(e))},add:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.priority,r=void 0===n?l.normal:n,o=e.requestedTop,i=void 0===o?"auto":o;if(p.has(t))return m.update(t);r=m.validatePriority(r);var a=m.getFixedRect(t,{priority:r,requestedTop:i});return p.set(t,{rect:a,priority:r,requestedTop:i}),v(r,t),a.top},update:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.forceNotify,r=void 0!==n&&n,o=p.get(t);if(!o)throw new Error("The element is not in the registry");var i=o.priority,a=o.requestedTop,c=o.rect,u=m.getFixedRect(t,{priority:i,requestedTop:a});return o.rect=u,p.set(t,o),(r||u.top!==c.top||u.bottom!==c.bottom||u.left!==c.left||u.right!==c.right)&&v(i,t),u.top},remove:function(t){var e=p.get(t);e&&(t.className.includes("sticky--fixed sticky--show")||p.delete(t),v(e.priority,t))},has:function(t){return p.has(t)},getAvailableTop:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.priority,r=void 0===n?l.normal:n,o=e.boundingRect;r=m.validatePriority(r);var i=[];if(p.forEach((function(e,n){n!==t&&e.priority>=r&&i.push(e)})),0===i.length)return 0;if(!o){var a=p.get(t);o=a?a.rect:g(t)}var c=[];return i.forEach((function(t){var e=t.rect;(e.right>=o.left||e.left<=o.right)&&c.push(e)})),Math.max.apply(Math,u(c.map((function(t){return t.bottom}))))},getTopmostPosition:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l.normal;t=m.validatePriority(t);var e=[];return p.forEach((function(n){n.priority>t&&e.push(n.rect.bottom)})),Math.max.apply(Math,e)},reset:function(){p.clear(),h.clear()}},b=m,_=(n("YcZG"),n("SE2S")),w=n("kl+4"),O=(n("4dDg"),"aBeagleFlipper__1Sq4F"),E="aBeagleFlipperTitle__2tWV1",j="toggleOpen__2iMAk",x="toggleClosed__11-zU",S="visuallyHidden__TCHEd",P="panel__3Hima",L="controls__20xVx",k="experimentList__EunKK",T="experimentListItem__-wLwY",A="actions__1Anym",C="primary__3JC83",I="empty__27yvl",N=n("GkCM");function D(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function M(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?D(Object(n),!0).forEach((function(e){Object(s.a)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):D(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function R(t){var e=t.experiments,n=t.className,i=void 0===n?"":n,a=t.collapse,c=void 0!==a&&a,u=e.eligible||{},f=u&&Object.keys(u).length>0,l=Object(r.useState)(!1),p=l[0],h=l[1],d=Object(r.useState)({}),v=d[0],y=d[1],g=Object(r.useState)(!c),m=g[0],b=g[1],_=Object(r.useState)(""),w=_[0],D=_[1],R=Object(r.useState)(""),F=R[0],z=R[1];function U(t){return function(e){var n=e.target,r=n.options[n.selectedIndex].value;r!==v[t]&&y(M(M({},v),{},Object(s.a)({},t,r)))}}return Object(r.useEffect)((function(){var t=Object.keys(u).reduce((function(t,e){return t[e]=u[e].value,t}),{}),e=window.location.href.split("?"),n=e[0];if(e[1]&&e[1].length){var r=new URLSearchParams(e[1]);r.forEach((function(e,n){n.startsWith("abeagle_")&&(t[n.replace("abeagle_","")]=e,r.delete(n))})),n+="?".concat(r.toString())}y(t),z(n),h(!0)}),[]),Object(r.useEffect)((function(){var t={};if(Object.keys(v).forEach((function(n){e.returned[n]&&e.returned[n].value!==v[n]&&(t["".concat("abeagle_").concat(n)]=v[n])})),Object.keys(t).length>0){var n=new URLSearchParams(t),r=F.includes("?")?"".concat(F,"&"):"".concat(F,"?");D("".concat(r).concat(n.toString()))}else D(F)}),[v,F,m]),p?o.a.createElement("section",{className:"".concat(O," ").concat(i),"aria-labelledby":"abeagle-flipper-title"},o.a.createElement("div",{className:E},o.a.createElement("h2",{id:"abeagle-flipper-title"},"Active A/B Tests on Current Page"),o.a.createElement("button",{type:"button",onClick:function(){b(!m)},title:m?"Hide All":"Show All",className:m?j:x},o.a.createElement("span",{className:S},m?"Hide":"Show"," All"),o.a.createElement(N.a,{width:30,title:m?"Hide All":"Show All","aria-hidden":"true"}))),m&&o.a.createElement("div",{className:P},f&&o.a.createElement("div",{className:L},o.a.createElement("ul",{className:k},Object.keys(u).map((function(t){return o.a.createElement("li",{key:t,className:T},o.a.createElement("label",{htmlFor:t},t),o.a.createElement("select",{id:t,value:v[t]||e.declared[t].variations[0],onChange:U(t),onBlur:U(t)},e.declared[t].variations.map((function(t){return o.a.createElement("option",{key:t,value:t},t)}))))}))),o.a.createElement("div",{className:A},o.a.createElement("a",{href:w,className:C},"Save and Reload"),o.a.createElement("a",{href:F},"Reset All"))),!f&&o.a.createElement("p",{className:I},"No experiments active on this page."))):null}function F(t){var e=t.experiments,n=t.className,r=t.collapse;return e&&e.loaded?o.a.createElement(R,{experiments:e,className:n,collapse:r}):null}var z=n("VX0Q"),U=n("ChH/"),G=n("mqXP"),H=n.n(G);function B(t,e){if(e&&("object"===H()(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function q(t){return(q=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function K(t,e){return(K=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function J(t,e,n){return e=q(e),B(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],q(t).constructor):e.apply(t,n))}var Y=function(t){function e(t){var n;return Object(z.a)(this,e),(n=J(this,e,[t])).state={hasError:!1},n}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&K(t,e)}(e,t),Object(U.a)(e,[{key:"componentDidCatch",value:function(t,e){"function"===typeof this.props.onError&&this.props.onError(t,e)}},{key:"render",value:function(){var t=this.state.hasError,e=this.props,n=e.children,r=e.fallbackRender;return t?"function"===typeof r?r():o.a.createElement("div",null):n}}],[{key:"getDerivedStateFromError",value:function(){return{hasError:!0}}}])}(o.a.Component);var V=n("D57K"),W=n("E3KH"),Z=Object.prototype.toString;function X(t,e){return Z.call(t)==="[object "+e+"]"}function Q(t){return X(t,"Object")}function $(t){return Boolean(t&&t.then&&"function"===typeof t.then)}function tt(t){var e,n;if(Q(t)){var r={};try{for(var o=Object(V.e)(Object.keys(t)),i=o.next();!i.done;i=o.next()){var a=i.value;"undefined"!==typeof t[a]&&(r[a]=tt(t[a]))}}catch(c){e={error:c}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(e)throw e.error}}return r}return Array.isArray(t)?t.map(tt):t}function et(){var t=Object(W.a)(),e=t.crypto||t.msCrypto;if(void 0!==e&&e.getRandomValues){var n=new Uint16Array(8);e.getRandomValues(n),n[3]=4095&n[3]|16384,n[4]=16383&n[4]|32768;var r=function(t){for(var e=t.toString(16);e.length<4;)e="0"+e;return e};return r(n[0])+r(n[1])+r(n[2])+r(n[3])+r(n[4])+r(n[5])+r(n[6])+r(n[7])}return"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,(function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)}))}var nt,rt=n("Sr99"),ot="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,it=Object(W.a)(),at=["debug","info","warn","error","log","assert"];function ct(t){var e=Object(W.a)();if(!("console"in e))return t();var n=e.console,r={};at.forEach((function(t){var o=n[t]&&n[t].__sentry_original__;t in e.console&&o&&(r[t]=n[t],n[t]=o)}));try{return t()}finally{Object.keys(r).forEach((function(t){n[t]=r[t]}))}}function ut(){var t=!1,e={enable:function(){t=!0},disable:function(){t=!1}};return ot?at.forEach((function(n){e[n]=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];t&&ct((function(){var t;(t=it.console)[n].apply(t,Object(V.d)(["Sentry Logger ["+n+"]:"],e))}))}})):at.forEach((function(t){e[t]=function(){}})),e}nt=ot?Object(W.b)("logger",ut):ut();var st=n("MNyZ"),ft="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;var lt=function(){function t(t){var e=this;this._state=0,this._handlers=[],this._resolve=function(t){e._setResult(1,t)},this._reject=function(t){e._setResult(2,t)},this._setResult=function(t,n){0===e._state&&($(n)?n.then(e._resolve,e._reject):(e._state=t,e._value=n,e._executeHandlers()))},this._executeHandlers=function(){if(0!==e._state){var t=e._handlers.slice();e._handlers=[],t.forEach((function(t){t[0]||(1===e._state&&t[1](e._value),2===e._state&&t[2](e._value),t[0]=!0)}))}};try{t(this._resolve,this._reject)}catch(n){this._reject(n)}}return t.prototype.then=function(e,n){var r=this;return new t((function(t,o){r._handlers.push([!1,function(n){if(e)try{t(e(n))}catch(r){o(r)}else t(n)},function(e){if(n)try{t(n(e))}catch(r){o(r)}else o(e)}]),r._executeHandlers()}))},t.prototype.catch=function(t){return this.then((function(t){return t}),t)},t.prototype.finally=function(e){var n=this;return new t((function(t,r){var o,i;return n.then((function(t){i=!1,o=t,e&&e()}),(function(t){i=!0,o=t,e&&e()})).then((function(){i?r(o):t(o)}))}))},t}(),pt=function(){function t(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={}}return t.clone=function(e){var n=new t;return e&&(n._breadcrumbs=Object(V.d)(e._breadcrumbs),n._tags=Object(V.a)({},e._tags),n._extra=Object(V.a)({},e._extra),n._contexts=Object(V.a)({},e._contexts),n._user=e._user,n._level=e._level,n._span=e._span,n._session=e._session,n._transactionName=e._transactionName,n._fingerprint=e._fingerprint,n._eventProcessors=Object(V.d)(e._eventProcessors),n._requestSession=e._requestSession),n},t.prototype.addScopeListener=function(t){this._scopeListeners.push(t)},t.prototype.addEventProcessor=function(t){return this._eventProcessors.push(t),this},t.prototype.setUser=function(t){return this._user=t||{},this._session&&this._session.update({user:t}),this._notifyScopeListeners(),this},t.prototype.getUser=function(){return this._user},t.prototype.getRequestSession=function(){return this._requestSession},t.prototype.setRequestSession=function(t){return this._requestSession=t,this},t.prototype.setTags=function(t){return this._tags=Object(V.a)(Object(V.a)({},this._tags),t),this._notifyScopeListeners(),this},t.prototype.setTag=function(t,e){var n;return this._tags=Object(V.a)(Object(V.a)({},this._tags),((n={})[t]=e,n)),this._notifyScopeListeners(),this},t.prototype.setExtras=function(t){return this._extra=Object(V.a)(Object(V.a)({},this._extra),t),this._notifyScopeListeners(),this},t.prototype.setExtra=function(t,e){var n;return this._extra=Object(V.a)(Object(V.a)({},this._extra),((n={})[t]=e,n)),this._notifyScopeListeners(),this},t.prototype.setFingerprint=function(t){return this._fingerprint=t,this._notifyScopeListeners(),this},t.prototype.setLevel=function(t){return this._level=t,this._notifyScopeListeners(),this},t.prototype.setTransactionName=function(t){return this._transactionName=t,this._notifyScopeListeners(),this},t.prototype.setTransaction=function(t){return this.setTransactionName(t)},t.prototype.setContext=function(t,e){var n;return null===e?delete this._contexts[t]:this._contexts=Object(V.a)(Object(V.a)({},this._contexts),((n={})[t]=e,n)),this._notifyScopeListeners(),this},t.prototype.setSpan=function(t){return this._span=t,this._notifyScopeListeners(),this},t.prototype.getSpan=function(){return this._span},t.prototype.getTransaction=function(){var t=this.getSpan();return t&&t.transaction},t.prototype.setSession=function(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this},t.prototype.getSession=function(){return this._session},t.prototype.update=function(e){if(!e)return this;if("function"===typeof e){var n=e(this);return n instanceof t?n:this}return e instanceof t?(this._tags=Object(V.a)(Object(V.a)({},this._tags),e._tags),this._extra=Object(V.a)(Object(V.a)({},this._extra),e._extra),this._contexts=Object(V.a)(Object(V.a)({},this._contexts),e._contexts),e._user&&Object.keys(e._user).length&&(this._user=e._user),e._level&&(this._level=e._level),e._fingerprint&&(this._fingerprint=e._fingerprint),e._requestSession&&(this._requestSession=e._requestSession)):Q(e)&&(e=e,this._tags=Object(V.a)(Object(V.a)({},this._tags),e.tags),this._extra=Object(V.a)(Object(V.a)({},this._extra),e.extra),this._contexts=Object(V.a)(Object(V.a)({},this._contexts),e.contexts),e.user&&(this._user=e.user),e.level&&(this._level=e.level),e.fingerprint&&(this._fingerprint=e.fingerprint),e.requestSession&&(this._requestSession=e.requestSession)),this},t.prototype.clear=function(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._span=void 0,this._session=void 0,this._notifyScopeListeners(),this},t.prototype.addBreadcrumb=function(t,e){var n="number"===typeof e?Math.min(e,100):100;if(n<=0)return this;var r=Object(V.a)({timestamp:Object(rt.a)()},t);return this._breadcrumbs=Object(V.d)(this._breadcrumbs,[r]).slice(-n),this._notifyScopeListeners(),this},t.prototype.clearBreadcrumbs=function(){return this._breadcrumbs=[],this._notifyScopeListeners(),this},t.prototype.applyToEvent=function(t,e){if(this._extra&&Object.keys(this._extra).length&&(t.extra=Object(V.a)(Object(V.a)({},this._extra),t.extra)),this._tags&&Object.keys(this._tags).length&&(t.tags=Object(V.a)(Object(V.a)({},this._tags),t.tags)),this._user&&Object.keys(this._user).length&&(t.user=Object(V.a)(Object(V.a)({},this._user),t.user)),this._contexts&&Object.keys(this._contexts).length&&(t.contexts=Object(V.a)(Object(V.a)({},this._contexts),t.contexts)),this._level&&(t.level=this._level),this._transactionName&&(t.transaction=this._transactionName),this._span){t.contexts=Object(V.a)({trace:this._span.getTraceContext()},t.contexts);var n=this._span.transaction&&this._span.transaction.name;n&&(t.tags=Object(V.a)({transaction:n},t.tags))}return this._applyFingerprint(t),t.breadcrumbs=Object(V.d)(t.breadcrumbs||[],this._breadcrumbs),t.breadcrumbs=t.breadcrumbs.length>0?t.breadcrumbs:void 0,t.sdkProcessingMetadata=this._sdkProcessingMetadata,this._notifyEventProcessors(Object(V.d)(ht(),this._eventProcessors),t,e)},t.prototype.setSDKProcessingMetadata=function(t){return this._sdkProcessingMetadata=Object(V.a)(Object(V.a)({},this._sdkProcessingMetadata),t),this},t.prototype._notifyEventProcessors=function(t,e,n,r){var o=this;return void 0===r&&(r=0),new lt((function(i,a){var c=t[r];if(null===e||"function"!==typeof c)i(e);else{var u=c(Object(V.a)({},e),n);$(u)?u.then((function(e){return o._notifyEventProcessors(t,e,n,r+1).then(i)})).then(null,a):o._notifyEventProcessors(t,u,n,r+1).then(i).then(null,a)}}))},t.prototype._notifyScopeListeners=function(){var t=this;this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach((function(e){e(t)})),this._notifyingListeners=!1)},t.prototype._applyFingerprint=function(t){t.fingerprint=t.fingerprint?Array.isArray(t.fingerprint)?t.fingerprint:[t.fingerprint]:[],this._fingerprint&&(t.fingerprint=t.fingerprint.concat(this._fingerprint)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint},t}();function ht(){return Object(W.b)("globalEventProcessors",(function(){return[]}))}var dt=function(){function t(t){this.errors=0,this.sid=et(),this.duration=0,this.status="ok",this.init=!0,this.ignoreDuration=!1;var e=Object(rt.b)();this.timestamp=e,this.started=e,t&&this.update(t)}return t.prototype.update=function(t){if(void 0===t&&(t={}),t.user&&(!this.ipAddress&&t.user.ip_address&&(this.ipAddress=t.user.ip_address),this.did||t.did||(this.did=t.user.id||t.user.email||t.user.username)),this.timestamp=t.timestamp||Object(rt.b)(),t.ignoreDuration&&(this.ignoreDuration=t.ignoreDuration),t.sid&&(this.sid=32===t.sid.length?t.sid:et()),void 0!==t.init&&(this.init=t.init),!this.did&&t.did&&(this.did=""+t.did),"number"===typeof t.started&&(this.started=t.started),this.ignoreDuration)this.duration=void 0;else if("number"===typeof t.duration)this.duration=t.duration;else{var e=this.timestamp-this.started;this.duration=e>=0?e:0}t.release&&(this.release=t.release),t.environment&&(this.environment=t.environment),!this.ipAddress&&t.ipAddress&&(this.ipAddress=t.ipAddress),!this.userAgent&&t.userAgent&&(this.userAgent=t.userAgent),"number"===typeof t.errors&&(this.errors=t.errors),t.status&&(this.status=t.status)},t.prototype.close=function(t){t?this.update({status:t}):"ok"===this.status?this.update({status:"exited"}):this.update()},t.prototype.toJSON=function(){return tt({sid:""+this.sid,init:this.init,started:new Date(1e3*this.started).toISOString(),timestamp:new Date(1e3*this.timestamp).toISOString(),status:this.status,errors:this.errors,did:"number"===typeof this.did||"string"===typeof this.did?""+this.did:void 0,duration:this.duration,attrs:{release:this.release,environment:this.environment,ip_address:this.ipAddress,user_agent:this.userAgent}})},t}(),vt=function(){function t(t,e,n){void 0===e&&(e=new pt),void 0===n&&(n=4),this._version=n,this._stack=[{}],this.getStackTop().scope=e,t&&this.bindClient(t)}return t.prototype.isOlderThan=function(t){return this._version<t},t.prototype.bindClient=function(t){this.getStackTop().client=t,t&&t.setupIntegrations&&t.setupIntegrations()},t.prototype.pushScope=function(){var t=pt.clone(this.getScope());return this.getStack().push({client:this.getClient(),scope:t}),t},t.prototype.popScope=function(){return!(this.getStack().length<=1)&&!!this.getStack().pop()},t.prototype.withScope=function(t){var e=this.pushScope();try{t(e)}finally{this.popScope()}},t.prototype.getClient=function(){return this.getStackTop().client},t.prototype.getScope=function(){return this.getStackTop().scope},t.prototype.getStack=function(){return this._stack},t.prototype.getStackTop=function(){return this._stack[this._stack.length-1]},t.prototype.captureException=function(t,e){var n=this._lastEventId=e&&e.event_id?e.event_id:et(),r=e;if(!e){var o=void 0;try{throw new Error("Sentry syntheticException")}catch(t){o=t}r={originalException:t,syntheticException:o}}return this._invokeClient("captureException",t,Object(V.a)(Object(V.a)({},r),{event_id:n})),n},t.prototype.captureMessage=function(t,e,n){var r=this._lastEventId=n&&n.event_id?n.event_id:et(),o=n;if(!n){var i=void 0;try{throw new Error(t)}catch(a){i=a}o={originalException:t,syntheticException:i}}return this._invokeClient("captureMessage",t,e,Object(V.a)(Object(V.a)({},o),{event_id:r})),r},t.prototype.captureEvent=function(t,e){var n=e&&e.event_id?e.event_id:et();return"transaction"!==t.type&&(this._lastEventId=n),this._invokeClient("captureEvent",t,Object(V.a)(Object(V.a)({},e),{event_id:n})),n},t.prototype.lastEventId=function(){return this._lastEventId},t.prototype.addBreadcrumb=function(t,e){var n=this.getStackTop(),r=n.scope,o=n.client;if(r&&o){var i=o.getOptions&&o.getOptions()||{},a=i.beforeBreadcrumb,c=void 0===a?null:a,u=i.maxBreadcrumbs,s=void 0===u?100:u;if(!(s<=0)){var f=Object(rt.a)(),l=Object(V.a)({timestamp:f},t),p=c?ct((function(){return c(l,e)})):l;null!==p&&r.addBreadcrumb(p,s)}}},t.prototype.setUser=function(t){var e=this.getScope();e&&e.setUser(t)},t.prototype.setTags=function(t){var e=this.getScope();e&&e.setTags(t)},t.prototype.setExtras=function(t){var e=this.getScope();e&&e.setExtras(t)},t.prototype.setTag=function(t,e){var n=this.getScope();n&&n.setTag(t,e)},t.prototype.setExtra=function(t,e){var n=this.getScope();n&&n.setExtra(t,e)},t.prototype.setContext=function(t,e){var n=this.getScope();n&&n.setContext(t,e)},t.prototype.configureScope=function(t){var e=this.getStackTop(),n=e.scope,r=e.client;n&&r&&t(n)},t.prototype.run=function(t){var e=gt(this);try{t(this)}finally{gt(e)}},t.prototype.getIntegration=function(t){var e=this.getClient();if(!e)return null;try{return e.getIntegration(t)}catch(n){return ft&&nt.warn("Cannot retrieve integration "+t.id+" from the current Hub"),null}},t.prototype.startSpan=function(t){return this._callExtensionMethod("startSpan",t)},t.prototype.startTransaction=function(t,e){return this._callExtensionMethod("startTransaction",t,e)},t.prototype.traceHeaders=function(){return this._callExtensionMethod("traceHeaders")},t.prototype.captureSession=function(t){if(void 0===t&&(t=!1),t)return this.endSession();this._sendSessionUpdate()},t.prototype.endSession=function(){var t=this.getStackTop(),e=t&&t.scope,n=e&&e.getSession();n&&n.close(),this._sendSessionUpdate(),e&&e.setSession()},t.prototype.startSession=function(t){var e=this.getStackTop(),n=e.scope,r=e.client,o=r&&r.getOptions()||{},i=o.release,a=o.environment,c=(Object(W.a)().navigator||{}).userAgent,u=new dt(Object(V.a)(Object(V.a)(Object(V.a)({release:i,environment:a},n&&{user:n.getUser()}),c&&{userAgent:c}),t));if(n){var s=n.getSession&&n.getSession();s&&"ok"===s.status&&s.update({status:"exited"}),this.endSession(),n.setSession(u)}return u},t.prototype._sendSessionUpdate=function(){var t=this.getStackTop(),e=t.scope,n=t.client;if(e){var r=e.getSession&&e.getSession();r&&n&&n.captureSession&&n.captureSession(r)}},t.prototype._invokeClient=function(t){for(var e,n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];var o=this.getStackTop(),i=o.scope,a=o.client;a&&a[t]&&(e=a)[t].apply(e,Object(V.d)(n,[i]))},t.prototype._callExtensionMethod=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];var r=yt(),o=r.__SENTRY__;if(o&&o.extensions&&"function"===typeof o.extensions[t])return o.extensions[t].apply(this,e);ft&&nt.warn("Extension method "+t+" couldn't be found, doing nothing.")},t}();function yt(){var t=Object(W.a)();return t.__SENTRY__=t.__SENTRY__||{extensions:{},hub:void 0},t}function gt(t){var e=yt(),n=_t(e);return wt(e,t),n}function mt(){var t=yt();return bt(t)&&!_t(t).isOlderThan(4)||wt(t,new vt),Object(st.b)()?function(t){try{var e=yt().__SENTRY__,n=e&&e.extensions&&e.extensions.domain&&e.extensions.domain.active;if(!n)return _t(t);if(!bt(n)||_t(n).isOlderThan(4)){var r=_t(t).getStackTop();wt(n,new vt(r.client,pt.clone(r.scope)))}return _t(n)}catch(o){return _t(t)}}(t):_t(t)}function bt(t){return!!(t&&t.__SENTRY__&&t.__SENTRY__.hub)}function _t(t){return Object(W.b)("hub",(function(){return new vt}),t)}function wt(t,e){return!!t&&((t.__SENTRY__=t.__SENTRY__||{}).hub=e,!0)}function Ot(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];var r=mt();if(r&&r[t])return r[t].apply(r,Object(V.d)(e));throw new Error("No hub defined or "+t+" was not found on the hub, please open a bug report.")}var Et,jt,xt=(Et=function(t){var e=t.css,n=t.html,i=t.js,a=t.stickyHeaderClass,c=void 0===a?"js-main-nav":a,u=t.stickyRegistry,s=void 0===u?b:u,f=Object(r.useRef)(null);Object(r.useEffect)((function(){!function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.head,n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];new Promise((function(r,o){var i=document.createElement("script");i.onload=function(){return r(i)},i.onerror=function(){o("Script at url ".concat(t," failed to load"))},i.src=t,i.async=n,i.type="text/javascript",e.appendChild(i)}))}(i)}),[i]),Object(r.useEffect)((function(){if(!f.current||!c)return function(){};var t=f.current.querySelector(".".concat(c));return t&&s.add(t,{priority:"high"}),function(){t&&s.remove(t)}}),[c,s]);var l=Object(r.useContext)(_.a).experiments,p=!w.b&&window.location.search.includes("abdebug"),h=!w.b&&window.location.search.includes("abdebug_collapse=true");return o.a.createElement("div",{ref:f},o.a.createElement("style",{dangerouslySetInnerHTML:{__html:e}}),o.a.createElement("link",{rel:"preload",href:i,as:"script"}),p?o.a.createElement(F,{experiments:l,collapse:h}):"",o.a.createElement("div",{dangerouslySetInnerHTML:{__html:n}}))},jt={onError:function(t,e){return Ot("captureException",t,{captureContext:e,originalException:t,syntheticException:new Error("Sentry syntheticException")})}},function(t){return o.a.createElement(Y,jt,o.a.createElement(Et,t))})},AC7c:function(t,e,n){"use strict";n.d(e,"a",(function(){return g}));var r=n("cxan"),o=n("zygG"),i=n("ERkP"),a=n.n(i),c=n("JWSz"),u=n.n(c),s=a.a.createElement;function f(t){var e=t.value,n=t.name,r=t.label,o=t.checked,a=t.onChange,c=Object(i.useCallback)((function(){return a(e)}),[e,a]);return s("div",{className:u.a.controlContainer},s("input",{type:"radio",id:"".concat(n,"-").concat(e),name:n,value:e,checked:o,onChange:c}),s("label",{className:u.a.label,htmlFor:"".concat(n,"-").concat(e),onKeyDown:function(t){32===t.keyCode&&c(e)},onClick:c},r))}var l=n("zjfJ");function p(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function h(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?p(Object(n),!0).forEach((function(e){Object(l.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var d=n("Lix2"),v=n.n(d),y=a.a.createElement;function g(t){var e=t.name,n=t.label,a=t.options,c=t.onChange,u=void 0===c?function(){}:c,s=function(t){var e=Object(i.useState)(t),n=e[0],r=e[1];return[n,function(t){r(n.map((function(e){var n=t===e.value;return h(h({},e),{},{checked:n})})))}]}(a),l=Object(o.a)(s,2),p=l[0],d=l[1],g=Object(i.useCallback)((function(t){d(t),u(t)}),[d,u]),m=p.map((function(t){return y(f,Object(r.a)({},t,{name:e,id:t.name,key:t.name,onChange:g}))}));return y("form",null,y("fieldset",null,y("div",{className:v.a.fieldsetInner},y("legend",{className:v.a.legend},n),m)))}},AxBp:function(t,e,n){"use strict";var r=n("7YTw"),o=/^\s*class[\s{/}]/,i=Function.prototype.toString;t.exports=function(t){return!!r(t)&&!o.test(i.call(t))}},BmUA:function(t,e,n){"use strict";var r=n("Yv2q"),o=n("WHdK"),i=n("/vbP");t.exports=function(t){var e,a=r(arguments[1]);return a.normalizer||0!==(e=a.length=o(a.length,t.length,a.async))&&(a.primitive?!1===e?a.normalizer=n("29HP"):e>1&&(a.normalizer=n("H0gn")(e)):a.normalizer=!1===e?n("x4Na")():1===e?n("MH1b")():n("C+Ej")(e)),a.async&&n("+5gW"),a.promise&&n("zgmP"),a.dispose&&n("SiiK"),a.maxAge&&n("ekro"),a.max&&n("HCjI"),a.refCounter&&n("r42V"),i(t,a)}},"C+Ej":function(t,e,n){"use strict";var r=n("FBSS"),o=Object.create;t.exports=function(t){var e=0,n=[[],[]],i=o(null);return{get:function(e){for(var o,i=0,a=n;i<t-1;){if(-1===(o=r.call(a[0],e[i])))return null;a=a[1][o],++i}return-1===(o=r.call(a[0],e[i]))?null:a[1][o]||null},set:function(o){for(var a,c=0,u=n;c<t-1;)-1===(a=r.call(u[0],o[c]))&&(a=u[0].push(o[c])-1,u[1].push([[],[]])),u=u[1][a],++c;return-1===(a=r.call(u[0],o[c]))&&(a=u[0].push(o[c])-1),u[1][a]=++e,i[e]=o,e},delete:function(e){for(var o,a=0,c=n,u=[],s=i[e];a<t-1;){if(-1===(o=r.call(c[0],s[a])))return;u.push(c,o),c=c[1][o],++a}if(-1!==(o=r.call(c[0],s[a]))){for(e=c[1][o],c[0].splice(o,1),c[1].splice(o,1);!c[0].length&&u.length;)o=u.pop(),(c=u.pop())[0].splice(o,1),c[1].splice(o,1);delete i[e]}},clear:function(){n=[[],[]],i=o(null)}}}},C5t7:function(t,e,n){"use strict";t.exports=function(){try{return Object.keys("primitive"),!0}catch(t){return!1}}},CZvW:function(t,e,n){"use strict";n.d(e,"c",(function(){return c})),n.d(e,"b",(function(){return u})),n.d(e,"d",(function(){return s})),n.d(e,"a",(function(){return f}));var r=n("E7fa");function o(t){return(t+="").indexOf("#")>-1?t.substr(t.indexOf("#"),t.length):""}function i(t){return(t+="").indexOf("#")>-1?t.substr(0,t.indexOf("#")):t}function a(t){return t.indexOf("?")>-1}function c(t){if(""===t||void 0===t||null===t)return{};t.indexOf("?")>-1&&(t=t.substr(t.indexOf("?")+1,t.length));var e=(t=i(t)).split("&"),n={};return e.forEach((function(t){var e=t.split("="),o=Object(r.a)(e,2),i=o[0],a=o[1],c=void 0===a?null:a;n[i]=c})),n}function u(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"?",n=[];return Object.keys(t).forEach((function(e){n.push("".concat(e,"=").concat(encodeURIComponent(t[e])))})),(e||"")+n.join("&")}function s(t){return c(function(t){var e="";return a(t=i(t))&&(e=t.substr(t.indexOf("?"),t.length)),e}(t))}function f(t){return t=function(t){if(!a(t))return t;var e=o(t);return(t=i(t)).substr(0,t.indexOf("?"))+e}(t=i(t))}},CcDX:function(t,e,n){"use strict";var r=n("MC86"),o=Object.create,i=Object.prototype.hasOwnProperty;t.exports=function(t){var e,n=0,a=1,c=o(null),u=o(null),s=0;return t=r(t),{hit:function(r){var o=u[r],f=++s;if(c[f]=r,u[r]=f,!o){if(++n<=t)return;return r=c[a],e(r),r}if(delete c[o],a===o)for(;!i.call(c,++a);)continue},delete:e=function(t){var e=u[t];if(e&&(delete c[e],delete u[t],--n,a===e)){if(!n)return s=0,void(a=1);for(;!i.call(c,++a);)continue}},clear:function(){n=0,a=1,c=o(null),u=o(null),s=0}}}},"ChH/":function(t,e,n){"use strict";function r(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function o(t,e,n){return e&&r(t.prototype,e),n&&r(t,n),t}n.d(e,"a",(function(){return o}))},DyUT:function(t,e,n){"use strict";var r=n("U4eW");t.exports=function(t){var e;return"function"===typeof t?{set:t,get:t}:(e={get:r(t.get)},void 0!==t.set?(e.set=r(t.set),t.delete&&(e.delete=r(t.delete)),t.clear&&(e.clear=r(t.clear)),e):(e.set=e.get,e))}},E3KH:function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return i})),n.d(e,"b",(function(){return a}));var r=n("MNyZ"),o={};function i(){return Object(r.b)()?t:"undefined"!==typeof window?window:"undefined"!==typeof self?self:o}function a(t,e,n){var r=n||i(),o=r.__SENTRY__=r.__SENTRY__||{};return o[t]||(o[t]=e())}}).call(this,n("fRV1"))},E7fa:function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));var r=n("Q1AV");var o=n("ODk3"),i=n("YPse");function a(t,e){return Object(r.a)(t)||function(t,e){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i=[],a=!0,c=!1;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(i.push(r.value),!e||i.length!==e);a=!0);}catch(u){c=!0,o=u}finally{try{a||null==n.return||n.return()}finally{if(c)throw o}}return i}}(t,e)||Object(o.a)(t,e)||Object(i.a)()}},Egbq:function(t,e,n){"use strict";e.a=function(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(function(t){return(t^function(){try{return crypto.getRandomValues(new Uint8Array(1))[0]}catch(t){return 255*Math.random()}}()&15>>t/4).toString(16)}))}},Emvt:function(t,e){t.exports={isServer:function(){return!1}}},FBSS:function(t,e,n){"use strict";var r=n("6+02"),o=n("MC86"),i=n("J2Kb"),a=Array.prototype.indexOf,c=Object.prototype.hasOwnProperty,u=Math.abs,s=Math.floor;t.exports=function(t){var e,n,f,l;if(!r(t))return a.apply(this,arguments);for(n=o(i(this).length),f=arguments[1],e=f=isNaN(f)?0:f>=0?s(f):o(this.length)-s(u(f));e<n;++e)if(c.call(this,e)&&(l=this[e],r(l)))return e;return-1}},FR8R:function(t,e,n){"use strict";n.d(e,"a",(function(){return s}));var r=n("fGyu"),o=n("ERkP"),i=n("ds4U"),a=n("SE2S"),c=n("kZhk"),u=n("O6my");function s(t){var e=t.layers,n=void 0===e?[]:e,s=Object(u.a)(),f=Object(o.useContext)(a.a).experiments;return Object(o.useEffect)((function(){var t=function(){};!f.stale&&f.loaded&&("on"===Object(i.a)(f,"TimeSpentRO_2",{rejectErrors:!1})&&(t=c.d.apply(void 0,[s()].concat(Object(r.a)(n)))));return t}),[f.loaded,n]),null}},GUVy:function(t,e,n){"use strict";var r=n("U4eW"),o=n("J4Mq"),i=Function.prototype.call;t.exports=function(t,e){var n={},a=arguments[2];return r(e),o(t,(function(t,r,o,c){n[r]=i.call(e,a,t,r,o,c)})),n}},H0gn:function(t,e,n){"use strict";t.exports=function(t){return t?function(e){for(var n=String(e[0]),r=0,o=t;--o;)n+="\x01"+e[++r];return n}:function(){return""}}},HCjI:function(t,e,n){"use strict";var r=n("MC86"),o=n("CcDX"),i=n("pmLj");i.max=function(t,e,n){var a,c,u;(t=r(t))&&(c=o(t),a=n.async&&i.async||n.promise&&i.promise?"async":"",e.on("set"+a,u=function(t){void 0!==(t=c.hit(t))&&e.delete(t)}),e.on("get"+a,u),e.on("delete"+a,c.delete),e.on("clear"+a,c.clear))}},"HX/o":function(t,e,n){"use strict";var r=n("7px9"),o=n("AxBp"),i=n("t7Nw"),a=n("Yv2q"),c=n("Q8aG");(t.exports=function(t,e){var n,o,u,s,f;return arguments.length<2||"string"!==typeof t?(s=e,e=t,t=null):s=arguments[2],r(t)?(n=c.call(t,"c"),o=c.call(t,"e"),u=c.call(t,"w")):(n=u=!0,o=!1),f={value:e,configurable:n,enumerable:o,writable:u},s?i(a(s),f):f}).gs=function(t,e,n){var u,s,f,l;return"string"!==typeof t?(f=n,n=e,e=t,t=null):f=arguments[3],r(e)?o(e)?r(n)?o(n)||(f=n,n=void 0):n=void 0:(f=e,e=n=void 0):e=void 0,r(t)?(u=c.call(t,"c"),s=c.call(t,"e")):(u=!0,s=!1),l={get:e,set:n,configurable:u,enumerable:s},f?i(a(f),l):l}},HbGN:function(t,e,n){"use strict";function r(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}n.d(e,"a",(function(){return r}))},I9c2:function(t,e,n){"use strict";t.exports=function(){}},ISyl:function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));n("E7fa");var r=n("PMPH");function o(){return r.a.get("bf-geo-country")||"US"}},J2Kb:function(t,e,n){"use strict";var r=n("UBbi");t.exports=function(t){if(!r(t))throw new TypeError("Cannot use null or undefined");return t}},J4Mq:function(t,e,n){"use strict";t.exports=n("btX6")("forEach")},JIjG:function(t,e,n){"use strict";var r=n("7poC");t.exports=function(t){try{return t&&r(t.toString)?t.toString():String(t)}catch(e){return"<Non-coercible to string value>"}}},JNgv:function(t,e,n){"use strict";n.d(e,"b",(function(){return m})),n.d(e,"a",(function(){return _})),n.d(e,"e",(function(){return r})),n.d(e,"f",(function(){return i})),n.d(e,"c",(function(){return o}));var r={};n.r(r),n.d(r,"doesConsentApply",(function(){return w})),n.d(r,"needsConsent",(function(){return O})),n.d(r,"needsGDPRConsent",(function(){return E})),n.d(r,"needsCCPAConsent",(function(){return j})),n.d(r,"isConsentStringCookieSet",(function(){return x})),n.d(r,"getConsentFramework",(function(){return S})),n.d(r,"getConsentFrameworkAsString",(function(){return P}));var o={};n.r(o),n.d(o,"configure",(function(){return U})),n.d(o,"init",(function(){return B})),n.d(o,"setTCFListener",(function(){return q})),n.d(o,"getInAppTCData",(function(){return K})),n.d(o,"getTCData",(function(){return J})),n.d(o,"uspApi",(function(){return Y})),n.d(o,"getUSPData",(function(){return V})),n.d(o,"setUSPDefaultData",(function(){return W}));var i={};n.r(i),n.d(i,"fetchAdPurposeConsent",(function(){return X})),n.d(i,"isEligibleForCCPA",(function(){return Q})),n.d(i,"fetchCCPAValue",(function(){return $})),n.d(i,"fetchCCPAOptOut",(function(){return tt})),n.d(i,"fetchRawVendorConsents",(function(){return et})),n.d(i,"fetchRawPublisherConsents",(function(){return nt})),n.d(i,"fetchTrackingConsent",(function(){return rt})),n.d(i,"hasConsented",(function(){return ot}));var a=n("xwdf"),c=n("8Ync"),u=n("iCr9"),s=!0,f="\ud83d\udd0f [bf consent] >>",l=Object(c.a)()?{}:Object(u.d)(window.location.search),p=function(){return!Object(c.a)()&&"true"===l["bf-consent-debug"]},h=function(){if(p()&&l["bf-consent-debug"]){var t;s&&(console.log(f,"====== BF Consent Debug Mode Enabled ====="),s=!1);for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];(t=console).log.apply(t,[f].concat(n))}},d=function(){var t=!1,e=!1,n=!1;return function(r){return"ccpa"===r?(t||(h("isDebugModeFor('".concat("ccpa","')"),"undefined"===typeof l.ccpa?"Unset -> using BF Cookie":"Force -> ".concat(l.ccpa)),t=!0),"true"===l.ccpa):"gdpr"===r?(e||(h("isDebugModeFor('".concat("gdpr","')"),"undefined"===typeof l.gdpr?"Unset -> using BF Cookie":"Force -> ".concat(l.gdpr)),e=!0),"true"===l.gdpr):(n||(h("isDebugModeFor('".concat(r,"')"),"No Setup for '".concat(r,"'")),n=!0),!1)}}(),v=[754],y=[359,360],g={INFORMATION_STORAGE:0,BASIC_ADS:1,CREATE_PERSONALISED_ADS:2,SELECT_PERSONALISED_ADS:3,CREATE_PERSONALISED_CONTENT_PROFILE:4,SELECT_PERSONALISED_CONTENT:5,MEASURE_AD_PERFORMANCE:6,MEASURE_CONTENT_PERFORMANCE:7,APPLY_MARKET_RESEARCH:8,DEVELOP_PRODUCTS:9},m="bf-rev-geo",b={"buzzfeed.bio":"30e86c5d-cf1a-40d8-b336-0b96685da11b","buzzfeed.com":"92123775-81ac-4a1b-b056-24d62d0e177f","buzzfeed.io":"39435fbf-e858-4eac-a529-5e12c567dc68","buzzfeednews.com":"38444766-23c0-4265-b9b9-49714f31124a","tasty.co":"0fb7adfb-5bc5-42bf-b659-28c1c288282c"},_={name:"consent_management_onetrust",variations:["on","off"],isEligible:function(){return!0}},w=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"ccpa";return d(t)?"true":"ccpa"===t?a.a.get("ccpa"):"gdpr"===t?a.a.get("gdpr"):"false"},O=function(){if(Object(c.a)())return!1;var t=w("gdpr"),e=w("ccpa");return"true"===t||"true"===e},E=function(){return!Object(c.a)()&&"true"===w("gdpr")},j=function(){return!Object(c.a)()&&"true"===w("ccpa")},x=function(){var t=a.a.get("eupubconsent-v2"),e=a.a.get("usprivacy");return"string"===typeof t||"string"===typeof e&&"Y"===e.charAt(2)},S=function(){return function(){!function(){for(var t,e=[],n=window;n;){try{if(n.frames.__tcfapiLocator){t=n;break}}catch(r){}if(n===window.top)break;n=n.parent}t||(!function t(){var e=n.document,r=!!n.frames.__tcfapiLocator;if(!r)if(e.body){var o=e.createElement("iframe");o.style.cssText="display:none",o.name="__tcfapiLocator",e.body.appendChild(o)}else setTimeout(t,5);return!r}(),n.__tcfapi=function(){var t,n=arguments;if(!n.length)return e;if("setGdprApplies"===n[0])n.length>3&&2===n[2]&&"boolean"===typeof n[3]&&(t=n[3],"function"===typeof n[2]&&n[2]("set",!0));else if("ping"===n[0]){var r={gdprApplies:t,cmpLoaded:!1,cmpStatus:"stub"};"function"===typeof n[2]&&n[2](r)}else e.push(n)},n.addEventListener("message",(function(t){var e="string"===typeof t.data,n={};try{n=e?JSON.parse(t.data):t.data}catch(r){}var o=n.__tcfapiCall;o&&window.__tcfapi(o.command,o.version,(function(n,r){var i={__tcfapiReturn:{returnValue:n,success:r,callId:o.callId}};e&&(i=JSON.stringify(i)),t.source.postMessage(i,"*")}),o.parameter)}),!1))}();"undefined"===typeof window.__uspapi&&(window.__uspapi=function t(){var e=arguments;typeof window.__uspapi!==t&&setTimeout((function(){"undefined"!==typeof window.__uspapi&&window.__uspapi.apply(window.__uspapi,e)}),500)});var t=window.Promise;return t.all([t.race([new t((function(t){window.__tcfapi("getInAppTCData",2,t)})),new t((function(t,e){setTimeout((function(){e("__tcfapi stub is defined, but CMP has not loaded within 10000ms")}),1e4)}))]),t.race([new t((function(t){window.__uspapi("getUSPData",1,t)})),new t((function(t,e){setTimeout((function(){e("__uspapi stub is defined, but CMP has not loaded within 10000ms")}),1e4)}))])])}},P=function(){return"(".concat(S().toString(),")();")},L=n("QsI/"),k=n("BmUA"),T=n.n(k),A=n("MMYH"),C=n("9fIP"),I=Object(A.a)((function t(){var e,n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=r.unsubscribe,i=void 0===o?function(){}:o;Object(C.a)(this,t);var a=new Promise((function(t,r){e=t,n=r}));return a.resolve=e,a.reject=n,a.unsubscribe=i,a}));function N(){N=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),c=new k(r||[]);return o(a,"_invoke",{value:x(t,n,c)}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",v={};function y(){}function g(){}function m(){}var b={};s(b,a,(function(){return this}));var _=Object.getPrototypeOf,w=_&&_(_(T([])));w&&w!==n&&r.call(w,a)&&(b=w);var O=m.prototype=y.prototype=Object.create(b);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function n(o,i,a,c){var u=l(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return n("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=S(c,r);if(u){if(u===v)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var s=l(e,n,r);if("normal"===s.type){if(o=r.done?d:"suspendedYield",s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=d,r.method="throw",r.arg=s.arg)}}}function S(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,S(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=l(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function k(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(O,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,u,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},E(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new j(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(O),s(O,u,"Generator"),s(O,a,(function(){return this})),s(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(L),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),L(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;L(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:T(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}var D="https://cdn.cookielaw.org",M=new I,R=new I;R.then((function(t){h("=== OT READY ===",t)})),M.then((function(t){h("=== CMP READY ===",t)}));var F,z=function(){if(window.location.search.includes("display-consent")){h("forcing display consent");var t=setInterval((function(){window.OneTrust&&document.getElementsByClassName("onetrust-pc-dark-filter").length>0&&(window.OneTrust.ToggleInfoDisplay(),clearInterval(t))}),500)}};function U(){F.set({useFallback:!1})}function G(){return new Promise((function(t,e){if(j()){var n=document.createElement("script");n.setAttribute("ccpa-opt-out-ids","SPD_BG"),n.setAttribute("ccpa-opt-out-geo","us"),n.setAttribute("ccpa-opt-out-lspa","false"),n.onerror=function(){e("CMP script otCCPAiab.js failed to load")},n.src="".concat(D,"/opt-out/otCCPAiab.js"),n.type="text/javascript",document.head.appendChild(n)}var r=document.createElement("script"),o=document.createElement("script"),i=function(){var t=p()?"-test":"",e=window.location.hostname;if(!e)return"".concat(b["buzzfeed.com"]).concat(t);var n=Object.keys(b).find((function(t){return e.includes(t)}));return n?"".concat(b[n]).concat(t):"".concat(b["buzzfeed.com"]).concat(t)}();h("OneTrust script ID:",i),r.setAttribute("data-domain-script",i),r.onload=function(){return t()},r.onerror=function(){e("CMP script stub failed to load")},r.src="".concat(D,"/scripttemplates/otSDKStub.js"),r.async=!0,r.type="text/javascript",o.text="function OptanonWrapper() { }",o.onerror=function(){e("CMP script OptanonWrapper failed to load")},o.type="text/javascript",document.head.appendChild(r),document.head.appendChild(o)}))}function H(){return(H=Object(L.a)(N().mark((function t(){var e;return N().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!document.querySelector('script[src*="'.concat(D,'"]'))){t.next=3;break}return t.abrupt("return");case 3:return t.prev=3,t.next=6,G();case 6:t.next=12;break;case 8:t.prev=8,t.t0=t.catch(3),h("CMP load error! throwing error..."),console.error("Error loading CMP",t.t0);case 12:e=setInterval((function(){if(window.OneTrust){clearInterval(e);var t=window.OneTrust.getGeolocationData();t&&null!==t&&void 0!==t&&t.country&&a.a.set({name:m,value:null===t||void 0===t?void 0:t.country,days:30,domain:window.location.hostname}),window.__uspapi?R.resolve({ccpaApplies:!0,gdprApples:!1,tcfapi:null,uspapi:window.__uspapi}):window.__tcfapi?R.resolve({ccpaApplies:!1,gdprApples:!0,tcfapi:window.__tcfapi,uspapi:null}):R.resolve({ccpaApplies:!1,gdprApplies:!1,tcfapi:null,uspapi:null})}}),10),z(),Promise.race([R,new Promise((function(t){var e=setInterval((function(){window.__uspapi&&(t({ccpaApplies:!0,gdprApples:!1,tcfapi:null,uspapi:window.__uspapi}),clearInterval(e))}),10)})),new Promise((function(t){var e=setInterval((function(){window.__tcfapi&&(t({ccpaApplies:!1,gdprApplies:!0,tcfapi:window.__tcfapi,uspapi:null}),clearInterval(e))}),10)})),new Promise((function(t,e){setTimeout((function(){h("CMP timed out! throwing error..."),e("CMP has not loaded within 10000ms")}),1e4)}))]).then((function(t){M.resolve(t)}));case 15:case"end":return t.stop()}}),t,null,[[3,8]])})))).apply(this,arguments)}!function(){var t;(F=new Promise((function(e){return t=e}))).set=t}();var B=T()((function(){return H.apply(this,arguments)})),q=function(){var t=Object(L.a)(N().mark((function t(e){var n,r,o;return N().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,M;case 2:if(n=t.sent,r=n.gdprApplies,o=n.tcfapi,r){t.next=8;break}return h("setTCFListener -- gdpr does not apply"),t.abrupt("return");case 8:o("addEventListener",2,(function(t,n){e({tcData:t,success:n})}));case 9:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),K=function(){var t=Object(L.a)(N().mark((function t(){var e;return N().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,M;case 2:if(e=t.sent,e.gdprApplies){t.next=7;break}return h("getInAppTCData -- gdpr does not apply"),t.abrupt("return",Promise.resolve({tcData:{gdprApplies:!1}}));case 7:return t.abrupt("return",new Promise((function(t,e){window.__tcfapi("getInAppTCData",2,(function(n,r){r?(h("getInAppTCData Success",n,r),t({tcData:n,success:r})):(h("getInAppTCData Failed",n,r),e(r))}))})));case 8:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),J=K,Y=function(){var t=Object(L.a)(N().mark((function t(e){var n;return N().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,M;case 2:if(n=t.sent,n.ccpaApplies){t.next=6;break}return t.abrupt("return",{uspData:{version:1,uspString:"1---"}});case 6:return t.abrupt("return",new Promise((function(t){window.__uspapi(e,1,(function(e,n){t({uspData:e,success:n})}))})));case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),V=function(){var t=Object(L.a)(N().mark((function t(){return N().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,M;case 2:return h("getUSPData",Y("getUSPData")),t.abrupt("return",Y("getUSPData"));case 4:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),W=function(){var t=Object(L.a)(N().mark((function t(){return N().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,M;case 2:return t.abrupt("return",Y("setUspDftData"));case 3:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();function Z(){Z=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),c=new k(r||[]);return o(a,"_invoke",{value:x(t,n,c)}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",v={};function y(){}function g(){}function m(){}var b={};s(b,a,(function(){return this}));var _=Object.getPrototypeOf,w=_&&_(_(T([])));w&&w!==n&&r.call(w,a)&&(b=w);var O=m.prototype=y.prototype=Object.create(b);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function n(o,i,a,c){var u=l(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return n("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=S(c,r);if(u){if(u===v)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var s=l(e,n,r);if("normal"===s.type){if(o=r.done?d:"suspendedYield",s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=d,r.method="throw",r.arg=s.arg)}}}function S(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,S(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=l(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function k(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(O,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,u,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},E(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new j(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(O),s(O,u,"Generator"),s(O,a,(function(){return this})),s(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(L),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),L(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;L(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:T(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}var X=function(){var t=Object(L.a)(Z().mark((function t(){var e,n,r,o,i,a,c,u=arguments;return Z().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=u.length>0&&void 0!==u[0]?u[0]:null,t.next=3,K();case 3:if(n=t.sent,r=n.success,!(o=n.tcData)||!("gdprApplies"in o)||o.gdprApplies){t.next=8;break}return t.abrupt("return",!0);case 8:if("1"===(i=o.purpose.consents)[g.INFORMATION_STORAGE]){t.next=11;break}return t.abrupt("return",!1);case 11:return a=o.vendor.consents||"",c=null===e||e&&a&&e.every((function(t){return"1"===a[t]})),t.abrupt("return",r&&i&&"1"===i[g.INFORMATION_STORAGE]&&"1"===i[g.CREATE_PERSONALISED_ADS]&&"1"===i[g.SELECT_PERSONALISED_ADS]&&"1"===i[g.APPLY_MARKET_RESEARCH]&&"1"===i[g.DEVELOP_PRODUCTS]&&c);case 14:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),Q=function(){var t=Object(L.a)(Z().mark((function t(){var e,n,r,o;return Z().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(h("isEligibleForCCPA...",j()),j()){t.next=3;break}return t.abrupt("return",!1);case 3:return t.next=5,V();case 5:return e=t.sent,n=e.success,r=e.uspData,o=r.uspString,h("uspData",r,n),t.abrupt("return","1---"!==o);case 11:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),$=function(){var t=Object(L.a)(Z().mark((function t(){return Z().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Q();case 2:return t.sent&&document.querySelector("html").classList.add("show-ccpa"),t.abrupt("return",a.a.get("usprivacy"));case 5:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),tt=function(){var t=Object(L.a)(Z().mark((function t(){var e;return Z().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,$();case 2:return e=t.sent,t.abrupt("return","string"===typeof e&&"Y"===e.charAt(2));case 4:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),et=function(){var t=Object(L.a)(Z().mark((function t(e){var n,r,o,i;return Z().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,K();case 2:if(n=t.sent,r=n.tcData,o=r.vendor.consents,e){t.next=7;break}return t.abrupt("return",o);case 7:return i={},e.forEach((function(t){i[t]=o[t]})),t.abrupt("return",i);case 10:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),nt=function(){var t=Object(L.a)(Z().mark((function t(e){var n,r,o,i;return Z().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,K();case 2:if(n=t.sent,r=n.tcData,o=r.publisher.consents,e){t.next=7;break}return t.abrupt("return",o);case 7:return i={},e.forEach((function(t){i[t]=o[t]})),t.abrupt("return",i);case 10:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),rt=function(){var t=Object(L.a)(Z().mark((function t(){var e,n,r,o,i,a,c,u=arguments;return Z().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=u.length>0&&void 0!==u[0]?u[0]:null,t.next=3,tt();case 3:if(!t.sent){t.next=6;break}return t.abrupt("return",!1);case 6:if(E()){t.next=8;break}return t.abrupt("return",!0);case 8:return t.next=10,K();case 10:if(n=t.sent,r=n.tcData,o=n.success,t.prev=13,!("gdprApplies"in r)||r.gdprApplies){t.next=16;break}return t.abrupt("return",!0);case 16:return i=r.vendor.consents||"",a=r.purpose.consents||"",c=null===e||e&&i&&e.every((function(t){return"1"===i[t]})),t.abrupt("return",o&&a&&"1"===a[g.INFORMATION_STORAGE]&&"1"===a[g.CREATE_PERSONALISED_CONTENT_PROFILE]&&c);case 22:return t.prev=22,t.t0=t.catch(13),t.abrupt("return",!1);case 25:case"end":return t.stop()}}),t,null,[[13,22]])})));return function(){return t.apply(this,arguments)}}(),ot=function(){var t=Object(L.a)(Z().mark((function t(e){var n,r;return Z().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n={ads:X,google:X.bind(null,v),tracking:rt,permutive:rt.bind(null,y)},!(r=n[e])){t.next=8;break}return t.next=5,r();case 5:t.t0=t.sent,t.next=9;break;case 8:t.t0=!1;case 9:return t.abrupt("return",t.t0);case 10:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();e.d={framework:r,rules:i,api:o}},JWSz:function(t,e,n){t.exports={controlContainer:"controlContainer__2KmZKb8OVF",label:"label__FTYiQu4VWr"}},K2uR:function(t,e,n){"use strict";n.d(e,"a",(function(){return E}));var r=n("ERkP"),o=n.n(r),i=(n("b1ot"),"cdnImage__3mJlv"),a="gif__playing__UA_gM",c="gifPlayButton__2SCJp",u="gifPlayButton_cta__2JyMD",s="gifPlayButton_bg__FCgUQ",f="gifPlayButton_circle__2oy9F",l="gifPlayButton_pauseCta__3gQC2",p="gif__loading__mFPFP",h="gifPlaceholder__2Z0Xs",d="gifPlayButton_icon__1yKof",v=n("E7fa"),y=n("kl+4"),g=function(t){var e=t.alt,n=void 0===e?"":e,i=t.height,a=void 0===i?"":i,p=t.id,g=void 0===p?Math.random().toString(36).replace(/[^a-z]+/g,""):p,m=t.isPlaying,b=void 0===m||m,_=t.setIsLoading,w=void 0===_?function(){}:_,O=t.setIsPlaying,E=void 0===O?function(){}:O,j=t.src,x=void 0===j?"":j,S=t.width,P=void 0===S?"":S,L=Object(r.useRef)(null),k="".concat(b?"Pause":"Play"," GIF"),T="gif-play-button-".concat(g);function A(){w(!1)}if(Object(r.useEffect)((function(){L&&L.current&&x&&(L.current.setAttribute("muted",""),L.current.readyState>=2?w(!1):L.current.addEventListener("loadeddata",A))}),[x]),!x)return o.a.createElement("div",{className:h});var C=Object(y.e)(x),I=Object(v.a)(C,2),N=I[0],D=I[1].filter((function(t){return!t.includes("output")})).concat(["output-format=mp4","output-quality=auto"]);return o.a.createElement(o.a.Fragment,null,o.a.createElement("button",{type:"button",className:c,onClick:function(){if(L&&L.current){var t=L.current.paused,e=t?"play":"pause";"function"===typeof L.current[e]&&(L.current[e](),E(t))}}},o.a.createElement("svg",{"aria-labelledby":T,viewBox:"0 0 48 48",className:d,fill:"none",role:"img"},o.a.createElement("title",{id:T},k),o.a.createElement("circle",{className:s,cx:"24",cy:"24",r:"20",fill:"rgba(0, 0, 0, .25)"}),o.a.createElement("circle",{className:f,cx:"24",cy:"24",r:"18",stroke:"rgba(255, 255, 255, 1)",strokeWidth:"4"}),o.a.createElement("path",{className:l,d:"M23.25,30V19.33H20.5V30ZM28,19.33H25.17V30h2.76Zm-.07,2.4,0,1.66"}),o.a.createElement("path",{className:u,d:"M17.416 30.192c2.016 0 3.584-.816 4.72-2.08v-4.24h-5.152v2.416h2.4v.832c-.384.32-1.184.64-1.968.64-1.744 0-2.976-1.344-2.976-3.088 0-1.76 1.232-3.088 2.976-3.088 1.04 0 1.856.656 2.256 1.36l2.304-1.2c-.736-1.328-2.176-2.592-4.56-2.592-3.184 0-5.776 2.128-5.776 5.52 0 3.392 2.592 5.52 5.776 5.52zM26.504 30V19.328h-2.752V30h2.752zm4.672 0v-4.208h4.96v-2.4h-4.96v-1.664h5.072v-2.4h-7.824V30h2.752z"}))),o.a.createElement("video",{"aria-label":"GIF: ".concat(n),autoPlay:!0,height:a,loop:!0,muted:!0,playsInline:!0,ref:L,width:P},o.a.createElement("source",{type:"video/mp4",src:Object(y.c)(N,D)})))},m=/data:image\/\w+;base64/g,b=/\S+(?:\s?\S+)?/g;function _(t){var e=t.match(b)||[],n=[],r=[];return e.forEach((function(t){var e=t.split(" "),o=Object(v.a)(e,2),i=o[0],a=o[1],c=void 0===a?"":a,u=Object(y.a)(i),s=i&&i.match(m),f=Object(y.e)(i),l=Object(v.a)(f,2),p=l[0],h=l[1].filter((function(t){return!t.includes("output")})),d=s?h:h.concat(["output-format=jpg","output-quality=auto"]),g=h.concat(["output-format=auto","output-quality=auto"]);r.push("".concat(Object(y.c)(p,d)," ").concat(c).trim()),u||s||n.push("".concat(Object(y.c)(p,g)," ").concat(c).trim())})),[r.join(" "),n.join(" ")]}var w=function(t){var e=t.alt,n=void 0===e?"":e,i=t.fetchpriority,a=void 0===i?"auto":i,c=t.height,u=void 0===c?"":c,s=t.loading,f=void 0===s?"eager":s,l=t.sizes,p=void 0===l?"":l,h=t.src,d=void 0===h?"":h,y=t.srcSet,g=void 0===y?"":y,m=t.width,b=void 0===m?"":m,w=Object(r.useRef)(null),O=_(d),E=Object(v.a)(O,1)[0],j=_(g||d),x=Object(v.a)(j,2),S=x[0],P=x[1];return o.a.createElement("picture",null,P&&o.a.createElement("source",{srcSet:P,sizes:p||null}),o.a.createElement("img",{alt:n,height:u,loading:f,fetchpriority:a,ref:w,sizes:p||null,src:E,srcSet:g?S:null,width:b}))};var O="data:image/gif;base64,R0lGODlhAQABAIAAAP///////yH5BAEAAAEALAAAAAABAAEAAAICTAEAOw==",E=function(t){var e=t.alt,n=void 0===e?"":e,c=t.className,u=void 0===c?"":c,s=t.height,f=void 0===s?"":s,l=t.id,h=void 0===l?"":l,d=t.lazy,v=void 0!==d&&d,m=t.loading,b=void 0===m?"eager":m,_=t.fetchpriority,E=void 0===_?"auto":_,j=t.sizes,x=void 0===j?"":j,S=t.src,P=void 0===S?"":S,L=t.srcSet,k=void 0===L?"":L,T=t.width,A=void 0===T?"":T,C=Object(r.useState)(!0),I=C[0],N=C[1],D=Object(r.useState)(!0),M=D[0],R=D[1],F=Object(y.a)(P),z=F&&!Object(y.d)(),U=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.pageId,n=t.once,o=void 0!==n&&n,i=t.root,a=void 0===i?null:i,c=t.rootMargin,u=void 0===c?"0px":c,s=t.threshold,f=void 0===s?0:s,l=t.defaultValue,p=void 0!==l&&l,h=Object(r.useState)(null),d=h[0],v=h[1],y=Object(r.useState)(p),g=y[0],m=y[1],b=Object(r.useRef)(null);return Object(r.useEffect)((function(){return d?(b.current?b.current.disconnect():b.current=new IntersectionObserver((function(t){var e;null!==(e=t[0])&&void 0!==e&&e.isIntersecting?(m(!0),o&&b.current&&b.current.disconnect()):g&&m(!1)}),{root:a,rootMargin:u,threshold:f}),b.current.observe(d),function(){b.current.disconnect()}):function(){}}),[d,e]),{isIntersecting:g,setObservable:v}}({rootMargin:"300px",once:!0}),G=U.isIntersecting,H=U.setObservable,B="lazy"===b&&(z||!Object(y.f)())||v&&(z||"lazy"!==b);if(!P)return null;var q=u?u.split(" "):[];F&&(q.push("js-gif-container",i),M&&z&&q.push(I?p:a));var K=B?G?P:O:P,J=K===O;return o.a.createElement("div",{className:q.join(" "),ref:B?H:null},z&&o.a.createElement(g,{alt:n,height:"".concat(f),id:h,isPlaying:M,setIsLoading:N,setIsPlaying:R,src:J?"":K,width:"".concat(A)}),!z&&o.a.createElement(w,{alt:n,height:"".concat(f),loading:B?null:b,fetchpriority:E,sizes:J?null:x,src:K,srcSet:J?null:k,width:"".concat(A)}))}},KpE9:function(t,e,n){"use strict";var r=String.prototype.indexOf;t.exports=function(t){return r.call(this,t,arguments[1])>-1}},Lix2:function(t,e,n){t.exports={fieldsetInner:"fieldsetInner__c4r-Jw4JwQ",legend:"legend__13mgde3Sff"}},MC86:function(t,e,n){"use strict";var r=n("gShS"),o=Math.max;t.exports=function(t){return o(0,r(t))}},MH1b:function(t,e,n){"use strict";var r=n("FBSS");t.exports=function(){var t=0,e=[],n=[];return{get:function(t){var o=r.call(e,t[0]);return-1===o?null:n[o]},set:function(r){return e.push(r[0]),n.push(++t),t},delete:function(t){var o=r.call(n,t);-1!==o&&(e.splice(o,1),n.splice(o,1))},clear:function(){e=[],n=[]}}}},MNyZ:function(t,e,n){"use strict";(function(t,r){n.d(e,"b",(function(){return i})),n.d(e,"a",(function(){return a}));var o=n("lwDF");function i(){return!Object(o.a)()&&"[object process]"===Object.prototype.toString.call("undefined"!==typeof t?t:0)}function a(t,e){return t.require(e)}}).call(this,n("F63i"),n("cyaT")(t))},MWCF:function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return f}));var r=n("zjfJ"),o=n("QsI/"),i=n("TWeG"),a=n.n(i);function c(){c=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function f(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var i=e&&e.prototype instanceof g?e:g,a=Object.create(i.prototype),c=new T(r||[]);return o(a,"_invoke",{value:S(t,n,c)}),a}function p(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var h="suspendedStart",d="executing",v="completed",y={};function g(){}function m(){}function b(){}var _={};f(_,a,(function(){return this}));var w=Object.getPrototypeOf,O=w&&w(w(A([])));O&&O!==n&&r.call(O,a)&&(_=O);var E=b.prototype=g.prototype=Object.create(_);function j(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function n(o,i,a,c){var u=p(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return n("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function S(e,n,r){var o=h;return function(i,a){if(o===d)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=P(c,r);if(u){if(u===y)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===h)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=d;var s=p(e,n,r);if("normal"===s.type){if(o=r.done?v:"suspendedYield",s.arg===y)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=v,r.method="throw",r.arg=s.arg)}}}function P(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var i=p(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,y):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function L(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(L,this),this.reset(!0)}function A(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return m.prototype=b,o(E,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:m,configurable:!0}),m.displayName=f(b,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,f(t,s,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},j(x.prototype),f(x.prototype,u,(function(){return this})),e.AsyncIterator=x,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new x(l(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),f(E,s,"Generator"),f(E,a,(function(){return this})),f(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=A,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),k(n),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;k(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:A(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),y}},e}function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(Object(n),!0).forEach((function(e){Object(r.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var f=function(){var e=Object(o.a)(c().mark((function e(n){var r,o,i,u,f,l,p,h,d=arguments;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=d.length>1&&void 0!==d[1]?d[1]:{},o=r.req,i=r.method,u=o?"http://localhost:".concat(t.env.PORT):"",f=(o||{}).headers,l=s(s({},f&&{headers:f}),i&&{method:i}),e.next=6,a()("".concat(u).concat(n),l);case 6:if((p=e.sent).ok){e.next=13;break}return e.next=10,p.text();case 10:h=e.sent;try{h=JSON.parse(h).message}catch(c){}return e.abrupt("return",{error:{error:h,statusCode:p.status}});case 13:return e.abrupt("return",p.json());case 14:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()}).call(this,n("F63i"))},Mqvj:function(t,e,n){"use strict";var r=n("HX/o"),o=n("oB+F").Symbol;t.exports=function(t){return Object.defineProperties(t,{hasInstance:r("",o&&o.hasInstance||t("hasInstance")),isConcatSpreadable:r("",o&&o.isConcatSpreadable||t("isConcatSpreadable")),iterator:r("",o&&o.iterator||t("iterator")),match:r("",o&&o.match||t("match")),replace:r("",o&&o.replace||t("replace")),search:r("",o&&o.search||t("search")),species:r("",o&&o.species||t("species")),split:r("",o&&o.split||t("split")),toPrimitive:r("",o&&o.toPrimitive||t("toPrimitive")),toStringTag:r("",o&&o.toStringTag||t("toStringTag")),unscopables:r("",o&&o.unscopables||t("unscopables"))})}},MrsR:function(t,e,n){"use strict";t.exports=n("0tOi")()?Array.from:n("OPr5")},NnKD:function(t,e,n){"use strict";var r=Object.prototype.toString,o=r.call(function(){return arguments}());t.exports=function(t){return r.call(t)===o}},Ntd4:function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));var r=n("xwdf"),o=(n("8o0J"),function(){return r.a.get("bf-xdomain-session-uuid","")})},O3Et:function(t,e,n){"use strict";n.d(e,"a",(function(){return c}));var r=n("VX0Q"),o=n("ChH/"),i=n("4h0E"),a=n("9rgT"),c=function(){return Object(o.a)((function t(e){var n=e.cookieName,o=e.daysExpiry,c=e.env,u=e.namespace,s=e.sourceOfTruthDomain,f=e.throttleTimer,l=void 0===f?null:f,p=e.secureOnly,h=void 0===p||p,d=e.localDomain,v=void 0===d?Object(a.a)():d;Object(r.a)(this,t),c="live"===c?"prod":c,this.xDomainCookies=new i.a({sourceOfTruthDomain:s||Object(a.b)(c),namespace:u,localDomain:v,env:c}),this.cookieName=n,this.daysExpiry=o,this.secureOnly=h,this.throttleTimer=l,this.inMemoryValue=null}),[{key:"get",value:function(){var t=this;return this.throttle?Promise.resolve(this.inMemoryValue):this.xDomainCookies.get(this.cookieName).then((function(e){return t.inMemoryValue=e,t.resetThrottle(),e}))}},{key:"set",value:function(t){return this.inMemoryValue=t,this.xDomainCookies.set({name:this.cookieName,value:t,days:this.daysExpiry,secureOnly:this.secureOnly})}},{key:"resetThrottle",value:function(){var t=this;this.throttleTimer&&(this.throttle=setTimeout((function(){t.throttle=null}),this.throttleTimer))}}])}()},O6my:function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));var r=n("ERkP"),o=n("pir2"),i=n("kZhk");function a(){var t=Object(r.useContext)(o.ProfileUserContext);return Object(i.e)({context_page_type:"user",context_page_id:t.uuid})}},O94r:function(t,e,n){var r;!function(){"use strict";var n={}.hasOwnProperty;function o(){for(var t="",e=0;e<arguments.length;e++){var n=arguments[e];n&&(t=a(t,i(n)))}return t}function i(t){if("string"===typeof t||"number"===typeof t)return t;if("object"!==typeof t)return"";if(Array.isArray(t))return o.apply(null,t);if(t.toString!==Object.prototype.toString&&!t.toString.toString().includes("[native code]"))return t.toString();var e="";for(var r in t)n.call(t,r)&&t[r]&&(e=a(e,r));return e}function a(t,e){return e?t?t+" "+e:t+e:t}t.exports?(o.default=o,t.exports=o):void 0===(r=function(){return o}.apply(e,[]))||(t.exports=r)}()},ODk3:function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));var r=n("Q92E");function o(t,e){if(t){if("string"===typeof t)return Object(r.a)(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Object(r.a)(t,e):void 0}}},OPr5:function(t,e,n){"use strict";var r=n("PyoE").iterator,o=n("NnKD"),i=n("QLbZ"),a=n("MC86"),c=n("U4eW"),u=n("J2Kb"),s=n("UBbi"),f=n("dAGX"),l=Array.isArray,p=Function.prototype.call,h={configurable:!0,enumerable:!0,writable:!0,value:null},d=Object.defineProperty;t.exports=function(t){var e,n,v,y,g,m,b,_,w,O,E=arguments[1],j=arguments[2];if(t=Object(u(t)),s(E)&&c(E),this&&this!==Array&&i(this))e=this;else{if(!E){if(o(t))return 1!==(g=t.length)?Array.apply(null,t):((y=new Array(1))[0]=t[0],y);if(l(t)){for(y=new Array(g=t.length),n=0;n<g;++n)y[n]=t[n];return y}}y=[]}if(!l(t))if(void 0!==(w=t[r])){for(b=c(w).call(t),e&&(y=new e),_=b.next(),n=0;!_.done;)O=E?p.call(E,j,_.value,n):_.value,e?(h.value=O,d(y,n,h)):y[n]=O,_=b.next(),++n;g=n}else if(f(t)){for(g=t.length,e&&(y=new e),n=0,v=0;n<g;++n)O=t[n],n+1<g&&(m=O.charCodeAt(0))>=55296&&m<=56319&&(O+=t[++n]),O=E?p.call(E,j,O,v):O,e?(h.value=O,d(y,v,h)):y[v]=O,++v;g=v}if(void 0===g)for(g=a(t.length),e&&(y=new e(g)),n=0;n<g;++n)O=E?p.call(E,j,t[n],n):t[n],e?(h.value=O,d(y,n,h)):y[n]=O;return e&&(h.value=null,y.length=g),y}},OfTf:function(t,e,n){"use strict";t.exports=function(){return"object"===typeof globalThis&&(!!globalThis&&globalThis.Array===Array)}},PMPH:function(t,e,n){"use strict";function r(t,e){var n=t.match(e);return n&&n.length?n[0]:null}e.a={getBuzzfeedSubdomainOrWildcard:function(t){var e=r(t,"(dev|stage|www).(buzzfeed|buzzfeednews).(com|io)$");return e||r(t,".?[a-z]+.[a-z]+$")},get:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n="".concat(t,"=");if("undefined"===typeof document)return e;for(var r=document.cookie.split(";"),o=0;o<r.length;o++){for(var i=r[o];" "===i.charAt(0);)i=i.substring(1,i.length);if(0===i.indexOf(n))return i.substring(n.length,i.length)}return e},set:function(t){var e=t.name,n=t.value,r=t.days,o=t.domain,i="";if(r){var a=new Date;a.setTime(a.getTime()+24*r*60*60*1e3),i="; expires=".concat(a.toGMTString())}var c="";return void 0!==o&&(c="; domain=".concat(o)),document.cookie="".concat(e,"=").concat(n).concat(i).concat(c,"; path=/")},remove:function(t,e){return this.set({name:t,value:"",days:-1,domain:e})}}},PmSW:function(t,e,n){"use strict";var r=n("7poC");t.exports=function(t){try{return t&&r(t.toString)?t.toString():String(t)}catch(e){throw new TypeError("Passed argument cannot be stringifed")}}},PyoE:function(t,e,n){"use strict";t.exports=n("arNB")()?n("oB+F").Symbol:n("dRla")},"Q1+2":function(t,e){t.exports=function(){var t,e,n=[],r=Array.prototype.slice.call(arguments),o=r.length,i=0;if(!o)throw new Error("zip requires at least one argument");for(t=0;t<o;t++){if(!Array.isArray(r[t]))throw new Error("all arguments must be arrays");var a=r[t].length;a>i&&(i=a)}for(t=0;t<i;t++){var c=[];for(e=0;e<o;e++){if(!Array.isArray(r[e]))throw new Error("all arguments must be arrays");c[e]=r[e][t]}n[t]=c}return n}},Q1AV:function(t,e,n){"use strict";function r(t){if(Array.isArray(t))return t}n.d(e,"a",(function(){return r}))},Q8aG:function(t,e,n){"use strict";t.exports=n("TKg6")()?String.prototype.contains:n("KpE9")},Q92E:function(t,e,n){"use strict";function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}n.d(e,"a",(function(){return r}))},QCvc:function(t,e,n){"use strict";n.d(e,"d",(function(){return i})),n.d(e,"b",(function(){return a})),n.d(e,"a",(function(){return c})),n.d(e,"c",(function(){return u}));var r=n("zygG"),o=n("6S4M");function i(){var t=Object(o.a)(),e=t.split("-"),n=Object(r.a)(e,2),i=n[0],a=n[1];return{edition:t,language:i,country:a,locale:i&&a?"".concat(i,"_").concat(a.toUpperCase()):i}}function a(t){return{mx:"es"}[t]||"en"}function c(t){return{"en-uk":"en-gb","en-us":"en","es-mx":"es","ja-jp":"ja"}[t]||t}function u(){return Object(o.b)()}},QLbZ:function(t,e,n){"use strict";var r=Object.prototype.toString,o=RegExp.prototype.test.bind(/^[object [A-Za-z0-9]*Function]$/);t.exports=function(t){return"function"===typeof t&&o(r.call(t))}},"R/i5":function(t,e,n){"use strict";n.d(e,"a",(function(){return c}));var r=n("9fIP"),o=n("MMYH"),i=n("xwdf"),a=function(t){return t.match(/^stage\./)?"https://".concat(t):"https://www.".concat(t)},c=function(){return Object(o.a)((function t(e){var n=this,o=e.sourceOfTruthDomain,i=e.localDomain,a=e.namespace,c=e.env,u=void 0===c?"dev":c,s=e.updateInterval,f=void 0===s?3e5:s,l=e.iframeTimeout,p=void 0===l?3e3:l;Object(r.a)(this,t),this.sourceOfTruthDomain=o,this.localDomain=i,this.env=u,this.namespace=a,this.iframeTimeout=p,this.cookies={},o!==i&&this.initIframe().then((function(){setInterval(n.updateFromIframe.bind(n),f)})).catch((function(){}))}),[{key:"get",value:function(t){var e=this;return this.sourceOfTruthDomain===this.localDomain?Promise.resolve(i.a.get(t)):this.initIframe().then((function(){return e.cookies[t]||i.a.get(t)})).catch((function(){return i.a.get(t)}))}},{key:"set",value:function(t){var e=this,n=t.name,r=t.value,o=t.days,c=t.secureOnly,u=void 0===c||c;i.a.set({name:n,value:r,days:o,domain:this.localDomain}),this.sourceOfTruthDomain!==this.localDomain&&this.initIframe().then((function(){var t={namespace:e.namespace,msgType:"destination-sync-write",cookieName:n,cookieVal:r,expiresDays:o,secureOnly:u},i=a(e.sourceOfTruthDomain);e.iframe.contentWindow.postMessage(JSON.stringify(t),i)})).catch((function(){return i.a.set({name:n,value:r,days:o,domain:e.localDomain})}))}},{key:"cleanup",value:function(){if(this.boundOnMessage&&window.removeEventListener("message",this.boundOnMessage),this.iframe){var t=new ErrorEvent({message:"XDomainCookies were cleaned up before ready"});this.iframe.dispatchEvent(t),this.iframe.remove()}this.iframeReady=null}},{key:"initIframe",value:function(){var t=this;if(this.iframeReady)return this.iframeReady;var e,n=new Promise((function(e,n){t.boundOnMessage=function(n){t.onMessage(n,e)},window.addEventListener("message",t.boundOnMessage),t.createIframe(n)}));return this.iframeReady=Promise.race([(e=this.iframeTimeout,new Promise((function(t,n){var r={type:"timeout",msg:"".concat(e,"ms timeout exceeded")};setTimeout((function(){return n(r)}),e)}))),n]).catch((function(e){throw"prod"===t.env&&window.raven&&window.raven.captureException("timeout"===e.type?new Error("Destination Sync: ".concat(e.msg)):e),console.error(e),e})),this.iframeReady}},{key:"createIframe",value:function(t){var e="xdomaincookies-".concat(this.namespace),n=document.getElementById(e);if(n)return n.addEventListener("error",(function(e){t(e)})),this.iframe=n,void(this.iframe.dataset.loaded&&this.updateFromIframe());var r=JSON.stringify({namespace:this.namespace,windowOrigin:window.location.origin}),o=document.createElement("iframe");o.style.display="none",o.addEventListener("error",(function(e){t(e)})),o.id=e,o.src=function(t,e){return"".concat(a(t),"/").concat("destination-sync.html","#").concat(encodeURIComponent(e))}(this.sourceOfTruthDomain,r),this.iframe=o,document.body.appendChild(o)}},{key:"updateFromIframe",value:function(){var t={namespace:this.namespace,msgType:"destination-sync-read"},e=a(this.sourceOfTruthDomain);this.iframe.contentWindow.postMessage(JSON.stringify(t),e)}},{key:"onMessage",value:function(t,e){var n={};try{n=JSON.parse(t.data)}catch(r){}n.namespace===this.namespace&&("destination-sync-init"===n.msgType&&(this.iframe.dataset.loaded=!0),"destination-sync-init"!==n.msgType&&"destination-sync-read"!==n.msgType||(this.cookies=n.cookies),e())}}])}()},SE2S:function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));var r=n("ERkP"),o=n.n(r).a.createContext({});o.Provider},SiiK:function(t,e,n){"use strict";var r=n("U4eW"),o=n("J4Mq"),i=n("pmLj"),a=Function.prototype.apply;i.dispose=function(t,e,n){var c;if(r(t),n.async&&i.async||n.promise&&i.promise)return e.on("deleteasync",c=function(e,n){a.call(t,null,n)}),void e.on("clearasync",(function(t){o(t,(function(t,e){c(e,t)}))}));e.on("delete",c=function(e,n){t(n)}),e.on("clear",(function(t){o(t,(function(t,e){c(e,t)}))}))}},Sr99:function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return u})),n.d(e,"b",(function(){return s}));var r=n("E3KH"),o=n("MNyZ"),i={nowSeconds:function(){return Date.now()/1e3}};var a=Object(o.b)()?function(){try{return Object(o.a)(t,"perf_hooks").performance}catch(e){return}}():function(){var t=Object(r.a)().performance;if(t&&t.now)return{now:function(){return t.now()},timeOrigin:Date.now()-t.now()}}(),c=void 0===a?i:{nowSeconds:function(){return(a.timeOrigin+a.now())/1e3}},u=i.nowSeconds.bind(i),s=c.nowSeconds.bind(c);!function(){var t=Object(r.a)().performance;if(t&&t.now){var e=t.now(),n=Date.now(),o=t.timeOrigin?Math.abs(t.timeOrigin+e-n):36e5,i=o<36e5,a=t.timing&&t.timing.navigationStart,c="number"===typeof a?Math.abs(a+e-n):36e5;return i||c<36e5?o<=c?("timeOrigin",t.timeOrigin):("navigationStart",a):("dateNow",n)}"none"}()}).call(this,n("cyaT")(t))},T8Fm:function(t,e,n){"use strict";n.d(e,"a",(function(){return l}));var r=n("cxan"),o=n("HbGN"),i=n("ERkP"),a=n.n(i),c=n("jvFD"),u=n.n(c),s=["href","as","replace","scroll","shallow","passHref","prefetch"],f=a.a.createElement,l=Object(i.forwardRef)((function(t,e){var n=t.href,i=t.as,a=t.replace,c=t.scroll,l=t.shallow,p=t.passHref,h=t.prefetch,d=Object(o.a)(t,s);return f(u.a,{href:n,as:i,replace:a,scroll:c,shallow:l,passHref:p,prefetch:h},f("a",Object(r.a)({},d,{ref:e})))}))},TKg6:function(t,e,n){"use strict";var r="razdwatrzy";t.exports=function(){return"function"===typeof r.contains&&(!0===r.contains("dwa")&&!1===r.contains("foo"))}},TWeG:function(t,e,n){"use strict";var r=self.fetch.bind(self);t.exports=r,t.exports.default=t.exports},Tjm3:function(t,e,n){"use strict";(function(e,n){var r=function(t){if("function"!==typeof t)throw new TypeError(t+" is not a function");return t},o=function(t){var e,n,o=document.createTextNode(""),i=0;return new t((function(){var t;if(e)n&&(e=n.concat(e));else{if(!n)return;e=n}if(n=e,e=null,"function"===typeof n)return t=n,n=null,void t();for(o.data=i=++i%2;n;)t=n.shift(),n.length||(n=null),t()})).observe(o,{characterData:!0}),function(t){r(t),e?"function"===typeof e?e=[e,t]:e.push(t):(e=t,o.data=i=++i%2)}};t.exports=function(){if("object"===typeof e&&e&&"function"===typeof e.nextTick)return e.nextTick;if("function"===typeof queueMicrotask)return function(t){queueMicrotask(r(t))};if("object"===typeof document&&document){if("function"===typeof MutationObserver)return o(MutationObserver);if("function"===typeof WebKitMutationObserver)return o(WebKitMutationObserver)}return"function"===typeof n?function(t){n(r(t))}:"function"===typeof setTimeout||"object"===typeof setTimeout?function(t){setTimeout(r(t),0)}:null}()}).call(this,n("F63i"),n("/Qos").setImmediate)},U4eW:function(t,e,n){"use strict";t.exports=function(t){if("function"!==typeof t)throw new TypeError(t+" is not a function");return t}},UBbi:function(t,e,n){"use strict";var r=n("I9c2")();t.exports=function(t){return t!==r&&null!==t}},Uj0u:function(t,e,n){t.exports={internetPoints:"internetPoints__3tGtXF2v1K",sparklesIcon:"sparklesIcon__3nUrQxEhJI",pointsValue:"pointsValue__8_B1IsnHwf",pointsLabel:"pointsLabel__34kTMY8B0C"}},VNia:function(t,e,n){"use strict";var r=Array.prototype.forEach,o=Object.create;t.exports=function(t){var e=o(null);return r.call(arguments,(function(t){e[t]=!0})),e}},VX0Q:function(t,e,n){"use strict";function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}n.d(e,"a",(function(){return r}))},VZI5:function(t,e,n){"use strict";var r=n("JIjG"),o=/[\n\r\u2028\u2029]/g;t.exports=function(t){var e=r(t);return e.length>100&&(e=e.slice(0,99)+"\u2026"),e=e.replace(o,(function(t){return JSON.stringify(t).slice(1,-1)}))}},WHdK:function(t,e,n){"use strict";var r=n("MC86");t.exports=function(t,e,n){var o;return isNaN(t)?(o=e)>=0?n&&o?o-1:o:1:!1!==t&&r(t)}},"XWg/":function(t,e,n){"use strict";var r=n("J2Kb"),o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols;t.exports=function(t,e){var n,u=Object(r(e));if(t=Object(r(t)),a(u).forEach((function(r){try{o(t,r,i(e,r))}catch(a){n=a}})),"function"===typeof c&&c(u).forEach((function(r){try{o(t,r,i(e,r))}catch(a){n=a}})),void 0!==n)throw n;return t}},Xkeh:function(t,e,n){"use strict";n.d(e,"a",(function(){return c}));var r=n("ERkP"),o=n("JNgv"),i=n("ds4U"),a=n("SE2S").a;function c(){var t=Object(r.useContext)(a).experiments;return Object(r.useEffect)((function(){if(t.loaded&&!t.stale){var e=Object(i.b)(t,o.a.name);o.c.configure({useFallback:!e})}}),[t.loaded,t.stale]),Object(r.useEffect)((function(){o.c.init()}),[]),null}},YPse:function(t,e,n){"use strict";function r(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}n.d(e,"a",(function(){return r}))},YcZG:function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.CDN_URL?window.CDN_URL:window.location.href;return-1!==t.search("[?&]?s=mobile_app([&#]|$)")}},YdKM:function(t,e,n){"use strict";var r=n("HX/o"),o=n("hvIx"),i=Object.create(null);t.exports=function(t){return Object.defineProperties(t,{for:r((function(e){return i[e]?i[e]:i[e]=t(String(e))})),keyFor:r((function(t){var e;for(e in o(t),i)if(i[e]===t)return e}))})}},"Yt/R":function(t,e,n){"use strict";t.exports=function(t){return!!t&&("symbol"===typeof t||!!t.constructor&&("Symbol"===t.constructor.name&&"Symbol"===t[t.constructor.toStringTag]))}},Yv2q:function(t,e,n){"use strict";var r=n("UBbi"),o=Array.prototype.forEach,i=Object.create,a=function(t,e){var n;for(n in t)e[n]=t[n]};t.exports=function(t){var e=i(null);return o.call(arguments,(function(t){r(t)&&a(Object(t),e)})),e}},ZpqU:function(t,e,n){"use strict";var r,o,i,a,c=n("MC86"),u=function(t,e){return e};try{Object.defineProperty(u,"length",{configurable:!0,writable:!1,enumerable:!1,value:1})}catch(s){}1===u.length?(r={configurable:!0,writable:!1,enumerable:!1},o=Object.defineProperty,t.exports=function(t,e){return e=c(e),t.length===e?t:(r.value=e,o(t,"length",r))}):(a=n("XWg/"),i=function(){var t=[];return function(e){var n,r=0;if(t[e])return t[e];for(n=[];e--;)n.push("a"+(++r).toString(36));return new Function("fn","return function ("+n.join(", ")+") { return fn.apply(this, arguments); };")}}(),t.exports=function(t,e){var n;if(e=c(e),t.length===e)return t;n=i(e)(t);try{a(n,t)}catch(s){}return n})},Zxt8:function(t,e,n){"use strict";n.d(e,"a",(function(){return c}));var r=n("ERkP"),o=n("lOqH"),i=n("kZhk"),a=n("O6my");function c(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];Object(o.a)("tracking");var n=Object(r.useRef)(null),c=Object(a.a)();return Object(r.useEffect)((function(){var r=n.current;if(!r)return function(){};var o=Object(i.b)(r,c(),t),a=e?Object(i.a)(r,c(),t):Object(i.c)(r,c(),t);return function(){o(),a()}}),[c,t,e]),n}},arNB:function(t,e,n){"use strict";var r=n("oB+F"),o={object:!0,symbol:!0};t.exports=function(){var t,e=r.Symbol;if("function"!==typeof e)return!1;t=e("test symbol");try{String(t)}catch(n){return!1}return!!o[typeof e.iterator]&&(!!o[typeof e.toPrimitive]&&!!o[typeof e.toStringTag])}},b1ot:function(t,e,n){t.exports={cdnImage__3mJlv:"cdnImage__3mJlv",gif__playing__UA_gM:"gif__playing__UA_gM",gifPlayButton__2SCJp:"gifPlayButton__2SCJp",gifPlayButton_cta__2JyMD:"gifPlayButton_cta__2JyMD",gifPlayButton_bg__FCgUQ:"gifPlayButton_bg__FCgUQ",gifPlayButton_circle__2oy9F:"gifPlayButton_circle__2oy9F",gifPlayButton_pauseCta__3gQC2:"gifPlayButton_pauseCta__3gQC2","fadeout-pause__1W59N":"fadeout-pause__1W59N",gif__loading__mFPFP:"gif__loading__mFPFP",spin__3xK93:"spin__3xK93",gifPlaceholder__2Z0Xs:"gifPlaceholder__2Z0Xs",gifPlayButton_icon__1yKof:"gifPlayButton_icon__1yKof","no-svg__3sfv4":"no-svg__3sfv4"}},bFlY:function(t,e,n){"use strict";n.d(e,"a",(function(){return u}));var r=n("fGyu"),o=n("ERkP"),i=n("lOqH"),a=n("kZhk"),c=n("O6my");function u(t){var e=t.layers,n=void 0===e?[]:e;Object(i.a)("tracking");var u=Object(c.a)();Object(o.useEffect)((function(){a.i.apply(void 0,[u()].concat(Object(r.a)(n)))}),[])}},bUN3:function(t,e,n){"use strict";function r(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.head,n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return new Promise((function(r,o){var i=document.createElement("script");i.onload=function(){return r(i)},i.onerror=function(){o("Script at url ".concat(t," failed to load"))},i.src=t,i.async=n,i.type="text/javascript",e.appendChild(i)}))}n.d(e,"a",(function(){return r}))},btX6:function(t,e,n){"use strict";var r=n("U4eW"),o=n("J2Kb"),i=Function.prototype.bind,a=Function.prototype.call,c=Object.keys,u=Object.prototype.propertyIsEnumerable;t.exports=function(t,e){return function(n,s){var f,l=arguments[2],p=arguments[3];return n=Object(o(n)),r(s),f=c(n),p&&f.sort("function"===typeof p?i.call(p,n):void 0),"function"!==typeof t&&(t=f[t]),a.call(t,f,(function(t,r){return u.call(n,t)?a.call(s,l,n[t],t,n,r):e}))}}},d8Ye:function(t,e,n){"use strict";var r=n("HX/o"),o=Object.create,i=Object.defineProperty,a=Object.prototype,c=o(null);t.exports=function(t){for(var e,n,o=0;c[t+(o||"")];)++o;return c[t+=o||""]=!0,i(a,e="@@"+t,r.gs(null,(function(t){n||(n=!0,i(this,e,r(t)),n=!1)}))),e}},dAGX:function(t,e,n){"use strict";var r=Object.prototype.toString,o=r.call("");t.exports=function(t){return"string"===typeof t||t&&"object"===typeof t&&(t instanceof String||r.call(t)===o)||!1}},dKMb:function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));function r(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(function(t){return(t^function(){try{return crypto.getRandomValues(new Uint8Array(1))[0]}catch(t){return 255*Math.random()}}()&15>>t/4).toString(16)}))}},dRla:function(t,e,n){"use strict";var r,o,i,a=n("HX/o"),c=n("hvIx"),u=n("oB+F").Symbol,s=n("d8Ye"),f=n("Mqvj"),l=n("YdKM"),p=Object.create,h=Object.defineProperties,d=Object.defineProperty;if("function"===typeof u)try{String(u()),i=!0}catch(v){}else u=null;o=function(t){if(this instanceof o)throw new TypeError("Symbol is not a constructor");return r(t)},t.exports=r=function t(e){var n;if(this instanceof t)throw new TypeError("Symbol is not a constructor");return i?u(e):(n=p(o.prototype),e=void 0===e?"":String(e),h(n,{__description__:a("",e),__name__:a("",s(e))}))},f(r),l(r),h(o.prototype,{constructor:a(r),toString:a("",(function(){return this.__name__}))}),h(r.prototype,{toString:a((function(){return"Symbol ("+c(this).__description__+")"})),valueOf:a((function(){return c(this)}))}),d(r.prototype,r.toPrimitive,a("",(function(){var t=c(this);return"symbol"===typeof t?t:t.toString()}))),d(r.prototype,r.toStringTag,a("c","Symbol")),d(o.prototype,r.toStringTag,a("c",r.prototype[r.toStringTag])),d(o.prototype,r.toPrimitive,a("c",r.prototype[r.toPrimitive]))},ds4U:function(t,e,n){"use strict";n.d(e,"a",(function(){return f})),n.d(e,"b",(function(){return l}));var r=n("wope");n("266R"),n("PMPH");function o(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function i(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?o(Object(n),!0).forEach((function(e){Object(r.a)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var a={rejectErrors:!0,defaultVariantIfUnbucketed:"control"},c={notFound:{logError:function(t){return'Experiment "'.concat(t,'" is not registered')},throwError:function(t){return{type:"ExperimentNotFound",name:t}}},notEligible:{logError:function(t){return'Experiment "'.concat(t,'" is not eligible')},throwError:function(t){return{type:"ExperimentNotEligible",name:t}}},error:{logError:function(t,e){return'Experiment "'.concat(t,'" error: ').concat(e)},throwError:function(t,e){return{type:"ExperimentServerError",name:t,error:e}}},missing:{logError:function(t){return'Experiment "'.concat(t,'" was not in the API response')},throwError:function(t){return{type:"ExperimentServerMissingResponse",name:t}}}};function u(t){var e=new URLSearchParams(window.location.search);e&&e.has("abdebug")&&console.debug(t)}function s(t){var e=t.errorType,n=t.experimentName,r=t.rejectErrors,o=t.serverError;if(r)throw e.throwError(n,o);return u(e.logError(n,o)),null}function f(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{declared:{},eligible:{},returned:{},loaded:!1},e=arguments.length>1?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=i(i({},a),n),o=r.rejectErrors,u=r.defaultVariantIfUnbucketed,f=new URLSearchParams(window.location.search);if(f&&f.has("abeagle_".concat(e))&&f.has("abdebug"))return f.get("abeagle_".concat(e));if(!t.loaded)return null;if(!t.declared[e])return s({errorType:c.notFound,experimentName:e,rejectErrors:o});if(!t.eligible[e])return s({errorType:c.notEligible,experimentName:e,rejectErrors:o});if(!t.returned[e])return s({errorType:c.missing,experimentName:e,rejectErrors:o});if(t.returned[e].error)return s({errorType:c.error,experimentName:e,rejectErrors:o,serverError:t.returned[e].error});var l=t.returned[e];return null===l.value&&null!==u?u:l.value}function l(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"on";if(!t.loaded)return null;var r=f(t,e,{rejectErrors:!1});return r===n}},e8ka:function(t,e,n){"use strict";t.exports=function(){var t=Math.sign;return"function"===typeof t&&(1===t(10)&&-1===t(-20))}},ekro:function(t,e,n){"use strict";var r=n("MrsR"),o=n("J4Mq"),i=n("Tjm3"),a=n("A4TU"),c=n("gHTN"),u=n("pmLj"),s=Function.prototype,f=Math.max,l=Math.min,p=Object.create;u.maxAge=function(t,e,n){var h,d,v,y;(t=c(t))&&(h=p(null),d=n.async&&u.async||n.promise&&u.promise?"async":"",e.on("set"+d,(function(n){h[n]=setTimeout((function(){e.delete(n)}),t),"function"===typeof h[n].unref&&h[n].unref(),y&&(y[n]&&"nextTick"!==y[n]&&clearTimeout(y[n]),y[n]=setTimeout((function(){delete y[n]}),v),"function"===typeof y[n].unref&&y[n].unref())})),e.on("delete"+d,(function(t){clearTimeout(h[t]),delete h[t],y&&("nextTick"!==y[t]&&clearTimeout(y[t]),delete y[t])})),n.preFetch&&(v=!0===n.preFetch||isNaN(n.preFetch)?.333:f(l(Number(n.preFetch),1),0))&&(y={},v=(1-v)*t,e.on("get"+d,(function(t,o,c){y[t]||(y[t]="nextTick",i((function(){var i;"nextTick"===y[t]&&(delete y[t],e.delete(t),n.async&&(o=r(o)).push(s),i=e.memoized.apply(c,o),n.promise&&a(i)&&("function"===typeof i.done?i.done(s,s):i.then(s,s)))})))}))),e.on("clear"+d,(function(){o(h,(function(t){clearTimeout(t)})),h={},y&&(o(y,(function(t){"nextTick"!==t&&clearTimeout(t)})),y={})})))}},f4fR:function(t,e,n){"use strict";n.d(e,"a",(function(){return O})),n.d(e,"b",(function(){return w})),n.d(e,"c",(function(){return E})),n.d(e,"d",(function(){return S})),n.d(e,"e",(function(){return j})),n.d(e,"f",(function(){return x})),n.d(e,"g",(function(){return L})),n.d(e,"h",(function(){return k}));var r=n("zygG"),o=n("MMYH"),i=n("9fIP"),a=n("K/z8"),c=n("sRHE"),u=n("8K1b"),s=n("XcBm");function f(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function l(t,e,n){return(l=f()?Reflect.construct:function(t,e,n){var r=[null];r.push.apply(r,e);var o=new(Function.bind.apply(t,r));return n&&Object(s.a)(o,n.prototype),o}).apply(null,arguments)}function p(t){var e="function"===typeof Map?new Map:void 0;return(p=function(t){if(null===t||(n=t,-1===Function.toString.call(n).indexOf("[native code]")))return t;var n;if("function"!==typeof t)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof e){if(e.has(t))return e.get(t);e.set(t,r)}function r(){return l(t,arguments,Object(c.a)(this).constructor)}return r.prototype=Object.create(t.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),Object(s.a)(r,t)})(t)}var h=n("zjfJ"),d=n("QsI/"),v=n("2JES"),y=n("Egbq");function g(t,e,n){return e=Object(c.a)(e),Object(a.a)(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],Object(c.a)(t).constructor):e.apply(t,n))}function m(){m=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),c=new T(r||[]);return o(a,"_invoke",{value:S(t,n,c)}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",v={};function y(){}function g(){}function b(){}var _={};s(_,a,(function(){return this}));var w=Object.getPrototypeOf,O=w&&w(w(A([])));O&&O!==n&&r.call(O,a)&&(_=O);var E=b.prototype=y.prototype=Object.create(_);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function n(o,i,a,c){var u=l(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return n("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function S(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=P(c,r);if(u){if(u===v)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var s=l(e,n,r);if("normal"===s.type){if(o=r.done?d:"suspendedYield",s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=d,r.method="throw",r.arg=s.arg)}}}function P(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=l(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function L(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(L,this),this.reset(!0)}function A(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=b,o(E,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=s(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,u,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},j(x.prototype),s(x.prototype,c,(function(){return this})),e.AsyncIterator=x,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new x(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,u,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=A,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),k(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;k(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:A(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function b(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function _(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?b(Object(n),!0).forEach((function(e){Object(h.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var w=function(t){return String(t).replace(/[^\w\s-_/]/g,"").replace(/\s+/g,"_").toLowerCase()},O=function(t){return t.map((function(t){return"undefined"===t?"":t}))},E=function(t){return null===t||isNaN(Number(t))?null:Number(t)},j=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return t&&Array.isArray(t)?t:e},x=function(){var t=Object(d.a)(m().mark((function t(e){var n,r,o,i,a,c;return m().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=e.context,r=e.layers,o=void 0===r?[]:r,i={},a=0;case 3:if(!(a<o.length)){t.next=13;break}if("function"!==typeof(c=o[a])){t.next=9;break}return t.next=8,c(n);case 8:c=t.sent;case 9:i=_(_({},i),c);case 10:a++,t.next=3;break;case 13:return t.abrupt("return",i);case 14:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),S=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.client_id_cookie_name,n=void 0===e?"_amp_pd":e,r=t.expires,o=void 0===r?365:r,i=v.a.get(n);if(!i){var a=window.location.hostname.split(".").splice(-2,2).join(".");i="amp-".concat(Object(y.a)()),v.a.set({name:n,value:i,days:o,domain:a})}return i},P=function(t){function e(t){var n;return Object(i.a)(this,e),(n=g(this,e,[t])).name="ClientEventSchemaLayerError",n}return Object(u.a)(e,t),Object(o.a)(e)}(p(Error)),L=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return new P('Missing required field: "'.concat(t,'"'))},k=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.debug,o=void 0!==n&&n,i=Object.entries(t).filter((function(t){var e=Object(r.a)(t,2)[1];return!(e instanceof P)||(o&&console.warn(e),!1)})),a=i.reduce((function(t,e){var n=Object(r.a)(e,2),o=n[0],i=n[1];return t[o]=i,t}),{});return a}},fq6u:function(t,e,n){"use strict";var r=n("fGyu"),o=n("zjfJ"),i=n("ERkP"),a=n.n(i),c=n("SE2S"),u=[n("JNgv").a].concat([{name:"TimeSpentRO_2",variations:["on","control"],isEligible:function(){return!0}}],[]),s=n("266R"),f=n("wope"),l=n("PMPH");function p(){p=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var i=e&&e.prototype instanceof g?e:g,a=Object.create(i.prototype),c=new T(r||[]);return o(a,"_invoke",{value:S(t,n,c)}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h="suspendedStart",d="executing",v="completed",y={};function g(){}function m(){}function b(){}var _={};s(_,a,(function(){return this}));var w=Object.getPrototypeOf,O=w&&w(w(A([])));O&&O!==n&&r.call(O,a)&&(_=O);var E=b.prototype=g.prototype=Object.create(_);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function n(o,i,a,c){var u=l(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return n("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function S(e,n,r){var o=h;return function(i,a){if(o===d)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=P(c,r);if(u){if(u===y)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===h)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=d;var s=l(e,n,r);if("normal"===s.type){if(o=r.done?v:"suspendedYield",s.arg===y)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=v,r.method="throw",r.arg=s.arg)}}}function P(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var i=l(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,y):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function L(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(L,this),this.reset(!0)}function A(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return m.prototype=b,o(E,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:m,configurable:!0}),m.displayName=s(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,u,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},j(x.prototype),s(x.prototype,c,(function(){return this})),e.AsyncIterator=x,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new x(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,u,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=A,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),k(n),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;k(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:A(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),y}},e}function h(t){var e=new URLSearchParams(window.location.search);e&&e.has("abdebug")&&console.debug(t)}function d(t){return v.apply(this,arguments)}function v(){return(v=Object(s.a)(p().mark((function t(e){var n,r,o,i,a,c,u,s;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.userId,r=e.data,o=e.source,i=e.experimentConfig,a=e.abeagleHost,c=e.trackFn,u={declared:{},eligible:{},returned:{},loaded:!1},i&&i.length){t.next=5;break}return u.loaded=!0,t.abrupt("return",u);case 5:return i.forEach((function(t){u.declared[t.name]=t,("boolean"===typeof t.isEligible?t.isEligible:t.isEligible(r))&&(u.eligible[t.name]=t)})),(s=new URLSearchParams).append("experiment_names",Object.keys(u.eligible).join(";")),s.append("user_id",n),s.append("source",o),s.toString(),t.next=13,fetch("".concat(a,"/public/v3/experiment_variants?").concat(s.toString())).then((function(t){return t.json()})).catch((function(t){return h(t),{}}));case 13:return u.returned=t.sent,c&&m(u.returned,c),Object.keys(u.returned).forEach((function(t){u.declared[t]&&(!u.returned[t].error&&u.eligible[t]&&(u.eligible[t]=u.returned[t]))})),u.loaded=!0,t.abrupt("return",u);case 18:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function y(){return l.a.getBuzzfeedSubdomainOrWildcard(window.location.hostname)}function g(t,e){var n=window.location.hostname.replace("www",""),r=y();l.a.remove(t,".".concat(n));var o=r==="www.".concat(n)?14:1;l.a.set({name:t,value:e,days:o,domain:r})}function m(t,e){var n=y();Object.keys(t).forEach((function(r){var o="".concat(r,"_version"),i=t[r],a=i.value,c=i.version,u=i.error,s=i.resolved;if(!u){if(s&&(a=a||"control"),null===a)return l.a.remove(r,n),void l.a.remove(o,n);var f=l.a.get(r)===String(a),p=l.a.get(o)===String(c);f&&p||(g(r,a),g(o,c),e({experiment:r,variation:t[r]}))}}))}function b(){b=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),c=new T(r||[]);return o(a,"_invoke",{value:S(t,n,c)}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",v={};function y(){}function g(){}function m(){}var _={};s(_,a,(function(){return this}));var w=Object.getPrototypeOf,O=w&&w(w(A([])));O&&O!==n&&r.call(O,a)&&(_=O);var E=m.prototype=y.prototype=Object.create(_);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function n(o,i,a,c){var u=l(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return n("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function S(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=P(c,r);if(u){if(u===v)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var s=l(e,n,r);if("normal"===s.type){if(o=r.done?d:"suspendedYield",s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=d,r.method="throw",r.arg=s.arg)}}}function P(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=l(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function L(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(L,this),this.reset(!0)}function A(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(E,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,u,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},j(x.prototype),s(x.prototype,c,(function(){return this})),e.AsyncIterator=x,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new x(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,u,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=A,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),k(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;k(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:A(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function _(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function w(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?_(Object(n),!0).forEach((function(e){Object(f.a)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var O=n("v0uu"),E=n("myWV");var j=n("kZhk"),x=function(t){return window.matchMedia(t).matches};n("xwdf"),n("p5SE"),n("QsI/");n("bUN3"),n("Ntd4"),n("5BFc"),n("hSXu"),n("/nJV"),n("R/i5"),n("8o0J"),n("iCr9"),n("Q1+2");n("zygG"),n("4HaG");var S=n("6S4M");n("HbGN");n("7JvT"),n("8Ync");var P=n("Emvt"),L=a.a.createElement;function k(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function T(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?k(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}e.a=function(t){var e,n=t.children,o=t.edition,a=void 0===o?{}:o,f=t.pageProps,l=void 0===f?{}:f,p=function(){var t=Object(i.useState)(null),e=t[0],n=t[1];return Object(i.useEffect)((function(){n(Object(E.a)())}),[]),e}(O.CLUSTER),h=Object(i.useRef)(null),v=(e={isBPage:!1,isNewBPage:!1,isFeedPage:!0,isNewHP:!1,isBFO:!0,isBFN:!1,localizationCountry:a,userCountry:Object(P.isServer)()?"":Object(S.b)(),edition:a,isAdPost:function(){return!1}},[{name:"ads_toolbar_bpages",variations:["off","on"],isEligible:function(){return e.isBPage&&e.isBFO&&x("(min-width: 52rem)")}},{name:"ads_toolbar_feeds",variations:["off","on"],isEligible:function(){return e.isFeedPage&&!e.isBFN&&x("(min-width: 52rem)")}},{name:"ads_toolbar_tasty",variations:["off","on"],isEligible:function(){return"tasty"===(null===e||void 0===e?void 0:e.destination)&&x("(min-width: 52rem)")}},{name:"ads_toolbar_bfn",variations:["off","on"],isEligible:function(){var t;return((null===e||void 0===e?void 0:e.isBFN)||"buzzfeed_news"===(null===(t=window)||void 0===t||null===(t=t.BZFD)||void 0===t||null===(t=t.Context)||void 0===t||null===(t=t.page)||void 0===t?void 0:t.destination))&&x("(min-width: 52rem)")}},{name:"ads_prebid",variations:["off","on"],isEligible:function(){return!0}},{name:"ads_bid_cache",variations:["off","on"],isEligible:function(){return!0}},{name:"ads_amazon_tam",variations:["off","on"],isEligible:function(){return!0}},{name:"ads_ad_lightning",variations:["off","on"],isEligible:function(){return!0}},{name:"ads_doubleverify",variations:["off","on"],isEligible:function(){return!0}},{name:"ads_doubleverify_refresh",variations:["off","on"],isEligible:function(){return!0}},{name:"ads_blockthrough",variations:["off","on"],isEligible:function(){return!0}},{name:"advertise_international",variations:["off","on"],isEligible:function(){return!["pt-br","es","ja-jp","es-mx","es-es"].includes(e.edition)}},{name:"non_us_ad_lookahead_adjustments",variations:["control","on"],isEligible:function(){return!0}},{name:"ADSGROUP-442-permutive",variations:["off","on"],isEligible:function(){return!0}},{name:"ADSGROUP-143_new_ad_calls_structure",variations:["off","on"],isEligible:function(){return!0}},{name:"ADS-1141-prefetch3",variations:["control","early","late"],isEligible:function(){return!x("(min-width: 52rem)")}},{name:"ads_facebook_density",variations:["control","var1","var2"],isEligible:function(t){var n=document.referrer||"";return(null===e||void 0===e||!e.isBPage||!(null!==t&&void 0!==t&&t.isShopping||null!==t&&void 0!==t&&t.isAd||null!==t&&void 0!==t&&t.is_quiz))&&("US"===(null===e||void 0===e?void 0:e.userCountry)||"CA"===e.userCountry)&&"tasty"!==(null===e||void 0===e?void 0:e.destination)&&n.length>0&&(n.includes("buzzfeed.bio")||n.includes("facebook.com"))}},{name:"ads_tam_hem",variations:["off","on"],isEligible:function(){return!0}},{name:"ADS-1791-new-bpage-gpt-lazyload",variations:["off","on"],isEligible:function(){return e.isNewBPage}},{name:"RT-994-swap-refresh",variations:["control","swap-refresh"],isEligible:function(){return!0}}]),y=Object(i.useMemo)((function(){return{userId:p,data:{}}}),[p]),g=function(t){var e=t.userId,n=t.data,r=t.experimentConfig,o=t.source,a=t.abeagleHost,c=t.trackFn,u={data:n,experimentConfig:r,source:o,abeagleHost:a},f=Object.keys(u).reduce((function(t,e){return u[e]||t.push(e),t}),[]);if(f.length)throw new Error("Missing required fields: ".concat(f.join(", ")));var l=Object(i.useRef)(w({},{declared:{},eligible:{},returned:{},loaded:!1})),p=Object(i.useState)(!1),h=p[0],v=p[1],y=Object(i.useMemo)((function(){return w(w({},l.current),{},{loaded:h})}),[h]);return Object(i.useEffect)((function(){e&&(v(!1),Object(s.a)(b().mark((function t(){return b().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,d({userId:e,data:n,experimentConfig:r,source:o,abeagleHost:a,trackFn:c});case 2:l.current=t.sent,v(!0);case 4:case"end":return t.stop()}}),t)})))())}),[e,n]),y}(T({abeagleHost:O.abeagle_host,experimentConfig:[].concat(Object(r.a)(u),Object(r.a)(v)),source:"buzz_web"},y)),m=l.username;h.current&&h.current.loaded===g.loaded||(h.current=T(T({},g),{},{pagePath:m})),h.current.stale=h.current.pagePath!==m;var _=h.current.loaded,k=g.eligible?Object.keys(g.eligible).join("|"):"";return Object(i.useEffect)((function(){if(_&&k.length){var t=[];Object.keys(g.eligible).forEach((function(e){var n=g.eligible[e];if(n&&n.value){var r=n.id,o=n.version,i=n.value,a=n.variant_id;t.push([e,r,o,i,a].join("|"))}})),Object(j.h)({context_page_type:"user",context_page_id:l.id},{experiment_id:t})}}),[l.username,_,k]),L(c.a.Provider,{value:{experiments:h.current}},n)}},gHTN:function(t,e,n){"use strict";var r=n("MC86"),o=n("l/SJ");t.exports=function(t){if((t=r(t))>o)throw new TypeError(t+" exceeds maximum possible timeout");return t}},gIIS:function(t,e,n){(function(t,e){!function(t,n){"use strict";if(!t.setImmediate){var r,o=1,i={},a=!1,c=t.document,u=Object.getPrototypeOf&&Object.getPrototypeOf(t);u=u&&u.setTimeout?u:t,"[object process]"==={}.toString.call(t.process)?r=function(t){e.nextTick((function(){f(t)}))}:function(){if(t.postMessage&&!t.importScripts){var e=!0,n=t.onmessage;return t.onmessage=function(){e=!1},t.postMessage("","*"),t.onmessage=n,e}}()?function(){var e="setImmediate$"+Math.random()+"$",n=function(n){n.source===t&&"string"===typeof n.data&&0===n.data.indexOf(e)&&f(+n.data.slice(e.length))};t.addEventListener?t.addEventListener("message",n,!1):t.attachEvent("onmessage",n),r=function(n){t.postMessage(e+n,"*")}}():t.MessageChannel?function(){var t=new MessageChannel;t.port1.onmessage=function(t){f(t.data)},r=function(e){t.port2.postMessage(e)}}():c&&"onreadystatechange"in c.createElement("script")?function(){var t=c.documentElement;r=function(e){var n=c.createElement("script");n.onreadystatechange=function(){f(e),n.onreadystatechange=null,t.removeChild(n),n=null},t.appendChild(n)}}():r=function(t){setTimeout(f,0,t)},u.setImmediate=function(t){"function"!==typeof t&&(t=new Function(""+t));for(var e=new Array(arguments.length-1),n=0;n<e.length;n++)e[n]=arguments[n+1];var a={callback:t,args:e};return i[o]=a,r(o),o++},u.clearImmediate=s}function s(t){delete i[t]}function f(t){if(a)setTimeout(f,0,t);else{var e=i[t];if(e){a=!0;try{!function(t){var e=t.callback,n=t.args;switch(n.length){case 0:e();break;case 1:e(n[0]);break;case 2:e(n[0],n[1]);break;case 3:e(n[0],n[1],n[2]);break;default:e.apply(void 0,n)}}(e)}finally{s(t),a=!1}}}}}("undefined"===typeof self?"undefined"===typeof t?this:t:self)}).call(this,n("fRV1"),n("F63i"))},gShS:function(t,e,n){"use strict";var r=n("1cz4"),o=Math.abs,i=Math.floor;t.exports=function(t){return isNaN(t)?0:0!==(t=Number(t))&&isFinite(t)?r(t)*i(o(t)):t}},gYXA:function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return o}));var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window,e=t.location.hostname,n=["buzzfeed","buzzfeednews","tasty","huffpost","huffingtonpost"],r=e.split(".")[0];return r&&"stage"!==r&&-1===n.indexOf(r)&&e.split(".").length>=3&&(e=e.substring(r.length+1)),e},o=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"dev";return"dev"===t?"dev.buzzfeed.io":"prod"===t||"app-west"===t?"buzzfeed.com":"stage.buzzfeed.com"}},hSXu:function(t,e,n){"use strict";n.d(e,"a",(function(){return y}));var r=n("zjfJ"),o=n("zygG"),i=(n("8o0J"),n("xwdf")),a=n("gYXA");function c(t,e,n){var r,o,i,a,c,u,s;for(this.version="1.0.1",this.minAlphabetLength=16,this.sepDiv=3.5,this.guardDiv=12,this.errorAlphabetLength="error: alphabet must contain at least X unique characters",this.errorAlphabetSpace="error: alphabet cannot contain spaces",this.alphabet="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",this.seps="cfhistuCFHISTU",this.minHashLength=parseInt(e,10)>0?e:0,this.salt="string"===typeof t?t:"","string"===typeof n&&(this.alphabet=n),r="",o=0,a=this.alphabet.length;o!==a;o++)-1===r.indexOf(this.alphabet[o])&&(r+=this.alphabet[o]);if(this.alphabet=r,this.alphabet.length<this.minAlphabetLength)throw this.errorAlphabetLength.replace("X",this.minAlphabetLength);if(-1!==this.alphabet.search(" "))throw this.errorAlphabetSpace;for(o=0,a=this.seps.length;o!==a;o++)-1===(i=this.alphabet.indexOf(this.seps[o]))?this.seps=this.seps.substr(0,o)+" "+this.seps.substr(o+1):this.alphabet=this.alphabet.substr(0,i)+" "+this.alphabet.substr(i+1);this.alphabet=this.alphabet.replace(/ /g,""),this.seps=this.seps.replace(/ /g,""),this.seps=this.consistentShuffle(this.seps,this.salt),(!this.seps.length||this.alphabet.length/this.seps.length>this.sepDiv)&&(1===(c=Math.ceil(this.alphabet.length/this.sepDiv))&&c++,c>this.seps.length?(u=c-this.seps.length,this.seps+=this.alphabet.substr(0,u),this.alphabet=this.alphabet.substr(u)):this.seps=this.seps.substr(0,c)),this.alphabet=this.consistentShuffle(this.alphabet,this.salt),s=Math.ceil(this.alphabet.length/this.guardDiv),this.alphabet.length<3?(this.guards=this.seps.substr(0,s),this.seps=this.seps.substr(s)):(this.guards=this.alphabet.substr(0,s),this.alphabet=this.alphabet.substr(s))}c.prototype.encode=function(){var t,e,n="",r=Array.prototype.slice.call(arguments);if(!r.length)return n;for(r[0]instanceof Array&&(r=r[0]),t=0,e=r.length;t!==e;t++)if("number"!==typeof r[t]||r[t]%1!==0||r[t]<0)return n;return this._encode(r)},c.prototype.decode=function(t){return t.length&&"string"===typeof t?this._decode(t,this.alphabet):[]},c.prototype.encodeHex=function(t){var e,n,r;if(t=t.toString(),!/^[0-9a-fA-F]+$/.test(t))return"";for(e=0,n=(r=t.match(/[\w\W]{1,12}/g)).length;e!==n;e++)r[e]=parseInt("1"+r[e],16);return this.encode.apply(this,r)},c.prototype.decodeHex=function(t){var e,n,r=[],o=this.decode(t);for(e=0,n=o.length;e!==n;e++)r+=o[e].toString(16).substr(1);return r},c.prototype._encode=function(t){var e,n,r,o,i,a,c,u,s,f,l,p=this.alphabet,h=t.length,d=0;for(r=0,o=t.length;r!==o;r++)d+=t[r]%(r+100);for(n=e=p[d%p.length],r=0,o=t.length;r!==o;r++)i=t[r],a=n+this.salt+p,p=this.consistentShuffle(p,a.substr(0,p.length)),e+=c=this.hash(i,p),r+1<h&&(u=(i%=c.charCodeAt(0)+r)%this.seps.length,e+=this.seps[u]);for(e.length<this.minHashLength&&(s=(d+e[0].charCodeAt(0))%this.guards.length,(e=this.guards[s]+e).length<this.minHashLength&&(s=(d+e[2].charCodeAt(0))%this.guards.length,e+=this.guards[s])),f=parseInt(p.length/2,10);e.length<this.minHashLength;)(l=(e=(p=this.consistentShuffle(p,p)).substr(f)+e+p.substr(0,f)).length-this.minHashLength)>0&&(e=e.substr(l/2,this.minHashLength));return e},c.prototype._decode=function(t,e){var n,r,o,i,a=[],c=0,u=new RegExp("["+this.guards+"]","g"),s=t.replace(u," "),f=s.split(" ");if(3!==f.length&&2!==f.length||(c=1),"undefined"!==typeof(s=f[c])[0]){for(n=s[0],s=s.substr(1),u=new RegExp("["+this.seps+"]","g"),c=0,r=(f=(s=s.replace(u," ")).split(" ")).length;c!==r;c++)o=f[c],i=n+this.salt+e,e=this.consistentShuffle(e,i.substr(0,e.length)),a.push(this.unhash(o,e));this._encode(a)!==t&&(a=[])}return a},c.prototype.consistentShuffle=function(t,e){var n,r,o,i,a,c;if(!e.length)return t;for(i=t.length-1,a=0,c=0;i>0;i--,a++)c+=n=e[a%=e.length].charCodeAt(0),o=t[r=(n+a+c)%i],t=(t=t.substr(0,r)+t[i]+t.substr(r+1)).substr(0,i)+o+t.substr(i+1);return t},c.prototype.hash=function(t,e){var n="",r=e.length;do{n=e[t%r]+n,t=parseInt(t/r,10)}while(t);return n},c.prototype.unhash=function(t,e){var n,r=0;for(n=0;n<t.length;n++)r+=e.indexOf(t[n])*Math.pow(e.length,t.length-n-1);return r};var u=n("/nJV");function s(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?s(Object(n),!0).forEach((function(e){Object(r.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var l=parseInt(1e10*Math.random(),10),p=Object(u.a)(),h=function(t){if(0!==t.indexOf(".")){var e=/[0-9A-Za-z]+/.exec(t);return null!==e&&e[0]===t&&parseInt(t,36)}var n=t.substr(1,2);return function(t){var e=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).salt;return new c(void 0===e?null:e).decode(t)[0]}(t.substr(3),{salt:n})},d=function(t){var e=parseInt(25*Math.random()+10,10).toString(36)+parseInt(25*Math.random()+10,10).toString(36);return".".concat(e).concat(function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.salt,r=void 0===n?null:n,o=e.length;return new c(r,void 0===o?32:o).encode(t)}(t,{salt:e,length:0}))},v=function(t){var e=decodeURIComponent(t).split("&").map((function(t){return t.split("=")})).reduce((function(t,e){var n=Object(o.a)(e,2),i=n[0],a=n[1];return f(f({},t),{},Object(r.default)({},i,a))}),{}),n=e.u,i=e.uuid;return{legacyIdentifier:h(n||""),identifier:i}},y=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.legacy,n=void 0!==e&&e,r={name:"bf_visit",days:1e4,domain:Object(a.a)()},o=i.a.get(r.name),c=v(o),u=c.legacyIdentifier,s=c.identifier,h=d(l);return n?u||(i.a.set(f(f({},r),{},{value:encodeURIComponent("u=".concat(h,"&uuid=").concat(s||p,"&v=2"))})),l):s||u?s||String(u):(i.a.set(f(f({},r),{},{value:encodeURIComponent("u=".concat(h,"&uuid=").concat(p,"&v=2"))})),p)}},hvIx:function(t,e,n){"use strict";var r=n("Yt/R");t.exports=function(t){if(!r(t))throw new TypeError(t+" is not a symbol");return t}},iCr9:function(t,e,n){"use strict";n.d(e,"c",(function(){return c})),n.d(e,"b",(function(){return u})),n.d(e,"d",(function(){return s})),n.d(e,"a",(function(){return f}));var r=n("zygG");function o(t){return(t+="").indexOf("#")>-1?t.substr(t.indexOf("#"),t.length):""}function i(t){return(t+="").indexOf("#")>-1?t.substr(0,t.indexOf("#")):t}function a(t){return t.indexOf("?")>-1}function c(t){if(""===t||void 0===t||null===t)return{};t.indexOf("?")>-1&&(t=t.substr(t.indexOf("?")+1,t.length));var e=(t=i(t)).split("&"),n={};return e.forEach((function(t){var e=t.split("="),o=Object(r.a)(e,2),i=o[0],a=o[1],c=void 0===a?null:a;n[i]=c})),n}function u(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"?",n=[];return Object.keys(t).forEach((function(e){n.push("".concat(e,"=").concat(encodeURIComponent(t[e])))})),(e||"")+n.join("&")}function s(t){return c(function(t){var e="";return a(t=i(t))&&(e=t.substr(t.indexOf("?"),t.length)),e}(t))}function f(t){return t=function(t){if(!a(t))return t;var e=o(t);return(t=i(t)).substr(0,t.indexOf("?"))+e}(t=i(t))}},ja9M:function(t,e,n){"use strict";n.d(e,"a",(function(){return W}));var r=n("fGyu"),o=n("HbGN"),i=n("QsI/"),a=n("2JES"),c=n("f4fR"),u=n("zygG"),s=n("zjfJ"),f=n("Egbq"),l={isEqual:!0,isMatchingKey:!0,isPromise:!0,maxSize:!0,onCacheAdd:!0,onCacheChange:!0,onCacheHit:!0,transformKey:!0},p=Array.prototype.slice;function h(t){var e=t.length;return e?1===e?[t[0]]:2===e?[t[0],t[1]]:3===e?[t[0],t[1],t[2]]:p.call(t,0):[]}function d(t,e){return t===e||t!==t&&e!==e}function v(t,e){var n={};for(var r in t)n[r]=t[r];for(var r in e)n[r]=e[r];return n}var y=function(){function t(t){this.keys=[],this.values=[],this.options=t;var e="function"===typeof t.isMatchingKey;e?this.getKeyIndex=this._getKeyIndexFromMatchingKey:t.maxSize>1?this.getKeyIndex=this._getKeyIndexForMany:this.getKeyIndex=this._getKeyIndexForSingle,this.canTransformKey="function"===typeof t.transformKey,this.shouldCloneArguments=this.canTransformKey||e,this.shouldUpdateOnAdd="function"===typeof t.onCacheAdd,this.shouldUpdateOnChange="function"===typeof t.onCacheChange,this.shouldUpdateOnHit="function"===typeof t.onCacheHit}return Object.defineProperty(t.prototype,"size",{get:function(){return this.keys.length},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"snapshot",{get:function(){return{keys:h(this.keys),size:this.size,values:h(this.values)}},enumerable:!0,configurable:!0}),t.prototype._getKeyIndexFromMatchingKey=function(t){var e=this.options,n=e.isMatchingKey,r=e.maxSize,o=this.keys,i=o.length;if(!i)return-1;if(n(o[0],t))return 0;if(r>1)for(var a=1;a<i;a++)if(n(o[a],t))return a;return-1},t.prototype._getKeyIndexForMany=function(t){var e=this.options.isEqual,n=this.keys,r=n.length;if(!r)return-1;if(1===r)return this._getKeyIndexForSingle(t);var o,i,a=t.length;if(a>1){for(var c=0;c<r;c++)if((o=n[c]).length===a){for(i=0;i<a&&e(o[i],t[i]);i++);if(i===a)return c}}else for(c=0;c<r;c++)if((o=n[c]).length===a&&e(o[0],t[0]))return c;return-1},t.prototype._getKeyIndexForSingle=function(t){var e=this.keys;if(!e.length)return-1;var n=e[0],r=n.length;if(t.length!==r)return-1;var o=this.options.isEqual;if(r>1){for(var i=0;i<r;i++)if(!o(n[i],t[i]))return-1;return 0}return o(n[0],t[0])?0:-1},t.prototype.orderByLru=function(t,e,n){for(var r=this.keys,o=this.values,i=r.length,a=n;a--;)r[a+1]=r[a],o[a+1]=o[a];r[0]=t,o[0]=e;var c=this.options.maxSize;i===c&&n===i?(r.pop(),o.pop()):n>=c&&(r.length=o.length=c)},t.prototype.updateAsyncCache=function(t){var e=this,n=this.options,r=n.onCacheChange,o=n.onCacheHit,i=this.keys[0],a=this.values[0];this.values[0]=a.then((function(n){return e.shouldUpdateOnHit&&o(e,e.options,t),e.shouldUpdateOnChange&&r(e,e.options,t),n}),(function(t){var n=e.getKeyIndex(i);throw-1!==n&&(e.keys.splice(n,1),e.values.splice(n,1)),t}))},t}();var g=function t(e,n){if(void 0===n&&(n={}),function(t){return"function"===typeof t&&t.isMemoized}(e))return t(e.fn,v(e.options,n));if("function"!==typeof e)throw new TypeError("You must pass a function to `memoize`.");var r=n.isEqual,o=void 0===r?d:r,i=n.isMatchingKey,a=n.isPromise,c=void 0!==a&&a,u=n.maxSize,s=void 0===u?1:u,f=n.onCacheAdd,p=n.onCacheChange,g=n.onCacheHit,m=n.transformKey,b=v({isEqual:o,isMatchingKey:i,isPromise:c,maxSize:s,onCacheAdd:f,onCacheChange:p,onCacheHit:g,transformKey:m},function(t){var e={};for(var n in t)l[n]||(e[n]=t[n]);return e}(n)),_=new y(b),w=_.keys,O=_.values,E=_.canTransformKey,j=_.shouldCloneArguments,x=_.shouldUpdateOnAdd,S=_.shouldUpdateOnChange,P=_.shouldUpdateOnHit,L=function t(){var n=j?h(arguments):arguments;E&&(n=m(n));var r=w.length?_.getKeyIndex(n):-1;if(-1!==r)P&&g(_,b,t),r&&(_.orderByLru(w[r],O[r],r),S&&p(_,b,t));else{var o=e.apply(this,arguments),i=j?n:h(arguments);_.orderByLru(i,o,w.length),c&&_.updateAsyncCache(t),x&&f(_,b,t),S&&p(_,b,t)}return O[0]};return L.cache=_,L.fn=e,L.isMemoized=!0,L.options=b,L};function m(t){return(t+="").indexOf("#")>-1?t.substr(t.indexOf("#"),t.length):""}function b(t){return(t+="").indexOf("#")>-1?t.substr(0,t.indexOf("#")):t}function _(t){return t.indexOf("?")>-1}function w(t){return t=function(t){if(!_(t))return t;var e=m(t);return(t=b(t)).substr(0,t.indexOf("?"))+e}(t=b(t))}var O=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window,e=t.location.hostname,n=["buzzfeed","buzzfeednews","tasty","huffpost","huffingtonpost"],r=e.split(".")[0];return r&&"stage"!==r&&-1===n.indexOf(r)&&e.split(".").length>=3&&(e=e.substring(r.length+1)),e};n("9fIP"),n("MMYH");function E(t,e,n){var r,o,i,a,c,u,s;for(this.version="1.0.1",this.minAlphabetLength=16,this.sepDiv=3.5,this.guardDiv=12,this.errorAlphabetLength="error: alphabet must contain at least X unique characters",this.errorAlphabetSpace="error: alphabet cannot contain spaces",this.alphabet="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",this.seps="cfhistuCFHISTU",this.minHashLength=parseInt(e,10)>0?e:0,this.salt="string"===typeof t?t:"","string"===typeof n&&(this.alphabet=n),r="",o=0,a=this.alphabet.length;o!==a;o++)-1===r.indexOf(this.alphabet[o])&&(r+=this.alphabet[o]);if(this.alphabet=r,this.alphabet.length<this.minAlphabetLength)throw this.errorAlphabetLength.replace("X",this.minAlphabetLength);if(-1!==this.alphabet.search(" "))throw this.errorAlphabetSpace;for(o=0,a=this.seps.length;o!==a;o++)-1===(i=this.alphabet.indexOf(this.seps[o]))?this.seps=this.seps.substr(0,o)+" "+this.seps.substr(o+1):this.alphabet=this.alphabet.substr(0,i)+" "+this.alphabet.substr(i+1);this.alphabet=this.alphabet.replace(/ /g,""),this.seps=this.seps.replace(/ /g,""),this.seps=this.consistentShuffle(this.seps,this.salt),(!this.seps.length||this.alphabet.length/this.seps.length>this.sepDiv)&&(1===(c=Math.ceil(this.alphabet.length/this.sepDiv))&&c++,c>this.seps.length?(u=c-this.seps.length,this.seps+=this.alphabet.substr(0,u),this.alphabet=this.alphabet.substr(u)):this.seps=this.seps.substr(0,c)),this.alphabet=this.consistentShuffle(this.alphabet,this.salt),s=Math.ceil(this.alphabet.length/this.guardDiv),this.alphabet.length<3?(this.guards=this.seps.substr(0,s),this.seps=this.seps.substr(s)):(this.guards=this.alphabet.substr(0,s),this.alphabet=this.alphabet.substr(s))}E.prototype.encode=function(){var t,e,n="",r=Array.prototype.slice.call(arguments);if(!r.length)return n;for(r[0]instanceof Array&&(r=r[0]),t=0,e=r.length;t!==e;t++)if("number"!==typeof r[t]||r[t]%1!==0||r[t]<0)return n;return this._encode(r)},E.prototype.decode=function(t){return t.length&&"string"===typeof t?this._decode(t,this.alphabet):[]},E.prototype.encodeHex=function(t){var e,n,r;if(t=t.toString(),!/^[0-9a-fA-F]+$/.test(t))return"";for(e=0,n=(r=t.match(/[\w\W]{1,12}/g)).length;e!==n;e++)r[e]=parseInt("1"+r[e],16);return this.encode.apply(this,r)},E.prototype.decodeHex=function(t){var e,n,r=[],o=this.decode(t);for(e=0,n=o.length;e!==n;e++)r+=o[e].toString(16).substr(1);return r},E.prototype._encode=function(t){var e,n,r,o,i,a,c,u,s,f,l,p=this.alphabet,h=t.length,d=0;for(r=0,o=t.length;r!==o;r++)d+=t[r]%(r+100);for(n=e=p[d%p.length],r=0,o=t.length;r!==o;r++)i=t[r],a=n+this.salt+p,p=this.consistentShuffle(p,a.substr(0,p.length)),e+=c=this.hash(i,p),r+1<h&&(u=(i%=c.charCodeAt(0)+r)%this.seps.length,e+=this.seps[u]);for(e.length<this.minHashLength&&(s=(d+e[0].charCodeAt(0))%this.guards.length,(e=this.guards[s]+e).length<this.minHashLength&&(s=(d+e[2].charCodeAt(0))%this.guards.length,e+=this.guards[s])),f=parseInt(p.length/2,10);e.length<this.minHashLength;)(l=(e=(p=this.consistentShuffle(p,p)).substr(f)+e+p.substr(0,f)).length-this.minHashLength)>0&&(e=e.substr(l/2,this.minHashLength));return e},E.prototype._decode=function(t,e){var n,r,o,i,a=[],c=0,u=new RegExp("["+this.guards+"]","g"),s=t.replace(u," "),f=s.split(" ");if(3!==f.length&&2!==f.length||(c=1),"undefined"!==typeof(s=f[c])[0]){for(n=s[0],s=s.substr(1),u=new RegExp("["+this.seps+"]","g"),c=0,r=(f=(s=s.replace(u," ")).split(" ")).length;c!==r;c++)o=f[c],i=n+this.salt+e,e=this.consistentShuffle(e,i.substr(0,e.length)),a.push(this.unhash(o,e));this._encode(a)!==t&&(a=[])}return a},E.prototype.consistentShuffle=function(t,e){var n,r,o,i,a,c;if(!e.length)return t;for(i=t.length-1,a=0,c=0;i>0;i--,a++)c+=n=e[a%=e.length].charCodeAt(0),o=t[r=(n+a+c)%i],t=(t=t.substr(0,r)+t[i]+t.substr(r+1)).substr(0,i)+o+t.substr(i+1);return t},E.prototype.hash=function(t,e){var n="",r=e.length;do{n=e[t%r]+n,t=parseInt(t/r,10)}while(t);return n},E.prototype.unhash=function(t,e){var n,r=0;for(n=0;n<t.length;n++)r+=e.indexOf(t[n])*Math.pow(e.length,t.length-n-1);return r};function j(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function x(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?j(Object(n),!0).forEach((function(e){Object(s.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var S=parseInt(1e10*Math.random(),10),P=Object(f.a)(),L=function(t){if(0!==t.indexOf(".")){var e=/[0-9A-Za-z]+/.exec(t);return null!==e&&e[0]===t&&parseInt(t,36)}var n=t.substr(1,2);return function(t){var e=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).salt;return new E(void 0===e?null:e).decode(t)[0]}(t.substr(3),{salt:n})},k=function(t){var e=parseInt(25*Math.random()+10,10).toString(36)+parseInt(25*Math.random()+10,10).toString(36);return".".concat(e).concat(function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.salt,r=void 0===n?null:n,o=e.length;return new E(r,void 0===o?32:o).encode(t)}(t,{salt:e,length:0}))},T=function(t){var e=decodeURIComponent(t).split("&").map((function(t){return t.split("=")})).reduce((function(t,e){var n=Object(u.a)(e,2),r=n[0],o=n[1];return x(x({},t),{},Object(s.default)({},r,o))}),{}),n=e.u,r=e.uuid;return{legacyIdentifier:L(n||""),identifier:r}},A=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.legacy,n=void 0!==e&&e,r={name:"bf_visit",days:1e4,domain:O()},o=a.a.get(r.name),i=T(o),c=i.legacyIdentifier,u=i.identifier,s=k(S);return n?c||(a.a.set(x(x({},r),{},{value:encodeURIComponent("u=".concat(s,"&uuid=").concat(u||P,"&v=2"))})),S):u||c?u||String(c):(a.a.set(x(x({},r),{},{value:encodeURIComponent("u=".concat(s,"&uuid=").concat(P,"&v=2"))})),P)};function C(t){return t+"|expiration"}var I=function(){try{return localStorage.setItem("ls_test",!0),localStorage.removeItem("ls_test"),!0}catch(t){return!1}}(),N={set:function(t){I&&"undefined"!==typeof t&&(localStorage.setItem(t.key,t.value),t.expires&&localStorage.setItem(C(t.key),Date.now()+t.expires))},get:function(t){return I?function(t){var e=localStorage.getItem(C(t));return e&&e<=Date.now()}(t)?(this.remove(t),null):localStorage.getItem(t):null},sessionSet:function(t){I&&"undefined"!==typeof t&&sessionStorage.setItem(t.key,t.value)},sessionGet:function(t){return I?sessionStorage.getItem(t):null},remove:function(t){I&&(localStorage.removeItem(C(t)),localStorage.removeItem(t))},clear:function(){I&&localStorage.clear()}},D=n("rdyg"),M=function(t){var e=N.get("cet-page_session_id");if(window[D.d]=window[D.d]||{},window[D.d].current_page_session_url===t&&e)return{page_session_id:e,previous_page_session_id:N.get("pdv3-previous_page_session_id")||""};window[D.d].current_page_session_url=t,e=Object(f.a)()||"00000000-0000-0000-0000-000000000000";var n=N.get("cet-page_session_id")||"";return N.set({key:"cet-page_session_id",value:e}),N.set({expires:18e5,key:"pdv3-previous_page_session_id",value:n}),{page_session_id:e,previous_page_session_id:n}};function R(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function F(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?R(Object(n),!0).forEach((function(e){Object(s.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):R(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var z=g((function(t){var e=t.url,n={name:"bf-xdomain-session-uuid",days:.5/24,domain:O()};return g((function(){var t=String(A()),r=a.a.get(n.name,Object(f.a)());return a.a.set(F(F({},n),{},{value:r})),F({client_uuid:t,client_session_id:r,random_user_uuid:a.a.get("user_uuid","unauthenticated"),referrer_uri:document.referrer},M(w(e)))}),{transformKey:JSON.stringify})}),{transformKey:function(t){return w(Object(u.a)(t,1)[0].url)}}),U=function(){return function(){return{event_uri:document.URL,event_ts:Math.round(Date.now()/1e3),event_uuid:Object(f.a)()}}},G=["track_amp"],H=g((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.track_amp,n=void 0!==e&&e,r=Object(o.a)(t,G);return n?g((function(){return{amp_id:Object(c.d)(r)}}),{transformKey:JSON.stringify}):{}}),{transformKey:JSON.stringify}),B=function(t){var e=t.page_edition,n=void 0===e?Object(c.g)("page_edition"):e;return function(){return{mode:window.matchMedia("screen and (max-width:51.9rem)").matches?"mobile":"desktop",page_edition:n,viewport_size:{width:Number(window.screen.width),height:Number(window.screen.height)}}}},q=function(t){var e=t.type,n=void 0===e?Object(c.g)("type"):e,r=t.source,o=void 0===r?Object(c.g)("source"):r;return function(){return{type:n,source:o}}};function K(t){var e=t.env,n=void 0===e?"dev":e,r=function(t){var e,n=t.debug,r=t.tracking_url,o=[],i=function(){if(o.length){var t=JSON.stringify(o),i=document.createEvent("CustomEvent");if(i.initCustomEvent("cet-event",!1,!1,o),dispatchEvent(i),n)window.fetch("".concat(r,"/events"),{method:"POST",body:t,keepalive:!0}).then((function(t){return t.json()})).then((function(t){t.errors&&t.debug&&(console.group("%c \ud83d\udea8 Rejected client events \ud83d\udea8","background-color: #250201; color: #ee8783; border: 1px solid #540b06"),t.debug.forEach((function(t){return console.table(t.validation)})),console.groupEnd())})).catch((function(){var e=JSON.parse(t);console.group("%cClient Event Tracking: %crun nsq_api_public to verify events","font-weight: bold;","color: gray; font-size: .5rem;"),e.forEach((function(t){console.groupCollapsed('"%c'.concat(t.type,'"'),"font-weight: bold; font-family: monospace;"),console.table(t),console.groupEnd()})),console.groupEnd()}));else{var a;if(navigator&&navigator.sendBeacon)a=navigator.sendBeacon("".concat(r,"/events"),t);else{var c=new XMLHttpRequest;c.open("POST","".concat(r,"/events"),!1),c.onerror=function(){},c.setRequestHeader("Accept","*/*"),c.setRequestHeader("Content-Type","text/plain;charset=UTF-8"),a=c.send(t)}!a&&window.raven&&Math.random()<.1&&window.raven.captureException("Client Event Tracking: sendBeacon could not process a queue.")}clearTimeout(e),e=null,o=[]}};return[function(t){o.push(t),10===o.length&&i(),e||(e=setTimeout(i,200))},i]}(D.a[n]),o=Object(u.a)(r,2),i=o[0],a=o[1];return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.flush,o=void 0!==r&&r,u=Object(c.h)(t,{debug:D.a[n].debug});i(u),o&&a()}}var J=["page_edition"];function Y(){Y=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),c=new k(r||[]);return o(a,"_invoke",{value:x(t,n,c)}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",v={};function y(){}function g(){}function m(){}var b={};s(b,a,(function(){return this}));var _=Object.getPrototypeOf,w=_&&_(_(T([])));w&&w!==n&&r.call(w,a)&&(b=w);var O=m.prototype=y.prototype=Object.create(b);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function n(o,i,a,c){var u=l(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return n("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=S(c,r);if(u){if(u===v)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var s=l(e,n,r);if("normal"===s.type){if(o=r.done?d:"suspendedYield",s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=d,r.method="throw",r.arg=s.arg)}}}function S(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,S(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=l(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function k(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(O,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,u,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},E(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new j(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(O),s(O,u,"Generator"),s(O,a,(function(){return this})),s(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(L),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),L(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;L(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:T(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function V(){var t="true"===a.a.get("is_bot");if(window.location.search.includes("e2e_test"))try{return{e2e_test:new URLSearchParams(window.location.search).get("e2e_test"),is_bot:!0}}catch(e){}return t?{is_bot:!0}:{}}var W=function(t){var e=t.amp_options,n=void 0===e?{}:e,a=t.env,u=t.source;var s=K({env:a});return function(){var t=Object(i.a)(Y().mark((function t(e){var i,f,l,p,h,d,v,y,g,m,b,_,w,O,E,j=arguments;return Y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:for(i=e.flush,f=void 0!==i&&i,l=e.required_layers,p=void 0===l?[]:l,h=e.sample_rate,d=void 0===h?1:h,v=e.type,y=j.length,g=new Array(y>1?y-1:0),m=1;m<y;m++)g[m-1]=j[m];return t.next=4,Object(c.f)({context:{env:a},layers:g});case 4:return b=t.sent,_=b.page_edition,w=Object(o.a)(b,J),O=[q({source:u,type:v}),U(),z({url:document.URL}),H(n),B({page_edition:_})].concat(g,Object(r.a)(p.map((function(t){return t(w)}))),[V()]),t.next=10,Object(c.f)({context:{env:a},layers:O});case 10:if(E=t.sent,!(window.location.search.includes("e2e_test")||Math.random()<=d)){t.next=14;break}return t.next=14,s(E,{flush:f});case 14:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}},jmmV:function(t,e){var n=function(){if("object"===typeof self&&self)return self;if("object"===typeof window&&window)return window;throw new Error("Unable to resolve global `this`")};t.exports=function(){if(this)return this;try{Object.defineProperty(Object.prototype,"__global__",{get:function(){return this},configurable:!0})}catch(t){return n()}try{return __global__||n()}finally{delete Object.prototype.__global__}}()},kZhk:function(t,e,n){"use strict";n.d(e,"e",(function(){return rt})),n.d(e,"f",(function(){return it})),n.d(e,"i",(function(){return at})),n.d(e,"h",(function(){return ut})),n.d(e,"g",(function(){return st})),n.d(e,"j",(function(){return ht})),n.d(e,"d",(function(){return dt})),n.d(e,"c",(function(){return vt})),n.d(e,"a",(function(){return yt})),n.d(e,"b",(function(){return gt}));var r=n("fGyu"),o=n("ja9M"),i=n("f4fR"),a=function(t){var e=t.context_page_id,n=void 0===e?Object(i.g)("context_page_id"):e,r=t.context_page_type,o=void 0===r?Object(i.g)("context_page_type"):r,a=t.destination,c=void 0===a?Object(i.g)("destination"):a;return function(){return{context_page_id:String(n),context_page_type:o,destination:c}}},c={required_layers:[a],type:"web_addressability"},u={required_layers:[a],type:"web_pageview"},s={required_layers:[a,function(t){var e=t.total_duration,n=void 0===e?Object(i.g)("total_duration"):e,r=t.active_duration,o=void 0===r?Object(i.g)("active_duration"):r;return function(){return{total_duration:n,active_duration:o}}}],type:"web_time_spent"},f={required_layers:[a,function(t){var e=t.experiment_id,n=void 0===e?Object(i.g)("experiment_id"):e;return{experiment_id:Object(i.e)(n)}}],type:"web_ab_test"},l=function(t){var e=t.item_name,n=void 0===e?Object(i.g)("item_name"):e,r=t.item_type,o=void 0===r?Object(i.g)("item_type"):r,a=t.position_in_subunit,c=t.position_in_unit;return function(){return{item_name:String(n),item_type:o,position_in_subunit:Object(i.c)(a),position_in_unit:Object(i.c)(c)}}},p=function(t){var e=t.subunit_name,n=void 0===e?"":e,r=t.subunit_type,o=void 0===r?"":r;return function(){return{subunit_name:Object(i.b)(n.toString()),subunit_type:o}}},h=function(t){var e=t.unit_name,n=void 0===e?Object(i.g)("unit_name"):e,r=t.unit_type,o=void 0===r?Object(i.g)("unit_type"):r;return function(){return{unit_type:o,unit_name:Object(i.b)(n)}}},d={flush:!0,required_layers:[function(t){var e=t.action_type,n=void 0===e?Object(i.g)("action_type"):e,r=t.action_value,o=void 0===r?Object(i.g)("action_value"):r;return function(){return{action_type:n,action_value:o.toString()}}},a,l,p,h],type:"web_content_action"},v=function(t){var e=t.data_source_algorithm,n=t.data_source_algorithm_version,r=t.data_source_name,o=void 0===r?"":r;return function(){return{data_source_algorithm:Object(i.a)(Object(i.e)(e)),data_source_algorithm_version:Object(i.a)(Object(i.e)(n)),data_source_name:decodeURIComponent(o)}}},y=function(t){var e=t.item_name,n=void 0===e?Object(i.g)("item_name"):e,r=t.target_content_id,o=void 0===r?n:r,a=t.target_content_type,c=void 0===a?Object(i.g)("target_content_type"):a;return function(){return{target_content_id:String(o),target_content_type:c}}},g={flush:!0,required_layers:[a,v,y,l,p,h],type:"web_internal_link"},m={flush:!0,required_layers:[a,function(t){var e=t.target_content_url,n=void 0===e?Object(i.g)("target_content_url"):e;return function(){return{target_content_url:n}}},l,p,h],type:"web_external_link"},b={required_layers:[a,v,y,l,p,h],type:"web_impression"},_=n("zjfJ");function w(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function O(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?w(Object(n),!0).forEach((function(e){Object(_.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):w(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var E=function(t){var e=["web_internal_link","web_external_link"],n=Date.now(),r=n,o=0,i=Date.now(),a=1,c=!1,u=function(){for(var u=arguments.length,s=new Array(u),f=0;f<u;f++)s[f]=arguments[f];n=Date.now(),r=n,o=0,a=1,c=!1;var l=null,p=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=Date.now(),i={active_duration:o-r,total_duration:o-n};i.total_duration>0&&t.apply(void 0,[{layers:[i,e]}].concat(s)),n=Date.now(),r=Date.now()},h=function(){if("hidden"===document.visibilityState){c=!0,p({time_spent_event_type:"page_visibility"}),cancelAnimationFrame(l)}else"visible"===document.visibilityState&&(r=Date.now(),setTimeout((function(){return c=!1}),2e3),v(i,a))},d=function(t){var o=t.detail;if(o.find((function(t){return e.includes(t.type)}))){var i=o[0];r=n;var a=O({},i);delete a.event_ts,delete a.event_uuid,delete a.type,delete a.source,a.time_spent_event_type="link_click",p(a)}},v=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;function n(t){var e=Date.now()-i;if(Math.floor(e/1e3)>=t&&!c){c=!0;p({time_spent_event_type:"heartbeat"}),setTimeout((function(){return c=!1}),1e3),i=Date.now(),t=t<5?t+2:10*Math.pow(2,Math.floor((o-3)/2)),a=t,o++}cancelAnimationFrame(l),l=requestAnimationFrame((function(){return n(t)}))}i=t||Date.now(),n(e)};return v(),document.addEventListener("visibilitychange",h),window.addEventListener("cet-event",d),function(){cancelAnimationFrame(l),document.removeEventListener("visibilitychange",h),window.removeEventListener("cet-event",d)}};return function(){return u.apply(void 0,arguments)}},j=function(t){return function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];var i=function(e){t.apply(void 0,[{event:e,layers:[{click_type:"left"}]}].concat(r))},a=function(e){t.apply(void 0,[{event:e,layers:[{click_type:"right"}]}].concat(r))};return e.addEventListener("click",i),e.addEventListener("contextmenu",a),function(){e.removeEventListener("click",i),e.removeEventListener("contextmenu",a)}}},x=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.threshold,r=void 0===n?.5:n,o=e.visibility_time,i=void 0===o?1e3:o;var a=new Map,c=new Map,u=function(t,e){t.forEach((function(t){if(a.has(t.target)){if(!t.isIntersecting)return clearTimeout(c.get(t.target)),void c.delete(t.target);if(!(c.has(t.target)||t.intersectionRatio<r)){var n=setTimeout((function(){a.get(t.target).call(null),e.unobserve(t.target),c.delete(t.target)}),i);c.set(t.target,n)}}}))},s=new IntersectionObserver(u,{threshold:[0,r,1]}),f=function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return a.has(e)||(a.set(e,(function(){return t.apply(void 0,r)})),s.observe(e)),function(){a.delete(e),s.unobserve(e),c.has(e)&&(clearTimeout(c.get(e)),c.delete(e))}};return function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return f.apply(void 0,[t].concat(n))}},S=n("QsI/"),P=function(){var t="undefined"!==typeof navigator&&(navigator.connection||navigator.mozConnection||navigator.webkitConnection);return t?t.effectiveType:""},L=function(){return function(){return{connection_type:P()}}};function k(){k=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),c=new T(r||[]);return o(a,"_invoke",{value:x(t,n,c)}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",v={};function y(){}function g(){}function m(){}var b={};s(b,a,(function(){return this}));var _=Object.getPrototypeOf,w=_&&_(_(A([])));w&&w!==n&&r.call(w,a)&&(b=w);var O=m.prototype=y.prototype=Object.create(b);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function n(o,i,a,c){var u=l(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return n("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=S(c,r);if(u){if(u===v)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var s=l(e,n,r);if("normal"===s.type){if(o=r.done?d:"suspendedYield",s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=d,r.method="throw",r.arg=s.arg)}}}function S(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,S(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=l(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function A(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(O,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,u,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},E(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new j(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(O),s(O,u,"Generator"),s(O,a,(function(){return this})),s(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=A,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(L),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),L(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;L(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:A(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}var T=function(t,e){var n=e.flush,o=void 0!==n&&n,i=e.layers,c=void 0===i?[]:i,u=e.sample_rate,s=void 0===u?.1:u,f=e.type;return Object(S.a)(k().mark((function e(){var n,i,u,l=arguments;return k().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(n=l.length,i=new Array(n),u=0;u<n;u++)i[u]=l[u];return e.next=3,t.apply(void 0,[{flush:o,required_layers:[a,L],sample_rate:s,type:f}].concat(Object(r.a)(c),i));case 3:case"end":return e.stop()}}),e)})))},A=function(){"__trackAbandons"in window&&document.removeEventListener("visibilitychange",window.__trackAbandons)},C=n("H8Cd"),I=function(t){Object(C.d)((function(e){t({metric_name:"interaction-to-next-paint",metric_type:"custom",metric_value:e.value})}))},N=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:250,n="";try{for(;t&&9!==t.nodeType;){var r=t.id?"#".concat(t.id):t.nodeName.toLowerCase()+(t.className&&t.className.length?".".concat(Array.from(t.classList.values()).join(".")):"");if(n.length+r.length>e-1)return n||r;if(n=n?"".concat(r," > ").concat(n):r,t.id)break;t=t.parentNode}}catch(o){}return n},D=function(t){Object(C.a)((function(e){var n=e.entries;if(n.length)try{var o=Object(r.a)(n);o.sort((function(t,e){return t&&t.value>e.value?-1:1})),o.slice(0,3).forEach((function(e,n){if(e&&e.sources&&e.sources.length){var r=e.sources.reduce((function(t,e){return!!t.node&&(t.previousRect.width*t.previousRect.height>e.previousRect.width*e.previousRect.height?t:e)}));t({metric_name:"largest-layout-shift-node-".concat(n),metric_type:"custom",metric_value:e.value,metric_metadata_type:"css-selector",metric_metadata_value:N(r.node)||""})}}))}catch(i){}}))},M=function(t){Object(C.c)((function(){if("PerformanceLongTaskTiming"in window){var e=new PerformanceObserver((function(n){var r=n.getEntries(),o=r.length,i=r.reduce((function(t,e){return t+e.duration-50}),0);o&&(t({metric_name:"cumulative-longtask-count",metric_type:"custom",metric_value:o}),t({metric_name:"cumulative-blocking-time",metric_type:"custom",metric_value:i})),e.disconnect()}));e.observe({type:"longtask",buffered:!0})}}))},R=n("1woP"),F=function(t){Object(C.a)((function(){if(performance.memory){var e=performance.memory,n={jsHeapSizeLimit:e.jsHeapSizeLimit,totalJSHeapSize:e.totalJSHeapSize,usedJSHeapSize:e.usedJSHeapSize};t({metric_name:"memory",metric_type:"custom",metric_value:0,metric_metadata_type:"json",metric_metadata_value:JSON.stringify(n)})}}))};function z(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function U(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?z(Object(n),!0).forEach((function(e){Object(_.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):z(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var G=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.layers,r=void 0===n?[]:n,o=e.profiling,i=void 0===o?{}:o,a=e.sample_rate,c=void 0===a?.1:a,u={layers:r,sample_rate:c,type:"web_performance_metric"},s=T(t,U(U({},u),{},{flush:!0})),f=T(t,u),l=T(t,U(U({},u),{},{sample_rate:1}));A(),I(f),D(s),M(f),Object(R.a)(l,i),F(s)},H=function(t){Object(C.a)((function(e){t({metric_name:"cumulative-layout-shift",metric_type:"web-vital",metric_value:e.value})}))},B=function(t){Object(C.b)((function(e){t({metric_name:"first-contentful-paint",metric_type:"web-vital",metric_value:e.value})}))},q=function(t){Object(C.c)((function(e){var n=e.entries,r=e.value,o=n[n.length-1],i=o.startTime,a=o.target,c={css:N(a),timeStamp:i};t({metric_name:"first-input-delay",metric_type:"web-vital",metric_value:r,metric_metadata_type:"json",metric_metadata_value:JSON.stringify(c)})}))},K=function(t){Object(C.e)((function(e){var n=e.entries,r=e.value,o=n[n.length-1],i=o.element,a=o.size,c=o.url,u=void 0===c?"":c,s={css:N(i),size:a,url:u};t({metric_name:"largest-contentful-paint",metric_type:"web-vital",metric_value:r,metric_metadata_type:"json",metric_metadata_value:JSON.stringify(s)})}))};function J(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Y(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?J(Object(n),!0).forEach((function(e){Object(_.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):J(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var V=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.layers,r=void 0===n?[]:n,o=e.sample_rate,i=void 0===o?.1:o,a={layers:r,sample_rate:i,type:"web_performance_metric"},c=T(t,a),u=T(t,Y(Y({},a),{},{flush:!0}));H(u),B(c),q(u),K(u)},W=n("zygG"),Z=function(t){if("PerformanceNavigationTiming"in window){var e=function(){var e=performance.getEntriesByType("navigation"),n=Object(W.a)(e,1)[0];t({timing_duration:n.duration,timing_name:n.name,timing_start_time:n.startTime,timing_type:n.entryType,resource_connect_end:n.connectEnd,resource_connect_start:n.connectStart,resource_decoded_body_size:void 0===n.decodedBodySize?null:n.decodedBodySize,resource_domain_lookup_end:n.domainLookupEnd,resource_domain_lookup_start:n.domainLookupStart,resource_encoded_body_size:void 0===n.encodedBodySize?null:n.encodedBodySize,resource_fetch_start:n.fetchStart,resource_initiator_type:n.initiatorType,resource_next_hop_protocol:void 0===n.nextHopProtocol?null:n.nextHopProtocol,resource_redirect_end:n.redirectEnd,resource_redirect_start:n.redirectStart,resource_request_start:n.requestStart,resource_response_end:n.responseEnd,resource_response_start:n.responseStart,resource_secure_connection_start:void 0===n.secureConnectionStart?null:n.secureConnectionStart,resource_transfer_size:void 0===n.transferSize?null:n.transferSize,resource_worker_start:void 0===n.workerStart?null:n.workerStart,navigation_dom_complete:n.domComplete,navigation_dom_content_loaded_event_end:n.domContentLoadedEventEnd,navigation_dom_content_loaded_event_start:n.domContentLoadedEventStart,navigation_dom_interactive:n.domInteractive,navigation_load_event_end:n.loadEventEnd,navigation_load_event_start:n.loadEventStart,navigation_redirect_count:n.redirectCount,navigation_type:n.type,navigation_unload_event_end:n.unloadEventEnd,navigation_unload_event_start:n.unloadEventStart})};"complete"===document.readyState?e():window.addEventListener("load",(function(){return requestAnimationFrame(e)}))}},X=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.layers,r=void 0===n?[]:n,o=e.sample_rate,i=void 0===o?.1:o,a={layers:r,sample_rate:i,type:"web_performance_navigation_timing"};Z(T(t,a))};function Q(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function $(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Q(Object(n),!0).forEach((function(e){Object(_.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Q(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var tt=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.layers,r=void 0===n?[]:n,o=e.sample_rate,i=void 0===o?.1:o,a=e.profiling,c=void 0===a?{}:a,u={layers:r,sample_rate:i};V(t,u),X(t,u),G(t,$($({},u),{},{profiling:c}))},et=n("v0uu"),nt=Object(o.a)({env:et.CLUSTER,source:"web_bf"}),rt=function(t){var e=t.context_page_type,n=t.context_page_id;return function(){return{destination:"buzzfeed",page_edition:"en-us",context_page_type:e,context_page_id:n}}},ot=function(t){return function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];nt.apply(void 0,[t,rt(e)].concat(r))}},it=ot(c),at=ot(u),ct=ot(s),ut=ot(f),st=ot(d),ft=ot(g),lt=ot(m),pt=ot(b),ht=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];tt(nt,{layers:[rt(t)].concat(n)})},dt=E((function(t){for(var e=t.layers,n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];ct.apply(void 0,o.concat(Object(r.a)(e)))})),vt=j((function(t){for(var e=t.layers,n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];ft.apply(void 0,o.concat(Object(r.a)(e)))})),yt=j((function(t){for(var e=t.layers,n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];lt.apply(void 0,o.concat(Object(r.a)(e)))})),gt=x(pt)},"kl+4":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return u})),n.d(e,"d",(function(){return o})),n.d(e,"e",(function(){return c})),n.d(e,"f",(function(){return i}));var r=!1,o=function(){return!r&&window.matchMedia&&window.matchMedia("(prefers-reduced-motion: reduce)").matches},i=function(){return!r&&HTMLImageElement&&"loading"in HTMLImageElement.prototype},a=function(t){return t.includes(".gif")},c=function(t){if(!t)return[t,[]];var e=t.split("?"),n=e[1]?e[1].split("&"):[];return[e[0],n]},u=function(t,e){if(!t)return"";var n=e.length>0?"?".concat(e.join("&")):"";return"".concat(t).concat(n)}},kxKB:function(t,e,n){t.exports={content:"content__2qE1M26HSu",content__editorial:"content__editorial__2FRh0o_WhN",contentBackground:"contentBackground__1pk4-H5fG7",contentBackground__trophies:"contentBackground__trophies__3x26gmmk4O",backgroundGray:"backgroundGray__2cwraWmCKt",backgroundWhite:"backgroundWhite__9haPM1dEOA",sidebarWrapper:"sidebarWrapper__3T_g6N5S2b",feeds:"feeds__7CbNK1Qfq9"}},ky1M:function(t,e,n){"use strict";var r=n("t7Nw"),o=n("wV7X"),i=n("UBbi"),a=Error.captureStackTrace;t.exports=function(e){var n=new Error(e),c=arguments[1],u=arguments[2];return i(u)||o(c)&&(u=c,c=null),i(u)&&r(n,u),i(c)&&(n.code=c),a&&a(n,t.exports),n}},"l/SJ":function(t,e,n){"use strict";t.exports=2147483647},lOqH:function(t,e,n){"use strict";n.d(e,"a",(function(){return st}));var r={};n.r(r),n.d(r,"doesConsentApply",(function(){return w})),n.d(r,"needsConsent",(function(){return O})),n.d(r,"needsGDPRConsent",(function(){return E})),n.d(r,"needsCCPAConsent",(function(){return j})),n.d(r,"isConsentStringCookieSet",(function(){return x})),n.d(r,"getConsentFramework",(function(){return S})),n.d(r,"getConsentFrameworkAsString",(function(){return P}));var o={};n.r(o),n.d(o,"configure",(function(){return U})),n.d(o,"init",(function(){return q})),n.d(o,"setTCFListener",(function(){return K})),n.d(o,"getInAppTCData",(function(){return J})),n.d(o,"getTCData",(function(){return Y})),n.d(o,"uspApi",(function(){return V})),n.d(o,"getUSPData",(function(){return W})),n.d(o,"setUSPDefaultData",(function(){return Z}));var i={};n.r(i),n.d(i,"fetchAdPurposeConsent",(function(){return Q})),n.d(i,"isEligibleForCCPA",(function(){return $})),n.d(i,"fetchCCPAValue",(function(){return tt})),n.d(i,"fetchCCPAOptOut",(function(){return et})),n.d(i,"fetchRawVendorConsents",(function(){return nt})),n.d(i,"fetchRawPublisherConsents",(function(){return rt})),n.d(i,"fetchTrackingConsent",(function(){return ot})),n.d(i,"hasConsented",(function(){return it}));var a=n("266R"),c=n("ERkP"),u=n("PMPH"),s=n("YcZG"),f=n("CZvW"),l=!0,p="\ud83d\udd0f [bf consent] >>",h=Object(s.a)()?{}:Object(f.d)(window.location.search),d=function(){return!Object(s.a)()&&"true"===h["bf-consent-debug"]},v=function(){if(d()&&h["bf-consent-debug"]){var t;l&&(console.log(p,"====== BF Consent Debug Mode Enabled ====="),l=!1);for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];(t=console).log.apply(t,[p].concat(n))}},y=function(){var t=!1,e=!1,n=!1,r=!1;return function(o){return"ccpa"===o?(t||(v("isDebugModeFor('".concat("ccpa","')"),"undefined"===typeof h.ccpa?"Unset -> using BF Cookie":"Force -> ".concat(h.ccpa)),t=!0),"true"===h.ccpa):"gdpr"===o?(e||(v("isDebugModeFor('".concat("gdpr","')"),"undefined"===typeof h.gdpr?"Unset -> using BF Cookie":"Force -> ".concat(h.gdpr)),e=!0),"true"===h.gdpr):"idnml"===o?(n||(v("isDebugModeFor('".concat("idnml","')"),"undefined"===typeof h.idnml?"Unset -> using BF Cookie":"Force -> ".concat(h.idnml)),n=!0),"true"===h.idnml):(r||(v("isDebugModeFor('".concat(o,"')"),"No Setup for '".concat(o,"'")),r=!0),!1)}}(),g=[754],m=[360],b={INFORMATION_STORAGE:0,BASIC_ADS:1,CREATE_PERSONALISED_ADS:2,SELECT_PERSONALISED_ADS:3,CREATE_PERSONALISED_CONTENT_PROFILE:4,SELECT_PERSONALISED_CONTENT:5,MEASURE_AD_PERFORMANCE:6,MEASURE_CONTENT_PERFORMANCE:7,APPLY_MARKET_RESEARCH:8,DEVELOP_PRODUCTS:9},_={"buzzfeed.bio":"30e86c5d-cf1a-40d8-b336-0b96685da11b","buzzfeed.com":"92123775-81ac-4a1b-b056-24d62d0e177f","buzzfeed.io":"39435fbf-e858-4eac-a529-5e12c567dc68","buzzfeednews.com":"38444766-23c0-4265-b9b9-49714f31124a","tasty.co":"0fb7adfb-5bc5-42bf-b659-28c1c288282c"},w=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"ccpa";return y(t)?"true":"ccpa"===t?u.a.get("ccpa"):"gdpr"===t?u.a.get("gdpr"):"false"},O=function(){if(Object(s.a)())return!1;var t=w("gdpr"),e=w("ccpa");return"true"===t||"true"===e},E=function(){return!Object(s.a)()&&"true"===w("gdpr")},j=function(){return!Object(s.a)()&&"true"===w("ccpa")},x=function(){var t=u.a.get("eupubconsent-v2"),e=u.a.get("usprivacy");return"string"===typeof t||"string"===typeof e&&"Y"===e.charAt(2)},S=function(){return function(){!function(){for(var t,e=[],n=window;n;){try{if(n.frames.__tcfapiLocator){t=n;break}}catch(r){}if(n===window.top)break;n=n.parent}t||(!function t(){var e=n.document,r=!!n.frames.__tcfapiLocator;if(!r)if(e.body){var o=e.createElement("iframe");o.style.cssText="display:none",o.name="__tcfapiLocator",e.body.appendChild(o)}else setTimeout(t,5);return!r}(),n.__tcfapi=function(){var t,n=arguments;if(!n.length)return e;if("setGdprApplies"===n[0])n.length>3&&2===n[2]&&"boolean"===typeof n[3]&&(t=n[3],"function"===typeof n[2]&&n[2]("set",!0));else if("ping"===n[0]){var r={gdprApplies:t,cmpLoaded:!1,cmpStatus:"stub"};"function"===typeof n[2]&&n[2](r)}else e.push(n)},n.addEventListener("message",(function(t){var e="string"===typeof t.data,n={};try{n=e?JSON.parse(t.data):t.data}catch(r){}var o=n.__tcfapiCall;o&&window.__tcfapi(o.command,o.version,(function(n,r){var i={__tcfapiReturn:{returnValue:n,success:r,callId:o.callId}};e&&(i=JSON.stringify(i)),t.source.postMessage(i,"*")}),o.parameter)}),!1))}();"undefined"===typeof window.__uspapi&&(window.__uspapi=function t(){var e=arguments;typeof window.__uspapi!==t&&setTimeout((function(){"undefined"!==typeof window.__uspapi&&window.__uspapi.apply(window.__uspapi,e)}),500)});var t=window.Promise;return t.all([t.race([new t((function(t){window.__tcfapi("getInAppTCData",2,t)})),new t((function(t,e){setTimeout((function(){e("__tcfapi stub is defined, but CMP has not loaded within 10000ms")}),1e4)}))]),t.race([new t((function(t){window.__uspapi("getUSPData",1,t)})),new t((function(t,e){setTimeout((function(){e("__uspapi stub is defined, but CMP has not loaded within 10000ms")}),1e4)}))])])}},P=function(){return"(".concat(S().toString(),")();")},L=n("BmUA"),k=n.n(L),T=n("ISyl"),A=n("ChH/"),C=n("VX0Q"),I=Object(A.a)((function t(){var e,n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=r.unsubscribe,i=void 0===o?function(){}:o;Object(C.a)(this,t);var a=new Promise((function(t,r){e=t,n=r}));return a.resolve=e,a.reject=n,a.unsubscribe=i,a}));function N(){N=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),c=new k(r||[]);return o(a,"_invoke",{value:x(t,n,c)}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",v={};function y(){}function g(){}function m(){}var b={};s(b,a,(function(){return this}));var _=Object.getPrototypeOf,w=_&&_(_(T([])));w&&w!==n&&r.call(w,a)&&(b=w);var O=m.prototype=y.prototype=Object.create(b);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function n(o,i,a,c){var u=l(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return n("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=S(c,r);if(u){if(u===v)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var s=l(e,n,r);if("normal"===s.type){if(o=r.done?d:"suspendedYield",s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=d,r.method="throw",r.arg=s.arg)}}}function S(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,S(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=l(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function k(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(O,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,u,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},E(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new j(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(O),s(O,u,"Generator"),s(O,a,(function(){return this})),s(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(L),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),L(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;L(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:T(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}var D="https://cdn.cookielaw.org",M=new I,R=new I;R.then((function(t){v("=== OT READY ===",t)})),M.then((function(t){v("=== CMP READY ===",t)}));var F,z=function(){if(window.location.search.includes("display-consent"))if(v("forcing display consent"),window._sp_)var t=setInterval((function(){var e;null!==(e=window._sp_.gdpr)&&void 0!==e&&e.loadPrivacyManagerModal&&(window._sp_.gdpr.loadPrivacyManagerModal(1161871),clearInterval(t))}),500);else var e=setInterval((function(){window.OneTrust&&document.getElementsByClassName("onetrust-pc-dark-filter").length>0&&(window.OneTrust.ToggleInfoDisplay(),clearInterval(e))}),500)};function U(){F.set({useFallback:!1})}function G(){var t=Object(T.a)().toLowerCase();return"gb"===t||"uk"===t||"ie"===t||y("idnml")}function H(t){var e=t.isBFN;return new Promise((function(t,n){if(!e&&G()){v("Loading SourcePoint CMP...");var r=document.createElement("script");r.innerHTML='"use strict";function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}!function(){var t=function(){var t,e,o=[],n=window,r=n;for(;r;){try{if(r.frames.__tcfapiLocator){t=r;break}}catch(t){}if(r===n.top)break;r=r.parent}t||(!function t(){var e=n.document,o=!!n.frames.__tcfapiLocator;if(!o)if(e.body){var r=e.createElement("iframe");r.style.cssText="display:none",r.name="__tcfapiLocator",e.body.appendChild(r)}else setTimeout(t,5);return!o}(),n.__tcfapi=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];if(!n.length)return o;"setGdprApplies"===n[0]?n.length>3&&2===parseInt(n[1],10)&&"boolean"==typeof n[3]&&(e=n[3],"function"==typeof n[2]&&n[2]("set",!0)):"ping"===n[0]?"function"==typeof n[2]&&n[2]({gdprApplies:e,cmpLoaded:!1,cmpStatus:"stub"}):o.push(n)},n.addEventListener("message",(function(t){var e="string"==typeof t.data,o={};if(e)try{o=JSON.parse(t.data)}catch(t){}else o=t.data;var n="object"===_typeof(o)&&null!==o?o.__tcfapiCall:null;n&&window.__tcfapi(n.command,n.version,(function(o,r){var a={__tcfapiReturn:{returnValue:o,success:r,callId:n.callId}};t&&t.source&&t.source.postMessage&&t.source.postMessage(e?JSON.stringify(a):a,"*")}),n.parameter)}),!1))};"undefined"!=typeof module?module.exports=t:t()}();',document.head.appendChild(r);var o=document.createElement("script");o.innerHTML="\n  window._sp_queue = [];\n  window._sp_ = {\n    config: {\n      accountId: 1746,\n      baseEndpoint: 'https://cdn.privacy-mgmt.com',\n      gdpr: { },\n      events: {\n        onMessageChoiceSelect: function() {\n          console.log('[event] onMessageChoiceSelect', arguments);\n        },\n        onMessageReady: function() {\n          console.log('[event] onMessageReady', arguments);\n        },\n        onMessageChoiceError: function() {\n          console.log('[event] onMessageChoiceError', arguments);\n        },\n        onPrivacyManagerAction: function() {\n          console.log('[event] onPrivacyManagerAction', arguments);\n        },\n        onPMCancel: function() {\n          console.log('[event] onPMCancel', arguments);\n        },\n        onMessageReceiveData: function() {\n          console.log('[event] onMessageReceiveData', arguments);\n        },\n        onSPPMObjectReady: function() {\n          console.log('[event] onSPPMObjectReady', arguments);\n        },\n        onConsentReady: function (consentUUID, euconsent) {\n          console.log('[event] onConsentReady', arguments);\n        },\n        onError: function() {\n          console.log('[event] onError', arguments);\n        },\n      }\n    }\n  }\n",document.head.appendChild(o);var i=document.createElement("script");return i.src="https://cdn.privacy-mgmt.com/unified/wrapperMessagingWithoutDetection.js",i.setAttribute("async",""),document.head.appendChild(i),void t()}if(v("Loading OneTrust CMP..."),j()){var a=document.createElement("script");a.setAttribute("ccpa-opt-out-ids","SPD_BG"),a.setAttribute("ccpa-opt-out-geo","us"),a.setAttribute("ccpa-opt-out-lspa","false"),a.onerror=function(){n("CMP script otCCPAiab.js failed to load")},a.src="".concat(D,"/opt-out/otCCPAiab.js"),a.type="text/javascript",document.head.appendChild(a)}var c=document.createElement("script"),u=document.createElement("script"),s=function(){var t=d()?"-test":"",e=window.location.hostname;if(!e)return"".concat(_["buzzfeed.com"]).concat(t);var n=Object.keys(_).find((function(t){return e.includes(t)}));return n?"".concat(_[n]).concat(t):"".concat(_["buzzfeed.com"]).concat(t)}();v("OneTrust script ID:",s),c.setAttribute("data-domain-script",s),c.onload=function(){return t()},c.onerror=function(){n("CMP script stub failed to load")},c.src="".concat(D,"/scripttemplates/otSDKStub.js"),c.async=!0,c.type="text/javascript",u.text="function OptanonWrapper() { }",u.onerror=function(){n("CMP script OptanonWrapper failed to load")},u.type="text/javascript",document.head.appendChild(c),document.head.appendChild(u)}))}function B(){return(B=Object(a.a)(N().mark((function t(){var e,n,r=arguments;return N().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e=r.length>0&&void 0!==r[0]?r[0]:{isBFN:!1},!document.querySelector('script[src*="'.concat(D,'"]'))){t.next=4;break}return t.abrupt("return");case 4:return t.prev=4,t.next=7,H(e);case 7:t.next=13;break;case 9:t.prev=9,t.t0=t.catch(4),v("CMP load error! throwing error..."),console.error("Error loading CMP",t.t0);case 13:n=setInterval((function(){(window._sp_||window.OneTrust&&"function"===typeof window.OneTrust.getGeolocationData)&&(clearInterval(n),window.__uspapi?R.resolve({ccpaApplies:!0,gdprApples:!1,tcfapi:null,uspapi:window.__uspapi}):window.__tcfapi?R.resolve({ccpaApplies:!1,gdprApples:!0,tcfapi:window.__tcfapi,uspapi:null}):R.resolve({ccpaApplies:!1,gdprApplies:!1,tcfapi:null,uspapi:null}))}),10),z(),Promise.race([new Promise((function(t){G()&&t({ccpaApplies:!1,gdprApplies:!0,tcfapi:window.__tcfapi,uspapi:null})})),R,new Promise((function(t){var e=setInterval((function(){window.__uspapi&&(t({ccpaApplies:!0,gdprApples:!1,tcfapi:null,uspapi:window.__uspapi}),clearInterval(e))}),10)})),new Promise((function(t){var e=setInterval((function(){window.__tcfapi&&(t({ccpaApplies:!1,gdprApplies:!0,tcfapi:window.__tcfapi,uspapi:null}),clearInterval(e))}),10)})),new Promise((function(t,e){setTimeout((function(){e("CMP has not loaded within 10000ms")}),1e4)}))]).then((function(t){M.resolve(t)})).catch((function(t){v("CMP timed out! throwing error...",t)}));case 16:case"end":return t.stop()}}),t,null,[[4,9]])})))).apply(this,arguments)}!function(){var t;(F=new Promise((function(e){return t=e}))).set=t}();var q=k()((function(){return B.apply(this,arguments)})),K=function(){var t=Object(a.a)(N().mark((function t(e){var n,r,o;return N().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,M;case 2:if(n=t.sent,r=n.gdprApplies,o=n.tcfapi,r){t.next=8;break}return v("setTCFListener -- gdpr does not apply"),t.abrupt("return");case 8:o("addEventListener",2,(function(t,n){e({tcData:t,success:n})}));case 9:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),J=function(){var t=Object(a.a)(N().mark((function t(){var e;return N().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,M;case 2:if(e=t.sent,e.gdprApplies){t.next=7;break}return v("getInAppTCData -- gdpr does not apply"),t.abrupt("return",Promise.resolve({tcData:{gdprApplies:!1}}));case 7:return t.abrupt("return",new Promise((function(t,e){window.__tcfapi("getInAppTCData",2,(function(n,r){r?(v("getInAppTCData Success",n,r),t({tcData:n,success:r})):(v("getInAppTCData Failed",n,r),e(r))}))})));case 8:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),Y=J,V=function(){var t=Object(a.a)(N().mark((function t(e){var n;return N().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,M;case 2:if(n=t.sent,n.ccpaApplies){t.next=6;break}return t.abrupt("return",{uspData:{version:1,uspString:"1---"}});case 6:return t.abrupt("return",new Promise((function(t){window.__uspapi(e,1,(function(e,n){t({uspData:e,success:n})}))})));case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),W=function(){var t=Object(a.a)(N().mark((function t(){return N().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,M;case 2:return v("getUSPData",V("getUSPData")),t.abrupt("return",V("getUSPData"));case 4:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),Z=function(){var t=Object(a.a)(N().mark((function t(){return N().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,M;case 2:return t.abrupt("return",V("setUspDftData"));case 3:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();function X(){X=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),c=new k(r||[]);return o(a,"_invoke",{value:x(t,n,c)}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",v={};function y(){}function g(){}function m(){}var b={};s(b,a,(function(){return this}));var _=Object.getPrototypeOf,w=_&&_(_(T([])));w&&w!==n&&r.call(w,a)&&(b=w);var O=m.prototype=y.prototype=Object.create(b);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function n(o,i,a,c){var u=l(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return n("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=S(c,r);if(u){if(u===v)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var s=l(e,n,r);if("normal"===s.type){if(o=r.done?d:"suspendedYield",s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=d,r.method="throw",r.arg=s.arg)}}}function S(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,S(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=l(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function k(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(O,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,u,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},E(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new j(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(O),s(O,u,"Generator"),s(O,a,(function(){return this})),s(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(L),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),L(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;L(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:T(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}var Q=function(){var t=Object(a.a)(X().mark((function t(){var e,n,r,o,i,a,c,u=arguments;return X().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=u.length>0&&void 0!==u[0]?u[0]:null,t.next=3,J();case 3:if(n=t.sent,r=n.success,!(o=n.tcData)||!("gdprApplies"in o)||o.gdprApplies){t.next=8;break}return t.abrupt("return",!0);case 8:if("1"===(i=o.purpose.consents)[b.INFORMATION_STORAGE]){t.next=11;break}return t.abrupt("return",!1);case 11:return a=o.vendor.consents||"",c=null===e||e&&a&&e.every((function(t){return"1"===a[t]})),t.abrupt("return",r&&i&&"1"===i[b.INFORMATION_STORAGE]&&"1"===i[b.CREATE_PERSONALISED_ADS]&&"1"===i[b.SELECT_PERSONALISED_ADS]&&"1"===i[b.APPLY_MARKET_RESEARCH]&&"1"===i[b.DEVELOP_PRODUCTS]&&c);case 14:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),$=function(){var t=Object(a.a)(X().mark((function t(){var e,n,r,o;return X().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(v("isEligibleForCCPA...",j()),j()){t.next=3;break}return t.abrupt("return",!1);case 3:return t.next=5,W();case 5:return e=t.sent,n=e.success,r=e.uspData,o=r.uspString,v("uspData",r,n),t.abrupt("return","1---"!==o);case 11:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),tt=function(){var t=Object(a.a)(X().mark((function t(){return X().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,$();case 2:return t.sent&&document.querySelector("html").classList.add("show-ccpa"),t.abrupt("return",u.a.get("usprivacy"));case 5:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),et=function(){var t=Object(a.a)(X().mark((function t(){var e;return X().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,tt();case 2:return e=t.sent,t.abrupt("return","string"===typeof e&&"Y"===e.charAt(2));case 4:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),nt=function(){var t=Object(a.a)(X().mark((function t(e){var n,r,o,i;return X().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,J();case 2:if(n=t.sent,r=n.tcData,o=r.vendor.consents,e){t.next=7;break}return t.abrupt("return",o);case 7:return i={},e.forEach((function(t){i[t]=o[t]})),t.abrupt("return",i);case 10:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),rt=function(){var t=Object(a.a)(X().mark((function t(e){var n,r,o,i;return X().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,J();case 2:if(n=t.sent,r=n.tcData,o=r.publisher.consents,e){t.next=7;break}return t.abrupt("return",o);case 7:return i={},e.forEach((function(t){i[t]=o[t]})),t.abrupt("return",i);case 10:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),ot=function(){var t=Object(a.a)(X().mark((function t(){var e,n,r,o,i,a,c,u=arguments;return X().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=u.length>0&&void 0!==u[0]?u[0]:null,t.next=3,et();case 3:if(!t.sent){t.next=6;break}return t.abrupt("return",!1);case 6:if(E()){t.next=8;break}return t.abrupt("return",!0);case 8:return t.next=10,J();case 10:if(n=t.sent,r=n.tcData,o=n.success,t.prev=13,!("gdprApplies"in r)||r.gdprApplies){t.next=16;break}return t.abrupt("return",!0);case 16:return i=r.vendor.consents||"",a=r.purpose.consents||"",c=null===e||e&&i&&e.every((function(t){return"1"===i[t]})),t.abrupt("return",o&&a&&"1"===a[b.INFORMATION_STORAGE]&&"1"===a[b.CREATE_PERSONALISED_CONTENT_PROFILE]&&c);case 22:return t.prev=22,t.t0=t.catch(13),t.abrupt("return",!1);case 25:case"end":return t.stop()}}),t,null,[[13,22]])})));return function(){return t.apply(this,arguments)}}(),it=function(){var t=Object(a.a)(X().mark((function t(e){var n,r;return X().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n={ads:Q,google:Q.bind(null,g),tracking:ot,permutive:ot.bind(null,m)},!(r=n[e])){t.next=8;break}return t.next=5,r();case 5:t.t0=t.sent,t.next=9;break;case 8:t.t0=!1;case 9:return t.abrupt("return",t.t0);case 10:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();function at(){at=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),c=new k(r||[]);return o(a,"_invoke",{value:x(t,n,c)}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",v={};function y(){}function g(){}function m(){}var b={};s(b,a,(function(){return this}));var _=Object.getPrototypeOf,w=_&&_(_(T([])));w&&w!==n&&r.call(w,a)&&(b=w);var O=m.prototype=y.prototype=Object.create(b);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function n(o,i,a,c){var u=l(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return n("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=S(c,r);if(u){if(u===v)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var s=l(e,n,r);if("normal"===s.type){if(o=r.done?d:"suspendedYield",s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=d,r.method="throw",r.arg=s.arg)}}}function S(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,S(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=l(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function k(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(O,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,u,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},E(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new j(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(O),s(O,u,"Generator"),s(O,a,(function(){return this})),s(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(L),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),L(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;L(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:T(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}var ct=r,ut=i;function st(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"tracking",e=ct.needsConsent(),n=Object(c.useState)(!e||ct.isConsentStringCookieSet()),r=n[0],o=n[1],i=Object(c.useState)(!e),u=i[0],s=i[1],f=Object(c.useState)(!e),l=f[0],p=f[1];return Object(c.useEffect)((function(){e&&function(){var e=Object(a.a)(at().mark((function e(){var n;return at().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,ut.hasConsented(t);case 2:n=e.sent,p(n),o(!0),s(!0);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()()}),[t,e]),{consentValue:l,isConsentKnown:r,isConsentReady:u}}},lwDF:function(t,e,n){"use strict";function r(){return"undefined"!==typeof __SENTRY_BROWSER_BUNDLE__&&!!__SENTRY_BROWSER_BUNDLE__}n.d(e,"a",(function(){return r}))},mcQc:function(t,e,n){"use strict";var r=n("ERkP"),o=n.n(r);var i=n("Uj0u"),a=n.n(i),c=n("fsQa"),u=o.a.createElement;e.a=Object(c.withTranslation)("common")((function(t){var e=t.internetPoints,n=t.t;return u("a",{href:"https://www.buzzfeed.com/annakopsky/internet-points-2019",title:"A Guide To Internet Points",className:a.a.internetPoints},u("svg",{className:a.a.sparklesIcon,"aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14 14"},u("path",{fill:"#FFF",fillRule:"nonzero",d:"M13.11 10.023c-1.628 0-2.724-2.3-2.724-3.95 0 1.65-1.096 3.95-2.725 3.95 1.629 0 2.725 2.318 2.725 3.95 0-1.65 1.113-3.95 2.725-3.95zM8.537 6.375C5.994 6.375 4.268 2.66 4.268 0 4.268 2.66 2.54 6.375 0 6.375c2.541 0 4.268 3.715 4.268 6.376 0-2.66 1.726-6.376 4.268-6.376z"})),u("span",{className:a.a.pointsValue},e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")),u("span",{className:a.a.pointsLabel},n("points")))}))},mqXP:function(t,e){function n(e){return"function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?(t.exports=n=function(t){return typeof t},t.exports.default=t.exports,t.exports.__esModule=!0):(t.exports=n=function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.default=t.exports,t.exports.__esModule=!0),n(e)}t.exports=n,t.exports.default=t.exports,t.exports.__esModule=!0},myWV:function(t,e,n){"use strict";n.d(e,"a",(function(){return y}));var r=n("wope"),o=n("E7fa"),i=(n("O3Et"),n("PMPH")),a=n("9rgT");function c(t,e,n){var r,o,i,a,c,u,s;for(this.version="1.0.1",this.minAlphabetLength=16,this.sepDiv=3.5,this.guardDiv=12,this.errorAlphabetLength="error: alphabet must contain at least X unique characters",this.errorAlphabetSpace="error: alphabet cannot contain spaces",this.alphabet="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",this.seps="cfhistuCFHISTU",this.minHashLength=parseInt(e,10)>0?e:0,this.salt="string"===typeof t?t:"","string"===typeof n&&(this.alphabet=n),r="",o=0,a=this.alphabet.length;o!==a;o++)-1===r.indexOf(this.alphabet[o])&&(r+=this.alphabet[o]);if(this.alphabet=r,this.alphabet.length<this.minAlphabetLength)throw this.errorAlphabetLength.replace("X",this.minAlphabetLength);if(-1!==this.alphabet.search(" "))throw this.errorAlphabetSpace;for(o=0,a=this.seps.length;o!==a;o++)-1===(i=this.alphabet.indexOf(this.seps[o]))?this.seps=this.seps.substr(0,o)+" "+this.seps.substr(o+1):this.alphabet=this.alphabet.substr(0,i)+" "+this.alphabet.substr(i+1);this.alphabet=this.alphabet.replace(/ /g,""),this.seps=this.seps.replace(/ /g,""),this.seps=this.consistentShuffle(this.seps,this.salt),(!this.seps.length||this.alphabet.length/this.seps.length>this.sepDiv)&&(1===(c=Math.ceil(this.alphabet.length/this.sepDiv))&&c++,c>this.seps.length?(u=c-this.seps.length,this.seps+=this.alphabet.substr(0,u),this.alphabet=this.alphabet.substr(u)):this.seps=this.seps.substr(0,c)),this.alphabet=this.consistentShuffle(this.alphabet,this.salt),s=Math.ceil(this.alphabet.length/this.guardDiv),this.alphabet.length<3?(this.guards=this.seps.substr(0,s),this.seps=this.seps.substr(s)):(this.guards=this.alphabet.substr(0,s),this.alphabet=this.alphabet.substr(s))}c.prototype.encode=function(){var t,e,n="",r=Array.prototype.slice.call(arguments);if(!r.length)return n;for(r[0]instanceof Array&&(r=r[0]),t=0,e=r.length;t!==e;t++)if("number"!==typeof r[t]||r[t]%1!==0||r[t]<0)return n;return this._encode(r)},c.prototype.decode=function(t){return t.length&&"string"===typeof t?this._decode(t,this.alphabet):[]},c.prototype.encodeHex=function(t){var e,n,r;if(t=t.toString(),!/^[0-9a-fA-F]+$/.test(t))return"";for(e=0,n=(r=t.match(/[\w\W]{1,12}/g)).length;e!==n;e++)r[e]=parseInt("1"+r[e],16);return this.encode.apply(this,r)},c.prototype.decodeHex=function(t){var e,n,r=[],o=this.decode(t);for(e=0,n=o.length;e!==n;e++)r+=o[e].toString(16).substr(1);return r},c.prototype._encode=function(t){var e,n,r,o,i,a,c,u,s,f,l,p=this.alphabet,h=t.length,d=0;for(r=0,o=t.length;r!==o;r++)d+=t[r]%(r+100);for(n=e=p[d%p.length],r=0,o=t.length;r!==o;r++)i=t[r],a=n+this.salt+p,p=this.consistentShuffle(p,a.substr(0,p.length)),e+=c=this.hash(i,p),r+1<h&&(u=(i%=c.charCodeAt(0)+r)%this.seps.length,e+=this.seps[u]);for(e.length<this.minHashLength&&(s=(d+e[0].charCodeAt(0))%this.guards.length,(e=this.guards[s]+e).length<this.minHashLength&&(s=(d+e[2].charCodeAt(0))%this.guards.length,e+=this.guards[s])),f=parseInt(p.length/2,10);e.length<this.minHashLength;)(l=(e=(p=this.consistentShuffle(p,p)).substr(f)+e+p.substr(0,f)).length-this.minHashLength)>0&&(e=e.substr(l/2,this.minHashLength));return e},c.prototype._decode=function(t,e){var n,r,o,i,a=[],c=0,u=new RegExp("["+this.guards+"]","g"),s=t.replace(u," "),f=s.split(" ");if(3!==f.length&&2!==f.length||(c=1),"undefined"!==typeof(s=f[c])[0]){for(n=s[0],s=s.substr(1),u=new RegExp("["+this.seps+"]","g"),c=0,r=(f=(s=s.replace(u," ")).split(" ")).length;c!==r;c++)o=f[c],i=n+this.salt+e,e=this.consistentShuffle(e,i.substr(0,e.length)),a.push(this.unhash(o,e));this._encode(a)!==t&&(a=[])}return a},c.prototype.consistentShuffle=function(t,e){var n,r,o,i,a,c;if(!e.length)return t;for(i=t.length-1,a=0,c=0;i>0;i--,a++)c+=n=e[a%=e.length].charCodeAt(0),o=t[r=(n+a+c)%i],t=(t=t.substr(0,r)+t[i]+t.substr(r+1)).substr(0,i)+o+t.substr(i+1);return t},c.prototype.hash=function(t,e){var n="",r=e.length;do{n=e[t%r]+n,t=parseInt(t/r,10)}while(t);return n},c.prototype.unhash=function(t,e){var n,r=0;for(n=0;n<t.length;n++)r+=e.indexOf(t[n])*Math.pow(e.length,t.length-n-1);return r};var u=n("dKMb");function s(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?s(Object(n),!0).forEach((function(e){Object(r.a)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var l=parseInt(1e10*Math.random(),10),p=Object(u.a)(),h=function(t){if(0!==t.indexOf(".")){var e=/[0-9A-Za-z]+/.exec(t);return null!==e&&e[0]===t&&parseInt(t,36)}var n=t.substr(1,2);return function(t){var e=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).salt;return new c(void 0===e?null:e).decode(t)[0]}(t.substr(3),{salt:n})},d=function(t){var e=parseInt(25*Math.random()+10,10).toString(36)+parseInt(25*Math.random()+10,10).toString(36);return".".concat(e).concat(function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.salt,r=void 0===n?null:n,o=e.length;return new c(r,void 0===o?32:o).encode(t)}(t,{salt:e,length:0}))},v=function(t){var e=decodeURIComponent(t).split("&").map((function(t){return t.split("=")})).reduce((function(t,e){var n=Object(o.a)(e,2),i=n[0],a=n[1];return f(f({},t),{},Object(r.a)({},i,a))}),{}),n=e.u,i=e.uuid;return{legacyIdentifier:h(n||""),identifier:i}},y=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.legacy,n=void 0!==e&&e,r={name:"bf_visit",days:1e4,domain:Object(a.a)()},o=i.a.get(r.name),c=v(o),u=c.legacyIdentifier,s=c.identifier,h=d(l);return n?u||(i.a.set(f(f({},r),{},{value:encodeURIComponent("u=".concat(h,"&uuid=").concat(s||p,"&v=2"))})),l):s||u?s||String(u):(i.a.set(f(f({},r),{},{value:encodeURIComponent("u=".concat(h,"&uuid=").concat(p,"&v=2"))})),p)}},nETP:function(t,e,n){"use strict";var r=n("MrsR"),o=Array.isArray;t.exports=function(t){return o(t)?t:r(t)}},nqu3:function(t,e,n){"use strict";var r=n("J2Kb"),o=n("PmSW");t.exports=function(t){return o(r(t))}},"oB+F":function(t,e,n){"use strict";t.exports=n("OfTf")()?globalThis:n("jmmV")},p5SE:function(t,e,n){"use strict";function r(t){return t+"|expiration"}var o=function(){try{return localStorage.setItem("ls_test",!0),localStorage.removeItem("ls_test"),!0}catch(t){return!1}}();e.a={set:function(t){o&&"undefined"!==typeof t&&(localStorage.setItem(t.key,t.value),t.expires&&localStorage.setItem(r(t.key),Date.now()+t.expires))},get:function(t){return o?function(t){var e=localStorage.getItem(r(t));return e&&e<=Date.now()}(t)?(this.remove(t),null):localStorage.getItem(t):null},sessionSet:function(t){o&&"undefined"!==typeof t&&sessionStorage.setItem(t.key,t.value)},sessionGet:function(t){return o?sessionStorage.getItem(t):null},remove:function(t){o&&(localStorage.removeItem(r(t)),localStorage.removeItem(t))},clear:function(){o&&localStorage.clear()}}},pVAg:function(t,e,n){"use strict";var r=n("7px9"),o={object:!0,function:!0,undefined:!0};t.exports=function(t){return!!r(t)&&hasOwnProperty.call(o,typeof t)}},pmLj:function(t,e,n){},r42V:function(t,e,n){"use strict";var r=n("HX/o"),o=n("pmLj"),i=Object.create,a=Object.defineProperties;o.refCounter=function(t,e,n){var c,u;c=i(null),u=n.async&&o.async||n.promise&&o.promise?"async":"",e.on("set"+u,(function(t,e){c[t]=e||1})),e.on("get"+u,(function(t){++c[t]})),e.on("delete"+u,(function(t){delete c[t]})),e.on("clear"+u,(function(){c={}})),a(e.memoized,{deleteRef:r((function(){var t=e.get(arguments);return null===t?null:c[t]?!--c[t]&&(e.delete(t),!0):null})),getRefCount:r((function(){var t=e.get(arguments);return null===t?0:c[t]?c[t]:0}))})}},rdyg:function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return i})),n.d(e,"d",(function(){return a}));var r={dev:{tracking_url:"https://nsq-api-public.dev.buzzfeed.io",debug:!0},test:{tracking_url:"https://nsq-api-public.dev.buzzfeed.io",debug:!0},stage:{tracking_url:"https://pixiedust-stage.buzzfeed.com",debug:!0},prod:{tracking_url:"https://pixiedust.buzzfeed.com",debug:!1},"app-west":{tracking_url:"https://pixiedust.buzzfeed.com",debug:!1}},o={HOMEPAGE:1,USER:2,SECTION:3,TAG:4,BADGE:5,TOPIC:6,SEARCH:7},i={AUTH:"auth",BUZZ:"buzz",FEED:"feed",USER:"user",VIDEO:"video"},a="CLIENT_EVENT_TRACKING"},t7Nw:function(t,e,n){"use strict";t.exports=n("23+d")()?Object.assign:n("xZY9")},"v/Kl":function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));var r=function(t){var e={},n=t&&t.headers?t.headers.cookie:null;return n&&n.split(";").forEach((function(t){var n=t.split("=");e[n.shift().trim()]=decodeURI(n.join("="))})),e}},vncP:function(t,e,n){"use strict";t.exports=function(t){return t=Number(t),isNaN(t)||0===t?t:t>0?1:-1}},wJaV:function(t,e,n){"use strict";n.d(e,"a",(function(){return l}));var r=n("fGyu"),o=n("ERkP"),i=n("lOqH"),a=n("kZhk"),c=n("O6my"),u=n("xwdf"),s=n("p5SE"),f=n("iCr9");function l(t){var e=t.layers,n=void 0===e?[]:e;Object(i.a)("tracking");var l=Object(c.a)();Object(o.useEffect)((function(){var t=u.a.get("hem"),e=u.a.get("bf2-b_info"),o=[];(Object(f.d)(window.location.search).email_hash||s.a.sessionGet("newsletterAddressable"))&&(s.a.sessionSet({key:"newsletterAddressable",value:"true"}),o.push("newsletter")),e&&o.push("auth"),a.f.apply(void 0,[l()].concat(Object(r.a)(n),[{is_addressable:!!t,addressable_source:o,addressable_partner:[]}]))}),[])}},wV7X:function(t,e,n){"use strict";var r=n("UBbi"),o={function:!0,object:!0};t.exports=function(t){return r(t)&&o[typeof t]||!1}},x4Na:function(t,e,n){"use strict";var r=n("FBSS"),o=Object.create;t.exports=function(){var t=0,e=[],n=o(null);return{get:function(t){var n,o=0,i=e,a=t.length;if(0===a)return i[a]||null;if(i=i[a]){for(;o<a-1;){if(-1===(n=r.call(i[0],t[o])))return null;i=i[1][n],++o}return-1===(n=r.call(i[0],t[o]))?null:i[1][n]||null}return null},set:function(o){var i,a=0,c=e,u=o.length;if(0===u)c[u]=++t;else{for(c[u]||(c[u]=[[],[]]),c=c[u];a<u-1;)-1===(i=r.call(c[0],o[a]))&&(i=c[0].push(o[a])-1,c[1].push([[],[]])),c=c[1][i],++a;-1===(i=r.call(c[0],o[a]))&&(i=c[0].push(o[a])-1),c[1][i]=++t}return n[t]=o,t},delete:function(t){var o,i=0,a=e,c=n[t],u=c.length,s=[];if(0===u)delete a[u];else if(a=a[u]){for(;i<u-1;){if(-1===(o=r.call(a[0],c[i])))return;s.push(a,o),a=a[1][o],++i}if(-1===(o=r.call(a[0],c[i])))return;for(t=a[1][o],a[0].splice(o,1),a[1].splice(o,1);!a[0].length&&s.length;)o=s.pop(),(a=s.pop())[0].splice(o,1),a[1].splice(o,1)}delete n[t]},clear:function(){e=[],n=o(null)}}}},xZY9:function(t,e,n){"use strict";var r=n("5u1z"),o=n("J2Kb"),i=Math.max;t.exports=function(t,e){var n,a,c,u=i(arguments.length,2);for(t=Object(o(t)),c=function(r){try{t[r]=e[r]}catch(o){n||(n=o)}},a=1;a<u;++a)r(e=arguments[a]).forEach(c);if(void 0!==n)throw n;return t}},xdv7:function(t,e,n){"use strict";t.exports=function(){var t=Number.isNaN;return"function"===typeof t&&(!t({})&&t(NaN)&&!t(34))}},y9gR:function(t,e){t.exports={AUTH_API:"/auth/user/profile",BUZZ_API:"/buzz/v3.1/buzzes",BUZZ_API_REBUZZES:"/buzz/v3.1/rebuzzes",BUZZ_API_BUZZ_COUNT:"/buzz/v3.1/buzz_count",BF_HEADER_UI:"/bf-header-ui",COMMENTS:"/comments-api/v1",INTERNET_POINTS:"/internet-points",USER_ID_UUID_API:"/user-id-uuid",USER_PROFILE_API:"/user-profile",TOP_SHOPPING:"/site-component/v1/en-us/top-shopping",TOP_USER_POSTS:"https://www.buzzfeed.com/site-component/v1/top-user-posts",CANONICAL_ROOT:"https://www.buzzfeed.com"}},zgmP:function(t,e,n){"use strict";var r=n("GUVy"),o=n("VNia"),i=n("nqu3"),a=n("VZI5"),c=n("A4TU"),u=n("Tjm3"),s=Object.create,f=o("then","then:finally","done","done:finally");n("pmLj").promise=function(t,e){var n=s(null),o=s(null),l=s(null);if(!0===t)t=null;else if(t=i(t),!f[t])throw new TypeError("'"+a(t)+"' is not valid promise mode");e.on("set",(function(r,i,a){var s=!1;if(!c(a))return o[r]=a,void e.emit("setasync",r,1);n[r]=1,l[r]=a;var f=function(t){var i=n[r];if(s)throw new Error("Memoizee error: Detected unordered then|done & finally resolution, which in turn makes proper detection of success/failure impossible (when in 'done:finally' mode)\nConsider to rely on 'then' or 'done' mode instead.");i&&(delete n[r],o[r]=t,e.emit("setasync",r,i))},p=function(){s=!0,n[r]&&(delete n[r],delete l[r],e.delete(r))},h=t;if(h||(h="then"),"then"===h){var d=function(){u(p)};"function"===typeof(a=a.then((function(t){u(f.bind(this,t))}),d)).finally&&a.finally(d)}else if("done"===h){if("function"!==typeof a.done)throw new Error("Memoizee error: Retrieved promise does not implement 'done' in 'done' mode");a.done(f,p)}else if("done:finally"===h){if("function"!==typeof a.done)throw new Error("Memoizee error: Retrieved promise does not implement 'done' in 'done:finally' mode");if("function"!==typeof a.finally)throw new Error("Memoizee error: Retrieved promise does not implement 'finally' in 'done:finally' mode");a.done(f),a.finally(p)}})),e.on("get",(function(t,r,o){var i;if(n[t])++n[t];else{i=l[t];var a=function(){e.emit("getasync",t,r,o)};c(i)?"function"===typeof i.done?i.done(a):i.then((function(){u(a)})):a()}})),e.on("delete",(function(t){if(delete l[t],n[t])delete n[t];else if(hasOwnProperty.call(o,t)){var r=o[t];delete o[t],e.emit("deleteasync",t,[r])}})),e.on("clear",(function(){var t=o;o=s(null),n=s(null),l=s(null),e.emit("clearasync",r(t,(function(t){return[t]})))}))}},zlHb:function(t,e,n){"use strict";t.exports=function(t){return t!==t}}}]);
//# sourceMappingURL=696e90556380e81282edf3e405e5a1af64b60a2b.714143bd011899ab74a7.js.map