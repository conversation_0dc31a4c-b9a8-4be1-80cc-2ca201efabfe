(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[727],{64731:function(e,t,n){"use strict";n.d(t,{S:function(){return c}});var r=n(52322),i=n(2784),a=n(20272),o=n.n(a),s=n(22887),c=function(e){var t=e.showAvatar,n=void 0===t||t,a=e.author,c=e.className,u=e.trending,l=void 0!==u&&u,A=(0,s.useTranslation)("common").t,f=(0,i.useState)(null===a||void 0===a?void 0:a.avatar),p=f[0],d=f[1];return(0,r.jsxs)("div",{className:c,children:[n?(0,r.jsx)("img",{className:l?o().trendingAvatar:o().avatar,src:p,alt:"",onError:function(){return d("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' id='default-avatar'%3E%3Cpath d='M256.271 12C121.181 12 12 121.182 12 256.271c0 135.09 109.182 244.271 244.271 244.271 135.09 0 244.271-109.182 244.271-244.271C500.542 121.181 391.36 12 256.271 12zM424.67 172.997c-12.954 0-27.758-3.701-40.712-14.804-16.655-12.954-22.206-31.459-18.505-55.516 24.057 18.505 44.413 42.562 59.217 70.321zM256.271 67.516c25.908 0 49.965 5.552 74.022 14.804-18.505 37.011-53.666 62.918-96.228 75.872-44.413 12.954-90.677 5.552-125.837-16.655 33.31-44.413 86.975-74.022 148.043-74.022zm0 377.51c-103.63 0-188.755-85.125-188.755-188.755 0-29.609 7.402-57.367 18.505-81.424 29.609 18.505 64.769 27.758 99.929 27.758 18.505 0 37.011-1.851 55.516-7.402 35.16-9.253 64.769-27.758 86.975-51.815 5.552 16.655 14.804 31.459 29.609 44.413 18.505 14.804 42.562 22.206 64.769 22.206 3.701 0 9.253 0 12.954-1.851 3.701 14.804 5.552 31.459 5.552 48.114 3.701 103.63-81.424 188.755-185.054 188.755zm0-59.217c35.16 0 62.918-27.758 62.918-62.918H191.502c1.851 33.31 29.609 62.918 64.769 62.918zM147.09 230.364h-33.31v31.459c0 18.505 14.804 31.459 31.459 31.459s31.459-14.804 31.459-31.459-12.954-31.459-29.609-31.459zm186.904 31.459c0 18.505 14.804 31.459 31.459 31.459s31.459-14.804 31.459-31.459v-31.459h-31.459c-16.655 0-31.459 12.954-31.459 31.459z' /%3E%3C/svg%3E")}}):(0,r.jsx)("p",{className:o().packageBy,children:A("by")}),l&&(0,r.jsx)("p",{className:o().by,children:A("by")}),(0,r.jsx)("p",{className:o().author,children:null===a||void 0===a?void 0:a.name})]})}},13993:function(e,t,n){"use strict";var r=n(52322),i=n(2784),a=n(17616),o=n.n(a),s=n(99376),c=n(32510);function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var l=function(e){var t=e.id,n=e.pos,r=e.tracking;return(0,i.useMemo)((function(){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){u(e,t,n[t])}))}return e}({item_type:"splash",item_name:t,target_content_type:"buzz",target_content_id:t,position_in_unit:n,unit_name:"feed_head",unit_type:"feed"},r)}),[t,n,r])};t.Z=function(e){var t=e.item,n=e.pos,i=t.id,a=t.tracking,u=t.images,A=l({id:i,pos:n,tracking:a}),f=(0,s.Z)(A),p=f.ref,d=f.trackClick;return(0,r.jsxs)("div",{className:o().container,ref:p,children:[(0,r.jsx)("a",{className:o().thumbnailContainer,href:t.url,onClick:d,children:(0,r.jsx)(c.Z,{images:u,size:"dblwide",resize:"700:250"})}),(0,r.jsxs)("div",{className:o().textContainer,children:[(0,r.jsx)("h2",{children:(0,r.jsx)("a",{className:o().title,href:t.url,onClick:d,children:null===t||void 0===t?void 0:t.title})}),(0,r.jsx)("p",{className:o().subtitle,children:null===t||void 0===t?void 0:t.subtitle})]})]})}},33176:function(e,t,n){"use strict";n.d(t,{Z:function(){return d}});var r=n(52322),i=(n(2784),n(13980)),a=n.n(i),o=function(){return(0,r.jsxs)("svg",{viewBox:"0 0 327 314",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M229.856 96.8102L229.855 289.251L37.4077 96.8097L229.856 96.8102Z",fill:"#FFEE00"}),(0,r.jsx)("path",{d:"M126.186 187.763L126.185 313.853L0.0996993 187.76L126.186 187.763Z",fill:"#FFEE00"})]})},s=function(){return(0,r.jsx)("svg",{width:"117",height:"117",viewBox:"0 0 117 117",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{d:"M117 78.1059V0H38.8941L77.7882 38.8941H0L78.1139 117V39.2118L117 78.1059Z",fill:"#FFEE00"})})},c=n(13374),u=n.n(c),l=function(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:u().trianglesDesktop,children:(0,r.jsx)(o,{})}),(0,r.jsx)("div",{className:u().trianglesMobile,children:(0,r.jsx)(s,{})})]})},A=n(93625),f=n.n(A),p=function(e){var t=e.title;return(0,r.jsxs)("div",{className:f().headerWrapper,children:[(0,r.jsx)(l,{}),(0,r.jsx)("h1",{className:f().title,children:t})]})},d=p;p.propTypes={title:a().string.isRequired}},32510:function(e,t,n){"use strict";var r=n(52322),i=n(2784),a=n(63965),o=n.n(a),s=n(67159),c=n.n(s),u={small:{width:90,height:60},standard:{width:125,height:83},big:{width:355,height:236},wide:{width:355,height:125},dblbig:{width:625,height:415},dblwide:{width:625,height:220}};t.Z=function(e){var t=e.className,n=void 0===t?"":t,a=e.images,s=e.size,l=e.resize,A=void 0===l?"266:176":l,f=(0,i.useState)(!1),p=f[0],d=f[1],m=(0,i.useRef)();(0,i.useEffect)((function(){m.current&&0===m.current.naturalWidth&&m.current.complete&&d(!0)}),[m]);if(!a||!a[s]||p)return(0,r.jsx)("img",{alt:"placeholder",src:c(),className:o().placeholder});var _=a[s].altText,h=new URL(a[s].url);h.searchParams.set("resize",A);var g=h.toString(),v=u[s],y=v.width,b=v.height;return(0,r.jsx)("img",{className:"".concat(o().image," ").concat(n),ref:m,src:g,alt:_,width:y,height:b,onError:function(){return d(!0)}})}},45626:function(e,t,n){"use strict";n.d(t,{Z:function(){return Z}});var r=n(94776),i=n.n(r),a=n(52322),o=n(2784),s=n(3176),c=n(59656),u=n(36412),l=n.n(u),A=n(45807),f=n.n(A),p=n(62057),d=/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/i,m=function(e){return d.test(e)},_=n(26528),h=n.n(_);function g(e,t,n,r,i,a,o){try{var s=e[a](o),c=s.value}catch(u){return void n(u)}s.done?t(c):Promise.resolve(c).then(r,i)}function v(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){g(a,r,i,o,s,"next",e)}function s(e){g(a,r,i,o,s,"throw",e)}o(void 0)}))}}function y(e,t){return b.apply(this,arguments)}function b(){return b=v(i().mark((function e(t,n){var r,a,o,s=arguments;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=s.length>2&&void 0!==s[2]?s[2]:{},a=s.length>3&&void 0!==s[3]&&s[3],e.prev=1,e.next=4,h()("".concat(p._H).concat(t),{method:"POST",body:JSON.stringify(n),headers:r});case 4:if((o=e.sent).ok){e.next=7;break}return e.abrupt("return",{error:o});case 7:if(!a){e.next=11;break}e.t0={ok:"ok"},e.next=14;break;case 11:return e.next=13,o.json();case 13:e.t0=e.sent;case 14:return e.t1=e.t0,e.abrupt("return",{response:e.t1});case 18:return e.prev=18,e.t2=e.catch(1),e.abrupt("return",{error:e.t2});case 21:case"end":return e.stop()}}),e,null,[[1,18]])}))),b.apply(this,arguments)}var x=function(){return(0,a.jsxs)("p",{className:l().reCaptchaText,children:["This site is protected by reCAPTCHA and the Google",(0,a.jsx)("a",{href:"https://policies.google.com/privacy",children:" Privacy Policy"})," and",(0,a.jsx)("a",{href:"https://policies.google.com/terms",children:" Terms of Service"})," apply."]})},j=n(99376),w=n(64203),z=n(35173),N=n(40497),M=n(45767),k=n.n(M),O=n(22887),C=n(34408),D=n(62528);function T(e,t,n,r,i,a,o){try{var s=e[a](o),c=s.value}catch(u){return void n(u)}s.done?t(c):Promise.resolve(c).then(r,i)}function P(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){T(a,r,i,o,s,"next",e)}function s(e){T(a,r,i,o,s,"throw",e)}o(void 0)}))}}function I(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function S(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){I(e,t,n[t])}))}return e}var E={item_type:"submission",item_name:"email",unit_name:"feed_head",unit_type:"feed"},L=function(){var e=P(i().mark((function e(t){var n,r,a,o,s;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.recaptcha,r=t.email,a=t.isMobile,n&&r){e.next=3;break}throw Error("Wrong params!");case 3:return e.next=5,n.executeAsync();case 5:return o=e.sent,n.reset(),e.next=9,y("/newsletters/api/subhub/v1/users",{brand:"buzzfeed",subscriptions:["buzzfeed_email_quizzes"],"g-recaptcha-response":o,source:a?"buzzfeed-mobileweb-hub":"buzzfeed_desktop_hub",email:r},{"Content-Type":"application/json"},!0);case 9:if(!(s=e.sent.error)){e.next=12;break}throw Error(s);case 12:return e.abrupt("return",!0);case 13:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Z=function(e){var t,n=e.pos,r=void 0===n?0:n,u=(0,o.useState)(!1),A=u[0],d=u[1],_=(0,o.useState)(""),h=_[0],g=_[1],v=(0,N.h)(),y=(null===(t=null===f()||void 0===f()?void 0:f().find((function(e){return e.url===v})))||void 0===t?void 0:t.name)||"",b=function(e){return(0,o.useMemo)((function(){return S({},E,{target_content_type:"submission",target_content_id:"quizzes-newsletter",position_in_unit:e})}),[e])}(r),M=function(e){return(0,o.useMemo)((function(){return S({},E,{action_type:"signup",action_value:"quizzes-newsletter",position_in_unit:e})}),[e])}(r),T=(0,j.Z)(b).ref,I=(0,w.Z)(M),Z=(0,o.useState)(!1),q=Z[0],U=Z[1],Q=(0,o.useState)(!1),W=Q[0],H=Q[1],Y=(0,o.useState)(""),R=Y[0],F=Y[1],B=(0,o.useRef)(),G=(0,z.Z)(),J=G.isXS,V=G.isSM,X=J||V,K="Latest"===y,$=(0,O.useTranslation)("common").t;function ee(){return(ee=P(i().mark((function e(t){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.preventDefault(),!q){e.next=3;break}return e.abrupt("return");case 3:if(I(),m(h)){e.next=7;break}return F($("newsletter_email_error")),e.abrupt("return");case 7:return e.prev=7,U(!0),F(""),e.next=12,L({recaptcha:B.current,email:h,isMobile:X});case 12:H(!0),d(!0),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(7),F($("newsletter_server_error"));case 19:U(!1);case 20:case"end":return e.stop()}}),e,null,[[7,16]])})))).apply(this,arguments)}return(0,a.jsxs)("div",{className:K?l().latestContainer:l().categoryContainer,ref:T,children:[W?(0,a.jsxs)("div",{className:l().successContainer,children:[(0,a.jsx)("div",{className:l().successTitle,children:$("newsletter_success_title")}),(0,a.jsx)("p",{className:l().successText,children:$("newsletter_success_message")}),(0,a.jsx)("img",{className:l().plane,src:k(),alt:"paper plane"})]}):(0,a.jsxs)("div",{className:l().signupWrapper,children:[(0,a.jsx)("div",{className:K?l().yellowCircle:l().categoryYellowCircle}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:l().titleContainer,children:[(0,a.jsx)("div",{className:l().title,children:$("newsletter_title")}),(0,a.jsx)("span",{className:l().emoji,role:"img","aria-label":"Mail",children:"\ud83d\udce5"})]}),(0,a.jsx)("p",{className:l().newsletterText,children:$("newsletter_description")})]}),(0,a.jsxs)("div",{className:l().signupFormWrapper,children:[(0,a.jsxs)("form",{className:l().form,children:[(0,a.jsx)(s.Z,{ref:B,size:"invisible",sitekey:p.A3}),(0,a.jsx)("input",{id:"email",value:h,onChange:function(e){return g(e.currentTarget.value)},className:l().input,type:"email",name:"email",size:15,required:!0,placeholder:$("newsletter_placeholder")}),(0,a.jsx)("button",{type:"submit",className:l().button,onClick:function(e){return ee.apply(this,arguments)},children:"Sign Up"})]}),R&&(0,a.jsx)("p",{className:l().validEmailError,children:R}),(0,a.jsx)(x,{})]})]}),A&&!D.Z.get("bf2-b_info")&&(0,a.jsx)(c.Z,{email:h,topic:"Quizzes Newsletter",onClose:function(){return d(!1)},track:{commonTrackingData:E,internalLink:function(e){return(0,C.Fr)({edition:"en-us"},e)},contentAction:function(e){return(0,C.l6)({edition:"en-us"},e)},impression:function(e){return(0,C.Oz)({edition:"en-us"},e)}}})]})}},35193:function(e,t,n){"use strict";n.d(t,{Z:function(){return w}});var r=n(52322),i=n(2784),a=n(76749),o=n.n(a),s=n(41440),c=n.n(s),u=function(e){var t=e.title;return(0,r.jsx)("div",{className:c().title,children:t})},l=n(34133),A=n(63808),f=n(5632),p=n(1621),d=n.n(p),m=n(99376),_=function(e){var t=e.name,n=e.url,a=e.active,o=e.feed,s=function(e){var t=e.pos,n=e.name,r=e.feed;return(0,i.useMemo)((function(){return{subunit_type:"component",subunit_name:"feed_filters",item_type:"button",item_name:n,position_in_unit:1,position_in_subunit:t,target_content_type:"feed",target_content_id:r}}),[t,n,r])}({pos:e.pos,name:t,feed:o}),c=(0,m.Z)(s,!0).ref;return a?(0,r.jsx)("div",{className:d().active,children:(0,r.jsx)("p",{className:d().name,children:t})}):(0,r.jsx)("a",{className:d().inactive,href:n,ref:c,children:(0,r.jsx)("span",{className:d().name,children:t})})},h=n(40497),g=n(45807),v=n.n(g),y=n(97051),b=n.n(y),x=n(62057),j=(0,A.J)((function(){var e,t=(0,f.useRouter)(),n=(0,h.h)(),i=(null===t||void 0===t||null===(e=t.asPath)||void 0===e?void 0:e.lastIndexOf("?"))||-1,a=-1!==i?t.asPath.slice(i):"";return(0,r.jsx)("div",{className:b().categoryWrapper,children:(0,r.jsx)("div",{className:b().categoryList,children:v().map((function(e,t){var i=e.name,o=e.url,s=e.feed;return(0,r.jsx)(_,{name:i,url:"".concat(["arcade","make-yours"].includes(s)?"":x.FH).concat(o).concat(a),active:n===o,feed:s,pos:t},i)}))})})}),l.aE);var w=(0,A.J)((function(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:o().titleAndSearch,children:(0,r.jsx)(u,{title:"Quizzes"})}),(0,r.jsx)("div",{className:o().categories,children:(0,r.jsx)(j,{})})]})}),l.aE)},13269:function(e,t,n){"use strict";n.d(t,{ZP:function(){return He}});var r=n(52322),i=n(2784),a=n(45807),o=n.n(a),s=n(40497),c=n(63808),u=n(35173),l=n(34133),A=n(70002),f=n(84910),p=n.n(f),d=function(e){var t=e.color,n=e.children,i=e.isCarouselWrapper,a=e.className,o=void 0===a?"":a;return i?(0,r.jsx)("div",{className:p()[t],children:(0,r.jsx)("div",{className:"".concat(p().mobileWrapper," ").concat(o),children:n})}):(0,r.jsx)("div",{className:p()[t],children:(0,r.jsx)("div",{className:"".concat(p().wrapper," ").concat(o),children:n})})},m=n(47864),_=n.n(m),h=n(99376),g=n(32510),v=n(64731);function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var b=function(e){var t=e.id,n=e.tracking,r=e.packageName,a=e.pos,o=e.index,s=e.type;return(0,i.useMemo)((function(){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){y(e,t,n[t])}))}return e}({subunit_type:"package",subunit_name:r,position_in_unit:a,position_in_subunit:o,item_type:s,item_name:t,target_content_type:"buzz",target_content_id:t},n)}),[t,n,r,a,o,s])},x=function(e){var t=e.item,n=e.packageName,i=e.pos,a=e.index,o=e.type,s=e.isCarouselCard,c=b({id:t.id,tracking:t.tracking,packageName:n,pos:i,index:a,type:o}),u=(0,h.Z)(c),l=u.ref,A=u.trackClick;return(0,r.jsxs)("div",{className:_().container,ref:l,children:[(0,r.jsx)("div",{className:_().thumbnailContainer,children:(0,r.jsx)("a",{className:_().thumbnailLink,href:t.url,onClick:A,children:(0,r.jsx)(g.Z,{images:t.images,size:"big"})})}),(0,r.jsxs)("div",{className:s?_().carouselTextContainer:_().textContainer,children:[(0,r.jsx)("a",{className:_().title,href:t.url,onClick:A,children:null===t||void 0===t?void 0:t.title}),(0,r.jsx)(v.S,{showAvatar:!1,author:null===t||void 0===t?void 0:t.author,className:_().author})]})]})},j=n(50293),w=n.n(j),z=n(22887),N=n(31872),M=n.n(N),k=n(28798),O=n.n(k),C=n(24663),D=n.n(C),T=function(e){var t=e.isQuizParty,n=e.heading,i=e.id,a=e.isCarousel,o="quizzes-hall-of-fame"===i,s=(0,z.useTranslation)("common").t;return t?(0,r.jsx)("img",{className:a?O().carouselQuizPartyHeading:O().quizPartyHeading,src:D(),alt:"quiz party logo"}):(0,r.jsx)("div",{className:a?O().carouselHeading:O().heading,children:o?s(n):"\ud83d\udca5 ".concat(n," \ud83d\udca5")})},P=n(47121),I=n.n(P),S=n(41505),E=n.n(S),L=function(){return(0,r.jsxs)("div",{className:I().quizPartyDescContainer,children:[(0,r.jsx)("p",{className:I().quizPartyPackageDesc,children:"Multiplayer quiz-taking with up to 5 friends."}),(0,r.jsx)("img",{className:I().confetti,src:E(),alt:"quiz party confetti"})]})},Z=function(e){var t=e.packageItem,n=e.pos,i=e.isQuizParty,a="quizzes-hall-of-fame"===t.id;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(T,{heading:t.name,id:t.id,isCarousel:!0,isQuizParty:i}),!a&&i&&(0,r.jsx)(L,{}),(0,r.jsx)("div",{className:M().carouselWrapper,children:t.items.map((function(e,i){return(0,r.jsx)(d,{isCarouselWrapper:!0,isQuizParty:U(t)===q,color:"yellow",children:(0,r.jsx)(x,{item:e,isCarouselCard:!0,packageName:U(t),pos:n,index:i,type:"card"})},e.id)}))})]})},q="throw_a_quiz_party",U=function(e){var t=e.id,n=e.cta;return n&&"/quizparty"===n.url?q:t},Q=function(e){var t=e.item,n=e.pos,a=e.title,o=(0,z.useTranslation)("common").t,s=t.cta||{},c=s.text,l=s.url,f=function(e){var t=e.pos,n=e.text;return(0,i.useMemo)((function(){return{subunit_type:"package",subunit_name:q,position_in_unit:t,position_in_subunit:5,item_type:"text",item_name:n,target_content_type:"feed",target_content_id:"quizparty"}}),[t,n])}({pos:n,text:c}),p=(0,h.Z)(f,!0).ref,m="quizzes-hall-of-fame"===t.id,_=U(t)===q,g=(0,u.Z)(),v=g.isSM,y=g.isXS;return v||y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Z,{packageItem:t,pos:n,isQuizParty:U(t)===q,heading:t.name}),c&&(0,r.jsxs)("a",{className:w().carouselCta,href:l,ref:p,children:[U(t)===q?o("try_quiz_party"):c,(0,r.jsx)(A.QU,{className:w().ctaIcon})]})]}):(0,r.jsxs)(d,{color:"yellow",children:[(0,r.jsx)(T,{heading:a||t.name,id:t.id,isQuizParty:_}),!m&&_&&(0,r.jsx)(L,{}),(0,r.jsx)("div",{className:w().innerWrapper,children:t.items.slice(0,3).map((function(e,i){return(0,r.jsx)("div",{className:w().item,children:(0,r.jsx)(x,{item:e,packageName:U(t),pos:n,index:i,type:"card"})},e.id)}))}),c&&(0,r.jsxs)("a",{className:w().cta,href:l,ref:p,children:[U(t)===q?o("try_quiz_party"):c,(0,r.jsx)(A.QU,{className:w().ctaIcon})]})]})},W=n(5415),H=n(94776),Y=n.n(H),R=n(62057),F=n(52580),B=n(39188);function G(e,t,n,r,i,a,o){try{var s=e[a](o),c=s.value}catch(u){return void n(u)}s.done?t(c):Promise.resolve(c).then(r,i)}function J(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){G(a,r,i,o,s,"next",e)}function s(e){G(a,r,i,o,s,"throw",e)}o(void 0)}))}}function V(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function X(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){V(e,t,n[t])}))}return e}function K(){return $.apply(this,arguments)}function $(){return($=J(Y().mark((function e(){var t,n,r,i,a,o;return Y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Promise.race([(0,F.n5)({env:R.ov}),new Promise((function(e){setTimeout((function(){return e(!1)}),1500)}))]);case 3:return t=e.sent,n=X({page_size:5,filter:6062770,badge:"quiz",format:"weaver"},t&&{client_id:t}),r=new URLSearchParams(n),i=r.toString(),e.next=9,fetch("".concat(R.ro,"/web/hall-of-fame/badges?").concat(i));case 9:return a=e.sent,e.next=12,a.json();case 12:return o=e.sent.results,e.abrupt("return",(0,B.parseHallOfFameItems)(o));case 16:return e.prev=16,e.t0=e.catch(0),e.abrupt("return",null);case 19:case"end":return e.stop()}}),e,null,[[0,16]])})))).apply(this,arguments)}function ee(e,t,n,r,i,a,o){try{var s=e[a](o),c=s.value}catch(u){return void n(u)}s.done?t(c):Promise.resolve(c).then(r,i)}var te=function(e){var t=e.pos,n=(0,z.useTranslation)("common").t,a=(0,i.useState)(null),o=a[0],s=a[1];return(0,i.useEffect)((function(){var e=function(){var e,t=(e=Y().mark((function e(){var t;return Y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,K();case 2:(t=e.sent)&&s(t);case 4:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){ee(a,r,i,o,s,"next",e)}function s(e){ee(a,r,i,o,s,"throw",e)}o(void 0)}))});return function(){return t.apply(this,arguments)}}();e()}),[]),o?(0,r.jsx)(Q,{item:o,pos:t,title:n(o.name)}):null},ne=n(74017),re=n.n(ne);function ie(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ae=function(e){var t=e.id,n=e.pos,r=e.tracking;return(0,i.useMemo)((function(){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ie(e,t,n[t])}))}return e}({item_type:"card",item_name:t,target_content_type:"buzz",target_content_id:t,position_in_unit:n},r)}),[t,n,r])},oe=function(e){var t=e.item,n=e.pos,i=t.id,a=t.tracking,o=t.images,s=ae({id:i,pos:n,tracking:a}),c=(0,h.Z)(s),u=c.ref,l=c.trackClick;return(0,r.jsxs)("div",{className:re().container,ref:u,children:[(0,r.jsx)("div",{className:re().thumbnailContainer,children:(0,r.jsx)("a",{className:re().thumbnailLink,href:t.url,onClick:l,children:(0,r.jsx)(g.Z,{images:o,size:"big"})})}),(0,r.jsxs)("div",{className:re().textContainer,children:[(0,r.jsx)("h2",{children:(0,r.jsx)("a",{className:re().title,href:t.url,onClick:l,children:null===t||void 0===t?void 0:t.title})}),(0,r.jsx)(v.S,{author:null===t||void 0===t?void 0:t.author,className:re().authorContainerHide})]})]})},se=n(22938),ce=n.n(se),ue=n(31387);function le(e,t,n,r,i,a,o){try{var s=e[a](o),c=s.value}catch(u){return void n(u)}s.done?t(c):Promise.resolve(c).then(r,i)}function Ae(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){le(a,r,i,o,s,"next",e)}function s(e){le(a,r,i,o,s,"throw",e)}o(void 0)}))}}function fe(e){return pe.apply(this,arguments)}function pe(){return(pe=Ae(Y().mark((function e(t){var n,r;return Y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n="dev"===R.ov?"":"/quizzes",e.prev=1,e.next=4,fetch("".concat(n,"/api/weaver?url=").concat(t),{method:"GET",headers:{"Content-Type":"application/json"}});case 4:if((r=e.sent).ok){e.next=7;break}throw Error(r.statusText);case 7:return e.abrupt("return",r.json());case 10:return e.prev=10,e.t0=e.catch(1),console.error(e.t0),e.abrupt("return",{error:"Error"});case 14:case"end":return e.stop()}}),e,null,[[1,10]])})))).apply(this,arguments)}var de=n(64203);function me(e,t,n,r,i,a,o){try{var s=e[a](o),c=s.value}catch(u){return void n(u)}s.done?t(c):Promise.resolve(c).then(r,i)}var _e=function(e){var t=e.nextUrl,n=e.pos,a=e.onFeedLoaded,o=(0,i.useState)(!1),s=o[0],c=o[1],u=(0,i.useState)(!1),l=u[0],A=u[1],f=(0,de.Z)(function(e){var t=e.pos;return{item_type:"button",item_name:e.text,position_in_unit:t,action_type:"show",action_value:"show_more_quizzes"}}({pos:n,text:"Load More Quizzes"})),p=(0,z.useTranslation)("common").t,d=function(){var e,n=(e=Y().mark((function e(){var n;return Y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return f(),A(!1),c(!0),e.next=5,fe(t);case 5:(null===(n=e.sent)||void 0===n?void 0:n.items)&&!n.error?a({newQuizzes:n.items,next:n.next}):A(!0),c(!1);case 8:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){me(a,r,i,o,s,"next",e)}function s(e){me(a,r,i,o,s,"throw",e)}o(void 0)}))});return function(){return n.apply(this,arguments)}}();return(0,r.jsxs)("div",{className:ce().container,children:[(0,r.jsx)(ue.ZP,{className:ce().loadMoreButton,onClick:d,disabled:s,children:p("load_more_quizzes")}),l&&(0,r.jsx)("p",{className:ce().errorMsg,children:p("more_quizzes_loading_error")})]})},he=n(33176),ge=n(45626),ve=n(13993),ye=n(96396),be=n.n(ye),xe=n(94580),je=n(67129),we=n(46872),ze=n(16557),Ne=n(49593);function Me(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ke(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Oe(e,t,n){return(Oe="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Ce(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(n):i.value}})(e,t,n||e)}function Ce(e){return(Ce=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function De(e,t){return!t||"object"!==Pe(t)&&"function"!==typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function Te(e,t){return(Te=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var Pe=function(e){return e&&"undefined"!==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};function Ie(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Ce(e);if(t){var i=Ce(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return De(this,n)}}var Se=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Te(e,t)}(a,e);var t,n,r,i=Ie(a);function a(e){var t,n=e.programmaticOnly,r=void 0!==n&&n;return Me(this,a),(t=i.call.apply(i,[this].concat(Array.prototype.slice.call(arguments))))._state.adCounter=0,t.programmaticOnly=r,t}return t=a,n=[{key:"getNextAd",value:function(){var e=Oe(Ce(a.prototype),"getNextAd",this).apply(this,arguments);return e&&!a.isDone(e)&&(e.key="".concat(e.slot.adPos,"-").concat(this._state.adCounter),this._state.adCounter++),this.programmaticOnly&&(e.slot.size=Ne.Z.exclude(e.slot.size,ze.J7.FLUID,ze.J7.NATIVE)),e}},{key:"isDone",value:function(e){return a.isDone(e)}},{key:"reset",value:function(){Oe(Ce(a.prototype),"reset",this).call(this),this._state.adCounter=0}}],n&&ke(t.prototype,n),r&&ke(t,r),a}(we.ZP),Ee=n(50433),Le=function(e){return e.adPos&&"post"===e.adType&&/^story/.test(e.adPos)&&!e.adPos.includes("-")};function Ze(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function qe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,s=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(c){s=!0,i=c}finally{try{o||null==n.return||n.return()}finally{if(s)throw i}}return a}}(e,t)||Qe(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ue(e){return function(e){if(Array.isArray(e))return Ze(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Qe(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Qe(e,t){if(e){if("string"===typeof e)return Ze(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ze(e,t):void 0}}var We={12:{item:{type:"mobile-newsletter",id:"mobile-newsletter"}},20:{item:{type:"hall-of-fame",id:"hall-of-fame"}}},He=(0,c.J)((function(e){var t,n=e.items,a=e.nextUrl,c=(e.dailyTrivia,(0,s.h)()),l=(0,u.Z)(),A=l.isXS,f=l.isSM,p=l.isMD,d=(null===(t=null===o()||void 0===o()?void 0:o().find((function(e){return e.url===c})))||void 0===t?void 0:t.name)||"",m=(0,i.useState)(n),_=m[0],h=m[1],g=(0,i.useState)(a),v=g[0],y=g[1],b=function(){var e=(0,u.Z)(),t=e.isXS,n=e.isSM,r=t||n,a=(0,i.useContext)(xe.$),o=(0,i.useContext)(Ee.z1).adsDisabled,s=(0,i.useState)(null),c=s[0],l=s[1];return(0,i.useEffect)((function(){if("loaded"!==a.status||o)return function(){};var e,t=new Se({config:{units:(e=je.Z,Object.values(e).filter(Le)||[]),density:6}});return t.init().then((function(){l(t)}),(function(){})),function(){l(null),t.destroy()}}),[a,r]),c}(),x=(0,i.useState)([]),j=x[0],w=x[1];(0,i.useEffect)((function(){var e=Ue(_);if(b){var t=0;do{var n=b.getAdForPlacement(t),r=e[t],i=r&&"ad"===r.type;if(b.isDone(n))break;n&&(n.type="ad",e.splice(t,i?1:0,n),t++),t++}while(t<e.length);b.reset()}w(function(e){var t=Ue(e);return Object.entries(We).forEach((function(e){var n=qe(e,2),r=n[0],i=n[1].item;(null===t||void 0===t?void 0:t.length)&&t.length>r&&t.splice(r,0,i)})),t}(e))}),[_,b]);var N="Latest"===d,M=(0,z.useTranslation)("common").t;return(0,r.jsxs)("div",{className:be().feed,children:[(0,r.jsx)(he.Z,{title:N?M("latest_quizzes"):"".concat(d," ").concat(M("quizzes"))}),(null===j||void 0===j?void 0:j.length)?j.map((function(e){switch(e.type){case"featured":return N?null:(0,r.jsx)(ve.Z,{item:e},e.id);case"item":return(0,r.jsx)(oe,{item:e},e.id);case"package":return(0,r.jsx)(Q,{item:e},e.id);case"mobile-newsletter":return A||f||p?(0,r.jsx)(ge.Z,{},e.id):null;case"hall-of-fame":return"Latest"===d?(0,r.jsx)(te,{},e.id):null;case"ad":return(0,r.jsx)(W.Z,{config:e.slot,className:"Ad--feed"},e.key);default:return null}})).filter((function(e){return null!==e})).map((function(e,t){return(0,i.cloneElement)(e,{pos:t})})):null,v&&(0,r.jsx)(_e,{nextUrl:v,onFeedLoaded:function(e){var t=e.newQuizzes,n=e.next;y(n),h(_.concat(t))},pos:j.length})]})}),l.W$)},1272:function(e,t,n){"use strict";var r=n(52322),i=n(97729),a=n.n(i),o=n(2784),s=n(90113),c=n(50433);t.Z=function(e){var t=e.html,n=void 0===t?"":t,i=e.js,u=void 0===i?"":i,l=e.css,A=(0,o.useContext)(c.oF);return(0,r.jsxs)(o.Fragment,{children:[(0,r.jsx)(a(),{children:(0,r.jsx)("script",{dangerouslySetInnerHTML:{__html:"\n  window.BZFD = {\n    Config: {\n      bfwInfoCookie: 'bf2-b_info'\n    }\n  };\n"}},"window-globals")}),(0,r.jsx)(s.Z,{html:n,js:u,css:l,stickyRegistry:A})]})}},21451:function(e,t,n){"use strict";var r=n(52322),i=(n(2784),n(34133)),a=n(5415),o=n(63808),s=n(1767),c=n.n(s);t.Z=(0,o.J)((function(e){var t=e.children;return(0,r.jsxs)("div",{className:c().sidebar,children:[t,(0,r.jsx)(a.Z,{type:"bigstory",className:"Ad--sidebar"},"sidebar-ad-unit")]})}),i.jy)},34133:function(e,t,n){"use strict";n.d(t,{GG:function(){return i},W$:function(){return r},aE:function(){return a},jy:function(){return o}});var r={unit_type:"feed",unit_name:"main"},i={unit_type:"feed",unit_name:"feed_head"},a={unit_type:"nav",unit_name:"quiz_header"},o={unit_type:"sidebar",unit_name:"right"}},64203:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});var r=n(2784),i=n(50433),a=n(34408);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e){var t=(0,r.useContext)(i.z1),n=(0,r.useContext)(i.ws);return(0,r.useCallback)((function(){return(0,a.l6)(t,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){o(e,t,n[t])}))}return e}({},n,e))}),[e,n,t])}},99376:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(2784),i=n(50433),a=n(34408);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){o(e,t,n[t])}))}return e}function c(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=(0,r.useRef)(null),o=(0,r.useContext)(i.z1),c=(0,r.useContext)(i.ws),u=(0,r.useCallback)((function(){return(0,a.Fr)(o,s({},c,e))}),[e,c,o]);return(0,r.useEffect)((function(){var r=s({},c,e),i=n.current;if(!i)return function(){};var u=(0,a.aF)(i,o,r),l=t?(0,a.y1)(i,o,r):function(){};return function(){u(),l()}}),[e,t,c,o]),{ref:n,trackClick:u}}},35173:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(30466),i=n(2784);function a(){var e=(0,r.Z)().width,t=e<640,n=e>=640&&e<832,a=e>=832&&e<1024,o=e>=1024;return(0,i.useMemo)((function(){return{isXS:t,isSM:n,isMD:a,isLG:o}}),[t,n,a,o])}},56392:function(e,t,n){"use strict";n.d(t,{T:function(){return a}});var r=n(2784),i=n(50433);function a(){var e=(0,r.useContext)(i.z1),t=e.adsDisabled,n=e.membershipAvailable,a=function(e){var r;"topic-nav-loaded"===(null===e||void 0===e||null===(r=e.data)||void 0===r?void 0:r.type)&&!t&&n&&window.postMessage({type:"show-membership-promo-button"},"*")};(0,r.useEffect)((function(){return window.addEventListener("message",a),function(){window.removeEventListener("message",a)}}),[])}},39188:function(e){"use strict";function t(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function n(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{},i=Object.keys(r);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),i.forEach((function(n){t(e,n,r[n])}))}return e}var r=function(e){var t={};return e[0]?(e[0].sizes.forEach((function(e){t[e.size]={url:e.url,altText:e.alt_text||""}})),t):t},i=function(e){return Array.isArray(e)?e:[e]},a=function(e){var t=e.data_source,r=e.data_source_algorithm,a=e.data_source_algorithm_version;return n({},t&&{data_source_name:t},r&&{data_source_algorithm:i(r)},a&&{data_source_algorithm_version:i(a)})},o=function(e){return{type:"item",id:e.id,title:e.name,url:e.canonical_path,createdAt:e.created_at,subtitle:e.description,author:(t=e.authors,t[0]?{name:t[0].name,username:t[0].username,avatar:t[0].avatars[0].sizes[0].url}:{}),images:r(e.thumbnails),tracking:a(e)};var t},s=function(e){return n({},o(e),{type:"featured"})},c=function(e){return{type:"package",id:e.id,name:e.name,cta:e.cta,items:e.items.map((function(e){return o(e)}))}};e.exports={parseWeaverItems:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e.map((function(e,n){var r;return(null===(r=e.treatments)||void 0===r?void 0:r.includes("featured"))||0===n&&t?s(e):"package"===e.type?c(e):"post"===e.type?o(e):null})).filter((function(e){return null!==e}))},parseHallOfFameItems:function(e){return{type:"package",id:"quizzes-hall-of-fame",name:"hall_of_fame",items:e.map((function(e){return o(e)}))}}}},20272:function(e){e.exports={avatar:"author_avatar__jDb6Q",trendingAvatar:"author_trendingAvatar___X7Ul",author:"author_author__CdOnM",by:"author_by__1Iz6Z",packageBy:"author_packageBy__7zfN8"}},1621:function(e){e.exports={category:"categoryitem_category__uj9wC",active:"categoryitem_active__CmBRt",inactive:"categoryitem_inactive__VpmZF",name:"categoryitem_name__2nqgt"}},97051:function(e){e.exports={categoryList:"categorylist_categoryList__2Pr5i",title:"categorylist_title__6UtZx"}},17616:function(e){e.exports={container:"featuredquizlistitem_container__rbYoW",thumbnailContainer:"featuredquizlistitem_thumbnailContainer__zAdaL",textContainer:"featuredquizlistitem_textContainer__1RV_m",title:"featuredquizlistitem_title__Aikdw",subtitle:"featuredquizlistitem_subtitle__yClxv"}},93625:function(e){e.exports={headerWrapper:"header-with-triangles_headerWrapper__6UlEA",title:"header-with-triangles_title__KQmse"}},63965:function(e){e.exports={image:"image_image___YiFD",placeholder:"image_placeholder__moDtc"}},22938:function(e){e.exports={container:"loadmorequizzes_container__ytIAv",loadMoreButton:"loadmorequizzes_loadMoreButton__hz48d",errorMsg:"loadmorequizzes_errorMsg__FQtmk"}},36412:function(e){e.exports={container:"newsletter_container__1HQOR",categoryContainer:"newsletter_categoryContainer__9p6c3",latestContainer:"newsletter_latestContainer__0AHlv",signupWrapper:"newsletter_signupWrapper__l22RL",signupFormWrapper:"newsletter_signupFormWrapper__3AoL_",yellowCircle:"newsletter_yellowCircle__QPvAj",categoryYellowCircle:"newsletter_categoryYellowCircle__Beo35",titleContainer:"newsletter_titleContainer__J7sxg",title:"newsletter_title__Pakag",newsletterText:"newsletter_newsletterText__MtJ8i",reCaptchaText:"newsletter_reCaptchaText__GQ3L7",emoji:"newsletter_emoji__4UpOw",form:"newsletter_form__UqIVJ",input:"newsletter_input__299b_",button:"newsletter_button__MI_R1",validEmailError:"newsletter_validEmailError__DqWHU",successContainer:"newsletter_successContainer__AOTjv",successTitle:"newsletter_successTitle__g3_6t",successText:"newsletter_successText__sJjiN",plane:"newsletter_plane__UQNlm",fly:"newsletter_fly__42Gca"}},50293:function(e){e.exports={innerWrapper:"package_innerWrapper__NWiRv",item:"package_item__w9wtP",cta:"package_cta__rBKBI",carouselCta:"package_carouselCta__Cmw_5",ctaIcon:"package_ctaIcon__YYgKg"}},31872:function(e){e.exports={carouselWrapper:"packageCarousel_carouselWrapper__0yCQG"}},47121:function(e){e.exports={confetti:"packageDescription_confetti__77qBp",quizPartyDescContainer:"packageDescription_quizPartyDescContainer__PRLzp",desc:"packageDescription_desc__Egpk5",quizPartyPackageDesc:"packageDescription_quizPartyPackageDesc__sxoyH",packageDesc:"packageDescription_packageDesc__Ziwm_"}},28798:function(e){e.exports={heading:"packageHeader_heading__6pT0g",quizPartyHeading:"packageHeader_quizPartyHeading__smMrI",carouselHeading:"packageHeader_carouselHeading__Qju4z",carouselQuizPartyHeading:"packageHeader_carouselQuizPartyHeading__RRb_0"}},84910:function(e){e.exports={wrapper:"packageWrapper_wrapper__GigrX",mobileWrapper:"packageWrapper_mobileWrapper__l_PvX",yellow:"packageWrapper_yellow__HGqiY",blue:"packageWrapper_blue__Bsgkb",peach:"packageWrapper_peach__xw5o1",black:"packageWrapper_black__M57du"}},76749:function(e){e.exports={titleAndSearch:"pageHeader_titleAndSearch__mWrna",categories:"pageHeader_categories__B4tN_"}},47864:function(e){e.exports={container:"quizcard_container__Kqb_E",thumbnailLink:"quizcard_thumbnailLink__MEEcO",thumbnailContainer:"quizcard_thumbnailContainer__WqM_L",textContainer:"quizcard_textContainer__sjVhe",carouselTextContainer:"quizcard_carouselTextContainer__FxioL",title:"quizcard_title__AHy3M",author:"quizcard_author__lpwc2"}},96396:function(e){e.exports={feed:"quiz-feed_feed__aX5ni"}},74017:function(e){e.exports={container:"quizlistitem_container__VuHUO",thumbnailLink:"quizlistitem_thumbnailLink__D9pC5",thumbnailContainer:"quizlistitem_thumbnailContainer__9btQm",textContainer:"quizlistitem_textContainer__DtRzc",title:"quizlistitem_title__3dZcw",authorContainerHide:"quizlistitem_authorContainerHide__qaudk"}},1767:function(e){e.exports={sidebar:"sidebar_sidebar__s_3t_"}},41440:function(e){e.exports={title:"title_title___w6Cu"}},13374:function(e){e.exports={trianglesDesktop:"triangles_trianglesDesktop__g95zU",trianglesMobile:"triangles_trianglesMobile__pSmW2"}},50232:function(e){e.exports={content:"index_content__k_CP2",body:"index_body__oZK2F",newsletterHomepage:"index_newsletterHomepage__6wUCU",feed:"index_feed__G4T29"}},67159:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAt4AAAHpCAMAAACC3cvlAAAAA1BMVEXBwcEJldWEAAABc0lEQVR4nO3BgQAAAADDoPtTX2AI1QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACgHfEIAAdRCZbEAAAAASUVORK5CYII="},41505:function(e){e.exports="https://www.buzzfeed.com/static-assets/bf-quiz-feed-ui/_next/static/images/confetti-752225a929ba09cb466da8830c21a3a0.svg"},45767:function(e){e.exports="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTMiIGhlaWdodD0iNjciIHZpZXdCb3g9IjAgMCA5MyA2NyIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEgMTYuNjM5MUw5MiAxTDQ5LjE3NjUgNjIuMDkwMkwzOS42ODcyIDUyLjA3MTRMMjMuMzg1IDY2TDE0LjEzOSAyOS4zNDU5TDEgMTYuNjM5MVoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOTIuODU0OCAwLjQzOTY1NUM5My4wNTc1IDAuNzYxOTUxIDkzLjA0NzIgMS4xNzUxMiA5Mi44Mjg4IDEuNDg2ODdMNDkuOTQ4MiA2Mi42ODI5QzQ5Ljc4NjMgNjIuOTE0MSA0OS41Mjk3IDYzLjA2MDEgNDkuMjQ5IDYzLjA4MDlDNDguOTY4NCA2My4xMDE2IDQ4LjY5MzMgNjIuOTk0OSA0OC40OTk0IDYyLjc5MDFMMzkuNjA5OSA1My40MDA5TDIzLjk2MyA2Ni43NzVDMjMuNzE1OSA2Ni45ODYyIDIzLjM3NzggNjcuMDU0NCAyMy4wNjg3IDY2Ljk1NTNDMjIuNzU5NiA2Ni44NTYzIDIyLjUyMzMgNjYuNjA0IDIyLjQ0MzYgNjYuMjg4TDEzLjI1MTIgMjkuODMxNUwwLjI4Nzc2NiAxNy4yODk2QzAuMDMyNTU4NCAxNy4wNDI3IC0wLjA2MjYzNjcgMTYuNjcyNSAwLjA0MTU4MjcgMTYuMzMyM0MwLjE0NTgwMiAxNS45OTIxIDAuNDMxNzAzIDE1LjczOTggMC43ODA5NDggMTUuNjc5OEw5MS45MDIxIDAuMDEzNTYxN0M5Mi4yNzYyIC0wLjA1MDc1MTUgOTIuNjUyMSAwLjExNzM1OSA5Mi44NTQ4IDAuNDM5NjU1Wk0xNS4yMTUzIDI5LjkzNDhMMjMuNDA4OCA2Mi40MjkyTDI5LjI3MjkgNDEuODA4M0MyOS4zMzQ1IDQxLjU5MTggMjkuNDcxIDQxLjQwNDYgMjkuNjU3OSA0MS4yODA0TDg0Ljk1MDggNC41MzY4N0wxNS4yMTUzIDI5LjkzNDhaTTgxLjg2MSAzLjY1NDY4TDIuOTI5NTEgMTcuMjI1MUwxNC4zMjYgMjguMjUxMUw4MS44NjEgMy42NTQ2OFpNODguNDI3MSA0LjQ4OTY5TDQ5LjA3MTMgNjAuNjU1NkwzMS42Mjk1IDQyLjIzMzJMODguNDI3MSA0LjQ4OTY5Wk0zMC42MjggNDMuOTE0TDI1LjExMDUgNjMuMzE2MkwzOC4zMTMyIDUyLjAzMTJMMzAuNjI4IDQzLjkxNFoiIGZpbGw9ImJsYWNrIi8+Cjwvc3ZnPgo="},24663:function(e){e.exports="https://www.buzzfeed.com/static-assets/bf-quiz-feed-ui/_next/static/images/quizpartylogo-7156a68054bbba957259a0f65da5b526.svg"}}]);
//# sourceMappingURL=727-c7a9562bc43d7ec1.js.map