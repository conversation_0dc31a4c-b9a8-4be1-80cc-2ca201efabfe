(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[847],{45412:function(e,t,n){"use strict";n.d(t,{Z:function(){return m}});var o=n(52322),r=n(2784),i=n(13980),a=n.n(i),c=n(48243),s=n(29169),u=n(95069),l=n.n(u),d=function(e){var t=e.adPos,n=e.sidebarEl,i=e.className,a=e.topPadding,u=(0,r.useState)(0),d=u[0],f=u[1],p=(0,r.useContext)(c.oF),h=(0,r.useContext)(c.z1).adsDisabled,m=(0,r.useCallback)((function(){if(n.current){var e=p.getAvailableTop(n.current);f("number"===typeof a?e+a:e<=75?75:e-50)}}),[n,p,a]);return(0,r.useEffect)((function(){return m(),p.subscribe(m),function(){return p.unsubscribe(m)}}),[m,p]),h?null:(0,o.jsx)("div",{className:"".concat(l().container," ").concat(i||""),style:{top:"".concat(d,"px")},children:(0,o.jsx)(s.Z,{type:t,className:"Ad--sidebar"})})},f=n(8321),p=n.n(f),h=function(e){var t=e.position,n=(0,r.useRef)(null),i="promo-infinite";return"string"===typeof t&&t&&(i=t),(0,o.jsx)("aside",{className:p().sidebar,ref:n,children:(0,o.jsx)(d,{adPos:t<11?"promo".concat(t):i,sidebarEl:n,className:p().sidebarAd,topPadding:28})})};h.propTypes={position:a().oneOfType([a().number,a().string]).isRequired};var m=h},14847:function(e,t,n){"use strict";n.d(t,{O:function(){return ce},Z:function(){return se}});var o=n(94776),r=n.n(o),i=n(52322),a=n(2784),c=n(9772),s=n(48243),u=n(34014),l=n(30353),d=n(23796),f=n(1013),p=n(56758),h=n(36864),m=n(97196),v=n(99404);function b(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function y(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function g(e,t,n){return g="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var o=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=_(e)););return e}(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(n):r.value}},g(e,t,n||e)}function _(e){return _=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},_(e)}function x(e,t){return!t||"object"!==w(t)&&"function"!==typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function j(e,t){return j=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},j(e,t)}var w=function(e){return e&&"undefined"!==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};function O(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,o=_(e);if(t){var r=_(this).constructor;n=Reflect.construct(o,arguments,r)}else n=o.apply(this,arguments);return x(this,n)}}var P=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&j(e,t)}(i,e);var t,n,o,r=O(i);function i(e){var t,n=e.programmaticOnly,o=void 0!==n&&n;return b(this,i),(t=r.call.apply(r,[this].concat(Array.prototype.slice.call(arguments))))._state.adCounter=0,t.programmaticOnly=o,t}return t=i,n=[{key:"getNextAd",value:function(){var e=g(_(i.prototype),"getNextAd",this).apply(this,arguments);return e&&!i.isDone(e)&&(e.key="".concat(e.slot.adPos,"-").concat(this._state.adCounter),this._state.adCounter++),this.programmaticOnly&&(e.slot.size=h.Z.exclude(e.slot.size,p.J7.FLUID,p.J7.NATIVE)),e}},{key:"isDone",value:function(e){return i.isDone(e)}},{key:"reset",value:function(){g(_(i.prototype),"reset",this).call(this),this._state.adCounter=0}}],n&&y(t.prototype,n),o&&y(t,o),i}(v.ZP),k=n(13980),C=n.n(k),S=n(41479),N=n(39786),I=n(33657),A=n(29169),F=n(4083);function E(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function T(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},o=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),o.forEach((function(t){E(e,t,n[t])}))}return e}function B(e){return(0,I.buildPosBound)("story",e,40)}var D=function(e){var t=e.className,n=void 0===t?"":t,o=e.config,r=void 0===o?null:o,a=e.isMobile,c=void 0!==a&&a,s=e.posnum,l=void 0===s?null:s,d=e.stickyWithinPlaceholder,f=void 0!==d&&d,p=(0,N.a)();if(!p)return null;var h,m=B(l);h=T({},r||u.Z[m]);var v="Ad--section-wide xs-hide sm-block ".concat(n).trim();return p.isLargeScreen&&(h.size=(0,I.addBillboards)(h)),!c&&(0,i.jsx)(A.Z,{config:T({},h,(0,I.getWireframeOptions)(h)),className:v,getBFPLayout:function(e){return"bfp_spotlight_ad"===e?"wide":null},stickyWithinPlaceholder:f})},R=function(e){var t=e.className,n=void 0===t?"":t,o=e.config,r=void 0===o?null:o,a=e.isMobile,c=void 0===a||a,s=e.posnum,u=e.stickyWithinPlaceholder,l=void 0!==u&&u,d=r?null:B(s),f="md-hide lg-hide ".concat(n).trim();return c&&(0,i.jsx)(A.Z,{className:f,config:r,stickyWithinPlaceholder:l,type:d})},M=function(e){return(0,i.jsxs)(F.Z,{className:"ad-placeholder-section",children:[(0,i.jsx)(R,T({},e)),(0,i.jsx)(D,T({},e))]})},W=n(45412),Z=function(e){var t=e.className,n=void 0===t?"":t;return(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 18 18",className:n,children:(0,i.jsx)("path",{stroke:"#3B2738",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M11.018 4.223c.664 1.042 1.66 1.978 2.764 2.53m-2.764-2.53c-2.55 2.617-5.11 5.24-7.642 7.873l-.103.56c-.143.778-.282 1.557-.423 2.335a.14.14 0 0 0 .163.163 454.145 454.145 0 0 0 2.9-.531c2.638-2.64 5.251-5.252 7.87-7.87m-2.764-2.53c.292-.306.589-.608.884-.911a1.125 1.125 0 0 1 1.56-.053l1.126 1.013c.479.431.498 1.176.042 1.632l-.849.849"})})},L=n(58599),z=n(41968),q=n.n(z);function J(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function G(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},o=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),o.forEach((function(t){J(e,t,n[t])}))}return e}var U=function(e){var t=e.hasSponsor,n=void 0!==t&&t,o=e.isTrackable,r=void 0!==o&&o,a=e.trackingData,c=void 0===a?{}:a;return(0,i.jsxs)("div",{className:"".concat(q().feedBottom," ").concat(n?q().sponsored:""),children:[(0,i.jsx)("h2",{children:"You\u2019re up to date \ud83c\udf89 Come back later for new content"}),!n&&r&&(0,i.jsxs)(L.Z,{href:"/community/contribute",className:q().button,commonTrackingData:G({item_type:"button",item_name:"write_your_own_post",target_content_type:"feed",target_content_id:"community_contribute"},c),children:[(0,i.jsx)(Z,{className:q().icon})," Write your own post or quiz"]}),!n&&!r&&(0,i.jsxs)("a",{href:"/community/contribute",className:q().button,children:[(0,i.jsx)(Z,{className:q().icon})," Write your own post or quiz"]})]})},H=n(17601),V=n(8321),Y=n.n(V);function X(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function $(e,t,n,o,r,i,a){try{var c=e[i](a),s=c.value}catch(u){return void n(u)}c.done?t(s):Promise.resolve(s).then(o,r)}function K(e){return function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function a(e){$(i,o,r,a,c,"next",e)}function c(e){$(i,o,r,a,c,"throw",e)}a(void 0)}))}}function Q(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ee(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},o=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),o.forEach((function(t){Q(e,t,n[t])}))}return e}function te(e,t){if(null==e)return{};var n,o,r=function(e,t){if(null==e)return{};var n,o,r={},i=Object.keys(e);for(o=0;o<i.length;o++)n=i[o],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ne(e){return function(e){if(Array.isArray(e))return X(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return X(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return X(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var oe=function(){var e=K(r().mark((function e(t){var n,o,i,a,c,s;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n="dev"===l.ov||window.location.hostname.includes("buzzfeed.io")?l.u8:l.Xb,o=new URL(t),i=o.pathname,a=o.search,c=i.split("feed-api/v1/")[1],s="".concat(n,"next/").concat(c).concat(a),e.prev=4,e.next=7,fetch(s,{"Content-Type":"application/json"});case 7:return e.abrupt("return",e.sent);case 10:return e.prev=10,e.t0=e.catch(4),console.error(e.t0),e.abrupt("return",{});case 14:case"end":return e.stop()}}),e,null,[[4,10]])})));return function(t){return e.apply(this,arguments)}}(),re=function(e){var t=e.object_type;return["package","page_header"].includes(t)},ie=function(e){var t=e.adSidebarPosition,n=e.children,o=e.content,r=e.displayOptions,a=e.headline,c="number"===typeof t?t-1:null,s=a&&(!r||!r.hasOwnProperty("show_display_name")||r.show_display_name);return o.length||n||s?(0,i.jsxs)("div",{className:Y().contentContainer,children:[n&&(0,i.jsx)("section",{className:Y().top,children:n}),s&&(0,i.jsx)("header",{className:Y().header,children:(0,i.jsx)("h2",{className:Y().headline,children:a})}),!!o.length&&(0,i.jsx)("ul",{className:"".concat(Y().content," ").concat(Y()[(null===r||void 0===r?void 0:r.grid)||"one_column"]),children:o}),"number"===typeof c&&(0,i.jsx)(W.Z,{position:0===c?"bigstory":c})]}):null};ie.propTypes={adSidebarPosition:C().number,children:C().node,className:C().string,content:C().arrayOf(C().node),displayOptions:C().object,headline:C().string};var ae={adsAfterInitialPositions:[],data:{},sponsor:{},trackingData:{}},ce=function(e){var t,n,o,l=e.adsAfterInitialPositions,v=void 0===l?ae.adsAfterInitialPositions:l,b=e.adsInlineAfterEveryNthPosition,y=void 0===b?5:b,g=e.adsStartIndex,_=void 0===g?1:g,x=e.children,j=e.componentIndex,w=void 0===j?0:j,O=e.data,k=void 0===O?ae.data:O,C=e.getTrackingDataWithPosition,N=e.headline,I=void 0===N?"":N,A=e.isTrackable,F=void 0!==A&&A,E=e.maxItemsPerSequence,T=void 0===E?5:E,B=e.pageName,D=void 0===B?"":B,R=e.sharedIndexRef,Z=e.showEndOfFeedCard,L=void 0!==Z&&Z,z=e.showNumbering,q=void 0!==z&&z,J=e.showSidebar,G=void 0===J||J,V=e.sponsor,X=void 0===V?ae.sponsor:V,$=e.trackingData,Q=void 0===$?ae.trackingData:$,ce=e.membershipFeedType,se=te(e,["adsAfterInitialPositions","adsInlineAfterEveryNthPosition","adsStartIndex","children","componentIndex","data","getTrackingDataWithPosition","headline","isTrackable","maxItemsPerSequence","pageName","sharedIndexRef","showEndOfFeedCard","showNumbering","showSidebar","sponsor","trackingData","membershipFeedType"]),ue=(0,a.useContext)(c.$),le=(0,a.useContext)(s.z1).adsDisabled,de=(0,a.useState)(!1),fe=de[0],pe=de[1],he=(0,a.useState)(null),me=he[0],ve=he[1],be=(0,m.ac)().isMobile,ye=(0,a.useState)(k.items||[]),ge=ye[0],_e=ye[1],xe=(0,a.useState)(k.next),je=xe[0],we=xe[1],Oe=(0,a.useState)(!1),Pe=Oe[0],ke=Oe[1],Ce=(0,m.EF)().trackContentAction,Se=null===(t=k.name)||void 0===t?void 0:t.includes("tab_latest"),Ne=!!(null===X||void 0===X||null===(n=X.name)||void 0===n?void 0:n.length),Ie=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{current:{}};return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>2?arguments[2]:void 0,o="".concat(arguments.length>1?arguments[1]:void 0,"-").concat(n);return e.current[t]||(e.current[t]={count:0,ads:{}}),void 0===e.current[t].ads[o]&&(e.current[t].count++,e.current[t].ads[o]=e.current[t].count),e.current[t].ads[o]}}(R),Ae=null===k||void 0===k||null===(o=k.metadata)||void 0===o?void 0:o.display_options;(0,a.useEffect)((function(){_e(k.items||[])}),[k,ce]),(0,a.useEffect)((function(){if(!ge||"loaded"!==ue.status)return function(){};se.isNext||pe(!0);var e=function(){pe(!0)};return window.addEventListener(d.SP,e),function(){window.removeEventListener(d.SP,e),pe(!1)}}),[ue,se.isNext]),(0,a.useEffect)((function(){if(!fe||!ge.length||le)return function(){};for(var e=[],t=_;void 0!==u.Z["story".concat(t)];t++){var n=u.Z["story".concat(t)];n.size=h.Z.exclude(n.size,p.J7.PROGRAMMATIC_BILLBOARD,p.J7.PROGRAMMATIC_SUPER_LEADERBOARD),e.push(n)}var o=new P({config:{units:e,density:0}}),i=function(){var e=K(r().mark((function e(){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,o.init();case 3:ve(o),e.next=9;break;case 6:e.prev=6,e.t0=e.catch(0),console.error(e.t0);case 9:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(){return e.apply(this,arguments)}}();return i(),function(){o.destroy(),ve(null)}}),[fe,_]);var Fe=(0,a.useMemo)((function(){return K(r().mark((function e(){var t,n,o,i;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ke(!0),F&&Ce(ee({},Q,{action_type:"show",action_value:"load_more",item_name:"load_more",item_type:"button",target_content_id:"",target_content_type:"",target_content_url:""})),e.next=4,oe(je);case 4:if(!(t=e.sent).ok){e.next=15;break}return e.next=8,null===t||void 0===t?void 0:t.json();case 8:n=e.sent,o=n.items,i=n.next,_e((function(e){return ne(e).concat(ne(o))})),we(i),e.next=16;break;case 15:we(null);case 16:ke(!1);case 17:case"end":return e.stop()}}),e)})))}),[F,Q,je,Ce,_e,we]),Ee=(0,a.useMemo)((function(){return(0,i.jsxs)(a.Fragment,{children:[(!Ae||Ae.enable_pagination)&&je&&(0,i.jsx)("li",{className:"".concat(Y().loadMoreButton," ").concat(Pe?Y().fetchingMoreFeedItems:""),children:(0,i.jsx)(H.Z,{onClick:Fe,title:"Load More"})}),L&&!je&&(0,i.jsx)("li",{children:(0,i.jsx)(U,{hasSponsor:Ne,isTrackable:F,trackingData:Q})})]},"feedListEnd")}),[je,Pe,L,Fe,Ne,F,Q]);return(0,a.useMemo)((function(){for(var e,t=ne(v).sort((function(e,t){return e-t})),n=t[t.length-1]||0,o=[],r=T&&n?n+y:T||1/0,c=[],s=0,u=0,l=0,d=null,p=(null===k||void 0===k?void 0:k.startIndexLabel)||0,h=(null===k||void 0===k||null===(e=k.metadata)||void 0===e?void 0:e.display_options)||{},m="standard_page"===D&&w>0,b=function(e){var t,n,r=e.isFullWidthContentItem,a=e.isLastItem,s=0===o.length;if(s&&r&&0===l&&"page_header"!==ge[0].object_type)return null;G&&(null===d&&l+(r?0:1)>=4||null!==d&&l-d-(r?1:0)>=4||ge.length<=4&&a&&!r)&&(n=Ie("promo",w,u),u++);var f=(0,i.jsx)(ie,{adSidebarPosition:n,content:c,displayOptions:(null===k||void 0===k||null===(t=k.metadata)||void 0===t?void 0:t.display_options)||{},headline:s&&"page_header"!==ge[0].object_type&&I,children:s&&x},"contentSegment-".concat(o.length));return c=[],f},g=function(e,t){t=null!==t?t:function(e){throw e}(new TypeError("Cannot destructure undefined"));var n={position_in_unit:e-1};if("function"===typeof C){var o=C(e-1)||{};"object"===typeof o&&Object.keys(o).length&&(n=o)}return ee({},Q,n)},j=1;j<=ge.length;j++){var O,P=ge[j-1],N=!m&&re(P),A=g(j,P),E=null!==(O=t[0])&&void 0!==O?O:0,B=j===ge.length,R=j<=r,Z=E||y,L=T&&(l+1)%(R?r:T)===0,z=!B&&!L&&(R&&(E||n)?Math.max(j-(E?0:n),1)%Z===0:(l+1)%Z===0),J=(0,i.jsx)(S.Z,{className:["bfp_content"===P.object_type?Y().bfpContent:"topic"===D?"topicPostCard":"",N&&"".concat(Y().segment," ").concat(Y().fullWidthContent)].filter(Boolean).join(" "),displayOptions:h,item:P,index:q?String(j+p):null,isTrackable:F,trackingData:A,showSection:Se},"contentComponent-".concat(P.id));if(N?(o.push((0,i.jsxs)(a.Fragment,{children:[b({isFullWidthContentItem:N,isLastItem:B}),J]},"fullWidthContent-".concat(P.id))),d=l):c.push((0,i.jsx)("li",{className:Y().feedItem,children:J},"content-".concat(P.id))),z){var U=Ie("story",w,s),H=null===me||void 0===me?void 0:me.getAd(U-1);me&&H&&me.isDone(H)||c.push((0,i.jsx)("li",{className:Y().feedItemAd,children:(0,i.jsx)(f.B,{config:null===H||void 0===H?void 0:H.slot,renderPlaceholderOnly:!me})},"inlineAd-".concat(U))),s++,t.shift()}if(B&&c.push(Ee),L||B){var V=null;if(!B){var X=Ie("story",w,s)-1+_;V=(0,i.jsx)(M,{isMobile:be,posnum:X,stickyWithinPlaceholder:!0}),s++}o.push((0,i.jsxs)(a.Fragment,{children:[!N&&b({isFullWidthContentItem:N,isLastItem:B}),V]},"lastItem-".concat(j))),d=null,l=0}else l++}return 0===ge.length&&x&&o.push((0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("div",{className:Y().contentContainer,children:[(0,i.jsx)(i.Fragment,{children:x&&(0,i.jsx)("section",{className:Y().top,children:x})}),(0,i.jsx)(W.Z,{position:"bigstory"})]},"feedGroupWrapper-".concat(0))})),o}),[me,x,ge,Ee,I,be,F,T,D,q,Se,ce])};ce.propTypes={adsInlineAfterEveryNthPosition:C().number,adsAfterInitialPositions:C().arrayOf(C().number),adsStartIndex:C().number,children:C().oneOfType([C().arrayOf(C().node),C().node]),data:C().object.isRequired,headline:C().oneOfType([C().string,C().node,C().elementType]),isTrackable:C().bool,maxItemsPerSequence:C().number,pageName:C().string,sharedIndexRef:C().object,showEndOfFeedCard:C().bool,showNumbering:C().bool,showSidebar:C().bool,sponsor:C().object,trackingData:C().object};var se=ce},95069:function(e){e.exports={container:"adStickySidebar_container__HS5kf"}},41968:function(e){e.exports={feedBottom:"feedBottom_feedBottom__q3BUn",sponsored:"feedBottom_sponsored__sPFAT",button:"feedBottom_button__eP1Hz",icon:"feedBottom_icon__Rg2uT"}},8321:function(e){e.exports={contentContainer:"FeedContent_contentContainer__ZoKVf",header:"FeedContent_header__qOAxr",top:"FeedContent_top__TYZB_",content:"FeedContent_content__uswYd",two_columns:"FeedContent_two_columns__WGgyQ",feedItem:"FeedContent_feedItem__TJB4I",headline:"FeedContent_headline__XJeuG",sidebar:"FeedContent_sidebar__xyZFx",sidebarAd:"FeedContent_sidebarAd__tyBSB",loadMoreButton:"FeedContent_loadMoreButton__JD6xu",fetchingMoreFeedItems:"FeedContent_fetchingMoreFeedItems___HxyN",ctaWrap:"FeedContent_ctaWrap__f5_Vq"}}}]);
//# sourceMappingURL=847-630b23a17f0a467e.js.map