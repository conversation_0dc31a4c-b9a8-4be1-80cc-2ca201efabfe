(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[852],{53428:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var r=n(4180),i=n(44629);class o{constructor({cookieName:e,daysExpiry:t,env:n,namespace:o,sourceOfTruthDomain:a,throttleTimer:u=null,secureOnly:s=!0,localDomain:c=(0,i.g)()}){n="live"===n?"prod":n,this.xDomainCookies=new r.Z({sourceOfTruthDomain:a||(0,i.Y)(n),namespace:o,localDomain:c,env:n}),this.cookieName=e,this.daysExpiry=t,this.secureOnly=s,this.throttleTimer=u,this.inMemoryValue=null}get(){return this.throttle?Promise.resolve(this.inMemoryValue):this.xDomainCookies.get(this.cookieName).then((e=>(this.inMemoryValue=e,this.resetThrottle(),e)))}set(e){return this.inMemoryValue=e,this.xDomainCookies.set({name:this.cookieName,value:e,days:this.daysExpiry,secureOnly:this.secureOnly})}resetThrottle(){this.throttleTimer&&(this.throttle=setTimeout((()=>{this.throttle=null}),this.throttleTimer))}}},44629:function(e,t,n){"use strict";n.d(t,{g:function(){return r},Y:function(){return i}});const r=function(e=window){var t=e.location.hostname;const n=t.split(".")[0];return n&&"stage"!==n&&-1===["buzzfeed","buzzfeednews","tasty","huffpost","huffingtonpost"].indexOf(n)&&t.split(".").length>=3&&(t=t.substring(n.length+1)),t},i=function(e="dev"){return"dev"===e?"dev.buzzfeed.io":"prod"===e||"app-west"===e?"buzzfeed.com":"stage.buzzfeed.com"}},4180:function(e,t,n){"use strict";n.d(t,{Z:function(){return u}});var r=n(74337);const i="destination-sync-init",o="destination-sync-read",a=function(e){return e.match(/^stage\./)?`https://${e}`:`https://www.${e}`};class u{constructor({sourceOfTruthDomain:e,localDomain:t,namespace:n,env:r="dev",updateInterval:i=3e5,iframeTimeout:o=3e3}){this.sourceOfTruthDomain=e,this.localDomain=t,this.env=r,this.namespace=n,this.iframeTimeout=o,this.cookies={},e!==t&&this.initIframe().then((()=>{setInterval(this.updateFromIframe.bind(this),i)})).catch((()=>{}))}get(e){return this.sourceOfTruthDomain===this.localDomain?Promise.resolve(r.Z.get(e)):this.initIframe().then((()=>this.cookies[e]||r.Z.get(e))).catch((()=>r.Z.get(e)))}set({name:e,value:t,days:n,secureOnly:i=!0}){r.Z.set({name:e,value:t,days:n,domain:this.localDomain}),this.sourceOfTruthDomain!==this.localDomain&&this.initIframe().then((()=>{var r={namespace:this.namespace,msgType:"destination-sync-write",cookieName:e,cookieVal:t,expiresDays:n,secureOnly:i};const o=a(this.sourceOfTruthDomain);this.iframe.contentWindow.postMessage(JSON.stringify(r),o)})).catch((()=>r.Z.set({name:e,value:t,days:n,domain:this.localDomain})))}cleanup(){if(this.boundOnMessage&&window.removeEventListener("message",this.boundOnMessage),this.iframe){const e=new ErrorEvent({message:"XDomainCookies were cleaned up before ready"});this.iframe.dispatchEvent(e),this.iframe.remove()}this.iframeReady=null}initIframe(){if(this.iframeReady)return this.iframeReady;let e=new Promise(((e,t)=>{this.boundOnMessage=t=>{this.onMessage(t,e)},window.addEventListener("message",this.boundOnMessage),this.createIframe(t)}));var t;return this.iframeReady=Promise.race([(t=this.iframeTimeout,new Promise(((e,n)=>{const r={type:"timeout",msg:`${t}ms timeout exceeded`};setTimeout((()=>n(r)),t)}))),e]).catch((e=>{throw"prod"===this.env&&window.raven&&window.raven.captureException("timeout"===e.type?new Error(`Destination Sync: ${e.msg}`):e),console.error(e),e})),this.iframeReady}createIframe(e){const t=`xdomaincookies-${this.namespace}`,n=document.getElementById(t);if(n)return n.addEventListener("error",(t=>{e(t)})),this.iframe=n,void(this.iframe.dataset.loaded&&this.updateFromIframe());const r=JSON.stringify({namespace:this.namespace,windowOrigin:window.location.origin}),i=document.createElement("iframe");i.style.display="none",i.addEventListener("error",(t=>{e(t)})),i.id=t,i.src=function(e,t){return`${a(e)}/destination-sync.html#${encodeURIComponent(t)}`}(this.sourceOfTruthDomain,r),this.iframe=i,document.body.appendChild(i)}updateFromIframe(){const e={namespace:this.namespace,msgType:o},t=a(this.sourceOfTruthDomain);this.iframe.contentWindow.postMessage(JSON.stringify(e),t)}onMessage(e,t){let n={};try{n=JSON.parse(e.data)}catch(r){}n.namespace===this.namespace&&(n.msgType===i&&(this.iframe.dataset.loaded=!0),n.msgType!==i&&n.msgType!==o||(this.cookies=n.cookies),t())}}},33812:function(e,t,n){"use strict";n.d(t,{TQ:function(){return d}});n(53428);var r=n(74337),i=n(44629);function o(e,t,n){var r,i,o,a,u,s,c;for(this.version="1.0.1",this.minAlphabetLength=16,this.sepDiv=3.5,this.guardDiv=12,this.errorAlphabetLength="error: alphabet must contain at least X unique characters",this.errorAlphabetSpace="error: alphabet cannot contain spaces",this.alphabet="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",this.seps="cfhistuCFHISTU",this.minHashLength=parseInt(t,10)>0?t:0,this.salt="string"===typeof e?e:"","string"===typeof n&&(this.alphabet=n),r="",i=0,a=this.alphabet.length;i!==a;i++)-1===r.indexOf(this.alphabet[i])&&(r+=this.alphabet[i]);if(this.alphabet=r,this.alphabet.length<this.minAlphabetLength)throw this.errorAlphabetLength.replace("X",this.minAlphabetLength);if(-1!==this.alphabet.search(" "))throw this.errorAlphabetSpace;for(i=0,a=this.seps.length;i!==a;i++)-1===(o=this.alphabet.indexOf(this.seps[i]))?this.seps=this.seps.substr(0,i)+" "+this.seps.substr(i+1):this.alphabet=this.alphabet.substr(0,o)+" "+this.alphabet.substr(o+1);this.alphabet=this.alphabet.replace(/ /g,""),this.seps=this.seps.replace(/ /g,""),this.seps=this.consistentShuffle(this.seps,this.salt),(!this.seps.length||this.alphabet.length/this.seps.length>this.sepDiv)&&(1===(u=Math.ceil(this.alphabet.length/this.sepDiv))&&u++,u>this.seps.length?(s=u-this.seps.length,this.seps+=this.alphabet.substr(0,s),this.alphabet=this.alphabet.substr(s)):this.seps=this.seps.substr(0,u)),this.alphabet=this.consistentShuffle(this.alphabet,this.salt),c=Math.ceil(this.alphabet.length/this.guardDiv),this.alphabet.length<3?(this.guards=this.seps.substr(0,c),this.seps=this.seps.substr(c)):(this.guards=this.alphabet.substr(0,c),this.alphabet=this.alphabet.substr(c))}o.prototype.encode=function(){var e,t,n="",r=Array.prototype.slice.call(arguments);if(!r.length)return n;for(r[0]instanceof Array&&(r=r[0]),e=0,t=r.length;e!==t;e++)if("number"!==typeof r[e]||r[e]%1!==0||r[e]<0)return n;return this._encode(r)},o.prototype.decode=function(e){return e.length&&"string"===typeof e?this._decode(e,this.alphabet):[]},o.prototype.encodeHex=function(e){var t,n,r;if(e=e.toString(),!/^[0-9a-fA-F]+$/.test(e))return"";for(t=0,n=(r=e.match(/[\w\W]{1,12}/g)).length;t!==n;t++)r[t]=parseInt("1"+r[t],16);return this.encode.apply(this,r)},o.prototype.decodeHex=function(e){var t,n,r=[],i=this.decode(e);for(t=0,n=i.length;t!==n;t++)r+=i[t].toString(16).substr(1);return r},o.prototype._encode=function(e){var t,n,r,i,o,a,u,s,c,l,d,f=this.alphabet,p=e.length,m=0;for(r=0,i=e.length;r!==i;r++)m+=e[r]%(r+100);for(n=t=f[m%f.length],r=0,i=e.length;r!==i;r++)o=e[r],a=n+this.salt+f,f=this.consistentShuffle(f,a.substr(0,f.length)),t+=u=this.hash(o,f),r+1<p&&(s=(o%=u.charCodeAt(0)+r)%this.seps.length,t+=this.seps[s]);for(t.length<this.minHashLength&&(c=(m+t[0].charCodeAt(0))%this.guards.length,(t=this.guards[c]+t).length<this.minHashLength&&(c=(m+t[2].charCodeAt(0))%this.guards.length,t+=this.guards[c])),l=parseInt(f.length/2,10);t.length<this.minHashLength;)(d=(t=(f=this.consistentShuffle(f,f)).substr(l)+t+f.substr(0,l)).length-this.minHashLength)>0&&(t=t.substr(d/2,this.minHashLength));return t},o.prototype._decode=function(e,t){var n,r,i,o,a=[],u=0,s=new RegExp("["+this.guards+"]","g"),c=e.replace(s," "),l=c.split(" ");if(3!==l.length&&2!==l.length||(u=1),"undefined"!==typeof(c=l[u])[0]){for(n=c[0],c=c.substr(1),s=new RegExp("["+this.seps+"]","g"),u=0,r=(l=(c=c.replace(s," ")).split(" ")).length;u!==r;u++)i=l[u],o=n+this.salt+t,t=this.consistentShuffle(t,o.substr(0,t.length)),a.push(this.unhash(i,t));this._encode(a)!==e&&(a=[])}return a},o.prototype.consistentShuffle=function(e,t){var n,r,i,o,a,u;if(!t.length)return e;for(o=e.length-1,a=0,u=0;o>0;o--,a++)u+=n=t[a%=t.length].charCodeAt(0),i=e[r=(n+a+u)%o],e=(e=e.substr(0,r)+e[o]+e.substr(r+1)).substr(0,o)+i+e.substr(o+1);return e},o.prototype.hash=function(e,t){var n="",r=t.length;do{n=t[e%r]+n,e=parseInt(e/r,10)}while(e);return n},o.prototype.unhash=function(e,t){var n,r=0;for(n=0;n<e.length;n++)r+=t.indexOf(e[n])*Math.pow(t.length,e.length-n-1);return r};const a=parseInt(1e10*Math.random(),10),u=([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(e=>(e^(()=>{try{return crypto.getRandomValues(new Uint8Array(1))[0]}catch(e){return 255*Math.random()}})()&15>>e/4).toString(16))),s=e=>{if(0!==e.indexOf(".")){const t=/[0-9A-Za-z]+/.exec(e);return null!==t&&t[0]===e&&parseInt(e,36)}const t=e.substr(1,2);return((e,{salt:t=null}={})=>new o(t).decode(e)[0])(e.substr(3),{salt:t})},c=e=>{const t=parseInt(25*Math.random()+10,10).toString(36)+parseInt(25*Math.random()+10,10).toString(36);return`.${t}${((e,{salt:t=null,length:n=32}={})=>new o(t,n).encode(e))(e,{salt:t,length:0})}`},l=e=>{const{u:t,uuid:n}=decodeURIComponent(e).split("&").map((e=>e.split("="))).reduce(((e,[t,n])=>({...e,[t]:n})),{});return{legacyIdentifier:s(t||""),identifier:n}},d=({legacy:e=!1}={})=>{const t={name:"bf_visit",days:1e4,domain:(0,i.g)()},n=r.Z.get(t.name),{legacyIdentifier:o,identifier:s}=l(n),d=c(a);return e?o||(r.Z.set({...t,value:encodeURIComponent(`u=${d}&uuid=${s||u}&v=2`)}),a):s||o?s||String(o):(r.Z.set({...t,value:encodeURIComponent(`u=${d}&uuid=${u}&v=2`)}),u)}},29474:function(e,t,n){"use strict";n.d(t,{F:function(){return i}});var r=n(52322),i=function(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("link",{rel:"dns-prefetch",href:"https://c.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://c.amazon-adsystem.com",crossOrigin:""}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"https://c.aps.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://c.aps.amazon-adsystem.com",crossOrigin:""}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"https://aax.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://aax.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"https://config.aps.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://config.aps.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"https://client.aps.amazon-adsystem.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://client.aps.amazon-adsystem.com"})]})}},18140:function(e,t,n){"use strict";n.d(t,{I:function(){return r}});var r=function(e){(null===e||void 0===e?void 0:e.getPageSessionId)&&e.getPageSessionId.then((function(e){e&&(window.BFADS.cpid=e)})).catch((function(e){console.error(e)}));return"(".concat(function(e){var t,n,r;(window.BFADS={authors:(null===e||void 0===e?void 0:e.authors)||null,badges:e.badges||null,buzz_id:null,compilation_id:null,cms_tag:e.cms_tags,cms_tags:e.cms_tags,cpid:null,dfp_keyword:e.dfp_keyword,pagetype:e.pagetype,recipe_id:null,section:e.section,title:(null===e||void 0===e?void 0:e.title)||null,video_id:null,zone3:e.zone3,poe:e.poe,w_category:null,w_keyword:null,w_sentiment:null},e.isTasty&&"recipe"===e.tastyPageType?window.BFADS.recipe_id=e.id:e.isTasty&&"compilation"===e.tastyPageType?window.BFADS.compilation_id=e.id:window.BFADS.buzz_id=e.id,e.video_id&&(window.BFADS.video_id=e.video_id),null===e||void 0===e?void 0:e.watsonTargeting)&&(window.BFADS.w_category=null===(t=e.watsonTargeting)||void 0===t?void 0:t.w_category,window.BFADS.w_keyword=null===(n=e.watsonTargeting)||void 0===n?void 0:n.w_keyword,window.BFADS.w_sentiment=null===(r=e.watsonTargeting)||void 0===r?void 0:r.w_sentiment);(null===e||void 0===e?void 0:e.cat)&&(window.BFADS.cat=e.cat),(null===e||void 0===e?void 0:e.cookieAffiliate)&&(window.BFADS.affiliate=e.cookieAffiliate)}.toString(),")(").concat(JSON.stringify(e),")")}},24027:function(e,t,n){"use strict";n.d(t,{$w:function(){return r},PA:function(){return i},qH:function(){return a},_x:function(){return u},XW:function(){return l},Vx:function(){return m}});function r(e,t,n){return t=c(t),function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,d()?Reflect.construct(t,n||[],c(e).constructor):t.apply(e,n))}function i(e,t){if(n=e,!(null!=(r=t)&&"undefined"!==typeof Symbol&&r[Symbol.hasInstance]?r[Symbol.hasInstance](n):n instanceof r))throw new TypeError("Cannot call a class as a function");var n,r}function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,g(r.key),r)}}function a(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function u(e,t,n){return(t=g(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(){return s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=p(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},s.apply(null,arguments)}function c(e){return(c=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function l(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&f(e,t)}function d(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(d=function(){return!!e})()}function f(e,t){return(f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function p(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=c(e)););return e}function m(e,t,n,r){var i=s(c(1&r?e.prototype:e),t,n);return 2&r&&"function"==typeof i?function(e){return i.apply(n,e)}:i}function g(e){var t,n=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==("undefined"===typeof n?"undefined":(t=n)&&"undefined"!==typeof Symbol&&t.constructor===Symbol?"symbol":typeof t)?n:n+""}},1915:function(e,t,n){"use strict";n.d(t,{C:function(){return s}});var r=n(3379),i=n(75951),o=n(70833),a=n(53709),u=n(88262),s=function(e,t){var n=t.env,s=t.bidder,c=void 0===s?"":s,l=t.isBFN,d=r.Z.get(i.U5),f=((d&&"null"!==d&&"undefined"!==d?d:n.userCountry)||"us").toLowerCase(),p=l||n.isBFN;(0,o.A)("info","adCall","core/ad-call: userCountry",{userCountry:f,revGeoCookie:d,"env.userCountry":n.userCountry});var m=(0,a.gB)(n),g=m.edition,v=m.lang;(0,o.A)("info","adCall","core/ad-call: page edition/country",{edition:g,lang:v});var b={env:n,lang:v,bidder:c},h="bzfd";return p?e.zone1="bfnews":h=(0,u.tb)(f,g),u.ZP[h].buildAdCall(e,b)}},70833:function(e,t,n){"use strict";n.d(t,{A:function(){return c}});var r=n(20238);function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function o(e){return function(e){if(Array.isArray(e))return i(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return i(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var a="\ud83d\udcb8 >>",u={adcall:"".concat(a," [ad call]"),amazonbidder:"".concat(a," [amazonBidder]"),consent:"".concat(a," [consent]"),general:"".concat(a," [adlib]"),lifecycle:"".concat(a," [ad lifecycle]"),performance:"".concat(a," [performance]"),placementlogic:"".concat(a," [placement logic]"),plugins:"".concat(a," [plugins] "),prebid:"".concat(a," [prebid]"),targeting:"".concat(a," [targeting]"),thirdparty:"".concat(a," [thirdparty]")},s=Object.keys(u).filter((function(e){return"general"!==e})).map((function(e){return e.toLowerCase()}));function c(e,t,n){for(var i=arguments.length,a=new Array(i>3?i-3:0),u=3;u<i;u++)a[u-3]=arguments[u];try{var c=(0,r.dn)(window.location.search),d=Object.keys(c).includes("adlib-debug-mode"),f=c["adlib-debug-mode"];if(d&&!f)return l.apply(void 0,[e,t.toLowerCase(),n].concat(o(a)));if(d&&f){var p=f.toLowerCase().split(",");p.forEach((function(r){return s.includes(r)&&t.toLowerCase()===r?l.apply(void 0,[e,t.toLowerCase(),n].concat(o(a))):null}))}}catch(m){console.log(m)}return null}function l(e,t,n){for(var r,i=arguments.length,a=new Array(i>3?i-3:0),s=3;s<i;s++)a[s-3]=arguments[s];return window.console&&window.console.log?(r=window.console)[e].apply(r,[u[t]+": "+n].concat(o(a))):""}},58451:function(e,t,n){"use strict";n.d(t,{$j:function(){return a},x9:function(){return o},ZP:function(){return i}});var r=n(24027);function i(e){Error.apply(this,arguments),this.message=e}i.prototype=Object.create(Error.prototype),i.prototype.constructor=i;var o=function(e){function t(){return(0,r.PA)(this,t),(0,r.$w)(this,t,arguments)}return(0,r.XW)(t,e),(0,r.qH)(t)}(i),a=function(e){function t(){return(0,r.PA)(this,t),(0,r.$w)(this,t,arguments)}return(0,r.XW)(t,e),(0,r.qH)(t)}(i)},39283:function(e,t,n){"use strict";n.d(t,{Y:function(){return o}});var r=function(e){return window.matchMedia(e).matches},i=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"us",t=["uk","gb","ie"];return t.includes(e.toLowerCase())},o=function(e){return[{name:"ads_toolbar_bpages",variations:["off","on"],isEligible:function(){return e.isBPage&&e.isBFO&&r("(min-width: 52rem)")}},{name:"ads_toolbar_feeds",variations:["off","on"],isEligible:function(){return e.isFeedPage&&!e.isBFN&&r("(min-width: 52rem)")}},{name:"ads_toolbar_tasty",variations:["off","on"],isEligible:function(){return"tasty"===(null===e||void 0===e?void 0:e.destination)&&r("(min-width: 52rem)")}},{name:"ads_toolbar_bfn",variations:["off","on"],isEligible:function(){var t,n,i;return((null===e||void 0===e?void 0:e.isBFN)||"buzzfeed_news"===(null===window||void 0===window||null===(t=window.BZFD)||void 0===t||null===(n=t.Context)||void 0===n||null===(i=n.page)||void 0===i?void 0:i.destination))&&r("(min-width: 52rem)")}},{name:"ads_prebid",variations:["off","on"],isEligible:function(){return!0}},{name:"ads_bid_cache",variations:["off","on"],isEligible:function(){return!0}},{name:"ads_amazon_tam",variations:["off","on"],isEligible:function(){return!0}},{name:"ads_ad_lightning",variations:["off","on"],isEligible:function(){return!0}},{name:"ads_doubleverify",variations:["off","on"],isEligible:function(){return!0}},{name:"ads_doubleverify_refresh",variations:["off","on"],isEligible:function(){return!0}},{name:"ads_blockthrough",variations:["off","on"],isEligible:function(){return!0}},{name:"ads_bfj_new_magnite",variations:["off","on"],isEligible:function(){var t,n,r;return"ja-jp"===e.edition||"ja-jp"===e.localizationCountry||"jp"===(null===window||void 0===window||null===(t=window.BZFD)||void 0===t||null===(n=t.Context)||void 0===n||null===(r=n.page)||void 0===r?void 0:r.edition)}},{name:"advertise_international",variations:["off","on"],isEligible:function(){return!["pt-br","es","ja-jp","es-mx","es-es"].includes(e.edition)}},{name:"non_us_ad_lookahead_adjustments",variations:["control","on"],isEligible:function(){return!0}},{name:"ADSGROUP-442-permutive",variations:["off","on"],isEligible:function(){return!0}},{name:"ADSGROUP-143_new_ad_calls_structure",variations:["off","on"],isEligible:function(){return!0}},{name:"ADS-1141-prefetch3",variations:["control","early","late"],isEligible:function(){return!r("(min-width: 52rem)")}},{name:"ads_tam_hem",variations:["off","on"],isEligible:function(){return!0}},{name:"ADS-1791-new-bpage-gpt-lazyload",variations:["off","on"],isEligible:function(){return e.isNewBPage}},{name:"tasty-swap-refresh",variations:["off","on"],isEligible:function(){return!0===(null===e||void 0===e?void 0:e.isTastyUI)&&!i(null===e||void 0===e?void 0:e.userCountry)}},{name:"bf-news-swap-refresh",variations:["off","on"],isEligible:function(){var t,n,r;return((null===e||void 0===e?void 0:e.isBFN)||"buzzfeed_news"===(null===window||void 0===window||null===(t=window.BZFD)||void 0===t||null===(n=t.Context)||void 0===n||null===(r=n.page)||void 0===r?void 0:r.destination)||!0===(null===e||void 0===e?void 0:e.isNewsFeedPager))&&!i(null===e||void 0===e?void 0:e.userCountry)}},{name:"bpage-swap-refresh",variations:["off","on"],isEligible:function(){return(null===e||void 0===e?void 0:e.isNewBPage)&&"ja-jp"!==e.edition&&!i(null===e||void 0===e?void 0:e.userCountry)}},{name:"homepage-swap-refresh",variations:["off","on"],isEligible:function(){return(null===e||void 0===e?void 0:e.isHomePage)&&!0===(null===e||void 0===e?void 0:e.isFeedUI)&&!i(null===e||void 0===e?void 0:e.userCountry)}},{name:"feedpage-swap-refresh",variations:["off","on"],isEligible:function(){var t,n,r;return!0===(null===e||void 0===e?void 0:e.isFeedPager)&&"jp"!==(null===window||void 0===window||null===(t=window.BZFD)||void 0===t||null===(n=t.Context)||void 0===n||null===(r=n.page)||void 0===r?void 0:r.edition)&&!i(null===e||void 0===e?void 0:e.userCountry)}},{name:"feed-ui-swap-refresh",variations:["off","on"],isEligible:function(){return!0===(null===e||void 0===e?void 0:e.isFeedUI)&&!1===(null===e||void 0===e?void 0:e.isHomePage)&&!i(null===e||void 0===e?void 0:e.userCountry)}},{name:"RT-1520-density",variations:["control","midway","increase"],isEligible:function(){var t,n=e.edition,r=e.isShopping,i=e.userCountry,o=e.isAdPost;return!r&&!(null===(t=null===o||void 0===o?void 0:o())||void 0===t||t)&&!["gb","uk","ie","au","nz"].includes(i.toLowerCase())&&!["ja-ja","ja-jp"].includes(n.toLowerCase())}},{name:"ads_amazon_nca_ads",variations:["control","on"],isEligible:function(){return!0}}]}},75127:function(e,t,n){"use strict";n.d(t,{Z:function(){return d}});var r=n(34686),i=n(75951),o=n(3379),a=n(70833);function u(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var s=new r.BH;function c(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(r){return Promise.reject(r)}}}i.MS.needsGDPRConsent()?i.hi.getTCData().then((function(e){e.tcData.gdprApplies||((0,a.A)("info","consent","consentIsKnown resolved, there is a gdpr cookie but gdpr does not apply"),s.resolve())})):((0,a.A)("info","consent","consentIsKnown resolved, no need for consent"),s.resolve()),i.hi.setTCFListener((function(e){var t=e.tcData;(0,a.A)("info","consent","tcf event:",t.eventStatus),"tcloaded"!==t.eventStatus&&"useractioncomplete"!==t.eventStatus||(s.resolve(),(0,a.A)("info","consent","consentIsKnown resolved, user action complete")),i.jQ.hasConsented("ads").then((function(e){"useractioncomplete"===t.eventStatus&&t.gdprApplies&&!e&&o.Z.remove("AMZN-Token","")}))}));var l={fetchAdPurposeConsent:c((function(){return i.MS.needsGDPRConsent()?u(s,(function(){return i.jQ.hasConsented("ads")})):Promise.resolve(!0)})),hasConsented:function(){return l.fetchAdPurposeConsent()},hasConsentedTracking:function(){return u(s,(function(){return i.jQ.hasConsented("tracking")}))},hasConsentedGoogle:c((function(){return i.MS.needsGDPRConsent()?u(s,(function(){return i.jQ.hasConsented("google")})):Promise.resolve(!0)})),hasOptedOutCCPA:function(){return i.MS.needsCCPAConsent()?i.jQ.fetchCCPAOptOut():Promise.resolve(!1)},needsConsent:i.MS.needsConsent()},d={};Object.defineProperties(d,Object.getOwnPropertyDescriptors(l))},53709:function(e,t,n){"use strict";n.d(t,{jP:function(){return f},fd:function(){return l},gZ:function(){return c},P$:function(){return p},vB:function(){return d},gB:function(){return s}});var r=n(20238),i=n(60736),o=n(71288),a=n(70833),u=n(63375),s=function(e){var t=e.localization,n="en",r="en-us";return n=t.language?t.language:"getRawPageLanguage"in t?t.getRawPageLanguage():"en",r=t.edition?t.edition:t.country?t.country:"getEdition"in t?t.getEdition():"en-us","es"===n&&("es-es"===r?n="sp":"es-mx"===r&&(n="mx")),{edition:r,lang:n}},c=function(){var e={adtest:"test",giraffe_test:"giraffe_test",adops_giraffe:"adops_giraffe",ads_qa:"ads_qa"},t=(0,r.jH)(window.location.search);for(var n in e)if(n in t)return e[n];return""},l=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{hasApp:!0,hasAdvertiserChange:!0},n=i.Z.isAny(["xs","sm"])?"mobileweb":"desktop";return t.hasApp&&o.ZP.isMobileApp()&&(n="bfapp_ios",o.ZP.isAndroid()&&(n="bfapp_android")),"thumb-header"===e.adPos&&(n="desktop"),t.hasAdvertiserChange&&e.advertiserContext&&"desktop"===n&&!e.useNewAdCallStructure&&(n=""),n},d=function(e){return(0,a.A)("info","adCall","services/ad-call-util: cms tags:",e.cmsTags),!(!e.pageFilters||!Object.keys(e.pageFilters).length)},f=function(e,t){var n=t.env,r=e.adPos,i=e.zone1,o=(n.pageSection||n.pageCategory||n.pageName||"home").toLowerCase();if(e.advertiserContext)e.useNewAdCallStructure?o="partner":(o="advertiser",/^(bigstory|subbuzz|quiz)/.test(r)&&(o="".concat(o,"/").concat(r),(0,a.A)("info","adCall","services/ad-call-util on page: ".concat(o),"is an advertiser page")));else if("bfd.bio"!==n.destination){var s;(null===n||void 0===n||null===(s=n.allPageSections)||void 0===s?void 0:s.indexOf("In the News"))>=0?o="news":"bringme"===o||/travel-/.test(o)?o="travel":d(n)?o=n.pageName:/news/.test(o)&&i!==u.uY?o="news":/(As\/?Is)/i.test(n.pageSection)?o="asis":n.pageFilter?Object.keys(n.pageFilters||{}).length>0?o="".concat(o,"-").concat(n.pageFilter):"health"===n.pageSection?o="health":/^(archive|badge)$/.test(o)||(o="ai-quiz"===n.pageFilter?"aiquizzes":n.pageFilter):/section/.test(o)&&(o=n.pageSection)}return o=o.replace(/&/g,"").replace(/\s+/g,"-"),(0,a.A)("info","adCall","services/ad-call-util",o,"".concat(o," is a non advertiser page")),o};function p(){var e,t=(0,r.jH)(null===window||void 0===window||null===(e=window.location)||void 0===e?void 0:e.search);return(null===t||void 0===t?void 0:t.origin)||""}},48307:function(e,t,n){"use strict";n.d(t,{U:function(){return o},B:function(){return a}});var r=n(3379),i=n(70833);function o(){var e=r.Z.get("hem");if(e)return u(e);var t=r.Z.get("bf2-b_info");return(0,i.A)("info","general","user info (bf2-b_info cookie)",t),t&&fetch("/auth/ad-track-token/hem",{credentials:"same-origin"}).then((function(t){return t.ok?(e=r.Z.get("hem"),window.dispatchEvent(new CustomEvent("hemCookieSet",{detail:{cookieValue:e}})),u(e)):(console.error("Failed to fetch HEM"),null)})).catch((function(e){return console.error(e)})),null}function a(){return!!r.Z.get("hem")}function u(e){return e=e.replace(/"/g,""),atob(e)}},18977:function(e,t,n){"use strict";n.d(t,{ZP:function(){return B},sH:function(){return E},GA:function(){return N},yJ:function(){return R},Zy:function(){return j},HU:function(){return C},kH:function(){return x},kw:function(){return k},ZU:function(){return M}});var r=n(76635),i=n(43790),o=n(71288),a=n(11313),u=n(9845),s=n(20238),c=n(92523),l=n(78727),d="CLIENT_EVENT_TRACKING";var f=n(3379),p=n(70833),m=n(53709),g={pinterest:"pinterest",twitter:"twitter","t.co":"twitter",facebook:"facebook","m.facebook":"facebook",fban:"facebook",google:"google",youtube:"youtube",yahoo:"yahoo"},v=["animals","arts-entertainment","asis","books","bringme","business","buzz","celebrity","community","culture","entertainment","food","giftguide","goodful","inequality","internet-culture","investigations","jpg","lgbtq","life","music","nifty","opinion","parents","politics","quizzes","reader","reviews","rewind","science","shopping","tech","travel-budget-trips","travel-destinations","travel-food","travel-hacks","travel-products","travel-unique-experiences","tvandmovies","unsolved","videos","weddings","world"],b=["/travel-destinations/australia","/travel-destinations/canada","/travel-destinations/japan","/travel-destinations/london","/travel-destinations/los-angeles","/travel-destinations/mexico","/travel-destinations/new-york-city","/travel-destinations/paris","/travel-destinations/united-states"];function h(e){var t=e.env,n=function(e){if(!e)return null;try{var t=new URL(e);if(!/buzzfeed(news)?.com/i.test(t.host))return null;var n=t.pathname;if(b.includes(n))return n.slice(1).replace(/\//g,"-")}catch(i){}var r=e.match(/buzzfeed(news)?.com\/(section\/)?(\w*)/i)||[];return(r=r.reverse()[0])&&v.includes(r)?r=r.toLowerCase():null}(document.referrer);return(t.isBPage&&n?n:null)||function(e){if(!e||""===e)return null;var t=new URL(e),n=null,r=(e.match(/(m\.)?facebook|t\.co|pinterest|google|youtube|yahoo/i)||[])[0];r&&(r=r.toLowerCase());/buzzfeed(news)?.com/i.test(t.host)||(n=t.host.replace("www.",""));return g[r]||n}(document.referrer)||function(e){var t=(e.match(/fban|twitter|pinterest|google|youtube/i)||[])[0];t&&(t=t.toLowerCase());return g[t]}(navigator.userAgent)||function(e){var t=(e.match(/referrer=(\w+)/)||[])[1];t&&(t=t.toLowerCase());return g[t]}(window.location.search)||""}var y=n(88262);function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function _(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(s){u=!0,i=s}finally{try{a||null==n.return||n.return()}finally{if(u)throw i}}return o}}(e,t)||A(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(e){return function(e){if(Array.isArray(e))return w(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||A(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function A(e,t){if(e){if("string"===typeof e)return w(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?w(e,t):void 0}}function I(e){if(e.length<=40)return e;var t=e.lastIndexOf("-"),n=e.substring(t),r=e.length-40,i=e.substring(0,t-r);return(i=i.replace(/(-|_){1}$/,""))+n}var P=function(e,t){return e.isOn(t).then((function(e){return"".concat(t,"|").concat(e)}))},z={prebidBidCache:function(e){return P(e,"ads_bid_cache")},tastySwapRefresh:function(e){return P(e,"tasty-swap-refresh")},bfNewsSwapRefresh:function(e){return P(e,"bf-news-swap-refresh")},bpageSwapRefresh:function(e){return P(e,"bpage-swap-refresh")},homepageSwapRefresh:function(e){return P(e,"homepage-swap-refresh")},feedpageSwapRefresh:function(e){return P(e,"feedpage-swap-refresh")}};function O(){var e=(0,s.jH)(window.location.search),t=decodeURIComponent(e["dfp-keyword"]||"").split(",");return/^([a-zA-Z0-9]|-|_|,)*$/.test(t)?t:""}function C(e){var t=e.gdpr,n=e.url;return t.hasConsented().then((function(e){if(!e)return"000000-noconsent";if(!n){0;var t=window.frameElement?window.parent:window;n=(0,s.SV)(t.document.URL)}var r=function(e){var t="pdv3-previous_page_session_id",n="cet-page_session_id",r=window.frameElement?window.parent:window,i=c.Z.get(n);if(r[d]=r[d]||{},r[d].current_page_session_url===e&&i)return{page_session_id:i,previous_page_session_id:c.Z.get(t)||""};r[d].current_page_session_url=e,i=(0,l.Z)()||"00000000-0000-0000-0000-000000000000";var o=c.Z.get(n)||"";return c.Z.set({key:n,value:i}),c.Z.set({expires:18e5,key:t,value:o}),{page_session_id:i,previous_page_session_id:o}}(n);return r.page_session_id}))}function j(e){var t=e.env;if(t.isFeedPage){if(t.isBFO&&t.pageName)return[t.pageName];if(t.isBFN&&t.pageSection)return[t.pageSection]}return t.allPageSections.map((function(e){return e=(e=e.toLowerCase().replace(/\/+/g,"")).replace(/ & /g,"and").replace("in the news","news").replace(/\s+/g,"-")}))}function k(e){return e.env.cmsTags.map((function(e){return e.toLowerCase().replace(/\s+/g,"_").replace(/\+/g,"").replace(/__/g,"_")}))}function E(e){var t,n=e.env;return(null===n||void 0===n||null===(t=n.contextualAdTargeting)||void 0===t?void 0:t.map((function(e){return e.clusterID})).join(","))||""}function M(e){var t,n;if(!(null===e||void 0===e||null===(t=e.laserTags)||void 0===t?void 0:t.watson))return!1;var r=Object.keys(null===e||void 0===e||null===(n=e.laserTags)||void 0===n?void 0:n.watson).reduce((function(t,n){var r,i,o;0===n.indexOf("category_level_")&&(t.w_category=t.w_category.concat(null===e||void 0===e||null===(r=e.laserTags)||void 0===r?void 0:r.watson[n].map((function(e){return e.tag_name}))));"sentiment"===n&&(t.w_sentiment=t.w_sentiment.concat(null===e||void 0===e||null===(i=e.laserTags)||void 0===i?void 0:i.watson[n].map((function(e){return e.tag_name}))));"keyword"===n&&(t.w_keyword=t.w_keyword.concat(null===e||void 0===e||null===(o=e.laserTags)||void 0===o?void 0:o.watson[n].map((function(e){return e.tag_name}))));return t}),{w_category:[],w_sentiment:[],w_keyword:[]});return Object.keys(r).reduce((function(e,t){return e[t]=r[t].join(","),e}),{})}var T={get poe(){return o.ZP.isMobileApp()?o.ZP.isIOS()?"bfapp_ios.fallback":"bfapp_android.fallback":o.ZP.isNewsApp()||o.ZP.isNews2App()?o.ZP.isIOS()?"newsapp_ios.fallback":"newsapp_android.fallback":null}};function x(e){var t=e.env;(0,p.A)("info","targeting","getPoe",[T.poe,h({env:t})]);var n=h({env:t});return T.poe||n?[T.poe,n].filter((function(e){return null!==e})):[]}function R(){return f.Z.get("bf-affiliate","")}function N(e){var t=e.env,n=e.abeagle,o=e.gdpr,s=(0,r.values)(z).map((function(e){return e(n)}));return Promise.all(s).then((function(e){var n,r={abtest:(n=[]).concat.apply(n,S(e)).filter((function(e){return e})).map(I)},o=function(e){var t=e.env;try{if(t.isFeedPage)return"A";if(t.isBPage)return"B"}catch(n){}return null}({env:t});o&&(r.pagetype=o),r.poe=x({env:t});var a=O();a.length>0&&(r["dfp-keyword"]=a),r.creativeSet=i.Z.choice(["A","B","C","D"]);var u=j({env:t});u.length&&(r.section=u);var s=k({env:t});s.length&&(r.cms_tag=s),(t.isBPage||"tasty"===t.destination&&("recipe"===(null===t||void 0===t?void 0:t.pageSection)||"article"===(null===t||void 0===t?void 0:t.pageSection)))&&(r.urlslug=window.location.pathname.split("/").slice(-1)[0].split("-").join(",")),r.destination=function(e){var t=e.env,n=[t.destination];if(t.isHomePage)n.push("".concat(t.destination,"homepage"));else if(t.isFeedpager||t.isFeed){var r=t.pageName||t.pageSection||t.pageCategory,i=/section/.test(r),o=/tag/.test(r),a=/vertical/.test(r),u=/badge/.test(r);if(i?r=t.pageSection:(o||a||u)&&(r=t.pageFilter,u&&n.push("badgefeed")),"buzz"===r&&(r="buzztag"),r){var s=t.pageFilter?"".concat(t.pageName,"-").concat(t.pageFilter):t.pageName,c=(0,m.vB)(t)?s:"".concat(r.toLowerCase());n.push("".concat(c,"feed"))}}return n}({env:t});var c=M(t);c&&(r.w_category=c.w_category,r.w_keyword=c.w_keyword,r.w_sentiment=c.w_sentiment);var l=function(e){var t,n,r,i=e.env;return(null===i||void 0===i||null===(t=i.laserTags)||void 0===t||null===(n=t.bf_content_description)||void 0===n||null===(r=n.workflow)||void 0===r?void 0:r.filter((function(e){var t;return e.tag_display_name=null===(t=e.metadata)||void 0===t?void 0:t.bfp_id})).map((function(e){return"bfp:".concat(e.metadata.bfp_id)})))||[]}({env:t});l.length&&(r.rmn=l);var d=E({env:t});d.length&&(r.cat=d);var f=(0,m.P$)();f&&(r.origin=f);var p=R();return p&&(r.affiliate=p),r})).then((function(e){return Promise.all([(n={gdpr:o},n.gdpr.hasConsented().then((function(e){return e?(0,u.TQ)():"000000-noconsent"}))),(t={gdpr:o},t.gdpr.hasConsented().then((function(e){return e?(0,a.lN)():"000000-noconsent"}))),C({gdpr:o})]).then((function(t){var n=_(t,3),r=n[0],i=n[1],o=n[2];return r&&(e.cuid=r.toString()),i&&(e.cvid=i),o&&(e.cpid=o),e}));var t,n}))}var B={getPageTargeting:function(e){var t=e.env,n=e.abeagle,i=e.gdpr,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=t.userCountry||"us",u=(0,m.gB)(t).edition,s=(0,y.PK)(a,u);return Promise.resolve(s({env:t,abeagle:n,gdpr:i})).then((function(e){return Promise.all([e,o])})).then((function(e){var t=_(e,2),n=t[0],i=t[1];return(0,r.mergeWith)(n,i,(function(e,t){if((0,r.isArray)(e))return e.concat(t)}))}))},getDFPKeyword:O,trimExperimentKey:I}},77892:function(e,t,n){"use strict";n.d(t,{tR:function(){return c},Ly:function(){return s},C2:function(){return l},Jg:function(){return i},fG:function(){return o},QK:function(){return a},N1:function(){return u},KS:function(){return r},po:function(){return d},dD:function(){return f},x6:function(){return p},l:function(){return m},i7:function(){return g},mB:function(){return v}});var r="web_performance_metric",i="ad_slot_render",o="ad_slot_request",a="gpt_tag_end",u="gpt_tag_load_time",s="adlib_init_start",c="adlib_init_end",l="adlib_init_time",d="prebid_bid_request",f="prebid_bid_response",p="prebid_duration",m="tam_bid_request",g="tam_bid_response",v="tam_duration"},83509:function(e,t,n){"use strict";n.d(t,{A:function(){return E},l:function(){return T}});var r=n(25616),i=n(70833),o=function(){var e=window;return"function"===typeof e.describe&&"function"===typeof e.expect},a=function(){return window._isAdsServiceTest},u=function(){var e="undefined"!==typeof navigator&&(navigator.connection||navigator.mozConnection||navigator.webkitConnection);return e?e.effectiveType:""},s=function(){return{connection_type:u()}},c=function(e){var t=e.destination,n=e.localization,r=e.pageId,i=e.analyticsPageType,o=e.pageCategory,a=e.webRoot,u=e.isBFO,s=e.isBFN,c=e.isBPage,l=e.isFeedPage,d=e.isHomePage,f="web_".concat(t);(u||s)&&(f="web_bf");var p=i||o;c&&(p="buzz"),(l||d)&&(p="feed");var m=["bfnews","buzzfeednews","buzzfeed_news"],g=t;return Array.isArray(g)&&g.some((function(e){return m.includes(e)}))&&(g="buzzfeed_news"),{destination:g,source:f,context_page_id:r,context_page_type:p,page_edition:n.country,referrer_uri:a||document.referrer||""}},l=n(77892);function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function f(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function m(e){return function(e){if(Array.isArray(e))return p(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return p(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return p(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function v(e){return function(e){if(Array.isArray(e))return g(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return g(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return g(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function h(e){return function(e){if(Array.isArray(e))return b(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return b(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return b(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function w(e){return function(e){if(Array.isArray(e))return y(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return y(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return y(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function S(e){return function(e){if(Array.isArray(e))return _(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return _(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function A(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function I(e){return function(e){if(Array.isArray(e))return A(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return A(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return A(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function P(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function z(e){return function(e){if(Array.isArray(e))return P(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return P(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return P(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function C(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function j(e){return function(e){if(Array.isArray(e))return O(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return O(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return O(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var k,E=function(){if(window.performance&&"function"===typeof window.performance.mark){for(var e,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];(0,i.A)("info","performance","performance.mark",n),(e=performance).mark.apply(e,j(n))}else(0,i.A)("info","performance","performance.mark not supported")},M=(C(k={},l.N1,(function(e){var t=!1;return function(){if(!t){for(var n=performance.getEntriesByName(l.QK)[0],r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];e.apply(void 0,[{type:l.KS},{metric_name:l.N1,metric_value:n.startTime,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:n?JSON.stringify(n):null}].concat(f(i))),t=!0}}})),C(k,l.C2,(function(e){return function(){for(var t=performance.measure(l.C2,l.Ly,l.tR),n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];e.apply(void 0,[{type:l.KS},{metric_name:l.C2,metric_value:t.duration,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:t?JSON.stringify(t):null}].concat(m(r)))}})),C(k,l.Jg,(function(e){var t=!1;return function(){if(!t){for(var n=performance.getEntriesByName(l.Jg)[0],r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];e.apply(void 0,[{type:l.KS},{metric_name:l.Jg,metric_value:n.startTime,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:n.detail?JSON.stringify(n.detail):null}].concat(v(i))),t=!0}}})),C(k,l.fG,(function(e){var t=!1;return function(){if(!t){for(var n=performance.getEntriesByName(l.fG)[0],r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];e.apply(void 0,[{type:l.KS},{metric_name:l.fG,metric_value:n.startTime,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:n.detail?JSON.stringify(n.detail):null}].concat(h(i))),t=!0}}})),C(k,l.po,(function(e){var t=!1;return function(){if(!t){for(var n=performance.getEntriesByName(l.po)[0],r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];e.apply(void 0,[{type:l.KS},{metric_name:l.po,metric_value:n.startTime,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:n.detail?JSON.stringify(n.detail):null}].concat(w(i))),t=!0}}})),C(k,l.x6,(function(e){return function(){for(var t=performance.measure(l.x6,l.po,l.dD),n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];e.apply(void 0,[{type:l.KS},{metric_name:l.x6,metric_value:t.duration,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:t?JSON.stringify(t):null}].concat(I(r)))}})),C(k,l.l,(function(e){var t=!1;return function(){if(!t){for(var n=performance.getEntriesByName(l.l)[0],r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];e.apply(void 0,[{type:l.KS},{metric_name:l.l,metric_value:n.startTime,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:n.detail?JSON.stringify(n.detail):null}].concat(S(i))),t=!0}}})),C(k,l.mB,(function(e){return function(){for(var t=performance.measure(l.mB,l.l,l.i7),n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];e.apply(void 0,[{type:l.KS},{metric_name:l.mB,metric_value:t.duration,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:t?JSON.stringify(t):null}].concat(z(r)))}})),k),T=function(){var e,t="stage",n={};return function(u){var l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!o()&&!a()){l.isProd?t="prod":l.isDev&&(t="dev"),e||(e=(0,r.H)({env:t,source:"adlib"}),(0,i.A)("info","performance","trackPerformanceEvent: env: ".concat(t)));var d=c(l),f=s();(0,i.A)("info","performance","trackPerformanceEvent: ".concat(u),d),M[u]?(n[u]||(n[u]=M[u](e)),n[u](d,f)):(0,i.A)("warn","performance","trackPerformanceEvent: ".concat(u," not found"))}}}()},3843:function(e,t,n){"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(s){u=!0,i=s}finally{try{a||null==n.return||n.return()}finally{if(u)throw i}}return o}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return r(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}n.d(t,{P:function(){return a},J:function(){return o}});var o={NATIVE:[5,5],NATIVE_COMPLEX_100:[100,100],NATIVE_COMPLEX_6:[6,6],NATIVE_COMPLEX_RECTANGLE:[1020,400],FLUID:"fluid",RESEARCH_PIXEL:[1,1],RESEARCH_SURVEY:[2,2],PROGRAMMATIC_PREBID:[1,1],PROGRAMMATIC_BILLBOARD:[970,250],PROGRAMMATIC_HORIZONTAL_4to1:[970,250],PROGRAMMATIC_SMARTPHONE_BANNER:[320,50],PROGRAMMATIC_HORIZONTAL_6to1:[320,50],PROGRAMMATIC_LEADERBOARD:[728,90],PROGRAMMATIC_HORIZONTAL_8to1:[728,90],PROGRAMMATIC_SUPER_LEADERBOARD:[970,90],PROGRAMMATIC_HORIZONTAL_10to1:[970,90],PROGRAMMATIC_VERTICAL:[300,600],PROGRAMMATIC_VERTICAL_1to2:[300,600],PROGRAMMATIC_MEDIUM_RECTANGLE:[300,250],PROGRAMMATIC_TILE_1to1:[300,250],PROGRAMMATIC_SMARTPHONE_BANNER_WIDE:[320,51],COMPLEX_XS_BANNER:[300,50],AMAZON_OUTSTREAM:[400,300]},a=Object.entries(o).filter((function(e){return i(e,1)[0].startsWith("PROGRAMMATIC")})).map((function(e){return i(e,2)[1]}))},89809:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(76635);function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(s){u=!0,i=s}finally{try{a||null==n.return||n.return()}finally{if(u)throw i}}return o}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return i(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var a={add:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return(0,r.unionBy)(e,n,JSON.stringify)},exclude:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return(0,r.differenceBy)(e,n,JSON.stringify)},_filterProgrammatic:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return!0};return e=e.map(JSON.stringify),t.filter((function(t){return-1===e.indexOf(JSON.stringify(t))||n(t)}))},filterProgrammatic:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.min,i=void 0===r?null:r,o=n.max,a=void 0===o?null:o;return this._filterProgrammatic(e,t,(function(e){return!!(i&&e[0]>=i[0]||a&&e[0]<=a[0])}))},excludeProgrammatic:function(e,t){return this._filterProgrammatic(e,t,(function(){return!1}))},getProgrammatic:function(e,t){return(0,r.differenceBy)(t,this.excludeProgrammatic(e,t),JSON.stringify)},isProgrammatic:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.strict,i=void 0===r||r,a=this.getProgrammatic(e,[t]),u=1===a.length;if(u)return!0;if(!i)try{var s=o(t,2),c=s[0],l=s[1];return c>15&&l>15}catch(d){return!1}return!1},isEqual:function(e,t){return e===t||JSON.stringify(e)===JSON.stringify(t)},contains:function(e,t){var n=this;return e.filter((function(e){return n.isEqual(e,t)})).length>0}}},63375:function(e,t,n){"use strict";n.d(t,{M9:function(){return r},uY:function(){return i},ds:function(){return o},aL:function(){return a}});var r="6556",i="bfnews",o="bfd",a="tasty"},88262:function(e,t,n){"use strict";n.d(t,{ZP:function(){return Cn},PK:function(){return Nn},c8:function(){return xn},RT:function(){return Tn},RB:function(){return Mn},tb:function(){return En},Uy:function(){return kn},tV:function(){return jn},Aw:function(){return Rn}});var r={};n.r(r),n.d(r,{BIDDERS:function(){return Pe},getAllBidRequesters:function(){return Ce},getPrebidInitializer:function(){return ze},setPrebidPageTargeting:function(){return Oe}});var i={};n.r(i),n.d(i,{BIDDERS:function(){return yn},getAllBidRequesters:function(){return _n},getPrebidInitializer:function(){return wn}});var o={};n.r(o),n.d(o,{BIDDERS:function(){return An},getAllBidRequesters:function(){return zn},getPrebidInitializer:function(){return Pn}});var a=n(40026),u=n(71288),s=n(63375),c=n(70833),l=n(53709);function d(e){return"div-gpt-ad-".concat(e)}var f=function(e,t){var n=function(e,t){var n=t.env,r=t.lang,i=e.adPos,o=e.adType,a=e.zone1||s.ds,c=e.dfpNetwork||s.M9;"undefined"===typeof e.noExCalls&&(e.noExCalls=!1);var d=(0,l.jP)(e,{env:n}),f=(0,l.gZ)(),p=(0,l.fd)(e),m="";e.advertiserContext?m="partner":/^awareness/.test(i)||/^awareness/.test(o)?m="awareness":/^infinite_post/.test(i)?(m="recirc",e.advertiserContext=!1):"ex"!==o||a===s.uY||a===s.aL||"giftguide"===d||e.noExCalls||(m="ex"),e.advertiserContext&&(m="partnerpost",u.ZP.isMobileApp()&&(p="mobile"));var g=[f,m,p].filter((function(e){return e})).join(".");return{network:c,zone1Obj:{test:f,type:m,platform:p},zone1:"".concat(a,".").concat(g),zone2:r,zone3:d,zone4:i}}(e,t),r=n.adPos,i=n.network,o=n.zone1,a=n.zone2,d=n.zone3,f=n.zone4,p=t.bidder;if((0,c.A)("info","adCall","default: buildAdCallObj",{adPos:r,network:i,zone1:o,zone2:a,zone3:d,zone4:f,advertiserContext:e.advertiserContext}),e.advertiserContext){var m="/".concat(i,"/").concat(o,"/").concat(d);return(0,c.A)("info","adCall","legacy advertiser ad call",m),m}var g="amazon"===p?"/".concat(i,"/").concat(o,"/").concat(d,"/").concat(f):"aiquizzes"===r?"/".concat(i,"/").concat(o,"/").concat(a,"/").concat(f):"/".concat(i,"/").concat(o,"/").concat(a,"/").concat(d,"/").concat(f);return(0,c.A)("info","adCall","is bidder amazon?","amazon"===p),(0,c.A)("info","adCall","Ad is built:",g),g},p=n(18977);function m(e){var t=e.env,n=e.abeagle,r=e.gdpr;return(0,p.GA)({env:t,abeagle:n,gdpr:r})}var g=n(24027),v=n(42235),b=n(34686),h=n(58451),y=n(76635),w=n(41559),_=n(1915);function S(e,t,n){var r=e.eventName,i=e.unitOptions,o=void 0===i?{}:i,a=e.tagOptions,u=void 0===a?{}:a,s=t.env,c=t.localization,l={};if("adPos"in o){l.pos=o.adPos;var d=(0,_.C)(o,{env:s,localization:c}).match(/6556\/([^/]+)/)[1].split(".").filter((function(e){return["bfd","desktop","mobileweb","partnerpost"].indexOf(e)>-1}));l.dfp_platform=d.join(".")}l.pos&&(l.pos=l.pos.replace(/-bp$/,"").replace(/[0-9]/g,"")),l.edition=s.localization.edition,Object.assign(l,u);var f,p=(f={samplingRate:A(r)},(0,y.pickBy)(f,(function(e){return void 0!==e})));n?n({eventName:r,tags:l,bfaConfig:p}):window.bfa&&(0,w.x)("ads",r,l,p)}function A(e){return/bid/.test(e)?.01:/click/.test(e)?1:void 0}var I,P,z={};function O(e){P=e||[]}function C(){P&&P.length&&(P=[])}var j=function(e){var t=function(e,t,n){return S({eventName:e,unitOptions:t,tagOptions:n},{env:r,localization:i},o&&o.trackPrebidEvents?o.trackPrebidEvents:null)},n=e.pbjs,r=e.env,i=e.localization,o=e.tracking;z={bidTimeout:O,bidResponse:function(e){return function(e,t){if(t){var n=P&&P.includes(t.bidder);if(n){var r=t.bidder,i=t.adUnitCode;window.raven&&window.raven.captureMessage("bidTimeout",{bidder:r,adUnitCode:i})}e("bid",I,{bidder:t.bidder,bidTimeToRespond:t.timeToRespond,bidTimeout:n?1:0})}}(t,e)},bidWon:function(e){return function(e,t){var n;t&&e("winningbid",I,{bidder:t.bidder,cpm:(n=t.cpm,Math.ceil(20*n)/20)})}(t,e)},auctionEnd:C},Object.keys(z).forEach((function(e){n.onEvent(e,z[e])}))},k=function(e){I=e},E=n(77892),M=n(83509),T=n(48307);function x(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function R(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function N(e,t){return null!=t&&"undefined"!==typeof Symbol&&t[Symbol.hasInstance]?t[Symbol.hasInstance](e):e instanceof t}function B(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){R(e,t,n[t])}))}return e}function U(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(s){u=!0,i=s}finally{try{a||null==n.return||n.return()}finally{if(u)throw i}}return o}}(e,t)||L(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(e){return function(e){if(Array.isArray(e))return x(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||L(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function L(e,t){if(e){if("string"===typeof e)return x(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?x(e,t):void 0}}var F,H={loadScript:v.v},Z=["subbuzz","pixel"],q="https://micro.rubiconproject.com/prebid/dynamic/13062.js",J=new b.BH;function G(e){for(var t=new Uint8Array(e.length/2),n=0;n<e.length;n+=2)t[n/2]=parseInt(e.substr(n,2),16);return t}function $(e){var t,n=(t=String).fromCharCode.apply(t,D(e));return btoa(n)}function Q(e){var t=e.abeagle;return/\bs=mobile_app\b/.test(window.location.search)?Promise.reject(new h.x9):t.isOn("ads_prebid").then((function(e){return e?Promise.resolve(!0):Promise.reject(new h.x9)}))}var K=function(){return(0,g.qH)((function e(t){var n=t.env,r=t.localization,i=t.abeagle,o=t.gdpr,a=t.ccpa,u=t.tracking,s=t.googletagReady,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,g.PA)(this,e),this._env=n,this._localization=r,this._abeagle=i,this._gdpr=o,this._ccpa=a,this._tracking=u,this.settings=c,this.googletagReady=s}),[{key:"init",value:function(){var e=this;if(F)return F;var t=(0,T.U)();return F=Q({abeagle:this._abeagle}).then((function(){return e._loadLib()})).then((function(){return J})).then((function(n){var r,i,o,a,u,s,d,f,m,g=n.getConfig(),v=(0,p.ZU)(e._env),b=v?B({},v):{},h=B({cms_tag:(0,p.kw)({env:e._env}),section:(0,p.Zy)({env:e._env}),zone3:(0,l.jP)({},{env:e._env}),bsc:null===window||void 0===window||null===(r=window.PQ)||void 0===r||null===(i=r.PTS)||void 0===i?void 0:i.BSC,ids:null===window||void 0===window||null===(o=window.PQ)||void 0===o||null===(a=o.PTS)||void 0===a?void 0:a.IDS,pagetype:(null===(u=e._env)||void 0===u?void 0:u.isBPage)?"B":"A",urlslug:window.location.pathname.split("/").slice(-1)[0].split("-").join(","),qt_loaded:null===window||void 0===window||null===(s=window.PQ)||void 0===s?void 0:s.loaded},b),y=(0,p.sH)({env:e._env}),w=(0,p.yJ)();y&&(h.cat=y),w&&(h.affiliate=w),g.ortb2={site:{ext:{data:B({},h)}}},g.userSync=g.userSync||{},g.userSync.encryptedSignalSources={sources:[{source:["uidapi.com"],encrypt:!1}]},!(null===(d=n.getUserIds())||void 0===d?void 0:d.uid2)&&t&&(g.userSync.userIds=[{name:"uid2",params:{serverPublicKey:"UID2-X-P-MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEOO7NEgQKc9gQyFbwC9FziwQ0Nmf6MCwkTbV/ewvRuYzMVyzBystEl/SHRM2rSkSFKna55c446OY4mU8+9ki8wQ==",subscriptionId:"SVxst33XoG",emailHash:$(G(t))}}]),n.setConfig(g),n.setBidderConfig({bidders:["criteo"],config:{ortb2:{site:{publisher:{id:"IBHQJV"}}}}},!0),n.setBidderConfig({bidders:["pubmatic"],config:{ortb2:{site:{ext:{key_val:(m=h,Object.entries(m).map((function(e){var t=U(e,2),n=t[0],r=t[1],i=Array.isArray(r)?r:[r];return"".concat(n,"=").concat(i.join(","))})).join("|"))}}}}}),(0,c.A)("info","prebid","_prebidConfig:",n.getConfig()),(0,c.A)("info","prebid","pbjs:",n),j({pbjs:n,env:e._env,localization:e._localization,tracking:e._tracking}),e.googletagReady.then((function(){n.registerSignalSources()})),(null===(f=n.getUserIds())||void 0===f?void 0:f.uid2)||n.refreshUserIds()})).catch((function(e){return N(e,h.x9)?null:(console.error(e),Promise.reject(e))}))}},{key:"_loadLib",value:function(){var e=q;return this.settings.accountSrc&&(e=this.settings.accountSrc),(0,c.A)("info","prebid","loading accountSrc:",e),H.loadScript(e).then((function(){return window.pbjs=window.pbjs||{},window.pbjs.que=window.pbjs.que||[],new Promise((function(e){window.pbjs.que.push((function(){J.resolve(window.pbjs),e()}))}))}))}}])}(),V=function(){return(0,g.qH)((function e(t){var n=t.env,r=t.gdpr,i=t.ccpa,o=t.abeagle;(0,g.PA)(this,e),this._env=n,this._gdpr=r,this._ccpa=i,this._abeagle=o}),[{key:"requestBid",value:function(e,t){var n=this,r=t.adPos,i=t.path,o=t.slot;return Z.includes(r)?Promise.resolve(null):Q({abeagle:this._abeagle}).then((function(){return J})).then((function(e){var a=new b.BH,u=n._env,s=function(){(0,M.A)(E.dD,{detail:{slot:i}}),(0,M.l)(E.x6,u),a.resolve(null)};return k(t),setTimeout((function(){var t,a,u,c,d,f;(0,M.A)(E.po,{detail:{slot:i}}),(0,M.l)(E.po,n._env);var m=(0,p.ZU)(n._env),g=m?B({},m):{},v=B({cms_tag:(0,p.kw)({env:n._env}),section:(0,p.Zy)({env:n._env}),zone3:(0,l.jP)({adPos:r},{env:n._env}),visitor:{permutive:JSON.parse(window.localStorage._prubicons||"[]").slice(0,250)},bsc:null===window||void 0===window||null===(t=window.PQ)||void 0===t||null===(a=t.PTS)||void 0===a?void 0:a.BSC,ids:null===window||void 0===window||null===(u=window.PQ)||void 0===u||null===(c=u.PTS)||void 0===c?void 0:c.IDS,pagetype:(null===(d=n._env)||void 0===d?void 0:d.isBPage)?"B":"A",urlslug:window.location.pathname.split("/").slice(-1)[0].split("-").join(","),qt_loaded:null===window||void 0===window||null===(f=window.PQ)||void 0===f?void 0:f.loaded},g);o.ortb2Imp={ext:{data:B({pos:r,appnexus_keywords:B({},v)},v)}},e.rp.requestBids({callback:s,data:{adserver:{adslot:i,name:"gam"},pbadslot:i},gptSlotObjects:[o]})}),0),a})).catch((function(e){return N(e,h.x9)||N(e,h.$j)?null:(console.error(e),Promise.reject(e))}))}}])}(),W=n(3379),X=n(84952),Y=n(89809),ee={automotive_and_vehicles:[{id:"1",name:"Automotive"}],books_and_literature:[{id:"42",name:"Books and Literature"}],business_and_industrial:[{id:"52",name:"Business and Finance"}],careers:[{id:"123",name:"Careers"}],education:[{id:"132",name:"Education"}],shows_and_events:[{id:"150",name:"Events and Attractions"}],family_and_parenting:[{id:"186",name:"Family and Relationships"}],art_and_entertainment:[{id:"201",name:"Fine Art"}],food_and_drink:[{id:"210",name:"Food & Drink"}],health_and_fitness:[{id:"223",name:"Healthy Living"},{id:"286",name:"Medical Health"}],hobbies_and_interests:[{id:"239",name:"Hobbies & Interests"}],home_and_garden:[{id:"274",name:"Home & Garden"}],science_medicine_medical_research:[{id:"286",name:"Medical Health"}],movies:[{id:"324",name:"Movies"}],music:[{id:"338",name:"Music and Audio"}],law_govt_and_politics:[{id:"379",name:"News and Politics"}],finance:[{id:"391",name:"Personal Finance"}],pets:[{id:"422",name:"Pets"}],art_and_entertainment_movies_and_tv_television:[{id:"432",name:"Pop Culture"},{id:"640",name:"Television"}],real_estate:[{id:"441",name:"Real Estate"}],religion_and_spirituality:[{id:"453",name:"Religion & Spirituality"}],science:[{id:"464",name:"Science"}],shopping:[{id:"473",name:"Shopping"}],sports:[{id:"483",name:"Sports"}],style_and_fashion:[{id:"552",name:"Style & Fashion"}],technology_and_computing:[{id:"596",name:"Technology & Computing"}],travel:[{id:"653",name:"Travel"}],games:[{id:"680",name:"Video Gaming"}]},te=function(e){var t;return(null===(t=ee[e])||void 0===t?void 0:t.map((function(e){return e.id})))||[]},ne=function(e){var t;return(null===(t=ee[e])||void 0===t?void 0:t.map((function(e){return e.name})))||[]},re=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"id",n={id:te,name:ne};return e.map((function(e){return n[t](e)})).reduce((function(e,t){return e.concat(t)}),[]).filter((function(e,t,n){return e&&n.indexOf(e)===t}))},ie=function(e){return Object.values((null===e||void 0===e?void 0:e.watson)||[]).reduce((function(e,t){return e.concat(t.map((function(e){return e.tag_name})))}),[])},oe=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"id";return re(ie(e),t)};function ae(e){return e||(e="JP"===(0,X.pP)()?"3675":"3713"),Promise.all([(0,v.v)("".concat("https://config.aps.amazon-adsystem.com/configs/").concat(e)),(0,v.v)("https://client.aps.amazon-adsystem.com/publisher.js")])}function ue(e){return e||(e="JP"===(0,X.pP)()?"3675":"3713"),new Promise((function(t){if(window.apstag||"undefined"!==typeof aps)t(window.apstag||window.aps);else{var n=document.createElement("script");n.type="text/javascript",n.innerHTML=function(e){return"\n    const aps = ((e = '".concat(e,"') => {\n      (window._aps = window._aps || new Map()),\n        _aps.has(e) ||\n          _aps.set(e, { queue: [], store: new Map([['listeners', new Map()]]) });\n      let t = {\n        accountID: e,\n        record: function (e, t) {\n          return new Promise((r, n) => {\n            _aps\n              .get(this.accountID)\n              .queue.push(\n                new CustomEvent(e, { detail: { ...t, resolve: r, reject: n } })\n              );\n          });\n        },\n        read: function (e) {\n          return (\n            this.record('bootstrap/store/willRead', { name: e }),\n            _aps.get(this.accountID).store.get(e)\n          );\n        },\n        run: function (e, t) {\n          _aps.get('_system')?.store.get('listeners').has(e)\n            ? this.record(e, t)\n            : this.record('bootstrap/run/willDrop', { name: e, detail: t });\n        },\n        react: function (e, t) {\n          _aps.get(this.accountID).store.get('listeners').set(e, t);\n          this.record('bootstrap/react/didExecute', { name: e });\n        },\n      };\n      return t.record('bootstrap/version/declare', { version: 5 }), t;\n    })();\n\n    ((t = aps) => {\n      window.apstag = window.apstag || {\n        init: function (e, r) {\n          t.record('legacy/init/enqueue', { config: e, callback: r });\n        },\n        fetchBids: function (e, r) {\n          t.record('legacy/fetchBids/enqueue', { bidConfig: e, callback: r });\n        },\n        setDisplayBids: function () {\n          t.run('legacy/setDisplayBids/enqueue', { arguments });\n        },\n        targetingKeys: function () {\n          return t.read('legacy/targetingKeys') ?? [];\n        },\n        rpa: function (e, r, n) {\n          t.record('legacy/rpa/enqueue', {\n            tokenConfig: e,\n            callback: r,\n            setCookie: n,\n          });\n        },\n        upa: function (e, r, n) {\n          t.record('legacy/upa/enqueue', {\n            tokenConfig: e,\n            callback: r,\n            setCookie: n,\n          });\n        },\n        dpa: function (e) {\n          t.record('legacy/dpa/enqueue', { callback: e });\n        },\n      };\n      t.record('bootstrapExtAPStag/version/declare', { version: 1 });\n    })();\n\n    ((t = aps) => {\n      window.apstag = {\n        ...window.apstag,\n        queue: {\n          push: function (e) {\n            t.record('legacy/queue/push', { callback: e });\n          },\n        },\n      };\n      t.record('bootstrapExtQueue/version/declare', { version: 1 });\n    })();\n\n    ((t = aps) => {\n      window.apstag = {\n        ...window.apstag,\n        customPlacements: function () {\n          return {\n            hint: function (e, r, n) {\n              t.record('customPlacement/hint/define', {\n                id: e,\n                isDefault: r,\n                hintMetadata: n,\n              });\n            },\n            enable: function () {\n              t.record('customPlacement/service/enable');\n            },\n            addEventListener: function (e, r) {\n              if (e === 'PlacementResponse') {\n                t.react('customPlacement/placement/didRespond', r);\n              } else {\n                throw new Error('Unsupported event type');\n              }\n            },\n          };\n        },\n      };\n      t.record('bootstrapExtASR/version/declare', { version: 1 });\n    })();\n\n    ((t = aps) => {\n      window.apstag = {\n        ...window.apstag,\n        nativeContent: function () {\n          return {\n            enable: function () {\n              t.record('nativeCommerceAds/service/enable');\n            },\n            registerHook: function (e, r) {\n              if (e === 'nativeCommerceAds.updateClickUrl') {\n                t.react('nativeCommerceAds/clickUrl/update', (k) => {\n                  return {\n                    status: 'completed',\n                    value: { url: r(k?.detail?.url, k?.detail?.element) },\n                  };\n                });\n              } else {\n                throw new Error('Unsupported event type');\n              }\n            },\n          };\n        },\n      };\n      t.record('bootstrapExtNCA/version/declare', { version: 1 });\n    })();\n  ")}(e),document.head.appendChild(n),window.aps=window.aps||("undefined"!==typeof aps?aps:{}),t(window.apstag||window.aps)}}))}function se(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ce(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function le(e,t){return null!=t&&"undefined"!==typeof Symbol&&t[Symbol.hasInstance]?t[Symbol.hasInstance](e):e instanceof t}function de(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ce(e,t,n[t])}))}return e}function fe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(s){u=!0,i=s}finally{try{a||null==n.return||n.return()}finally{if(u)throw i}}return o}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return se(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return se(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var pe=(0,X.pP)();var me,ge,ve,be="ads_amazon_nca_ads",he="ads_amazon_nca";function ye(e){var t=e.abeagle;return/\bs=mobile_app\b/.test(window.location.search)?Promise.reject(new h.x9):t.isOn("ads_amazon_tam").then((function(e){return!!e||Promise.reject(new h.x9)}))}me=void 0,ge=new b.BH,ve=new b.BH;var we=function(e){var t=(0,p.Zy)({env:e});return{site:{cat:oe(e.laserTags,"id"),cattax:6,ext:{sitetaxonomy:oe(e.laserTags,"name").join()},kwarray:oe(e.laserTags,"name"),page:window.location.origin+window.location.pathname,pagecat:re([e.pageCategory.toLowerCase()],"id"),publisher:{id:"1978e933-c244-42df-b00f-66a0b717c789"},sectioncat:re(t.map((function(e){return e.toLowerCase()})),"id")}}},_e=function(){return(0,g.qH)((function e(t){var n=t.env,r=t.localization,i=t.abeagle,o=t.gdpr,a=t.ccpa;(0,g.PA)(this,e),this._env=n,this._localization=r,this._abeagle=i,this._gdpr=o,this._ccpa=a,this.isNCAFlagOn=!1}),[{key:"init",value:function(){var e=this;return me||(me=ue().then((function(){return ye({abeagle:e._abeagle})})).then((function(){return Promise.all([e._abeagle.isOn(he),ue()])})).then((function(t){var n=fe(t,1)[0];return e.isNCAFlagOn=n,ae()})).then((function(){return e._configure()})).catch((function(e){return le(e,h.x9)?null:(console.error(e),Promise.reject(e))})))}},{key:"_updateAMZNToken",value:function(e,t,n){var r={optOut:null!==e&&"Y"===e.charAt(2)||null!==t&&!t,hashedRecords:new Array({type:"email",record:n})};(0,c.A)("info","amazonBidder","token config",r),window.apstag.rpa(r,(function(e){(0,c.A)("info","amazonBidder","token:",e)}))}},{key:"_formatSection",value:function(e){return e||(e="NoSection"),e.match("/")&&(e=e.replace("/","")),(0,c.A)("info","amazonBidder","section",e),e}},{key:"_configure",value:function(){var e=this;if(!window.apstag){var t=new Error("No `apstag` global");throw ge.reject(t),t}if("undefined"===typeof window.aps){var n=new Error("No `aps` global");throw ve.reject(n),n}var r=this._env.pageSection||"",i=(0,T.U)();i||((0,c.A)("info","amazonBidder","no hem cookie - user is not logged in"),W.Z.get("AMZN-Token")&&((0,c.A)("info","amazonBidder","found AMZN-Token, deleting"),window.apstag.dpa()));return Promise.all([this._ccpa.getConsentValue(),this._gdpr.fetchAdPurposeConsent()]).then((function(t){var n,o=fe(t,2),a=o[0],u=o[1],s=(0,p.Zy)({env:e._env}),l={pubID:"JP"===pe?"3675":"3713",adServer:"googletag",params:{cms_tags:(0,p.kw)({env:e._env}),section:s,si_section:e._formatSection(r),us_privacy:a||"1---"},gdpr:{cmpTimeout:50},deals:!0};e.isNCAFlagOn&&window.apstag.queue.push((function(){window.apstag.nativeContent().registerHook("nativeCommerceAds.updateClickUrl",(function(e,t){(0,c.A)("info","amazonBidder","nativeContent.updateClickUrl",e,t);var n="https://r.bttn.io?btn_ref=org-21949126c81417e9&btn_url="+encodeURIComponent(e);return t&&(t.classList.add("js-buttonposttap-modified"),t.classList.add("noskim"),t.classList.add("aps-nca-processed")),n})),window.apstag.nativeContent().enable()})),(null===e||void 0===e||null===(n=e._env)||void 0===n?void 0:n.laserTags)&&(l.videoServer="DFP",l.signals={ortb2:we(null===e||void 0===e?void 0:e._env)}),(0,c.A)("info","amazonBidder","apstag.init params",de({},l)),window.apstag.init(l),ge.resolve(window.apstag),ve.resolve(window.aps),e._abeagle.isOn("ads_tam_hem").then((function(t){(0,c.A)("info","amazonBidder","ads_tam_hem feature flag on?",t),i&&t&&e._updateAMZNToken(a,u,i)}))})),this._ccpa.getConsentValue()}}])}(),Se=function(e,t){e&&t&&(t.isOn(be).then((function(t){t&&window.aps.record("ad/signals/define",{context:we(e)})})),t.isOn(he).then((function(e){e&&window.apstag.queue.push((function(){window.apstag.nativeContent().enable()}))})))},Ae=function(){return(0,g.qH)((function e(t){var n=t.env,r=t.googletagReady,i=t.abeagle;(0,g.PA)(this,e),this._env=n,this._localization=n.localization,this._googletagReady=r,this._abeagle=i,this.isAmazonNCAADSFlagOn=!1,(0,c.A)("info","amazonBidder","BidRequester env:",n)}),[{key:"requestBid",value:function(e,t){var n=this;(0,c.A)("info","amazonBidder","requestBid options:",t);var r=this._env;return ye({abeagle:this._abeagle}).then((function(){return n._abeagle.isOn(be)})).then((function(e){n.isAmazonNCAADSFlagOn=e})).then((function(){return Promise.all([ge,ve,n._getBidSlot(e,t)])})).then((function(e){var t=fe(e,3),n=t[0],r=t[1],i=t[2];return i?((0,c.A)("info","amazonBidder","slot:",i),[n,r,i]):Promise.reject(new h.$j)})).then((function(e){var t=fe(e,3),i=t[0],o=t[1],a=t[2],u=n.isAmazonNCAADSFlagOn;return new Promise((function(e,t){var n=function(){(0,M.A)(E.i7,{detail:{slot:(null===a||void 0===a?void 0:a.slotNames)||(null===a||void 0===a?void 0:a.id)}}),(0,M.l)(E.mB,r),e()};setTimeout((function(){(0,M.A)(E.l,{detail:{slot:(null===a||void 0===a?void 0:a.slotNames)||(null===a||void 0===a?void 0:a.id)}}),(0,M.l)(E.l,r);try{var e={slots:[a],timeout:800};if((0,c.A)("info","amazonBidder","fetchBidsParams",de({},e)),u){var s=[a],l=(d=function(){var e,t,r,i=s.map((function(e){return e.id}));return e=o.record("ad/targeting/fetch",{itemIds:i}),t=function(){window.googletag.cmd.push((function(){o.run("ad/targeting/attach",{itemIds:i,adServer:"googletag"})})),n()},r?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return Promise.resolve(d.apply(this,e))}catch(n){return Promise.reject(n)}});o.record("ad/slot/define",{imp:s}),l()}else i.fetchBids(e,n)}catch(f){console.error(f),t()}var d}),0)}))})).then((function(){return new Ie({options:t,googletagReady:n._googletagReady,isAmazonNCAADSFlagOn:n.isAmazonNCAADSFlagOn})})).catch((function(e){return le(e,h.x9)||le(e,h.$j)?null:(console.error(e),Promise.reject(e))}))}},{key:"_isPageMultiFormatEligible",value:function(e){var t=(e.pageSection||e.pageCategory||e.pageName||"home").toLowerCase();return!e.hasQuiz&&"quizzes"!==t&&(e.isBFO||e.isBFN)}},{key:"_getBidSlotNativeAds",value:function(e,t){var n,r,i,o,a,u=t.adPos,s=t.wid,c=t.size,d=Y.Z.getProgrammatic(this._env.programmaticSizes,c);if(0===d.length)return null;var f={id:e.getSlotContainerId(s),banner:{tagid:e.buildAdCall(t,{env:this._env,localization:this._localization,bidder:"amazon"}),format:d.map((function(e){return{w:e[0],h:e[1]}}))},ext:{cms_tag:(0,p.kw)({env:this._env}),section:(0,p.Zy)({env:this._env}),zone3:(0,l.jP)({},{env:this._env}),bsc:null===window||void 0===window||null===(n=window.PQ)||void 0===n||null===(r=n.PTS)||void 0===r?void 0:r.BSC,ids:null===window||void 0===window||null===(i=window.PQ)||void 0===i||null===(o=i.PTS)||void 0===o?void 0:o.IDS,pagetype:(null===(a=this._env)||void 0===a?void 0:a.isBPage)?"B":"A"}};["awareness","tb","comment"].some((function(e){return u&&0===u.indexOf(e)}))||(f.native={request:{ver:"1.2",context:1,contextsubtype:10,plcmttype:1,assets:[{id:1,title:{len:100}},{id:2,img:{type:3,w:300,h:250}}]}});var m=/(^promo-inline.+)|(^sidebar.+)/.test(u);return this._isPageMultiFormatEligible(this._env)&&m&&(f.video={w:400,h:300,mimes:["video/x-flv","video/mp4","application/javascript"],minduration:5,maxduration:60,protocols:[2,3]}),f}},{key:"_getBidSlot",value:function(e,t){var n,r,i,o,a;if(this.isAmazonNCAADSFlagOn)return this._getBidSlotNativeAds(e,t);var u=t.adPos,s=t.wid,c=t.size,d=Y.Z.getProgrammatic(this._env.programmaticSizes,c);if(0===d.length)return null;var f={slotID:e.getSlotContainerId(s),slotName:e.buildAdCall(t,{env:this._env,localization:this._localization,bidder:"amazon"}),slotParams:{cms_tag:(0,p.kw)({env:this._env}),section:(0,p.Zy)({env:this._env}),zone3:(0,l.jP)({},{env:this._env}),bsc:null===window||void 0===window||null===(n=window.PQ)||void 0===n||null===(r=n.PTS)||void 0===r?void 0:r.BSC,ids:null===window||void 0===window||null===(i=window.PQ)||void 0===i||null===(o=i.PTS)||void 0===o?void 0:o.IDS,pagetype:(null===(a=this._env)||void 0===a?void 0:a.isBPage)?"B":"A"}},m=/(^promo-inline.+)|(^sidebar.+)/.test(u);return this._isPageMultiFormatEligible(this._env)&&m?(f.mediaType="multi-format",f.multiFormatProperties={display:{sizes:d},video:{sizes:[[400,300]]}}):f.sizes=d,f}}])}(),Ie=function(){return(0,g.qH)((function e(t){var n=t.options,r=t.googletagReady,i=t.isAmazonNCAADSFlagOn;(0,g.PA)(this,e),this.options=n,this._googletagReady=r,this.isAmazonNCAADSFlagOn=i}),[{key:"resetTargeting",value:function(){var e=this;return this._googletagReady.then((function(t){t.pubads().getSlots().filter((function(t){return String(t.getTargeting("wid"))===String(e.options.wid)})).forEach((function(e){e.getTargetingKeys().filter((function(e){return/^amzn/.test(e)})).forEach((function(t){return e.clearTargeting(t)}))}))}))}},{key:"setResponseTargeting",value:function(){var e=this;return Promise.all([ge,this._googletagReady]).then((function(t){var n=fe(t,2),r=n[0],i=n[1];(0,c.A)("info","amazonBidder","#setResponseTargeting - apstag, googletag:",r,i);try{e.isAmazonNCAADSFlagOn||r.setDisplayBids()}catch(o){(0,c.A)("error","amazonBidder","#setResponsTargeting - apstag error",o)}}))}},{key:"setTargeting",value:function(){var e=this;return this.resetTargeting().then((function(){return e.setResponseTargeting()}))}}])}(),Pe=["prebid","amazon"],ze=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"prebid",t=arguments.length>1?arguments[1]:void 0;return"prebid"===e?new K(t,{accountSrc:q}):"amazon"===e?new _e(t):null},Oe=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"prebid",t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;return"amazon"===e?Se(t,n):null},Ce=function(e){return[new V(e),new Ae(e)]};var je={buildAdCall:f,getSlotContainerId:d,Prebid:r,useService:function(e){return"doubleverify"===e?"signals":""},getCommonTargeting:m};function ke(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ee={buildAdCall:function(e,t){var n=s.ds;return"tasty"===t.env.destination&&(n=s.aL),f(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ke(e,t,n[t])}))}return e}({},e,{zone1:"au.".concat(n),noExCalls:n===s.aL}),t)}},Me=n(60736),Te=n(3843),xe={size:[Te.J.PROGRAMMATIC_LEADERBOARD]},Re={size:[Te.J.PROGRAMMATIC_MEDIUM_RECTANGLE,[336,280],[250,250],[200,200],[1,1]]},Ne={awareness:{desktop:{overrideSizes:!0,platform:"desktop",ukPos:"billboard",size:[Te.J.PROGRAMMATIC_BILLBOARD,Te.J.PROGRAMMATIC_LEADERBOARD]},mobileweb:{disableUnit:!0}},tb:{desktop:{disableUnit:!0},mobileweb:{overrideSizes:!0,platform:"mobileweb",ukPos:"bottom_banner",size:[Te.J.PROGRAMMATIC_SMARTPHONE_BANNER]}},bigstory:{mobileweb:{disableUnit:!0},desktop:{overrideSizes:!0,platform:"desktop",ukPos:"siderail1",size:[Te.J.PROGRAMMATIC_MEDIUM_RECTANGLE,Te.J.PROGRAMMATIC_VERTICAL]}},promo1:{mobileweb:{disableUnit:!0},desktop:{overrideSizes:!0,platform:"desktop",ukPos:"siderail2",size:[Te.J.PROGRAMMATIC_MEDIUM_RECTANGLE,Te.J.PROGRAMMATIC_VERTICAL]}},"promo-inline-infinite":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},"story-bpage":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},subbuzz:{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},pixel:{desktop:{overrideSizes:!0,platform:"desktop",ukPos:"skin",size:[Te.J.RESEARCH_PIXEL]},mobileweb:{disableUnit:!0}},"promo-bottom1":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},"promo-bottom2":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},"promo-bottom3":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},"promo-bottom4":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},"promo-bottom5":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},"promo-bottom6":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},"promo-bottom7":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},"promo-bottom8":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},"promo-bottom9":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},"promo-bottom10":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},"promo-bottom-infinite":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},"promo-wide-infinite":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}},"promo-infinite-bp":{desktop:{disableUnit:!0},mobileweb:{disableUnit:!0}}},Be={0:"skin",1301:"siderail1",1302:"siderail2",1303:"siderail-infinite",1304:"siderail-infinite",1305:"siderail-infinite",1306:"siderail-infinite",1307:"siderail-infinite",1308:"siderail-infinite",1309:"siderail-infinite",1310:"siderail-infinite",13e3:"siderail-infinite",13:"siderail1",130:"mpu-infinite",230:"siderail2",231:"siderail-infinite",232:"siderail-infinite-1",233:"siderail-infinite-2",234:"siderail-infinite-3",2300:"siderail-infinite-4",210:"mpu1",211:"mpu2",212:"mpu3",213:"mpu4",214:"mpu5",215:"mpu6",216:"mpu7",217:"mpu8",218:"mpu9",219:"mpu10",2e3:"mpu-infinite",200:"mpu-infinite-200",201:"mpu-infinite-201",202:"mpu-infinite-202",203:"mpu-infinite-203",204:"mpu-infinite-204",205:"mpu-infinite-205",206:"mpu-infinite-206",207:"mpu-infinite-207",208:"mpu-infinite-208",209:"mpu-infinite-209",2010:"siderail1",2011:"siderail2",220:"mpu1",225:"mpu2",250:"mpu-infinite",260:"mpu-infinite",270:"mpu1",280:"mpu2",281:"mpu3",282:"mpu4",290:"siderail1",291:"siderail2",292:"siderail-infinite",293:"siderail-infinite-1",294:"siderail-infinite-2",1:"mpu1",2:"mpu2",3:"mpu3",4:"mpu4",5:"mpu5",6:"mpu6",7:"mpu7",8:"mpu8",9:"mpu9",11:"mpu10",12:"mpu-infinite-11",16:"mpu-infinite-12",17:"mpu-infinite-13",18:"mpu-infinite-14",19:"mpu-infinite-15",20:"mpu-infinite-16",21:"mpu-infinite-17",22:"mpu-infinite-18",23:"mpu-infinite-19",24:"mpu-infinite-20",25:"mpu-infinite-21",28:"mpu-infinite-22",29:"mpu-infinite-23",30:"mpu-infinite-24",31:"mpu-infinite-25",32:"mpu-infinite-26",33:"mpu-infinite-27",34:"mpu-infinite-28",35:"mpu-infinite-29",36:"mpu-infinite-30",37:"mpu-infinite-31",38:"mpu-infinite-32",39:"mpu-infinite-33",40:"mpu-infinite-34",41:"mpu-infinite-35",43:"mpu-infinite-36",44:"mpu-infinite-37",50:"mpu-infinite-38",51:"mpu-infinite-39",42:"billboard",52:"bottom_banner",83:"bottom_banner"};function Ue(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function De(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ue(e,t,n[t])}))}return e}var Le="71347885,".concat(s.M9),Fe={hasApp:!1,hasAdvertiserChange:!1};function He(e){var t=e.match(/\d+/);if(t&&parseInt(t[0],10)<11)return t[0];return"-infinite"}var Ze=function(){var e={};return function(t){var n;if(e[t.adPos])return e[t.adPos];var r=(0,l.fd)(t,Fe),i=t.adPos.includes("story-bpage")?t.adPos:t.adPos.replace("-bp",""),o=t.targeting||{};if("wid"in o||(o.wid=t.wid),"pos"in o||(o.pos=[t.adPos]),null===(n=Ne[i])||void 0===n?void 0:n[r])e[t.adPos]=De({},t,{targeting:o},Ne[i][r]);else if(/^promo(\d+)?-wide|inline|(^story(\d+|-bpage))/g.test(i)){var a=He(i),u="mpu".concat(a),s=xe;"mobileweb"===r&&(u+="-m",s=Re),e[t.adPos]=De({},t,{targeting:o},s,{ukPos:u})}else if(function(e){return/^promo(?!.*bottom).*|sidebar/g.test(e)}(i)){var d=He(i),f="siderail-infinite";"1"!==d&&"2"!==d||(f="siderail".concat(d)),e[t.adPos]=De({},t,{targeting:o},Ne.bigstory[r],{ukPos:f})}else(0,c.A)("info","adCall","geo-uk: remapAdSlotOptions","no remap",t.adPos),e[t.adPos]=De({},t,{targeting:o});return e[t.adPos]}}(),qe=n(20238);var Je=[1,1],Ge=[728,90],$e=[300,250],Qe=[300,600],Ke=[336,280],Ve=[250,250],We=[200,200],Xe={MPU1:[{from:0,supportedSizes:[Ge]}],MPU2:[{from:0,supportedSizes:[Ge]}],MPU3:[{from:0,supportedSizes:[Ge]}],MPU4:[{from:0,supportedSizes:[Ge]}],MPU5:[{from:0,supportedSizes:[Ge]}],MPU6:[{from:0,supportedSizes:[Ge]}],MPU7:[{from:0,supportedSizes:[Ge]}],MPU8:[{from:0,supportedSizes:[Ge]}],MPU9:[{from:0,supportedSizes:[Ge]}],MPU10:[{from:0,supportedSizes:[Ge]}],MPU1_M:[{from:0,supportedSizes:[$e,Ke,Ve,We,Je]}],MPU2_M:[{from:0,supportedSizes:[$e,Ke,Ve,We,Je]}],MPU3_M:[{from:0,supportedSizes:[$e,Ke,Ve,We,Je]}],MPU4_M:[{from:0,supportedSizes:[$e,Ke,Ve,We,Je]}],MPU5_M:[{from:0,supportedSizes:[$e,Ke,Ve,We,Je]}],MPU6_M:[{from:0,supportedSizes:[$e,Ke,Ve,We,Je]}],MPU7_M:[{from:0,supportedSizes:[$e,Ke,Ve,We,Je]}],MPU8_M:[{from:0,supportedSizes:[$e,Ke,Ve,We,Je]}],MPU9_M:[{from:0,supportedSizes:[$e,Ke,Ve,We,Je]}],MPU10_M:[{from:0,supportedSizes:[$e,Ke,Ve,We,Je]}],SIDERAIL1:[{from:0,supportedSizes:[$e,Qe]}],SIDERAIL2:[{from:0,supportedSizes:[$e,Qe]}],BOTTOM_BANNER:[{from:0,supportedSizes:[[320,50]]}],BILLBOARD:[{from:0,supportedSizes:[[970,250],Ge]}],SKIN:[{from:0,supportedSizes:[Je]}]};function Ye(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function et(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ye(e,t,n[t])}))}return e}var tt="gumgum",nt="muakzgkq",rt=12197,it={billboard:{zone:nt,pubId:rt,product:"skins"},siderail1:{zone:nt,pubId:rt,slot:1117381},siderail2:{zone:nt,pubId:rt,slot:1117381},mpu1:{zone:nt,pubId:rt,slot:1117387},mpu2:{zone:nt,pubId:rt,slot:1117381},mpu3:{zone:nt,pubId:rt,slot:1117387},mpu4:{zone:nt,pubId:rt,slot:1117381},mpu5:{zone:nt,pubId:rt,slot:1117387},mpu6:{zone:nt,pubId:rt,slot:1117387},mpu7:{zone:nt,pubId:rt,slot:1117387},mpu8:{zone:nt,pubId:rt,slot:1117387},mpu9:{zone:nt,pubId:rt,slot:1117387},mpu10:{zone:nt,pubId:rt,slot:1117387},bottom_banner:{zone:nt,pubId:rt},"mpu1-m":{zone:nt,pubId:rt,slot:1117381},"mpu2-m":{zone:nt,pubId:rt,slot:1113862},"mpu3-m":{zone:nt,pubId:rt,slot:1117381},"mpu4-m":{zone:nt,pubId:rt,slot:1117381},"mpu5-m":{zone:nt,pubId:rt,slot:1117381},"mpu6-m":{zone:nt,pubId:rt,slot:1117381},"mpu7-m":{zone:nt,pubId:rt,slot:1117381},"mpu8-m":{zone:nt,pubId:rt,slot:1117381},"mpu9-m":{zone:nt,pubId:rt,slot:1117381},"mpu10-m":{zone:nt,pubId:rt,slot:1117381}},ot={bidderName:tt,params:function(e){var t=e.id,n=it[t];return n?{bidder:tt,params:et({zone:n.zone},it[t].pubId&&{pubId:it[t].pubId},it[t].slot&&{slot:it[t].slot},it[t].product&&{product:it[t].product})}:null}},at={billboard:{siteId:1112950},siderail1:{siteId:1112951},siderail2:{siteId:1112952},mpu1:{siteId:1112953},mpu2:{siteId:1112954},mpu3:{siteId:1112956},mpu4:{siteId:1112957},mpu5:{siteId:1112959},mpu6:{siteId:1112960},mpu7:{siteId:1112961},mpu8:{siteId:1112962},mpu9:{siteId:1112963},mpu10:{siteId:1112964},bottom_banner:{siteId:1112966},"mpu1-m":{siteId:1112967},"mpu2-m":{siteId:1112968},"mpu3-m":{siteId:1112970},"mpu4-m":{siteId:1112971},"mpu5-m":{siteId:1112973},"mpu6-m":{siteId:1112974},"mpu7-m":{siteId:1112975},"mpu8-m":{siteId:1112976},"mpu9-m":{siteId:1112977},"mpu10-m":{siteId:1112978}},ut={bidderName:"ix",bidderAliases:"indexExchange",params:function(e){var t=e.id,n=at[t];return n?{bidder:"ix",params:{siteId:n.siteId}}:null}},st="invibes",ct="https://prebid.videostep.com/Bid/VideoAdContent",lt={mpu2:{placementId:"invibes_buzzfeed_mpu2d_t2",customEndpoint:ct,customEndpointRichMedia:ct},mpu4:{placementId:"invibes_buzzfeed_mpu4d_t2",customEndpoint:ct,customEndpointRichMedia:ct},"mpu2-m":{placementId:"invibes_buzzfeed_mpu2m_t2",customEndpoint:ct,customEndpointRichMedia:ct},"mpu3-m":{placementId:"invibes_buzzfeed_mpu3",customEndpoint:ct},"mpu4-m":{placementId:"invibes_buzzfeed_mpu4m_t2",customEndpoint:ct,customEndpointRichMedia:ct},"mpu5-m":{placementId:"invibes_buzzfeed_mpu5",customEndpoint:ct},"mpu6-m":{placementId:"invibes_buzzfeed_mpu6",customEndpoint:ct},"mpu7-m":{placementId:"invibes_buzzfeed_mpu7",customEndpoint:ct},"mpu8-m":{placementId:"invibes_buzzfeed_mpu8",customEndpoint:ct},"mpu9-m":{placementId:"invibes_buzzfeed_mpu9",customEndpoint:ct},"mpu10-m":{placementId:"invibes_buzzfeed_mpu10",customEndpoint:ct}},dt={bidderName:st,params:function(e){var t=e.id,n=lt[t];return n?{bidder:st,params:{placementId:n.placementId,customEndpoint:n.customEndpoint}}:null}};function ft(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function pt(e){return function(e){if(Array.isArray(e))return ft(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return ft(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ft(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var mt="kargo",gt={billboard:{placementId:"_llbABCTo3n"},siderail1:{placementId:"_v9e2nmbOms"},siderail2:{placementId:"_v9e2nmbOms"},mpu1:{placementId:"_hAOkg4MFlk"},mpu2:{placementId:"_jbnBCIYtLH"},mpu3:{placementId:"_hAOkg4MFlk"},mpu4:{placementId:"_jbnBCIYtLH"},mpu5:{placementId:"_hAOkg4MFlk"},mpu6:{placementId:"_hAOkg4MFlk"},mpu7:{placementId:"_hAOkg4MFlk"},mpu8:{placementId:"_hAOkg4MFlk"},mpu9:{placementId:"_hAOkg4MFlk"},mpu10:{placementId:"_hAOkg4MFlk"},bottom_banner:{placementId:"_hqW38NeZg8"},"mpu1-m":{placementId:"_o2odIodQMo"},"mpu2-m":{placementId:"_f6Ewtl8wGh"},"mpu3-m":{placementId:"_o2odIodQMo"},"mpu4-m":{placementId:"_f6Ewtl8wGh"},"mpu5-m":{placementId:"_o2odIodQMo"},"mpu6-m":{placementId:"_o2odIodQMo"},"mpu7-m":{placementId:"_o2odIodQMo"},"mpu8-m":{placementId:"_o2odIodQMo"},"mpu9-m":{placementId:"_o2odIodQMo"},"mpu10-m":{placementId:"_o2odIodQMo"}},vt={bidderName:mt,params:function(e){var t=e.id,n=gt[t];return pt(n?[{bidder:mt,params:n}]:[])}},bt="rubicon",ht={billboard:{siteId:549646,zoneId:3430730},siderail1:{siteId:549646,zoneId:3430734},siderail2:{siteId:549646,zoneId:3430736},mpu1:{siteId:549646,zoneId:3430738},mpu2:{siteId:549646,zoneId:3430742},mpu3:{siteId:549646,zoneId:3430744},mpu4:{siteId:549646,zoneId:3430750},mpu5:{siteId:549646,zoneId:3430752},mpu6:{siteId:549646,zoneId:3430754},mpu7:{siteId:549646,zoneId:3430756},mpu8:{siteId:549646,zoneId:3430758},mpu9:{siteId:549646,zoneId:3430760},mpu10:{siteId:549646,zoneId:3430762},bottom_banner:{siteId:549648,zoneId:3430768},"mpu1-m":{siteId:549648,zoneId:3430772},"mpu2-m":{siteId:549648,zoneId:3430794},"mpu3-m":{siteId:549648,zoneId:3430776},"mpu4-m":{siteId:549648,zoneId:3430796},"mpu5-m":{siteId:549648,zoneId:3430780},"mpu6-m":{siteId:549648,zoneId:3430782},"mpu7-m":{siteId:549648,zoneId:3430784},"mpu8-m":{siteId:549648,zoneId:3430786},"mpu9-m":{siteId:549648,zoneId:3430790},"mpu10-m":{siteId:549648,zoneId:3430792}},yt={bidderName:bt,params:function(e){var t=e.id,n=ht[t];return n?{bidder:bt,params:{zoneId:n.zoneId,accountId:17292,siteId:n.siteId}}:null}},wt=function(e){return e.map((function(e){return e.label}))},_t=function(){var e=[];try{var t=localStorage.getItem("_pdfps");e=t?JSON.parse(t):[]}catch(l){}if(!window.JSGlobals)return{permutive:e};var n,r=JSGlobals.pageId,i=JSGlobals.topictags,o=JSGlobals.pageType,a=JSGlobals.isLiveBlog,u=JSGlobals.isCommercial,s=JSGlobals.mantis_channels,c=JSGlobals.mantis_context;return{mantis:(null===s||void 0===s?void 0:s.ratings)&&(n=s.ratings,n.map((function(e){var t=e.customer,n=e.rating;return"".concat(t,"-").concat(n)}))),mantis_context:c&&wt(c),topictags:i,pageType:o,liveblog:a?"y":"n",commercial:u?"y":"n",article:r,permutive:e}};function St(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function At(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){St(e,t,n[t])}))}return e}var It="ozone",Pt={billboard:{placementId:1500000459},siderail1:{placementId:3500015215},siderail2:{placementId:3500015216},mpu1:{placementId:3500015217},mpu2:{placementId:3500015241},mpu3:{placementId:3500015219},mpu4:{placementId:3500015242},mpu5:{placementId:3500015221},mpu6:{placementId:3500015222},mpu7:{placementId:3500015223},mpu8:{placementId:3500015224},mpu9:{placementId:3500015225},mpu10:{placementId:3500015226},bottom_banner:{placementId:3500015228},"mpu1-m":{placementId:3500015231},"mpu2-m":{placementId:3500015243},"mpu3-m":{placementId:3500015233},"mpu4-m":{placementId:3500015244},"mou5-m":{placementId:3500015235},"mpu5-m":{placementId:3500015235},"mpu6-m":{placementId:3500015236},"mpu7-m":{placementId:3500015237},"mpu8-m":{placementId:3500015238},"mpu9-m":{placementId:3500015239},"mpu10-m":{placementId:3500015240}},zt=_t(),Ot=function(){var e=JSON.parse(localStorage.getItem("_psegs")||"[]").map(Number).filter((function(e){return e>=1e6})).map(String),t=JSON.parse(window.localStorage._ppam||"[]"),n=JSON.parse(window.localStorage._pcrprs||"[]");return e.concat(t).concat(n)};function Ct(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function jt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ct(e,t,n[t])}))}return e}var kt="pubmatic",Et={billboard:{adSlot:"buzz_billboard"},siderail1:{adSlot:"buzz_siderail1"},siderail2:{adSlot:"buzz_siderail2"},mpu1:{adSlot:"buzz_mpu1_d"},mpu2:{adSlot:"buzz_mpu2_d"},mpu3:{adSlot:"buzz_mpu3_d"},mpu4:{adSlot:"buzz_mpu4_d"},mpu5:{adSlot:"buzz_mpu5_d"},mpu6:{adSlot:"buzz_mpu6_d"},mpu7:{adSlot:"buzz_mpu7_d"},mpu8:{adSlot:"buzz_mpu8_d"},mpu9:{adSlot:"buzz_mpu9_d"},mpu10:{adSlot:"buzz_mpu10_d"},bottom_banner:{adSlot:"buzz_sticky_footer"},"mpu1-m":{adSlot:"buzz_mpu1_m"},"mpu2-m":{adSlot:"buzz_mpu2_m"},"mpu3-m":{adSlot:"buzz_mpu3_m"},"mpu4-m":{adSlot:"buzz_mpu4_m"},"mpu5-m":{adSlot:"buzz_mpu5_m"},"mpu6-m":{adSlot:"buzz_mpu6_m"},"mpu7-m":{adSlot:"buzz_mpu7_m"},"mpu8-m":{adSlot:"buzz_mpu8_m"},"mpu9-m":{adSlot:"buzz_mpu9_m"},"mpu10-m":{adSlot:"buzz_mpu10_m"}},Mt={bidderName:kt,params:function(e){var t=e.id,n=_t(),r=Object.keys(n).reduce((function(e,t){var r="".concat(t,"=").concat(JSON.stringify(n[t]));return 0===e.length?r:"".concat(e,"|").concat(r)}),""),i=Et[t];return i?{bidder:kt,params:{publisherId:"160736",adSlot:i.adSlot,keywords:jt({p_standard:Ot()},n),dctr:r}}:null}},Tt="teads",xt={billboard:{pageId:209392,placementId:225745},siderail1:{pageId:209391,placementId:225744},siderail2:{pageId:209390,placementId:225743},mpu2:{pageId:209396,placementId:225749},mpu4:{pageId:209395,placementId:225748},bottom_banner:{pageId:209379,placementId:225732},"mpu1-m":{pageId:209378,placementId:225731},"mpu2-m":{pageId:209394,placementId:225747},"mpu3-m":{pageId:209376,placementId:225729},"mpu4-m":{pageId:209393,placementId:225746},"mou5-m":{pageId:209374,placementId:225727},"mpu5-m":{pageId:209374,placementId:225727},"mpu6-m":{pageId:209373,placementId:225726},"mpu7-m":{pageId:209372,placementId:225725},"mpu8-m":{pageId:209371,placementId:225724},"mpu9-m":{pageId:209370,placementId:225723},"mpu10-m":{pageId:209369,placementId:225722}};function Rt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Nt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Rt(e,t,n[t])}))}return e}var Bt,Ut="appnexus",Dt={billboard:{placementId:32889274},siderail1:{placementId:32889275},siderail2:{placementId:32889276},mpu1:{placementId:32889277},mpu2:{placementId:32889480,placementIdRichMedia:32889480},mpu3:{placementId:32889279},mpu4:{placementId:32889479,placementIdRichMedia:32889479},mpu5:{placementId:32889282},mpu6:{placementId:32889283},mpu7:{placementId:32889284},mpu8:{placementId:32889285},mpu9:{placementId:32889286},mpu10:{placementId:32889287},bottom_banner:{placementId:32889290},"mpu1-m":{placementId:32889291},"mpu2-m":{placementId:32889479,placementIdRichMedia:32889479},"mpu3-m":{placementId:32889293},"mpu4-m":{placementId:32889511,placementIdRichMedia:32889511},"mpu5-m":{placementId:32889295},"mpu6-m":{placementId:32889297},"mpu7-m":{placementId:32889298},"mpu8-m":{placementId:32889299},"mpu9-m":{placementId:32889300}},Lt={bidderName:Ut,params:function(e){var t=e.id,n=_t(),r=Dt[t];return r?{bidder:Ut,params:{placementId:parseInt(r.placementId),keywords:Nt({p_standard:Ot()},n)}}:null}},Ft=[ot,ut,dt,vt,{bidderName:It,params:function(e){var t=e.id,n=e.video,r=Pt[t];return r?{bidder:It,params:At({publisherId:"OZONEESI0001",siteId:"1500000424",placementId:r.placementId,customData:[{targeting:At({},zt,{tile:t,permutiveId:localStorage.getItem("permutive-id")})}]},n?{video:{skippable:!0,playback_method:["auto_play_sound_off"]}}:{})}:null}},Mt,yt,{bidderName:Tt,params:function(e){var t=e.id,n=xt[t];return n?{bidder:Tt,params:n}:null}},Lt],Ht={buckets:[{max:3,increment:.02},{max:5,increment:.05},{max:20,increment:.5}]},Zt=[{id:"mpu1-m",sizes:Xe.MPU1_M},{id:"mpu2-m",sizes:Xe.MPU2_M},{id:"mpu3-m",sizes:Xe.MPU3_M},{id:"mpu4-m",sizes:Xe.MPU4_M},{id:"mpu5-m",sizes:Xe.MPU5_M},{id:"mpu6-m",sizes:Xe.MPU6_M},{id:"mpu7-m",sizes:Xe.MPU7_M},{id:"mpu8-m",sizes:Xe.MPU8_M},{id:"mpu9-m",sizes:Xe.MPU9_M},{id:"mpu10-m",sizes:Xe.MPU10_M},{id:"bottom_banner",sizes:Xe.BOTTOM_BANNER},{id:"billboard",sizes:Xe.BILLBOARD},{id:"skin",sizes:Xe.SKIN},{id:"mpu1",sizes:Xe.MPU1},{id:"mpu2",sizes:Xe.MPU2},{id:"mpu3",sizes:Xe.MPU3},{id:"mpu4",sizes:Xe.MPU4},{id:"mpu5",sizes:Xe.MPU5},{id:"mpu6",sizes:Xe.MPU6},{id:"mpu7",sizes:Xe.MPU7},{id:"mpu8",sizes:Xe.MPU8},{id:"mpu9",sizes:Xe.MPU9},{id:"mpu10",sizes:Xe.MPU10},{id:"siderail1",sizes:Xe.SIDERAIL1},{id:"siderail2",sizes:Xe.SIDERAIL2}];function qt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Jt(e){return function(e){if(Array.isArray(e))return qt(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return qt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return qt(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Gt=function(){if("undefined"===typeof Bt){var e,t=new URLSearchParams(null===window||void 0===window||null===(e=window.location)||void 0===e?void 0:e.search);Bt=t.has("__DEBUG__")}return Bt},$t=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i=Gt();i&&(r=console).log.apply(r,Jt(t))};function Qt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Kt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Vt(e){return function(e){if(Array.isArray(e))return Qt(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return Qt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Qt(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Wt=function(e){return e.map((function(e){return{minViewPort:[e.from,0],sizes:e.supportedSizes}}))},Xt=function(e,t){var n=e.map((function(e){var n=e.id,r=e.sizes,i=e.video,o={};o=["mpu2","mpu4","mpu2-m","mpu4-m"].includes(n)&&i?{video:i,banner:{sizeConfig:Wt(r)}}:i?{video:i}:{banner:{sizeConfig:Wt(r)}};var a=function(e){var t=e.id;return e.bidders.map((function(e){return e.params?e.params({id:t}):null})).map((function(e){return e?(e.params?e.params.keywords=_t():e.params={keywords:_t()},Array.isArray(e)&&(e=e.map((function(e){return e.params?e.params.keywords=_t():e.params={keywords:_t()},e}))),e):null})).filter((function(e){return e})).reduce((function(e,t){return Array.isArray(t)?Vt(e).concat(Vt(t)):Vt(e).concat([t])}),[])}({id:n,bidders:t});return{code:n,mediaTypes:o,bids:a}}));return $t("prebidAdConfig: ",n),n},Yt=!1,en=function(){return new Promise((function(e){if(Yt)e();else{Yt=!0;var t=window.pbjs||{};t.que=t.que||[],t.que.push((function(){t.setConfig({appnexusAuctionKeywords:{perid:localStorage.getItem("cohort_ids")?JSON.parse(localStorage.getItem("cohort_ids")):[]},useBidCache:!0,bidCacheFilterFunction:function(e){return"video"!==e.mediaType},realTimeData:{auctionDelay:200,dataProviders:[{name:"permutive",waitForIt:!0,params:{acBidders:["appnexus","pubmatic"]}},{name:"brandmetrics",waitForIt:!0,params:{bidders:["ozone"]}}]},fledgeForGpt:{enabled:!0,defaultForSlots:1},consentManagement:{usp:{cmpApi:"iab",timeout:50},gdpr:{cmpApi:"iab",defaultGdprScope:!0,timeout:6e3},rules:[{purpose:"storage",enforcePurpose:!0,enforceVendor:!0,vendorExceptions:["permutive"]},{purpose:"basicAds",enforcePurpose:!0,enforceVendor:!0,vendorExceptions:[]}]},currency:{adServerCurrency:"USD",granularityMultiplier:1},priceGranularity:Ht,userSync:{userIds:[{name:"teadsId",params:{pubId:5668}}],filterSettings:{iframe:{bidders:Ft.map((function(e){return e.bidderName})),filter:"include"}}}}),t.setBidderConfig({bidders:["appnexus"],config:{ortb2:{user:{keywords:localStorage.getItem("cohort_ids")?JSON.parse(localStorage.getItem("cohort_ids")).map((function(e){return"perid=".concat(e)})).join(", "):[]}}}},!0),t.setBidderConfig({bidders:["criteo","smartadserver"],config:{ortb2:{user:{data:[{name:"anonymised.io",ext:{segtax:1e3},segment:localStorage.getItem("cohort_ids")?JSON.parse(localStorage.getItem("cohort_ids")).map((function(e){return{id:e}})):[]}]}}}},!0),t.bidderSettings=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Kt(e,t,n[t])}))}return e}({},t.bidderSettings,{grid:{bidCpmAdjustment:function(e,t){return.7*e}},adagio:{storageAllowed:!0}});var n=Xt(Zt,Ft);t.addAdUnits(n),e()}))}}))},tn=!1,nn=function(){return new Promise((function(e,t){tn&&e(),tn=!0;var n=document.createElement("script");n.type="text/javascript",n.innerHTML='\n    !(function () {\n      var a = "pbjs";\n      var r = (window[a] = window[a] || {});\n      if (((r.que = r.que || []), !r.version)) {\n        var t = "https://floor.pbxai.com/?pubxId=b5a8c20e-7642-44b9-82a5-8c896cbfc109&page='.concat(window.location.href,'",\n          n = document.createElement("link");\n        (n.rel = "preload"), (n.href = t), (n.as = "fetch"), (n.crossOrigin = !0), document.head.appendChild(n);\n      }\n      try {\n        var o = localStorage.getItem("pubx:defaults"), l = o ? JSON.parse(o).data : null;\n      } catch (i) {\n        console.error("Pubx: Error parsing defaults", i), (l = null);\n      }\n      r.que.push(function () {\n        r.setConfig({ floors: { enforcement: { floorDeals: !0 }, auctionDelay: 100, endpoint: { url: t }, data: l } });\n      });\n      var p = document.createElement("script"), s = "https://cdn.pbxai.com/b5a8c20e-7642-44b9-82a5-8c896cbfc109.js?pbxd=').concat(encodeURIComponent(window.location.origin),'";\n      (p.src = s), (p.async = !0), document.head.appendChild(p);\n    })();\n  '),document.head.appendChild(n),e()}))},rn=n(75951);function on(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var an,un="3579",sn={loadScript:v.v},cn=new b.BH,ln=function(){function e(t){var n=t.env,r=t.localization,i=t.gdpr;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._env=n,this._localization=r,this._gdpr=i}var t,n,r;return t=e,(n=[{key:"init",value:function(){return an||(an=this._loadLib().then((function(){return cn})).then((function(){return Promise.all([en(),(apstag.init({pubID:"3579",adServer:"googletag",videoAdServer:"DFP",deals:!0}),void $t("APS init"))])})).then((function(){(0,c.A)("info","prebid","IDNML > Header bidding initialized")})))}},{key:"_loadLib",value:function(){(0,c.A)("info","prebid","IDNML > Load header bidding...");var e=this._env.webRoot||"http://www.buzzfeed.com",t=(0,qe.jH)(window.location.search),n=t["idnml-aps"]||"release",r=t["idnml-pbjs"]||"release";return rn.jQ.hasConsented("amazon").then((function(t){if((0,c.A)("info","prebid","IDNML > Amazon consent:",t),t)return Promise.all([nn(),sn.loadScript("".concat(e,"/static-assets/npm/adlib/").concat(n,"/idnml/aps.js")),sn.loadScript("".concat(e,"/static-assets/npm/adlib/").concat(r,"/idnml/prebid.js")),ue(un),ae(un)]).then((function(){return window.pbjs=window.pbjs||{},window.pbjs.que=window.pbjs.que||[],new Promise((function(e){window.pbjs.que.push((function(){cn.resolve(window.pbjs),e()}))}))}));throw new h.x9("Amazon Signal is disabled")}))}}])&&on(t.prototype,n),r&&on(t,r),e}(),dn=n(94776),fn=n.n(dn);function pn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function mn(e){return function(e){if(Array.isArray(e))return pn(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return pn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return pn(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var gn=function(e,t){return e.map((function(e){return{slotID:e.id,sizes:mn(new Set(Zt.find((function(t){return t.id===e.id})).sizes.flatMap((function(e){return e.supportedSizes})))),slotName:t}}))};function vn(e,t,n,r,i,o,a){try{var u=e[o](a),s=u.value}catch(c){return void n(c)}u.done?t(s):Promise.resolve(s).then(r,i)}var bn=function(e,t){return null===t||void 0===t?void 0:t.filter((function(t){return e.find((function(e){return e.id===t.id}))}))},hn=function(){var e,t=(e=fn().mark((function e(t,n){var r,i,o;return fn().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((r=bn(Zt,t)).length){e.next=3;break}return e.abrupt("return");case 3:return i=new Promise((function(e){var t=gn(r,n);$t("APS slots:",t),apstag.fetchBids({slots:t},(function(t){$t("APS bids:",t),googletag.cmd.push((function(){null===apstag||void 0===apstag||apstag.setDisplayBids(),e()}))}))})),o=new Promise((function(e){var t=r.map((function(e){return e.id})),n=window.pbjs||{};n.que=n.que||[],n.que.push((function(){n.requestBids({labels:["DISPLAY"],timeout:1500,adUnitCodes:t,bidsBackHandler:function(){r.forEach((function(e){n.setTargetingForGPTAsync([e.id])})),e()}}),$t("pbjs:","request sent")}))})),e.next=7,Promise.race([Promise.all([o,i]),new Promise((function(e){return setTimeout(e,2e3)}))]);case 7:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){vn(o,r,i,a,u,"next",e)}function u(e){vn(o,r,i,a,u,"throw",e)}a(void 0)}))});return function(e,n){return t.apply(this,arguments)}}(),yn=["prebid"],wn=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"prebid",t=arguments.length>1?arguments[1]:void 0;return"prebid"===e?new ln(t):null},_n=function(){return[{requestBid:function(e,t){var n=Ze(t).ukPos;return hn([{id:n}],t.path)}}]};var Sn={buildAdCall:function(e,t){var n=t.env,r=Ze(e);if(r.disableUnit)return"/DISABLE/";var i="tasty"===n.destination?"_main_tasty":"_main_buzzfeed",o="/".concat(Le,"/").concat(i,"/").concat(r.ukPos);return(0,c.A)("info","adCall","geo-uk: buildAdCall",o,e.adPos),o},getCommonTargeting:function(e){var t=e.env,n=e.abeagle,r=e.gdpr;return m({env:t,abeagle:n,gdpr:r}).then((function(e){e.category1=(0,l.jP)({},{env:t}),e.category2=((null===e||void 0===e?void 0:e.section)||[]).filter((function(t){return t!==e.category1}));var n=function(){var e;return(0,qe.jH)(null===window||void 0===window||null===(e=window.location)||void 0===e?void 0:e.search)["campaign-override"]||""}();return n&&(e.campaign_override=n),e}))},getNewConfig:Ze,getSlotContainerId:function(e){var t=Me.Z.isAny(["xs","sm"])?"mobileweb":"desktop";if(e=parseInt(e,10),Be[e]){var n="".concat(Be[e]);return"mobileweb"===t&&n.includes("mpu")?n+"-m":n}return d(e)},Prebid:i,useService:function(e){return"doubleverify"===e?"unity":""}},An=["prebid","amazon"],In="https://micro.rubiconproject.com/prebid/dynamic/22508.js",Pn=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"prebid",t=arguments.length>1?arguments[1]:void 0;return"bfj_prebid"===e?((0,c.A)("info","prebid","BFJ using *NEW* BZFD Prebid Stack (Demand Manager)"),new K(t,{accountSrc:In})):"prebid"===e?((0,c.A)("info","prebid","BFJ using DEFAULT BZFD Prebid Stack (Demand Manager)"),new K(t)):"amazon"===e?new _e(t):null},zn=function(e){return[new V(e),new Ae(e)]},On={buildAdCall:function(e,t){var n=f(e,t);return e.advertiserContext&&!e.useNewAdCallStructure&&(n=n.replace("partnerpost","ja.partnerpost")),n},Prebid:o,debugName:"Stack - BFJ"},Cn=(a.Eh,a.d_,a.nj,{bzfd:je,"geo-au":Ee,"geo-nz":Ee,"geo-gb":Sn,"geo-uk":Sn,"geo-ie":Sn,"ed-ja-ja":On,"ed-ja-jp":On});function jn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e=e.toLowerCase(),a.ED.includes(e)}function kn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e=e.toLowerCase(),a.L.includes(e)}function En(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"us",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n="bzfd";return kn(t)?n="ed-".concat(t):jn(e)&&(n="geo-".concat(e)),n}function Mn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"bzfd",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=e.toLowerCase();var n=Cn[e];return n&&n.getNewConfig?n.getNewConfig(t):t}function Tn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"us",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=En(e.toLowerCase(),t),r=Cn[n];return(0,c.A)("info","prebid","ads > getPrebidStack",n),r&&r.Prebid?r.Prebid:je.Prebid}function xn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"us",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=e.toLowerCase(),r=En(n,t),i=Cn[r];return i&&i.getSlotContainerId?i.getSlotContainerId:je.getSlotContainerId}function Rn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"us",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=t.toLowerCase(),i=En(r,n),o=Cn[i];return o&&o.useService?o.useService(e):je.useService(e)}function Nn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"us",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=e.toLowerCase(),r=En(n,t),i=Cn[r];return i&&i.getCommonTargeting?i.getCommonTargeting:je.getCommonTargeting}},40026:function(e,t,n){"use strict";n.d(t,{nj:function(){return u},L:function(){return i},ED:function(){return r},Eh:function(){return o},d_:function(){return a}});var r=["gb","uk","ie","au","nz"],i=["ja-ja","ja-jp","jp-ja","jp-jp","ja","jp"],o=["geo-gb","geo-uk","geo-ie"],a=["geo-au","geo-nz"],u=["ed-ja-ja","ed-ja-jp","ed-jp-ja","ed-jp-jp","ed-ja","ed-jp"]},41559:function(e,t,n){"use strict";n.d(t,{x:function(){return o}});n(76635);var r="track/website/instrumentation";var i=["samplingRate"];function o(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if("function"===typeof window.bfa){i.some((function(e){return n.hasOwnProperty(e)}))&&(o=n,n={});var a=o.samplingRate,u=o.platform,s={data:{target:e,value:t,tags:n}};u&&(s.data.platform=o.platform),a&&(a>1?"dev"===window.BZFD.Config.env&&console.error("Your sampling rate is above 100%."):Object.assign(s,{opts:{samplingRate:a}})),window.bfa(r,s)}else"undefined"!==typeof window.raven&&window.raven.captureException(new Error("Instrumentation tracking issue: BFA is not available"))}},43790:function(e,t){"use strict";t.Z={choice:function(e){return e&&e[Math.floor(Math.random()*e.length)]}}},60736:function(e,t,n){"use strict";var r,i=n(76635),o=window,a={xs:"(max-width:39.9rem)",sm:"(min-width:40rem) and (max-width:51.9rem)",md:"(min-width:52rem) and (max-width:63.9rem)",lg:"(min-width:64rem)"};function u(e){return"screen and ".concat(a[e])}var s={observers:new Set,init:function(){var e=this;"matchMedia"in o&&Object.keys(a).forEach((function(t){var n=e,r=o.matchMedia(u(t));r.addListener((function(e){e.matches&&n.observers.forEach((function(e){e.fire(t),e.fire("match",{breakpoint:t})}))})),e[t]=r}))}};s.init(),r={_getWindowWidth:function(){return Number(o.innerWidth||0)},_isRetinaDevice:function(){return"matchMedia"in o&&o.matchMedia("(-webkit-min-device-pixel-ratio: 2)").matches},_makeSizeMediaQuery:u,_mediaMatches:function(e){return e in a&&s[e].matches},isBreakpoint:function(e){return r._mediaMatches(e)},isXsmall:function(){return r._mediaMatches("xs")},isSmall:function(){return r._mediaMatches("sm")},isMedium:function(){return r._mediaMatches("md")},isLarge:function(){return r._mediaMatches("lg")},isAny:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return(0,i.some)(e,r._mediaMatches)},isSmallerThan:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.inclusive,i=void 0===n||n;return"xs"===e?!!i&&r.isXsmall():"sm"===e?i?r.isAny(["xs","sm"]):r.isXsmall():"md"===e?i?r.isAny(["xs","sm","md"]):r.isAny(["xs","sm"]):"lg"===e&&(!!i||r.isAny(["xs","sm","md"]))},isLargerThan:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.inclusive,i=void 0===n||n;return"xs"===e?!!i||r.isAny(["sm","md","lg"]):"sm"===e?i?r.isAny(["sm","md","lg"]):r.isAny(["md","lg"]):"md"===e?i?r.isAny(["md","lg"]):r.isLarge():"lg"===e&&(!!i&&r.isLarge())},breakPoint:function(e){return r._mediaMatches(e)},getBreakPoint:function(){var e;for(var t in a)if(r.breakPoint(t)){e=t;break}return e},getWindowWidth:function(){return r._isRetinaDevice()?2*r._getWindowWidth():r._getWindowWidth()},get cssBreakpoints(){return{xs:null,sm:"40rem",md:"52rem",lg:"64rem"}},breakpointObserver:{subscribe:function(e){return s.observers.add(e)},unsubscribe:function(e){return s.observers.delete(e)}}},t.Z=r},34686:function(e,t,n){"use strict";n.d(t,{MA:function(){return m},Ul:function(){return b},BH:function(){return h}});var r=n(76635),i=n(41559);function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e){return a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},a(e)}function u(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&l(e,t)}function s(e,t){return null!=t&&"undefined"!==typeof Symbol&&t[Symbol.hasInstance]?t[Symbol.hasInstance](e):e instanceof t}function c(e,t){return!t||"object"!==d(t)&&"function"!==typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function l(e,t){return l=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},l(e,t)}var d=function(e){return e&&"undefined"!==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};function f(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=a(e);if(t){var i=a(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return c(this,n)}}var p={};function m(e,t){return e+"-"+t.id}p.getKeys=function(e){var t;if(Object.keys)return Object.keys(e);for(var n in t=[],e)e.hasOwnProperty(n)&&t.push(n);return t},p.getUniqueEventName=m,p.capitalize=function(e){return"string"!==typeof e?e:e.replace(/\w+/g,(function(e){return e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()}))},p.hasQueryString=function(e){return e.indexOf("?")>-1},p.removeHash=function(e){return(e+="").indexOf("#")>-1?e.substr(0,e.indexOf("#")):e},p.removeQueryString=function(e){return(e+="").indexOf("?")>-1?-1===e.indexOf("#")?e.substr(0,e.indexOf("?")):e.substr(0,e.indexOf("?"))+e.substr("#",e.length):e},p.getQueryString=function(e){var t="";return(e=p.removeHash(e)).indexOf("?")>-1&&(t=e.substr(e.indexOf("?"),e.length)),t},p.getHash=function(e){return(e+="").indexOf("#")>-1?e.substr(e.indexOf("#"),e.length):""},p.queryStringToObject=function(e){if(""===e||void 0===e||null===e)return{};var t,n=e.split("&"),r={};return p.each(n,(function(e,n){""===(t=n.split("="))[1]&&(t[1]=null),r[t[0]]=t[1]})),r},p.objectToQueryString=function(e){var t=[];return p.each(e,(function(e,n){t.push(e+"="+n)})),"?"+t.join("&")},p.getBaseUrl=function(e){return e=p.removeHash(e),e=p.removeQueryString(e)},p.addParams=function(e,t){var n,r,i=p.getQueryString(e),o=p.getHash(e);return e=p.removeHash(e),e=p.removeQueryString(e),i=p.queryStringToObject(i.substr(1,i.length)),p.each(t,(function(e,t){n=t[0],r=t[1],i[n]=r})),e+p.objectToQueryString(i)+o},p.removeParams=function(e,t){var n=p.getBaseUrl(e),r=p.getQueryString(e),i=p.getHash(e),o=p.queryStringToObject(r.substr(1,r.length));return p.each(t,(function(e,t){delete o[t]})),n+p.objectToQueryString(o)+i},p.prepareUrl=function(e,t){var n=[];return p.each(t,(function(e,t){"undefined"!==typeof t&&n.push([e,encodeURIComponent(t)])})),p.addParams(e,n)},p.redirect=function(e){"string"===typeof e&&e&&(window.location.href=e)},p.openPopup=function(e,t){if("string"===typeof e&&e){var n="_blank",r="";if(t&&t.height&&t.width){var i=window.screen.height/2-t.height/2,o=window.screen.width/2-t.width/2;r="scrollbars=yes, toolbar=0, status=0, width="+t.width+", height="+t.height+", top="+i+", left="+o,n=t.name?t.name:n}window.open(e,n,r)}},p.urlToId=function(e){var t=e;try{t=new URL(e).pathname}catch(n){}return"/"===t.charAt(0)&&(t=t.slice(1,t.length)),t.replace(/\//g,"_")},p.each=function(e,t){if(Array.isArray(e))e.forEach((function(e,n){return t(n,e)}));else for(var n in e)e.hasOwnProperty(n)&&t(n,e[n])},p.extend=r.merge,p.getQueryParams=function(){var e=window.location.search.substring(1),t={};return e&&e.split("&").forEach((function(e){var n=e.split("="),r=decodeURIComponent(n[0]),i=decodeURIComponent(n.slice(1).join("="));t[r]=i})),t},p.addQueryParam=function(e,t,n){return(e=e||window.location.href)+(e.split("?")[1]?"&":"?")+t+"="+n},p.largeNumberNotation=function(e){return e<1e3?e:e<1e4?e.toString().charAt(0)+","+e.toString().substring(1):e>=1e6?(e/1e6).toFixed(e%1e6!==0)+"M":(e/1e3).toFixed(e%1e3!==0)+"K"},p.numberWithCommas=function(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")},p.min=function(){return Math.min.apply(Math,Array.prototype.filter.call(arguments,(function(e){return!isNaN(+e)})))},p.toQueryString=function(e){var t=[];return this.each(e,(function(e,n){null!==n&&""!==n&&"undefined"!==typeof n&&t.push(e+"="+n)})),t.join("&")},p.getMessageHandler=function(e){return function(t,n){var r=e[t];"function"===typeof r&&r.call(this,n)}},p.getEventHandler=function(e){return function(t,n,r){var i=e[r];"function"===typeof i&&i.call(this,t,n)}},p.padLeft=function(e,t,n){for(var r=n-e.toString().length,i="",o=0;o<r;o++)i+=t;return i+e},p.freeFormFormat=function(e){return e.replace(/["'"\u201c\u2019 ]/g,"").toLowerCase()},p.secondsToTimeStamp=function(e){var t=new Date(1e3*e).toISOString().substr(11,8);return e<3600&&(t=t.substr(3,5)),t.replace(/^0+(\d:)/,"$1")},p.timeSince=function(e){var t,n=["year","years"],r=["month","months"],i=["day","days"],o=["hour","hours"],a=["minute","minutes"],u=["just now"],s=Math.floor(((new Date).getTime()-new Date(e).getTime())/1e3);return(t=Math.floor(s/31536e3))>=1?{type:"year",value:t,text:t+" "+(1===t?n[0]:n[1])+" ago"}:(t=Math.floor(s/2592e3))>=1?{type:"month",value:t,text:t+" "+(1===t?r[0]:r[1])+" ago"}:(t=Math.floor(s/86400))>=1?{type:"day",value:t,text:t+" "+(1===t?i[0]:i[1])+" ago"}:(t=Math.floor(s/3600))>=1?{type:"hour",value:t,text:t+" "+(1===t?o[0]:o[1])+" ago"}:(t=Math.floor(s/60))>1?{type:"minute",value:t,text:t+" "+(1===t?a[0]:a[1])+" ago"}:{type:"now",value:"",text:u[0]}},p.bulkDelete=function(e,t){p.each(e,(function(e,n){delete t[n]}))},p.bfaTrack=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};window.bfa&&s(window.bfa,Function)&&window.bfaBinder&&window.bfa(e,t)},p.getScreenOrientation=function(){if(screen.orientation&&screen.orientation.type)return screen.orientation.type.replace("-primary","");if(window.orientation)return 90===Math.abs(window.orientation)?"landscape":"portrait";if(window.matchMedia){if(window.matchMedia("(orientation: portrait)").matches)return"portrait";if(window.matchMedia("(orientation: landscape)").matches)return"landscape"}return"landscape"},p.isIOSIPad="undefined"!==typeof navigator&&!!navigator.userAgent.toLowerCase().match(/ipad/),p.isIOSMobile="undefined"!==typeof navigator&&!!navigator.userAgent.toLowerCase().match(/iphone|ipod/);var g=p.isIOSIPad||p.isIOSMobile;p.isIOS=g;var v=/constructor/i.test(window.HTMLElement)||"[object SafariRemoteNotification]"===(!window.safari||"undefined"!==typeof safari&&safari.pushNotification).toString();function b(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=e;return function(){return t++}}p.isSafari=v,p.createScript=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.head,n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return new Promise((function(o,a){var u=document.createElement("script");u.onload=function(){return o(u)},u.onerror=function(e){(0,i.x)("script","error",{url:(0,r.get)(e,"currentTarget.src","").split("?")[0]}),a("Script failed to load")},u.src=e,u.async=n,t.appendChild(u)}))},p.createStyleSheet=function(e){return new Promise((function(t,n){var r=document.createElement("link");r.rel="stylesheet",r.href=e,r.onload=function(){return t(r)},r.onerror=function(){return n("".concat(e," failed to load"))},document.head.appendChild(r)}))},p.stripHTML=function(e){var t=Array.isArray(e)?[]:{},n=document.createElement("div");switch("undefined"===typeof e?"undefined":d(e)){case"string":return n.innerHTML=e,n.textContent||"";case"object":return p.each(e,(function(e,n){return t[e]=p.stripHTML(n)})),t}return e},p.truncate=function(e,t){if(!e||!e.textContent||!e.textContent.length)return 0;var n=e.textContent,r=e.textContent.substring(0,t),i=e.innerHTML?e.innerHTML.substring(0,t):null;if(!e.innerHTML||r===i)return e.textContent=n===r?r:r.replace(/\s*$/,"")+"...",r.length;var o=0;return p.each(e.childNodes,(function(n,r){o>=t?e.removeChild(r):o+=p.truncate(r,t-o)})),e.textContent.length},p.decorateWithMixins=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];if(!(0,r.isFunction)(e))throw new TypeError("".concat(e," is not a function"));n.forEach((function(e,t){if(void 0===e)throw new TypeError("Mixin at position ".concat(t," is undefined"))}));var a=[e];return n.forEach((function(e){var t=a[0],n=function(e){u(n,e);var t=f(n);function n(){return o(this,n),t.apply(this,arguments)}return n}(t);(0,r.isFunction)(e)&&(e=e(t.prototype)),Object.assign(n.prototype,e),a.unshift(n)})),a[0]},p.idGenerator=b;var h=function e(){var t,n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=r.unsubscribe,a=void 0===i?function(){}:i;o(this,e);var u=new Promise((function(e,r){t=e,n=r}));return u.resolve=t,u.reject=n,u.unsubscribe=a,u};p.Deferred=h,p.moduleIsInContext=function(){return!0},t.ZP=p},71288:function(e,t,n){"use strict";var r={};r.isMobileApp=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.CDN_URL?window.CDN_URL:window.location.href;return-1!==e.search("[?&]?s=mobile_app([&#]|$)")};r.isNewsApp=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.CDN_URL?window.CDN_URL:window.location.href;return-1!==e.search("[?&]?app=news([&#]|$)")};r.isNews2App=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.CDN_URL?window.CDN_URL:window.location.href;return-1!==e.search("[?&]?app=buzznews([&#]|$)")};r.isIOS=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.CDN_URL?window.CDN_URL:window.location.href;return-1!==e.search("[?&]?os=ios([&#]|$)")};r.isAndroid=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.CDN_URL?window.CDN_URL:window.location.href;return-1!==e.search("[?&]?os=android([&#]|$)")};t.ZP=r},81550:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var r=n(2784),i=n(21871);function o(e,t){return function(n){return r.createElement(i.Z,t,r.createElement(e,n))}}},69186:function(e,t,n){"use strict";n.d(t,{Z:function(){return U}});var r=n(2784),i=n(13980),o=n.n(i);n(74337);!function(){try{localStorage.setItem("ls_test",!0),localStorage.removeItem("ls_test")}catch(e){return!1}}();n(53428);n(33812),n(4180),n(96989);const a="sticky:members-update",u={normal:1,medium:2,high:3},s=new Map,c=new Map,l={};function d(e,t){const n={priority:e},r=new Set;s.forEach(((t,n)=>{t.priority>e||r.add(n)})),c.forEach(((e,i)=>{i!==t&&(s.has(i)&&!r.has(i)||e.forEach((e=>{try{"function"===typeof e?e(n):"fire"in e&&e.fire(a,n)}catch(t){console.error(t)}})))}))}function f(e){return"fixed"===getComputedStyle(e).position}function p(e,t){void 0===t&&(t=f(e));let{top:n,right:r,bottom:i,left:o,width:a,height:u}=e.getBoundingClientRect();const s=window.pageXOffset;return t||(o+=s,r+=s),{top:n,right:r,bottom:i,left:o,width:a,height:u}}const m={get defaultPriorities(){return u},MEMBERS_UPDATE:a,validatePriority(e){if(isNaN(Number(e))){if("string"!==typeof e)throw new TypeError("Unrecognized priority, should be a number or a name");if(void 0===(e=u[e]))throw new TypeError(`Unknown priority name, should be one of ${Object.keys(u)}`)}return e},isFixed:f,getFixedRect(e,{priority:t=u.normal,requestedTop:n="auto"}={}){t=m.validatePriority(t);const r=p(e);let i;return i="auto"===n?m.getAvailableTop(e,{priority:t,boundingRect:r}):n,r.top=i,r.bottom=i+r.height,r},subscribe(e,t=l){c.has(t)||c.set(t,new Set);c.get(t).add(e)},unsubscribe(e,t=l){const n=c.get(t);n&&(n.delete(e),t!==l&&0===n.size&&c.delete(t))},add(e,{priority:t=u.normal,requestedTop:n="auto"}={}){if(s.has(e))return m.update(e);t=m.validatePriority(t);const r=m.getFixedRect(e,{priority:t,requestedTop:n});return s.set(e,{rect:r,priority:t,requestedTop:n}),d(t,e),r.top},update(e,{forceNotify:t=!1}={}){const n=s.get(e);if(!n)throw new Error("The element is not in the registry");const{priority:r,requestedTop:i}=n,o=n.rect,a=m.getFixedRect(e,{priority:r,requestedTop:i});return n.rect=a,s.set(e,n),(t||a.top!==o.top||a.bottom!==o.bottom||a.left!==o.left||a.right!==o.right)&&d(r,e),a.top},remove(e){const t=s.get(e);t&&(e.className.includes("sticky--fixed sticky--show")||s.delete(e),d(t.priority,e))},has:e=>s.has(e),getAvailableTop(e,{priority:t=u.normal,boundingRect:n}={}){t=m.validatePriority(t);const r=[];if(s.forEach(((n,i)=>{i!==e&&n.priority>=t&&r.push(n)})),0===r.length)return 0;if(!n){const t=s.get(e);n=t?t.rect:p(e)}const i=[];return r.forEach((({rect:e})=>{(e.right>=n.left||e.left<=n.right)&&i.push(e)})),Math.max(...i.map((({bottom:e})=>e)))},getTopmostPosition(e=u.normal){e=m.validatePriority(e);const t=[];return s.forEach((n=>{n.priority>e&&t.push(n.rect.bottom)})),Math.max(...t)},reset(){s.clear(),c.clear()}};var g=m;var v=n(27625),b=n(85953),h=(n(85266),"aBeagleFlipper__1Sq4F"),y="aBeagleFlipperTitle__2tWV1",w="toggleOpen__2iMAk",_="toggleClosed__11-zU",S="visuallyHidden__TCHEd",A="panel__3Hima",I="controls__20xVx",P="experimentList__EunKK",z="experimentListItem__-wLwY",O="actions__1Anym",C="primary__3JC83",j="empty__27yvl",k=n(11423);function E(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var M="abeagle_";function T(e){var t=function(e){return function(t){var n=t.target,r=n.options[n.selectedIndex].value;r!==m[e]&&g(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){E(e,t,n[t])}))}return e}({},m,E({},e,r)))}},n=e.experiments,i=e.className,o=void 0===i?"":i,a=e.collapse,u=void 0!==a&&a,s=n.eligible||{},c=s&&Object.keys(s).length>0,l=(0,r.useState)(!1),d=l[0],f=l[1],p=(0,r.useState)({}),m=p[0],g=p[1],v=(0,r.useState)(!u),b=v[0],T=v[1],x=(0,r.useState)(""),R=x[0],N=x[1],B=(0,r.useState)(""),U=B[0],D=B[1];return(0,r.useEffect)((function(){var e=Object.keys(s).reduce((function(e,t){return e[t]=s[t].value,e}),{}),t=window.location.href.split("?"),n=t[0];if(t[1]&&t[1].length){var r=new URLSearchParams(t[1]);r.forEach((function(t,n){n.startsWith(M)&&(e[n.replace(M,"")]=t,r.delete(n))})),n+="?".concat(r.toString())}g(e),D(n),f(!0)}),[]),(0,r.useEffect)((function(){var e={};if(Object.keys(m).forEach((function(t){n.returned[t]&&n.returned[t].value!==m[t]&&(e["".concat(M).concat(t)]=m[t])})),Object.keys(e).length>0){var t=new URLSearchParams(e),r=U.includes("?")?"".concat(U,"&"):"".concat(U,"?");N("".concat(r).concat(t.toString()))}else N(U)}),[m,U,b]),d?r.createElement("section",{className:"".concat(h," ").concat(o),"aria-labelledby":"abeagle-flipper-title"},r.createElement("div",{className:y},r.createElement("h2",{id:"abeagle-flipper-title"},"Active A/B Tests on Current Page"),r.createElement("button",{type:"button",onClick:function(){T(!b)},title:b?"Hide All":"Show All",className:b?w:_},r.createElement("span",{className:S},b?"Hide":"Show"," All"),r.createElement(k.VA,{width:30,title:b?"Hide All":"Show All","aria-hidden":"true"}))),b&&r.createElement("div",{className:A},c&&r.createElement("div",{className:I},r.createElement("ul",{className:P},Object.keys(s).map((function(e){return r.createElement("li",{key:e,className:z},r.createElement("label",{htmlFor:e},e),r.createElement("select",{id:e,value:m[e]||n.declared[e].variations[0],onChange:t(e),onBlur:t(e)},n.declared[e].variations.map((function(e){return r.createElement("option",{key:e,value:e},e)}))))}))),r.createElement("div",{className:O},r.createElement("a",{href:R,className:C},"Save and Reload"),r.createElement("a",{href:U},"Reset All"))),!c&&r.createElement("p",{className:j},"No experiments active on this page."))):null}function x(e){var t=e.experiments,n=e.className,i=e.collapse;return t&&t.loaded?r.createElement(T,{experiments:t,className:n,collapse:i}):null}T.propTypes={experiments:o().object,className:o().string,collapse:o().bool};var R=n(81550),N=n(410);function B(e){var t=e.css,n=e.html,i=e.js,o=e.stickyHeaderClass,a=void 0===o?"js-main-nav":o,u=e.stickyRegistry,s=void 0===u?g:u,c=(0,r.useRef)(null);(0,r.useEffect)((function(){!function(e,t=document.head,n=!0){new Promise(((r,i)=>{const o=document.createElement("script");o.onload=()=>r(o),o.onerror=()=>{i(`Script at url ${e} failed to load`)},o.src=e,o.async=n,o.type="text/javascript",t.appendChild(o)}))}(i)}),[i]),(0,r.useEffect)((function(){if(!c.current||!a)return function(){};var e=c.current.querySelector(".".concat(a));return e&&s.add(e,{priority:"high"}),function(){e&&s.remove(e)}}),[a,s]);var l=(0,r.useContext)(v.Z).experiments,d=!b.sk&&window.location.search.includes("abdebug"),f=!b.sk&&window.location.search.includes("abdebug_collapse=true");return r.createElement("div",{ref:c},r.createElement("style",{dangerouslySetInnerHTML:{__html:t}}),r.createElement("link",{rel:"preload",href:i,as:"script"}),d?r.createElement(x,{experiments:l,collapse:f}):"",r.createElement("div",{dangerouslySetInnerHTML:{__html:n}}))}B.propTypes={css:o().string,html:o().string.isRequired,js:o().string.isRequired,stickyRegistry:o().object,stickyHeaderClass:o().string};var U=(0,R.Z)(B,{onError:N.Tb})},45201:function(e,t,n){"use strict";n.d(t,{Z:function(){return y}});var r=n(94776),i=n.n(r),o=n(2784),a=n(74337);function u(e,t,n,r,i,o,a){try{var u=e[o](a),s=u.value}catch(c){return void n(c)}u.done?t(s):Promise.resolve(s).then(r,i)}function s(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){u(o,r,i,a,s,"next",e)}function s(e){u(o,r,i,a,s,"throw",e)}a(void 0)}))}}var c="abdebug";function l(e){var t=new URLSearchParams(window.location.search);t&&t.has(c)&&console.debug(e)}function d(e){return f.apply(this,arguments)}function f(){return(f=s(i().mark((function e(t){var n,r,o,a,u,s,c,d;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.userId,r=t.data,o=t.source,a=t.experimentConfig,u=t.abeagleHost,s=t.trackFn,c={declared:{},eligible:{},returned:{},loaded:!1},a&&a.length){e.next=5;break}return c.loaded=!0,e.abrupt("return",c);case 5:return a.forEach((function(e){c.declared[e.name]=e,("boolean"===typeof e.isEligible?e.isEligible:e.isEligible(r))&&(c.eligible[e.name]=e)})),(d=new URLSearchParams).append("experiment_names",Object.keys(c.eligible).join(";")),d.append("user_id",n),d.append("source",o),d.toString(),e.next=13,fetch("".concat(u,"/public/v3/experiment_variants?").concat(d.toString())).then((function(e){return e.json()})).catch((function(e){return l(e),{}}));case 13:return c.returned=e.sent,s&&g(c.returned,s),Object.keys(c.returned).forEach((function(e){c.declared[e]&&(!c.returned[e].error&&c.eligible[e]&&(c.eligible[e]=c.returned[e]))})),c.loaded=!0,e.abrupt("return",c);case 18:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function p(){return a.Z.getBuzzfeedSubdomainOrWildcard(window.location.hostname)}function m(e,t){var n=window.location.hostname.replace("www",""),r=p();a.Z.remove(e,".".concat(n));var i=r==="www.".concat(n)?14:1;a.Z.set({name:e,value:t,days:i,domain:r})}function g(e,t){var n=p();Object.keys(e).forEach((function(r){var i="".concat(r,"_version"),o=e[r],u=o.value,s=o.version,c=o.error,l=o.resolved;if(!c){if(l&&(u=u||"control"),null===u)return a.Z.remove(r,n),void a.Z.remove(i,n);var d=a.Z.get(r)===String(u),f=a.Z.get(i)===String(s);d&&f||(m(r,u),m(i,s),t({experiment:r,variation:e[r]}))}}))}function v(e,t,n,r,i,o,a){try{var u=e[o](a),s=u.value}catch(c){return void n(c)}u.done?t(s):Promise.resolve(s).then(r,i)}function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){b(e,t,n[t])}))}return e}function y(e){var t=e.userId,n=e.data,r=e.experimentConfig,a=e.source,u=e.abeagleHost,s=e.trackFn,c={data:n,experimentConfig:r,source:a,abeagleHost:u},l=Object.keys(c).reduce((function(e,t){return c[t]||e.push(t),e}),[]);if(l.length)throw new Error("Missing required fields: ".concat(l.join(", ")));var f=(0,o.useRef)(h({},{declared:{},eligible:{},returned:{},loaded:!1})),p=(0,o.useState)(!1),m=p[0],g=p[1],b=(0,o.useMemo)((function(){return h({},f.current,{loaded:m})}),[m]);return(0,o.useEffect)((function(){var e;t&&(g(!1),(e=i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,d({userId:t,data:n,experimentConfig:r,source:a,abeagleHost:u,trackFn:s});case 2:f.current=e.sent,g(!0);case 4:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){v(o,r,i,a,u,"next",e)}function u(e){v(o,r,i,a,u,"throw",e)}a(void 0)}))})())}),[t,n]),b}},85953:function(e,t,n){"use strict";n.d(t,{ge:function(){return c},Kl:function(){return a},sk:function(){return r},vV:function(){return l},tY:function(){return s},nk:function(){return i},O2:function(){return u},Dn:function(){return o}});var r=!1,i=function(){return!r&&window.matchMedia&&window.matchMedia("(prefers-reduced-motion: reduce)").matches},o=function(){return!r&&HTMLImageElement&&"loading"in HTMLImageElement.prototype},a=function(e){return e.includes(".gif")},u=function(e){if(!e)return[e,[]];var t=e.split("?"),n=t[1]?t[1].split("&"):[];return[t[0],n]},s=function(e,t){if(!e)return"";var n=t.length>0?"?".concat(t.join("&")):"";return"".concat(e).concat(n)},c=function(){var e="https://stage.buzzfeed.com";return/^www\.buzzfeed(?:news)?\.com$/.test(window.location.hostname)&&(e="https://www.buzzfeed.com"),e},l=function(e){return/^([\w-]+(?:\.[\w-]+)*)(\+[\w]+)?@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/i.test(e)}},26002:function(e,t,n){"use strict";n.d(t,{I:function(){return o}});var r=n(2784),i=n(33812);function o(){var e=(0,r.useState)(null),t=e[0],n=e[1];return(0,r.useEffect)((function(){n((0,i.TQ)())}),[]),t}},2706:function(e){"use strict";e.exports={isServer:function(){return!1}}},99036:function(e,t,n){"use strict";var r;!function(i){var o=function(e,t,n){e=e||"";var r=a(t=t||[],n=n||"");return u(e,r)},a=function(e,t){return{allowable_tags:e=s(e),tag_replacement:t,state:d,tag_buffer:"",depth:0,in_quote_char:""}},u=function(e,t){if("string"!=typeof e)throw new TypeError("'html' parameter must be a string");for(var n=t.allowable_tags,r=t.tag_replacement,i=t.state,o=t.tag_buffer,a=t.depth,u=t.in_quote_char,s="",l=0,m=e.length;l<m;l++){var g=e[l];if(i===d)if("<"===g)i=f,o+=g;else s+=g;else if(i===f)switch(g){case"<":if(u)break;a++;break;case">":if(u)break;if(a){a--;break}u="",i=d,o+=">",n.has(c(o))?s+=o:s+=r,o="";break;case'"':case"'":u=g===u?"":u||g,o+=g;break;case"-":"<!-"===o&&(i=p),o+=g;break;case" ":case"\n":if("<"===o){i=d,s+="< ",o="";break}o+=g;break;default:o+=g}else if(i===p)if(">"===g)"--"==o.slice(-2)&&(i=d),o="";else o+=g}return t.state=i,t.tag_buffer=o,t.depth=a,t.in_quote_char=u,s},s=function(e){var t=new Set;if("string"===typeof e)for(var n;n=m.exec(e);)t.add(n[1]);else l.nonNative||"function"!==typeof e[l.iterator]?"function"===typeof e.forEach&&e.forEach(t.add,t):t=new Set(e);return t},c=function(e){var t=g.exec(e);return t?t[1].toLowerCase():null};if("function"!==typeof l){var l=function(e){return e};l.nonNative=!0}var d=l("plaintext"),f=l("html"),p=l("comment"),m=/<(\w*)>/g,g=/<\/?([^\s\/>]+)/;o.init_streaming_mode=function(e,t){var n=a(e=e||[],t=t||"");return function(e){return u(e||"",n)}},void 0===(r=function(){return o}.call(t,n,t,e))||(e.exports=r)}()},85266:function(){}}]);
//# sourceMappingURL=852-e1d08a8072de9b39.js.map