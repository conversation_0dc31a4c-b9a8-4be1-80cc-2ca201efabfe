(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[862],{79114:function(e,t,n){"use strict";n.d(t,{Z:function(){return W}});var r={};n.r(r),n.d(r,{getComponent:function(){return M},getTemplate:function(){return I},getWireframeOptions:function(){return A}});var i=n(52322),o=n(2784),a=n(13980),u=n.n(a),c=n(56758);function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){s(e,t,n[t])}))}return e}for(var f={},p={adPos:"promo",adType:"ex",isInfinite:!0,platform:"mobileweb",renderLookahead:"x0.25",size:[c.J7.FLUID,c.J7.NATIVE,c.J7.PROGRAMMATIC_MEDIUM_RECTANGLE,c.J7.PROGRAMMATIC_SMARTPHONE_BANNER],viewability:"low"},d=1;d<=6;d++){var g="promo".concat(d);f[g]=l({},p,{adPos:g,wid:"23".concat(d-1)})}f["promo-infinite"]=l({},p,{adPos:"promo-infinite",wid:2300});var v=Object.freeze(l({},c.vc,f)),m=n(14007),h=n(6294),y=n(2706),b=n(97890),_=n(1879),w=n(21576),x=n(89780),j=n(71049),O=n(44399),P=n(22606),S=n(28403),N=n(12239);n(67419);b.lT.prototype.getEventId=function(){return"".concat(this.config.adPos,"-").concat(this.instanceId)};var E={AdAwareness:_.Z,AdAffiliatePixel:w.Z,AdEx:x.S,AdToolbar:j.Z},k={AdAwareness:O.Z,AdAffiliatePixel:P.Z,AdEx:S.Z,AdToolbar:N.Z};function I(e){var t=e.adPos;return/^awareness/.test(t)?k.AdAwareness:/^pixel/.test(t)?k.AdAffiliatePixel:/^tb$/.test(t)?k.AdToolbar:k.AdEx}function M(e){var t=e.adPos;return/^awareness/.test(t)?E.AdAwareness:/^pixel/.test(t)?E.AdAffiliatePixel:/^tb$/.test(t)?E.AdToolbar:E.AdEx}function A(){return{}}var T=n(88306),C=n(26054),z=n(410);function L(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function D(e){var t=e.type,n=e.config,a=e.mode,u=e.onAdViewed,c=e.onAdRender,s=e.className,l=void 0===s?"":s,f=(0,o.useContext)(T.oF),p=(0,o.useContext)(T.z1),d=p.adsDisabled,g=p.pagePath,b=!(0,y.isServer)()&&window.matchMedia&&(0,h.tq)()?"x0.5":"x0.25",_=(0,o.useMemo)((function(){var e=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){L(e,t,n[t])}))}return e}({},n||v[t]);return"renderLookahead"in e||(e.renderLookahead=b),/^awareness/.test(e.adPos)&&(e.adPos="awareness"),e}),[n,t,b]);return d?null:(0,i.jsx)(m.Z,{adUnitUtils:r,config:_,pageId:g,mode:a,onAdViewed:u,onAdRender:c,stickyManager:f,className:l})}D.propTypes={type:u().string,config:u().object,mode:u().oneOf(["active","preload"]),onAdViewed:u().func,onAdRender:u().func,pageLanguage:u().string,className:u().string};var W=(0,C.Z)(D,{onError:z.Tb})},16679:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(52322),i=n(88163);function o(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function a(e){var t=e.children,n=e.lazy,a=void 0===n||n,u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.preloadDistance,n=void 0===t?.75:t,r=e.rootMargin;if(void 0===r){var o="undefined"!==typeof document?document.documentElement.clientHeight:0;r="".concat(o*n,"px 0px")}var a=(0,i.Z)({rootMargin:r,once:!0}),u=a.isIntersecting,c=a.setObservable;return{canLoad:u,setObservable:c}}(o(e,["children","lazy"])),c=u.canLoad,s=u.setObservable;return(0,r.jsx)("div",{ref:a?s:null,children:!a||c?t:null})}},65786:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(52322),i=(n(2784),n(12764)),o=n.n(i);function a(e){var t=e.onOverlayClick,n=e.showOverlay,i=function(){t(!0)},a="";return!0!==n&&(a=o()[n]),(0,r.jsx)("div",{role:"presentation",className:"".concat(o().pageOverlay," ").concat(a),onClick:i,onKeyDown:i})}t.C=a},44644:function(e,t,n){"use strict";n.d(t,{Z:function(){return ct}});var r=n(52322),i=n(2784),o=n(17613),a=n(13980),u=n.n(a),c=n(88306),s=n(21038),l=n(84952);function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function p(e){return function(e){if(Array.isArray(e))return f(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return f(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var d=[{name:"TimeSpentRO_2",variations:["on","control"],isEligible:function(){return!0}},{name:"COMMERCE-social_reviews_1",variations:["control","on"],isEligible:function(){return!0}},{name:"shoppybot-clippy-shopping",variations:["control","on"],isEligible:function(){return!0}},{name:"shoppybot",variations:["control","on"],isEligible:function(){return!0}},{name:"target_shoppy_bot",variations:["control","on"],isEligible:function(){return!0}},{name:"RT-1559-AdShield-script-on-BFDC",variations:["control","on"],isEligible:function(e){return!0}},{name:"RT-1751-prime-day-shopping-header",variations:["control","on"],isEligible:function(){return"US"===(0,l.pP)()}}],g=[{name:"RT-1710-adshield-experiment",variations:["control","adshield"],isEligible:function(){return!["gb","ie","uk","nz","au","jp"].includes((0,l.pP)().toLowerCase())}}],v=[s.Yx].concat(p(d),p(g)),m=n(45201),h=n(30353),y=n(26002),b=n(50240),_=n(39283),w=n(68438),x=n(2706),j=n(73035),O=n(74967);function P(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function N(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){S(e,t,n[t])}))}return e}function E(e){return function(e){if(Array.isArray(e))return P(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return P(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return P(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function k(e){var t=e.children,n=e.edition,o=e.tracking,a=void 0===o?{}:o,u=e.pageProps,s=void 0===u?{}:u,l=(0,y.I)({cluster:h.CLUSTER}),f=(0,i.useRef)(null),p=(0,_.Y)({isBPage:!1,isHomePage:!1,isNewBPage:!1,isFeedPage:!0,isBFO:!0,isBFN:!1,localizationCountry:n,userCountry:(0,x.isServer)()?"":(0,w.pP)(),edition:n,isAdPost:function(){return!1}}),d=(0,i.useMemo)((function(){return{userId:l,data:{}}}),[l]),g=(0,m.Z)(N({abeagleHost:h.abeagle_host,experimentConfig:E(v).concat(E(p)),source:"buzz_web"},d)),P=s.pagePath;f.current&&f.current.loaded===g.loaded||(f.current=N({},g,{pagePath:P})),f.current.stale=f.current.pagePath!==P;var S=f.current.loaded,k=g.eligible?Object.keys(g.eligible).join("|"):"";(0,i.useEffect)((function(){if(S&&k.length){var e=[];Object.keys(g.eligible).forEach((function(t){var n=g.eligible[t];if(n&&n.value){var r=n.id,i=n.version,o=n.value,a=n.variant_id;e.push([t,r,i,o,a].join("|")),(0,j.nz)(v,t,n)}})),(0,b.bI)(a,{experiment_id:e})}}),[P,S,k]);return(0,r.jsx)(c.WN.Provider,{value:{experiments:f.current,getFeatureFlagValue:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"on";return(0,O.F7)(g,e,t)},getExperimentValue:function(e,t){return(0,O.ts)(g,e,t)}},children:t})}k.propTypes={children:u().oneOfType([u().arrayOf(u().node),u().node]),edition:u().string,tracking:u().object,pageProps:u().object};var I=k,M=n(94776),A=n.n(M),T=n(41419),C=n(59855),z=n(56758),L=n(44941);function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function W(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(c){u=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(u)throw i}}return o}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return D(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return D(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"us",t=W((e=(0,L.pageEditionCode)(e)).split("-"),2),n=t[0],r=t[1];return{edition:e,language:n,country:r}}function Z(){var e=(0,l.Js)(),t=W(e.split("-"),2),n=t[0],r=t[1];return{edition:e,language:n,country:r,locale:n&&r?"".concat(n,"_").concat(r.toUpperCase()):n}}function U(){return(0,l.pP)()}var F=n(78140);function G(e,t,n,r,i,o,a){try{var u=e[o](a),c=u.value}catch(s){return void n(s)}u.done?t(c):Promise.resolve(c).then(r,i)}function B(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){G(o,r,i,a,u,"next",e)}function u(e){G(o,r,i,a,u,"throw",e)}a(void 0)}))}}function V(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){V(e,t,n[t])}))}return e}function Q(){return Q=B(A().mark((function e(t){var n,r,i,o,a,u,c,s,l,f,p,d;return A().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.abeagle,r=t.edition,i=t.pageName,o=t.pageSection,a=t.tracking,u=t.pageType,c=t.pagePath,s=Y({},R(r)),l=[],"collection"===u&&l.push(c.split("/").pop()),f=Z(),p=U(),d={adSizes:z.J7,programmaticSizes:z.PP,destination:"buzzfeed",isBFN:!1,isE2ETest:!1,isFeed:!1,isFeedPage:!0,isFeedpager:!1,isHomePage:!1,isNewsPost:!1,isBFO:!0,isBPage:!1,isDev:"dev"===h.CLUSTER,isProd:"prod"===h.CLUSTER,pageFilter:null,pageFilters:{},pageMainFilter:null,type:h.CLUSTER,webRoot:h.bf_url,allPageSections:["shopping"],author:null,cmsTags:l,hasQuiz:!1,isAsIs:!1,isPharmaceutical:!1,isWidePost:!1,pageCategory:"shopping",pageSection:o,pageName:i,pageId:F.Ck.TOPIC.toString(),analyticsPageType:F.un.FEED,isAdPost:function(){return!1},userCountry:p,localization:s,locale:f.locale,localizationCountry:f.country},e.abrupt("return",{env:d,abeagle:n,localization:s,stickyRegistry:C.Z,tracking:a});case 8:case"end":return e.stop()}}),e)}))),Q.apply(this,arguments)}function H(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function J(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){H(e,t,n[t])}))}return e}var K=function(){var e={};return function(e,t){var n={bindBfpClientContentAction:function(e){var n=e.element,r=e.data;n&&r&&(0,b.Q8)(n,{},J({},r,t,{unit_type:"feed",unit_name:"main"}))},trackBfpClientContentAction:function(e){var n=e.data;(0,b.bC)({},J({},n,t,{unit_type:"feed",unit_name:"main"}))}};Object.keys(n).forEach((function(t){return e[t]=n[t]}))}(e,(0,i.useContext)(c.z1).tracking),e};function q(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function X(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(c){u=!0,i=c}finally{try{a||null==n.return||n.return()}finally{if(u)throw i}}return o}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return q(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return q(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(){var e,t=(0,i.useContext)(c.WN).experiments,n=(0,i.useRef)(),r=(0,i.useRef)({});n.current||(e=X(function(){var e;return[new Promise((function(t){return e=t})),e]}(),2),r.current.loaded=e[0],r.current.resolve=e[1]);n.current=!0;var o=(0,i.useRef)({experiments:r.current.loaded,isOn:function(){var e=arguments;return r.current.loaded.then((function(t){return O.F7.apply(void 0,[t].concat(Array.prototype.slice.call(e)))}))},getExperimentVariant:function(){var e=arguments;return r.current.loaded.then((function(t){return O.ts.apply(void 0,[t].concat(Array.prototype.slice.call(e)))}))}});return(0,i.useEffect)((function(){t&&t.loaded&&!t.stale&&r.current.resolve(t)}),[t]),o.current}function ee(e,t,n,r,i,o,a){try{var u=e[o](a),c=u.value}catch(s){return void n(s)}u.done?t(c):Promise.resolve(c).then(r,i)}function te(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){ee(o,r,i,a,u,"next",e)}function u(e){ee(o,r,i,a,u,"throw",e)}a(void 0)}))}}function ne(){return(ne=te(A().mark((function e(t){var n,r,i,o,a,u;return A().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.abeagle,r=t.pageType,i=t.pagePath,o={pagetype:"A"},a=["RT-1710-adshield-experiment"],e.next=5,Promise.all(a.map((function(e){return n.getExperimentVariant(e,{rejectErrors:!1,defaultVariantIfUnbucketed:null})})));case 5:return u=e.sent,o.abtest=a.map((function(e,t){return"".concat(e,"|").concat(u[t])})),"collection"===r&&i&&(o.collection=i.split("/").filter((function(e){return""!==e})).pop()),"category"===r&&i&&(o.category=i.split("/").filter((function(e){return""!==e})).pop()),e.abrupt("return",o);case 10:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function re(e){var t=e.edition,n=e.pagePath,o=e.children,a=e.pageName,u=e.pageSection,c=e.pageType,s=$(),l=K(),f=(0,i.useCallback)((function(){return function(e){return Q.apply(this,arguments)}({edition:t,abeagle:s,pageName:a,pageSection:u,tracking:l,pageType:c,pagePath:n})}),[t,s,a,u,l]),p=(0,i.useCallback)((function(){return function(e){return ne.apply(this,arguments)}({abeagle:s,pageType:c,pagePath:n})}),[s,c,n]);return(0,r.jsx)(T.Z,{pageId:n,pageLanguage:t,adsEnabled:!0,getPageContext:f,getPageTargeting:p,children:o})}re.propTypes={edition:u().string,pagePath:u().string,children:u().oneOfType([u().arrayOf(u().node),u().node])};var ie=n(79114),oe=n(75650),ae=n(64435),ue=n(72527);var ce=function(e){var t=e.searchQuery,n=e.setShowOverlay,o=e.showOverlay,a=(0,i.useContext)(c.z1),u=a.edition,s=a.tracking,l=a.pageType,f=a.baseUrl,p=a.baseParamsString,d=(0,(0,i.useContext)(c.WN).getFeatureFlagValue)("RT-1751-prime-day-shopping-header");return(0,r.jsx)(oe.U,{edition:u,tracking:s,searchQuery:t,setShowOverlay:n,showOverlay:o,pageType:l,trackClientContentAction:b.bC,attachClientImpressionHandler:b.aF,useUnitTracking:ae.R,useClickTracking:ue.v,baseUrl:f,baseParamsString:p,isPrimeDay:d})};function se(e){var t=e.isBFN,n=void 0!==t&&t,r=e.isLoadIDNML,o=void 0!==r&&r,a=(0,i.useContext)(c.WN).experiments;return(0,i.useEffect)((function(){if(a.loaded&&!a.stale){var e=(0,O.F7)(a,s.Yx.name);s.hi.configure({useFallback:!e})}}),[a.loaded,a.stale]),(0,i.useEffect)((function(){s.hi.init({isBFN:n,isLoadIDNML:o})}),[]),null}var le=n(49277);function fe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function pe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){fe(e,t,n[t])}))}return e}var de=n(3379),ge=n(92523),ve=n(20238);var me=n(99377),he=n(65852);function ye(e,t,n,r,i,o,a){try{var u=e[o](a),c=u.value}catch(s){return void n(s)}u.done?t(c):Promise.resolve(c).then(r,i)}function be(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){ye(o,r,i,a,u,"next",e)}function u(e){ye(o,r,i,a,u,"throw",e)}a(void 0)}))}}function _e(){return(_e=be(A().mark((function e(){return A().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:new he.BE({cluster:h.CLUSTER});case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var we,xe,je=n(23904);function Oe(e,t,n,r,i,o,a){try{var u=e[o](a),c=u.value}catch(s){return void n(s)}u.done?t(c):Promise.resolve(c).then(r,i)}function Pe(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){Oe(o,r,i,a,u,"next",e)}function u(e){Oe(o,r,i,a,u,"throw",e)}a(void 0)}))}}var Se,Ne="undefined"!==typeof document?new URL(document.location):{},Ee="prod"!==h.CLUSTER&&"facebook"===(null===(we=Ne.searchParams)||void 0===we?void 0:we.get("tracking_debug")),ke=function(){return xe||(xe=new he.mS({trackingId:h.facebook_tracking_id,cluster:h.CLUSTER})),xe};function Ie(){return(Ie=Pe(A().mark((function e(t){return A().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,ke();case 2:e.sent.trackEvent({eventType:"PageView",data:t}),Ee&&(0,je.L)("Facebook",{eventName:"track",eventType:"PageView",eventData:t},Ee);case 5:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Me(e,t,n,r,i,o,a){try{var u=e[o](a),c=u.value}catch(s){return void n(s)}u.done?t(c):Promise.resolve(c).then(r,i)}function Ae(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){Me(o,r,i,a,u,"next",e)}function u(e){Me(o,r,i,a,u,"throw",e)}a(void 0)}))}}function Te(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ce=function(e,t){var n=function(e){var t=h.permutive_creds[e]||h.permutive_creds.default,n=t.api_key;return{projectId:t.project_id,apiKey:n}}(e);return Se||(Se=new he.TZ(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Te(e,t,n[t])}))}return e}({cluster:h.CLUSTER},n))),Se};function ze(){return(ze=Ae(A().mark((function e(t){var n,r,i,o;return A().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=t.edition,r=t.section,i=Ce(n),o={page:{meta:{type:r,publisher:"buzzfeed",platform:"web",edition:n}}},i.trackPageView(o);case 4:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Le(e){(0,i.useEffect)((function(){(0,b.Yq)(e),function(e){Ie.apply(this,arguments)}({section:"Shopping",Edition:e.page_edition,SocialReferral:(0,me.an)()}),function(e){ze.apply(this,arguments)}({section:"Shopping",edition:e.page_edition}),function(){_e.apply(this,arguments)}()}),[])}function De(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function We(e){return function(e){if(Array.isArray(e))return De(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return De(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return De(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Re=n(99151);function Ze(e){var t=e.tracking,n=(0,i.useContext)(c.WN).experiments;return(0,i.useEffect)((function(){var e=function(){};!n.stale&&n.loaded&&("on"===(0,O.ts)(n,"TimeSpentRO_2",{rejectErrors:!1})&&(e=(0,b.ff)(t)));return e}),[n.loaded,t]),null}var Ue=n(7723),Fe=n.n(Ue),Ge=n(39786),Be=n(60957),Ve=n.n(Be);function Ye(e){var t=e.homebuilderData,n=(0,(0,i.useContext)(c.WN).getFeatureFlagValue)("shoppybot")&&(0,l.pP)().includes("US"),o=(0,Ge.a)(),a=t.display_description,u=t.display_title,s=t.image_small_url,f=t.image_large_url,p=a.replace(/\\\"/g,'"'),d=o.isMobile?s:f;return u&&s&&f?(0,r.jsx)(r.Fragment,{children:n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:Fe().shoppyBanner,children:[(0,r.jsx)("img",{src:Ve(),alt:""}),"Unlock Your Gifting Superpowers with Shoppy, Your New Gifting Sidekick"]}),(0,r.jsx)("div",{style:{height:"650px"},children:(0,r.jsx)("iframe",{border:"0",height:"100%",id:"chatframe",scrolling:"no",src:"/ai-ui/games/shoppy-chat",width:"100%"})})]}):(0,r.jsxs)("div",{className:Fe().headerWrapper,children:[(0,r.jsx)("div",{className:Fe().headerOverlay,children:(0,r.jsx)("img",{className:Fe().headerImg,alt:u,src:"".concat(d,"?output-format=auto&output-quality=100")})}),(0,r.jsx)("div",{className:Fe().headerContent,children:(0,r.jsxs)("div",{className:Fe().headerContentCenter,children:[(0,r.jsx)("h1",{className:Fe().headerTitle,children:u}),(0,r.jsx)("p",{className:Fe().headerDescription,dangerouslySetInnerHTML:{__html:p}})]})})]})}):""}var Qe=n(31706),He=n.n(Qe),Je=function(e){var t=e.className,n=e.title;return(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 38 38",className:t,children:[(0,r.jsx)("title",{children:n}),(0,r.jsx)("path",{d:"M34 28.5c-.5 0-1-.2-1.4-.6L19 14.3 5.4 27.9c-.8.8-2 .8-2.8 0-.8-.8-.8-2 0-2.8L19 8.7l16.4 16.4c.8.8.8 2 0 2.8-.4.4-.9.6-1.4.6z"})]})},Ke=function(e){var t=e.className,n=e.title;return(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 38 38",className:t,children:[(0,r.jsx)("title",{children:n}),(0,r.jsx)("path",{d:"M19 29.3L2.6 12.9c-.8-.8-.8-2 0-2.8.8-.8 2-.8 2.8 0L19 23.7l13.6-13.6c.8-.8 2-.8 2.8 0 .8.8.8 2 0 2.8L19 29.3z"})]})};function qe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Xe=n(35930);function $e(e){var t=e.setShowOverlay,n=["Recipient","Category","Interest","Price Range","Community Impact"],o=(0,i.useState)(!1),a=o[0],u=o[1],s=(0,i.useState)(0),l=s[0],f=s[1],p=(0,i.useRef)(),d=(0,i.useRef)(),g=(0,i.useContext)(c.z1),v=g.isMobile,m=g.isTablet,h=g.isTabletPro,y=function(e,n){v&&"click"!==(null===n||void 0===n?void 0:n.type)&&"mousedown"!==(null===n||void 0===n?void 0:n.type)||(a===e?setTimeout((function(){v&&(d.current.scrollLeft="0"),u(!1),t(!1),p.current.style.zIndex=0}),v||m||h?0:800):(!function(e){if((null===e||void 0===e?void 0:e.target)&&v){var t=e.currentTarget.getBoundingClientRect().left-d.current.getBoundingClientRect().left;d.current.scrollLeft+=t}}(n),u(e),t(!0),f(n.currentTarget.getBoundingClientRect().left),p.current.style.zIndex=5))};return(0,i.useEffect)((function(){var e=function(e){a&&p.current&&!p.current.contains(e.target)&&y(a,e)};return a&&document.addEventListener("mousedown",e),function(){document.removeEventListener("mousedown",e)}}),[a,y]),(0,r.jsxs)("div",{ref:p,className:He().navWrapper,onMouseLeave:function(){return u(!1),void t(!1)},children:[(0,r.jsxs)("div",{className:He().navMenu,children:[(0,r.jsx)("div",{ref:d,className:He().navMenuItemWrapper,children:n.map((function(e,t){var n=e===a?He().selected:"";return(0,r.jsxs)("div",{className:"".concat(He().navMenuItem," ").concat(n),onClick:function(t){return y(e,t)},onKeyDown:function(t){return y(e,t)},onMouseEnter:function(t){return y(e,t)},role:"button",tabIndex:t,children:[(0,r.jsx)("span",{className:He().navMenuItemTitle,children:e}),a===e?(0,r.jsx)(Je,{className:He().arrowUp}):(0,r.jsx)(Ke,{className:He().arrowDown})]},"m-".concat(t))}))}),(0,r.jsx)("div",{className:He().mobileOverlay})]}),(0,r.jsx)("div",{className:He().subNavWrapper,children:n.map((function(e,t){return(0,r.jsx)(et,{menuName:e,openMenuItem:a,toggleSubMenu:y,subNavIndex:t,selectedMenuLeftPos:l,isMobile:v},"ggsn-".concat(t))}))})]})}function et(e){var t=e.menuName,n=e.openMenuItem,i=e.toggleSubMenu,o=e.subNavIndex,a=e.selectedMenuLeftPos,u=e.isMobile,c=Xe["en-us"].giftguide.category.menu,s=t===n?He().showSubMenu:He().hideSubMenu,l={left:"".concat(a,"px"),position:"relative"},f=c[t].map((function(e,t){return(0,r.jsx)(tt,{subMenuItem:e,subIndex:t},"ggsni-".concat(t))})).reduce((function(e,t,n){return n%4===0&&e.push([]),e[e.length-1].push(t),e}),[]).map((function(e,t){return(0,r.jsx)("div",{className:He().col,children:e},"rowcon-".concat(t))}));return(0,r.jsx)("div",{role:"button",onMouseLeave:function(e){return i(t,e)},onKeyDown:function(e){return i(t,e)},className:"".concat(He().subNavMenuWrapper," ").concat(s),tabIndex:o,style:u?{}:l,children:(0,r.jsx)("div",{className:He().colWrapper,children:f})})}function tt(e){var t=e.subMenuItem,n=e.subIndex,o=(0,i.useContext)(c.z1).tracking,a=(0,i.useMemo)((function(){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){qe(e,t,n[t])}))}return e}({},o,{item_type:"text",item_name:t.title,target_content_type:"url",target_content_id:t.link,target_content_url:"/giftguide/".concat(t.link),subunit_name:"feed_filters",subunit_type:"component",unit_name:"subcategories",unit_type:"nav",position_in_subunit:n})}),[o]),u=(0,ae.R)(a);return(0,r.jsx)("div",{className:He().subNavMenuItem,children:(0,r.jsx)("a",{ref:u,href:"/giftguide/".concat(t.link),className:He().subNavMenuItemLink,children:t.title})})}function nt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function rt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){nt(e,t,n[t])}))}return e}var it=function(e){var t=e.children;return(0,r.jsx)(c.oF.Provider,{value:rt({},C.Z,{notify:function(e,t){return(0,t.callback)({shouldStick:!0})}}),children:t})};var ot=function(){var e="adshield"===(0,(0,i.useContext)(c.WN).getExperimentValue)("RT-1710-adshield-experiment",{rejectErrors:!1}),t=(0,i.useState)(!1),n=t[0],o=t[1];return(0,i.useEffect)((function(){e&&!n&&(o(!0),w._c.init({isShopping:!0,destination:"buzzfeed"}))}),[e]),(0,r.jsx)(r.Fragment,{})};function at(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ut(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){at(e,t,n[t])}))}return e}var ct=function(e){var t=e.homebuilderData,n=void 0===t?{}:t,a=e.header,u=e.searchQuery,s=e.children,l=e.setShowOverlay,f=e.showOverlay,p=(0,i.useContext)(c.z1),d=p.adsDisabled,g=p.tracking,v=p.edition,m=p.pageEdition,y=p.pagePath,_=p.pageGroup,w=p.pageType;return function(e){(0,i.useEffect)((function(){var t=de.Z.get("hem"),n=de.Z.get("bf2-b_info"),r=[];((0,ve.dn)(window.location.search).email_hash||ge.Z.sessionGet("newsletterAddressable"))&&(ge.Z.sessionSet({key:"newsletterAddressable",value:"true"}),r.push("newsletter")),n&&r.push("auth"),(0,b.Xg)(e,{is_addressable:!!t,addressable_source:r,addressable_partner:[]})}),[])}(g),Le(g),function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];(0,i.useEffect)((function(){b.AG.apply(void 0,[e].concat(We(n)))}),[])}(g),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,le.Z)("tracking");var t=pe({},e);(0,i.useEffect)((function(){window.clientEventTracking={getPageContextLayer:function(){return function(){return t}},env:h.CLUSTER}}),[t])}(g),function(e){(0,le.Z)("tracking");var t=(0,i.useState)(!1),n=t[0],r=t[1];(0,i.useEffect)((function(){var t=function(t){(0,b.Y3)(e,{pixel_depth:parseInt((window.scrollY||window.pageYOffset)+window.innerHeight,10),marker:t})},i=function(){n||"hidden"!==document.visibilityState||t("distraction_depth")},o=function(){t("exit_depth"),r(!0)};return window.addEventListener("beforeunload",o),document.addEventListener("visibilitychange",i),function(){window.removeEventListener("beforeunload",o),document.removeEventListener("visibilitychange",i)}}),[e,n])}(g),(0,Re.Z)(y),function(){var e=(0,i.useContext)(c.z1),t=e.adsDisabled,n=e.membershipAvailable,r=function(e){var r;"topic-nav-loaded"===(null===e||void 0===e||null===(r=e.data)||void 0===r?void 0:r.type)&&!t&&n&&window.postMessage({type:"show-membership-promo-button"},"*")};(0,i.useEffect)((function(){return window.addEventListener("message",r),function(){window.removeEventListener("message",r)}}),[])}(),(0,r.jsxs)(I,{edition:v,tracking:g,children:[(0,r.jsx)(se,{}),(0,r.jsx)(Ze,{tracking:g}),(0,r.jsx)(it,{children:(0,r.jsxs)(re,{edition:m,pagePath:y,pageName:_,pageSection:_,pageType:w,children:[!d&&(0,r.jsx)(ot,{}),("shopping"===_||"giftguide"===_)&&(0,r.jsx)(ie.Z,{type:"awareness",className:"Ad--awareness"}),"giftguide"===_&&(0,r.jsx)(ie.Z,{type:"pixel",className:"Ad--pixel"}),(0,r.jsx)(o.Z,ut({},a)),"giftguide"===_&&n&&(0,r.jsx)(Ye,{homebuilderData:n}),"giftguide"===_&&(0,r.jsx)($e,{setShowOverlay:l}),"shopping"===_&&(0,r.jsx)(ce,{searchQuery:u,setShowOverlay:l,showOverlay:f}),("shopping"===_||"giftguide"===_)&&(0,r.jsx)(ie.Z,{type:"toolbar"}),s]})})]})}},68499:function(e,t,n){"use strict";var r=n(52322),i=n(2784),o=n(88306),a=n(64435),u=n(61957),c=n(13980),s=n.n(c),l=n(70733),f=n.n(l),p=n(43705);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(e){var t=e.className,n=void 0===t?"":t,c=e.buzzId,s=void 0===c?"":c,l=e.href,g=void 0===l?"":l,m=e.title,h=void 0===m?"":m,y=e.thumbnail,b=void 0===y?"":y,_=e.altText,w=void 0===_?"":_,x=e.origin,j=void 0===x?"":x,O=e.tracking,P=void 0===O?{}:O,S=e.postTags,N=void 0===S?[]:S,E=(0,i.useContext)(o.z1).pageGroup,k=(0,i.useMemo)((function(){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){d(e,t,n[t])}))}return e}({},P,{item_type:"card",item_name:"post",target_content_type:"buzz",target_content_id:s})}),[P,s]),I=(0,a.R)(k),M=(0,i.useMemo)((function(){return(0,u.addOrigin)({origin:j,href:g})}),[j,g]);return(0,r.jsxs)("div",{ref:I,className:"".concat(f().post," ").concat("giftguide"===E?f().postGG:""," ").concat(n),children:[(0,r.jsx)("a",{href:M,className:f().postImage,children:(0,r.jsx)("img",{className:f().thumbnail,src:(0,p.optimizeImage)(b),alt:w})}),(0,r.jsxs)("div",{className:f().textWrapper,children:[N.length>0&&(0,r.jsx)(v,{postTags:N,buzzId:s}),(0,r.jsx)("a",{href:M,children:(0,r.jsx)("h2",{className:f().title,children:h})})]})]})}function v(e){var t=e.postTags,n=e.buzzId,i=t.slice(0,2);return(0,r.jsx)("div",{className:f().postTags,children:i.length>0&&i.map((function(e,t){return(0,r.jsxs)("span",{className:f().tagWrapper,children:[(0,r.jsx)("a",{className:f().postTag,href:e.link,children:e.title}),t<i.length-1&&" , "]},"posttag-".concat(n,"-").concat(t))}))})}g.propTypes={buzzId:s().string,href:s().string,title:s().string,thumbnail:s().string,altText:s().string,origin:s().string,tracking:s().object},t.Z=g},36109:function(e,t,n){"use strict";var r=n(94776),i=n.n(r),o=n(52322),a=n(2784),u=n(88306),c=n(78194),s=n.n(c),l=n(68499),f=n(36595),p=n.n(f);function d(e,t,n,r,i,o,a){try{var u=e[o](a),c=u.value}catch(s){return void n(s)}u.done?t(c):Promise.resolve(c).then(r,i)}function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}t.Z=function(e){var t=e.offset,r=void 0===t?0:t,c=e.posts,f=void 0===c?[]:c,v=e.endpoint,m=e.setPosts,h=e.origin,y=void 0===h?"":h,b=e.tracking,_=void 0===b?{}:b,w=e.excludeIds,x=void 0===w?[]:w,j=(0,a.useContext)(u.z1).pageGroup,O=n(31965),P=O.buildApiUrl,S=O.fetchContent,N=(0,a.useState)(!1),E=N[0],k=N[1];(0,a.useEffect)((function(){var e=function(){var e,t=(e=i().mark((function e(){var t,n,r;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,n=P({endpoint:v}),e.next=5,S(n);case 5:r=null===(r=e.sent)||void 0===r||null===(t=r.results)||void 0===t?void 0:t.filter((function(e){return!x.includes(e.id)})),m(r),k(!0),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.log("Error fetching posts: ".concat(e.t0));case 14:case"end":return e.stop()}}),e,null,[[0,11]])})),function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){d(o,r,i,a,u,"next",e)}function u(e){d(o,r,i,a,u,"throw",e)}a(void 0)}))});return function(){return t.apply(this,arguments)}}();v&&m&&!E&&e()}),[v,m,P,S,x]);var I=f.slice(r,r+6).map((function(e,t){var n,i=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){g(e,t,n[t])}))}return e}({},_,{subunit_name:"post_list",subunit_type:"package",position_in_subunit:t+r+1,position_in_unit:t+1});return(0,o.jsx)("div",{className:s().post,children:(0,o.jsx)(l.Z,{buzzId:null===e||void 0===e?void 0:e.id,href:null===e||void 0===e?void 0:e.canonical_url,title:null===e||void 0===e?void 0:e.title,description:null===e||void 0===e?void 0:e.description,thumbnail:null===e||void 0===e||null===(n=e.images)||void 0===n?void 0:n.big,altText:null===e||void 0===e?void 0:e.altText,postTags:e.postTags,origin:y,tracking:i},t)},"pc-".concat(t))}));return I.length?(0,o.jsx)("div",{className:s().post_list,children:(0,o.jsx)("div",{className:"".concat(s().contentWrapper," ").concat("giftguide"===j?s().ggContentWrapper:p().contentWrapper),children:I})}):""}},94840:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(52322),i=n(16679),o=n(36109);function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){a(e,t,n[t])}))}return e}function c(e){return(0,r.jsx)(i.Z,{children:(0,r.jsx)(o.Z,u({},e))})}},72527:function(e,t,n){"use strict";n.d(t,{v:function(){return a}});var r=n(2784),i=n(49277),o=n(50240);function a(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];(0,i.Z)("tracking");var n=(0,r.useRef)(null);return(0,r.useEffect)((function(){var r=n.current;if(!r)return function(){};var i=t?(0,o.W3)(r,{},e):(0,o.Ev)(r,{},e);return function(){i()}}),[e,t]),n}},64435:function(e,t,n){"use strict";n.d(t,{R:function(){return a}});var r=n(2784),i=n(49277),o=n(50240);function a(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];(0,i.Z)("tracking");var n=(0,r.useRef)(null);return(0,r.useEffect)((function(){var r=n.current;if(!r)return function(){};var i=(0,o.aF)(r,{},e),a=t?(0,o.W3)(r,{},e):(0,o.Ev)(r,{},e);return function(){i(),a()}}),[e,t]),n}},39786:function(e,t,n){"use strict";n.d(t,{a:function(){return o}});var r=n(76635),i=n(2784),o=function(){var e=(0,i.useState)(""),t=e[0],n=e[1];(0,i.useEffect)((function(){var e=!0,t=(0,r.mapValues)({xs:"(max-width:639px)",sm:"(min-width:640px) and (max-width:831px)",md:"(min-width:832px) and (max-width:1023px)",lg:"(min-width:1024px)"},(function(e){return window.matchMedia(e)})),i=function(){if(e){var i=(0,r.findKey)(t,(function(e){return e.matches}));n(i)}};return(0,r.each)(t,(function(e){return e.addEventListener("change",i)})),i(),function(){e=!1,(0,r.each)(t,(function(e){return e.removeEventListener("change",i)}))}}),[]);var o="xs"===t,a="sm"===t;return{breakpoint:t,isMobile:o,isTablet:a,isTabletPro:"md"===t,isSmall:o||a,isDesktop:!o&&!a,isLargeScreen:"lg"===t}}},61957:function(e){"use strict";e.exports={addOrigin:function(e){var t=e.href,n=e.origin;if(!n)return t;try{var r=new URL(t);return r.searchParams.set("origin",n),r.toString()}catch(i){return t}}}},31965:function(e,t,n){"use strict";var r,i=(r=n(94776))&&r.__esModule?r:{default:r};function o(e,t,n,r,i,o,a){try{var u=e[o](a),c=u.value}catch(s){return void n(s)}u.done?t(c):Promise.resolve(c).then(r,i)}var a=function(e){return e.match("proxy/weaver")},u=function(e){return e.match("proxy/posts-and-products")},c=function(e){return e.match("proxy/get-weaver-posts-and-search-products")},s=function(){var e,t=(e=i.default.mark((function e(t){var n;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,fetch(t);case 2:return e.next=4,e.sent.json();case 4:return n=e.sent,e.abrupt("return",n);case 6:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function u(e){o(a,r,i,u,c,"next",e)}function c(e){o(a,r,i,u,c,"throw",e)}u(void 0)}))});return function(e){return t.apply(this,arguments)}}();e.exports={isWeaverPostsAndSearchProductsEndpoint:c,isWeaverEndpoint:a,isCommerceSearchEndpoint:u,buildApiUrl:function(e){var t=e.endpoint,n=e.currentPage,r=void 0===n?1:n,i=e.pageSize,o=void 0===i?36:i;if("undefined"===typeof window)return null;var s=new URL(window.location.origin+t);return c(t)?(s.searchParams.set("startIndex",o*r),s.searchParams.set("size",o),s.searchParams.set("page",r),s.searchParams.set("page_size",o),s.searchParams.set("map_post_data",1)):a(t)?(s.searchParams.set("page",r),s.searchParams.set("page_size",o),s.searchParams.set("map_post_data",1)):u(t)&&(s.searchParams.set("startIndex",o*r),s.searchParams.set("size",o)),s},fetchContent:s}},7723:function(e){e.exports={headerWrapper:"gg_feed_header_headerWrapper__TIBrw",headerOverlay:"gg_feed_header_headerOverlay__09mrp",headerImg:"gg_feed_header_headerImg__ZWeIl",headerContent:"gg_feed_header_headerContent__T8y4g",headerContentCenter:"gg_feed_header_headerContentCenter__XsJhl",headerTitle:"gg_feed_header_headerTitle__Lkq6t",headerDescription:"gg_feed_header_headerDescription__SjAuD",shoppyBanner:"gg_feed_header_shoppyBanner__NivUi"}},31706:function(e){e.exports={navMenu:"styles_navMenu__8VnqM",mobileOverlay:"styles_mobileOverlay__VBrCr",navWrapper:"styles_navWrapper__oou_l",navMenuItemWrapper:"styles_navMenuItemWrapper__rWGv5",navMenuItem:"styles_navMenuItem__UsZwX",selected:"styles_selected__8_3oQ",navMenuItemTitle:"styles_navMenuItemTitle__7aDAX",arrowDown:"styles_arrowDown__oKsTX",arrowUp:"styles_arrowUp__EC_fz",subNavWrapper:"styles_subNavWrapper__BKd9N",subNavMenuWrapper:"styles_subNavMenuWrapper__SIZrF",hideSubMenu:"styles_hideSubMenu__qLaxK",colWrapper:"styles_colWrapper__F6SUj",col:"styles_col__7TnE1",subNavMenuItem:"styles_subNavMenuItem__5x_wP",subNavMenuItemLink:"styles_subNavMenuItemLink__zOdv0"}},12764:function(e){e.exports={pageOverlay:"pageOverlay_pageOverlay__LaUzF",fixed:"pageOverlay_fixed__ahRLu"}},70733:function(e){e.exports={post:"post_post__37JY0",postGG:"post_postGG__H5CN1",thumbnail:"post_thumbnail__932uO",title:"post_title__hU5uq",textWrapper:"post_textWrapper__huErG",postImage:"post_postImage__YOQ_o",tagWrapper:"post_tagWrapper__zrebg",postTags:"post_postTags__Wm_UV",postTag:"post_postTag__tyC17"}},78194:function(e){e.exports={post_list:"post_list_post_list__nMLp8",contentWrapper:"post_list_contentWrapper__iGKie",post:"post_list_post__DQKII",ggContentWrapper:"post_list_ggContentWrapper__yrbw2"}},36595:function(e){e.exports={onlyScreenReader:"index_onlyScreenReader__NDcJS",content:"index_content__k_CP2",spacer72:"index_spacer72__pbyPT",contentGG:"index_contentGG__Gb7_1",contentWrapper:"index_contentWrapper__U6n7t",label:"index_label__n5qh7",sectionLink:"index_sectionLink__T5wK1",rightCaret:"index_rightCaret__tmfcg",subNavWrapper:"index_subNavWrapper__3JdeA",adSpacing:"index_adSpacing__3yWUu"}},60957:function(e){e.exports="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzgiIGhlaWdodD0iMzUiIHZpZXdCb3g9IjAgMCAzOCAzNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggaWQ9InBhdGgiIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMzcuNzk5MiAxNi40NTY3TDM1LjMxMTQgMEwxOS45NDQgNi4wMjE5N0wyNi4wNzQ0IDkuNTg4NzZMMjAuMzA2OCAxOS42MDYxTDEwLjQwMzMgMTQuMDI4M0wwLjUgMzAuOTg5NEw1Ljk2NDA0IDM0LjIxNDdMMTIuNjA0MSAyMi41NjU4TDIyLjUwNzYgMjguMjk1NEwzMS41MDA0IDEyLjYyNDNMMzcuNzk5MiAxNi40NTY3WiIgZmlsbD0iYmxhY2siLz4KPC9zdmc+Cg=="}}]);
//# sourceMappingURL=862-36989a28007dad1a.js.map