(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[941],{37963:function(e,t,n){"use strict";var r=n(52322),o=n(2784),a=n(13980),i=n.n(a),c=n(48243),l=n(61360),s=n(45201),u=n(30353),d=n(26002),f=n(74967),p=n(60565),v=n(39283),m=n(29875),g=n(2706);function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){h(e,t,n[t])}))}return e}function y(e){return function(e){if(Array.isArray(e))return b(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return b(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return b(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function x(e){var t=e.children,n=e.pageName,a=(0,d.I)({cluster:u.ov}),i=(0,p.E)(),b=i.pageInfo,h=i.trackExperimentActive,x=(0,o.useContext)(c.z1).path,j=(0,o.useRef)(null),w=(null===b||void 0===b?void 0:b.page_edition)||"en-us",k=(0,v.Y)({isFeedUI:!0,isBPage:!1,isHomePage:"home"===n,isNewBPage:!1,isFeedPage:!0,isBFO:!0,isBFN:!1,localizationCountry:w,userCountry:(0,g.isServer)()?"":(0,m.pP)(),edition:w,isAdPost:function(){return!1}}),O=(0,o.useMemo)((function(){return{userId:a,data:_({},b)}}),[a]),C=(0,s.Z)(_({abeagleHost:u.in,experimentConfig:y(l.ZP).concat(y(k)),source:"buzz_web"},O));j.current&&j.current.loaded===C.loaded||(j.current=_({},C,{path:x})),j.current.stale=j.current.path!==x;var P=j.current.loaded,N=C.eligible?Object.keys(C.eligible).join("|"):"";(0,o.useEffect)((function(){if(P&&N.length){var e=[];Object.keys(C.eligible).forEach((function(t){var n=C.eligible[t];if(n&&n.value){var r=n.id,o=n.version,a=n.value,i=n.variant_id;e.push([t,r,o,a,i].join("|"))}})),h({experiment_id:e})}}),[x,P,N]);return(0,r.jsx)(c.WN.Provider,{value:{experiments:j.current,getFeatureFlagValue:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"on";return(0,f.F7)(C,e,t)},getExperimentValue:function(e,t){return(0,f.ts)(C,e,t)}},children:t})}x.propTypes={children:i().oneOfType([i().arrayOf(i().node),i().node])},t.Z=x},29169:function(e,t,n){"use strict";var r=n(52322),o=n(2784),a=n(13980),i=n.n(a),c=n(81550),l=n(34014),s=n(14007),u=n(6294),d=n(2706),f=n(33657),p=n(48243);function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e){var t=e.className,n=void 0===t?"":t,a=e.config,i=e.getBFPLayout,c=void 0===i?null:i,m=e.mode,g=e.onAdRender,b=e.onAdViewed,h=e.stickyWithinPlaceholder,_=void 0!==h&&h,y=e.type,x=(0,o.useState)("loading"),j=x[0],w=x[1],k=(0,o.useContext)(p.oF),O=(0,o.useContext)(p.z1),C=O.adsDisabled,P=O.pagePath,N=!(0,d.isServer)()&&window.matchMedia&&(0,u.tq)()?"x1.0":"x1.25",S=[_&&"ad-sticky-within-placeholder",n].filter(Boolean).join(" "),T=(0,o.useMemo)((function(){var e=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){v(e,t,n[t])}))}return e}({},a||l.Z[y]);return"renderLookahead"in e||(e.renderLookahead=N),/^awareness/.test(e.adPos)&&(e.adPos="awareness"),e}),[a,y,N]),A=(0,o.useCallback)((function(e){e.type&&w("empty"===e.type?"unfilled":"loaded"),"function"===typeof g&&g(e)}),[T,g]);return(0,o.useEffect)((function(){w("loading")}),[T]),C?null:(0,r.jsx)(s.Z,{adUnitUtils:f,config:T,pageId:P,mode:m,onAdViewed:b,onAdRender:A,getBFPLayout:c,stickyManager:k,className:"".concat(S," Ad--").concat(j)})}m.propTypes={type:i().string,config:i().object,mode:i().oneOf(["active","preload"]),onAdViewed:i().func,onAdRender:i().func,pageLanguage:i().string,className:i().string},t.Z=(0,c.Z)(m,{onError:function(e){return console.error("Error caught in Ads",e)}})},35464:function(e,t,n){"use strict";n.d(t,{Z:function(){return O}});var r=n(94776),o=n.n(r),a=n(52322),i=n(2784),c=n(13980),l=n.n(c),s=n(33565),u=n(59855),d=n(56758),f=n(30353),p=n(29875);function v(e,t,n,r,o,a,i){try{var c=e[a](i),l=c.value}catch(s){return void n(s)}c.done?t(l):Promise.resolve(l).then(r,o)}function m(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){v(a,r,o,i,c,"next",e)}function c(e){v(a,r,o,i,c,"throw",e)}i(void 0)}))}}function g(){return p.Qk.get("bf-geo-country")||"US"}function b(){return(b=m(o().mark((function e(t){var n,r,a,i,c,l;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.abeagle,r=t.pageName,a=t.tracking,i={edition:"en-us",language:"en",country:"us"},c="en_us",l={adSizes:d.J7,programmaticSizes:d.PP,destination:"buzzfeed",isBFN:!1,isE2ETest:!1,isFeed:!1,isFeedPage:!0,isFeedpager:!1,isHomePage:"home"===r,isNewsPost:!1,isBFO:!0,isBPage:!1,isDev:"dev"===f.ov,isProd:"prod"===f.ov,pageFilter:null,pageFilters:{},pageMainFilter:null,type:f.ov,webRoot:f._H,allPageSections:[],author:null,cmsTags:[],hasQuiz:!1,isAsIs:!1,isPharmaceutical:!1,isWidePost:!1,pageCategory:"",pageId:"home",pageName:r,isAdPost:function(){return!1},localization:i,locale:c.locale,localizationCountry:c.country,userCountry:g()},e.abrupt("return",{env:l,abeagle:n,localization:i,stickyRegistry:u.Z,tracking:a});case 5:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var h=n(48243),_=n(91254),y=n(15971),x=n(61360);function j(e,t,n,r,o,a,i){try{var c=e[a](i),l=c.value}catch(s){return void n(s)}c.done?t(l):Promise.resolve(l).then(r,o)}function w(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){j(a,r,o,i,c,"next",e)}function c(e){j(a,r,o,i,c,"throw",e)}i(void 0)}))}}function k(){return(k=w(o().mark((function e(t){var n,r,a,i,c,l,s;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=t.pageName,r=t.pagePath,a=t.abeagle,i={},e.t0=n,e.next="home"===e.t0?5:"standard_page"===e.t0?7:"topic"===e.t0?10:13;break;case 5:case 13:return i.destination=["newhp"],e.abrupt("break",15);case 7:return i.standard_page=(null===window||void 0===window||null===(c=window.location)||void 0===c?void 0:c.pathname.split("/").slice(-1)[0])||"",e.abrupt("break",15);case 10:return i.destination=["topicpage"],i.topic=r,e.abrupt("break",15);case 15:return l=x.zx.map((function(e){return e.name})),e.next=18,Promise.all(l.map((function(e){return a.getExperimentVariant(e,{rejectErrors:!1,defaultVariantIfUnbucketed:null})})));case 18:return s=e.sent,i.abtest=l.map((function(e,t){return"".concat(e,"|").concat(s[t])})),e.abrupt("return",i);case 21:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function O(e){var t=e.edition,n=void 0===t?"en-us":t,r=e.pagePath,o=void 0===r?"hp":r,c=e.pageName,l=e.children,u=(0,_.Z)(),d=(0,y.Z)(),f=(0,i.useContext)(h.z1).adsDisabled,p=(0,i.useCallback)((function(){return function(e){return b.apply(this,arguments)}({edition:n,abeagle:u,tracking:d,pageName:c})}),[n,u,d,c]),v=(0,i.useCallback)((function(){return function(e){return k.apply(this,arguments)}({pageName:c,pagePath:o,abeagle:u})}),[u]);return(0,a.jsx)(s.Z,{pageId:o,pageLanguage:n,adsEnabled:!f,getPageContext:p,getPageTargeting:v,children:l})}O.propTypes={edition:l().string,pagePath:l().string,children:l().oneOfType([l().arrayOf(l().node),l().node])}},1013:function(e,t,n){"use strict";n.d(t,{B:function(){return u}});var r=n(52322),o=n(89809),a=n(56758),i=n(34014),c=n(4083),l=n(29169);function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var u=function(e){var t,n=e.className,u=void 0===n?"":n,d=e.config,f=e.renderPlaceholderOnly,p=void 0!==f&&f,v=e.type;t=d||function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){s(e,t,n[t])}))}return e}({},i.Z[v]);var m="Ad--feed ".concat(u).trim();return t.size=o.Z.exclude(t.size,a.J7.PROGRAMMATIC_SUPER_LEADERBOARD,a.J7.PROGRAMMATIC_BILLBOARD),(0,r.jsx)(c.Z,{renderChildren:!p,className:"ad-placeholder-feed-story",children:(0,r.jsx)(l.Z,{config:t,className:m,stickyWithinPlaceholder:!0})})}},4083:function(e,t,n){"use strict";var r=n(52322),o=n(13980),a=n.n(o),i=n(2784),c=n(48243),l=function(e){var t=e.className,n=void 0===t?"":t,o=e.children,a=e.renderChildren,l=void 0===a||a,s=(0,i.useContext)(c.oF),u=(0,i.useContext)(c.z1).adsDisabled,d=(0,i.useState)(0),f=d[0],p=d[1];return(0,i.useEffect)((function(){var e=null===s||void 0===s?void 0:s.getTopmostPosition();p(Number.isFinite(e)?e:0)}),[s]),u?null:(0,r.jsx)("aside",{className:"ad-placeholder ".concat(n),style:{"--ad-sticky-top":"".concat(f,"px")},children:l&&o})};l.propTypes={className:a().string,children:a().node,renderChildren:a().bool},t.Z=l},97143:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(2784),o=n(75951),a=n(74967),i=n(48243);function c(e){var t=e.isCommunity,n=(0,r.useContext)(i.WN).experiments;return t&&o.jQ.fetchCCPAValue(),(0,r.useEffect)((function(){if(n.loaded&&!n.stale){var e=(0,a.F7)(n,o.Yx.name);o.hi.configure({useFallback:!e})}}),[n.loaded,n.stale]),(0,r.useEffect)((function(){o.hi.init()}),[]),null}},8520:function(e,t,n){"use strict";n.d(t,{Z:function(){return W}});var r=n(94776),o=n.n(r),a=n(52322),i=n(2784),c=n(13980),l=n.n(c),s=n(81550),u=n(42630),d=n.n(u);function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function p(e){return function(e){if(Array.isArray(e))return f(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return f(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var v=function(e,t){var n;e>t&&(e=(n=[t,e])[0],t=n[1]);return Math.floor(Math.random()*(t-e+1))+e},m=function(e){var t=e.className,n=void 0===t?"":t,r=e.emoji,o=e.onTransitionEndComplete,c=e.range,l=void 0===c?[10,15]:c,s=(0,i.useRef)(v.apply(void 0,p(l))),u=(0,i.useRef)(function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.degreeMax,r=void 0===n?45:n,o=t.degreeMin,a=void 0===o?-45:o,i=t.distributionFactor,c=void 0===i?1:i,l=[],s=r-a,u=0;u<e;u++){var d=Math.floor((Math.random()*s*c+u*s/e)%s)+a;l.push(d)}return l}(s.current)),f=(0,i.useRef)(null),m=(0,i.useState)(""),g=m[0],b=m[1],h=(0,i.useState)(0),_=h[0],y=h[1];(0,i.useEffect)((function(){window.matchMedia("(prefers-reduced-motion: reduce)").matches?"function"===typeof o&&o():b(d().animate)}),[o]),(0,i.useEffect)((function(){_===s.current&&"function"===typeof o&&"function"===typeof requestIdleCallback&&requestIdleCallback(o)}),[_,o]),(0,i.useEffect)((function(){return function(){f.current&&cancelIdleCallback(f.current)}}),[]);return(0,a.jsx)("div",{className:"".concat(n," ").concat(d().container," ").concat(g),"aria-hidden":"true",children:(0,a.jsx)("ul",{className:d().emojis,onAnimationEnd:function(e){"function"===typeof requestIdleCallback&&(f.current=requestIdleCallback((function(){_<s.current&&e.animationName===d().fadeInOut&&y((function(e){return e+1}))})))},children:Array.from({length:s.current}).map((function(e,t){var n=v(25,25*s.current),o=v(25,100)/100,i=v(10,85),c=i/85,l=Math.pow(1+c,1.5);return(0,a.jsx)("li",{className:d().emoji,style:{"--confetti-rotate":"".concat(u.current[t],"deg"),"--confetti-distance-horizontal":"".concat(i,"px"),"--confetti-scaling-factor":l,"--confetti-delay":"".concat(n,"ms"),"--confetti-scale":o,zIndex:i},children:(0,a.jsx)("div",{className:d().verticalLayer,children:(0,a.jsx)("div",{className:d().scaleLayer,children:(0,a.jsx)("div",{className:d().opacityLayer,children:r})})})},t)}))})})};m.propTypes={className:l().string,emoji:l().string.isRequired,onTransitionEndComplete:l().func,range:l().arrayOf(l().number)};var g=(0,s.Z)(m,{onError:function(){}}),b=n(9845),h=n(98286),_=n(28964),y=n(20125),x=function(e){var t=e.className;return(0,a.jsxs)("svg",{"aria-label":"Add emoji reaction",className:t,height:"20",role:"img",width:"22",xmlns:"http://www.w3.org/2000/svg",fill:"#757575",preserveAspectRatio:"xMidYMid meet",viewBox:"0 0 22 20",children:[(0,a.jsx)("path",{d:"M5.43 7.3a1.37 1.37 0 1 1 1.94 1.93A1.37 1.37 0 0 1 5.43 7.3Zm5.49 0a1.37 1.37 0 1 1 1.94 1.93 1.37 1.37 0 0 1-1.94-1.94Z"}),(0,a.jsx)("path",{fillRule:"evenodd",d:"M9.15 13.3c1.2 0 2.2-.84 2.45-1.96a.91.91 0 1 1 1.79.41 4.35 4.35 0 0 1-8.48 0 .91.91 0 1 1 1.79-.4 2.52 2.52 0 0 0 2.45 1.94Z",clipRule:"evenodd"}),(0,a.jsx)("path",{d:"M2.68 4.31a9.15 9.15 0 0 1 10.1-1.93c-.13.24-.2.52-.2.81l.01 1.13a7.32 7.32 0 1 0 3.71 4.9c.**********.47.07h1.4A9.14 9.14 0 1 1 2.69 4.31Z"}),(0,a.jsx)("path",{d:"M16.77 7.58V5.1H14.3V3.2h2.48V.71h1.9v2.47h2.49v1.9h-2.48v2.49h-1.9Z"})]})},j=n(97196),w=n(30353),k=n(83816),O=n.n(k);function C(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function P(e,t,n,r,o,a,i){try{var c=e[a](i),l=c.value}catch(s){return void n(s)}c.done?t(l):Promise.resolve(l).then(r,o)}function N(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){P(a,r,o,i,c,"next",e)}function c(e){P(a,r,o,i,c,"throw",e)}i(void 0)}))}}function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function T(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){S(e,t,n[t])}))}return e}function A(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function I(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a=[],i=!0,c=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(l){c=!0,o=l}finally{try{i||null==n.return||n.return()}finally{if(c)throw o}}return a}}(e,t)||D(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(e){return function(e){if(Array.isArray(e))return C(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||D(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(e,t){if(e){if("string"===typeof e)return C(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?C(e,t):void 0}}var R=(0,y.a)({namespace:"localUserEmojis"}),Z=E(h.T).concat(E(h.n)),L=function(e){var t=e.contentType,n=e.contentId,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=new URL("".concat(w._H,"/content-reactions-api/v2/emoji/").concat(t,"/").concat(n));return o.searchParams.set("edition","en-us"),Object.entries(r).forEach((function(e){var t=I(e,2),n=t[0],r=t[1];o.searchParams.set(n,r)})),o.toString()},M=function(){var e=N(o().mark((function e(t){var n,r,a,i;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.contentType,r=t.contentId,a=L({contentType:n,contentId:r}),e.prev=2,e.next=5,fetch(a,{credentials:"include"});case 5:if((i=e.sent).ok){e.next=8;break}return e.abrupt("return",null);case 8:return e.abrupt("return",i);case 11:return e.prev=11,e.t0=e.catch(2),console.error(e.t0),e.abrupt("return",null);case 15:case"end":return e.stop()}}),e,null,[[2,11]])})));return function(t){return e.apply(this,arguments)}}(),F=function(){var e=N(o().mark((function e(t){var n,r,a,i,c,l,s,u;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.contentId,r=t.contentType,a=t.pageId,i=t.pageType,c=void 0===i?"feed":i,l=t.reaction,s=L({contentType:r,contentId:n},{page_type:c,page_id:a,reaction:l,client_uuid:(0,b.TQ)()}),u={method:"POST",credentials:"include"},e.prev=3,e.next=6,fetch(s,u);case 6:e.next=11;break;case 8:e.prev=8,e.t0=e.catch(3),console.error(e.t0);case 11:case"end":return e.stop()}}),e,null,[[3,8]])})));return function(t){return e.apply(this,arguments)}}(),z=function(e){var t=e.reactions,n=void 0===t?[]:t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=r.isSponsored,a=void 0!==o&&o,i=r.max,c=void 0===i?2:i;return n.reduce((function(e,t){var n=t.count,r=t.label;return a&&h.n.includes(r)||n>0&&e.length<c&&e.push(r),e}),[])},B=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.className,n=void 0===t?"":t,r=e.clientSideEnrichment,c=void 0!==r&&r,l=e.contentId,s=e.contentType,u=void 0===s?"content-object":s,d=e.data,f=void 0===d?{}:d,p=e.isSponsored,v=void 0!==p&&p,m=e.isTrackable,b=void 0!==m&&m,y=e.trackingData,w=void 0===y?{}:y,k=(0,i.useState)(z(f,{isSponsored:v})),C=k[0],P=k[1],S=(0,j.EF)().trackContentAction,I=(0,i.useState)(null),E=I[0],D=I[1],L=(0,i.useState)(f.total_reactions||0),B=L[0],W=L[1],H=(0,i.useState)(!1),V=H[0],U=H[1],q=w.context_page_id,J=w.context_page_type,G=(0,i.useRef)(),Q=(0,i.useRef)(),K=(0,i.useMemo)((function(){var e;return null===f||void 0===f||null===(e=f.reactions)||void 0===e?void 0:e.reduce((function(e,t){return e[t.label]=t.count||"",t.label===E&&(e[t.label]=t.count+1),e}),{})}),[E]);(0,i.useLayoutEffect)((function(){V&&Q.current.classList.remove(O().clearAnimation)}),[V]),(0,i.useEffect)((function(){var e=R.get("".concat(u,":").concat(l));if(e){var t=e.split(":").pop();(!v&&Z.includes(t)||v&&h.T.includes(t))&&D(t)}}),[l,u]),(0,i.useEffect)((function(){var e=function(){var e=N(o().mark((function e(){var t,n,r,a,i,s,d,p,m,g;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(c&&l&&(!c||!(null===(t=f.reactions)||void 0===t?void 0:t.length))){e.next=3;break}return e.abrupt("return");case 3:return e.prev=3,e.next=7,M({contentId:l,contentType:u});case 7:return r=e.sent,e.next=11,null===r||void 0===r||null===(n=r.json)||void 0===n?void 0:n.call(r);case 11:if(e.t1=a=e.sent,e.t0=null!==e.t1,!e.t0){e.next=15;break}e.t0=void 0!==a;case 15:if(!e.t0){e.next=19;break}e.t2=a,e.next=20;break;case 19:e.t2={};case 20:i=e.t2,s=i.reactions,d=void 0===s?[]:s,p=i.total_reactions,m=void 0===p?0:p,g=z({reactions:d},{isSponsored:v}),m>=5&&g.length&&(W(m),P(g)),e.next=32;break;case 29:e.prev=29,e.t3=e.catch(3),console.error(e.t3);case 32:case"end":return e.stop()}}),e,null,[[3,29]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var Y=function(){var e=G.current,t=e.getBoundingClientRect(),n=document.documentElement.clientWidth;t.right>n?e.style.setProperty("--left-offset","calc(".concat(Math.ceil(t.right-n),"px + 1rem)")):t.left<0&&e.style.setProperty("--left-offset","calc(".concat(Math.floor(t.left),"px - 1rem)"))},$=function(){var e=G.current;e.open||(e.removeAttribute("style"),e.show(),Y(),requestAnimationFrame((function(){document.addEventListener("click",ee,{once:!0})})))},X=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).onAnimationEnd,t=G.current,n=O().animateClose;t.addEventListener("animationend",(function r(o){[O().bounceOut,O().fadeOut].includes(o.animationName)&&(t.close(),t.classList.remove(n),t.removeEventListener("animationend",r),"function"===typeof e&&e())})),t.classList.add(n)},ee=function(e){var t=G.current;t&&!t.contains(e.target)&&X()},te=function(e){if("button"===e.target.tagName.toLowerCase()){X({onAnimationEnd:function(){U(!0)}});var t=e.target.innerText.trim();if(b){w.target_content_id,w.target_content_type,w.target_content_url;var n=A(w,["target_content_id","target_content_type","target_content_url"]);S(T({},n,{item_type:"button",action_type:"react",action_value:t}))}var r=T({},K);E?r[E]>0&&(r[E]=r[E]-1):(B>=5&&W(B+1),F({contentId:l,contentType:u,pageId:q,pageType:J,reaction:t})),r[t]=r[t]+1,D(t),R.set("".concat(u,":").concat(l,":").concat(t)),document.removeEventListener("click",ee)}},ne=function(){return U(!1)},re=function(e){e.animationName===O().bounceIn&&Q.current.classList.add(O().clearAnimation)},oe=!!(null===C||void 0===C?void 0:C.length)&&B>=5;return(0,a.jsxs)("section",{className:"".concat(n," ").concat(O().reactions),children:[(0,a.jsxs)("ul",{"aria-label":"Emoji Reactions","aria-controls":"dialog",className:O().list,onClick:$,role:"button",tabIndex:"0",children:[C.map((function(e,t){return e!==E&&(0,a.jsx)("li",{className:O().listItem,children:e},"top-emoji-".concat(t))})),E&&(0,a.jsxs)("li",{className:"".concat(O().listItem," ").concat(O().userReaction),children:[(0,a.jsx)("span",{className:O().openEmojiDialogBtn,onAnimationEnd:re,ref:Q,children:E}),V&&(0,a.jsx)(g,{className:O().confetti,emoji:E,onTransitionEndComplete:ne})]}),oe&&(0,a.jsx)("li",{className:O().total,children:(0,a.jsx)("span",{"aria-label":"Total Emoji Reactions:",children:(0,_.LA)(B)})}),!E&&(0,a.jsx)("li",{className:O().addNewEmoji,children:(0,a.jsxs)("span",{className:O().openEmojiDialogBtn,children:[(0,a.jsx)(x,{}),!(null===C||void 0===C?void 0:C.length)&&(0,a.jsx)("span",{className:O().react,"aria-label":"React",children:"React"})]})})]}),(0,a.jsx)("dialog",{"aria-modal":"true",className:O().dialog,ref:G,children:(0,a.jsxs)("ul",{"aria-label":"Select an emoji reaction",className:O().list,onClick:te,children:[(0,a.jsx)(a.Fragment,{children:h.T.map((function(e){return(0,a.jsxs)("li",{children:[(0,a.jsx)("button",{disabled:E===e,children:e}),oe&&(0,a.jsx)("span",{className:O().reactionCount,children:K[e]})]},e)}))}),(0,a.jsx)(a.Fragment,{children:!v&&h.n.map((function(e){return(0,a.jsxs)("li",{children:[(0,a.jsx)("button",{disabled:E===e,children:e}),oe&&(0,a.jsx)("span",{className:O().reactionCount,children:K[e]})]},e)}))})]})})]})};B.propTypes={className:l().string,clientSideEnrichment:l().bool,contentId:l().number.isRequired,contentType:l().string,data:l().object.isRequired,isSponsored:l().bool,isTrackable:l().bool,trackingData:l().object};var W=B},36774:function(e,t,n){"use strict";var r=n(52322),o=n(2784),a=n(39852),i=n(48243),c=n(5819),l=n(8996),s=n.n(l);function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var d=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.split("/");return(null===t||void 0===t?void 0:t.length)>1&&t[0]===t[1]?"square":"standard"};t.Z=function(e){var t,n,l,f,p,v,m,g,b,h,_,y=e.className,x=void 0===y?"":y,j=e.imageRatio,w=void 0===j?"3/2":j,k=e.imageRatioDesktop,O=void 0===k?"3/2":k,C=e.imageRatioTablet,P=void 0===C?"3/2":C,N=e.index,S=void 0===N?0:N,T=e.isPrimary,A=void 0===T||T,I=e.isTrackable,E=void 0!==I&&I,D=e.item,R=e.showTagLabel,Z=void 0!==R&&R,L=e.trackingData,M=void 0===L?{}:L,F=e.origin,z=void 0===F?"":F,B=D||{},W=B.content,H=B.id,V=B.object_type,U=(0,o.useContext)(i.z1).pageType,q=void 0===U?"":U;if(!W)return null;var J=W.trackingData,G=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){u(e,t,n[t])}))}return e}({},{subunit_type:"component",subunit_name:"".concat(V,"|").concat(H),position_in_subunit:0},J,{item_type:"card",item_name:null===W||void 0===W?void 0:W.id,target_content_id:null===W||void 0===W?void 0:W.id,target_content_type:"buzz"},M),Q=[s().splashCard,A?s().primaryPost:s().secondaryPost,S>0&&s().hasIndex,!(null===W||void 0===W?void 0:W.hideReactions)&&s().hasReactions,s()[x],s()[q]].filter(Boolean).join(" "),K=d(w),Y=d(P),$=d(O);return(0,r.jsxs)(a.Z,{className:Q,isTrackable:E,trackingData:G,children:[(0,r.jsxs)(a.Z.Link,{url:(0,c.b)(W.url,z),isTrackable:E,className:s().linkWrap,trackingData:G,children:[(0,r.jsx)("figure",{className:s().thumbnail,children:(0,r.jsxs)("picture",{children:[K!==Y&&(0,r.jsx)("source",{srcSet:null===(t=W.thumbnail)||void 0===t||null===(n=t[Y])||void 0===n?void 0:n.url,media:"(min-width: 40rem)"}),Y!==$&&(0,r.jsx)("source",{srcSet:null===(l=W.thumbnail)||void 0===l||null===(f=l[$])||void 0===f?void 0:f.url,media:"(min-width: 64rem)"}),(0,r.jsx)(a.Z.Thumbnail,{thumbnail:null===(p=W.thumbnail)||void 0===p||null===(v=p[K])||void 0===v?void 0:v.url,alt:null===(m=W.thumbnail)||void 0===m||null===(g=m[K])||void 0===g?void 0:g.alt,ratio:w,ratioTablet:P,ratioDesktop:O})]})}),(0,r.jsxs)("div",{className:s().textWrapper,children:[Z&&(0,r.jsx)(a.Z.InfoLabel,{className:s().tagLabel,label:"Popular"}),S>0&&(0,r.jsx)(a.Z.IndexLabel,{className:s().index,children:S}),(0,r.jsx)("h3",{className:s().heading,children:W.title})]})]}),!(null===W||void 0===W?void 0:W.hideReactions)&&(0,r.jsxs)("div",{className:s().statsWrapper,children:[(0,r.jsx)(a.Z.Reactions,{className:s().reactions,contentId:Number(W.id),contentType:"buzz",data:W.contentReactions,isTrackable:E,trackingData:G}),(0,r.jsx)(a.Z.Stats,{className:s().stats,commentsClassName:s().statsLink,commentsCount:(null===W||void 0===W||null===(b=W.comments)||void 0===b?void 0:b.count)>4?null===W||void 0===W||null===(h=W.comments)||void 0===h?void 0:h.countFormatted:null,commentLink:W.url,isTrackable:E,trackingData:G,viewsCount:null===W||void 0===W||null===(_=W.pageviews)||void 0===_?void 0:_.countFormatted})]})]})}},39852:function(e,t,n){"use strict";n.d(t,{Z:function(){return y}});var r=n(52322),o=n(2784),a=n(20238),i=n(58599),c=n(8520),l=n(52003),s=n.n(l),u=n(97196),d=n(64319),f=function(e){var t=e.className,n=void 0===t?"":t;return(0,r.jsx)("svg",{width:"18",height:"21",viewBox:"0 -3 18 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:n,children:(0,r.jsx)("path",{d:"M0.999999 3.7907C0.999999 2.24944 2.30245 1 3.90909 1H14.0909C15.6976 1 17 2.24944 17 3.7907L17 9.37209C17 10.9134 15.6976 12.1628 14.0909 12.1628H12.6364V16L6.09093 12.1628H3.90908C2.30244 12.1628 0.999999 10.9134 0.999999 9.37209C0.999999 7.51163 0.999999 5.65116 0.999999 3.7907Z",stroke:"#757575",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})},p=function(e){var t=e.className,n=void 0===t?"":t;return(0,r.jsx)("svg",{viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:n,children:(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.4378 9.18761C12.8078 15.4331 5.18809 15.4514 2.55809 9.19762C2.52214 9.11213 2.52247 8.89639 2.55809 8.81165C5.18809 2.55783 12.808 2.56309 15.4378 8.80873C15.4788 8.9062 15.4793 9.08906 15.4378 9.18761ZM11.25 9.5625C11.25 10.8051 10.1178 11.8125 9.01202 11.8125C7.90621 11.8125 6.75 10.8051 6.75 9.5625C6.75 8.31986 7.90621 7.3125 9.01202 7.3125C10.1178 7.3125 11.25 8.31986 11.25 9.5625Z",fill:"#F0FA81",stroke:"none",strokeWidth:"0.75",strokeLinejoin:"round"})})},v=n(48243),m=n(69327),g=n.n(m);function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){b(e,t,n[t])}))}return e}var _=function(e){var t=e.children,n=e.className,o=e.isTrackable,a=void 0!==o&&o,i=e.trackingData,c=void 0===i?{}:i,l=(0,u.Si)({trackingData:c}).setObservable;return(0,r.jsx)("article",{className:n||"",ref:function(e){return t=e,void(a&&l(t));var t},children:t})};_.Link=function(e){var t=e.children,n=e.url,o=e.className,a=void 0===o?"":o,c=e.isTrackable,l=void 0===c||c,s=e.trackingData,u=void 0===s?{}:s,d=e.forceNewBrowserTab,f=void 0!==d&&d;return n?l?(0,r.jsx)(i.Z,{href:n,className:a||"",commonTrackingData:u,forceNewBrowserTab:f,children:t}):(0,r.jsx)("a",{href:n,className:a||"",target:f?"_blank":"_self",rel:f?"noreferrer":void 0,children:t}):null},_.Reactions=function(e){var t=e.className,n=void 0===t?"":t,a=e.contentId,i=e.contentType,l=void 0===i?"buzz":i,s=e.data,u=e.isTrackable,d=void 0!==u&&u,f=e.trackingData,p=void 0===f?{}:f,m=(0,o.useContext)(v.Ui),g=(0,o.useContext)(v.z1).isSponsored,b=void 0!==g&&g;return(null===s||void 0===s?void 0:s.reactions)?(0,r.jsx)(c.Z,{className:n,contentId:a,contentType:l,data:s,isSponsored:b,isTrackable:d,trackingData:h({},m,p)}):null},_.InfoLabel=function(e){var t=e.className,n=void 0===t?"":t,o=e.label,a=void 0===o?"":o,i=e.createdAt,c=void 0===i?"":i;return c||a?(0,r.jsxs)("header",{className:"".concat(g().info," ").concat(n||""),children:[a&&(0,r.jsxs)("span",{className:g().tag,children:[(0,r.jsx)(d.O,{class:"xs-mr1"})," ",a]}),c&&(0,r.jsx)("time",{dateTime:c,children:c})]}):null},_.Stats=function(e){var t=e.className,n=void 0===t?"":t,o=e.viewsCount,c=void 0===o?"":o,l=e.isTrackable,s=void 0!==l&&l,u=e.commentsClassName,d=void 0===u?"":u,v=e.commentsCount,m=void 0===v?"":v,b=e.forceCommentLink,_=void 0!==b&&b,y=e.commentLink,x=void 0===y?"":y,j=e.trackingData,w=void 0===j?{}:j;if(!(null===c||void 0===c?void 0:c.length)&&!(null===m||void 0===m?void 0:m.length))return null;var k=function(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(f,{}),(0,r.jsx)("span",{className:"screen-reader-only",children:" The amount of the post comments: "}),m]})},O=function(){var e=(0,a.F1)(x,{open_comments:"on"});if(!x.length)return(0,r.jsx)(k,{});if(x.length&&!s)return(0,r.jsx)("a",{href:e,className:d||"",target:_?"_blank":"_self",rel:_?"noreferrer":void 0,children:(0,r.jsx)(k,{})});if(x.length&&s){var t=h({},w,{item_type:"button"});return(0,r.jsx)(i.Z,{href:e,className:d||"",forceNewBrowserTab:_,commonTrackingData:t,children:(0,r.jsx)(k,{})})}};return(0,r.jsxs)("ul",{className:"".concat(g().stats," ").concat(n||""),children:[c&&(0,r.jsxs)("li",{children:[(0,r.jsx)(p,{"aria-hidden":"true"}),(0,r.jsx)("div",{className:"screen-reader-only",children:" The amount of the post views: "}),c]}),m&&(0,r.jsx)("li",{children:(0,r.jsx)(O,{})})]})},_.Thumbnail=function(e){var t=e.className,n=void 0===t?"":t,o=e.thumbnail,a=void 0===o?"":o,i=e.alt,c=void 0===i?"":i,l=e.lazyLoadThumbnail,u=void 0!==l&&l,d=e.ratio,f=void 0===d?"3/2":d,p=e.ratioTablet,v=e.ratioDesktop,m=p||f,b=v||m;return(0,r.jsx)("img",{className:"".concat(g().thumbnail," ").concat(n||""),src:a.length?a:s(),alt:c,loading:u?"lazy":"",style:{"--thumbnailAspectRatio":f,"--thumbnailAspectRatioTablet":m,"--thumbnailAspectRatioDesktop":b}})},_.IndexLabel=function(e){var t=e.className,n=void 0===t?"":t,o=e.children;return o?(0,r.jsxs)("span",{className:n||void 0,children:[(0,r.jsx)("span",{className:"screen-reader-only",children:" Position in the list: "}),(0,r.jsxs)("span",{children:[" ",o," "]})]}):null},_.Section=function(e){var t=e.children,n=e.className,o=void 0===n?"":n;return t?(0,r.jsx)("div",{className:"".concat(g().section," ").concat(o),children:(0,r.jsx)("span",{children:t})}):null};var y=_},41479:function(e,t,n){"use strict";n.d(t,{Z:function(){return ne}});var r=n(52322),o=n(13980),a=n.n(o),i=n(2784),c=n(39852),l=n(44677),s=n.n(l),u=n(48243);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var f=function(e){var t,n,o,a,l,f,p,v=e.className,m=void 0===v?"":v,g=e.index,b=e.isTrackable,h=void 0!==b&&b,_=e.item,y=e.parentLayout,x=void 0===y?"one_column":y,j=e.popularLabel,w=void 0===j?"":j,k=e.showSection,O=void 0!==k&&k,C=e.variant,P=void 0===C?"small":C,N=e.trackingData,S=void 0===N?{}:N,T=_||{},A=T.content,I=T.id,E=T.object_type,D=(0,i.useContext)(u.z1).pageType,R=void 0===D?"":D;if(!A)return null;var Z,L=A.trackingData,M=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){d(e,t,n[t])}))}return e}({},{subunit_type:"component",subunit_name:"".concat(E,"|").concat(I),position_in_subunit:0},L,{item_type:"card",item_name:null===A||void 0===A?void 0:A.id,target_content_type:"buzz",target_content_id:null===A||void 0===A?void 0:A.id},S),F=[s().feedArticle,s()[m],s()[R],s()[x],s()[P]].filter(Boolean).join(" ");return(0,r.jsxs)(c.Z,{className:F,isTrackable:h,trackingData:M,children:[O&&!!(null===(t=A.category)||void 0===t||null===(n=t.label)||void 0===n?void 0:n.length)&&(0,r.jsx)(c.Z.Section,{className:s().category,children:A.category.label}),(0,r.jsxs)("div",{className:s().thumbnailWrapper,children:[(0,r.jsx)(c.Z.Link,{url:A.url,isTrackable:h,trackingData:M,children:(0,r.jsx)(c.Z.Thumbnail,{thumbnail:null===(o=A.thumbnail)||void 0===o||null===(a=o.standard)||void 0===a?void 0:a.url,alt:String(null!==(Z=null===(l=A.thumbnail)||void 0===l||null===(f=l.standard)||void 0===f?void 0:f.alt)&&void 0!==Z?Z:""),className:s().thumbnail,lazyLoadThumbnail:!0})},"imageLink"),g&&(0,r.jsx)(c.Z.IndexLabel,{className:s().index,children:g})]}),(0,r.jsxs)("div",{className:s().textWrapper,children:[(0,r.jsx)(c.Z.InfoLabel,{className:s().infoLabel,createdAt:A.timestamp,label:A.isPopular?"Popular":w}),(0,r.jsx)(c.Z.Link,{url:A.url,isTrackable:h,trackingData:M,children:(0,r.jsx)("h2",{className:s().heading,children:A.title})},"headingLink"),!(null===A||void 0===A?void 0:A.hideReactions)&&(0,r.jsxs)("div",{className:s().statsWrapper,children:[(0,r.jsx)(c.Z.Reactions,{className:s().reactions,contentId:Number(A.id),data:A.contentReactions,isTrackable:h,trackingData:M}),(0,r.jsx)(c.Z.Stats,{className:s().statistics,commentsClassName:s().statsLink,commentLink:A.url,commentsCount:null===A||void 0===A||null===(p=A.comments)||void 0===p?void 0:p.countFormatted,isTrackable:h,trackingData:M})]})]})]})},p=n(36774),v=n(8520),m=n(87720),g=n.n(m),b=n(45847),h=n(97196);function _(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var y=function(e){var t,n,o=e.item,a=void 0===o?{}:o,c=e.isTrackable,l=e.trackingData,s=a||{},d=s.content,f=s.id,p=s.object_type,m=d.trackingData,y=(null===d||void 0===d||null===(t=d.oembed_data)||void 0===t?void 0:t.html)||"",x={subunit_type:"component",subunit_name:"".concat(p,"|").concat(f),position_in_subunit:0},j=(0,i.useMemo)((function(){var e=y.match(/src=["'](.*(instagram|reddit|twitter|tiktok).*?(embed|s.js|widgets.js).*?)["']/),t=e?e[1]:null;return t?(0,r.jsx)(b.default,{src:t}):null}),[d]),w=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){_(e,t,n[t])}))}return e}({},m,x,{item_type:"button",item_name:(null===d||void 0===d||null===(n=d.oembed_data)||void 0===n?void 0:n.url)||f,target_content_type:"url"},l),k=(0,i.useContext)(u.z1).isSponsored,O=null===d||void 0===d?void 0:d.description,C=(0,h.Si)({trackingData:w}).setObservable;return y?(0,r.jsxs)("article",{className:g().feedSocialContainer,ref:function(e){return C(e)},children:[(0,r.jsx)("header",{className:g().header,children:(0,r.jsx)("h2",{className:g().title,children:null===d||void 0===d?void 0:d.title})}),O&&(0,r.jsx)("p",{className:g().description,children:O}),(0,r.jsxs)("div",{className:g().embed,children:[(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:y}}),j]}),(0,r.jsx)(v.Z,{className:g().reactions,contentId:f,contentType:"content-object",data:null===d||void 0===d?void 0:d.content_reactions,isSponsored:k,isTrackable:c,trackingData:w})]}):null},x=n(94776),j=n.n(x),w=n(42235),k=n(30353),O=n(81346),C=n.n(O);function P(e,t,n,r,o,a,i){try{var c=e[a](i),l=c.value}catch(s){return void n(s)}c.done?t(l):Promise.resolve(l).then(r,o)}function N(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function S(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){N(e,t,n[t])}))}return e}var T=function(e){var t,n,o,a=e.className,c=e.item,l=void 0===c?{}:c,s=e.trackingData,d=l.content,f=l.id,p=l.object_type,m=d.trackingData,g=(0,i.useContext)(u.Ui),b=S({context_page_id:g.context_page_id,context_page_type:g.context_page_type,destination:g.destination,page_edition:g.page_edition},m,{subunit_type:"component",subunit_name:"".concat(p,"|").concat(f)},{position_in_subunit:0},s),h=S({},b,b.item_name?{}:{item_name:f}),_=(0,i.useRef)(null),y=(0,i.useContext)(u.z1).isSponsored;(0,i.useEffect)((function(){x()}),[]);var x=function(){var e,t=(e=j().mark((function e(){var t,n,r,o,a,i,c,l,s,u;return j().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n="",r=[];try{r=k.vA.split(",")||[]}catch(p){}if(!r.includes(null===d||void 0===d||null===(t=d.bfp)||void 0===t?void 0:t.format_name)){e.next=15;break}return o=null===d||void 0===d?void 0:d.bfp.format_name.replace("_","-"),e.next=8,fetch("/api/local-bfp?format=".concat(o));case 8:return a=e.sent,e.next=11,a.json();case 11:i=e.sent,n=i.renderKitUrl,e.next=16;break;case 15:s=null===d||void 0===d||null===(c=d.bfp)||void 0===c||null===(l=c.render_kit)||void 0===l?void 0:l.find((function(e){var t;return null===e||void 0===e||null===(t=e.url)||void 0===t?void 0:t.includes("render_kit.")})),n="https://".concat("prod"===k.ov?"www":"stage",".buzzfeed.com").concat(null===s||void 0===s?void 0:s.url);case 16:if(!n){e.next=21;break}return e.next=19,(0,w.v)(n);case 19:if(u=null===d||void 0===d?void 0:d.bfp.format_name,(null===_||void 0===_?void 0:_.current)&&window["bfp_".concat(u)])try{window["bfp_".concat(u)].init({element:_.current,config:{data:d.bfp.data,context:{env:k.ov,clientEventTracking:b,consumer:"feed_ui",contentObjectId:f,bfpId:d.bfp.id}},broadcast:function(){}})}catch(p){console.error("Unable to load BFP - ".concat(p.message))}case 21:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){P(a,r,o,i,c,"next",e)}function c(e){P(a,r,o,i,c,"throw",e)}i(void 0)}))});return function(){return t.apply(this,arguments)}}(),O=null===d||void 0===d||null===(t=d.bfp)||void 0===t||null===(n=t.renders)||void 0===n?void 0:n.oo_web,N=[a,C()[null===d||void 0===d||null===(o=d.bfp)||void 0===o?void 0:o.format_name]].filter(Boolean).join(" ");return(0,r.jsxs)("article",{"data-name":"bfp-wrapper",className:N,"aria-label":"Interactive content container",children:[(0,r.jsx)("script",{"data-name":"tracking-data",type:"application/json",dangerouslySetInnerHTML:{__html:JSON.stringify(b)}}),(0,r.jsx)("div",{className:C().container,ref:_,"aria-live":"polite",children:O&&(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:O}})}),!1===d.allow_reactions?null:(0,r.jsx)(v.Z,{className:C().reactions,contentId:f,data:d.content_reactions,isTrackable:!0,isSponsored:y,trackingData:h})]})};T.propTypes={className:a().string,item:a().object.isRequired,trackingData:a().object};var A=T,I=n(58599),E=n(5819),D=n(52003),R=n.n(D),Z=n(25969),L=n(99523),M=n.n(L);function F(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function z(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){F(e,t,n[t])}))}return e}var B=function(e){var t,n,o=e.cta,a=e.trackingData;return(null===o||void 0===o||null===(t=o.text)||void 0===t?void 0:t.length)?(null===o||void 0===o||null===(n=o.link)||void 0===n?void 0:n.length)?(0,r.jsxs)(I.Z,{href:o.link,className:M().cta,commonTrackingData:a,children:[o.text,(0,r.jsx)(Z._,{})]}):(0,r.jsx)("span",{className:M().cta,children:o.text}):null},W=function(e){var t,n,o=e.item,a=void 0===o?{}:o,l=e.className,s=e.trackingData,d=void 0===s?{}:s,f=(0,i.useContext)(u.z1),v=f.path,m=void 0===v?"":v,g=f.isHp,b=(void 0===g?"/"===m:g)?"hflspl":"",h=a||{},_=h.content,y=h.id,x=_||{},j=x.items,w=void 0===j?[]:j,k=x.cta,O=x.title,C=x.trackingData;if((null!==(n=null===_||void 0===_||null===(t=_.items)||void 0===t?void 0:t.length)&&void 0!==n?n:0)<3)return null;var P=!!(null===w||void 0===w?void 0:w.length)&&w[0],N=!!(null===w||void 0===w?void 0:w.length)&&(null===w||void 0===w?void 0:w.slice(1,3)),S=!!(null===w||void 0===w?void 0:w.length)&&(null===w||void 0===w?void 0:w.slice(3,6)),T=z({},d,{subunit_type:"package",subunit_name:"package|".concat(y)}),A=function(e,t){switch(e){case"splash":var n;return z({},T,null===P||void 0===P||null===(n=P.content)||void 0===n?void 0:n.trackingData,{position_in_subunit:0});case"trending":var r,o,a,i,c,l;return z({},T,null===(r=N[t])||void 0===r||null===(o=r.content)||void 0===o?void 0:o.trackingData,{item_type:"card",item_name:null===(a=N[t])||void 0===a||null===(i=a.content)||void 0===i?void 0:i.id,position_in_subunit:1+t,target_content_type:"buzz",target_content_id:null===(c=N[t])||void 0===c||null===(l=c.content)||void 0===l?void 0:l.id});case"topic":var s,u,d,f,p,v;return z({},T,null===(s=S[t])||void 0===s||null===(u=s.content)||void 0===u?void 0:u.trackingData,{item_type:"card",item_name:null===(d=S[t])||void 0===d||null===(f=d.content)||void 0===f?void 0:f.id,position_in_subunit:3+t,target_content_type:"buzz",target_content_id:null===(p=S[t])||void 0===p||null===(v=p.content)||void 0===v?void 0:v.id});default:return z({},T,C,{item_type:"text",item_name:null===k||void 0===k?void 0:k.text,target_content_type:"url",target_content_url:null===k||void 0===k?void 0:k.link,target_content_id:null===k||void 0===k?void 0:k.link})}};return(0,r.jsx)("section",{className:"".concat(M().package," ").concat(l||""),children:(0,r.jsxs)("div",{className:M().content,children:[!!(null===O||void 0===O?void 0:O.length)&&(0,r.jsx)("h2",{className:M().title,children:O}),(0,r.jsxs)("div",{className:M().postsWrap,children:[(0,r.jsx)(p.Z,{className:"packageSplashCard",isTrackable:!0,item:P,imageRatio:"3/2",imageRatioTablet:"1/1",imageRatioDesktop:"1/1",trackingData:A("splash"),origin:b}),(0,r.jsx)("div",{className:M().trendingPosts,children:!!(null===N||void 0===N?void 0:N.length)&&N.map((function(e,t){var n,o,a,i;return(0,r.jsx)(c.Z,{isTrackable:!0,className:M().trendingPost,trackingData:A("trending",t),children:(0,r.jsxs)(c.Z.Link,{isTrackable:!0,url:(0,E.b)(e.content.url,b),trackingData:A("trending",t),children:[(0,r.jsx)(c.Z.Thumbnail,{alt:null===(n=e.content.thumbnail)||void 0===n||null===(o=n.standard)||void 0===o?void 0:o.alt,className:M().thumbnail,lazyLoadThumbnail:!0,thumbnail:(null===(a=e.content.thumbnail)||void 0===a||null===(i=a.standard)||void 0===i?void 0:i.url)||R(),ratio:"3/2"}),(0,r.jsx)("h3",{children:e.content.title})]})},e.content.id)}))}),(0,r.jsx)("div",{className:M().topicPosts,children:!!(null===S||void 0===S?void 0:S.length)&&S.map((function(e,t){var n,o,a,i;return(0,r.jsxs)(c.Z,{isTrackable:!0,className:M().topicPost,trackingData:A("topic",t),children:[0===t&&(0,r.jsx)("div",{className:"xs-hide lg-block",children:(0,r.jsx)(c.Z.Link,{isTrackable:!0,url:(0,E.b)(e.content.url,b),trackingData:A("topic",t),children:(0,r.jsx)(c.Z.Thumbnail,{alt:null===(n=e.content.thumbnail)||void 0===n||null===(o=n.standard)||void 0===o?void 0:o.alt,className:M().thumbnail,lazyLoadThumbnail:!0,thumbnail:(null===(a=e.content.thumbnail)||void 0===a||null===(i=a.standard)||void 0===i?void 0:i.url)||R(),ratio:"3/2"})})}),e.content.category&&(0,r.jsx)("span",{className:M().topicTitle,children:e.content.category.label}),(0,r.jsx)(c.Z.Link,{isTrackable:!0,url:(0,E.b)(e.content.url,b),trackingData:A("topic",t),children:(0,r.jsx)("h3",{children:e.content.title})})]},e.content.id)}))})]}),(0,r.jsx)(B,{cta:null===_||void 0===_?void 0:_.cta,trackingData:A("cta")})]})})};W.propTypes={item:a().shape({content:a().shape({items:a().arrayOf(a().object),cta:a().shape({text:a().string,link:a().string}),title:a().string,trackingData:a().object})}),className:a().string,trackingData:a().object};var H=W,V=n(84597),U=n(68002),q=n(75471),J=n.n(q);function G(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Q(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){G(e,t,n[t])}))}return e}var K=function(e){var t,n,o,a,c,l,s=e.item,u=e.trackingData,d=(0,h.ac)().breakpoint,f=s||{},p=f.content,v=f.id,m=f.object_type,g=p||{},b=g.badge,_=g.images,y=g.title,x=g.description,j=g.trackingData,w=(0,i.useState)({alt:(null===_||void 0===_?void 0:_.mobile_alt_text)||(null===_||void 0===_?void 0:_.standard_alt_text)||y,credit:(null===_||void 0===_?void 0:_.mobile_credit)||(null===_||void 0===_?void 0:_.standard_credit)||""}),k=w[0],O=w[1],C=Q({},u,j,{subunit_type:"component",subunit_name:"".concat(m,"|").concat(v),position_in_subunit:0});(0,i.useEffect)((function(){d&&O(Q({},k,"xs"!==d?{credit:(null===_||void 0===_?void 0:_.standard_credit)||""}:{credit:(null===_||void 0===_?void 0:_.mobile_credit)||""}))}),[d]);var P=(0,h.JG)(C);return(null===y||void 0===y?void 0:y.length)||(null===x||void 0===x?void 0:x.length)||(null===_||void 0===_||null===(t=_.standard)||void 0===t?void 0:t.length)||(null===_||void 0===_||null===(n=_.mobile)||void 0===n?void 0:n.length)||(null===b||void 0===b||null===(o=b.source)||void 0===o?void 0:o.length)?(0,r.jsxs)("header",{className:J().pageHeader,children:[((null===_||void 0===_?void 0:_.standard)||(null===_||void 0===_?void 0:_.mobile))&&(0,r.jsx)("figure",{className:J().imageContainer,children:(0,r.jsxs)("picture",{children:[!!k.credit.length&&(0,r.jsx)("figcaption",{className:"".concat(J().credit," "),children:k.credit}),!!(null===_||void 0===_||null===(a=_.mobile)||void 0===a?void 0:a.length)&&(0,r.jsx)("source",{media:"(max-width: 39.99rem)",srcSet:_.mobile}),(0,r.jsx)("img",{src:null===_||void 0===_?void 0:_.standard,alt:k.alt})]})}),(!!(null===y||void 0===y?void 0:y.length)||!!(null===x||void 0===x?void 0:x.length)||!!(null===b||void 0===b||null===(c=b.source)||void 0===c?void 0:c.length))&&(0,r.jsxs)("div",{className:J().textWrapper,children:[(!!(null===b||void 0===b||null===(l=b.source)||void 0===l?void 0:l.length)||!!(null===y||void 0===y?void 0:y.length))&&(0,r.jsxs)("h1",{className:J().title,children:[!!(null===b||void 0===b?void 0:b.source.length)&&(0,r.jsx)("img",{src:b.source,alt:"","aria-hidden":"true"}),y]}),x&&(0,r.jsx)("div",{className:J().description,dangerouslySetInnerHTML:{__html:x},ref:P})]})]}):null};function Y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Y(e,t,n[t])}))}return e}function X(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var ee={bfp_content:A,discussion_question:U.Z,package:H,"post_promo:small":f,"post_promo:medium":f,"post_promo:large":p.Z,social_embed:y,voting:V.Z,page_header:K},te=function(e){var t=e.displayOptions,n=X(e,["displayOptions"]),o=((null===n||void 0===n?void 0:n.item)||{}).object_type,a=$({},n),i=ee[o];if("post_promo"===o){var c=t||{},l=c.grid,s=c.post_promo_size,u=void 0===s?"small":s;i=ee["post_promo:".concat(u)]||ee["post_promo:small"],a.variant=u,a.parentLayout=l,"large"===u&&"two_columns"===l&&(a.imageRatioTablet="1/1",a.imageRatioDesktop="1/1")}return i?(0,r.jsx)(i,$({},a)):null};te.propTypes={className:a().string,displayOptions:a().object,item:a().object.isRequired,index:a().number,isTrackable:a().bool,popularLabel:a().oneOfType([a().object,a().string]),showSection:a().bool,trackingData:a().object};var ne=te},17601:function(e,t,n){"use strict";var r=n(52322),o=n(51078),a=n.n(o);t.Z=function(e){var t=e.className,n=void 0===t?"":t,o=e.title,i=e.onClick,c=e.url,l=void 0===c?"":c;return(0,r.jsx)(r.Fragment,{children:l.length?(0,r.jsx)("a",{href:l,className:"".concat(a().button," ").concat(n),onClick:i,children:o}):(0,r.jsx)("button",{className:"".concat(a().button," ").concat(n),type:"button",onClick:i,children:o})})}},68002:function(e,t,n){"use strict";var r=n(52322),o=n(17601),a=n(58599),i=n(25969),c=n(97196),l=n(14839),s=n.n(l),u=n(5819);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){d(e,t,n[t])}))}return e}t.Z=function(e){var t=e.item,n=e.isTrackable,l=e.trackingData,d=t||{},p=d.content,v=d.id,m=d.object_type,g=p.question,b=p.post,h=p.trackingData,_=p.comments,y=void 0===_?{}:_,x={subunit_type:"component",subunit_name:"".concat(m,"|").concat(v),position_in_subunit:0},j=(0,c.EF)().trackInternalLink,w=f({},h,l,x,{item_type:"button",item_name:"add_your_answer",target_content_type:"buzz",target_content_id:b.id}),k=!!(null===y||void 0===y?void 0:y.count)&&(0,r.jsxs)(a.Z,{className:s().replies,href:(0,u.b)(b.canonical_url,"hffun"),commonTrackingData:f({},w,{item_type:"text"}),children:["".concat(y.countFormatted," repl").concat(y.count>1?"ies":"y")," ",(0,r.jsx)(i._,{})]}),O=(0,c.Si)({trackingData:w}).setObservable;return(0,r.jsxs)("div",{className:"".concat(s().container," ").concat(s().addDiscussion),children:[(0,r.jsx)("h2",{className:s().subTitle,children:"JOIN THE DISCUSSION"}),(0,r.jsx)("h3",{className:s().title,children:g}),(0,r.jsxs)("div",{className:"".concat(s().repliesWrap," xs-flex xs-flex-align-center"),ref:function(e){return t=e,void(n&&O(t));var t},children:[(0,r.jsx)(o.Z,{title:(null===y||void 0===y?void 0:y.count)?"Add Your Answer":"Be The First To Comment",onClick:function(){n&&j(w)},url:(0,u.b)(b.canonical_url,"hffun")}),k]})]})}},84597:function(e,t,n){"use strict";var r=n(94776),o=n.n(r),a=n(52322),i=n(2784),c=n(13980),l=n.n(c),s=n(20125),u=n(30353),d=n(97196),f=n(80713),p=n(14839),v=n.n(p);function m(e,t,n,r,o,a,i){try{var c=e[a](i),l=c.value}catch(s){return void n(s)}c.done?t(l):Promise.resolve(l).then(r,o)}function g(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){m(a,r,o,i,c,"next",e)}function c(e){m(a,r,o,i,c,"throw",e)}i(void 0)}))}}function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){b(e,t,n[t])}))}return e}var _=(0,s.a)({namespace:"localUserVotes"}),y=function(){var e=g(o().mark((function e(t){var n,r,a,i,c;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.answerId,r=t.objectId,a="".concat(u._H,"/polls-api/v2/content-object/").concat(r),i={credentials:"include",headers:{"Content-Type":"application/json"}},"number"===typeof n&&(i.body=JSON.stringify({result:n}),i.method="POST"),e.prev=4,e.next=7,fetch(a,i);case 7:if((c=e.sent).ok){e.next=10;break}throw new Error("Fetch request for '".concat(a,"' failed with status: ").concat(c.status," - ").concat(c.statusText));case 10:return e.next=12,c.json();case 12:return e.abrupt("return",e.sent);case 15:return e.prev=15,e.t0=e.catch(4),console.error(e.t0),e.abrupt("return",null);case 19:case"end":return e.stop()}}),e,null,[[4,15]])})));return function(t){return e.apply(this,arguments)}}(),x=function(e){var t,n,r,c,l,s,u,p,m,b,x=e.item,j=e.isTrackable,w=e.trackingData,k=(e.index,x.content),O=x.id,C=x.object_type,P=x.sponsorship,N=x.id,S=k.trackingData,T=(0,d.EF)().trackContentAction,A=k.answers,I=void 0===A?[]:A,E=k.question,D=(0,i.useState)(null),R=D[0],Z=D[1],L=(0,i.useState)(null),M=L[0],F=L[1],z=h({},S,w,{subunit_type:"component",subunit_name:"".concat(C,"|").concat(O)}),B=(0,i.useCallback)((function(e){return g(o().mark((function t(){var n,r,a,i,c;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=null===(n=I[e])||void 0===n?void 0:n.id,null!==R||M||"number"!==typeof r){t.next=7;break}return t.next=5,y({answerId:r,objectId:N});case 5:(a=t.sent)&&(_.set("content-object:".concat(N,":").concat(r)),Z(r),F(a),j&&T(h({},z,{position_in_subunit:e,item_type:"button",item_name:(null===(i=I[e])||void 0===i?void 0:i.text)||"answer_".concat(r),action_type:"select",action_value:(null===(c=I[e])||void 0===c?void 0:c.text)||"answer_".concat(r)})));case 7:case"end":return t.stop()}}),t)})))}),[I,N,M,R]);return(0,i.useEffect)((function(){var e=function(){var e=g(o().mark((function e(){var t,n,r;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t=_.get("content-object:".concat(N)))){e.next=8;break}return n=parseFloat(t.split(":").pop()),Z(isNaN(n)?null:n),e.next=6,y({objectId:N,type:"content-object"});case 6:r=e.sent,F(r);case 8:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[N]),(null===I||void 0===I?void 0:I.length)&&(null===E||void 0===E?void 0:E.length)?(0,a.jsxs)("article",{className:v().container,style:{"--sponsorTextColor":null===P||void 0===P||null===(t=P.data)||void 0===t||null===(n=t.colors)||void 0===n?void 0:n.text,"--sponsorAccentColor":null===P||void 0===P||null===(r=P.data)||void 0===r||null===(c=r.colors)||void 0===c?void 0:c.accent,"--sponsorBackgroundColor":null===P||void 0===P||null===(l=P.data)||void 0===l||null===(s=l.colors)||void 0===s?void 0:s.background},children:[(0,a.jsxs)("div",{className:v().headerWrap,children:[(null===P||void 0===P?void 0:P.sponsor)&&(0,a.jsx)(f.V,{data:P.sponsor,isPromoted:!!(null===P||void 0===P||null===(u=P.data)||void 0===u||null===(p=u.colors)||void 0===p?void 0:p.text),trackingData:h({},z,{item_type:"text",item_name:"presented_by",target_content_type:"url",target_content_url:null===P||void 0===P||null===(m=P.sponsor)||void 0===m||null===(b=m.client_data)||void 0===b?void 0:b.url,position_in_subunit:null})}),(0,a.jsx)("h2",{className:v().subTitle,children:"WHAT\u2019S YOUR VOTE?"})]}),(0,a.jsx)("h3",{className:v().title,children:E}),(0,a.jsx)("ul",{className:"".concat(v().votingAnswers," ").concat(null!==R&&M?v().isVoted:""," ").concat(null===R||M?"":v().loading),children:I.map((function(e,t){var n,r;return(0,a.jsx)("li",{className:R===e.id?v().active:"",style:{"--votingAnswerValue":"".concat(Math.round((null===M||void 0===M||null===(n=M.percentages)||void 0===n?void 0:n[e.id])||0),"%")},children:(0,a.jsxs)("button",{className:v().votingAnswer,onClick:B(t),"aria-label":e.text,children:[(0,a.jsx)("span",{className:v().answerTitle,children:e.text}),e.image&&(0,a.jsx)("span",{className:v().answerImage,children:(0,a.jsx)("img",{src:e.image,alt:e.text})}),(0,a.jsxs)("span",{className:v().answerValue,children:[Math.round((null===M||void 0===M||null===(r=M.percentages)||void 0===r?void 0:r[e.id])||0),"%"]})]})},e.id)}))})]}):null};x.propTypes={content:l().shape({answers:l().arrayOf(l().shape({id:l().number,image:l().string,text:l().string})),question:l().string}),id:l().oneOfType([l().number,l().string]),index:l().oneOfType([l().number,l().string]),isTrackable:l().bool,sponsorship:l().object,trackingData:l().object},t.Z=x},55190:function(e,t,n){"use strict";var r=n(52322),o=(n(2784),n(30353));t.Z=function(){return(0,r.jsx)("noscript",{children:(0,r.jsx)("iframe",{title:"google-tag-manager",src:"https://www.googletagmanager.com/ns.html?id=".concat(o.bj),height:"0",width:"0",style:{display:"none",visibility:"hidden"}})})}},58599:function(e,t,n){"use strict";var r=n(52322),o=n(97196),a=n(90093);function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){i(e,t,n[t])}))}return e}t.Z=function(e){var t=e.href,n=e.children,i=e.className,l=void 0===i?"":i,s=e.commonTrackingData,u=void 0===s?{}:s,d=e.forceNewBrowserTab,f=void 0!==d&&d,p=(0,o.EF)(),v=p.trackExternalLink,m=p.trackInternalLink,g=!!t&&!t.toLowerCase().match(/.*buzzfeed\.(com|io)|(^\/.*$)/),b=(0,a.p)(t,"buzzfeednews.com")||(0,a.p)(t,"tasty.co"),h=f||g&&!b?"_blank":"_self",_=!1,y=function(e){if(!_){_=!0;var n="left";1===e.button?n="middle":"contextmenu"===e.type&&(n="right"),g?v(c({},u,{click_type:n,target_content_url:t,target_content_type:void 0,target_content_id:void 0})):m(c({click_type:n},u)),setTimeout((function(){_=!1}),50)}};return(0,r.jsx)("a",{href:t,onClick:y,onContextMenu:y,onMouseUp:y,className:l,target:h,children:n})}},31066:function(e,t,n){"use strict";var r=n(52322),o=n(2784),a=n(5632),i=n(97729),c=n(69186),l=n(48243),s=n(75127),u=n(18140),d=n(18977),f=n(99036),p=n.n(f);t.Z=function(e){var t=e.html,n=void 0===t?"":t,f=e.js,v=void 0===f?"":f,m=e.css,g=e.communityPage,b=void 0!==g&&g,h=(0,o.useContext)(l.oF),_=(0,a.useRouter)();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(i.default,{children:[(0,r.jsx)("script",{dangerouslySetInnerHTML:{__html:"\n  window.BZFD = window.BZFD || {};\n  window.BZFD.Config = {\n    bfwInfoCookie: 'bf2-b_info'\n  };\n"}},"window-globals"),(0,r.jsx)("script",{dangerouslySetInnerHTML:{__html:(0,u.I)({badges:[],cms_tags:[],dfp_keyword:p()(_.query["dfp-keyword"])||null,id:"home",section:[],zone3:"",isTasty:!1,pagetype:"A",poe:(0,d.kH)({env:{isBPage:!1}}),getPageSessionId:(0,d.HU)({gdpr:s.Z})})}},"ads-context")]}),b?(0,r.jsx)(c.Z,{html:n,js:v,css:m}):(0,r.jsx)(c.Z,{html:n,js:v,css:m,stickyRegistry:h})]})}},80713:function(e,t,n){"use strict";n.d(t,{V:function(){return s}});var r=n(52322),o=n(13980),a=n.n(o),i=n(58599),c=n(35335),l=n.n(c),s=function(e){var t,n=e.data,o=e.className,a=void 0===o?"":o,c=e.isPromoted,s=void 0!==c&&c,u=e.showLogo,d=void 0===u||u,f=e.trackingData,p=void 0===f?{}:f,v=e.label,m=void 0===v?"Presented By":v,g=n.client_data,b=void 0===g?{}:g,h=n.display_name,_=b.images,y=void 0===_?{}:_,x=b.url,j=void 0===x?"":x;return(0,r.jsxs)("header",{className:"".concat(l().brand," ").concat(a," ").concat(s?l().isPromoted:""),children:[!!(null===y||void 0===y||null===(t=y.standard)||void 0===t?void 0:t.length)&&d&&(0,r.jsx)("div",{className:l().logo,children:(0,r.jsx)("img",{src:y.standard,alt:h,loading:"lazy"})}),(0,r.jsxs)("span",{className:l().name,children:[(0,r.jsxs)("span",{className:l().label,children:[m," "]}),(0,r.jsx)(i.Z,{href:j,commonTrackingData:p,children:h})]})]})};s.propTypes={data:a().shape({client_data:a().shape({images:a().shape({standard:a().string}),url:a().string}),display_name:a().string}).isRequired,className:a().string,isPromoted:a().bool,showLogo:a().bool,trackingData:a().object,label:a().string},t.Z=s},91807:function(e,t,n){"use strict";var r=n(52322),o=(n(2784),n(59855)),a=n(48243);function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){i(e,t,n[t])}))}return e}t.Z=function(e){var t=e.children;return(0,r.jsx)(a.oF.Provider,{value:c({},o.Z,{notify:function(e,t){return(0,t.callback)({shouldStick:!0})}}),children:t})}},57084:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(2784),o=n(74967),a=n(48243),i=n(97196);function c(){var e=(0,r.useContext)(a.WN).experiments,t=(0,i.EF)().trackTimeSpent;return(0,r.useEffect)((function(){var n=function(){};!e.stale&&e.loaded&&("on"===(0,o.ts)(e,"TimeSpentRO_1",{rejectErrors:!1})&&(n=t({})));return n}),[e.loaded]),null}},61360:function(e,t,n){"use strict";n.d(t,{zx:function(){return c}});var r=n(75951),o=n(84952);function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function i(e){return function(e){if(Array.isArray(e))return a(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var c=[{name:"RT-1710-adshield-experiment",variations:["control","adshield"],isEligible:function(){return!["gb","ie","uk","nz","au","jp"].includes((0,o.pP)().toLowerCase())}}],l=[r.Yx].concat(i([{name:"TimeSpentRO_1",variations:["on","control"],isEligible:function(){return!0}},{name:"RT-1559-AdShield-script-on-BFDC",variations:["control","on"],isEligible:function(){return!0}},{name:"BF-14546-member-shopping-control",variations:["control","on"],isEligible:function(){return!0}}]),i(c));t.ZP=l},34014:function(e,t,n){"use strict";var r=n(56758);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){o(e,t,n[t])}))}return e}for(var i={},c=1;c<41;c++){var l="story".concat(c);i[l]={adType:"post",adPos:l,pos:l,wid:c,targeting:{pos:[l],wid:c},platform:"autodetect",position:null,viewability:c<3?"high":"low",size:[r.J7.FLUID,r.J7.NATIVE,r.J7.PROGRAMMATIC_SMARTPHONE_BANNER,r.J7.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J7.PROGRAMMATIC_LEADERBOARD],strategy:1===c?"batch":"single"}}i.story1.loadgroup="top";var s={},u={adType:"post",adPos:"bigstory",platform:"desktop",wid:130,size:[r.J7.NATIVE,r.J7.FLUID,r.J7.PROGRAMMATIC_MEDIUM_RECTANGLE,r.J7.PROGRAMMATIC_VERTICAL],strategy:"single"};s.bigstory=a({},u,{adPos:"bigstory",strategy:"batch",loadgroup:"top"});for(var d=0;d<10;d++){var f="promo".concat(d+1);s[f]=a({},u,{adPos:f,wid:230+d,pos:f})}s["promo-infinite"]=a({},u,{adPos:"promo-infinite",wid:2300});var p=Object.freeze(a({},r.vc,i,s));t.Z=p},23796:function(e,t,n){"use strict";n.d(t,{pX:function(){return r},KN:function(){return o},SP:function(){return a}});var r="video",o="activeTabListener",a="feedContentTransitionEnd"},25969:function(e,t,n){"use strict";n.d(t,{_:function(){return o}});var r=n(52322),o=function(e){var t=e.className,n=void 0===t?"":t;return(0,r.jsx)("svg",{width:"16",height:"17",viewBox:"0 0 16 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:n,children:(0,r.jsx)("path",{d:"M5.5 3.5L10.5 8.5L5.5 13.5",stroke:"#5246F5",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}},64319:function(e,t,n){"use strict";n.d(t,{O:function(){return o}});var r=n(52322),o=function(e){var t=e.className,n=void 0===t?"":t;return(0,r.jsx)("svg",{className:n,fill:"#EB5369",height:"19",preserveAspectRatio:"xMidYMid meet",viewBox:"0 0 18 19",width:"18",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{clipRule:"evenodd",d:"M5.57 1.47A.7.7 0 0 1 6.24.5c2.02.07 3.42.68 4.35 ********* 1.18 1.53 1.39 2.3.13-.24.21-.5.26-.82.05-.4.45-.74.92-.58a6.08 6.08 0 0 1 2.72 2.22 9.78 9.78 0 0 1 1.63 3.73c.57 2.77-.09 5.94-3.17 7.97a10.5 10.5 0 0 1-6.06 1.64 8.1 8.1 0 0 1-5.2-1.64 6.87 6.87 0 0 1-2.36-3.78A7.58 7.58 0 0 1 .88 8.9a4.96 4.96 0 0 1 2.37-2.93c.5-.25 1 .16 1 .**********.*********.***********.18A4.28 4.28 0 0 0 6 5.45c.27-.97.24-2.25-.44-3.98Zm2.92 9.04c.07.7.06 1.26-.12 1.7-.22.54-.64.83-1.15 1.01a.72.72 0 0 1-.5 0c-.28-.1-.6-.3-.81-.64-.59.54-.87 1.65-.57 **********.48 1.09.99 ********* 1.2.64 2.16.64 1.25 0 2.06-.3 2.72-.72a4 4 0 0 0 1.9-3.14c.04-1-.34-1.8-.86-2.14l-.03.04-.2.3c-.16.25-.39.5-.6.72-.32.3-.74.21-.95.11a1 1 0 0 1-.58-.66c-.15-.73-.68-1.34-1.4-1.41Z",fillRule:"evenodd"})})}},33657:function(e,t,n){"use strict";n.r(t),n.d(t,{addBillboards:function(){return P},buildPosBound:function(){return O},getComponent:function(){return k},getMaxSizes:function(){return C},getTemplate:function(){return w},getWireframeOptions:function(){return N},isSidebarAd:function(){return j},isWideInline:function(){return x}});var r=n(56758),o=n(89809),a=n(35473),i=n(36610),c=n(95466),l=n(31207),s=n(71049),u=n(44399),d=n(54442),f=n(28403),p=n(76562),v=n(12239),m=(n(67419),{AdAwareness:a.Z,AdBigstory:i.Z,AdEx:c.S,AdFeedStory:l.Z,AdToolbar:s.Z}),g={AdAwareness:u.Z,AdBigstory:d.Z,AdEx:f.Z,AdFeedStory:p.Z,AdToolbar:v.Z};function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a=[],i=!0,c=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(l){c=!0,o=l}finally{try{i||null==n.return||n.return()}finally{if(c)throw o}}return a}}(e,t)||y(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(e){return function(e){if(Array.isArray(e))return b(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||y(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e,t){if(e){if("string"===typeof e)return b(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?b(e,t):void 0}}function x(e){return/^promo\d*-wide/.test(e)}function j(e){return/^promo/.test(e)||/^bigstory/.test(e)}var w=function(e){var t=e.adPos;return e.useTemplate||e.useComponent?g[e.useTemplate||e.useComponent]:/^awareness/.test(t)?g.AdAwareness:/^story/.test(t)?g.AdFeedStory:j(t)?g.AdBigstory:x(t)||/^promo/.test(t)?g.AdEx:/tb$/.test(t)?g.AdToolbar:void 0},k=function(e){var t=e.adPos;return e.useComponent?m[e.useComponent]:/^awareness/.test(t)?m.AdAwareness:/^story/.test(t)?m.AdFeedStory:j(t)?m.AdBigstory:/tb$/.test(t)?m.AdToolbar:m.AdEx},O=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o="".concat(e).concat(t);return t>n&&(o=null!==r?r:"".concat(e).concat(n)),o},C=function(e){var t,n,a=o.Z.getProgrammatic(r.PP,e.size),i=(t=Math).max.apply(t,[0].concat(_(a.map((function(e){return h(e,2)[1]})))));return[(n=Math).max.apply(n,[0].concat(_(a.map((function(e){return h(e,1)[0]}))))),i]},P=function(e){return o.Z.add(e.size,r.J7.PROGRAMMATIC_SUPER_LEADERBOARD,r.J7.PROGRAMMATIC_BILLBOARD)},N=function(e){var t=C(e);return{programmaticWireframes:!0,wireframeWidth:t[0],wireframeHeight:t[1],wireframeClasses:function(e){return"".concat(e," ad-wireframe-wrapper--inline")}}}},28964:function(e,t,n){"use strict";n.d(t,{Xi:function(){return d},pY:function(){return v},LA:function(){return u},Oy:function(){return m},vW:function(){return p},tc:function(){return f}});var r=n(94776),o=n.n(r),a=(n(42646),n(42598),n(17396),n(51344),n(20238));n(23796),n(30353);function i(e,t,n,r,o,a,i){try{var c=e[a](i),l=c.value}catch(s){return void n(s)}c.done?t(l):Promise.resolve(l).then(r,o)}function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){c(e,t,n[t])}))}return e}var s={"output-format":"auto","output-quality":"auto"},u=(l({fill:"307:203;center,top"},s),l({fill:"720:480;center,top"},s),l({downsize:"640:*",fill:"*:*;center,top"},s),l({fill:"480:480;center,top"},s),function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;if(!e)return"0";var n={billion:"B",million:"M",thousand:"K"};return e>=1e9?(e/1e9).toFixed(t).replace(/\.0+$/,"")+n.billion:e>=1e6?(e/1e6).toFixed(t).replace(/\.0+$/,"")+n.million:e>=1e3?(e/1e3).toFixed(t).replace(/\.0+$/,"")+n.thousand:e.toString()}),d=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"web-hf";return!e||e.indexOf("origin=")>-1?e:t?(0,a.F1)(e,{origin:t}):e},f=function(e){if(!e)return"";var t=e.color1,n=e.color2,r=e.textColor,o=e.darkModeColor1,a=e.darkModeColor2,i=e.darkModeTextColor;return"\n    :root {\n      --themeColor1: ".concat(t||"initial",";\n      --themeColor2: ").concat(n||"initial",";\n      --themeTextColor: ").concat(r||"initial",";\n      --themeDarkModeColor1: ").concat(o||"initial",";\n      --themeDarkModeColor2: ").concat(a||"initial",";\n      --themeDarkModeTextColor: ").concat(i||"initial",";\n    }")},p=function(e){var t,n,r,o,a,i,c,l,s,u,d,f,p,v,m,g,b;if(!e)return"";var h=(null===e||void 0===e||null===(t=e.data)||void 0===t?void 0:t.assets)||{},_=null===e||void 0===e||null===(n=e.data)||void 0===n||null===(r=n.tab)||void 0===r?void 0:r.highlight_color,y=null===e||void 0===e||null===(o=e.data)||void 0===o||null===(a=o.tab)||void 0===a?void 0:a.highlight_contrast_color,x=h.cursor?"url('".concat(h.cursor,"'), auto"):"auto",j="image"===(null===(i=h.background)||void 0===i?void 0:i.type)&&(null===(c=h.background)||void 0===c?void 0:c.value)?"url('".concat(h.background.value,"') no-repeat fixed center / cover"):null===(l=h.background)||void 0===l?void 0:l.value,w=(null===e||void 0===e||null===(s=e.data)||void 0===s||null===(u=s.tab)||void 0===u||null===(d=u.background)||void 0===d?void 0:d.value)&&"image"===(null===e||void 0===e||null===(f=e.data)||void 0===f||null===(p=f.tab)||void 0===p||null===(v=p.background)||void 0===v?void 0:v.type)?"url('".concat(e.data.tab.background.value,"') no-repeat fixed center / cover"):null===e||void 0===e||null===(m=e.data)||void 0===m||null===(g=m.tab)||void 0===g||null===(b=g.background)||void 0===b?void 0:b.value;return"\n    :root {\n      --sponsorshipBackground: ".concat(j||"initial",";\n      --sponsorshipTabBackground: ").concat(w||"initial",", ").concat(j||"initial",";\n      --sponsorshipCursor: ").concat(x||"auto",";\n      --sponsorshipLogoColor: ").concat(h.logo_color||"initial",";\n      --sponsorshipHighlightColor: ").concat(_||"initial",";\n      --sponsorshipHighlightContrastColor: ").concat(y||"initial",";\n    }")},v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e&&Object.keys(e).length&&(null===t||void 0===t?void 0:t.length)?Object.keys(e).reduce((function(n,r){return r.startsWith(t)&&(n[r]=e[r]),n}),{}):{}},m=function(e,t){var n,r=t?new Date(t):new Date,o=r.getMonth()+1,a=r.getDate();return(null===e||void 0===e?void 0:e.length)&&(null===(n=e[0].content)||void 0===n?void 0:n.facts)?e[0].content.facts[o][a]:null};!function(){var e,t=(e=o().mark((function e(){var t,n,r,a,i,c,l,s,u,d,f,p,v,m,g,b,h,_,y,x,j,w,k,O=arguments;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=O.length>0&&void 0!==O[0]?O[0]:[],n=0,t&&t.length){e.next=4;break}return e.abrupt("return",n);case 4:r=["discussion_question"],a=!0,i=!1,c=void 0,e.prev=6,l=t[Symbol.iterator]();case 8:if(a=(s=l.next()).done){e.next=31;break}for(u=s.value,d=u.items,f=void 0===d?[]:d,p=!0,v=!1,m=void 0,e.prev=11,g=f[Symbol.iterator]();!(p=(b=g.next()).done);p=!0)h=b.value,_=h.content,y=void 0===_?{}:_,x=h.object_type,j=void 0===x?"":x,k=null===(w=y.comments)||void 0===w?void 0:w.count,r.includes(j)&&k&&(n+=null!==k&&void 0!==k?k:0);e.next=19;break;case 15:e.prev=15,e.t0=e.catch(11),v=!0,m=e.t0;case 19:e.prev=19,e.prev=20,p||null==g.return||g.return();case 22:if(e.prev=22,!v){e.next=25;break}throw m;case 25:return e.finish(22);case 26:return e.finish(19);case 27:case 28:a=!0,e.next=8;break;case 31:e.next=37;break;case 33:e.prev=33,e.t1=e.catch(6),i=!0,c=e.t1;case 37:e.prev=37,e.prev=38,a||null==l.return||l.return();case 40:if(e.prev=40,!i){e.next=43;break}throw c;case 43:return e.finish(40);case 44:return e.finish(37);case 45:return e.abrupt("return",n);case 47:case"end":return e.stop()}}),e,null,[[6,33,37,45],[11,15,19,27],[20,,22,26],[38,,40,44]])})),function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function c(e){i(a,r,o,c,l,"next",e)}function l(e){i(a,r,o,c,l,"throw",e)}c(void 0)}))})}()},5819:function(e,t,n){"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a=[],i=!0,c=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(l){c=!0,o=l}finally{try{i||null==n.return||n.return()}finally{if(c)throw o}}return a}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return r(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}n.d(t,{b:function(){return a}});var a=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(!e)return e;var n=o(e.split("?"),2),r=n[0],a=n[1],i=void 0===a?"":a,c=new URLSearchParams(i);t&&(c.delete("origin"),c.append("origin",t));var l=c.toString();return l?"".concat(r,"?").concat(l):r}},20125:function(e,t,n){"use strict";n.d(t,{a:function(){return o}});var r="user-entries",o=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.namespace,n=void 0===t?r:t,o=e.limit,a=void 0===o?100:o;return{getAll:function(){try{return JSON.parse(window.localStorage.getItem(n))||[]}catch(e){return[]}},get:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!e)throw new Error("An `entry` value is required.");var t=this.getAll();return t.find((function(t){return t.startsWith(e)}))||""},set:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!e)throw new Error("An `entry` value is required.");var t=this.getAll();t.includes(e)?t.splice(t.indexOf(e),1):t.unshift(e),a&&t.length>a&&(t=t.slice(0,a)),window.localStorage.setItem(n,JSON.stringify(t))}}}},81346:function(e){e.exports={container:"BFPContent_container__MHUEh",reactions:"BFPContent_reactions__17Dgb",faceoff_bracket:"BFPContent_faceoff_bracket__Ply0E"}},42630:function(e){e.exports={container:"confetti_container__Fh3oR",emojis:"confetti_emojis__1pg_f",emoji:"confetti_emoji__8_nbH",verticalLayer:"confetti_verticalLayer__5gzHh",scaleLayer:"confetti_scaleLayer__q7z_w",opacityLayer:"confetti_opacityLayer__GZPeQ",animate:"confetti_animate__YW9ip",fadeInOut:"confetti_fadeInOut__0cAP8"}},83816:function(e){e.exports={reactions:"emojiReactions_reactions__vzAf7",list:"emojiReactions_list__aG62w",listItem:"emojiReactions_listItem__2_Fva",total:"emojiReactions_total__YTI3d",react:"emojiReactions_react__PuQ7E",dialog:"emojiReactions_dialog__qKxDH",reactionCount:"emojiReactions_reactionCount__dbLFy",bounceIn:"emojiReactions_bounceIn__Vig3K",animateClose:"emojiReactions_animateClose__Nj0K_",bounceOut:"emojiReactions_bounceOut__GTXJT",addNewEmoji:"emojiReactions_addNewEmoji__kl3rq",userReaction:"emojiReactions_userReaction__aeWcC",openEmojiDialogBtn:"emojiReactions_openEmojiDialogBtn__xTsT0",clearAnimation:"emojiReactions_clearAnimation__DWyHO",confetti:"emojiReactions_confetti__7cYwh",fadeIn:"emojiReactions_fadeIn__uiThL",fadeOut:"emojiReactions_fadeOut__mxTM_"}},44677:function(e){e.exports={feedArticle:"singleCard_feedArticle__G7_RX",thumbnailWrapper:"singleCard_thumbnailWrapper__yZfa9",textWrapper:"singleCard_textWrapper__gJ1S8",heading:"singleCard_heading__bGGsJ",thumbnail:"singleCard_thumbnail__IvWZb",index:"singleCard_index__iL_5O",statsWrapper:"singleCard_statsWrapper__OlxGB",statsLink:"singleCard_statsLink__qOHr_",statistics:"singleCard_statistics__rBnlD",reactions:"singleCard_reactions__1lvAV",topicPostCard:"singleCard_topicPostCard__qn4yu",category:"singleCard_category__1662M",infoLabel:"singleCard_infoLabel___TKFv",small:"singleCard_small__31QaC",two_columns:"singleCard_two_columns__ZqUqP",medium:"singleCard_medium__eEGDO",standard_page:"singleCard_standard_page__zQhri"}},8996:function(e){e.exports={splashCard:"splashCard_splashCard__9xSrc",heading:"splashCard_heading__EzXmj",stats:"splashCard_stats___ebyB",statsLink:"splashCard_statsLink__ZigVZ",statsWrapper:"splashCard_statsWrapper__fCnn_",thumbnail:"splashCard_thumbnail__CFymG",reactions:"splashCard_reactions__krz7S",index:"splashCard_index__IXlMQ",tagLabel:"splashCard_tagLabel__TfoIW",linkWrap:"splashCard_linkWrap__PEcVi",statistics:"splashCard_statistics__oWozm",primaryPost:"splashCard_primaryPost__bx1Yu",textWrapper:"splashCard_textWrapper__xlULB",hasIndex:"splashCard_hasIndex__kETo5",primaryCard:"splashCard_primaryCard____anA",hasReactions:"splashCard_hasReactions__71k2y",secondaryPost:"splashCard_secondaryPost__rK8f3",topicSplashCard:"splashCard_topicSplashCard__I_6H9",topicSplashCardNoBanner:"splashCard_topicSplashCardNoBanner__Hu_MJ",topicSplashCardSponsored:"splashCard_topicSplashCardSponsored__N9zhH",packageSplashCard:"splashCard_packageSplashCard__HGn5e",standard_page:"splashCard_standard_page__dvIF4"}},69327:function(e){e.exports={thumbnail:"feedCard_thumbnail__X4TUe",info:"feedCard_info__GAAPM",tag:"feedCard_tag__FPZPO",stats:"feedCard_stats__JBOXT",section:"feedCard_section__F9ai9"}},87720:function(e){e.exports={feedSocialContainer:"feedSocial_feedSocialContainer__khcLS",title:"feedSocial_title__pXJv1",reactions:"feedSocial_reactions__zIkiJ",header:"feedSocial_header__P1__C",description:"feedSocial_description__Z0yNM",embed:"feedSocial_embed__uSkkZ"}},51078:function(e){e.exports={button:"FeedsButton_button__niygo"}},14839:function(e){e.exports={container:"funmodule_container__ILs7e",scrollableContainer:"funmodule_scrollableContainer__orVAw",notSingleColumn:"funmodule_notSingleColumn__WSgWd",waves8:"funmodule_waves8___HYqH",heading:"funmodule_heading__l_cgv",title:"funmodule_title__Uu4V3",subTitle:"funmodule_subTitle__C1Yep",addDiscussion:"funmodule_addDiscussion__sA3Wm",replies:"funmodule_replies__x00BD",twoColumns:"funmodule_twoColumns__Zl7qf",textWrapper:"funmodule_textWrapper__geJzl",imageWrapper:"funmodule_imageWrapper__BI5Mc",imageWrapperWide:"funmodule_imageWrapperWide__uZerk",withDecor:"funmodule_withDecor__P1U5w",text:"funmodule_text__057EQ",avatar:"funmodule_avatar__km0RC",article:"funmodule_article__8F1_G",imagePlaceholder:"funmodule_imagePlaceholder__KvVZa",inOut:"funmodule_inOut__aFDjT",readMore:"funmodule_readMore__tpgiV",caretIcon:"funmodule_caretIcon__dN9St",list:"funmodule_list__ZqOdQ",slide:"funmodule_slide__vdwm7",columnContainer:"funmodule_columnContainer__rrtnR",topComment:"funmodule_topComment__OhZYe",wrap:"funmodule_wrap__5BnQw",votingAnswer:"funmodule_votingAnswer__m1pfp",active:"funmodule_active__RO4dZ",voteTitle:"funmodule_voteTitle__UhpGy",answerTitle:"funmodule_answerTitle__2UuuO",answerImage:"funmodule_answerImage__Tk6_m",answerValue:"funmodule_answerValue__nyygB",votingAnswers:"funmodule_votingAnswers__dLqF9",loading:"funmodule_loading__4f8Rf",isVoted:"funmodule_isVoted__CKHUW",headerWrap:"funmodule_headerWrap__EOHsa"}},99523:function(e){e.exports={package:"package_package__L3CEO",content:"package_content__owZug",title:"package_title__PCRbQ",postsWrap:"package_postsWrap__WV1oi",topicPosts:"package_topicPosts__VSJpg",trendingPost:"package_trendingPost__DF316",trendingPosts:"package_trendingPosts__5xhV0",thumbnail:"package_thumbnail__mIR7a",topicPost:"package_topicPost__jb6gO",topicTitle:"package_topicTitle__vovdq",cta:"package_cta__rBKBI"}},75471:function(e){e.exports={pageHeader:"pageHeader_pageHeader__ATtKT",imageContainer:"pageHeader_imageContainer__qticB",textWrapper:"pageHeader_textWrapper__Hx4gy",title:"pageHeader_title__C5QAK",description:"pageHeader_description__QLruw"}},35335:function(e){e.exports={brand:"sponsor_brand__ORlmm",name:"sponsor_name__OJyo1",logo:"sponsor_logo__DcckR",isPromoted:"sponsor_isPromoted__y1wza",label:"sponsor_label__5lKDd"}},52003:function(e){e.exports="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogIDxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNFREVERUQiLz4KPC9zdmc+Cg=="}}]);
//# sourceMappingURL=941-f7474f408b2824c4.js.map