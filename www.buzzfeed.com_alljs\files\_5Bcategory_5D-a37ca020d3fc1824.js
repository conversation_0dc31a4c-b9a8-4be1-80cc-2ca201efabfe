(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[716],{74465:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/[category]",function(){return t(29493)}])},29493:function(e,n,t){"use strict";t.r(n),t.d(n,{CategoryPage:function(){return h},__N_SSP:function(){return g}});var r=t(52322),i=t(22887),o=t(50142),c=t(75592),u=t(21451),a=t(13269),s=t(45626),l=t(35193),f=t(1272),d=t(56392),j=t(50232),_=t.n(j);function b(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function v(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},r=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),r.forEach((function(n){b(e,n,t[n])}))}return e}var g=!0;function h(e){var n=e.header,t=e.feed,i=e.pageConfig,j=(null===t||void 0===t?void 0:t.next)||"",b=(null===t||void 0===t?void 0:t.items)||[];return(0,d.T)(),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.default,{pageConfig:i}),(0,r.jsx)(f.Z,v({},n)),(0,r.jsxs)("main",{className:_().content,children:[(0,r.jsx)(l.Z,{}),(0,r.jsxs)("div",{className:_().body,children:[(0,r.jsx)("div",{className:_().feed,children:(0,r.jsx)(a.ZP,{items:b,nextUrl:j})}),(0,r.jsx)(u.Z,{children:(0,r.jsx)(s.Z,{})})]}),(0,r.jsx)(o.z,{})]})]})}n.default=(0,i.withTranslation)("common")(h)}},function(e){e.O(0,[297,592,727,774,888,179],(function(){return n=74465,e(e.s=n);var n}));var n=e.O();_N_E=n}]);
//# sourceMappingURL=[category]-a37ca020d3fc1824.js.map