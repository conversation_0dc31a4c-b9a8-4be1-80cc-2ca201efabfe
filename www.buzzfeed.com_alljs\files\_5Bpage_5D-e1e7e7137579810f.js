(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[523],{7789:function(n,e,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/page/[page]",function(){return t(75073)}])},39034:function(n,e,t){"use strict";var r=t(52322),i=t(2784),a=t(37963),o=t(97143),u=t(55190),c=t(57084),s=t(91807),d=t(48243),l=t(97196),f=t(49277),g=t(30353);e.Z=function(n){var e=n.children,t=n.userGeo,m=(0,f.Z)("tracking"),p=m.consentValue,v=m.isConsentKnown,h=m.isConsentReady,y=g.cG&&"US"!==t,_=(0,i.useContext)(d.Ui),x=_.context_page_id,b=_.context_page_type,w=_.destination,j=_.page_edition;return(0,l.cB)(),(0,l.kJ)({}),(0,l.Jb)(),(0,l.uV)(),(0,l.sv)({context_page_id:x,context_page_type:b,destination:w,page_edition:j}),(0,r.jsx)(d.xr,{value:{tracking:{consentValue:p,isConsentKnown:v,isConsentReady:h}},children:(0,r.jsxs)(a.Z,{children:[(0,r.jsx)(c.Z,{}),(0,r.jsx)(o.Z,{}),y&&(0,r.jsx)(u.Z,{}),(0,r.jsx)(s.Z,{children:e})]})})}},74104:function(n,e,t){"use strict";t.d(e,{T:function(){return a}});var r=t(2784),i=t(48243);function a(){var n=(0,r.useContext)(i.z1),e=n.adsDisabled,t=n.isSponsored,a=n.membershipAvailable,o=function(n){var r;"topic-nav-loaded"!==(null===n||void 0===n||null===(r=n.data)||void 0===r?void 0:r.type)||e||t||!a||window.postMessage({type:"show-membership-promo-button"},"*")};(0,r.useEffect)((function(){return window.addEventListener("message",o),function(){window.removeEventListener("message",o)}}),[])}},75073:function(n,e,t){"use strict";t.r(e),t.d(e,{__N_SSP:function(){return x},Page:function(){return b}});var r=t(52322),i=t(2784),a=t(74104),o=t(48243),u=t(35464),c=t(29169),s=t(28964),d=t(86),l=t(14847),f=t(45412),g=t(31066),m=t(39034),p=t(62187),v=t.n(p);function h(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function y(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),r.forEach((function(e){h(n,e,t[e])}))}return n}var _=function(n){return{position_in_unit:n}},x=!0;function b(n){var e=n.header,t=n.metadata,p=void 0===t?{}:t,h=n.pageConfig,x=n.zoneList,b=void 0===x?[]:x,w=n.standardPageName,j=n.userGeo,P=(0,s.tc)(null===p||void 0===p?void 0:p.theme),O=(0,i.useContext)(o.Ui).page_edition,S=(0,i.useRef)({});(0,a.T)();var N=(0,i.useMemo)((function(){return b.reduce((function(n,e,t){var i,a,o=(0,r.jsx)(l.Z,{componentIndex:t,data:e,getTrackingDataWithPosition:_,headline:(null===e||void 0===e||null===(i=e.metadata)||void 0===i||null===(a=i.display_options)||void 0===a?void 0:a.show_display_name)?e.display_name:"",isTrackable:!0,maxItemsPerSequence:0,pageName:"standard_page",sharedIndexRef:S,showEndOfFeedCard:!1,showSidebar:!1,trackingData:{unit_type:"feed",unit_name:"".concat(e.name,"|").concat(t)}},t);return(null===e||void 0===e?void 0:e.hasPinnedItems)&&!n.contentHeader&&0===t?n.contentHeader=o:n.content.push(o),n}),{contentHeader:null,content:[]})}),[b,S]);return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(m.Z,{userGeo:j,children:(0,r.jsxs)(u.Z,{edition:O,pagePath:w,pageName:"standard_page",children:[(0,r.jsx)(c.Z,{type:"toolbar"}),(0,r.jsx)(c.Z,{className:"Ad--standard",type:"awareness"}),(0,r.jsx)(g.Z,y({},e)),(0,r.jsx)(d.default,{page:"standard",pageConfig:h,inlineCss:P,userGeo:j}),(0,r.jsxs)("main",{className:"feed-content-area standard-page ".concat(v().main),children:[N.contentHeader&&(0,r.jsx)("section",{role:"region",children:N.contentHeader}),(0,r.jsx)("div",{children:N.content}),(0,r.jsx)(f.Z,{position:"bigstory"})]})]})})})}e.default=b},36864:function(n,e,t){"use strict";var r=t(76635);function i(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function a(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!==typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,a=[],o=!0,u=!1;try{for(t=t.call(n);!(o=(r=t.next()).done)&&(a.push(r.value),!e||a.length!==e);o=!0);}catch(c){u=!0,i=c}finally{try{o||null==t.return||t.return()}finally{if(u)throw i}}return a}}(n,e)||function(n,e){if(!n)return;if("string"===typeof n)return i(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return i(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}e.Z={add:function(n){for(var e=arguments.length,t=new Array(e>1?e-1:0),i=1;i<e;i++)t[i-1]=arguments[i];return(0,r.unionBy)(n,t,JSON.stringify)},exclude:function(n){for(var e=arguments.length,t=new Array(e>1?e-1:0),i=1;i<e;i++)t[i-1]=arguments[i];return(0,r.differenceBy)(n,t,JSON.stringify)},_filterProgrammatic:function(n){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return!0};return n=n.map(JSON.stringify),e.filter((function(e){return-1===n.indexOf(JSON.stringify(e))||t(e)}))},filterProgrammatic:function(n,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t.min,i=void 0===r?null:r,a=t.max,o=void 0===a?null:a;return this._filterProgrammatic(n,e,(function(n){return!!(i&&n[0]>=i[0]||o&&n[0]<=o[0])}))},excludeProgrammatic:function(n,e){return this._filterProgrammatic(n,e,(function(){return!1}))},getProgrammatic:function(n,e){return(0,r.differenceBy)(e,this.excludeProgrammatic(n,e),JSON.stringify)},isProgrammatic:function(n,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t.strict,i=void 0===r||r,o=this.getProgrammatic(n,[e]),u=1===o.length;if(u)return!0;if(!i)try{var c=a(e,2),s=c[0],d=c[1];return s>15&&d>15}catch(l){return!1}return!1},isEqual:function(n,e){return n===e||JSON.stringify(n)===JSON.stringify(e)},contains:function(n,e){var t=this;return n.filter((function(n){return t.isEqual(n,e)})).length>0}}},62187:function(n){n.exports={main:"page_main__JcaJd"}}},function(n){n.O(0,[182,167,995,852,195,407,86,196,941,847,774,888,179],(function(){return e=7789,n(n.s=e);var e}));var e=n.O();_N_E=e}]);
//# sourceMappingURL=[page]-e1e7e7137579810f.js.map