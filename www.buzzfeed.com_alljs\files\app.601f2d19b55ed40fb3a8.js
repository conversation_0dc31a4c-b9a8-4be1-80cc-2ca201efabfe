!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=91)}([function(e,t,n){"use strict";n.d(t,"a",(function(){return j})),n.d(t,"b",(function(){return k})),n.d(t,"c",(function(){return q})),n.d(t,"d",(function(){return G})),n.d(t,"e",(function(){return v})),n.d(t,"f",(function(){return O})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return V})),n.d(t,"i",(function(){return o})),n.d(t,"j",(function(){return W})),n.d(t,"k",(function(){return I}));var r,o,i,a,c,s,u,l,f,p,d,h={},g=[],m=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,b=Array.isArray;function y(e,t){for(var n in t)e[n]=t[n];return e}function _(e){var t=e.parentNode;t&&t.removeChild(e)}function v(e,t,n){var o,i,a,c={};for(a in t)"key"==a?o=t[a]:"ref"==a?i=t[a]:c[a]=t[a];if(arguments.length>2&&(c.children=arguments.length>3?r.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(a in e.defaultProps)void 0===c[a]&&(c[a]=e.defaultProps[a]);return w(e,c,o,i,null)}function w(e,t,n,r,a){var c={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==a?++i:a,__i:-1,__u:0};return null==a&&null!=o.vnode&&o.vnode(c),c}function O(){return{current:null}}function k(e){return e.children}function j(e,t){this.props=e,this.context=t}function C(e,t){if(null==t)return e.__?C(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?C(e):null}function S(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return S(e)}}function x(e){(!e.__d&&(e.__d=!0)&&a.push(e)&&!N.__r++||c!==o.debounceRendering)&&((c=o.debounceRendering)||s)(N)}function N(){var e,t,n,r,i,c,s,l;for(a.sort(u);e=a.shift();)e.__d&&(t=a.length,r=void 0,c=(i=(n=e).__v).__e,s=[],l=[],n.__P&&((r=y({},i)).__v=i.__v+1,o.vnode&&o.vnode(r),D(n.__P,r,i,n.__n,void 0!==n.__P.ownerSVGElement,32&i.__u?[c]:null,s,null==c?C(i):c,!!(32&i.__u),l),r.__v=i.__v,r.__.__k[r.__i]=r,R(s,r,l),r.__e!=c&&S(r)),a.length>t&&a.sort(u));N.__r=0}function E(e,t,n,r,o,i,a,c,s,u,l){var f,p,d,m,b,y=r&&r.__k||g,_=t.length;for(n.__d=s,P(n,t,y),s=n.__d,f=0;f<_;f++)null!=(d=n.__k[f])&&"boolean"!=typeof d&&"function"!=typeof d&&(p=-1===d.__i?h:y[d.__i]||h,d.__i=f,D(e,d,p,o,i,a,c,s,u,l),m=d.__e,d.ref&&p.ref!=d.ref&&(p.ref&&F(p.ref,null,d),l.push(d.ref,d.__c||m,d)),null==b&&null!=m&&(b=m),65536&d.__u||p.__k===d.__k?(s&&!s.isConnected&&(s=C(p)),s=T(d,s,e)):"function"==typeof d.type&&void 0!==d.__d?s=d.__d:m&&(s=m.nextSibling),d.__d=void 0,d.__u&=-196609);n.__d=s,n.__e=b}function P(e,t,n){var r,o,i,a,c,s=t.length,u=n.length,l=u,f=0;for(e.__k=[],r=0;r<s;r++)a=r+f,null!=(o=e.__k[r]=null==(o=t[r])||"boolean"==typeof o||"function"==typeof o?null:"string"==typeof o||"number"==typeof o||"bigint"==typeof o||o.constructor==String?w(null,o,null,null,null):b(o)?w(k,{children:o},null,null,null):void 0===o.constructor&&o.__b>0?w(o.type,o.props,o.key,o.ref?o.ref:null,o.__v):o)?(o.__=e,o.__b=e.__b+1,c=z(o,n,a,l),o.__i=c,i=null,-1!==c&&(l--,(i=n[c])&&(i.__u|=131072)),null==i||null===i.__v?(-1==c&&f--,"function"!=typeof o.type&&(o.__u|=65536)):c!==a&&(c===a+1?f++:c>a?l>s-a?f+=c-a:f--:c<a?c==a-1&&(f=c-a):f=0,c!==r+f&&(o.__u|=65536))):(i=n[a])&&null==i.key&&i.__e&&0==(131072&i.__u)&&(i.__e==e.__d&&(e.__d=C(i)),U(i,i,!1),n[a]=null,l--);if(l)for(r=0;r<u;r++)null!=(i=n[r])&&0==(131072&i.__u)&&(i.__e==e.__d&&(e.__d=C(i)),U(i,i))}function T(e,t,n){var r,o;if("function"==typeof e.type){for(r=e.__k,o=0;r&&o<r.length;o++)r[o]&&(r[o].__=e,t=T(r[o],t,n));return t}e.__e!=t&&(n.insertBefore(e.__e,t||null),t=e.__e);do{t=t&&t.nextSibling}while(null!=t&&8===t.nodeType);return t}function I(e,t){return t=t||[],null==e||"boolean"==typeof e||(b(e)?e.some((function(e){I(e,t)})):t.push(e)),t}function z(e,t,n,r){var o=e.key,i=e.type,a=n-1,c=n+1,s=t[n];if(null===s||s&&o==s.key&&i===s.type&&0==(131072&s.__u))return n;if(r>(null!=s&&0==(131072&s.__u)?1:0))for(;a>=0||c<t.length;){if(a>=0){if((s=t[a])&&0==(131072&s.__u)&&o==s.key&&i===s.type)return a;a--}if(c<t.length){if((s=t[c])&&0==(131072&s.__u)&&o==s.key&&i===s.type)return c;c++}}return-1}function L(e,t,n){"-"===t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||m.test(t)?n:n+"px"}function M(e,t,n,r,o){var i;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||L(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||L(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?r?n.u=r.u:(n.u=l,e.addEventListener(t,i?p:f,i)):e.removeEventListener(t,i?p:f,i);else{if(o)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,n))}}function A(e){return function(t){if(this.l){var n=this.l[t.type+e];if(null==t.t)t.t=l++;else if(t.t<n.u)return;return n(o.event?o.event(t):t)}}}function D(e,t,n,r,i,a,c,s,u,l){var f,p,d,h,g,m,_,v,w,O,C,S,x,N,P,T=t.type;if(void 0!==t.constructor)return null;128&n.__u&&(u=!!(32&n.__u),a=[s=t.__e=n.__e]),(f=o.__b)&&f(t);e:if("function"==typeof T)try{if(v=t.props,w=(f=T.contextType)&&r[f.__c],O=f?w?w.props.value:f.__:r,n.__c?_=(p=t.__c=n.__c).__=p.__E:("prototype"in T&&T.prototype.render?t.__c=p=new T(v,O):(t.__c=p=new j(v,O),p.constructor=T,p.render=H),w&&w.sub(p),p.props=v,p.state||(p.state={}),p.context=O,p.__n=r,d=p.__d=!0,p.__h=[],p._sb=[]),null==p.__s&&(p.__s=p.state),null!=T.getDerivedStateFromProps&&(p.__s==p.state&&(p.__s=y({},p.__s)),y(p.__s,T.getDerivedStateFromProps(v,p.__s))),h=p.props,g=p.state,p.__v=t,d)null==T.getDerivedStateFromProps&&null!=p.componentWillMount&&p.componentWillMount(),null!=p.componentDidMount&&p.__h.push(p.componentDidMount);else{if(null==T.getDerivedStateFromProps&&v!==h&&null!=p.componentWillReceiveProps&&p.componentWillReceiveProps(v,O),!p.__e&&(null!=p.shouldComponentUpdate&&!1===p.shouldComponentUpdate(v,p.__s,O)||t.__v===n.__v)){for(t.__v!==n.__v&&(p.props=v,p.state=p.__s,p.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.forEach((function(e){e&&(e.__=t)})),C=0;C<p._sb.length;C++)p.__h.push(p._sb[C]);p._sb=[],p.__h.length&&c.push(p);break e}null!=p.componentWillUpdate&&p.componentWillUpdate(v,p.__s,O),null!=p.componentDidUpdate&&p.__h.push((function(){p.componentDidUpdate(h,g,m)}))}if(p.context=O,p.props=v,p.__P=e,p.__e=!1,S=o.__r,x=0,"prototype"in T&&T.prototype.render){for(p.state=p.__s,p.__d=!1,S&&S(t),f=p.render(p.props,p.state,p.context),N=0;N<p._sb.length;N++)p.__h.push(p._sb[N]);p._sb=[]}else do{p.__d=!1,S&&S(t),f=p.render(p.props,p.state,p.context),p.state=p.__s}while(p.__d&&++x<25);p.state=p.__s,null!=p.getChildContext&&(r=y(y({},r),p.getChildContext())),d||null==p.getSnapshotBeforeUpdate||(m=p.getSnapshotBeforeUpdate(h,g)),E(e,b(P=null!=f&&f.type===k&&null==f.key?f.props.children:f)?P:[P],t,n,r,i,a,c,s,u,l),p.base=t.__e,t.__u&=-161,p.__h.length&&c.push(p),_&&(p.__E=p.__=null)}catch(e){t.__v=null,u||null!=a?(t.__e=s,t.__u|=u?160:32,a[a.indexOf(s)]=null):(t.__e=n.__e,t.__k=n.__k),o.__e(e,t,n)}else null==a&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=B(n.__e,t,n,r,i,a,c,u,l);(f=o.diffed)&&f(t)}function R(e,t,n){t.__d=void 0;for(var r=0;r<n.length;r++)F(n[r],n[++r],n[++r]);o.__c&&o.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){o.__e(e,t.__v)}}))}function B(e,t,n,o,i,a,c,s,u){var l,f,p,d,g,m,y,v=n.props,w=t.props,O=t.type;if("svg"===O&&(i=!0),null!=a)for(l=0;l<a.length;l++)if((g=a[l])&&"setAttribute"in g==!!O&&(O?g.localName===O:3===g.nodeType)){e=g,a[l]=null;break}if(null==e){if(null===O)return document.createTextNode(w);e=i?document.createElementNS("http://www.w3.org/2000/svg",O):document.createElement(O,w.is&&w),a=null,s=!1}if(null===O)v===w||s&&e.data===w||(e.data=w);else{if(a=a&&r.call(e.childNodes),v=n.props||h,!s&&null!=a)for(v={},l=0;l<e.attributes.length;l++)v[(g=e.attributes[l]).name]=g.value;for(l in v)g=v[l],"children"==l||("dangerouslySetInnerHTML"==l?p=g:"key"===l||l in w||M(e,l,null,g,i));for(l in w)g=w[l],"children"==l?d=g:"dangerouslySetInnerHTML"==l?f=g:"value"==l?m=g:"checked"==l?y=g:"key"===l||s&&"function"!=typeof g||v[l]===g||M(e,l,g,v[l],i);if(f)s||p&&(f.__html===p.__html||f.__html===e.innerHTML)||(e.innerHTML=f.__html),t.__k=[];else if(p&&(e.innerHTML=""),E(e,b(d)?d:[d],t,n,o,i&&"foreignObject"!==O,a,c,a?a[0]:n.__k&&C(n,0),s,u),null!=a)for(l=a.length;l--;)null!=a[l]&&_(a[l]);s||(l="value",void 0!==m&&(m!==e[l]||"progress"===O&&!m||"option"===O&&m!==v[l])&&M(e,l,m,v[l],!1),l="checked",void 0!==y&&y!==e[l]&&M(e,l,y,v[l],!1))}return e}function F(e,t,n){try{"function"==typeof e?e(t):e.current=t}catch(e){o.__e(e,n)}}function U(e,t,n){var r,i;if(o.unmount&&o.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||F(r,null,t)),null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){o.__e(e,t)}r.base=r.__P=null}if(r=e.__k)for(i=0;i<r.length;i++)r[i]&&U(r[i],t,n||"function"!=typeof e.type);n||null==e.__e||_(e.__e),e.__c=e.__=e.__e=e.__d=void 0}function H(e,t,n){return this.constructor(e,n)}function W(e,t,n){var i,a,c,s;o.__&&o.__(e,t),a=(i="function"==typeof n)?null:n&&n.__k||t.__k,c=[],s=[],D(t,e=(!i&&n||t).__k=v(k,null,[e]),a||h,h,void 0!==t.ownerSVGElement,!i&&n?[n]:a?null:t.firstChild?r.call(t.childNodes):null,c,!i&&n?n:a?a.__e:t.firstChild,i,s),R(c,e,s)}function V(e,t){W(e,t,V)}function q(e,t,n){var o,i,a,c,s=y({},e.props);for(a in e.type&&e.type.defaultProps&&(c=e.type.defaultProps),t)"key"==a?o=t[a]:"ref"==a?i=t[a]:s[a]=void 0===t[a]&&void 0!==c?c[a]:t[a];return arguments.length>2&&(s.children=arguments.length>3?r.call(arguments,2):n),w(e.type,s,o||e.key,i||e.ref,null)}function G(e,t){var n={__c:t="__cC"+d++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,r;return this.getChildContext||(n=[],(r={})[t]=this,this.getChildContext=function(){return r},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.some((function(e){e.__e=!0,x(e)}))},this.sub=function(e){n.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n.splice(n.indexOf(e),1),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n}r=g.slice,o={__e:function(e,t,n,r){for(var o,i,a;t=t.__;)if((o=t.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(e)),a=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(e,r||{}),a=o.__d),a)return o.__E=o}catch(t){e=t}throw e}},i=0,j.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=y({},this.state),"function"==typeof e&&(e=e(y({},n),this.props)),e&&y(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),x(this))},j.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),x(this))},j.prototype.render=k,a=[],s="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,u=function(e,t){return e.__v.__b-t.__v.__b},N.__r=0,l=0,f=A(!1),p=A(!0),d=0},function(e,t,n){"use strict";n.r(t),n.d(t,"Component",(function(){return c.a})),n.d(t,"Fragment",(function(){return c.b})),n.d(t,"createContext",(function(){return c.d})),n.d(t,"createElement",(function(){return c.e})),n.d(t,"createRef",(function(){return c.f})),n.d(t,"useCallback",(function(){return S})),n.d(t,"useContext",(function(){return x})),n.d(t,"useDebugValue",(function(){return N})),n.d(t,"useEffect",(function(){return w})),n.d(t,"useErrorBoundary",(function(){return E})),n.d(t,"useId",(function(){return P})),n.d(t,"useImperativeHandle",(function(){return j})),n.d(t,"useLayoutEffect",(function(){return O})),n.d(t,"useMemo",(function(){return C})),n.d(t,"useReducer",(function(){return v})),n.d(t,"useRef",(function(){return k})),n.d(t,"useState",(function(){return _})),n.d(t,"Children",(function(){return G})),n.d(t,"PureComponent",(function(){return F})),n.d(t,"StrictMode",(function(){return Te})),n.d(t,"Suspense",(function(){return X})),n.d(t,"SuspenseList",(function(){return J})),n.d(t,"__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED",(function(){return ve})),n.d(t,"cloneElement",(function(){return Se})),n.d(t,"createFactory",(function(){return Oe})),n.d(t,"createPortal",(function(){return ne})),n.d(t,"default",(function(){return Be})),n.d(t,"findDOMNode",(function(){return Ne})),n.d(t,"flushSync",(function(){return Pe})),n.d(t,"forwardRef",(function(){return V})),n.d(t,"hydrate",(function(){return le})),n.d(t,"isElement",(function(){return Ae})),n.d(t,"isFragment",(function(){return je})),n.d(t,"isMemo",(function(){return Ce})),n.d(t,"isValidElement",(function(){return ke})),n.d(t,"lazy",(function(){return $})),n.d(t,"memo",(function(){return U})),n.d(t,"render",(function(){return ue})),n.d(t,"startTransition",(function(){return Ie})),n.d(t,"unmountComponentAtNode",(function(){return xe})),n.d(t,"unstable_batchedUpdates",(function(){return Ee})),n.d(t,"useDeferredValue",(function(){return ze})),n.d(t,"useInsertionEffect",(function(){return Me})),n.d(t,"useSyncExternalStore",(function(){return De})),n.d(t,"useTransition",(function(){return Le})),n.d(t,"version",(function(){return we}));var r,o,i,a,c=n(0),s=0,u=[],l=[],f=c.i,p=f.__b,d=f.__r,h=f.diffed,g=f.__c,m=f.unmount,b=f.__;function y(e,t){f.__h&&f.__h(o,e,s||t),s=0;var n=o.__H||(o.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({__V:l}),n.__[e]}function _(e){return s=1,v(D,e)}function v(e,t,n){var i=y(r++,2);if(i.t=e,!i.__c&&(i.__=[n?n(t):D(void 0,t),function(e){var t=i.__N?i.__N[0]:i.__[0],n=i.t(t,e);t!==n&&(i.__N=[n,i.__[1]],i.__c.setState({}))}],i.__c=o,!o.u)){var a=function(e,t,n){if(!i.__c.__H)return!0;var r=i.__c.__H.__.filter((function(e){return!!e.__c}));if(r.every((function(e){return!e.__N})))return!c||c.call(this,e,t,n);var o=!1;return r.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(o=!0)}})),!(!o&&i.__c.props===e)&&(!c||c.call(this,e,t,n))};o.u=!0;var c=o.shouldComponentUpdate,s=o.componentWillUpdate;o.componentWillUpdate=function(e,t,n){if(this.__e){var r=c;c=void 0,a(e,t,n),c=r}s&&s.call(this,e,t,n)},o.shouldComponentUpdate=a}return i.__N||i.__}function w(e,t){var n=y(r++,3);!f.__s&&A(n.__H,t)&&(n.__=e,n.i=t,o.__H.__h.push(n))}function O(e,t){var n=y(r++,4);!f.__s&&A(n.__H,t)&&(n.__=e,n.i=t,o.__h.push(n))}function k(e){return s=5,C((function(){return{current:e}}),[])}function j(e,t,n){s=6,O((function(){return"function"==typeof e?(e(t()),function(){return e(null)}):e?(e.current=t(),function(){return e.current=null}):void 0}),null==n?n:n.concat(e))}function C(e,t){var n=y(r++,7);return A(n.__H,t)?(n.__V=e(),n.i=t,n.__h=e,n.__V):n.__}function S(e,t){return s=8,C((function(){return e}),t)}function x(e){var t=o.context[e.__c],n=y(r++,9);return n.c=e,t?(null==n.__&&(n.__=!0,t.sub(o)),t.props.value):e.__}function N(e,t){f.useDebugValue&&f.useDebugValue(t?t(e):e)}function E(e){var t=y(r++,10),n=_();return t.__=e,o.componentDidCatch||(o.componentDidCatch=function(e,r){t.__&&t.__(e,r),n[1](e)}),[n[0],function(){n[1](void 0)}]}function P(){var e=y(r++,11);if(!e.__){for(var t=o.__v;null!==t&&!t.__m&&null!==t.__;)t=t.__;var n=t.__m||(t.__m=[0,0]);e.__="P"+n[0]+"-"+n[1]++}return e.__}function T(){for(var e;e=u.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(L),e.__H.__h.forEach(M),e.__H.__h=[]}catch(t){e.__H.__h=[],f.__e(t,e.__v)}}f.__b=function(e){o=null,p&&p(e)},f.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),b&&b(e,t)},f.__r=function(e){d&&d(e),r=0;var t=(o=e.__c).__H;t&&(i===o?(t.__h=[],o.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.__V=l,e.__N=e.i=void 0}))):(t.__h.forEach(L),t.__h.forEach(M),t.__h=[],r=0)),i=o},f.diffed=function(e){h&&h(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==u.push(t)&&a===f.requestAnimationFrame||((a=f.requestAnimationFrame)||z)(T)),t.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.__V!==l&&(e.__=e.__V),e.i=void 0,e.__V=l}))),i=o=null},f.__c=function(e,t){t.some((function(e){try{e.__h.forEach(L),e.__h=e.__h.filter((function(e){return!e.__||M(e)}))}catch(n){t.some((function(e){e.__h&&(e.__h=[])})),t=[],f.__e(n,e.__v)}})),g&&g(e,t)},f.unmount=function(e){m&&m(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach((function(e){try{L(e)}catch(e){t=e}})),n.__H=void 0,t&&f.__e(t,n.__v))};var I="function"==typeof requestAnimationFrame;function z(e){var t,n=function(){clearTimeout(r),I&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);I&&(t=requestAnimationFrame(n))}function L(e){var t=o,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),o=t}function M(e){var t=o;e.__c=e.__(),o=t}function A(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function D(e,t){return"function"==typeof t?t(e):t}function R(e,t){for(var n in t)e[n]=t[n];return e}function B(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var r in t)if("__source"!==r&&e[r]!==t[r])return!0;return!1}function F(e,t){this.props=e,this.context=t}function U(e,t){function n(e){var n=this.props.ref,r=n==e.ref;return!r&&n&&(n.call?n(null):n.current=null),t?!t(this.props,e)||!r:B(this.props,e)}function r(t){return this.shouldComponentUpdate=n,Object(c.e)(e,t)}return r.displayName="Memo("+(e.displayName||e.name)+")",r.prototype.isReactComponent=!0,r.__f=!0,r}(F.prototype=new c.a).isPureReactComponent=!0,F.prototype.shouldComponentUpdate=function(e,t){return B(this.props,e)||B(this.state,t)};var H=c.i.__b;c.i.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),H&&H(e)};var W="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function V(e){function t(t){var n=R({},t);return delete n.ref,e(n,t.ref||null)}return t.$$typeof=W,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(e.displayName||e.name)+")",t}var q=function(e,t){return null==e?null:Object(c.k)(Object(c.k)(e).map(t))},G={map:q,forEach:q,count:function(e){return e?Object(c.k)(e).length:0},only:function(e){var t=Object(c.k)(e);if(1!==t.length)throw"Children.only";return t[0]},toArray:c.k},K=c.i.__e;c.i.__e=function(e,t,n,r){if(e.then)for(var o,i=t;i=i.__;)if((o=i.__c)&&o.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),o.__c(e,t);K(e,t,n,r)};var Z=c.i.unmount;function X(){this.__u=0,this.t=null,this.__b=null}function Y(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function $(e){var t,n,r;function o(o){if(t||(t=e()).then((function(e){n=e.default||e}),(function(e){r=e})),r)throw r;if(!n)throw t;return Object(c.e)(n,o)}return o.displayName="Lazy",o.__f=!0,o}function J(){this.u=null,this.o=null}c.i.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&32&e.__u&&(e.type=null),Z&&Z(e)},(X.prototype=new c.a).__c=function(e,t){var n=t.__c,r=this;null==r.t&&(r.t=[]),r.t.push(n);var o=Y(r.__v),i=!1,a=function(){i||(i=!0,n.__R=null,o?o(c):c())};n.__R=a;var c=function(){if(!--r.__u){if(r.state.__a){var e=r.state.__a;r.__v.__k[0]=function e(t,n,r){return t&&r&&(t.__v=null,t.__k=t.__k&&t.__k.map((function(t){return e(t,n,r)})),t.__c&&t.__c.__P===n&&(t.__e&&r.appendChild(t.__e),t.__c.__e=!0,t.__c.__P=r)),t}(e,e.__c.__P,e.__c.__O)}var t;for(r.setState({__a:r.__b=null});t=r.t.pop();)t.forceUpdate()}};r.__u++||32&t.__u||r.setState({__a:r.__b=r.__v.__k[0]}),e.then(a,a)},X.prototype.componentWillUnmount=function(){this.t=[]},X.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=function e(t,n,r){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach((function(e){"function"==typeof e.__c&&e.__c()})),t.__c.__H=null),null!=(t=R({},t)).__c&&(t.__c.__P===r&&(t.__c.__P=n),t.__c=null),t.__k=t.__k&&t.__k.map((function(t){return e(t,n,r)}))),t}(this.__b,n,r.__O=r.__P)}this.__b=null}var o=t.__a&&Object(c.e)(c.b,null,e.fallback);return o&&(o.__u&=-33),[Object(c.e)(c.b,null,t.__a?null:e.children),o]};var Q=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2]}};function ee(e){return this.getChildContext=function(){return e.context},e.children}function te(e){var t=this,n=e.i;t.componentWillUnmount=function(){Object(c.j)(null,t.l),t.l=null,t.i=null},t.i&&t.i!==n&&t.componentWillUnmount(),t.l||(t.i=n,t.l={nodeType:1,parentNode:n,childNodes:[],appendChild:function(e){this.childNodes.push(e),t.i.appendChild(e)},insertBefore:function(e,n){this.childNodes.push(e),t.i.appendChild(e)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.i.removeChild(e)}}),Object(c.j)(Object(c.e)(ee,{context:t.context},e.__v),t.l)}function ne(e,t){var n=Object(c.e)(te,{__v:e,i:t});return n.containerInfo=t,n}(J.prototype=new c.a).__a=function(e){var t=this,n=Y(t.__v),r=t.o.get(e);return r[0]++,function(o){var i=function(){t.props.revealOrder?(r.push(o),Q(t,e,r)):o()};n?n(i):i()}},J.prototype.render=function(e){this.u=null,this.o=new Map;var t=Object(c.k)(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},J.prototype.componentDidUpdate=J.prototype.componentDidMount=function(){var e=this;this.o.forEach((function(t,n){Q(e,n,t)}))};var re="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,oe=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,ie=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,ae=/[A-Z0-9]/g,ce="undefined"!=typeof document,se=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(e)};function ue(e,t,n){return null==t.__k&&(t.textContent=""),Object(c.j)(e,t),"function"==typeof n&&n(),e?e.__c:null}function le(e,t,n){return Object(c.h)(e,t),"function"==typeof n&&n(),e?e.__c:null}c.a.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(e){Object.defineProperty(c.a.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})}));var fe=c.i.event;function pe(){}function de(){return this.cancelBubble}function he(){return this.defaultPrevented}c.i.event=function(e){return fe&&(e=fe(e)),e.persist=pe,e.isPropagationStopped=de,e.isDefaultPrevented=he,e.nativeEvent=e};var ge,me={enumerable:!1,configurable:!0,get:function(){return this.class}},be=c.i.vnode;c.i.vnode=function(e){"string"==typeof e.type&&function(e){var t=e.props,n=e.type,r={};for(var o in t){var i=t[o];if(!("value"===o&&"defaultValue"in t&&null==i||ce&&"children"===o&&"noscript"===n||"class"===o||"className"===o)){var a=o.toLowerCase();"defaultValue"===o&&"value"in t&&null==t.value?o="value":"download"===o&&!0===i?i="":"translate"===a&&"no"===i?i=!1:"ondoubleclick"===a?o="ondblclick":"onchange"!==a||"input"!==n&&"textarea"!==n||se(t.type)?"onfocus"===a?o="onfocusin":"onblur"===a?o="onfocusout":ie.test(o)?o=a:-1===n.indexOf("-")&&oe.test(o)?o=o.replace(ae,"-$&").toLowerCase():null===i&&(i=void 0):a=o="oninput","oninput"===a&&r[o=a]&&(o="oninputCapture"),r[o]=i}}"select"==n&&r.multiple&&Array.isArray(r.value)&&(r.value=Object(c.k)(t.children).forEach((function(e){e.props.selected=-1!=r.value.indexOf(e.props.value)}))),"select"==n&&null!=r.defaultValue&&(r.value=Object(c.k)(t.children).forEach((function(e){e.props.selected=r.multiple?-1!=r.defaultValue.indexOf(e.props.value):r.defaultValue==e.props.value}))),t.class&&!t.className?(r.class=t.class,Object.defineProperty(r,"className",me)):(t.className&&!t.class||t.class&&t.className)&&(r.class=r.className=t.className),e.props=r}(e),e.$$typeof=re,be&&be(e)};var ye=c.i.__r;c.i.__r=function(e){ye&&ye(e),ge=e.__c};var _e=c.i.diffed;c.i.diffed=function(e){_e&&_e(e);var t=e.props,n=e.__e;null!=n&&"textarea"===e.type&&"value"in t&&t.value!==n.value&&(n.value=null==t.value?"":t.value),ge=null};var ve={ReactCurrentDispatcher:{current:{readContext:function(e){return ge.__n[e.__c].props.value}}}},we="17.0.2";function Oe(e){return c.e.bind(null,e)}function ke(e){return!!e&&e.$$typeof===re}function je(e){return ke(e)&&e.type===c.b}function Ce(e){return!!e&&!!e.displayName&&("string"==typeof e.displayName||e.displayName instanceof String)&&e.displayName.startsWith("Memo(")}function Se(e){return ke(e)?c.c.apply(null,arguments):e}function xe(e){return!!e.__k&&(Object(c.j)(null,e),!0)}function Ne(e){return e&&(e.base||1===e.nodeType&&e)||null}var Ee=function(e,t){return e(t)},Pe=function(e,t){return e(t)},Te=c.b;function Ie(e){e()}function ze(e){return e}function Le(){return[!1,Ie]}var Me=O,Ae=ke;function De(e,t){var n=t(),r=_({h:{__:n,v:t}}),o=r[0].h,i=r[1];return O((function(){o.__=n,o.v=t,Re(o)&&i({h:o})}),[e,n,t]),w((function(){return Re(o)&&i({h:o}),e((function(){Re(o)&&i({h:o})}))}),[e]),n}function Re(e){var t,n,r=e.v,o=e.__;try{var i=r();return!((t=o)===(n=i)&&(0!==t||1/t==1/n)||t!=t&&n!=n)}catch(e){return!0}}var Be={useState:_,useId:P,useReducer:v,useEffect:w,useLayoutEffect:O,useInsertionEffect:Me,useTransition:Le,useDeferredValue:ze,useSyncExternalStore:De,startTransition:Ie,useRef:k,useImperativeHandle:j,useMemo:C,useCallback:S,useContext:x,useDebugValue:N,version:"17.0.2",Children:G,render:ue,hydrate:le,unmountComponentAtNode:xe,createPortal:ne,createElement:c.e,createContext:c.d,createFactory:Oe,cloneElement:Se,createRef:c.f,Fragment:c.b,isValidElement:ke,isElement:Ae,isFragment:je,isMemo:Ce,findDOMNode:Ne,Component:c.a,PureComponent:F,memo:U,forwardRef:V,flushSync:Pe,unstable_batchedUpdates:Ee,StrictMode:Te,Suspense:X,SuspenseList:J,lazy:$,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:ve}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",moreNav:"moreNav__3ENzC",withDestinations:"withDestinations__3set5",visible:"visible__159hS",moreNavInner:"moreNavInner__1BIZf",moreNavLabel:"moreNavLabel__2myqk",section:"section__3eRH-",sectionTitle:"sectionTitle__3RT_B",link:"link__2Q-2j",sectionsSection:"sectionsSection__CR1Rx",sectionLinksSection:"sectionLinksSection__3Fg2T",sectionItems:"sectionItems__25qE1",otherLinksSection:"otherLinksSection__1xHp7",headerSectionContainer:"headerSectionContainer__1dPWB",headerSection:"headerSection__t9FBx",headerCta:"headerCta__1XoKe",headerCtaTitle:"headerCtaTitle__2ILHr",newsletterIcon:"newsletterIcon__V2uvQ",rightCaret:"rightCaret__3XCOV",headerLink:"headerLink__306O2",footerSectionContainer:"footerSectionContainer__2uG21",footerSection:"footerSection__hDmJa",copyright:"copyright__3l_gL",CCPAConsentModule:"CCPAConsentModule__3gLTb",ccpaCopy:"ccpaCopy__3fMe-",GDPRConsentModule:"GDPRConsentModule__3QRDK",IDNMLConsentModule:"IDNMLConsentModule__vi58S",footerSubSection:"footerSubSection__2k2Zl",footerNav:"footerNav__14aBz",destinationsSection:"destinationsSection__2Jh0F",destinationItem:"destinationItem__3l9i4",destinationLink:"destinationLink__3Y1ie",bfnLogo:"bfnLogo__3RBSy",tastyLogo:"tastyLogo__UPbsS",goodfulLogo:"goodfulLogo__2B4fl",asisLogo:"asisLogo__3XtDj",huffpostLogo:"huffpostLogo__1tb1I",menuToggle:"menuToggle__1pVB_",pageOverlay:"pageOverlay__2bdTH",bodyWithMoreNav:"bodyWithMoreNav__1zNGD",smallSecondaryButton:"smallSecondaryButton__2VTNl"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",notifications:"notifications__2ugKZ",notificationsTitle:"notificationsTitle__2Y5im",unreadTitleContainer:"unreadTitleContainer__1nq3b",unreadTitle:"unreadTitle__FqaRy",readTitle:"readTitle__3TKl_",unreadIcon:"unreadIcon__3hOeD",notification:"notification__1-gZq",notificationTitle:"notificationTitle__1GI6E",notificationTimestamp:"notificationTimestamp__3VCGg",notificationEmptyState:"notificationEmptyState__1zceD",unreadNotificationsContainer:"unreadNotificationsContainer__11tjM",readNotificationsContainer:"readNotificationsContainer__35rpQ",trophyAddedContainer:"trophyAddedContainer__2TNkZ",markAllAsReadButton:"markAllAsReadButton__1udLI",markAsRead:"markAsRead__1G45K",sparklesSVG:"sparklesSVG__o8KCT",postTitle:"postTitle__2QXsa",postTitleContainer:"postTitleContainer__34Cm9",buttonCTA:"buttonCTA__2-S-4",buttonCTAChevron:"buttonCTAChevron__1k2RM",teaserTrophyInfo:"teaserTrophyInfo__2DKYZ",newFeatureBadge:"newFeatureBadge__2ObZL",seeReplyButton:"seeReplyButton__2sMPm",postLink:"postLink__DHzIg",regular:"regular__28vSV"}},function(e,t){function n(){return e.exports=n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.default=e.exports,e.exports.__esModule=!0,n.apply(this,arguments)}e.exports=n,e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",newsletter:"newsletter__1bviw","modal__content-fade":"modal__content-fade__3MpZi",newsletterContent:"newsletterContent__3b8Cz","modal__content-scale":"modal__content-scale__2qzEg",newsletterContent__open:"newsletterContent__open__1fMyr",newsletterFieldset:"newsletterFieldset__hGWr8",newsletterSubhead:"newsletterSubhead__1lvnp",newsletterClose:"newsletterClose__3UAF3",newsletterCloseIcon:"newsletterCloseIcon__18WxL",newsletterTitle:"newsletterTitle__WZSG9",newsletterLegal:"newsletterLegal__1x_8Q",newsletterLink:"newsletterLink__1AEcf",newsletterDesktopOnly:"newsletterDesktopOnly__3v3ad",newsletterAlignCenter:"newsletterAlignCenter__2jpqJ",newsletterItemContainer:"newsletterItemContainer__2H4hD",newsletterItemContainer__scroll:"newsletterItemContainer__scroll__3ltZN",newsletterItemContainer__open:"newsletterItemContainer__open__1on_C",newsletterItem:"newsletterItem__1OBvE",newsletterItemTitle:"newsletterItemTitle__3i5oF",newsletterItemFrequency:"newsletterItemFrequency__2mTz1",newsletterItemText:"newsletterItemText__rsf0d",newsletterItemWrapper:"newsletterItemWrapper__iFk5t",newsletterCheckboxContainer:"newsletterCheckboxContainer__2k3y4",newsletterCheckbox:"newsletterCheckbox__1EB_z",newsletterCheckboxCheckmark:"newsletterCheckboxCheckmark__2mFMr",newsletterSubmit:"newsletterSubmit__3IRqw",newsletterFooter:"newsletterFooter__3AWz6"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",wrapper:"wrapper__7JIQ_",subNavContainer:"subNavContainer__2UWL9",isSecondNav:"isSecondNav__GUhoI",isSticking:"isSticking__op_Vt",isPrimaryNav:"isPrimaryNav__o_SGr",bfLogo:"bfLogo__1cUlo",topicNav:"topicNav__S23Dx",link:"link__32D57",fadeOut:"fadeOut__10Jqd",collapsedTrending:"collapsedTrending__UDZlX",topicNavItem:"topicNavItem__1GfBa",mobileItem:"mobileItem__3Uyal",desktopItem:"desktopItem__2zT4T",topicNavItemWithLogo:"topicNavItemWithLogo__1j1ZJ",mdHide:"mdHide__Ib1Xv",downCaret:"downCaret__3gV-x",dailyFact:"dailyFact__1f35W",dailyFactLink:"dailyFactLink__2NhRU"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",userAvatarContainer:"userAvatarContainer__3N1zs",signIn:"signIn__MenGa",userAvatar:"userAvatar__3MGZN",userOptions:"userOptions__4TkE7",blink:"blink__3-FbP",menuToggle:"menuToggle__340o8",displayName:"displayName__yMDHL",userOptionsProfile:"userOptionsProfile__27DEO",userProfileName:"userProfileName__3eo4S",footer:"footer__2rwfi",email:"email__2RNp9",logout:"logout__gZ3G_",notificationsWrapper:"notificationsWrapper__3eqnB",links:"links__kLaht",newPost:"newPost__bO2xr",userMenuContainer:"userMenuContainer__25kT7",visible:"visible__3tvDf",growBox:"growBox__2dbyd",fadeIn:"fadeIn__1C-zs"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",header:"header__2w0-b",wrapper:"wrapper__1SCdo",logoWrapper:"logoWrapper__2Uw7a",isSticking:"isSticking__1C8UU",navMembershipLink:"navMembershipLink__hfG-o",wavingHand:"wavingHand__1zCHz",newslettersWrapper:"newslettersWrapper__3eAJw",searchWrapper:"searchWrapper__3k7tl",navIconToggles:"navIconToggles__1Ye9q",hasUserInfo:"hasUserInfo__1vgew",navLoginLink:"navLoginLink__2G7GX"}},function(e,t,n){e.exports=n(89)()},function(e,t,n){var r=n(67),o=n(68),i=n(35),a=n(69);e.exports=function(e,t){return r(e)||o(e,t)||i(e,t)||a()}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",search:"search__2NNLj",active:"active__2twu2",searchLink:"searchLink__BEuOP",searchButton:"searchButton__1Cjq6",searchIcon:"searchIcon__EV1ud",searchInputContainer:"searchInputContainer__1x3YB",searchInput:"searchInput__2U8-q",searchLabel:"searchLabel__3Ws5H"}},function(e,t,n){var r=n(93),o=n(78).default,i=n(37),a=n(88);o="function"==typeof o.default?o.default:o;var c={lowerCaseAttributeNames:!1};function s(e,t){if("string"!=typeof e)throw new TypeError("First argument must be a string");return""===e?[]:a(o(e,(t=t||{}).htmlparser2||c),t)}s.domToReact=a,s.htmlToDOM=o,s.attributesToProps=i,s.Comment=r.Comment,s.Element=r.Element,s.ProcessingInstruction=r.ProcessingInstruction,s.Text=r.Text,e.exports=s,s.default=s},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",toastIcon:"toastIcon__2Q5bv",toastSuccessIcon:"toastSuccessIcon__2iQ1j",toastErrorIcon:"toastErrorIcon__2WXSf",toast:"toast__3E7GJ",toastError:"toastError__fFBA2"}},function(e,t){e.exports=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}},function(e,t){function n(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}e.exports=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e}},function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px"}},function(e,t,n){e.exports=n(62)},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",container:"container__lkWih",title:"title__2GoYw",items:"items__2x9Jo",item:"item__1U_wa",cta:"cta__1aHq-"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",signInPrompt:"signInPrompt__2V0F5",closeButtonWrapper:"closeButtonWrapper__nqJJd",cta:"cta__DPw4k",ctaLink:"ctaLink__2kMEs",sell:"sell__3D915"}},,function(e,t){function n(e,t,n,r,o,i,a){try{var c=e[i](a),s=c.value}catch(e){return void n(e)}c.done?t(s):Promise.resolve(s).then(r,o)}e.exports=function(e){return function(){var t=this,r=arguments;return new Promise((function(o,i){var a=e.apply(t,r);function c(e){n(a,o,i,c,s,"next",e)}function s(e){n(a,o,i,c,s,"throw",e)}c(void 0)}))}}},function(e,t,n){var r=n(63);e.exports=function(e,t){if(null==e)return{};var n,o,i=r(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}},function(e,t){function n(t){return e.exports=n=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},n(t)}e.exports=n},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",navLabel:"navLabel__1qTxb",badge:"badge__3OvKz"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",flyout:"flyout__1ngyW",flyoutInner:"flyoutInner__1kgSG",arrow:"arrow__1z_uS"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",mainNavContainer:"mainNavContainer__2iSCa",mainNav:"mainNav__1kaRQ",stickyMainNav:"stickyMainNav__23oiZ",overflowHeader:"overflowHeader__mPZYq"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",logoContainer:"logoContainer__2-BWv",newsLogoContainer:"newsLogoContainer__3eRGV",microBrandContainer:"microBrandContainer__394qL",microBrandLink:"microBrandLink__mVQ1f",microBrandDelimiter:"microBrandDelimiter__cni4E","delimiter-nifty":"delimiter-nifty__2py7j","delimiter-goodful":"delimiter-goodful__1UeWV","delimiter-asis":"delimiter-asis__1lOQb","delimiter-lgbtq":"delimiter-lgbtq__3cjcc","delimiter-cocoabutter":"delimiter-cocoabutter__3VYSz","delimiter-perolike":"delimiter-perolike__1Wim2","delimiter-work-and-money":"delimiter-work-and-money__wEBYw",bfo:"bfo__1VGu2",secondary:"secondary__3slPZ",news:"news__11yd7",newsTagline:"newsTagline__3yaYq",goodful:"goodful__3rUt6",nifty:"nifty__2cCjX",asis:"asis__XoOaW",lgbtq:"lgbtq__2Qycd",perolike:"perolike__15EH6",cocoabutter:"cocoabutter__1HDMr","work-and-money":"work-and-money__hR9co"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",newsletters:"newsletters__XOinl",newslettersLink:"newslettersLink__1psMk",newslettersIcon:"newslettersIcon__mVr13"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",notifTooltipWrapper:"notifTooltipWrapper__1gxVd",notifTooltip:"notifTooltip__zFxaT","fade-in":"fade-in__60k5u",notifTooltipText:"notifTooltipText__1nIzF"}},function(e,t){function n(t,r){return e.exports=n=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},n(t,r)}e.exports=n},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",editionSelect:"editionSelect__2jZs3",select:"select__2FQi5"}},function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=r(n(86));t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,o.default)(e),i="function"==typeof t;return r.forEach((function(e){if("declaration"===e.type){var r=e.property,o=e.value;i?t(r,o,e):o&&((n=n||{})[r]=o)}})),n}},function(e,t){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}},function(e,t,n){var r=n(34);e.exports=function(e,t){if(e){if("string"==typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}},function(e,t,n){"use strict";var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();var o=n(1),i=n(1),a=n(75),c=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.setFocusTrapElement=function(e){n.focusTrapElement=e},"undefined"!=typeof document&&(n.previouslyFocusedElement=document.activeElement),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"componentDidMount",value:function(){var e=this.props.focusTrapOptions,t={returnFocusOnDeactivate:!1};for(var n in e)e.hasOwnProperty(n)&&"returnFocusOnDeactivate"!==n&&(t[n]=e[n]);var r=i.findDOMNode(this.focusTrapElement);this.focusTrap=this.props._createFocusTrap(r,t),this.props.active&&this.focusTrap.activate(),this.props.paused&&this.focusTrap.pause()}},{key:"componentDidUpdate",value:function(e){if(e.active&&!this.props.active){var t={returnFocus:this.props.focusTrapOptions.returnFocusOnDeactivate||!1};this.focusTrap.deactivate(t)}else!e.active&&this.props.active&&this.focusTrap.activate();e.paused&&!this.props.paused?this.focusTrap.unpause():!e.paused&&this.props.paused&&this.focusTrap.pause()}},{key:"componentWillUnmount",value:function(){this.focusTrap.deactivate(),!1!==this.props.focusTrapOptions.returnFocusOnDeactivate&&this.previouslyFocusedElement&&this.previouslyFocusedElement.focus&&this.previouslyFocusedElement.focus()}},{key:"render",value:function(){var e=this,t=o.Children.only(this.props.children);return o.cloneElement(t,{ref:function(n){e.setFocusTrapElement(n),"function"==typeof t.ref&&t.ref(n)}})}}]),t}(o.Component);c.defaultProps={active:!0,paused:!1,focusTrapOptions:{},_createFocusTrap:a},e.exports=c},function(e,t,n){var r=n(82),o=n(38),i=["checked","value"],a=["input","select","textarea"],c={reset:!0,submit:!0};function s(e){return r.possibleStandardNames[e]}e.exports=function(e,t){var n,u,l,f,p,d={},h=(e=e||{}).type&&c[e.type];for(n in e)if(l=e[n],r.isCustomAttribute(n))d[n]=l;else if(f=s(u=n.toLowerCase()))switch(p=r.getPropertyInfo(f),-1===i.indexOf(f)||-1===a.indexOf(t)||h||(f=s("default"+u)),d[f]=l,p&&p.type){case r.BOOLEAN:d[f]=!0;break;case r.OVERLOADED_BOOLEAN:""===l&&(d[f]=!0)}else o.PRESERVE_CUSTOM_ATTRIBUTES&&(d[n]=l);return o.setStyleProp(e.style,d),d}},function(e,t,n){var r=n(1),o=n(84).default,i=new Set(["annotation-xml","color-profile","font-face","font-face-src","font-face-uri","font-face-format","font-face-name","missing-glyph"]);var a={reactCompat:!0};var c=r.version.split(".")[0]>=16,s=new Set(["tr","tbody","thead","tfoot","colgroup","table","head","html","frameset"]);e.exports={PRESERVE_CUSTOM_ATTRIBUTES:c,ELEMENTS_WITH_NO_TEXT_CHILDREN:s,isCustomComponent:function(e,t){return-1===e.indexOf("-")?t&&"string"==typeof t.is:!i.has(e)},setStyleProp:function(e,t){if(null!=e)try{t.style=o(e,a)}catch(e){t.style={}}},canTextBeChildOfNode:function(e){return!s.has(e.name)},returnFirstArg:function(e){return e}}},function(e,t,n){"use strict";(function(e){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var r="object"==(void 0===e?"undefined":n(e))&&e&&e.Object===Object&&e;t.a=r}).call(this,n(57))},function(e,t,n){var r=n(58)("jsonp");e.exports=function(e,t,n){"function"==typeof t&&(n=t,t={});t||(t={});var a,c,s=t.prefix||"__jp",u=t.name||s+o++,l=t.param||"callback",f=null!=t.timeout?t.timeout:6e4,p=encodeURIComponent,d=document.getElementsByTagName("script")[0]||document.head;f&&(c=setTimeout((function(){h(),n&&n(new Error("Timeout"))}),f));function h(){a.parentNode&&a.parentNode.removeChild(a),window[u]=i,c&&clearTimeout(c)}return window[u]=function(e){r("jsonp got",e),h(),n&&n(null,e)},e=(e+=(~e.indexOf("?")?"&":"?")+l+"="+p(u)).replace("?&","?"),r('jsonp req "%s"',e),(a=document.createElement("script")).src=e,d.parentNode.insertBefore(a,d),function(){window[u]&&h()}};var o=0;function i(){}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",text:"text__XYCjV",link:"link__357dN",linkCta:"linkCta__NNr1e",mainNavContainer:"mainNavContainer__2l6Vz",bfoLogo:"bfoLogo__1KJMe",menuToggle:"menuToggle__2EiBQ",userAvatar:"userAvatar__je28w",newslettersIcon:"newslettersIcon__DjR9H",searchIcon:"searchIcon__1Ebx7",moreNav:"moreNav__2sbYE",secondaryButton:"secondaryButton__1n7Yk",trendingNavWrapper:"trendingNavWrapper__3gOq_",topicNavWrapper:"topicNavWrapper__2NeIp",scrollingWrapper:"scrollingWrapper__2sil1"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",text:"text__1dl8A",link:"link__2d6hQ",linkCta:"linkCta__2WKTn",mainNavContainer:"mainNavContainer__3mRKb",bfoLogo:"bfoLogo__198yR",menuToggle:"menuToggle__3tmWj",userAvatar:"userAvatar__DDJBk",newslettersIcon:"newslettersIcon__3NdRy",searchIcon:"searchIcon__1dTDa",moreNav:"moreNav__3FbPP",secondaryButton:"secondaryButton__2eG-m",trendingNavWrapper:"trendingNavWrapper__1povE",topicNavWrapper:"topicNavWrapper__1oERL",scrollingWrapper:"scrollingWrapper__bWL7J",topicNav:"topicNav__2qARK"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",text:"text__1xHxO",link:"link__2GH1p",linkCta:"linkCta__k0XAy",mainNavContainer:"mainNavContainer__3CMRZ",stickyMainNav:"stickyMainNav__3ewBi",bfoLogo:"bfoLogo__3BmVE",menuToggle:"menuToggle__9wS7_",userAvatar:"userAvatar__t87aP",newslettersIcon:"newslettersIcon__1ZoHj",searchIcon:"searchIcon__2Bn8c",moreNav:"moreNav__2XkeS",secondaryButton:"secondaryButton__WdNFY",trendingNavWrapper:"trendingNavWrapper__20_UI",topicNavWrapper:"topicNavWrapper__2_Z5F",scrollingWrapper:"scrollingWrapper__3mYIL",logoWrapperSmall:"logoWrapperSmall__Qhj_X",logoWrapperMW:"logoWrapperMW__3CUls",navigationLink:"navigationLink__32avd",navigationLinks__li:"navigationLinks__li__3VCbj",bottomBorder:"bottomBorder__2Aes2",signInMW:"signInMW__1sZFg",signInLink:"signInLink__1MkSL",navigationLinks:"navigationLinks__26RHv"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",text:"text__2GJy6",link:"link__4kL1A",linkCta:"linkCta__HkprS",mainNavContainer:"mainNavContainer__2Aqwj",stickyMainNav:"stickyMainNav__2oHZR",bfoLogo:"bfoLogo__3SY2w",menuToggle:"menuToggle__naF7a",userAvatar:"userAvatar__2o-9Z",newslettersIcon:"newslettersIcon__29PJQ",searchIcon:"searchIcon__seocY",moreNav:"moreNav__3ua-G",secondaryButton:"secondaryButton__3Egwx",trendingNavWrapper:"trendingNavWrapper__3JZRs",topicNavWrapper:"topicNavWrapper__10FLS",scrollingWrapper:"scrollingWrapper__D6N8K",logoWrapperSmall:"logoWrapperSmall__2m2c-",logoWrapperMW:"logoWrapperMW__2hVuu",navigationLink:"navigationLink__Zixe7",navigationLinks__li:"navigationLinks__li__SJ69W",bottomBorder:"bottomBorder__QUy3-",signInMW:"signInMW__WxpZ0",signInLink:"signInLink__2PaVd",navigationLinks:"navigationLinks__1GmMM"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",text:"text__1kUoX",link:"link__1sCJa",linkCta:"linkCta__kP6Db",mainNavContainer:"mainNavContainer__1LgVX",stickyMainNav:"stickyMainNav__2d8Ws",bfoLogo:"bfoLogo__raOnf",menuToggle:"menuToggle__2Gizu",userAvatar:"userAvatar__FSsjT",newslettersIcon:"newslettersIcon__1m7gm",searchIcon:"searchIcon__1eMtC",moreNav:"moreNav__u7Ziy",secondaryButton:"secondaryButton__XkdUV",trendingNavWrapper:"trendingNavWrapper__1UdlY",topicNavWrapper:"topicNavWrapper__2p54m",scrollingWrapper:"scrollingWrapper__R19CD",logoWrapperSmall:"logoWrapperSmall__39-xh",logoWrapperMW:"logoWrapperMW__2tgg8",navigationLink:"navigationLink__zC1vP",navigationLinks__li:"navigationLinks__li__1xR-C",bottomBorder:"bottomBorder__1slwP",signInMW:"signInMW__1i7fB",signInLink:"signInLink__3dXg2",sticky:"sticky__13fu6",navigationLinks:"navigationLinks__Fn0aS"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",text:"text__L3wzM",link:"link__1fQJY",linkCta:"linkCta__1XHkZ",mainNavContainer:"mainNavContainer__2E_FX",stickyMainNav:"stickyMainNav__32QuR",bfoLogo:"bfoLogo__1hKTW",menuToggle:"menuToggle__jkp2V",userAvatar:"userAvatar__WLKi9",searchIcon:"searchIcon__KRbza",moreNav:"moreNav__3ons1",secondaryButton:"secondaryButton__1i3r_",trendingNavWrapper:"trendingNavWrapper__20q1D",topicNavWrapper:"topicNavWrapper__gTz59",scrollingWrapper:"scrollingWrapper___OSBQ",logoWrapperSmall:"logoWrapperSmall__xG7rb",logoWrapperMW:"logoWrapperMW__2N1hv",navigationLink:"navigationLink__1y2gf",navigationLinks__li:"navigationLinks__li__IGyhx",bottomBorder:"bottomBorder__kwdL_",signInMW:"signInMW__erEqy",signInLink:"signInLink__bT96L",sticky:"sticky__26Eiz",newslettersIcon:"newslettersIcon__mLiN9",navigationLinks:"navigationLinks__Cj728"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",text:"text__1i0LC",link:"link__1AOx5",linkCta:"linkCta__9Gro7",mainNavContainer:"mainNavContainer__19fgC",bfoLogo:"bfoLogo__8U8zP",menuToggle:"menuToggle__1re0g",userAvatar:"userAvatar__AXadf",secondNavLogo:"secondNavLogo__cgJMa",newslettersIcon:"newslettersIcon__2LvHO",searchIcon:"searchIcon__1OxzK",moreNav:"moreNav__3wavb",secondaryButton:"secondaryButton__11eLZ",trendingNavWrapper:"trendingNavWrapper__3x_53",topicNavWrapper:"topicNavWrapper__sryE7",scrollingWrapper:"scrollingWrapper__20Z4D",topicNav:"topicNav__3Ih5Z",secondSubNavContainer:"secondSubNavContainer__vYr9W",isSticking:"isSticking__2R8W1"}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",text:"text__3vN1r",link:"link__hsehD",linkCta:"linkCta__22SuS",mainNavContainer:"mainNavContainer__wGzbh",bfoLogo:"bfoLogo__31xoh",menuToggle:"menuToggle__psOKX",userAvatar:"userAvatar__2l77Y",secondNavLogo:"secondNavLogo__19Tya",newslettersIcon:"newslettersIcon__1ROYp",searchIcon:"searchIcon__1XFRT",moreNav:"moreNav__1jtmy",secondaryButton:"secondaryButton__1oQAd",trendingNavWrapper:"trendingNavWrapper__lH0dK",topicNavWrapper:"topicNavWrapper__1Ln7P",scrollingWrapper:"scrollingWrapper__1oiXr",topicNav:"topicNav__2S-JV",secondSubNavContainer:"secondSubNavContainer__279bh",isSticking:"isSticking__30LaX"}},function(e,t,n){var r=n(64),o=n(65),i=n(35),a=n(66);e.exports=function(e){return r(e)||o(e)||i(e)||a()}},function(e,t,n){var r=n(31);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&r(e,t)}},function(e,t,n){var r=n(70),o=n(71);e.exports=function(e,t){return!t||"object"!==r(t)&&"function"!=typeof t?o(e):t}},function(e,t,n){var r=n(24),o=n(31),i=n(72),a=n(73);function c(t){var n="function"==typeof Map?new Map:void 0;return e.exports=c=function(e){if(null===e||!i(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==n){if(n.has(e))return n.get(e);n.set(e,t)}function t(){return a(e,arguments,r(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),o(t,e)},c(t)}e.exports=c},function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=r(n(79)),i=n(80),a=/<(![a-zA-Z\s]+)>/;t.default=function(e){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(""===e)return[];var t=e.match(a),n=t?t[1]:void 0;return(0,i.formatDOM)((0,o.default)(e),null,n)}},function(e,t,n){e.exports={breakpointMedium:"500px",breakpointLarge:"790px",breakpointSticky:"500px",skipNav:"skipNav__nP6WN"}},function(e,t,n){var r;
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var i=typeof r;if("string"===i||"number"===i)e.push(r);else if(Array.isArray(r)&&r.length){var a=o.apply(null,r);a&&e.push(a)}else if("object"===i)for(var c in r)n.call(r,c)&&r[c]&&e.push(c)}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(r=function(){return o}.apply(t,[]))||(e.exports=r)}()},,function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){(function(r){function o(){var e;try{e=t.storage.debug}catch(e){}return!e&&void 0!==r&&"env"in r&&(e=r.env.DEBUG),e}(t=e.exports=n(60)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},t.formatArgs=function(e){var n=this.useColors;if(e[0]=(n?"%c":"")+this.namespace+(n?" %c":" ")+e[0]+(n?"%c ":" ")+"+"+t.humanize(this.diff),!n)return;var r="color: "+this.color;e.splice(1,0,r,"color: inherit");var o=0,i=0;e[0].replace(/%[a-zA-Z%]/g,(function(e){"%%"!==e&&(o++,"%c"===e&&(i=o))})),e.splice(i,0,r)},t.save=function(e){try{null==e?t.storage.removeItem("debug"):t.storage.debug=e}catch(e){}},t.load=o,t.useColors=function(){if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)return!0;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(e){}}(),t.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],t.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},t.enable(o())}).call(this,n(59))},function(e,t){var n,r,o=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function c(e){if(n===setTimeout)return setTimeout(e,0);if((n===i||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:i}catch(e){n=i}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(e){r=a}}();var s,u=[],l=!1,f=-1;function p(){l&&s&&(l=!1,s.length?u=s.concat(u):f=-1,u.length&&d())}function d(){if(!l){var e=c(p);l=!0;for(var t=u.length;t;){for(s=u,u=[];++f<t;)s&&s[f].run();f=-1,t=u.length}s=null,l=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function g(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];u.push(new h(e,t)),1!==u.length||l||c(d)},h.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=g,o.addListener=g,o.once=g,o.off=g,o.removeListener=g,o.removeAllListeners=g,o.emit=g,o.prependListener=g,o.prependOnceListener=g,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},function(e,t,n){var r;function o(e){function n(){if(n.enabled){var e=n,o=+new Date,i=o-(r||o);e.diff=i,e.prev=r,e.curr=o,r=o;for(var a=new Array(arguments.length),c=0;c<a.length;c++)a[c]=arguments[c];a[0]=t.coerce(a[0]),"string"!=typeof a[0]&&a.unshift("%O");var s=0;a[0]=a[0].replace(/%([a-zA-Z%])/g,(function(n,r){if("%%"===n)return n;s++;var o=t.formatters[r];if("function"==typeof o){var i=a[s];n=o.call(e,i),a.splice(s,1),s--}return n})),t.formatArgs.call(e,a);var u=n.log||t.log||console.log.bind(console);u.apply(e,a)}}return n.namespace=e,n.enabled=t.enabled(e),n.useColors=t.useColors(),n.color=function(e){var n,r=0;for(n in e)r=(r<<5)-r+e.charCodeAt(n),r|=0;return t.colors[Math.abs(r)%t.colors.length]}(e),"function"==typeof t.init&&t.init(n),n}(t=e.exports=o.debug=o.default=o).coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){t.enable("")},t.enable=function(e){t.save(e),t.names=[],t.skips=[];for(var n=("string"==typeof e?e:"").split(/[\s,]+/),r=n.length,o=0;o<r;o++)n[o]&&("-"===(e=n[o].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.substr(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){var n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(61),t.names=[],t.skips=[],t.formatters={}},function(e,t){var n=1e3,r=6e4,o=60*r,i=24*o;function a(e,t,n){if(!(e<t))return e<1.5*t?Math.floor(e/t)+" "+n:Math.ceil(e/t)+" "+n+"s"}e.exports=function(e,t){t=t||{};var c,s=typeof e;if("string"===s&&e.length>0)return function(e){if((e=String(e)).length>100)return;var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(e);if(!t)return;var a=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*a;case"days":case"day":case"d":return a*i;case"hours":case"hour":case"hrs":case"hr":case"h":return a*o;case"minutes":case"minute":case"mins":case"min":case"m":return a*r;case"seconds":case"second":case"secs":case"sec":case"s":return a*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}(e);if("number"===s&&!1===isNaN(e))return t.long?a(c=e,i,"day")||a(c,o,"hour")||a(c,r,"minute")||a(c,n,"second")||c+" ms":function(e){if(e>=i)return Math.round(e/i)+"d";if(e>=o)return Math.round(e/o)+"h";if(e>=r)return Math.round(e/r)+"m";if(e>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},function(e,t,n){var r=function(e){"use strict";var t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",i=r.asyncIterator||"@@asyncIterator",a=r.toStringTag||"@@toStringTag";function c(e,t,n,r){var o=t&&t.prototype instanceof l?t:l,i=Object.create(o.prototype),a=new O(r||[]);return i._invoke=function(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return j()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var c=_(a,n);if(c){if(c===u)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var l=s(e,t,n);if("normal"===l.type){if(r=n.done?"completed":"suspendedYield",l.arg===u)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r="completed",n.method="throw",n.arg=l.arg)}}}(e,n,a),i}function s(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var u={};function l(){}function f(){}function p(){}var d={};d[o]=function(){return this};var h=Object.getPrototypeOf,g=h&&h(h(k([])));g&&g!==t&&n.call(g,o)&&(d=g);var m=p.prototype=l.prototype=Object.create(d);function b(e){["next","throw","return"].forEach((function(t){e[t]=function(e){return this._invoke(t,e)}}))}function y(e,t){var r;this._invoke=function(o,i){function a(){return new t((function(r,a){!function r(o,i,a,c){var u=s(e[o],e,i);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==typeof f&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,a,c)}),(function(e){r("throw",e,a,c)})):t.resolve(f).then((function(e){l.value=e,a(l)}),(function(e){return r("throw",e,a,c)}))}c(u.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}}function _(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,_(e,t),"throw"===t.method))return u;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return u}var r=s(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,u;var o=r.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function v(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function w(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(v,this),this.reset(!0)}function k(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:j}}function j(){return{value:void 0,done:!0}}return f.prototype=m.constructor=p,p.constructor=f,p[a]=f.displayName="GeneratorFunction",e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,a in e||(e[a]="GeneratorFunction")),e.prototype=Object.create(m),e},e.awrap=function(e){return{__await:e}},b(y.prototype),y.prototype[i]=function(){return this},e.AsyncIterator=y,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new y(c(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(m),m[a]="Generator",m[o]=function(){return this},m.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=k,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(w),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,u):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),w(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;w(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}(e.exports);try{regeneratorRuntime=r}catch(e){Function("r","regeneratorRuntime = r")(r)}},function(e,t){e.exports=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}},function(e,t,n){var r=n(34);e.exports=function(e){if(Array.isArray(e))return r(e)}},function(e,t){e.exports=function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}},function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}},function(e,t){e.exports=function(e){if(Array.isArray(e))return e}},function(e,t){e.exports=function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var n=[],r=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(r=(a=c.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{r||null==c.return||c.return()}finally{if(o)throw i}}return n}}},function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}},function(e,t){function n(t){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?e.exports=n=function(e){return typeof e}:e.exports=n=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(t)}e.exports=n},function(e,t){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}},function(e,t){e.exports=function(e){return-1!==Function.toString.call(e).indexOf("[native code]")}},function(e,t,n){var r=n(31),o=n(74);function i(t,n,a){return o()?e.exports=i=Reflect.construct:e.exports=i=function(e,t,n){var o=[null];o.push.apply(o,t);var i=new(Function.bind.apply(e,o));return n&&r(i,n.prototype),i},i.apply(null,arguments)}e.exports=i},function(e,t){e.exports=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}},function(e,t,n){var r,o=n(76),i=n(77),a=(r=[],{activateTrap:function(e){if(r.length>0){var t=r[r.length-1];t!==e&&t.pause()}var n=r.indexOf(e);-1===n||r.splice(n,1),r.push(e)},deactivateTrap:function(e){var t=r.indexOf(e);-1!==t&&r.splice(t,1),r.length>0&&r[r.length-1].unpause()}});function c(e){return setTimeout(e,0)}e.exports=function(e,t){var n=document,r="string"==typeof e?n.querySelector(e):e,s=i({returnFocusOnDeactivate:!0,escapeDeactivates:!0},t),u={firstTabbableNode:null,lastTabbableNode:null,nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1},l={activate:function(e){if(u.active)return;v(),u.active=!0,u.paused=!1,u.nodeFocusedBeforeActivation=n.activeElement;var t=e&&e.onActivate?e.onActivate:s.onActivate;t&&t();return p(),l},deactivate:f,pause:function(){if(u.paused||!u.active)return;u.paused=!0,d()},unpause:function(){if(!u.paused||!u.active)return;u.paused=!1,p()}};return l;function f(e){if(u.active){d(),u.active=!1,u.paused=!1,a.deactivateTrap(l);var t=e&&void 0!==e.onDeactivate?e.onDeactivate:s.onDeactivate;return t&&t(),(e&&void 0!==e.returnFocus?e.returnFocus:s.returnFocusOnDeactivate)&&c((function(){w(u.nodeFocusedBeforeActivation)})),l}}function p(){if(u.active)return a.activateTrap(l),v(),c((function(){w(g())})),n.addEventListener("focusin",b,!0),n.addEventListener("mousedown",m,!0),n.addEventListener("touchstart",m,!0),n.addEventListener("click",_,!0),n.addEventListener("keydown",y,!0),l}function d(){if(u.active)return n.removeEventListener("focusin",b,!0),n.removeEventListener("mousedown",m,!0),n.removeEventListener("touchstart",m,!0),n.removeEventListener("click",_,!0),n.removeEventListener("keydown",y,!0),l}function h(e){var t=s[e],r=t;if(!t)return null;if("string"==typeof t&&!(r=n.querySelector(t)))throw new Error("`"+e+"` refers to no known node");if("function"==typeof t&&!(r=t()))throw new Error("`"+e+"` did not return a node");return r}function g(){var e;if(!(e=null!==h("initialFocus")?h("initialFocus"):r.contains(n.activeElement)?n.activeElement:u.firstTabbableNode||h("fallbackFocus")))throw new Error("You can't have a focus-trap without at least one focusable element");return e}function m(e){r.contains(e.target)||(s.clickOutsideDeactivates?f({returnFocus:!o.isFocusable(e.target)}):e.preventDefault())}function b(e){r.contains(e.target)||e.target instanceof Document||(e.stopImmediatePropagation(),w(u.mostRecentlyFocusedNode||g()))}function y(e){if(!1!==s.escapeDeactivates&&function(e){return"Escape"===e.key||"Esc"===e.key||27===e.keyCode}(e))return e.preventDefault(),void f();(function(e){return"Tab"===e.key||9===e.keyCode})(e)&&function(e){if(v(),e.shiftKey&&e.target===u.firstTabbableNode)return e.preventDefault(),void w(u.lastTabbableNode);if(!e.shiftKey&&e.target===u.lastTabbableNode)e.preventDefault(),w(u.firstTabbableNode)}(e)}function _(e){s.clickOutsideDeactivates||r.contains(e.target)||(e.preventDefault(),e.stopImmediatePropagation())}function v(){var e=o(r);u.firstTabbableNode=e[0]||g(),u.lastTabbableNode=e[e.length-1]||g()}function w(e){e!==n.activeElement&&(e&&e.focus?(e.focus(),u.mostRecentlyFocusedNode=e,function(e){return e.tagName&&"input"===e.tagName.toLowerCase()&&"function"==typeof e.select}(e)&&e.select()):w(g()))}}},function(e,t){var n=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'],r=n.join(","),o="undefined"==typeof Element?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector;function i(e,t){t=t||{};var n,i,c,s=[],f=[],d=new p(e.ownerDocument||e),h=e.querySelectorAll(r);for(t.includeContainer&&o.call(e,r)&&(h=Array.prototype.slice.apply(h)).unshift(e),n=0;n<h.length;n++)a(i=h[n],d)&&(0===(c=u(i))?s.push(i):f.push({documentOrder:n,tabIndex:c,node:i}));return f.sort(l).map((function(e){return e.node})).concat(s)}function a(e,t){return!(!c(e,t)||function(e){return function(e){return f(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t=function(e){for(var t=0;t<e.length;t++)if(e[t].checked)return e[t]}(e.ownerDocument.querySelectorAll('input[type="radio"][name="'+e.name+'"]'));return!t||t===e}(e)}(e)||u(e)<0)}function c(e,t){return t=t||new p(e.ownerDocument||e),!(e.disabled||function(e){return f(e)&&"hidden"===e.type}(e)||t.isUntouchable(e))}i.isTabbable=function(e,t){if(!e)throw new Error("No node provided");return!1!==o.call(e,r)&&a(e,t)},i.isFocusable=function(e,t){if(!e)throw new Error("No node provided");return!1!==o.call(e,s)&&c(e,t)};var s=n.concat("iframe").join(",");function u(e){var t=parseInt(e.getAttribute("tabindex"),10);return isNaN(t)?function(e){return"true"===e.contentEditable}(e)?0:e.tabIndex:t}function l(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex}function f(e){return"INPUT"===e.tagName}function p(e){this.doc=e,this.cache=[]}p.prototype.hasDisplayNone=function(e,t){if(e.nodeType!==Node.ELEMENT_NODE)return!1;var n=function(e,t){for(var n=0,r=e.length;n<r;n++)if(t(e[n]))return e[n]}(this.cache,(function(t){return t===e}));if(n)return n[1];var r=!1;return"none"===(t=t||this.doc.defaultView.getComputedStyle(e)).display?r=!0:e.parentNode&&(r=this.hasDisplayNone(e.parentNode)),this.cache.push([e,r]),r},p.prototype.isUntouchable=function(e){if(e===this.doc.documentElement)return!1;var t=this.doc.defaultView.getComputedStyle(e);return!!this.hasDisplayNone(e,t)||"hidden"===t.visibility},e.exports=i},function(e,t){e.exports=function(){for(var e={},t=0;t<arguments.length;t++){var r=arguments[t];for(var o in r)n.call(r,o)&&(e[o]=r[o])}return e};var n=Object.prototype.hasOwnProperty},function(e,t,n){"use strict";n.r(t);var r=n(53);t.default=r.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=/<([a-zA-Z]+[0-9]?)/,o=/<head[^]*>/i,i=/<body[^]*>/i,a=function(e,t){throw new Error("This browser does not support `document.implementation.createHTMLDocument`")},c=function(e,t){throw new Error("This browser does not support `DOMParser.prototype.parseFromString`")},s="object"==typeof window&&window.DOMParser;if("function"==typeof s){var u=new s;a=c=function(e,t){return t&&(e="<".concat(t,">").concat(e,"</").concat(t,">")),u.parseFromString(e,"text/html")}}if("object"==typeof document&&document.implementation){var l=document.implementation.createHTMLDocument();a=function(e,t){if(t){var n=l.documentElement.querySelector(t);return n&&(n.innerHTML=e),l}return l.documentElement.innerHTML=e,l}}var f,p="object"==typeof document&&document.createElement("template");p&&p.content&&(f=function(e){return p.innerHTML=e,p.content.childNodes}),t.default=function(e){var t,n,s=e.match(r),u=s&&s[1]?s[1].toLowerCase():"";switch(u){case"html":var l=c(e);if(!o.test(e))null===(t=null==(d=l.querySelector("head"))?void 0:d.parentNode)||void 0===t||t.removeChild(d);if(!i.test(e))null===(n=null==(d=l.querySelector("body"))?void 0:d.parentNode)||void 0===n||n.removeChild(d);return l.querySelectorAll("html");case"head":case"body":var p=a(e).querySelectorAll(u);return i.test(e)&&o.test(e)?p[0].parentNode.childNodes:p;default:return f?f(e):(d=a(e,"body").querySelector("body")).childNodes;var d}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatDOM=t.formatAttributes=void 0;var r=n(94),o=n(81);function i(e){for(var t={},n=0,r=e.length;n<r;n++){var o=e[n];t[o.name]=o.value}return t}function a(e){var t=function(e){return o.CASE_SENSITIVE_TAG_NAMES_MAP[e]}(e=e.toLowerCase());return t||e}t.formatAttributes=i,t.formatDOM=function e(t,n,o){void 0===n&&(n=null);for(var c,s=[],u=0,l=t.length;u<l;u++){var f=t[u];switch(f.nodeType){case 1:var p=a(f.nodeName);(c=new r.Element(p,i(f.attributes))).children=e("template"===p?f.content.childNodes:f.childNodes,c);break;case 3:c=new r.Text(f.nodeValue);break;case 8:c=new r.Comment(f.nodeValue);break;default:continue}var d=s[u-1]||null;d&&(d.next=c),c.parent=n,c.prev=d,c.next=null,s.push(c)}return o&&((c=new r.ProcessingInstruction(o.substring(0,o.indexOf(" ")).toLowerCase(),o)).next=s[0]||null,c.parent=n,s.unshift(c),s[1]&&(s[1].prev=s[0])),s}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CASE_SENSITIVE_TAG_NAMES_MAP=t.CASE_SENSITIVE_TAG_NAMES=void 0,t.CASE_SENSITIVE_TAG_NAMES=["animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","linearGradient","radialGradient","textPath"],t.CASE_SENSITIVE_TAG_NAMES_MAP=t.CASE_SENSITIVE_TAG_NAMES.reduce((function(e,t){return e[t.toLowerCase()]=t,e}),{})},function(e,t,n){"use strict";function r(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],a=!0,c=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){c=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(c)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return o(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}Object.defineProperty(t,"__esModule",{value:!0});function i(e,t,n,r,o,i,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var a={};["children","dangerouslySetInnerHTML","defaultValue","defaultChecked","innerHTML","suppressContentEditableWarning","suppressHydrationWarning","style"].forEach((function(e){a[e]=new i(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=r(e,2),n=t[0],o=t[1];a[n]=new i(n,1,!1,o,null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){a[e]=new i(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){a[e]=new i(e,2,!1,e,null,!1,!1)})),["allowFullScreen","async","autoFocus","autoPlay","controls","default","defer","disabled","disablePictureInPicture","disableRemotePlayback","formNoValidate","hidden","loop","noModule","noValidate","open","playsInline","readOnly","required","reversed","scoped","seamless","itemScope"].forEach((function(e){a[e]=new i(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){a[e]=new i(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){a[e]=new i(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){a[e]=new i(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){a[e]=new i(e,5,!1,e.toLowerCase(),null,!1,!1)}));var c=/[\-\:]([a-z])/g,s=function(e){return e[1].toUpperCase()};["accent-height","alignment-baseline","arabic-form","baseline-shift","cap-height","clip-path","clip-rule","color-interpolation","color-interpolation-filters","color-profile","color-rendering","dominant-baseline","enable-background","fill-opacity","fill-rule","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","glyph-name","glyph-orientation-horizontal","glyph-orientation-vertical","horiz-adv-x","horiz-origin-x","image-rendering","letter-spacing","lighting-color","marker-end","marker-mid","marker-start","overline-position","overline-thickness","paint-order","panose-1","pointer-events","rendering-intent","shape-rendering","stop-color","stop-opacity","strikethrough-position","strikethrough-thickness","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-anchor","text-decoration","text-rendering","underline-position","underline-thickness","unicode-bidi","unicode-range","units-per-em","v-alphabetic","v-hanging","v-ideographic","v-mathematical","vector-effect","vert-adv-y","vert-origin-x","vert-origin-y","word-spacing","writing-mode","xmlns:xlink","x-height"].forEach((function(e){var t=e.replace(c,s);a[t]=new i(t,1,!1,e,null,!1,!1)})),["xlink:actuate","xlink:arcrole","xlink:role","xlink:show","xlink:title","xlink:type"].forEach((function(e){var t=e.replace(c,s);a[t]=new i(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(c,s);a[t]=new i(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){a[e]=new i(e,1,!1,e.toLowerCase(),null,!1,!1)}));a.xlinkHref=new i("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){a[e]=new i(e,1,!1,e.toLowerCase(),null,!0,!0)}));var u=n(83),l=u.CAMELCASE,f=u.SAME,p=u.possibleStandardNames,d=RegExp.prototype.test.bind(new RegExp("^(data|aria)-[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$")),h=Object.keys(p).reduce((function(e,t){var n=p[t];return n===f?e[t]=t:n===l?e[t.toLowerCase()]=t:e[t]=n,e}),{});t.BOOLEAN=3,t.BOOLEANISH_STRING=2,t.NUMERIC=5,t.OVERLOADED_BOOLEAN=4,t.POSITIVE_NUMERIC=6,t.RESERVED=0,t.STRING=1,t.getPropertyInfo=function(e){return a.hasOwnProperty(e)?a[e]:null},t.isCustomAttribute=d,t.possibleStandardNames=h},function(e,t){t.SAME=0;t.CAMELCASE=1,t.possibleStandardNames={accept:0,acceptCharset:1,"accept-charset":"acceptCharset",accessKey:1,action:0,allowFullScreen:1,alt:0,as:0,async:0,autoCapitalize:1,autoComplete:1,autoCorrect:1,autoFocus:1,autoPlay:1,autoSave:1,capture:0,cellPadding:1,cellSpacing:1,challenge:0,charSet:1,checked:0,children:0,cite:0,class:"className",classID:1,className:1,cols:0,colSpan:1,content:0,contentEditable:1,contextMenu:1,controls:0,controlsList:1,coords:0,crossOrigin:1,dangerouslySetInnerHTML:1,data:0,dateTime:1,default:0,defaultChecked:1,defaultValue:1,defer:0,dir:0,disabled:0,disablePictureInPicture:1,disableRemotePlayback:1,download:0,draggable:0,encType:1,enterKeyHint:1,for:"htmlFor",form:0,formMethod:1,formAction:1,formEncType:1,formNoValidate:1,formTarget:1,frameBorder:1,headers:0,height:0,hidden:0,high:0,href:0,hrefLang:1,htmlFor:1,httpEquiv:1,"http-equiv":"httpEquiv",icon:0,id:0,innerHTML:1,inputMode:1,integrity:0,is:0,itemID:1,itemProp:1,itemRef:1,itemScope:1,itemType:1,keyParams:1,keyType:1,kind:0,label:0,lang:0,list:0,loop:0,low:0,manifest:0,marginWidth:1,marginHeight:1,max:0,maxLength:1,media:0,mediaGroup:1,method:0,min:0,minLength:1,multiple:0,muted:0,name:0,noModule:1,nonce:0,noValidate:1,open:0,optimum:0,pattern:0,placeholder:0,playsInline:1,poster:0,preload:0,profile:0,radioGroup:1,readOnly:1,referrerPolicy:1,rel:0,required:0,reversed:0,role:0,rows:0,rowSpan:1,sandbox:0,scope:0,scoped:0,scrolling:0,seamless:0,selected:0,shape:0,size:0,sizes:0,span:0,spellCheck:1,src:0,srcDoc:1,srcLang:1,srcSet:1,start:0,step:0,style:0,summary:0,tabIndex:1,target:0,title:0,type:0,useMap:1,value:0,width:0,wmode:0,wrap:0,about:0,accentHeight:1,"accent-height":"accentHeight",accumulate:0,additive:0,alignmentBaseline:1,"alignment-baseline":"alignmentBaseline",allowReorder:1,alphabetic:0,amplitude:0,arabicForm:1,"arabic-form":"arabicForm",ascent:0,attributeName:1,attributeType:1,autoReverse:1,azimuth:0,baseFrequency:1,baselineShift:1,"baseline-shift":"baselineShift",baseProfile:1,bbox:0,begin:0,bias:0,by:0,calcMode:1,capHeight:1,"cap-height":"capHeight",clip:0,clipPath:1,"clip-path":"clipPath",clipPathUnits:1,clipRule:1,"clip-rule":"clipRule",color:0,colorInterpolation:1,"color-interpolation":"colorInterpolation",colorInterpolationFilters:1,"color-interpolation-filters":"colorInterpolationFilters",colorProfile:1,"color-profile":"colorProfile",colorRendering:1,"color-rendering":"colorRendering",contentScriptType:1,contentStyleType:1,cursor:0,cx:0,cy:0,d:0,datatype:0,decelerate:0,descent:0,diffuseConstant:1,direction:0,display:0,divisor:0,dominantBaseline:1,"dominant-baseline":"dominantBaseline",dur:0,dx:0,dy:0,edgeMode:1,elevation:0,enableBackground:1,"enable-background":"enableBackground",end:0,exponent:0,externalResourcesRequired:1,fill:0,fillOpacity:1,"fill-opacity":"fillOpacity",fillRule:1,"fill-rule":"fillRule",filter:0,filterRes:1,filterUnits:1,floodOpacity:1,"flood-opacity":"floodOpacity",floodColor:1,"flood-color":"floodColor",focusable:0,fontFamily:1,"font-family":"fontFamily",fontSize:1,"font-size":"fontSize",fontSizeAdjust:1,"font-size-adjust":"fontSizeAdjust",fontStretch:1,"font-stretch":"fontStretch",fontStyle:1,"font-style":"fontStyle",fontVariant:1,"font-variant":"fontVariant",fontWeight:1,"font-weight":"fontWeight",format:0,from:0,fx:0,fy:0,g1:0,g2:0,glyphName:1,"glyph-name":"glyphName",glyphOrientationHorizontal:1,"glyph-orientation-horizontal":"glyphOrientationHorizontal",glyphOrientationVertical:1,"glyph-orientation-vertical":"glyphOrientationVertical",glyphRef:1,gradientTransform:1,gradientUnits:1,hanging:0,horizAdvX:1,"horiz-adv-x":"horizAdvX",horizOriginX:1,"horiz-origin-x":"horizOriginX",ideographic:0,imageRendering:1,"image-rendering":"imageRendering",in2:0,in:0,inlist:0,intercept:0,k1:0,k2:0,k3:0,k4:0,k:0,kernelMatrix:1,kernelUnitLength:1,kerning:0,keyPoints:1,keySplines:1,keyTimes:1,lengthAdjust:1,letterSpacing:1,"letter-spacing":"letterSpacing",lightingColor:1,"lighting-color":"lightingColor",limitingConeAngle:1,local:0,markerEnd:1,"marker-end":"markerEnd",markerHeight:1,markerMid:1,"marker-mid":"markerMid",markerStart:1,"marker-start":"markerStart",markerUnits:1,markerWidth:1,mask:0,maskContentUnits:1,maskUnits:1,mathematical:0,mode:0,numOctaves:1,offset:0,opacity:0,operator:0,order:0,orient:0,orientation:0,origin:0,overflow:0,overlinePosition:1,"overline-position":"overlinePosition",overlineThickness:1,"overline-thickness":"overlineThickness",paintOrder:1,"paint-order":"paintOrder",panose1:0,"panose-1":"panose1",pathLength:1,patternContentUnits:1,patternTransform:1,patternUnits:1,pointerEvents:1,"pointer-events":"pointerEvents",points:0,pointsAtX:1,pointsAtY:1,pointsAtZ:1,prefix:0,preserveAlpha:1,preserveAspectRatio:1,primitiveUnits:1,property:0,r:0,radius:0,refX:1,refY:1,renderingIntent:1,"rendering-intent":"renderingIntent",repeatCount:1,repeatDur:1,requiredExtensions:1,requiredFeatures:1,resource:0,restart:0,result:0,results:0,rotate:0,rx:0,ry:0,scale:0,security:0,seed:0,shapeRendering:1,"shape-rendering":"shapeRendering",slope:0,spacing:0,specularConstant:1,specularExponent:1,speed:0,spreadMethod:1,startOffset:1,stdDeviation:1,stemh:0,stemv:0,stitchTiles:1,stopColor:1,"stop-color":"stopColor",stopOpacity:1,"stop-opacity":"stopOpacity",strikethroughPosition:1,"strikethrough-position":"strikethroughPosition",strikethroughThickness:1,"strikethrough-thickness":"strikethroughThickness",string:0,stroke:0,strokeDasharray:1,"stroke-dasharray":"strokeDasharray",strokeDashoffset:1,"stroke-dashoffset":"strokeDashoffset",strokeLinecap:1,"stroke-linecap":"strokeLinecap",strokeLinejoin:1,"stroke-linejoin":"strokeLinejoin",strokeMiterlimit:1,"stroke-miterlimit":"strokeMiterlimit",strokeWidth:1,"stroke-width":"strokeWidth",strokeOpacity:1,"stroke-opacity":"strokeOpacity",suppressContentEditableWarning:1,suppressHydrationWarning:1,surfaceScale:1,systemLanguage:1,tableValues:1,targetX:1,targetY:1,textAnchor:1,"text-anchor":"textAnchor",textDecoration:1,"text-decoration":"textDecoration",textLength:1,textRendering:1,"text-rendering":"textRendering",to:0,transform:0,typeof:0,u1:0,u2:0,underlinePosition:1,"underline-position":"underlinePosition",underlineThickness:1,"underline-thickness":"underlineThickness",unicode:0,unicodeBidi:1,"unicode-bidi":"unicodeBidi",unicodeRange:1,"unicode-range":"unicodeRange",unitsPerEm:1,"units-per-em":"unitsPerEm",unselectable:0,vAlphabetic:1,"v-alphabetic":"vAlphabetic",values:0,vectorEffect:1,"vector-effect":"vectorEffect",version:0,vertAdvY:1,"vert-adv-y":"vertAdvY",vertOriginX:1,"vert-origin-x":"vertOriginX",vertOriginY:1,"vert-origin-y":"vertOriginY",vHanging:1,"v-hanging":"vHanging",vIdeographic:1,"v-ideographic":"vIdeographic",viewBox:1,viewTarget:1,visibility:0,vMathematical:1,"v-mathematical":"vMathematical",vocab:0,widths:0,wordSpacing:1,"word-spacing":"wordSpacing",writingMode:1,"writing-mode":"writingMode",x1:0,x2:0,x:0,xChannelSelector:1,xHeight:1,"x-height":"xHeight",xlinkActuate:1,"xlink:actuate":"xlinkActuate",xlinkArcrole:1,"xlink:arcrole":"xlinkArcrole",xlinkHref:1,"xlink:href":"xlinkHref",xlinkRole:1,"xlink:role":"xlinkRole",xlinkShow:1,"xlink:show":"xlinkShow",xlinkTitle:1,"xlink:title":"xlinkTitle",xlinkType:1,"xlink:type":"xlinkType",xmlBase:1,"xml:base":"xmlBase",xmlLang:1,"xml:lang":"xmlLang",xmlns:0,"xml:space":"xmlSpace",xmlnsXlink:1,"xmlns:xlink":"xmlnsXlink",xmlSpace:1,y1:0,y2:0,y:0,yChannelSelector:1,z:0,zoomAndPan:1}},function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=r(n(85)),i=n(87);t.default=function(e,t){var n={};return e&&"string"==typeof e?((0,o.default)(e,(function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)})),n):n}},function(e,t,n){"use strict";n.r(t);var r=n(33);t.default=r.default||r},function(e,t){var n=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,r=/\n/g,o=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,a=/^:\s*/,c=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,s=/^[;\s]*/,u=/^\s+|\s+$/g;function l(e){return e?e.replace(u,""):""}e.exports=function(e,t){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];t=t||{};var u=1,f=1;function p(e){var t=e.match(r);t&&(u+=t.length);var n=e.lastIndexOf("\n");f=~n?e.length-n:f+e.length}function d(){var e={line:u,column:f};return function(t){return t.position=new h(e),y(),t}}function h(e){this.start=e,this.end={line:u,column:f},this.source=t.source}h.prototype.content=e;var g=[];function m(n){var r=new Error(t.source+":"+u+":"+f+": "+n);if(r.reason=n,r.filename=t.source,r.line=u,r.column=f,r.source=e,!t.silent)throw r;g.push(r)}function b(t){var n=t.exec(e);if(n){var r=n[0];return p(r),e=e.slice(r.length),n}}function y(){b(o)}function _(e){var t;for(e=e||[];t=v();)!1!==t&&e.push(t);return e}function v(){var t=d();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return m("End of comment missing");var r=e.slice(2,n-2);return f+=2,p(r),e=e.slice(n),f+=2,t({type:"comment",comment:r})}}function w(){var e=d(),t=b(i);if(t){if(v(),!b(a))return m("property missing ':'");var r=b(c),o=e({type:"declaration",property:l(t[0].replace(n,"")),value:r?l(r[0].replace(n,"")):""});return b(s),o}}return y(),function(){var e,t=[];for(_(t);e=w();)!1!==e&&(t.push(e),_(t));return t}()}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var r=/^--[a-zA-Z0-9-]+$/,o=/-([a-z])/g,i=/^[^-]+$/,a=/^-(webkit|moz|ms|o|khtml)-/,c=/^-(ms)-/,s=function(e,t){return t.toUpperCase()},u=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){return void 0===t&&(t={}),function(e){return!e||i.test(e)||r.test(e)}(e)?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(c,u):e.replace(a,u)).replace(o,s))}},function(e,t,n){var r=n(1),o=n(37),i=n(38),a=i.setStyleProp,c=i.canTextBeChildOfNode;function s(e){return i.PRESERVE_CUSTOM_ATTRIBUTES&&"tag"===e.type&&i.isCustomComponent(e.name,e.attribs)}e.exports=function e(t,n){for(var u,l,f,p,d,h=(n=n||{}).library||r,g=h.cloneElement,m=h.createElement,b=h.isValidElement,y=[],_="function"==typeof n.replace,v=n.transform||i.returnFirstArg,w=n.trim,O=0,k=t.length;O<k;O++)if(u=t[O],_&&b(f=n.replace(u)))k>1&&(f=g(f,{key:f.key||O})),y.push(v(f,u,O));else if("text"!==u.type){switch(p=u.attribs,s(u)?a(p.style,p):p&&(p=o(p,u.name)),d=null,u.type){case"script":case"style":u.children[0]&&(p.dangerouslySetInnerHTML={__html:u.children[0].data});break;case"tag":"textarea"===u.name&&u.children[0]?p.defaultValue=u.children[0].data:u.children&&u.children.length&&(d=e(u.children,n));break;default:continue}k>1&&(p.key=O),y.push(v(m(u.name,p,d),u,O))}else{if((l=!u.data.trim().length)&&u.parent&&!c(u.parent))continue;if(w&&l)continue;y.push(v(u.data,u,O))}return 1===y.length?y[0]:y}},function(e,t,n){"use strict";var r=n(90);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,i,a){if(a!==r){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return n.PropTypes=n,n}},function(e,t,n){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},function(e,t,n){"use strict";n.r(t);var r=n(0);function o(e,t){var n=e.match(t);return n&&n.length?n[0]:null}function i(){return"prod"===window.BZFD.Config.env?"buzzfeed.com":window.location.hostname}var a={getBuzzfeedSubdomainOrWildcard:function(e){var t=o(e,"(dev|stage|www).(buzzfeed|buzzfeednews).(com|io)$");return t||o(e,".?[a-z]+.[a-z]+$")},get:function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n="".concat(e,"="),r=document.cookie.split(";"),o=0;o<r.length;o++){for(var i=r[o];" "===i.charAt(0);)i=i.substring(1,i.length);if(0===i.indexOf(n))return i.substring(n.length,i.length)}return t},set:function(e){var t=e.name,n=e.value,r=e.days,o=e.domain;o=o||i();var a="";if(r){var c=new Date;c.setTime(c.getTime()+24*r*60*60*1e3),a="; expires=".concat(c.toGMTString())}return document.cookie="".concat(t,"=").concat(n).concat(a,"; path=/; domain=").concat(o)},remove:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i();return this.set({name:e,value:"",days:-1,domain:t})}},c="function"==typeof fetch?fetch:function(e,t){return t=t||{},new Promise((function(n,r){var o=new XMLHttpRequest;for(var i in o.open(t.method||"get",e),t.headers)o.setRequestHeader(i,t.headers[i]);function a(){var e,t=[],n=[],r={};return o.getAllResponseHeaders().replace(/^(.*?):\s*([\s\S]*?)$/gm,(function(o,i,a){t.push(i=i.toLowerCase()),n.push([i,a]),e=r[i],r[i]=e?e+","+a:a})),{ok:1==(o.status/200|0),status:o.status,statusText:o.statusText,url:o.responseURL,clone:a,text:function(){return Promise.resolve(o.responseText)},json:function(){return Promise.resolve(o.responseText).then(JSON.parse)},xml:function(){return Promise.resolve(o.responseXML)},blob:function(){return Promise.resolve(new Blob([o.response]))},headers:{keys:function(){return t},entries:function(){return n},get:function(e){return r[e.toLowerCase()]},has:function(e){return e.toLowerCase()in r}}}}o.withCredentials="include"==t.credentials,o.onload=function(){n(a())},o.onerror=r,o.send(t.body)}))},s=(Array.isArray,n(39));function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var l="object"==("undefined"==typeof self?"undefined":u(self))&&self&&self.Object===Object&&self,f=s.a||l||Function("return this")(),p=f.Symbol,d=Object.prototype,h=d.hasOwnProperty,g=d.toString,m=p?p.toStringTag:void 0;var b=function(e){var t=h.call(e,m),n=e[m];try{e[m]=void 0;var r=!0}catch(e){}var o=g.call(e);return r&&(t?e[m]=n:delete e[m]),o},y=Object.prototype.toString;var _=function(e){return y.call(e)},v=p?p.toStringTag:void 0;var w=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":v&&v in Object(e)?b(e):_(e)};function O(e){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var k=function(e){var t=O(e);return null!=e&&("object"==t||"function"==t)};var j,C=function(e){if(!k(e))return!1;var t=w(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},S=f["__core-js_shared__"],x=(j=/[^.]+$/.exec(S&&S.keys&&S.keys.IE_PROTO||""))?"Symbol(src)_1."+j:"";var N=function(e){return!!x&&x in e},E=Function.prototype.toString;var P=function(e){if(null!=e){try{return E.call(e)}catch(e){}try{return e+""}catch(e){}}return""},T=/^\[object .+?Constructor\]$/,I=Function.prototype,z=Object.prototype,L=I.toString,M=z.hasOwnProperty,A=RegExp("^"+L.call(M).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var D=function(e){return!(!k(e)||N(e))&&(C(e)?A:T).test(P(e))};var R=function(e,t){return null==e?void 0:e[t]};var B=function(e,t){var n=R(e,t);return D(n)?n:void 0},F=B(Object,"create");var U=function(){this.__data__=F?F(null):{},this.size=0};var H=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},W=Object.prototype.hasOwnProperty;var V=function(e){var t=this.__data__;if(F){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return W.call(t,e)?t[e]:void 0},q=Object.prototype.hasOwnProperty;var G=function(e){var t=this.__data__;return F?void 0!==t[e]:q.call(t,e)};var K=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=F&&void 0===t?"__lodash_hash_undefined__":t,this};function Z(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Z.prototype.clear=U,Z.prototype.delete=H,Z.prototype.get=V,Z.prototype.has=G,Z.prototype.set=K;var X=Z;var Y=function(){this.__data__=[],this.size=0};var $=function(e,t){return e===t||e!=e&&t!=t};var J=function(e,t){for(var n=e.length;n--;)if($(e[n][0],t))return n;return-1},Q=Array.prototype.splice;var ee=function(e){var t=this.__data__,n=J(t,e);return!(n<0)&&(n==t.length-1?t.pop():Q.call(t,n,1),--this.size,!0)};var te=function(e){var t=this.__data__,n=J(t,e);return n<0?void 0:t[n][1]};var ne=function(e){return J(this.__data__,e)>-1};var re=function(e,t){var n=this.__data__,r=J(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};function oe(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}oe.prototype.clear=Y,oe.prototype.delete=ee,oe.prototype.get=te,oe.prototype.has=ne,oe.prototype.set=re;var ie=oe,ae=B(f,"Map");var ce=function(){this.size=0,this.__data__={hash:new X,map:new(ae||ie),string:new X}};function se(e){return(se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var ue=function(e){var t=se(e);return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};var le=function(e,t){var n=e.__data__;return ue(t)?n["string"==typeof t?"string":"hash"]:n.map};var fe=function(e){var t=le(this,e).delete(e);return this.size-=t?1:0,t};var pe=function(e){return le(this,e).get(e)};var de=function(e){return le(this,e).has(e)};var he=function(e,t){var n=le(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};function ge(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}ge.prototype.clear=ce,ge.prototype.delete=fe,ge.prototype.get=pe,ge.prototype.has=de,ge.prototype.set=he;var me=ge;function be(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function n(){var r=arguments,o=t?t.apply(this,r):r[0],i=n.cache;if(i.has(o))return i.get(o);var a=e.apply(this,r);return n.cache=i.set(o,a)||i,a};return n.cache=new(be.Cache||me),n}be.Cache=me;var ye=be;var _e=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,ve=/\\(\\)?/g;!function(e){var t=ye(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(_e,(function(e,n,r,o){t.push(r?o.replace(ve,"$1"):n||e)})),t}));var we=p?p.prototype:void 0;we&&we.toString;var Oe="track/website/instrumentation";function ke(){return(ke=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var je=["samplingRate"];n(40);function Ce(){return(Ce=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Se(e){var t=[];for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];null!==r&&""!==r&&void 0!==r&&t.push(n+"="+r)}return t.join("&")}function xe(e){return new Promise((function(t,n){setTimeout((function(){return n({type:"timeout",msg:"".concat(e,"ms timeout exceeded")})}),e)}))}function Ne(e){var t=e.url;return function(e){return function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if("function"==typeof window.bfa){je.some((function(e){return n.hasOwnProperty(e)}))&&(r=n,n={});var o=r,i=o.samplingRate,a=o.platform,c={data:{target:e,value:t,tags:n}};a&&(c.data.platform=r.platform),i&&(i>1?"dev"===window.BZFD.Config.env&&console.error("Your sampling rate is above 100%."):ke(c,{opts:{samplingRate:i}})),window.bfa(Oe,c)}else void 0!==window.raven&&window.raven.captureException(new Error("Instrumentation tracking issue: BFA is not available"))}("xhr",e.type||"error",{url:t,status:e.status||0}),Promise.reject(e)}}function Ee(e){return e.ok?Promise.resolve(e):Promise.reject({type:"error",status:e.status,statusText:e.statusText})}function Pe(e){return e.json()}function Te(e){return e.text()}function Ie(e){if("dev"!==BZFD.Config.env)return e;var t=e.indexOf("?")>-1?"&":"?";return e+t+"eca385331012a621bc93fcda0a953a97"}var ze=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.data,o=n.type,i=void 0===o?"json":o,a=n.params,s=void 0===a?{}:a,u=n.skipAuth,l=void 0!==u&&u,f=n.raw,p=void 0!==f&&f,d=n.timeout;if(!e)return Promise.reject("URL parameter is required");if(!r)return Promise.reject("Can not send POST request without data");l&&(e=Ie(e));var h=Ce({headers:{"Content-type":"application/x-www-form-urlencoded"},credentials:"same-origin"},s);switch(h.method="POST",h.body=p?r:Se(r),i){case"json":t=c(e,h).then(Ee).then(Pe);break;case"text":t=c(e,h).then(Ee).then(Te);break;default:t=Promise.reject("Unsupported type ".concat(i))}return(d?Promise.race([xe(d),t]):t).catch(Ne({url:e}))};function Le(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(r=(a=c.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{r||null==c.return||c.return()}finally{if(o)throw i}}return n}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Me(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Me(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Me(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ae=function(){return BZFD.Config.bfwInfoCookie},De={general_admin:[4,0],bento_suggest_all:[116,1],bento_suggest_edition:[115,2],bento_user:[115,3],freelance_contributors:[129,1]},Re={isLoggedIn:function(){return!!a.get(Ae())},signout:function(){return ze("/buzzfeed/_admin_user",{data:{action:"logout"},params:{headers:{"x-requested-with":"XMLHttpRequest","content-type":"application/x-www-form-urlencoded"}}}).then((function(){a.remove(Ae())}))},getUserInfo:function(){var e=decodeURIComponent(a.get(Ae(),{})),t={},n=e.split("&"),r=n.findIndex((function(e){return e.match("^image=")}));if(-1!==r&&n[r+1]&&n[r+1].match("^crop=")){var o=n.splice(r+1,1);n[r]+="&"+o}return n.forEach((function(e){var n=e.split("="),r=n.shift(),o=n.join("=");t[r]=decodeURIComponent(o)})),t},isAdmin:function(){var e=this.getUserInfo();return!!(e.p_admin||e.p_editor_admin||e.p_developer_admin||e.p_partner_admin)},userCan:function(e){var t=this.getUserInfo().permission_key;if(!t)return!1;var n=Le(De[e],2),r=n[0],o=n[1],i=t.substr(t.length-(r+1),1);return(parseInt(i,16)&1<<o)>0}};function Be(e,t){var n=e.match(t);return n&&n.length?n[0]:null}var Fe={getBuzzfeedSubdomainOrWildcard:function(e){var t=Be(e,"(dev|stage|www).(buzzfeed|buzzfeednews).(com|io)$");return t||Be(e,".?[a-z]+.[a-z]+$")},get:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n="".concat(e,"=");if("undefined"==typeof document)return t;for(var r=document.cookie.split(";"),o=0;o<r.length;o++){for(var i=r[o];" "===i.charAt(0);)i=i.substring(1,i.length);if(0===i.indexOf(n))return i.substring(n.length,i.length)}return t},set:function(e){var t=e.name,n=e.value,r=e.days,o=e.domain,i="";if(r){var a=new Date;a.setTime(a.getTime()+24*r*60*60*1e3),i="; expires=".concat(a.toGMTString())}var c="";return void 0!==o&&(c="; domain=".concat(o)),document.cookie="".concat(t,"=").concat(n).concat(i).concat(c,"; path=/")},remove:function(e,t){return this.set({name:e,value:"",days:-1,domain:t})}};var Ue,He=(Ue=function(e){var t=e.userId,n=e.data,r=e.source,o=e.experimentConfig,i=e.abeagleHost,a=e.trackFn,c=We();if(!o||!o.length)return c.loaded=!0,c;o.forEach((function(e){c.declared[e.name]=e,("boolean"==typeof e.isEligible?e.isEligible:e.isEligible(n))&&(c.eligible[e.name]=e)}));var s,u,l,f=new URLSearchParams;return f.append("experiment_names",Object.keys(c.eligible).join(";")),f.append("user_id",t),f.append("source",r),f.toString(),s=fetch("".concat(i,"/public/v3/experiment_variants?").concat(f.toString())).then((function(e){return e.json()})).catch((function(e){return Ve(e),{}})),u=function(e){return c.returned=e,a&&function(e,t){var n=qe();Object.keys(e).forEach((function(r){var o="".concat(r,"_version"),i=e[r],a=i.value,c=i.version,s=i.error,u=i.resolved;if(!s){if(u&&(a=a||"control"),null===a)return Fe.remove(r,n),void Fe.remove(o,n);var l=Fe.get(r)===String(a),f=Fe.get(o)===String(c);l&&f||(Ge(r,a),Ge(o,c),t({experiment:r,variation:e[r]}))}}))}(c.returned,a),Object.keys(c.returned).forEach((function(e){c.declared[e]&&!c.returned[e].error&&c.eligible[e]&&(c.eligible[e]=c.returned[e])})),c.loaded=!0,c},l?u?u(s):s:(s&&s.then||(s=Promise.resolve(s)),u?s.then(u):s)},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return Promise.resolve(Ue.apply(this,e))}catch(e){return Promise.reject(e)}}),We=function(){return{declared:{},eligible:{},returned:{},loaded:!1}};function Ve(e){var t="undefined"!=typeof window?new URLSearchParams(window.location.search):void 0;t&&t.has("abdebug")&&console.debug(e)}function qe(){return Fe.getBuzzfeedSubdomainOrWildcard(window.location.hostname)}function Ge(e,t){var n=window.location.hostname.replace("www",""),r=qe();Fe.remove(e,".".concat(n));var o=r==="www.".concat(n)?14:1;Fe.set({name:e,value:t,days:o,domain:r})}var Ke=n(1),Ze=n(9),Xe=n.n(Ze),Ye=n(4),$e=n.n(Ye);function Je(){return(Je=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Qe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function et(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Qe(Object(n),!0).forEach((function(t){tt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Qe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function tt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function nt(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function rt(e){var t=e.viewBox,n=e.title,r=e.path,o=nt(e,["viewBox","title","path"]);return Ke.default.createElement("svg",$e()({xmlns:"http://www.w3.org/2000/svg",viewBox:t||"0 0 38 38"},o),Ke.default.createElement("title",null,n),Ke.default.createElement("path",{d:r}))}function ot(e){return Ke.default.createElement(rt,e)}function it(e){if(!e.contentFill)return Ke.default.createElement(rt,e);var t=e.viewBox,n=e.title,r=e.path,o=e.contentFill,i=nt(e,["viewBox","title","path","contentFill"]);return Ke.default.createElement("svg",$e()({xmlns:"http://www.w3.org/2000/svg",viewBox:t||"0 0 38 38"},i),Ke.default.createElement("title",null,n),Ke.default.createElement("circle",{cx:"50%",cy:"50%",r:"35%",fill:o}),Ke.default.createElement("path",{d:r}))}var at=function(e){return ot(et({title:"Apple",path:"M35.3 27.9c-.9 2-1.3 2.9-2.5 4.6-1.5 2.4-3.8 5.5-6.6 5.5-2.5 0-3.1-1.6-6.5-1.6S15.6 38 13.1 38c-2.8 0-4.9-2.8-6.5-5.2C2.1 26 1.6 18 4.4 13.7c2-3 5.1-4.8 8-4.8 3 0 4.8 1.6 7.3 1.6 2.4 0 3.8-1.6 7.3-1.6 2.6 0 5.3 1.4 7.3 3.9-6.5 3.5-5.5 12.6 1 15.1zm-11-21.7c1.2-1.6 2.2-3.9 1.9-6.2-2 .1-4.4 1.4-5.8 3.1-1.3 1.5-2.3 3.8-1.9 6 2.2.1 4.5-1.2 5.8-2.9z"},e))},ct=function(e){return ot(et({title:"Caret Right",path:"M11.5 36c-.5 0-1-.2-1.4-.6-.8-.8-.8-2 0-2.8L23.7 19 10.1 5.4c-.8-.8-.8-2 0-2.8.8-.8 2-.8 2.8 0L29.3 19 12.9 35.4c-.4.4-.9.6-1.4.6z"},e))},st=function(e){return it(et({title:"Circle Check",path:"M19 0C8.5 0 0 8.5 0 19s8.5 19 19 19 19-8.5 19-19S29.5 0 19 0zm0 24.8c-1.6 1.6-4.2 1.6-5.8 0l-5.4-5.4 2.9-2.9 5.4 5.4 11-11 2.9 2.9-11 11z"},e))},ut=function(e){return it(et({title:"Circle Exclamation",path:"M19 0C8.5 0 0 8.5 0 19s8.5 19 19 19 19-8.5 19-19S29.5 0 19 0zm-2 7h4v14h-4V7zm2 24c-1.7 0-3-1.3-3-3s1.3-3 3-3 3 1.3 3 3-1.3 3-3 3z"},e))},lt=function(e){return ot(et({title:"Facebook",path:"M38,19.12A19,19,0,1,0,16,38V24.64H11.21V19.12H16V14.9c0-4.79,2.84-7.43,7.18-7.43a29.21,29.21,0,0,1,4.25.37v4.7H25.07a2.76,2.76,0,0,0-3.1,3v3.59h5.27l-.84,5.52H22V38A19.08,19.08,0,0,0,38,19.12Z"},e))},ft=function(e){var t=Je({},e);return Ke.default.createElement("svg",$e()({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 48 48",title:"Google"},t),Ke.default.createElement("path",{d:"m43.611 20.083h-1.611v-.083h-18v8h11.303c-1.649 4.657-6.08 8-11.303 8-6.627 0-12-5.373-12-12s5.373-12 12-12c3.059 0 5.842 1.154 7.961 3.039l5.657-5.657c-3.572-3.329-8.35-5.382-13.618-5.382-11.045 0-20 8.955-20 20s8.955 20 20 20 20-8.955 20-20c0-1.341-.138-2.65-.389-3.917z",fill:"#ffc107"}),Ke.default.createElement("path",{d:"m6.306 14.691 6.571 4.819c1.778-4.402 6.084-7.51 11.123-7.51 3.059 0 5.842 1.154 7.961 3.039l5.657-5.657c-3.572-3.329-8.35-5.382-13.618-5.382-7.682 0-14.344 4.337-17.694 10.691z",fill:"#ff3d00"}),Ke.default.createElement("path",{d:"m24 44c5.166 0 9.86-1.977 13.409-5.192l-6.19-5.238c-2.008 1.521-4.504 2.43-7.219 2.43-5.202 0-9.619-3.317-11.283-7.946l-6.522 5.025c3.31 6.477 10.032 10.921 17.805 10.921z",fill:"#4caf50"}),Ke.default.createElement("path",{d:"m43.611 20.083h-1.611v-.083h-18v8h11.303c-.792 2.237-2.231 4.166-4.087 5.571.001-.001.002-.001.003-.002l6.19 5.238c-.438.398 6.591-4.807 6.591-14.807 0-1.341-.138-2.65-.389-3.917z",fill:"#1976d2"}))},pt=function(e){return ot(et({title:"X",path:"M30.3 10.5l-2.8-2.8-8.5 8.5-8.5-8.5-2.8 2.8 8.5 8.5-8.5 8.5 2.8 2.8 8.5-8.5 8.5 8.5 2.8-2.8-8.5-8.5z"},e))};var dt=function(){var e="https://stage.buzzfeed.com";return"undefined"!=typeof window&&"www.buzzfeed.com"===window.location.hostname&&(e="https://www.buzzfeed.com"),e},ht=function(e){return/^([\w-]+(?:\.[\w-]+)*)(\+[\w]+)?@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/i.test(e)},gt="signInModal__eoWwW",mt="container__2DSNS",bt="header__3IJeE",yt="socialSigninButton__3NxfE",_t="submitButton__3CL8N",vt="inputEmail__xzzWI",wt="icon__3HKBy",Ot="errorSection__1gXwu",kt="iconError__3evLB",jt="errorMessage__3iLMS",Ct="linkButton__m86IK",St="dividerOr__sCtBz",xt="socialSigninText__3yJdx",Nt="signupSection__TqgIL",Et="signupText__2EWvq";function Pt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Tt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Pt(Object(n),!0).forEach((function(t){It(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function It(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function zt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(r=(a=c.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{r||null==c.return||c.return()}finally{if(o)throw i}}return n}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Lt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Lt(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Lt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Mt={unit_type:"modal",unit_name:"signin_modal"},At=["google","facebook","apple"];function Dt(e){var t=e.provider,n=e.onTrackExternalLink,r=e.redirectUrl,o={google:{connection:"google-oauth2",text:"Connect with Google",Icon:function(){return Ke.default.createElement(ft,{className:wt,fill:"#DD4B39"})}},facebook:{connection:"facebook",text:"Connect with Facebook",Icon:function(){return Ke.default.createElement(lt,{className:wt,fill:"#3B5998"})}},apple:{connection:"apple",text:"Connect with Apple",Icon:function(){return Ke.default.createElement(at,{className:wt})}}}[t],i=o.connection,a=o.Icon,c=o.text,s=new URL(dt()+"/auth/csrf");s.searchParams.set("provider","auth0"),s.searchParams.set("connection",i),s.searchParams.set("redirect",r||window.location.href.toString());return Ke.default.createElement("button",{type:"submit",className:yt,onClick:function(){n({item_type:"button",item_name:"".concat(t,"_login"),target_content_url:s.toString()}),window.location.href=s.toString()}},Ke.default.createElement(a,null),Ke.default.createElement("div",{className:xt},c))}function Rt(e){var t=e.isOpen,n=void 0!==t&&t,r=e.onClose,o=e.track,i=void 0===o?{}:o,a=e.redirectUrl,c=void 0===a?"":a,s=Object(Ke.useRef)(null),u=Object(Ke.useRef)(null),l=zt(Object(Ke.useState)(""),2),f=l[0],p=l[1],d=zt(Object(Ke.useState)(""),2),h=d[0],g=d[1],m=Object(Ke.useCallback)((function(e,t){"function"==typeof i[e]&&i[e](t)}),[i]),b=function(e){m("externalLink",Tt(Tt({},Mt),e))},y=function(){if(!ht(f))return g("Email is not valid.");g("");var e=function(e){for(var t=e+"=",n=document.cookie.split(";"),r=0;r<n.length;r++){for(var o=n[r];" "===o.charAt(0);)o=o.substring(1,o.length);if(0===o.indexOf(t))return o.substring(t.length,o.length)}return null}("_xsrf"),t=new URLSearchParams({email:encodeURIComponent(f),_xsrf:encodeURIComponent(e)}).toString();return fetch("".concat(dt(),"/auth/ad-track-token/hem?").concat(t)).catch((function(){})).finally((function(){var e=new URL("/auth/csrf",dt());e.searchParams.set("provider","auth0"),e.searchParams.set("connection","email"),e.searchParams.set("login_hint",f),e.searchParams.set("redirect",c||window.location.href.toString()),b({item_type:"button",item_name:"passwordless",target_content_url:e.toString()}),window.location.href=e.toString()})),""},_=function(e){var t=new URL("/auth/csrf",dt());if(t.searchParams.set("provider","auth0"),t.searchParams.set("redirect",c||window.location.href.toString()),"sign_up"===e&&t.searchParams.set("screen_hint","signup"),f){if(!ht(f))return void g("Email is not valid.");t.searchParams.set("login_hint",f)}b({item_type:"text",item_name:e,target_content_url:t.toString()}),window.location.href=t.toString()},v=function(){r(),p(""),g(""),m("contentAction",Tt(Tt({},Mt),{},{subunit_type:"",subunit_name:"",item_type:"button",item_name:"overlay",action_type:"hide",action_value:"signin_modal"}))},w=function(e){s&&s.current&&(s.current.contains(e.target)||v())};Object(Ke.useEffect)((function(){document.addEventListener("mousedown",w),document.body.style.overflow="hidden"}),[]),Object(Ke.useEffect)((function(){n?(document.body.style.overflow="hidden",m("contentAction",{action_type:"show",action_value:"signin_modal"})):document.body.style.overflow="unset"}),[n,m]);var O=function(){var e=Object(Ke.useCallback)((function(e){"Enter"===e.key?y():"Escape"===e.key&&v()}),[]);return Object(Ke.useEffect)((function(){return n?window.addEventListener("keydown",e):window.removeEventListener("keydown",e),function(){window.removeEventListener("keydown",e)}}),[n,e]),""};return Object(Ke.useEffect)((function(){n&&u.current&&u.current.focus()}),[n]),n?Ke.default.createElement("div",{className:gt},Ke.default.createElement(O,null),Ke.default.createElement("div",{ref:s,className:mt},Ke.default.createElement("h2",{className:bt},"Confirm it's you"),Ke.default.createElement("input",{ref:u,type:"text",className:vt,value:f,onChange:function(e){return t=e.target.value.trim(),p(t),void(h&&g(""));var t},placeholder:"Enter your email"}),h&&Ke.default.createElement("div",{className:Ot},Ke.default.createElement(ut,{className:"".concat(wt," ").concat(kt),fill:"#d00e17"}),Ke.default.createElement("p",{className:jt},h)),Ke.default.createElement("button",{type:"submit",className:_t,onClick:y},"Send me a code"),Ke.default.createElement("button",{type:"submit",className:Ct,onClick:function(){return _("password")}},"Continue with password"),Ke.default.createElement("div",{className:St},Ke.default.createElement("span",null,"OR")),Ke.default.createElement("div",null,At.map((function(e,t){return Ke.default.createElement(Dt,{key:t,provider:e,onTrackExternalLink:b,redirectUrl:c})}))),Ke.default.createElement("div",{className:Nt},Ke.default.createElement("p",{className:Et},"Don't have an account?",Ke.default.createElement("button",{className:Ct,onClick:function(){return _("sign_up")}},"Sign up"))))):null}Rt.propTypes={isOpen:Xe.a.bool.isRequired,onClose:Xe.a.func.isRequired,redirectUrl:Xe.a.string,track:Xe.a.shape({contentAction:Xe.a.func,externalLink:Xe.a.func})};var Bt=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n="".concat(e,"=");if("undefined"==typeof document)return t;for(var r=document.cookie.split(";"),o=0;o<r.length;o++){for(var i=r[o];" "===i.charAt(0);)i=i.substring(1,i.length);if(0===i.indexOf(n))return i.substring(n.length,i.length)}return t},Ft=function(e){var t=e.name,n=e.value,r=e.days,o=e.domain,i="";if(r){var a=new Date;a.setTime(a.getTime()+24*r*60*60*1e3),i="; expires=".concat(a.toGMTString())}var c="";return void 0!==o&&(c="; domain=".concat(o)),document.cookie="".concat(t,"=").concat(n).concat(i).concat(c,"; path=/")};var Ut=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window,t=e.location.hostname,n=["buzzfeed","buzzfeednews","tasty","huffpost","huffingtonpost"],r=t.split(".")[0];return r&&"stage"!==r&&-1===n.indexOf(r)&&t.split(".").length>=3&&(t=t.substring(r.length+1)),t};function Ht(e,t,n){var r,o,i,a,c,s,u;for(this.version="1.0.1",this.minAlphabetLength=16,this.sepDiv=3.5,this.guardDiv=12,this.errorAlphabetLength="error: alphabet must contain at least X unique characters",this.errorAlphabetSpace="error: alphabet cannot contain spaces",this.alphabet="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",this.seps="cfhistuCFHISTU",this.minHashLength=parseInt(t,10)>0?t:0,this.salt="string"==typeof e?e:"","string"==typeof n&&(this.alphabet=n),r="",o=0,a=this.alphabet.length;o!==a;o++)-1===r.indexOf(this.alphabet[o])&&(r+=this.alphabet[o]);if(this.alphabet=r,this.alphabet.length<this.minAlphabetLength)throw this.errorAlphabetLength.replace("X",this.minAlphabetLength);if(-1!==this.alphabet.search(" "))throw this.errorAlphabetSpace;for(o=0,a=this.seps.length;o!==a;o++)-1===(i=this.alphabet.indexOf(this.seps[o]))?this.seps=this.seps.substr(0,o)+" "+this.seps.substr(o+1):this.alphabet=this.alphabet.substr(0,i)+" "+this.alphabet.substr(i+1);this.alphabet=this.alphabet.replace(/ /g,""),this.seps=this.seps.replace(/ /g,""),this.seps=this.consistentShuffle(this.seps,this.salt),(!this.seps.length||this.alphabet.length/this.seps.length>this.sepDiv)&&(1===(c=Math.ceil(this.alphabet.length/this.sepDiv))&&c++,c>this.seps.length?(s=c-this.seps.length,this.seps+=this.alphabet.substr(0,s),this.alphabet=this.alphabet.substr(s)):this.seps=this.seps.substr(0,c)),this.alphabet=this.consistentShuffle(this.alphabet,this.salt),u=Math.ceil(this.alphabet.length/this.guardDiv),this.alphabet.length<3?(this.guards=this.seps.substr(0,u),this.seps=this.seps.substr(u)):(this.guards=this.alphabet.substr(0,u),this.alphabet=this.alphabet.substr(u))}Ht.prototype.encode=function(){var e,t,n="",r=Array.prototype.slice.call(arguments);if(!r.length)return n;for(r[0]instanceof Array&&(r=r[0]),e=0,t=r.length;e!==t;e++)if("number"!=typeof r[e]||r[e]%1!=0||r[e]<0)return n;return this._encode(r)},Ht.prototype.decode=function(e){return e.length&&"string"==typeof e?this._decode(e,this.alphabet):[]},Ht.prototype.encodeHex=function(e){var t,n,r;if(e=e.toString(),!/^[0-9a-fA-F]+$/.test(e))return"";for(t=0,n=(r=e.match(/[\w\W]{1,12}/g)).length;t!==n;t++)r[t]=parseInt("1"+r[t],16);return this.encode.apply(this,r)},Ht.prototype.decodeHex=function(e){var t,n,r=[],o=this.decode(e);for(t=0,n=o.length;t!==n;t++)r+=o[t].toString(16).substr(1);return r},Ht.prototype._encode=function(e){var t,n,r,o,i,a,c,s,u,l,f,p=this.alphabet,d=e.length,h=0;for(r=0,o=e.length;r!==o;r++)h+=e[r]%(r+100);for(n=t=p[h%p.length],r=0,o=e.length;r!==o;r++)i=e[r],a=n+this.salt+p,p=this.consistentShuffle(p,a.substr(0,p.length)),t+=c=this.hash(i,p),r+1<d&&(s=(i%=c.charCodeAt(0)+r)%this.seps.length,t+=this.seps[s]);for(t.length<this.minHashLength&&(u=(h+t[0].charCodeAt(0))%this.guards.length,(t=this.guards[u]+t).length<this.minHashLength&&(u=(h+t[2].charCodeAt(0))%this.guards.length,t+=this.guards[u])),l=parseInt(p.length/2,10);t.length<this.minHashLength;)(f=(t=(p=this.consistentShuffle(p,p)).substr(l)+t+p.substr(0,l)).length-this.minHashLength)>0&&(t=t.substr(f/2,this.minHashLength));return t},Ht.prototype._decode=function(e,t){var n,r,o,i,a=[],c=0,s=new RegExp("["+this.guards+"]","g"),u=e.replace(s," "),l=u.split(" ");if(3!==l.length&&2!==l.length||(c=1),void 0!==(u=l[c])[0]){for(n=u[0],u=u.substr(1),s=new RegExp("["+this.seps+"]","g"),c=0,r=(l=(u=u.replace(s," ")).split(" ")).length;c!==r;c++)o=l[c],i=n+this.salt+t,t=this.consistentShuffle(t,i.substr(0,t.length)),a.push(this.unhash(o,t));this._encode(a)!==e&&(a=[])}return a},Ht.prototype.consistentShuffle=function(e,t){var n,r,o,i,a,c;if(!t.length)return e;for(i=e.length-1,a=0,c=0;i>0;i--,a++)c+=n=t[a%=t.length].charCodeAt(0),o=e[r=(n+a+c)%i],e=(e=e.substr(0,r)+e[i]+e.substr(r+1)).substr(0,i)+o+e.substr(i+1);return e},Ht.prototype.hash=function(e,t){var n="",r=t.length;do{n=t[e%r]+n,e=parseInt(e/r,10)}while(e);return n},Ht.prototype.unhash=function(e,t){var n,r=0;for(n=0;n<e.length;n++)r+=t.indexOf(e[n])*Math.pow(t.length,e.length-n-1);return r};function Wt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Vt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Wt(Object(n),!0).forEach((function(t){qt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Wt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function qt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Gt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(r=(a=c.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{r||null==c.return||c.return()}finally{if(o)throw i}}return n}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Kt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Kt(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Kt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Zt=parseInt(1e10*Math.random(),10),Xt=([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(function(e){return(e^function(){try{return crypto.getRandomValues(new Uint8Array(1))[0]}catch(e){return 255*Math.random()}}()&15>>e/4).toString(16)})),Yt=function(e){if(0!==e.indexOf(".")){var t=/[0-9A-Za-z]+/.exec(e);return null!==t&&t[0]===e&&parseInt(e,36)}var n=e.substr(1,2);return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.salt,r=void 0===n?null:n;return new Ht(r).decode(e)[0]}(e.substr(3),{salt:n})},$t=function(e){var t=parseInt(25*Math.random()+10,10).toString(36)+parseInt(25*Math.random()+10,10).toString(36);return".".concat(t).concat(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.salt,r=void 0===n?null:n,o=t.length,i=void 0===o?32:o;return new Ht(r,i).encode(e)}(e,{salt:t,length:0}))},Jt=function(e){var t=decodeURIComponent(e).split("&").map((function(e){return e.split("=")})).reduce((function(e,t){var n=Gt(t,2),r=n[0],o=n[1];return Vt(Vt({},e),{},qt({},r,o))}),{}),n=t.u,r=t.uuid;return{legacyIdentifier:Yt(n||""),identifier:r}},Qt=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.legacy,n=void 0!==t&&t,r={name:"bf_visit",days:1e4,domain:Ut()},o=Bt(r.name),i=Jt(o),a=i.legacyIdentifier,c=i.identifier,s=$t(Zt);return n?a||(Ft(Vt(Vt({},r),{},{value:encodeURIComponent("u=".concat(s,"&uuid=").concat(c||Xt,"&v=2"))})),Zt):c||a?c||String(a):(Ft(Vt(Vt({},r),{},{value:encodeURIComponent("u=".concat(s,"&uuid=").concat(Xt,"&v=2"))})),Xt)},en=n(8),tn=n.n(en),nn=n(2),rn=n.n(nn),on=n(32),an=n.n(on),cn=Object(r.d)({}),sn=Object(r.d)({}),un=Object(r.d)("light"),ln=Object(r.d)({}),fn=n(41),pn=n.n(fn),dn=n(42),hn=n.n(dn),gn=n(43),mn=n.n(gn),bn=n(44),yn=n.n(bn),_n=n(45),vn=n.n(_n),wn=n(46),On=n.n(wn),kn=n(47),jn=n.n(kn),Cn=n(48),Sn=n.n(Cn);function xn(){return(xn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Nn={bf_default:jn.a,bf_default_home:Sn.a,green:vn.a,light:hn.a,light_news:mn.a,light_news_feed:yn.a,purple:pn.a,transparent:On.a},En=function(e){return function(t){return Object(r.g)(un.Consumer,null,(function(n){return Object(r.g)(e,xn({theme:Nn[n],themeName:n},t))}))}};function Pn(e){return(Pn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Tn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function In(e,t){return(In=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function zn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=An(e);if(t){var o=An(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Ln(this,n)}}function Ln(e,t){return!t||"object"!==Pn(t)&&"function"!=typeof t?Mn(e):t}function Mn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function An(e){return(An=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Dn=[{code:"us",name:"US"},{code:"uk",name:"UK"},{code:"au",name:"Australia"},{code:"ca",name:"Canada"},{code:"de",name:"Deutschland"},{code:"in",name:"India"},{code:"ja-jp",name:"Japan"},{code:"mx",name:"Latam"}],Rn=En(function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&In(e,t)}(c,e);var t,n,o,i=zn(c);function c(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,c),(t=i.call(this,e)).state={edition:"us"},t.handleSelectChange=t.handleSelectChange.bind(Mn(t)),t}return t=c,(n=[{key:"componentDidMount",value:function(){this.setState({edition:a.get("country","us")})}},{key:"handleSelectChange",value:function(e){"de"===e.target.value?window.location="/de":(a.set({name:"country",value:e.target.value,domain:"dev"===window.BZFD.Config.env?"buzzfeed.io":"buzzfeed.com",days:180}),window.location="/")}},{key:"render",value:function(e,t){var n=this,o=e.theme,i=t.edition;return Object(r.g)(cn.Consumer,null,(function(e){return Object(r.g)("div",{className:an.a.editionSelect},Object(r.g)("label",{className:o.text,for:"js-header-edition-select"},e.edition),Object(r.g)("select",{id:"js-header-edition-select",className:an.a.select,value:i,onBlur:n.handleSelectChange},Dn.map((function(e){var t=e.code,n=e.name;return Object(r.g)("option",{"data-bfa":"@a:Main-Nav;@d:".concat(n,";"),value:t},n)}))))}))}}])&&Tn(t.prototype,n),o&&Tn(t,o),c}(r.a));function Bn(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var Fn=function(e){var t=e.id,n=e.children,o=Bn(e,["id","children"]);return Object(r.g)("svg",o,n,Object(r.g)("use",{xlinkHref:"#".concat(t)}))};function Un(){return(Un=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Hn={id:"bfo-logo",width:315.7,height:53.2},Wn=function(e){return Object(r.g)(Fn,Un({id:Hn.id,viewBox:"0 0 ".concat(Hn.width," ").concat(Hn.height)},e))};function Vn(){return(Vn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var qn={width:48,height:18,id:"asis-logo"},Gn=function(e){var t=Vn({},e);return Object(r.g)(Fn,Vn({},qn,{viewBox:"0 0 ".concat(qn.width," ").concat(qn.height)},t))};function Kn(){return(Kn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Zn={width:95,height:18,id:"bfn-logo"},Xn=function(e){var t=Kn({},e);return Object(r.g)(Fn,Kn({},Zn,{viewBox:"0 0 ".concat(Zn.width," ").concat(Zn.height)},t))};function Yn(){return(Yn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var $n={id:"bfo-logo",width:315.7,height:53.2},Jn=function(e){return Object(r.g)(Fn,Yn({id:$n.id,viewBox:"0 0 ".concat($n.width," ").concat($n.height)},e))};function Qn(){return(Qn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var er={width:48,height:18,id:"goodful-logo"},tr=function(e){var t=Qn({},e);return Object(r.g)(Fn,Qn({},er,{viewBox:"0 0 ".concat(er.width," ").concat(er.height)},t))};function nr(){return(nr=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var rr={width:32,height:18,id:"tasty-logo"},or=function(e){var t=nr({},e);return Object(r.g)(Fn,nr({},rr,{viewBox:"0 0 ".concat(rr.width," ").concat(rr.height)},t))};function ir(){return(ir=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var ar={width:386,height:45,id:"huffpost-logo"},cr=function(e){var t=ir({},e);return Object(r.g)(Fn,ir({},ar,{viewBox:"0 0 ".concat(ar.width," ").concat(ar.height)},t))};function sr(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var ur=function(e){var t=e.destinationName,n=sr(e,["destinationName"]);switch(t){case"bfo":return Object(r.g)(Wn,n);case"asis":return Object(r.g)(Gn,n);case"bfn":return Object(r.g)(Xn,n);case"buzzfeed":return Object(r.g)(Jn,n);case"goodful":return Object(r.g)(tr,n);case"tasty":return Object(r.g)(or,n);case"huffpost":return Object(r.g)(cr,n)}return null};function lr(){return(lr=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var fr={id:"newsletter-icon",width:32,height:32},pr=function(e){var t=lr({},e);return Object(r.g)(Fn,lr({id:fr.id,viewBox:"0 0 ".concat(fr.width," ").concat(fr.height)},t))},dr={id:"newsletter-color-icon",width:28,height:33},hr=function(e){var t=lr({},e);return Object(r.g)(Fn,lr({id:dr.id,viewBox:"0 0 ".concat(dr.width," ").concat(dr.height)},t))};function gr(){return(gr=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var mr={id:"caret-icon",width:38,height:38},br=function(e){var t=gr({},e);return Object(r.g)(Fn,gr({id:mr.id,viewBox:"0 0 ".concat(mr.width," ").concat(mr.height)},t))},yr=function(e){var t=e.label,n=e.location,r=e.ga;(window[r]||_r)("send",{hitType:"event",eventCategory:"Nav:".concat(n),eventAction:"click",eventLabel:t})};function _r(){console.group("Dummy ga global called with");var e=Array.prototype.slice.call(arguments);console.log(e[0]),console.log(JSON.stringify(e[1])),console.groupEnd()}function vr(e){return(vr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function wr(){return(wr=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Or(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function kr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function jr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Cr(e,t){return(Cr=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Sr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Nr(e);if(t){var o=Nr(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return xr(this,n)}}function xr(e,t){return!t||"object"!==vr(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function Nr(e){return(Nr=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Er=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Cr(e,t)}(a,e);var t,n,o,i=Sr(a);function a(){return kr(this,a),i.apply(this,arguments)}return t=a,(n=[{key:"clickHandler",value:function(e){var t=e.onClick,n=e.label,r=e.location,o=e.isInfoPage,i=e.ga,a=e.url,c=e.trackingData,s=e.trackCETNavClick;return function(e){return yr({label:n,location:r,ga:i}),s({location:r,url:a,isInfoPage:o,label:n,trackingData:c}),t(e)}}},{key:"render",value:function(e){var t=this,n=e.href,o=e.children,i=e.label,a=e.location,c=e.isInfoPage,s=e.onClick,u=void 0===s?function(){}:s,l=e.trackingData,f=Or(e,["href","children","label","location","isInfoPage","onClick","trackingData"]);return Object(r.g)(sn.Consumer,null,(function(e){var s=n.match(/^https?:/)?n:"".concat(e.bf_url).concat(n);return Object(r.g)(ln.Consumer,null,(function(n){return Object(r.g)("a",wr({href:s,onClick:t.clickHandler({onClick:u,label:i,location:a,isInfoPage:c,ga:e.ga,url:s,trackingData:l,trackCETNavClick:n.trackNavClick})},f),o)}))}))}}])&&jr(t.prototype,n),o&&jr(t,o),a}(r.a),Pr=En((function(e){var t=e.className,n=e.theme,o=Or(e,["className","theme"]);return Object(r.g)(Er,wr({className:"".concat(t," ").concat(n.link)},o))}));function Tr(e){return(Tr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ir(){return(Ir=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function zr(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function Lr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Mr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Ar(e,t){return(Ar=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Dr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Br(e);if(t){var o=Br(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Rr(this,n)}}function Rr(e,t){return!t||"object"!==Tr(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function Br(e){return(Br=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Fr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Ar(e,t)}(a,e);var t,n,o,i=Dr(a);function a(){return Lr(this,a),i.apply(this,arguments)}return t=a,(n=[{key:"clickHandler",value:function(e){var t=e.onClick,n=e.label,r=e.location,o=e.ga;return function(e){return yr({label:n,location:r,ga:o}),t(e,n,r)}}},{key:"render",value:function(e){var t=this,n=e.onClick,o=e.children,i=e.label,a=e.location,c=zr(e,["onClick","children","label","location"]);return Object(r.g)(sn.Consumer,null,(function(e){return Object(r.g)("button",Ir({onClick:t.clickHandler({onClick:n,label:i,location:a,ga:e.ga}),type:"button"},c),o)}))}}])&&Mr(t.prototype,n),o&&Mr(t,o),a}(r.a),Ur=n(25),Hr=n.n(Ur);function Wr(e){return(Wr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Vr(){return(Vr=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function qr(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function Gr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Kr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Zr(e,t){return(Zr=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Xr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=$r(e);if(t){var o=$r(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Yr(this,n)}}function Yr(e,t){return!t||"object"!==Wr(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function $r(e){return($r=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Jr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Zr(e,t)}(a,e);var t,n,o,i=Xr(a);function a(){return Gr(this,a),i.apply(this,arguments)}return t=a,(n=[{key:"render",value:function(e){var t=e.children,n=e.navLabelType,o=qr(e,["children","navLabelType"]);if(!n)return null;var i=Hr.a[n]?Hr.a[n]:"";return Object(r.g)("span",Vr({className:"".concat(Hr.a.navLabel," ").concat(i)},o),t)}}])&&Kr(t.prototype,n),o&&Kr(t,o),a}(r.a),Qr={bfo:{displayName:"BuzzFeed"},bfreviews:{displayName:"BuzzFeed Reviews"},tasty:{displayName:"BuzzFeed Tasty"},goodful:{displayName:"BuzzFeed Goodful"},asis:{displayName:"BuzzFeed As Is"},perolike:{displayName:"BuzzFeed Pero Like"},cocoabutter:{displayName:"BuzzFeed Cocoa Butter"},huffpost:{displayName:"HuffPost"}},eo=function(e){if(!e)return"";var t=e;try{t=new URL(e).pathname}catch(e){}return"/"===t.charAt(0)&&(t=t.slice(1,t.length)),t.replace(/\//g,"_")};function to(e){return e.replace("http://","").replace("https://","").replace("www.","")}var no=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.threshold,r=void 0===n?.5:n,o=t.visibility_time,i=void 0===o?1e3:o;if("undefined"==typeof window)return function(){};var a=new Map,c=new Map,s=function(e,t){e.forEach((function(e){if(a.has(e.target)){if(!e.isIntersecting)return clearTimeout(c.get(e.target)),void c.delete(e.target);if(!(c.has(e.target)||e.intersectionRatio<r)){var n=setTimeout((function(){a.get(e.target).call(null),t.unobserve(e.target),c.delete(e.target)}),i);c.set(e.target,n)}}}))},u=new IntersectionObserver(s,{threshold:[0,r,1]}),l=function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return a.has(t)||(a.set(t,(function(){return e.apply(void 0,r)})),u.observe(t)),function(){a.delete(t),u.unobserve(t),c.has(t)&&(clearTimeout(c.get(t)),c.delete(t))}};return function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return l.apply(void 0,[e].concat(n))}};function ro(e){return(ro="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function oo(e){return function(e){if(Array.isArray(e))return io(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return io(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return io(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function io(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ao(e,t,n){if(n)return t?t(e()):e();try{var r=Promise.resolve(e());return t?r.then(t):r}catch(e){return Promise.reject(e)}}function co(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function so(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?co(Object(n),!0).forEach((function(t){uo(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):co(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function uo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function lo(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return lo=function(){return e},e}function fo(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}var po=fo((function(e){var t=Oo();if(void 0!==t)return ao(go,(function(n){(0,n.impression)(so(so({},bo),e),t())}))})),ho=function(e){var t=e.linkData,n=e.layers;return ao(go,(function(e){e.externalClick.apply(void 0,[t].concat(oo(n)))}))},go=fo((function(){return Promise.resolve().then((function(){return function(e){if(e&&e.__esModule)return e;if(null===e||"object"!==ro(e)&&"function"!=typeof e)return{default:e};var t=lo();if(t&&t.has(e))return t.get(e);var n={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var i=r?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,t&&t.set(e,n),n}(n(92))}))})),mo=["bfnews","tasty"],bo={unit_type:"nav",unit_name:"main"},yo=function(e){var t=e.label,n=e.location,r=e.url,o=e.isInfoPage,i=e.trackingData,a=void 0===i?{}:i,c=Oo();if(void 0!==c){var s,u,l,f,p,d=!!r&&!r.toLowerCase().match(/.*buzzfeed\.(com|io)|(^\/.*$)/),h=so(so({},bo),{},{position_in_unit:"",item_type:"text",item_name:eo(r)||t.toLowerCase()}),g={badgeBar:so(so({},h),{},{item_type:"badge"}),logo:so(so({},h),{},{item_name:t}),trendingBar:so(so({},h),{},{unit_name:"topic_nav"}),moreMenu:so(so({},h),{},{unit_name:"hidden"}),newsletters:so(so({},h),{},{unit_name:"main",item_type:"button",item_name:t}),userMenu:so(so({},h),{},{unit_name:"profile_nav"})}[n]||h,m=!1;"userMenu"===n?"notification-link"===t?(g.item_type="notification",g.item_name=a.item_name||"",s="",u=""):"comment-notification-link"===t?(g.item_type="text",g.item_name=a.item_name||"",g.subunit_type="package",g.subunit_name="recent_comments",s="buzz",u=a.target_content_id||""):"teaser-notification-link"===t?(g.item_type="teaser_notification",g.item_name=a.item_name||"",s="",u=""):"profile"===t?(g.item_type="profile",g.item_name="icon",u=Bt("user_uuid")||"unauthenticated",s="user"):(g.item_type="text",s="url",u=r,g.item_name=t.toLowerCase().replace(/ /g,"_")):"membership"===n&&"membership"===t&&(g.item_type="button"),o&&(s="info"),"logo"===n&&(u=t),"newsletters"===n&&(u=t),"merch"===t&&(g.item_name="merch",u="shop.buzzfeed.com"),"secondary"===n&&(g.unit_name="secondary"),"daily_fact"===t&&(g.unit_name="secondary",g.item_type="text",g.item_name="daily_fact",g.position_in_unit=a.position_in_unit,d||(g.target_content_type="url",g.target_content_id=to(r)),m=d),mo.includes(t.toLowerCase())||m?(g.item_name=t.toLowerCase(),ho({linkData:so(so({click_type:"left"},g),{},{target_content_url:to(r)}),layers:[c()]})):(l={linkData:so(so({click_type:"left"},g),{},{target_content_type:s||"feed",target_content_id:u||eo(r)||to(r)},a),layers:[c()]},f=l.linkData,p=l.layers,ao(go,(function(e){e.internalClick.apply(void 0,[f].concat(oo(p)))})))}},_o=function(e){var t,n,r=e.location,o=e.action_value,i=e.action_type,a=Oo(),c={subunit_type:"",subunit_name:""},s={search:so(so({},bo),{},{item_type:"submission",item_name:"search",position_in_unit:7,action_type:"search"}),ccpa:so(so({},bo),{},{unit_name:"hidden",item_type:"button",item_name:"ccpa_button",action_type:"show"}),notificationMenu:so(so({},bo),{},{item_type:"button",item_name:"notification_menu",action_type:"show"}),teaserNotificationMenu:so(so({},bo),{},{item_type:"button",item_name:"teaser_notification_menu",action_type:"show"}),signInModal:so(so({},bo),{},{item_type:"text",item_name:"auth_signin",action_type:i}),newsletterSignupModal:so(so({},bo),{},{item_type:e.item_type,item_name:e.item_name,action_type:i,action_value:o})};("newsletterSignupModal"===r&&(c.subunit_type="component",c.subunit_name="newsletter_subscription"),void 0!==a&&s[r])&&(t={layers:[s[r],so(so({},c),{},{action_value:o}),a()]},n=t.layers,ao(go,(function(e){e.contentAction.apply(void 0,oo(n))})))},vo=function(e){var t=Oo();if(void 0!==t){var n=[];Object.keys(e.eligible).forEach((function(t){var r=e.eligible[t];r&&r.value&&n.push([t,r.id,r.version,r.value,r.variant_id].join("|"))})),function(e){var t=e.layers,n=e.experiment_id;ao(go,(function(e){e.abTest.apply(void 0,oo(t).concat([{experiment_id:n}]))}))}({layers:[t()],experiment_id:n})}},wo=no(po);function Oo(){if("undefined"!=typeof window&&window.clientEventTracking&&"function"==typeof window.clientEventTracking.getPageContextLayer)return window.clientEventTracking.getPageContextLayer()}function ko(){return(ko=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var jo,Co={id:"pyramid-scheme-logo-2",width:114,height:9},So=function(e){return Object(r.g)(Fn,ko({id:Co.id,viewBox:"0 0 ".concat(Co.width," ").concat(Co.height)},e),Object(r.g)("symbol",Co,Object(r.g)("path",{d:"M2.01052 9C3.58666 9 4.03345 8.6087 4.03345 8.06324C4.03345 7.77866 3.90934 7.56522 3.57425 7.31621C3.31363 7.12648 3.10265 6.85376 3.10265 6.43874V5.79842C3.10265 5.66799 3.15229 5.59684 3.25158 5.59684C3.53702 5.59684 3.90934 6.083 4.6788 6.083C6.11843 6.083 7.35949 4.80237 7.35949 3.083C7.35949 1.26877 6.317 0.142293 3.97139 0.142293H1.6382C0.384729 0.142293 0 0.474308 0 0.98419C0 1.2332 0.124106 1.44664 0.40955 1.73123C0.645351 1.96838 0.794279 2.28854 0.794279 3.10672V6.07115C0.794279 6.77075 0.645351 7.09091 0.421961 7.28063C0.148927 7.5415 0 7.79051 0 8.08696C0 8.63241 0.632941 9 2.01052 9ZM3.8597 4.89723C3.46256 4.89723 3.10265 4.70751 3.10265 4.31621V2.00395C3.10265 1.57708 3.25158 1.39921 3.72318 1.39921C4.443 1.39921 4.95183 2.05138 4.95183 3.13044C4.95183 4.28063 4.48023 4.89723 3.8597 4.89723Z",fill:"url(#paint0_linear_149_29_2)"}),Object(r.g)("path",{d:"M11.4798 9C12.9318 9 13.4283 8.6087 13.4283 8.0751C13.4283 7.77866 13.2917 7.58893 13.0063 7.29249C12.7953 7.06719 12.634 6.67589 12.634 6.16601V5.35968C13.2421 4.52964 14.2598 3.13044 14.6693 2.54941C14.9051 2.21739 15.1781 1.92095 15.4512 1.73123C15.8111 1.43478 15.9352 1.25692 15.9352 0.960475C15.9352 0.320158 15.2898 0.0237152 14.0736 0.0237152C12.845 0.0237152 12.2492 0.296443 12.2492 0.83004C12.2492 1.11462 12.4106 1.26877 12.5843 1.39921C12.7829 1.55336 12.9691 1.71937 12.9691 1.90909C12.9691 2.12253 12.8822 2.32411 12.6588 2.6087C12.3858 3.03557 12.1003 3.48617 11.7901 3.93676C11.4674 3.47431 11.1323 3 10.8344 2.53755C10.6483 2.3004 10.5366 2.14625 10.5366 1.89723C10.5366 1.69565 10.6607 1.5415 10.822 1.42293C11.033 1.28063 11.1571 1.12648 11.1571 0.865613C11.1571 0.343873 10.6855 0.0237152 9.09695 0.0237152C7.80625 0.0237152 7.08644 0.355731 7.08644 0.901186C7.08644 1.24506 7.18572 1.42292 7.5084 1.66008C7.7442 1.8498 7.89313 2.02767 8.33991 2.64427C8.87356 3.41502 9.82918 4.64822 10.3132 5.39526V6.16601C10.3132 6.67589 10.1643 7.06719 9.95329 7.28063C9.68025 7.55336 9.51892 7.7668 9.51892 8.0751C9.51892 8.6087 10.0278 9 11.4798 9Z",fill:"url(#paint1_linear_149_29_2)"}),Object(r.g)("path",{d:"M24.1 5.53755C23.9511 5.53755 23.827 5.63241 23.6656 5.85771C23.4919 6.05929 23.2685 6.23715 23.0203 6.23715C22.648 6.23715 22.4246 5.96443 21.8413 4.80237C22.8838 4.31621 23.7649 3.43874 23.7649 2.32411C23.7649 1.04348 22.6852 0.142293 20.5257 0.142293H17.7458C16.4923 0.142293 16.1076 0.474308 16.1076 0.98419C16.1076 1.2332 16.2317 1.44664 16.5171 1.73123C16.7529 1.96838 16.9018 2.28854 16.9018 3.10672V6.07115C16.9018 6.77075 16.7529 7.09091 16.5295 7.28063C16.2565 7.5415 16.1076 7.79051 16.1076 8.08696C16.1076 8.63241 16.666 9 18.0188 9C19.5453 9 20.0293 8.62055 20.0293 8.08696C20.0293 7.79051 19.868 7.56522 19.6074 7.30435C19.3964 7.07905 19.2102 6.78261 19.2102 6.29644V5.59684C19.2102 5.3004 19.3591 5.1581 19.5329 5.1581C19.7439 5.1581 19.8928 5.28854 20.1162 5.78656C21.1215 7.9921 21.9778 9 22.8341 9C24.0504 9 24.6088 7.67194 24.6088 6.50988C24.6088 5.95257 24.4227 5.53755 24.1 5.53755ZM19.5825 4.25692C19.3467 4.25692 19.2102 4.17391 19.2102 3.93676V1.77866C19.2102 1.50593 19.3219 1.37549 19.8556 1.37549C20.6995 1.37549 21.2828 1.74308 21.2828 2.49012C21.2828 3.5336 20.2899 4.25692 19.5825 4.25692Z",fill:"url(#paint2_linear_149_29_2)"}),Object(r.g)("path",{d:"M33.3505 7.29249C33.0154 7.06719 32.8789 6.85375 32.7176 6.34387L30.9677 0.98419C30.7567 0.320158 30.4465 0.142293 29.5777 0.142293H27.5548C26.5868 0.142293 26.2393 0.343873 26.2393 0.87747C26.2393 1.44664 26.5247 1.75494 27.108 1.86166L27.2197 1.88538L25.7056 6.37945C25.5691 6.78261 25.3457 7.09091 24.9361 7.42293C24.5886 7.6838 24.477 7.87352 24.477 8.13439C24.477 8.71542 25.0602 9 26.2641 9C27.5424 9 28.1008 8.71542 28.1008 8.16996C28.1008 7.88538 27.9395 7.71937 27.6665 7.55336C27.4679 7.42293 27.3314 7.29249 27.3314 7.10277C27.3314 6.99605 27.3562 6.88933 27.381 6.81818L27.5424 6.35573C27.6168 6.13044 27.7409 6.03557 28.0264 6.03557H29.7515C30.0493 6.03557 30.1982 6.16601 30.2851 6.40316L30.3844 6.72332C30.4216 6.81818 30.4465 6.94862 30.4465 7.05534C30.4465 7.25692 30.2975 7.43478 30.0865 7.56522C29.7763 7.7668 29.677 7.88538 29.677 8.20553C29.677 8.76285 30.2975 9 31.7868 9C33.2513 9 33.8966 8.71542 33.8966 8.06324C33.8966 7.7668 33.7725 7.57708 33.3505 7.29249ZM28.8455 1.95652L29.8135 4.92095H27.9519L28.8455 1.95652Z",fill:"url(#paint3_linear_149_29_2)"}),Object(r.g)("path",{d:"M35.7088 9C37.1857 9 37.6325 8.59684 37.6325 8.06324C37.6325 7.73123 37.4339 7.51779 37.1981 7.28063C36.9747 7.07905 36.8258 6.71146 36.8258 6.05929V3.16601L38.7618 6.80632C38.8735 7.00791 38.9976 7.09091 39.1466 7.09091C39.3451 7.09091 39.4444 6.98419 39.5437 6.80632L41.5418 3.14229V6.01186C41.5418 6.71146 41.3929 7.07905 41.1819 7.28063C40.9337 7.51779 40.7475 7.73123 40.7475 8.0751C40.7475 8.59684 41.2439 9 42.7208 9C44.0239 9 44.5576 8.59684 44.5576 8.0751C44.5576 7.74308 44.4211 7.56522 44.1356 7.28063C43.9246 7.09091 43.7633 6.74704 43.7633 6.01186V2.96443C43.7633 2.35968 43.8874 1.96838 44.1356 1.70751C44.4211 1.42293 44.5824 1.20949 44.5824 0.889328C44.5824 0.533597 44.297 0.142293 43.3413 0.142293H42.5595C41.7652 0.142293 41.3432 0.41502 40.9213 1.20949L39.4568 4.06719L38.0296 1.20949C37.6697 0.486166 37.1857 0.142293 36.379 0.142293H35.2745C34.3685 0.142293 34.021 0.498024 34.021 0.913044C34.021 1.22134 34.1575 1.42293 34.4553 1.70751C34.716 1.95652 34.8277 2.35968 34.8277 2.96443V6.05929C34.8277 6.74704 34.6663 7.07905 34.4678 7.28063C34.1823 7.56522 34.0334 7.74308 34.0334 8.06324C34.0334 8.59684 34.3933 9 35.7088 9Z",fill:"url(#paint4_linear_149_29_2)"}),Object(r.g)("path",{d:"M47.0653 9C48.5546 9 49.0262 8.59684 49.0262 8.0751C49.0262 7.77866 48.8896 7.57708 48.5918 7.28063C48.3932 7.09091 48.2195 6.77075 48.2195 5.98814V3.01186C48.2195 2.22925 48.3932 1.86166 48.5918 1.67194C48.8896 1.38735 49.0262 1.17391 49.0262 0.87747C49.0262 0.391304 48.5421 0.0237152 47.0653 0.0237152C45.6008 0.0237152 45.1168 0.391304 45.1168 0.87747C45.1168 1.17391 45.2533 1.38735 45.5388 1.67194C45.7498 1.87352 45.9111 2.22925 45.9111 3.01186V5.98814C45.9111 6.77075 45.7498 7.07905 45.5388 7.28063C45.2533 7.57708 45.1168 7.77866 45.1168 8.0751C45.1168 8.59684 45.6008 9 47.0653 9Z",fill:"url(#paint5_linear_149_29_2)"}),Object(r.g)("path",{d:"M53.1453 8.85771C56.4837 8.85771 58.0847 6.96047 58.0847 4.28063C58.0847 1.51779 56.2728 0.142293 53.3315 0.142293H50.8866C50.0302 0.142293 49.571 0.474309 49.571 0.960475C49.571 1.24506 49.7324 1.48221 49.993 1.75494C50.2164 1.96838 50.3653 2.32411 50.3653 3.03557V5.94071C50.3653 6.68775 50.2164 7.00791 49.9806 7.25692C49.7076 7.5415 49.571 7.80237 49.571 8.06324C49.571 8.62055 50.0426 8.85771 50.8617 8.85771H53.1453ZM53.5797 7.57708C52.9715 7.57708 52.6737 7.17391 52.6737 6.43874V2.53755C52.6737 1.79051 52.9095 1.41107 53.5921 1.41107C54.8207 1.41107 55.6026 2.6087 55.6026 4.26877C55.6026 6.35573 54.709 7.57708 53.5797 7.57708Z",fill:"url(#paint6_linear_149_29_2)"}),Object(r.g)("path",{d:"M63.0571 9C63.5163 9 63.7893 8.65613 64.2733 8.65613C64.7822 8.65613 65.3655 9 66.1349 9C67.9593 9 69.1755 7.57708 69.1755 6.15415C69.1755 4.89723 68.3936 4.1502 66.2342 3.3913C64.658 2.84585 64.1864 2.49012 64.1864 1.8498C64.1864 1.29249 64.5836 0.901186 65.2662 0.901186C66.0729 0.901186 66.5072 1.47036 67.0533 2.25296C67.438 2.81028 67.7607 3.2253 68.2075 3.2253C68.6294 3.2253 68.8652 2.78656 68.8652 2.11067C68.8652 1.03162 68.3936 0 67.7359 0C67.2891 0 66.954 0.355732 66.6686 0.711463C66.4079 0.41502 65.7998 0 64.6829 0C62.8213 0 61.7912 1.17391 61.7912 2.47826C61.7912 3.74704 62.5607 4.49407 64.6829 5.21739C66.1845 5.71542 66.6437 6.05929 66.6437 6.65217C66.6437 7.38735 66.0977 7.8498 65.3034 7.8498C64.3354 7.8498 63.6652 7.32806 62.9826 6C62.7096 5.47826 62.4738 5.03953 62.0642 5.03953C61.605 5.03953 61.3692 5.64427 61.3692 6.45059C61.3692 7.62451 62.0146 9 63.0571 9Z",fill:"url(#paint7_linear_149_29_2)"}),Object(r.g)("path",{d:"M73.7122 9C75.7227 9 77.2988 7.62451 77.2988 6.32016C77.2988 5.89328 77.0506 5.51383 76.7776 5.51383C76.6659 5.51383 76.5542 5.6087 76.4549 5.83399C75.9709 6.78261 75.3255 7.28063 74.4816 7.28063C73.2033 7.28063 72.2477 5.73913 72.2477 3.55731C72.2477 2.0751 72.8558 1.1502 73.6749 1.1502C74.283 1.1502 74.705 1.51779 75.2635 2.50198C75.7847 3.45059 76.0702 3.73518 76.4549 3.73518C76.9389 3.73518 77.274 3.17787 77.274 2.25296C77.274 0.98419 76.7652 0 76.0826 0C75.7227 0 75.3628 0.26087 74.916 0.901186C74.5188 0.308301 73.9107 0 73.1288 0C71.2052 0 69.7532 1.86166 69.7532 4.37549C69.7532 7.11462 71.3665 9 73.7122 9Z",fill:"url(#paint8_linear_149_29_2)"}),Object(r.g)("path",{d:"M85.9628 3.01186C85.9628 2.22925 86.1241 1.86166 86.3351 1.67194C86.6205 1.38735 86.7694 1.17391 86.7694 0.87747C86.7694 0.391304 86.273 0.0237152 84.8086 0.0237152C83.3317 0.0237152 82.8477 0.391304 82.8477 0.87747C82.8477 1.17391 82.9842 1.38735 83.2821 1.67194C83.4806 1.87352 83.6544 2.22925 83.6544 3.01186V3.78261H80.8248V3.01186C80.8248 2.22925 80.9985 1.86166 81.1971 1.67194C81.4949 1.38735 81.6315 1.17391 81.6315 0.87747C81.6315 0.391304 81.1474 0.0237152 79.6706 0.0237152C78.2061 0.0237152 77.7221 0.391304 77.7221 0.87747C77.7221 1.17391 77.8586 1.38735 78.1441 1.67194C78.355 1.87352 78.5164 2.22925 78.5164 3.01186V5.98814C78.5164 6.77075 78.355 7.07905 78.1441 7.28063C77.8586 7.57708 77.7221 7.77866 77.7221 8.0751C77.7221 8.59684 78.2061 9 79.6706 9C81.1474 9 81.6315 8.59684 81.6315 8.0751C81.6315 7.77866 81.4949 7.57708 81.1971 7.28063C80.9985 7.09091 80.8248 6.77075 80.8248 5.98814V4.99209H83.6544V5.98814C83.6544 6.77075 83.4806 7.07905 83.2821 7.28063C82.9842 7.57708 82.8477 7.77866 82.8477 8.0751C82.8477 8.59684 83.3317 9 84.8086 9C86.273 9 86.7694 8.59684 86.7694 8.0751C86.7694 7.77866 86.6205 7.57708 86.3351 7.28063C86.1241 7.09091 85.9628 6.77075 85.9628 5.98814V3.01186Z",fill:"url(#paint9_linear_149_29_2)"}),Object(r.g)("path",{d:"M93.5083 8.85771C94.3274 8.85771 94.9231 7.8498 94.9231 6.48617C94.9231 5.62055 94.6749 5.03953 94.2902 5.03953C94.0792 5.03953 93.9054 5.21739 93.5083 5.88142C92.9374 6.98419 92.4658 7.42293 91.7956 7.42293H91.3613C90.6911 7.42293 90.4056 7.03162 90.4056 6.37945V5.76285C90.4056 5.27668 90.629 4.95652 90.9517 4.95652H91.3488C91.6095 4.95652 91.7708 5.1581 91.8825 5.47826C91.9818 5.70356 92.0687 5.86957 92.2796 5.86957C92.6395 5.86957 92.925 5.20553 92.925 4.33992C92.925 3.5336 92.6768 2.97629 92.3169 2.97629C92.0562 2.97629 91.9445 3.17787 91.8328 3.3913C91.7212 3.65217 91.5846 3.80632 91.3488 3.80632H90.9641C90.6166 3.80632 90.4056 3.58103 90.4056 3.02372V2.62055C90.4056 1.95652 90.629 1.57708 91.2371 1.57708H91.6839C92.4782 1.57708 93.0243 2.31225 93.471 3.15415C93.6572 3.50988 93.7689 3.6166 93.9178 3.6166C94.2405 3.6166 94.5135 2.917 94.5135 2.06324C94.5135 1.09091 94.0668 0.142293 92.9746 0.142293H88.5813C87.7746 0.142293 87.303 0.474309 87.303 0.960475C87.303 1.2332 87.4395 1.4585 87.7249 1.75494C87.9235 1.95652 88.0973 2.25296 88.0973 3.10672V5.89328C88.0973 6.74704 87.9111 7.05534 87.7125 7.25692C87.4519 7.51779 87.303 7.79051 87.303 8.06324C87.303 8.52569 87.6877 8.85771 88.482 8.85771H93.5083Z",fill:"url(#paint10_linear_149_29_2)"}),Object(r.g)("path",{d:"M96.9719 9C98.4488 9 98.8955 8.59684 98.8955 8.06324C98.8955 7.73123 98.697 7.51779 98.4612 7.28063C98.2378 7.07905 98.0888 6.71146 98.0888 6.05929V3.16601L100.025 6.80632C100.137 7.00791 100.261 7.09091 100.41 7.09091C100.608 7.09091 100.707 6.98419 100.807 6.80632L102.805 3.14229V6.01186C102.805 6.71146 102.656 7.07905 102.445 7.28063C102.197 7.51779 102.011 7.73123 102.011 8.0751C102.011 8.59684 102.507 9 103.984 9C105.287 9 105.821 8.59684 105.821 8.0751C105.821 7.74308 105.684 7.56522 105.399 7.28063C105.188 7.09091 105.026 6.74704 105.026 6.01186V2.96443C105.026 2.35968 105.15 1.96838 105.399 1.70751C105.684 1.42293 105.845 1.20949 105.845 0.889328C105.845 0.533597 105.56 0.142293 104.604 0.142293H103.823C103.028 0.142293 102.606 0.41502 102.184 1.20949L100.72 4.06719L99.2927 1.20949C98.9328 0.486166 98.4488 0.142293 97.6421 0.142293H96.5375C95.6315 0.142293 95.284 0.498024 95.284 0.913044C95.284 1.22134 95.4206 1.42293 95.7184 1.70751C95.979 1.95652 96.0907 2.35968 96.0907 2.96443V6.05929C96.0907 6.74704 95.9294 7.07905 95.7308 7.28063C95.4454 7.56522 95.2965 7.74308 95.2965 8.06324C95.2965 8.59684 95.6564 9 96.9719 9Z",fill:"url(#paint11_linear_149_29_2)"}),Object(r.g)("path",{d:"M112.585 8.85771C113.404 8.85771 114 7.8498 114 6.48617C114 5.62055 113.752 5.03953 113.367 5.03953C113.156 5.03953 112.982 5.21739 112.585 5.88142C112.014 6.98419 111.543 7.42293 110.873 7.42293H110.438C109.768 7.42293 109.483 7.03162 109.483 6.37945V5.76285C109.483 5.27668 109.706 4.95652 110.029 4.95652H110.426C110.686 4.95652 110.848 5.1581 110.959 5.47826C111.059 5.70356 111.146 5.86957 111.357 5.86957C111.716 5.86957 112.002 5.20553 112.002 4.33992C112.002 3.5336 111.754 2.97629 111.394 2.97629C111.133 2.97629 111.021 3.17787 110.91 3.3913C110.798 3.65217 110.662 3.80632 110.426 3.80632H110.041C109.694 3.80632 109.483 3.58103 109.483 3.02372V2.62055C109.483 1.95652 109.706 1.57708 110.314 1.57708H110.761C111.555 1.57708 112.101 2.31225 112.548 3.15415C112.734 3.50988 112.846 3.6166 112.995 3.6166C113.317 3.6166 113.59 2.917 113.59 2.06324C113.59 1.09091 113.144 0.142293 112.052 0.142293H107.658C106.851 0.142293 106.38 0.474309 106.38 0.960475C106.38 1.2332 106.516 1.4585 106.802 1.75494C107 1.95652 107.174 2.25296 107.174 3.10672V5.89328C107.174 6.74704 106.988 7.05534 106.789 7.25692C106.529 7.51779 106.38 7.79051 106.38 8.06324C106.38 8.52569 106.765 8.85771 107.559 8.85771H112.585Z",fill:"url(#paint12_linear_149_29_2)"}),Object(r.g)("defs",null,Object(r.g)("linearGradient",{id:"paint0_linear_149_29_2",x1:"115.793",y1:"4.5",x2:"0",y2:"4.5",gradientUnits:"userSpaceOnUse"},Object(r.g)("stop",{stopColor:"#EE448B"}),Object(r.g)("stop",{offset:"1",stopColor:"#8541DB"})),Object(r.g)("linearGradient",{id:"paint1_linear_149_29_2",x1:"115.793",y1:"4.5",x2:"0",y2:"4.5",gradientUnits:"userSpaceOnUse"},Object(r.g)("stop",{stopColor:"#EE448B"}),Object(r.g)("stop",{offset:"1",stopColor:"#8541DB"})),Object(r.g)("linearGradient",{id:"paint2_linear_149_29_2",x1:"115.793",y1:"4.5",x2:"0",y2:"4.5",gradientUnits:"userSpaceOnUse"},Object(r.g)("stop",{stopColor:"#EE448B"}),Object(r.g)("stop",{offset:"1",stopColor:"#8541DB"})),Object(r.g)("linearGradient",{id:"paint3_linear_149_29_2",x1:"115.793",y1:"4.5",x2:"0",y2:"4.5",gradientUnits:"userSpaceOnUse"},Object(r.g)("stop",{stopColor:"#EE448B"}),Object(r.g)("stop",{offset:"1",stopColor:"#8541DB"})),Object(r.g)("linearGradient",{id:"paint4_linear_149_29_2",x1:"115.793",y1:"4.5",x2:"0",y2:"4.5",gradientUnits:"userSpaceOnUse"},Object(r.g)("stop",{stopColor:"#EE448B"}),Object(r.g)("stop",{offset:"1",stopColor:"#8541DB"})),Object(r.g)("linearGradient",{id:"paint5_linear_149_29_2",x1:"115.793",y1:"4.5",x2:"0",y2:"4.5",gradientUnits:"userSpaceOnUse"},Object(r.g)("stop",{stopColor:"#EE448B"}),Object(r.g)("stop",{offset:"1",stopColor:"#8541DB"})),Object(r.g)("linearGradient",{id:"paint6_linear_149_29_2",x1:"115.793",y1:"4.5",x2:"0",y2:"4.5",gradientUnits:"userSpaceOnUse"},Object(r.g)("stop",{stopColor:"#EE448B"}),Object(r.g)("stop",{offset:"1",stopColor:"#8541DB"})),Object(r.g)("linearGradient",{id:"paint7_linear_149_29_2",x1:"115.793",y1:"4.5",x2:"0",y2:"4.5",gradientUnits:"userSpaceOnUse"},Object(r.g)("stop",{stopColor:"#EE448B"}),Object(r.g)("stop",{offset:"1",stopColor:"#8541DB"})),Object(r.g)("linearGradient",{id:"paint8_linear_149_29_2",x1:"115.793",y1:"4.5",x2:"0",y2:"4.5",gradientUnits:"userSpaceOnUse"},Object(r.g)("stop",{stopColor:"#EE448B"}),Object(r.g)("stop",{offset:"1",stopColor:"#8541DB"})),Object(r.g)("linearGradient",{id:"paint9_linear_149_29_2",x1:"115.793",y1:"4.5",x2:"0",y2:"4.5",gradientUnits:"userSpaceOnUse"},Object(r.g)("stop",{stopColor:"#EE448B"}),Object(r.g)("stop",{offset:"1",stopColor:"#8541DB"})),Object(r.g)("linearGradient",{id:"paint10_linear_149_29_2",x1:"115.793",y1:"4.5",x2:"0",y2:"4.5",gradientUnits:"userSpaceOnUse"},Object(r.g)("stop",{stopColor:"#EE448B"}),Object(r.g)("stop",{offset:"1",stopColor:"#8541DB"})),Object(r.g)("linearGradient",{id:"paint11_linear_149_29_2",x1:"115.793",y1:"4.5",x2:"0",y2:"4.5",gradientUnits:"userSpaceOnUse"},Object(r.g)("stop",{stopColor:"#EE448B"}),Object(r.g)("stop",{offset:"1",stopColor:"#8541DB"})),Object(r.g)("linearGradient",{id:"paint12_linear_149_29_2",x1:"115.793",y1:"4.5",x2:"0",y2:"4.5",gradientUnits:"userSpaceOnUse"},Object(r.g)("stop",{stopColor:"#EE448B"}),Object(r.g)("stop",{offset:"1",stopColor:"#8541DB"})))))};function xo(e){return(xo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function No(){return(No=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Eo(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function Po(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function To(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Io(e,t,n){return t&&To(e.prototype,t),n&&To(e,n),e}function zo(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Lo(e,t)}function Lo(e,t){return(Lo=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Mo(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Ro(e);if(t){var o=Ro(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Ao(this,n)}}function Ao(e,t){return!t||"object"!==xo(t)&&"function"!=typeof t?Do(e):t}function Do(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ro(e){return(Ro=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}"undefined"!=typeof window&&(jo=n(36));var Bo=function(e){return"js-destination-item-".concat(e,"-more")},Fo=En(function(e){zo(n,e);var t=Mo(n);function n(){return Po(this,n),t.apply(this,arguments)}return Io(n,[{key:"render",value:function(e){var t=e.children,n=e.theme,o=Eo(e,["children","theme"]);return Object(r.g)(Fr,No({},o,{className:"".concat(rn.a.menuToggle," ").concat(n.menuToggle),onClick:this.props.onClick,location:"hamburger"}),Object(r.g)("i",null,t))}}]),n}(r.a)),Uo=function(e){zo(n,e);var t=Mo(n);function n(){var e;return Po(this,n),(e=t.call(this)).onKeyDown=e.onKeyDown.bind(Do(e)),e}return Io(n,[{key:"onKeyDown",value:function(e){27===e.keyCode&&this.props.onHide(e)}},{key:"componentDidMount",value:function(){document.body.classList.add(rn.a.bodyWithMoreNav),window.addEventListener("keydown",this.onKeyDown,!0)}},{key:"componentWillUnmount",value:function(){document.body.classList.remove(rn.a.bodyWithMoreNav),window.removeEventListener("keydown",this.onKeyDown,!0)}},{key:"render",value:function(e){var t=e.top,n=e.onHide,o=parseInt(t,10);return Object(r.g)("div",{className:rn.a.pageOverlay,style:"top:".concat(o,"px"),onMouseDown:n,role:"presentation"})}}]),n}(r.a),Ho=En((function(e){var t=e.name,n=e.logo,o=e.url,i=e.label,a=e.nav_label,c=e.nav_label_type,s=e.theme;return["Kids","Sex Toys","Stores","Subscriptions"].includes(t)?"":Object(r.g)("li",null,Object(r.g)(Er,{href:o,className:"".concat(rn.a.link," ").concat(s.link),label:i||t,location:"moreMenu"},n?"PyramidScheme"===n?Object(r.g)(So,{width:114,height:9}):t:t,Object(r.g)(Jr,{navLabelType:c},a)))})),Wo=En((function(e){var t=e.name,n=e.url,o=e.label,i=e.theme;return Object(r.g)("li",null,Object(r.g)(Er,{href:n,className:"".concat(rn.a.link," ").concat(i.link),label:o||t,location:"moreMenu",isInfoPage:!0},t))})),Vo=En((function(e){var t=e.name,n=e.url,o=e.label,i=e.theme,a=e.subSections,c=void 0===a?[]:a,s=c.length>0;return Object(r.g)("nav",{className:"".concat(rn.a.section," ").concat(rn.a.sectionLinksSection),"aria-label":t},Object(r.g)("div",{className:"".concat(rn.a.sectionTitle," ").concat(i.text)},n?Object(r.g)(Er,{href:n,className:"".concat(rn.a.link," ").concat(i.link),label:o||t,location:"moreMenu"},t):t),s&&Object(r.g)("ul",{className:rn.a.sectionItems},c.map(Ho)))})),qo=En((function(e){var t=e.name,n=e.url,o=e.description,i=e.label,a=void 0===i?"":i,c=e.theme;return Object(r.g)(Er,{href:n,className:"".concat(rn.a.headerLink," ").concat(rn.a.link," ").concat(c.link),label:o,location:"moreMenu",trackingData:{unit_type:"nav",unit_name:"hidden",item_type:"text",item_name:a,target_content_type:"feed",target_content_id:a}},Object(r.g)("div",{className:"".concat(rn.a.headerSection)},"newsletters"===a&&Object(r.g)("div",null,Object(r.g)(hr,{className:rn.a.newsletterIcon,"aria-hidden":!0,height:33,width:28})),Object(r.g)("div",{className:"".concat(rn.a.headerCta)},Object(r.g)("div",{className:"".concat(rn.a.headerCtaTitle)},t),Object(r.g)("p",null,o)),Object(r.g)("div",null,Object(r.g)(br,{className:rn.a.rightCaret,"aria-hidden":!0}))))})),Go=function(e){var t=e.navItems,n=t.sections,o=void 0===n?[]:n,i=t.about,a=void 0===i?[]:i,c=t.header,s=void 0===c?[]:c,u=t.footer,l=void 0===u?[]:u,f=t.community,p=void 0===f?[]:f,d=t.destinations,h=void 0===d?[]:d,g=t.fromPartners,m=void 0===g?[]:g,b=t.giftGuides,y=void 0===b?[]:b,_=e.theme,v=h.length>0,w=s.length>0,O=o.length>0,k=p.length>0,j=m.length>0,C=y.length>0;return Object(r.g)("div",{className:rn.a.moreNavInner},Object(r.g)(cn.Consumer,null,(function(e){return Object(r.g)("div",{className:rn.a.moreNavLabel,id:"nav-menu-title"}," ",e.browse_links," ")})),Object(r.g)("div",{className:rn.a.sectionsSection},w&&Object(r.g)("div",{className:rn.a.headerSectionContainer},s.map(qo)),O&&o.map(Vo),k&&Object(r.g)(cn.Consumer,null,(function(e){return Object(r.g)("nav",{className:"".concat(rn.a.section," ").concat(rn.a.sectionLinksSection),"aria-label":e.community},Object(r.g)("div",{className:"".concat(rn.a.sectionTitle," ").concat(_.text)},Object(r.g)(Er,{href:"/community",className:"".concat(rn.a.link," ").concat(_.link),label:e.community,location:"moreMenu",isInfoPage:!0},e.community)),Object(r.g)("ul",{className:rn.a.sectionItems},p.map(Wo)))})),C&&Object(r.g)(cn.Consumer,null,(function(e){return Object(r.g)("nav",{className:"".concat(rn.a.section," ").concat(rn.a.sectionLinksSection),"aria-label":e.gift_guides},Object(r.g)("div",{className:"".concat(rn.a.sectionTitle," ").concat(_.text)},e.gift_guides),Object(r.g)("ul",{className:rn.a.sectionItems},y.map(Wo)))})),j&&Object(r.g)(cn.Consumer,null,(function(e){return Object(r.g)("nav",{className:"".concat(rn.a.section," ").concat(rn.a.sectionLinksSection),"aria-label":e.from_partners},Object(r.g)("div",{className:"".concat(rn.a.sectionTitle," ").concat(_.text)},e.from_partners),Object(r.g)("ul",{className:rn.a.sectionItems},m.map(Wo)))})),Object(r.g)(cn.Consumer,null,(function(e){return Object(r.g)("nav",{className:"".concat(rn.a.section," ").concat(rn.a.sectionLinksSection),"aria-label":e.about},Object(r.g)("div",{className:"".concat(rn.a.sectionTitle," ").concat(_.text)},e.about),Object(r.g)("ul",{className:rn.a.sectionItems},a.map(Wo)))})),Object(r.g)("div",{className:"".concat(rn.a.section," ").concat(rn.a.footerSectionContainer)},Object(r.g)("div",{className:"".concat(rn.a.footerSection)},Object(r.g)("div",{className:rn.a.footerSubSection},Object(r.g)(Rn,null)),Object(r.g)("div",{id:"CCPAModule",className:"".concat(rn.a.CCPAConsentModule)},Object(r.g)("div",{className:"".concat(rn.a.ccpaCopy," ").concat(_.text)},'US residents can opt out of "sales" of personal data.'),Object(r.g)(Fr,{className:"ot-sdk-show-settings ".concat(rn.a.smallSecondaryButton),onClick:function(){_o({location:"usprivacy",action_value:"ccpa_module"})},id:"ot-sdk-btn"},"Do Not Sell or Share My Personal Information")),Object(r.g)("div",{id:"GDPRModule",className:"".concat(rn.a.GDPRConsentModule)},Object(r.g)(Fr,{className:"ot-gdpr-btn ".concat(rn.a.smallSecondaryButton),onClick:function(){window.OneTrust.ToggleInfoDisplay()}},"Privacy Settings")),Object(r.g)("div",{id:"IDNMLModule",className:"".concat(rn.a.IDNMLConsentModule)},Object(r.g)(Fr,{className:"idnml-sp-btn ".concat(rn.a.smallSecondaryButton),onClick:function(){window._sp_.gdpr.loadPrivacyManagerModal(1161871,"purposes")}},"Privacy Settings"))),Object(r.g)("div",{className:"".concat(rn.a.footerNav)},Object(r.g)("ul",null,Object(r.g)("li",{className:"".concat(rn.a.copyright," ").concat(_.text)},"  Â© ",(new Date).getFullYear()," BuzzFeed, Inc "),l.map(Wo))))),v&&Object(r.g)(cn.Consumer,null,(function(e){return Object(r.g)("nav",{className:"".concat(rn.a.section," ").concat(rn.a.destinationsSection),"aria-label":e.browse_brands},Object(r.g)("ul",{className:rn.a.sectionItems},h.map((function(e){var t=e.url,n=e.name,o=e.description,i=e.label;return Object(r.g)("li",{className:rn.a.destinationItem},Object(r.g)(Er,{className:rn.a.destinationLink,href:t,location:"moreMenu",label:i||n},Object(r.g)("div",null,Object(r.g)("i",{className:rn.a["".concat(n,"Logo")]},Object(r.g)(ur,{destinationName:n,role:"img","aria-labelledby":Bo(n)},Object(r.g)("title",{id:Bo(n)},Qr[n].displayName)))),Object(r.g)("span",null,o)))}))))})))},Ko=En(function(e){zo(n,e);var t=Mo(n);function n(){return Po(this,n),t.apply(this,arguments)}return Io(n,[{key:"render",value:function(e){var t=this,n=e.isVisible,o=Eo(e,["isVisible"]),i=o.navItems&&o.navItems.destinations&&o.navItems.destinations.length>0;return Object(r.g)("section",{className:"".concat(rn.a.moreNav," ").concat(o.theme.moreNav," ").concat(n?rn.a.visible:""," ").concat(i?rn.a.withDestinations:""),id:"js-more-nav",ref:function(e){return t.container=e},"aria-labelledby":"nav-menu-title",role:"dialog","aria-modal":"true"},n?Object(r.g)(jo,{focusTrapOptions:{clickOutsideDeactivates:!0}},Object(r.g)(Go,o)):Object(r.g)(Go,o))}}]),n}(r.a)),Zo=n(26),Xo=n.n(Zo);function Yo(e){return(Yo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function $o(){return($o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Jo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Qo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Jo(Object(n),!0).forEach((function(t){ei(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Jo(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ei(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ti(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ni(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function ri(e,t,n){return t&&ni(e.prototype,t),n&&ni(e,n),e}function oi(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&ii(e,t)}function ii(e,t){return(ii=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function ai(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=ui(e);if(t){var o=ui(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return ci(this,n)}}function ci(e,t){return!t||"object"!==Yo(t)&&"function"!=typeof t?si(e):t}function si(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ui(e){return(ui=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function li(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var fi=function(e){var t=e.children,n=li(e,["children"]);return Object(r.g)("div",n,t)},pi=function(e){oi(n,e);var t=ai(n);function n(){return ti(this,n),t.apply(this,arguments)}return ri(n,[{key:"shouldComponentUpdate",value:function(){return!1}},{key:"render",value:function(){return Object(r.g)("div",{className:"js-main-nav-flyout"})}}]),n}(r.a),di=function(e){oi(n,e);var t=ai(n);function n(){var e;return ti(this,n),(e=t.call(this)).state={hovered:!1,enabled:!0,showFallback:!0},e.onMouseEnter=e.onMouseEnter.bind(si(e)),e.onMouseLeave=e.onMouseLeave.bind(si(e)),e.onTouchStart=e.onTouchStart.bind(si(e)),e}return ri(n,[{key:"componentDidMount",value:function(){this.setState({showFallback:!1})}},{key:"getOffset",value:function(){var e=this.toggle.getBoundingClientRect(),t=e.width,n=(e.x||e.left)+t/2-this.props.width/2,r=!0;return n+this.props.width>document.body.clientWidth&&(r=!1,n=document.body.clientWidth-this.props.width),n<0&&(r=!1,n=0),{leftOffset:n,isCentered:r}}},{key:"onMouseEnter",value:function(){this.toggleFlyout({forceVisible:!0})}},{key:"onMouseLeave",value:function(){this.toggleFlyout({forceVisible:!1})}},{key:"onTouchStart",value:function(e){this.props.enabled()&&(e.preventDefault(),this.toggleFlyout({delay:0}))}},{key:"toggleFlyout",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.forceVisible,r=void 0===n?!this.state.hovered:n,o=t.delay,i=void 0===o?300:o;if(r){clearTimeout(this.willHideFlyout);var a=this.getOffset(),c=a.leftOffset,s=a.isCentered;this.setState(Qo(Qo({},this.state),{},{hovered:!0,leftOffset:c,enabled:this.props.enabled(),showArrow:s}))}else this.willHideFlyout=setTimeout((function(){return e.setState(Qo(Qo({},e.state),{},{hovered:!1}))}),i)}},{key:"render",value:function(e,t){var n=this,o=e.children,i=e.width,a=void 0===i?200:i,c=li(e,["children","width"]),s=o.filter((function(e){return e.nodeName===fi})),u=o.filter((function(e){return e.nodeName!==fi})),l={width:"".concat(a,"px")};t.leftOffset&&(l.left=t.leftOffset);var f=Object(r.g)("div",{className:Xo.a.flyoutInner,style:l,onMouseEnter:this.onMouseEnter,onMouseLeave:this.onMouseLeave},t.showArrow&&Object(r.g)("span",{className:Xo.a.arrow}),u);return Object(r.g)("div",$o({className:Xo.a.flyout,onTouchStart:this.onTouchStart,onMouseEnter:this.onMouseEnter,onMouseLeave:this.onMouseLeave},c),Object(r.g)("div",{ref:function(e){return n.toggle=e}},s),t.showFallback&&Object(r.g)("div",{style:"display: none"},f),t.enabled&&t.hovered&&Object(Ke.createPortal)(Object(r.g)("div",{className:"js-main-nav-flyout"},f),document.body))}}]),n}(r.a);ei(di,"defaultProps",{enabled:function(){return!0}});var hi=n(6),gi=n.n(hi),mi=n(19),bi=n.n(mi),yi=Math.floor(6*Math.random()),_i=function(e){var t=e.title,n=e.items,o=e.cta,i=e.location;return Object(r.g)("div",{className:bi.a.container},Object(r.g)("div",{className:bi.a.title},t),Object(r.g)("div",{className:bi.a.items},n.map((function(e){var t=Array.isArray(e.img)?e.img[yi]:e.img;return Object(r.g)(Er,{href:e.url,location:i,label:e.label,className:bi.a.item},Object(r.g)("figure",null,Object(r.g)(sn.Consumer,null,(function(n){return Object(r.g)("img",{alt:e.title,src:"".concat(n.image_service_url,"/").concat(t,"?output-format=auto&output-quality=auto&downsize=*:80")})})),Object(r.g)("figcaption",null,e.title)))}))),o?Object(r.g)(Er,{className:bi.a.cta,href:o.url,location:i,label:o.label},o.title,Object(r.g)(br,{width:16,height:16,"aria-hidden":!0})):"")},vi=n(17);function wi(){return(wi=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Oi="arcade",ki=13,ji=15,Ci=function(e){return Object(r.g)(Fn,wi({id:Oi,viewBox:"0 0 ".concat(ki," ").concat(ji)},e),Object(r.g)("path",{d:"M0.115133 8.18548L3.35639 9.99933C3.40739 10.0278 3.4449 10.0717 3.4614 10.1232L4.76398 14.0924C4.83299 14.3025 5.16701 14.3025 5.23602 14.0924L6.5386 10.1232C6.5556 10.0717 6.59311 10.0278 6.64361 9.99933L9.88487 8.18548C10.0384 8.0996 10.0384 7.9004 9.88487 7.81452L6.64361 6.00067C6.59261 5.97221 6.5551 5.92827 6.5386 5.87685L5.23602 1.90764C5.16701 1.69745 4.83299 1.69745 4.76398 1.90764L3.4614 5.87685C3.4444 5.92827 3.40689 5.97221 3.35639 6.00067L0.115133 7.81452C-0.0383778 7.9004 -0.0383778 8.0996 0.115133 8.18548Z",fill:"#481F6B"}),Object(r.g)("path",{d:"M9.04605 2.57419L10.3426 3.29973C10.363 3.31112 10.378 3.32869 10.3846 3.34926L10.9056 4.93694C10.9332 5.02102 11.0668 5.02102 11.0944 4.93694L11.6154 3.34926C11.6222 3.32869 11.6372 3.31112 11.6574 3.29973L12.9539 2.57419C13.0154 2.53984 13.0154 2.46016 12.9539 2.42581L11.6574 1.70027C11.637 1.68888 11.622 1.67131 11.6154 1.65074L11.0944 0.0630579C11.0668 -0.0210193 10.9332 -0.0210193 10.9056 0.0630579L10.3846 1.65074C10.3778 1.67131 10.3628 1.68888 10.3426 1.70027L9.04605 2.42581C8.98465 2.46016 8.98465 2.53984 9.04605 2.57419Z",fill:"#481F6B"}))},Si=n(12),xi=(Si.domToReact,Si.htmlToDOM,Si.attributesToProps,Si.Comment,Si.Element,Si.ProcessingInstruction,Si.Text,Si);function Ni(){return(Ni=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Ei={width:1024,height:1024,id:"cute-badge"},Pi=function(e){var t=Ni({},e);return Object(r.g)(Fn,Ni({id:Ei.id,viewBox:"0 0 ".concat(Ei.width," ").concat(Ei.height)},t))};function Ti(){return(Ti=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Ii={width:1024,height:1024,id:"lol-badge"},zi=function(e){var t=Ti({},e);return Object(r.g)(Fn,Ti({id:Ii.id,viewBox:"0 0 ".concat(Ii.width," ").concat(Ii.height)},t))};function Li(){return(Li=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Mi={width:512,height:512,id:"trending-badge"},Ai=function(e){var t=Li({},e);return Object(r.g)(Fn,Li({id:Mi.id,viewBox:"0 0 ".concat(Mi.width," ").concat(Mi.height)},t))};function Di(){return(Di=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Ri={width:1024,height:1024,id:"omg-badge"},Bi=function(e){var t=Di({},e);return Object(r.g)(Fn,Di({id:Ri.id,viewBox:"0 0 ".concat(Ri.width," ").concat(Ri.height)},t))};function Fi(){return(Fi=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Ui={width:200,height:200,id:"wtf-badge"},Hi=function(e){var t=Fi({},e);return Object(r.g)(Fn,Fi({id:Ui.id,viewBox:"0 0 ".concat(Ui.width," ").concat(Ui.height)},t))};function Wi(){return(Wi=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Vi={width:1024,height:1024,id:"meudeus-badge"},qi=function(e){var t=Wi({},e);return Object(r.g)(Fn,Wi({id:Vi.id,viewBox:"0 0 ".concat(Vi.width," ").concat(Vi.height)},t))};function Gi(){return(Gi=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Ki={width:1024,height:1024,id:"hahaha-badge"},Zi=function(e){var t=Gi({},e);return Object(r.g)(Fn,Gi({id:Ki.id,viewBox:"0 0 ".concat(Ki.width," ").concat(Ki.height)},t))};function Xi(){return(Xi=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Yi={width:1024,height:1024,id:"fofo-badge"},$i=function(e){var t=Xi({},e);return Object(r.g)(Fn,Xi({id:Yi.id,viewBox:"0 0 ".concat(Yi.width," ").concat(Yi.height)},t))};function Ji(){return(Ji=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Qi={width:1024,height:1024,id:"jajaja-badge"},ea=function(e){var t=Ji({},e);return Object(r.g)(Fn,Ji({id:Qi.id,viewBox:"0 0 ".concat(Qi.width," ").concat(Qi.height)},t))};function ta(){return(ta=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var na={width:200,height:200,id:"kawaii-badge"},ra=function(e){var t=ta({},e);return Object(r.g)(Fn,ta({id:na.id,viewBox:"0 0 ".concat(na.width," ").concat(na.height)},t))};function oa(){return(oa=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var ia={width:512,height:512,id:"madremia-badge"},aa=function(e){var t=oa({},e);return Object(r.g)(Fn,oa({id:ia.id,viewBox:"0 0 ".concat(ia.width," ").concat(ia.height)},t))};function ca(){return(ca=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var sa={width:1024,height:1024,id:"lindo-badge"},ua=function(e){var t=ca({},e);return Object(r.g)(Fn,ca({id:sa.id,viewBox:"0 0 ".concat(sa.width," ").concat(sa.height)},t))};function la(){return(la=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var fa={width:512,height:512,id:"oooh-badge"},pa=function(e){var t=la({},e);return Object(r.g)(Fn,la({id:fa.id,viewBox:"0 0 ".concat(fa.width," ").concat(fa.height)},t))};function da(){return(da=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var ha={width:200,height:200,id:"win-badge"},ga=function(e){var t=da({},e);return Object(r.g)(Fn,da({id:ha.id,viewBox:"0 0 ".concat(ha.width," ").concat(ha.height)},t))};function ma(){return(ma=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var ba={width:200,height:200,id:"adobe-badge"},ya=function(e){var t=ma({},e);return Object(r.g)(Fn,ma({id:ba.id,viewBox:"0 0 ".concat(ba.width," ").concat(ba.height)},t))};function _a(){return(_a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var va={width:500,height:500,id:"omshiro-badge",fill:"none"},wa=function(e){var t=_a({},e);return Object(r.g)(Fn,_a({id:va.id,viewBox:"0 0 ".concat(va.width," ").concat(va.height)},t))};function Oa(){return(Oa=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var ka={width:500,height:500,id:"kando-badge",fill:"none"},ja=function(e){var t=Oa({},e);return Object(r.g)(Fn,Oa({id:ka.id,viewBox:"0 0 ".concat(ka.width," ").concat(ka.height)},t))};function Ca(){return(Ca=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Sa={width:500,height:500,id:"oishii-badge",fill:"none"},xa=function(e){var t=Ca({},e);return Object(r.g)(Fn,Ca({id:Sa.id,viewBox:"0 0 ".concat(Sa.width," ").concat(Sa.height)},t))};function Na(){return(Na=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Ea={width:500,height:500,id:"doubutsu-badge",fill:"none"},Pa=function(e){var t=Na({},e);return Object(r.g)(Fn,Na({id:Ea.id,viewBox:"0 0 ".concat(Ea.width," ").concat(Ea.height)},t))};function Ta(){return(Ta=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Ia={width:500,height:500,id:"trending-jp-badge",fill:"none"},za=function(e){var t=Ta({},e);return Object(r.g)(Fn,Ta({id:Ia.id,viewBox:"0 0 ".concat(Ia.width," ").concat(Ia.height)},t))};function La(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var Ma=function(e){var t=e.badgeName,n=La(e,["badgeName"]);switch(t){case"adobe":return Object(r.g)(ya,n);case"cute":return Object(r.g)(Pi,n);case"lol":return Object(r.g)(zi,n);case"omg":return Object(r.g)(Bi,n);case"wtf":return Object(r.g)(Hi,n);case"trending":return Object(r.g)(Ai,n);case"meudeus":return Object(r.g)(qi,n);case"hahaha":return Object(r.g)(Zi,n);case"fofo":return Object(r.g)($i,n);case"jajaja":return Object(r.g)(ea,n);case"kawaii":return Object(r.g)(ra,n);case"madremia":return Object(r.g)(aa,n);case"lindo":return Object(r.g)(ua,n);case"oooh":return Object(r.g)(pa,n);case"win":return Object(r.g)(ga,n);case"omoshiro":return Object(r.g)(wa,n);case"kando":return Object(r.g)(ja,n);case"oishii":return Object(r.g)(xa,n);case"doubutsu":return Object(r.g)(Pa,n);case"trending-jp":return Object(r.g)(za,n)}return null};function Aa(e){return(Aa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Da(e){return function(e){if(Array.isArray(e))return Ra(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Ra(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ra(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ra(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ba(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Fa(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ba(Object(n),!0).forEach((function(t){Ka(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ba(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ua(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Ha(e,t){return(Ha=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Wa(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Ga(e);if(t){var o=Ga(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Va(this,n)}}function Va(e,t){return!t||"object"!==Aa(t)&&"function"!=typeof t?qa(e):t}function qa(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ga(e){return(Ga=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Ka(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Za(){return(Za=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Xa(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function Ya(){return!!window.matchMedia("(min-width:".concat(vi.breakpointLarge,")")).matches}var $a=En((function(e){var t=e.theme,n=e.flyout,o=void 0===n?null:n,i=Xa(e,["theme","flyout"]);return o?Object(r.g)(sn.Consumer,null,(function(e){return Object(r.g)(di,{width:420,enabled:Ya,location:"topicBar",label:i.label,ga:e.ga},Object(r.g)(fi,null,Object(r.g)(Er,Za({location:"topicBar",className:"".concat(gi.a.link," ").concat(t.link)},i),i.emoji," ",i.children,Object(r.g)(br,{className:gi.a.downCaret,"aria-hidden":!0}))),Object(r.g)(_i,Za({location:i.label},o)))})):Object(r.g)(Er,Za({location:"topicBar",className:"".concat(gi.a.link," ").concat(t.link," ").concat(i.mobileOnly?gi.a.mdHide:"")},i))})),Ja=En(function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Ha(e,t)}(a,e);var t,n,o,i=Wa(a);function a(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),Ka(qa(t=i.call(this,e)),"updateDailyFact",(function(e){var n,r,o;"fact-of-the-day"===(null==e||null===(n=e.data)||void 0===n?void 0:n.type)&&(null==e||null===(r=e.data)||void 0===r||null===(o=r.message)||void 0===o?void 0:o.text)&&t.setState(Fa(Fa({},t.state),{},{dailyFact:e.data.message}))})),t.state={displayedItems:Da(e.topics),dailyFact:null,isSticking:e.isSticking,isSecondNav:e.isSecondNav},t}return t=a,(n=[{key:"componentDidMount",value:function(){window.addEventListener("message",this.updateDailyFact),window.parent.postMessage({type:"topic-nav-loaded"},"*")}},{key:"componentDidUpdate",value:function(e){e.isSticking!==this.props.isSticking&&this.setState({isSticking:this.props.isSticking})}},{key:"componentWillUnmount",value:function(){window.removeEventListener("message",this.updateDailyFact)}},{key:"getLogo",value:function(e){return"Arcade"===e?Object(r.g)(Ci,{width:13,height:15}):null}},{key:"render",value:function(e,t){var n=this,o=e.theme;return Object(r.g)(cn.Consumer,null,(function(e){var i,a,c,s,u,l,f,p,d;return Object(r.g)("div",{className:"".concat(t.isSecondNav?o.secondSubNavContainer:""," ").concat(gi.a.subNavContainer," ").concat(t.isSticking?gi.a.isSticking:""," ").concat(t.isSecondNav?gi.a.isSecondNav:gi.a.isPrimaryNav)},Object(r.g)("nav",{className:"".concat(o.topicNavWrapper," ").concat(gi.a.wrapper),"aria-label":e.hot_topics},Object(r.g)("div",{className:"".concat(gi.a.bfLogo," ").concat(o.secondNavLogo)},Object(r.g)(sn.Consumer,null,(function(e){var t=e.brand_urls;return Object(r.g)(Er,{href:t.default.bfo,label:"buzzfeed",location:"logo"},Object(r.g)(Ma,{badgeName:"trending",brand:"buzzfeed",role:"img","aria-labelledby":"buzzfeed"},Object(r.g)("title",{id:"buzzfeed"},"BuzzFeed")))}))),Object(r.g)("ul",{className:"".concat(o.topicNav," ").concat(gi.a.topicNav),ref:function(e){return n.element=e}},t.displayedItems.map((function(e){var t=e.url,o=e.name,i=e.label,a=Xa(e,["url","name","label"]);return Object(r.g)("li",{className:"".concat(gi.a.topicNavItem," ").concat(n.getLogo(o)?gi.a.topicNavItemWithLogo:""," ").concat(a.isMobile?gi.a.mobileItem:gi.a.desktopItem),key:o},Object(r.g)($a,Za({href:t,label:i||o},a),n.getLogo(o),o))}))),!!(null===(i=t.dailyFact)||void 0===i||null===(a=i.text)||void 0===a?void 0:a.length)&&!(null===(c=t.dailyFact)||void 0===c||null===(s=c.url)||void 0===s?void 0:s.length)&&Object(r.g)("span",{className:"".concat(gi.a.dailyFact," ").concat(t.isSticking?gi.a.isSticking:""),id:"daily-fact",dangerouslySetInnerHTML:{__html:t.dailyFact.text}}),!!(null===(u=t.dailyFact)||void 0===u||null===(l=u.text)||void 0===l?void 0:l.length)&&!!(null===(f=t.dailyFact)||void 0===f||null===(p=f.url)||void 0===p?void 0:p.length)&&Object(r.g)(Er,{className:"".concat(gi.a.dailyFactLink," ").concat(t.isSticking?gi.a.isSticking:""),id:"daily-fact",href:t.dailyFact.url,location:"dailyFact",label:"daily_fact",target:(null===(d=t.dailyFact)||void 0===d?void 0:d.forceNewBrowserTab)?"_blank":"_self",trackingData:{position_in_unit:null==t?void 0:t.displayedItems.length}},xi(t.dailyFact.text))))}))}}])&&Ua(t.prototype,n),o&&Ua(t,o),a}(r.a)),Qa=n(27),ec=n.n(Qa);function tc(e){return(tc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nc(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function rc(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function oc(e,t){return(oc=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function ic(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=cc(e);if(t){var o=cc(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return ac(this,n)}}function ac(e,t){return!t||"object"!==tc(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function cc(e){return(cc=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var sc=En(function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&oc(e,t)}(a,e);var t,n,o,i=ic(a);function a(){return nc(this,a),i.apply(this,arguments)}return t=a,(n=[{key:"render",value:function(e){var t=e.navItems,n=e.theme,o=e.showMoreNav,i=e.isSticking,a=e.children;return Object(r.g)("div",{className:"".concat(ec.a.mainNavContainer," ").concat(n.mainNavContainer," js-main-nav\n          ").concat(i?ec.a.stickyMainNav:"","\n        ")},Object(r.g)("div",{className:en.wrapper},Object(r.g)("div",{className:ec.a.mainNav},a,Object(r.g)(Ja,{topics:t.topics,isSticking:i})),Object(r.g)(Ko,{navItems:t,isVisible:o})),Object(r.g)(pi,null))}}])&&rc(t.prototype,n),o&&rc(t,o),a}(r.a)),uc=n(54),lc=n.n(uc);function fc(e){return(fc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pc(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function dc(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function hc(e,t){return(hc=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function gc(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=bc(e);if(t){var o=bc(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return mc(this,n)}}function mc(e,t){return!t||"object"!==fc(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function bc(e){return(bc=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var yc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&hc(e,t)}(a,e);var t,n,o,i=gc(a);function a(){return pc(this,a),i.apply(this,arguments)}return t=a,(n=[{key:"clickHandler",value:function(e){return function(){yr({location:"skipNav",ga:e})}}},{key:"render",value:function(){var e=this;return Object(r.g)(sn.Consumer,null,(function(t){return Object(r.g)(cn.Consumer,null,(function(n){return Object(r.g)("a",{href:"#buzz-content",className:lc.a.skipNav,onClick:e.clickHandler(t.ga)},n.skip_to_content)}))}))}}])&&dc(t.prototype,n),o&&dc(t,o),a}(r.a);function _c(){return(_c=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var vc={id:"close-icon",width:38,height:38},wc=function(e){var t=_c({},e);return Object(r.g)(Fn,_c({id:vc.id,viewBox:"0 0 ".concat(vc.width," ").concat(vc.height)},t))};function Oc(){return(Oc=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var kc={id:"hamburger",width:16,height:12},jc=function(e){var t=Oc({},e);return Object(r.g)(Fn,Oc({id:kc.id,viewBox:"0 0 ".concat(kc.width," ").concat(kc.height)},t))},Cc=n(28),Sc=n.n(Cc);function xc(){return(xc=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Nc={id:"bfj-logo",width:953,height:162,fill:"none"},Ec=function(e){return Object(r.g)(Fn,xc({id:Nc.id,viewBox:"0 0 ".concat(Nc.width," ").concat(Nc.height)},e))};function Pc(){return(Pc=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Tc(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var Ic="js-bfo-logo-title",zc=En((function(e){var t=e.brand,n=void 0===t?"bfo":t,o=e.theme,i=e.edition,a=Tc(e,["brand","theme","edition"]);return Object(r.g)(sn.Consumer,null,(function(e){var t=e.brand_urls;return Object(r.g)(Er,{href:t.default.bfo,className:Sc.a.logoContainer,label:"buzzfeed",location:"logo"},"jp"!==i&&Object(r.g)(Wn,Pc({className:"".concat(Sc.a[n]," ").concat(o.bfoLogo),role:"img","aria-labelledby":Ic},a),Object(r.g)("title",{id:Ic},"BuzzFeed Homepage")),"jp"===i&&Object(r.g)(Ec,Pc({className:"".concat(Sc.a[n]," ").concat(o.bfoLogo),role:"img","aria-labelledby":Ic},a),Object(r.g)("title",{id:Ic},"BuzzFeed Homepage")))}))})),Lc=n(29),Mc=n.n(Lc),Ac=En((function(e){var t=e.theme;return Object(r.g)(sn.Consumer,null,(function(){return Object(r.g)("div",{className:Mc.a.newsletters},Object(r.g)("div",null,Object(r.g)(Er,{className:Mc.a.newslettersLink,href:"/newsletters?origin=navIcon",label:"newsletters",location:"newsletters"},Object(r.g)(pr,{height:20,width:20,className:"".concat(Mc.a.newslettersIcon," ").concat(t.newslettersIcon),"aria-label":"Newsletters"}))))}))})),Dc=n(11),Rc=n.n(Dc);var Bc=function(){return Object(r.g)("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Object(r.g)("path",{d:"M10.1051 10.125C11.1245 9.13241 11.757 7.74898 11.757 6.21875C11.757 3.19844 9.29299 0.75 6.2535 0.75C3.214 0.75 0.75 3.19844 0.75 6.21875C0.75 9.23906 3.214 11.6875 6.2535 11.6875C7.75305 11.6875 9.11252 11.0916 10.1051 10.125ZM10.1051 10.125L13.25 13.25",stroke:"black","stroke-width":"1.5","stroke-linecap":"round"}))};function Fc(e){return(Fc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Uc(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Hc(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Uc(Object(n),!0).forEach((function(t){Wc(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Uc(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Wc(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Vc(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function qc(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Gc(e,t,n){return t&&qc(e.prototype,t),n&&qc(e,n),e}function Kc(e){var t=es();return function(){var n,r=ns(e);if(t){var o=ns(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Yc(this,n)}}function Zc(e,t){Zc=function(e,t){return new i(e,void 0,t)};var n=Jc(RegExp),r=RegExp.prototype,o=new WeakMap;function i(e,t,r){var i=n.call(this,e,t);return o.set(i,r||o.get(e)),i}function a(e,t){var n=o.get(t);return Object.keys(n).reduce((function(t,r){return t[r]=e[n[r]],t}),Object.create(null))}return Xc(i,n),i.prototype.exec=function(e){var t=r.exec.call(this,e);return t&&(t.groups=a(t,this)),t},i.prototype[Symbol.replace]=function(e,t){if("string"==typeof t){var n=o.get(this);return r[Symbol.replace].call(this,e,t.replace(/\$<([^>]+)>/g,(function(e,t){return"$"+n[t]})))}if("function"==typeof t){var i=this;return r[Symbol.replace].call(this,e,(function(){var e=[];return e.push.apply(e,arguments),"object"!==Fc(e[e.length-1])&&e.push(a(e,i)),t.apply(this,e)}))}return r[Symbol.replace].call(this,e,t)},Zc.apply(this,arguments)}function Xc(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&ts(e,t)}function Yc(e,t){return!t||"object"!==Fc(t)&&"function"!=typeof t?$c(e):t}function $c(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Jc(e){var t="function"==typeof Map?new Map:void 0;return(Jc=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return Qc(e,arguments,ns(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),ts(r,e)})(e)}function Qc(e,t,n){return(Qc=es()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var o=new(Function.bind.apply(e,r));return n&&ts(o,n.prototype),o}).apply(null,arguments)}function es(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function ts(e,t){return(ts=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function ns(e){return(ns=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var rs=Zc(/\/(quizzes)(\/?)/g,{page:1}),os=En((function(e){var t=e.theme,n=e.id;return Object(r.g)(Bc,{className:"".concat(Rc.a.searchIcon," ").concat(t.searchIcon),role:"img","aria-labelledby":n},Object(r.g)(cn.Consumer,null,(function(e){return Object(r.g)("title",{id:n},e.search)})))})),is=function(e){Xc(n,e);var t=Kc(n);function n(){return Vc(this,n),t.apply(this,arguments)}return Gc(n,[{key:"componentDidMount",value:function(){this.input.focus()}},{key:"render",value:function(e){var t=this,n=e.id;return Object(r.g)(cn.Consumer,null,(function(e){return Object(r.g)("form",{onSubmit:function(){return _o({location:"search",action_value:t.input.value})},className:Rc.a.searchInputContainer,role:"search",action:"/search"},Object(r.g)("label",{htmlFor:n,className:Rc.a.searchLabel},e.search),Object(r.g)("input",{id:n,className:Rc.a.searchInput,type:"search",name:"q",placeholder:e.search,autocomplete:"off",ref:function(e){return t.input=e},"data-bfa":"@a:search;@d:input;"}),t.props.isQuizFeed?Object(r.g)("input",{type:"hidden",name:"type",value:"quiz"}):null)}))}}]),n}(r.a),as=function(e){Xc(n,e);var t=Kc(n);function n(){var e;return Vc(this,n),(e=t.call(this)).state={inputVisible:!1,isQuizFeed:!1},e.onClick=e.onClick.bind($c(e)),e}return Gc(n,[{key:"componentDidMount",value:function(){"undefined"!=typeof window&&this.setState(Hc(Hc({},this.state),{},{isQuizFeed:rs.test(window.location.pathname)}))}},{key:"onClick",value:function(e){e.preventDefault(),this.setState(Hc(Hc({},this.state),{},{inputVisible:!this.state.inputVisible}))}},{key:"render",value:function(){var e=this;return Object(r.g)("div",{className:this.state.inputVisible?"".concat(Rc.a.search," ").concat(Rc.a.active):Rc.a.search},Object(r.g)("div",null,Object(r.g)(Er,{className:"".concat(Rc.a.searchLink),href:this.state.isQuizFeed?"/search?type=quiz":"/search",location:"search"},Object(r.g)(os,{id:"".concat("js-search-button","-mobile")})),Object(r.g)(cn.Consumer,null,(function(t){return Object(r.g)(Fr,{className:"".concat(Rc.a.searchButton),"aria-label":t.a11y_search,onClick:e.onClick,location:"search","aria-controls":"js-header-search","aria-expanded":String(e.state.inputVisible)},Object(r.g)(os,{id:"".concat("js-search-button","-desktop")}))}))),this.state.inputVisible&&Object(r.g)(is,{id:"js-header-search",isQuizFeed:this.state.isQuizFeed}))}}]),n}(r.a),cs=["trophy_earned"];function ss(){var e,t,n=(e="user_auth_passthrough",(t=window.localStorage.getItem(e))?JSON.parse(t):null);if(n){var r=n.filter((function(e){return!cs.includes(e.type)}));window.localStorage.setItem("user_auth_passthrough",JSON.stringify(r))}}function us(e,t){if(t.length<e)throw new TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function ls(e){us(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new Date(e.getTime()):"number"==typeof e||"[object Number]"===t?new Date(e):("string"!=typeof e&&"[object String]"!==t||"undefined"==typeof console||(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as arguments. Please use `parseISO` to parse strings. See: https://git.io/fjule"),console.warn((new Error).stack)),new Date(NaN))}function fs(e){if(null===e||!0===e||!1===e)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)}function ps(e,t){us(1,arguments);var n=t||{},r=n.locale,o=r&&r.options&&r.options.weekStartsOn,i=null==o?0:fs(o),a=null==n.weekStartsOn?i:fs(n.weekStartsOn);if(!(a>=0&&a<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var c=ls(e),s=c.getDay(),u=(s<a?7:0)+s-a;return c.setDate(c.getDate()-u),c.setHours(0,0,0,0),c}function ds(e,t,n){us(2,arguments);var r=ps(e,n),o=ps(t,n);return r.getTime()===o.getTime()}var hs=function(){if(!window||!window.localStorage)return!1;var e=window.localStorage.getItem("header_notif_tooltip_dismissed");if(!e||isNaN(e))return!1;var t=parseInt(e,10);return function(e,t){return us(1,arguments),ds(e,Date.now(),t)}(new Date(t),{weekStartsOn:1})},gs=n(5),ms=n.n(gs),bs={newsletters:[{name:"buzzfeed_email_daily",slug:"buzzfeed-daily",title:"BuzzFeed Daily",description:"Keep up with the latest daily buzz with the BuzzFeed Daily newsletter!",frequency:"5 updates a week",image:"https://img.buzzfeed.com/store-an-image-stage-us-east-1/DnIwt1zIv.gif",brand:"buzzfeed",category:"trending",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_tasty",slug:"tasty",title:"Tasty",description:"Recipes, food quizzes, & cooking hacks.",frequency:"4 updates a week",image:"https://www.buzzfeed.com/static-assets/img/tasty-banner.4a67be8439b077d014d554646015d1e2.jpg",brand:"buzzfeed",category:"trending",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_books",slug:"book",title:"Books",description:"The newsletter for all things books! Get recommendations for your TBR, bookish quizzes, and info on new releases.",frequency:"2 updates a week",image:"https://www.buzzfeed.com/static-assets/img/books-banner.a3436be4d2e4579f699a0e57b0afdb37.jpg",brand:"buzzfeed",category:"trending",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_quizzes",slug:"quizzes",title:"Quizzes",description:"The quizzes you know and love delivered straight to your inbox!",frequency:"3 updates a week",image:"https://www.buzzfeed.com/static-assets/img/quizzes-banner.2a6a3220521d83e523e772655fd7405b.jpg",brand:"buzzfeed",category:"trending",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_suspicious",slug:"suspicious-circumstances",title:"Suspicious Circumstances",description:"A true crime inside guide to the biggest unsolved mysteries, white collar scandals, and grim cases of today.",frequency:"weekly on Tuesdays",image:"https://www.buzzfeed.com/static-assets/img/suspiciouscircumstances-banner.86797ea7c0e0f4f968447f3f5d1d422e.jpg",brand:"buzzfeed",category:"buzzfeed_news",editions:["en-us"]},{name:"buzzfeed_email_nifty",slug:"nifty",title:"Nifty",description:"DIY projects, organization tips, & home decor recommendations.",frequency:"3 updates a week",image:"https://www.buzzfeed.com/static-assets/img/nifty-banner.05e8cbe401f3a1c4d1d101759a32a831.jpg",brand:"buzzfeed",category:"trending",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_shopping",slug:"shopping",title:"Shopping",description:"Find great products - plus cool stuff you didnâ€™t even know you needed.",frequency:"3 updates a week",image:"https://www.buzzfeed.com/static-assets/img/shopping-banner.ef0739cdec4787dd652d74174a2ebdcf.jpg",brand:"buzzfeed",category:"trending",editions:["en-us"]},{name:"buzzfeed_email_incoming",slug:"incoming",title:"Incoming",description:"Your weekday morning guide to breaking news, cultural analysis, and everything in between",frequency:"5 updates a week",image:"https://www.buzzfeed.com/static-assets/img/incomingnews-banner.349462814c804424f475ac4cdf2cc595.jpg",brand:"buzzfeed",category:"buzzfeed_news",editions:["en-us"]},{name:"buzzfeed_email_shop-buzzfeed",slug:"shop-buzzfeed-merch",title:"Shop BuzzFeed Merch",description:"Love BuzzFeed as much as we do? Then sign up for our Shop BuzzFeed merch newsletter to stay up to date on all official show merch and promotions!",frequency:"1 update a week",image:"https://www.buzzfeed.com/static-assets/img/merch-banner.d286b851e47560b3f0c62a4977dbdcb7.jpg",brand:"buzzfeed",category:"trending",editions:["en-us"]},{name:"buzzfeed_email_royal-tea",slug:"royal-tea",title:"The Royal Tea",description:"Royal family news and analysis, served piping hot.",frequency:"1 update a month",image:"https://www.buzzfeed.com/static-assets/img/royaltea-banner.78d8ec132d0e62b613cfc24425c75f41.jpg",brand:"buzzfeed",category:"buzzfeed_news",editions:["en-us"]},{name:"buzzfeed_email_asis",slug:"asis",title:"As/Is",description:"Style tips, makeup advice, and must-have products.",frequency:"3 updates a week",image:"https://www.buzzfeed.com/static-assets/img/asis-banner.0da0dcb905112ae75646e8e3e6e03d73.jpg",brand:"buzzfeed",category:"trending",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_course-better-skin",slug:"course-better-skin",title:"Better Skin Challenge",description:"Tips from a dermatologist for getting clearer, healthier skin.",frequency:"7 days",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/koyRs2-N5.png",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_course-strong-core",slug:"course-strong-core",title:"Strong Core Challenge",description:"An exercise plan to get stronger abs!",frequency:"21 days",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/fwyZaVEP8.png",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_course-best-semester",slug:"course-best-semester",title:"Best Semester Ever Course",description:"Make college a bit easier this semester with these helpful tips.",frequency:"10 days",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/TjtlP6KUI.png",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_cleanse-the-timeline",slug:"cleanse-the-timeline",title:"Cleanse The Timeline",description:"Our bimonthly newsletter covers every pivotal, unforgettable, zany moment in the cultural landscape.",frequency:"twice a month",image:"https://www.buzzfeed.com/static-assets/img/cleansetimeline-banner.2a3d493e8fe55145f1e0b5ebc79d83f5.jpg",brand:"buzzfeed",category:"buzzfeed_news",editions:["en-us"]},{name:"buzzfeed_email_course-dogs",slug:"course-dogs",title:"From Pup To Parenthood",description:"A 30-day guide on becoming the best dog parent.",frequency:"30 days",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/ZHoqQUgYLL.png",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_course-chocolate-chip",slug:"course-chocolate-chip",title:"BuzzFeeds Best Chocolate Chip Cookie Guide",description:"How to test different variables to bake the worlds tastiest chocolate chip cookie.",frequency:"9 days",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/tJOSUqcLC.png",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_course-tasty-meal-plan",slug:"course-tasty-meal-plan",title:"Tastyâ€™s Make-Ahead Meal Plan",description:"A 7-day plan of easy meals for your busy week.",frequency:"7 days",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/wiZ8vm9fR.png",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_course-natural-hair",slug:"course-natural-hair",title:"Natural Hair Challenge",description:"Get healthier natural hair in less than a week.",frequency:"5 days",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/Qd2wwPBM2.png",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_tvandmovies",slug:"tvandmovies",title:"TV & Movies",description:"Get all the best moments in pop culture & entertainment delivered to your inbox.",frequency:"3 updates a week",image:"https://www.buzzfeed.com/static-assets/img/tvandmovies-banner.8facfb8dab88289c675ce77e768c1582.jpg",brand:"buzzfeed",category:"trending",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_community_stats",slug:"community-stats",title:"Community Weekly Stats",description:"Get a weekly rundown of your top-performing posts, where you rank leaderboard-wise, and more!",frequency:"monthly",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/9v5YhcFtZ.png",brand:"buzzfeed",category:"icymi",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_course-spring-cleaning",slug:"course-spring-cleaning",title:"Spring Cleaning Challenge",description:"Give your home a restart with this cleaning challenge.",frequency:"7 days",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/d3L-0pEun.png",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_jpg",slug:"jpg",title:"JPG",description:"Your lens to the internets most powerful photographs.",frequency:"1 update a week",image:"https://www.buzzfeed.com/static-assets/img/jpgnewsletter-banner.3dd29be166bb014f01e815662bc77323.jpg",brand:"buzzfeed",category:"buzzfeed_news",editions:["en-us"]},{name:"buzzfeed_email_course-less-waste",slug:"course-less-waste",title:"Goodfulâ€™s 7-Day Guide To Creating Less Waste",description:"Little things you can do to help this big beautiful planet we call home.",frequency:"7 days",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/J9OiCkw96.png",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_course-detox-thoughts",slug:"course-detox-thoughts",title:"Goodfulâ€™s Detox Your Thoughts",description:"A month-long guide to ditching the mental traps that are making you miserable.",frequency:"28 days",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/yU9VtK5Ev.png",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_community",slug:"community",title:"Community Contributor Updates",description:"Get updated every time thereâ€™s a chance to earn $$$ for making your own quizzes and posts and other important BuzzFeed Community announcements!",frequency:"monthly",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/9v5YhcFtZ.png",brand:"buzzfeed",category:"icymi",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_course-healthy-eating",slug:"course-healthy-eating",title:"Goodfulâ€™s Healthy Eating Challenge",description:"Two weeksâ€™ worth of healthy recipes with nutritional info.",frequency:"14 days",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/zE3vxsFOI.png",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_please-like-me",slug:"please-like-me",title:"Please Like Me",description:"The girlies from the social news team show you the best (and worst) of influencers and internet culture in our weekly newsletter.",frequency:"1 update a week",image:"https://www.buzzfeed.com/static-assets/img/pleaselikeme-banner.277b90dd4a17f1cfb527a414da5b499b.jpg",brand:"buzzfeed",category:"buzzfeed_news",editions:["en-us"]},{name:"buzzfeed_email_quibbles-bits",slug:"quibbles-bits",title:"Quibbles & Bits",description:"Sign up for for deep dives about sensitive and inclusive language, style guidance, grammar memes, and more",frequency:"1 update a month",image:"https://www.buzzfeed.com/static-assets/img/quibbleslanguage-banner.c2b8fc16b74bad7e375026c951f30e59.jpg",brand:"buzzfeed",category:"buzzfeed_news",editions:["en-us"]},{name:"buzzfeed_email_course-butt-challenge",slug:"course-butt-challenge",title:"Butt Challenge",description:"Develop a stronger butt in 30 days with the help of a fitness instructor!",frequency:"30 days",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/Rlu-X345j.png",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_course-goodful-5k",slug:"course-goodful-5k",title:"Goodfulâ€™s 4 Weeks to 5K",description:"A good place to start if youâ€™ve ever thought about getting into running.",frequency:"28 days",image:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/Hb2K6l6nT.png",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]},{name:"buzzfeed_email_course-overcome-anxiety",slug:"course-overcome-anxiety",title:"Goodfuls 8-Day Guide to Overcoming Anxiety",description:"Helping you develop healthy habits to make working through anxiety a lil bit easier.",frequency:"8 Days",image:"https://www.buzzfeed.com/static-assets/img/goodful8dayanxiety-banner.887e3a68044068b57ac5103e5de9e7e0.jpg",brand:"buzzfeed",category:"courses",editions:["en-us","en-uk","en-ca","en-au","en-in"]}]},ys=n(55),_s=n.n(ys),vs=n(13),ws=n.n(vs),Os=function(e){var t=e.message,n=e.icon,o=e.showToast,i=e.className;return o&&t?Object(r.g)("div",{className:_s()(ws.a.toast,i)},n,t):null};Os.propTypes={message:Xe.a.string,icon:Xe.a.element,showToast:Xe.a.bool.isRequired,className:Xe.a.string};var ks=Os;function js(e){return(js="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Cs(){return(Cs=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Ss(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function xs(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ss(Object(n),!0).forEach((function(t){Ls(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ss(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ns(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Es(e,t){return(Es=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Ps(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=zs(e);if(t){var o=zs(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Ts(this,n)}}function Ts(e,t){return!t||"object"!==js(t)&&"function"!=typeof t?Is(e):t}function Is(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function zs(e){return(zs=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Ls(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ms="/newsletters/api/subhub/v1",As={"Content-type":"application/json","X-XSRF":Bt("_xsrf","")},Ds=["trending","in_case_you_missed_it","courses"],Rs=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Es(e,t)}(a,e);var t,n,o,i=Ps(a);function a(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),(e=i.call(this)).submitButton=Object(r.f)(),e.state={scroll:!1,order:["buzzfeed_email_daily","buzzfeed_email_shopping","buzzfeed_email_quizzes","buzzfeed_email_tasty"],checked:[],options:[],showToast:!1,error:!1,trackingData:{location:"newsletterSignupModal",unit_type:"nav",unit_name:"main",item_type:"submission",item_name:"email",subunit_type:"component",subunit_name:"newsletter_subscription",target_content_type:"submission",target_content_id:"newsletter_subscription"},errorToast:{message:"Oops, something went wrong. Please try again.",icon:Object(r.g)(ut,{className:"".concat(ws.a.toastIcon," ").concat(ws.a.toastErrorIcon)}),className:ws.a.toastError},successToast:{message:"Your subscriptions have been updated.",icon:Object(r.g)(st,{className:"".concat(ws.a.toastIcon," ").concat(ws.a.toastSuccessIcon)})}},e.handleHide=e.handleHide.bind(Is(e)),e.handleScroll=e.handleScroll.bind(Is(e)),e.handleCloseToast=e.handleCloseToast.bind(Is(e)),e.onKeyDown=e.onKeyDown.bind(Is(e)),e.onClickOutside=e.onClickOutside.bind(Is(e)),e.handleFetch=e.handleFetch.bind(Is(e)),e.handleSubmit=e.handleSubmit.bind(Is(e)),e.handleSeeAllNewsletters=e.handleSeeAllNewsletters.bind(Is(e)),e}return t=a,(n=[{key:"setOptions",value:function(e){var t,n,r,o=this,i=(t=e.filter((function(e){var t=e.category;return Ds.includes(t)})),n=this.state.order,r=new Map(n.map((function(e,t){return[e,t]}))),t.sort((function(e,t){var n=r.get(e.name),o=r.get(t.name);return void 0!==n&&void 0!==o?n-o:void 0!==n?-1:void 0!==o?1:0}))).map((function(e){return xs(xs({},e),{},{checked:o.state.checked.includes(e.name)?{checked:!0,"aria-checked":!0}:{disabled:!1}})}));this.setState({options:i})}},{key:"handleError",value:function(){this.setState({showToast:!0,error:!0}),setTimeout(this.props.onHide,6e3),this.props.track.contentAction(xs(xs({},this.state.trackingData),{},{item_type:"submission",item_name:"email",action_type:"submit",action_value:"buzzfeed_email_failed"}))}},{key:"handleSuccess",value:function(){this.setState({showToast:!0}),setTimeout(this.props.onHide,6e3)}},{key:"handleFetch",value:function(){var e=this;if(window.location.hostname.includes("bf-header-ui.dev.buzzfeed.io"))return this.setOptions(bs.newsletters),null;var t=this.state.edition?"&edition=".concat(this.state.edition):"";return fetch("".concat(Ms,"/newsletters?subhub=true&brand=").concat(this.state.brand).concat(t)).then((function(e){return e.json()})).then((function(t){t.hasOwnProperty("newsletters")&&t.newsletters.length?e.setOptions(t.newsletters):e.props.onHide()})).catch((function(t){console.error(t),e.handleError()})),null}},{key:"handleSubmit",value:function(e){var t=this;"function"==typeof(null==e?void 0:e.preventDefault)&&e.preventDefault();var n=Array.from(document.getElementById("newsletter").querySelectorAll("input[type=checkbox]:checked")).map((function(e){return e.value})),r={subscriptions:n,brand:this.state.brand,source:this.state.source};fetch("".concat(Ms,"/users/subscribe"),{headers:As,method:"POST",credentials:"include",body:JSON.stringify(r)}).then((function(e){[200,201,202,203].includes(e.status)?(t.handleSuccess(),t.props.track.contentAction(xs(xs({},t.state.trackingData),{},{item_type:"submission",item_name:"email",action_type:"submit",action_value:"".concat(n.join(","))}))):t.handleError()})).catch((function(e){console.error(e),t.handleError()}))}},{key:"handleSeeAllNewsletters",value:function(){this.setState({limit:100,toggleSeeAll:!0}),this.props.track.contentAction(xs(xs({},this.state.trackingData),{},{item_type:"button",item_name:"see_all_newsletters",action_type:"maximize",action_value:"see_all_newsletters"}))}},{key:"handleScroll",value:function(){this.state.scroll||this.setState({scroll:!0})}},{key:"onKeyDown",value:function(e){27===e.keyCode&&this.props.onHide(e)}},{key:"onClickOutside",value:function(e){e.preventDefault(),"newsletter"===e.target.id&&this.props.onHide(e)}},{key:"handleCloseToast",value:function(){this.setState({showToast:!1})}},{key:"handleHide",value:function(e){e.preventDefault();var t="close_modal";e.target.className.toString().includes("newsletterLink")&&(t="maybe_later"),this.props.onHide(),this.props.track.contentAction(xs(xs({},this.state.trackingData),{},{item_name:t,item_type:"button",action_value:"newsletter_subscription",action_type:"close"}))}},{key:"detectBrand",value:function(e){var t="buzzfeed";return(e.includes("huffpost")||e.includes("huffingtonpost"))&&(t="huffpost"),e.includes("buzzfeed.com")&&(t="buzzfeed"),t}},{key:"componentWillUnmount",value:function(){if("function"==typeof this.detachImpressionHandler)try{this.detachImpressionHandler()}catch(e){console.error("Unable to call detachImpressionHandler: ",e)}window.removeEventListener("keydown",this.onKeyDown,!0)}},{key:"componentDidMount",value:function(){var e=this,t=this.context.createImpressionHandler(this.submitButton.current,xs({},this.state.trackingData));this.detachImpressionHandler=function(){return t()},window.addEventListener("keydown",this.onKeyDown,!0);var n=window.matchMedia("(max-width: 500px)").matches,r=n?"mobile":"desktop",o=this.detectBrand(window.location.hostname),i=navigator.language.toLocaleLowerCase().split("-").slice(0)[0],a="".concat(i,"-").concat(this.props.edition);this.setState({edition:a,brand:o,source:"".concat(o,"-").concat(r,"-nav"),checked:"us"===this.props.edition?["buzzfeed_email_daily","buzzfeed_email_shopping"]:[],limit:n?100:4,toggleSeeAll:!1},(function(){e.handleFetch()}));var c=document.getElementById("newsletterItemContainer");c&&c.focus()}},{key:"render",value:function(){var e=this;return this.state.showToast?Object(r.g)(ks,Cs({},this.state.error?this.state.errorToast:this.state.successToast,{showToast:!0,onClose:function(){}})):Object(r.g)("div",{id:"newsletter",onMouseDown:this.onClickOutside,role:"button",tabIndex:0,className:ms.a.newsletter},Object(r.g)("form",{className:"".concat(ms.a.newsletterContent," ").concat(this.state.toggleSeeAll?ms.a.newsletterContent__open:"")},Object(r.g)("button",{type:"button",onKeyDown:this.handleHide,onClick:this.handleHide,"aria-label":"Close",className:ms.a.newsletterClose},Object(r.g)(pt,{className:ms.a.newsletterCloseIcon})),Object(r.g)("fieldset",{className:ms.a.newsletterFieldset},Object(r.g)("legend",{className:ms.a.newsletterTitle},"Sign up for newsletters"),Object(r.g)("div",{className:ms.a.newsletterSubhead},"Keep up with trending buzz youâ€™ll want to share with your friends."),Object(r.g)("div",{id:"newsletterItemContainer",onScroll:this.handleScroll,onTouchStart:this.handleScroll,className:"".concat(ms.a.newsletterItemContainer," \n                  ").concat(this.state.scroll?ms.a.newsletterItemContainer__scroll:"","\n                  ").concat(this.state.toggleSeeAll?ms.a.newsletterItemContainer__open:"")},this.state.options.filter((function(t,n){return n<e.state.limit})).map((function(e){return Object(r.g)("div",{className:ms.a.newsletterItem},Object(r.g)("img",{alt:e.title,src:e.image}),Object(r.g)("br",null),Object(r.g)("div",{className:ms.a.newsletterItemWrapper},Object(r.g)("div",{className:ms.a.newsletterCheckboxContainer},Object(r.g)("input",Cs({id:e.name,value:e.name,tabindex:0,"area-label":e.title,type:"checkbox",className:ms.a.newsletterCheckbox},e.checked)),Object(r.g)("label",{for:e.name,className:ms.a.newsletterCheckboxCheckmark})),Object(r.g)("div",{className:ms.a.newsletterItemDetails},Object(r.g)("div",{className:ms.a.newsletterItemTitle},e.title),Object(r.g)("div",{className:ms.a.newsletterItemFrequency},e.frequency),Object(r.g)("div",{className:ms.a.newsletterItemText},e.description))))}))),Object(r.g)("div",{className:ms.a.newsletterFooter},!1===this.state.toggleSeeAll&&Object(r.g)("button",{type:"button",onKeyDown:this.handleSeeAllNewsletters,onClick:this.handleSeeAllNewsletters,"aria-label":"See all newsletters",className:"".concat(ms.a.newsletterLink," ").concat(ms.a.newsletterDesktopOnly)},"See all newsletters"),Object(r.g)("div",{className:ms.a.newsletterLegal},"By subscribing, you're agreeing to let us send you customized messages regarding our content. You are also agreeing to our Terms of Service and Privacy Policy."),Object(r.g)("button",{type:"button",ref:this.submitButton,onKeyDown:this.handleSubmit,onClick:this.handleSubmit,"aria-label":"Subscribe to newsletters",className:ms.a.newsletterSubmit},"Subscribe to newsletters"),Object(r.g)("button",{type:"button",onKeyDown:this.handleHide,onClick:this.handleHide,"aria-label":"Maybe later",className:"".concat(ms.a.newsletterLink," ").concat(ms.a.newsletterAlignCenter)},"Maybe later")))))}}])&&Ns(t.prototype,n),o&&Ns(t,o),a}(r.a);Ls(Rs,"contextType",ln);var Bs=[].concat([],[]),Fs=n(7),Us=n.n(Fs);function Hs(){return(Hs=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Ws={id:"default-user-icon",width:512,height:512},Vs=function(e){var t=Hs({},e);return Object(r.g)(Fn,Hs({id:Ws.id,viewBox:"0 0 ".concat(Ws.width," ").concat(Ws.height)},t))};function qs(){return(qs=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Gs={id:"sparkles-icon",width:14,height:14,fill:"#E40C78"},Ks=function(e){var t=qs({},e);return Object(r.g)(Fn,qs({id:Gs.id,viewBox:"0 0 ".concat(Gs.width," ").concat(Gs.height)},t))},Zs=n(3),Xs=n.n(Zs);function Ys(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var $s=function(e){var t=e.title,n=void 0===t?"":t,o=e.url,i=void 0===o?"":o;n.length>65&&(n="".concat(n.substr(0,65).trim(),"..."));var a=Object(r.g)("span",{dangerouslySetInnerHTML:{__html:n}});return i?Object(r.g)("a",{href:i,className:Xs.a.postLink},a):a};var Js={comments_new_reply:function(e){var t=e.reply_guy,n=void 0===t?{}:t,o=e.title,i=e.url,a=n.display_name||n.username||"Somebody";return Object(r.g)(r.b,null,Object(r.g)("div",{className:Xs.a.postTitle},a,Object(r.g)("span",{className:Xs.a.regular}," replied to your comment on "),Object(r.g)($s,{title:o,url:i}),"."),Object(r.g)("a",{className:Xs.a.seeReplyButton,href:i},"See Reply",Object(r.g)(ct,{width:".75rem",height:".75rem"})))},comments_new_mention:function(e){var t=e.mentioned_by,n=void 0===t?{}:t,o=e.title,i=e.url,a=n.display_name||n.username||"Somebody";return Object(r.g)(r.b,null,Object(r.g)("div",{className:Xs.a.postTitle},a,Object(r.g)("span",{className:Xs.a.regular}," mentioned you on "),Object(r.g)($s,{title:o,url:i}),"."),Object(r.g)("a",{className:Xs.a.seeReplyButton,href:i},"See Comment",Object(r.g)(ct,{width:".75rem",height:".75rem"})))},post_promote:function(){return Object(r.g)("span",{className:Xs.a.postTitle},"Congratulations, your post was promoted!")},ipa_post_promote:function(e){return Object(r.g)("span",{className:Xs.a.postTitle},"Your post, ",Object(r.g)($s,e),", was promoted.")},ipa_post_publish:function(e){return Object(r.g)("span",{className:Xs.a.postTitle},"You published ",Object(r.g)($s,e),".")},ipa_view_threshold:function(e){var t=e.threshold,n=Ys(e,["threshold"]);return Object(r.g)("span",{className:Xs.a.postTitle},"Your post, ",Object(r.g)($s,n),", got ",t," views!")},ipa_comment_heart_threshold:function(e){var t=e.threshold;return Object(r.g)("span",{className:Xs.a.postTitle},"Your comment got ",t," hearts!")},ipa_trophy_added:function(e){var t=e.trophy_name,n=e.trophy_img_url,o=e.date;return Object(r.g)("div",{className:Xs.a.trophyAddedContainer},Object(r.g)("img",{alt:t,width:"75",height:"75",src:"".concat(n,"?resize=75:75")}),Object(r.g)("div",{className:Xs.a.postTitleContainer},Object(r.g)("span",{className:Xs.a.postTitle},t),Object(r.g)("span",{className:Xs.a.notificationTimestamp},o)))},teaser_trophy_added:function(e){var t=e.trophy_name,n=e.trophy_img_url;return Object(r.g)("span",{className:Xs.a.trophyAddedContainer},Object(r.g)("img",{alt:t,width:"75",height:"75",src:"".concat(n,"?resize=75:75")}),Object(r.g)("span",{className:Xs.a.postTitle},t),Object(r.g)("button",{className:Xs.a.buttonCTA,type:"button"},"Sign in to claim this trophy"," ",Object(r.g)("span",{className:Xs.a.buttonCTAChevron},"â€º")))}};function Qs(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(r=(a=c.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{r||null==c.return||c.return()}finally{if(o)throw i}}return n}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return eu(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return eu(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eu(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function tu(e){return(e+="").indexOf("#")>-1?e.substr(0,e.indexOf("#")):e}function nu(e){return e.indexOf("?")>-1}function ru(e){return function(e){if(""===e||null==e)return{};e.indexOf("?")>-1&&(e=e.substr(e.indexOf("?")+1,e.length));var t=(e=tu(e)).split("&"),n={};return t.forEach((function(e){var t=Qs(e.split("="),2),r=t[0],o=t[1],i=void 0===o?null:o;n[r]=i})),n}(function(e){var t="";return nu(e=tu(e))&&(t=e.substr(e.indexOf("?"),e.length)),t}(e))}function ou(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function iu(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ou(Object(n),!0).forEach((function(t){au(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ou(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function au(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function cu(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function su(){return(su=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function uu(e){return(uu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function lu(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function fu(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function pu(e,t,n){return t&&fu(e.prototype,t),n&&fu(e,n),e}function du(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&hu(e,t)}function hu(e,t){return(hu=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function gu(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=bu(e);if(t){var o=bu(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return mu(this,n)}}function mu(e,t){return!t||"object"!==uu(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function bu(e){return(bu=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var yu=function(e){var t,n=Date.now()/1e3-e.valueOf()/1e3;if(n<3600){var r=Math.round(n/60);t=r<2?"1 minute ago":r+" minutes ago"}else if(n<86400){var o=Math.round(n/3600);t=o<2?"1 hour ago":o+" hours ago"}else t="on "+function(e){return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",year:"numeric"}).format(e)}(e);return t},_u=function(e){du(n,e);var t=gu(n);function n(){return lu(this,n),t.apply(this,arguments)}return pu(n,[{key:"render",value:function(e){var t=e.count;return Object(r.g)("div",{className:Xs.a.unreadIcon}," ",t," ")}}]),n}(r.a),vu=function(e){du(n,e);var t=gu(n);function n(){return lu(this,n),t.apply(this,arguments)}return pu(n,[{key:"clickHandler",value:function(e){var t=e.onClick,n=e.label,r=e.location,o=e.url,i=e.ga;return function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e){return e.preventDefault(),yr({label:n,location:r,ga:i}),a=t(e),c=function(){window.location=o},s?c?c(a):a:(a&&a.then||(a=Promise.resolve(a)),c?a.then(c):a);var a,c,s}))}},{key:"getNotificationTitle",value:function(e,t){switch(e){case"post_promote":return Object(r.g)("span",null," Post Promoted ");case"ipa_trophy_added":case"teaser_trophy_added":return Object(r.g)("span",null," New Trophy! ");case"comments_new_reply":return Object(r.g)("span",null," New Comment Reply! ");case"comments_new_mention":return Object(r.g)("span",null," New Comment Mention! ")}return t.points?Object(r.g)("span",null,Object(r.g)(Ks,{className:Xs.a.sparklesSVG,height:14,width:14,"aria-hidden":!0}),t.points.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")," Internet Points earned!"):""}},{key:"getNotificationBody",value:function(e){var t=e.type,n=e.meta,o=e.url,i=e.trackingData,a=e.date;if(Js[t]){var c=Js[t];return Object(r.g)(c,su({},n,{url:o,trackingData:i,date:a}))}return Object(r.g)("span",{className:Xs.a.postTitle},"You have a new notification!")}},{key:"getNotificationTracking",value:function(e){var t=e.content_id,n=e.content_type,r=e.id_seq,o=e.isTeaser,i=e.meta,a=e.teaserName,c=e.url,s="notification-link",u={item_name:r};if(o&&(s="teaser-notification-link",u.item_name=a),"comment"===n){s="comment-notification-link",u.item_name=t;var l=ru(c);if(l.comment_id){var f=l.comment_id.split("_")[0],p=parseInt(f,10);isNaN(p)||(u.item_name=p)}i.buzz_id&&(u.target_content_id=i.buzz_id.toString())}return{cetLabel:s,cetTracking:u}}},{key:"render",value:function(e){var t=this,n=e.onClick,o=void 0===n?function(){}:n,i=e.label,a=e.location,c=e.meta,s=e.type,u=e.url,l=e.triggered_at,f=e.newFeature,p=void 0!==f&&f,d=e.trackingData,h=void 0===d?{}:d,g=e.index,m=cu(e,["onClick","label","location","meta","type","url","triggered_at","newFeature","trackingData","index"]);h.position_in_unit=g;var b=this.getNotificationTracking(iu(iu({},m),{},{meta:c,url:u})),y=b.cetLabel,_=b.cetTracking;return Object(r.g)("li",null,Object(r.g)(sn.Consumer,null,(function(e){return Object(r.g)("div",{className:Xs.a.notification},Object(r.g)(pt,{className:Xs.a.markAsRead,onClick:o}),Object(r.g)(Er,{href:u,location:"userMenu",label:y,onClick:t.clickHandler({onClick:o,label:i,location:a,url:u,ga:e.ga}),trackingData:iu(iu({},_),h)},Object(r.g)("div",{className:Xs.a.notificationTitle},t.getNotificationTitle(s,c),p?Object(r.g)("span",{className:Xs.a.newFeatureBadge},"New Feature"):null),t.getNotificationBody({type:s,meta:c,url:u,trackingData:h,date:yu(new Date(l))})))})))}}]),n}(r.a),wu=function(e){du(n,e);var t=gu(n);function n(){return lu(this,n),t.apply(this,arguments)}return pu(n,[{key:"render",value:function(e){var t=e.unreadNotifications,n=e.readNotifications,o=e.totalUnreadCount,i=e.onMarkAsReadClicked,a=t.length>0,c=n.length>0;return Object(r.g)("div",{className:Xs.a.notifications},Object(r.g)("div",{className:Xs.a.unreadTitleContainer},Object(r.g)("div",{className:Xs.a.notificationsTitle},"Notifications"),o>0&&Object(r.g)(_u,{count:o}),a&&Object(r.g)(Fr,{className:Xs.a.markAllAsReadButton,onClick:function(){return i(t[0],!0,t)},location:"userMenu",label:"notification-read-all"},"Dismiss all")),a?Object(r.g)("div",{className:Xs.a.unreadNotificationsContainer},Object(r.g)("ul",null,t.map((function(e,t){return Object(r.g)(vu,su({onClick:function(){return i(e,!1)},location:"userMenu",label:"notification-read",index:t},e))}))),t.some((function(e){return"teaser_trophy_added"===e.type}))&&Object(r.g)("div",{className:Xs.a.teaserTrophyInfo},Object(r.g)("span",{role:"img","aria-label":"finger"},"â˜�ï¸�"),Object(r.g)("b",null," Hey, you earned a trophy! ")," Trophies are awarded to BuzzFeed Community members to celebrate their accomplishments."," ",Object(r.g)("a",{href:"/annakopsky/internet-points-2019"},Object(r.g)("b",null,"Learn more here")),".")):Object(r.g)("div",{className:Xs.a.notificationEmptyState},"No unread notifications."),c&&Object(r.g)("div",{className:Xs.a.readNotificationsContainer},Object(r.g)("ul",null,n.map((function(e,t){return Object(r.g)(vu,su({},e,{index:t}))})))))}}]),n}(r.a),Ou=n(20),ku=n.n(Ou),ju=["to leave a comment!","to make your own quizzes!","to save your quiz results!","to earn Internet Points!","to earn trophies!"];var Cu,Su=function(e){var t=e.closeButton,n=e.showRandomLoginCta;return Object(r.g)(cn.Consumer,null,(function(e){return Object(r.g)("div",{className:ku.a.signInPrompt},Object(r.g)("div",{className:ku.a.closeButtonWrapper},t),Object(r.g)("p",{className:ku.a.cta},Object(r.g)(Pr,{className:ku.a.ctaLink,href:"/auth/signin",location:"mainNav",label:"login"},n?"Sign in to BuzzFeed":e.log_in)),n&&Object(r.g)("p",{className:ku.a.sell},ju[Math.floor(Math.random()*ju.length)]))}))},xu=n(30),Nu=n.n(xu);function Eu(e){var t=e.teaserNotifications,n=e.tooltipDismissed,o=e.onNotifTooltipClicked,i=e.setShowNotifToolotip,a=e.isNewOrCommunityUser,c=e.isLoggedInUser,s=e.children,u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.type;if(t)switch(t){case"teaser_trophy_added":default:return{text:"Trophy earned!",label:"Trophy earned. Click to dismiss"}}return{text:"Trophy earned!",label:"Trophy earned. Click to dismiss"}}(t[0]),l=t&&t.length>0,f=a&&l&&!1===n&&!c;return i(f),Object(r.g)("div",{className:Nu.a.notifTooltipWrapper},s,f&&Object(r.g)("button",{"aria-label":u.label,className:Nu.a.notifTooltip,onClick:function(e){e.stopPropagation(),o()},onKeyPress:function(e){13===e.keyCode&&o()}},Object(r.g)("span",{className:Nu.a.notifTooltipText},u.text)))}function Pu(e){return function(e){if(Array.isArray(e))return Tu(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Tu(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Tu(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tu(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Iu(e){return(Iu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function zu(){return(zu=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Lu(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function Mu(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Au(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Du(e,t,n){return t&&Au(e.prototype,t),n&&Au(e,n),e}function Ru(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Bu(e,t)}function Bu(e,t){return(Bu=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Fu(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Wu(e);if(t){var o=Wu(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Uu(this,n)}}function Uu(e,t){return!t||"object"!==Iu(t)&&"function"!=typeof t?Hu(e):t}function Hu(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Wu(e){return(Wu=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}"undefined"!=typeof window&&(Cu=n(36));var Vu=["au","ca","in","us","uk"],qu=En(function(e){Ru(n,e);var t=Fu(n);function n(){return Mu(this,n),t.apply(this,arguments)}return Du(n,[{key:"render",value:function(e){var t=e.children,n=e.theme,o=Lu(e,["children","theme"]);return Object(r.g)(Fr,zu({},o,{className:"".concat(Us.a.menuToggle," ").concat(n.menuToggle),"aria-controls":"js-user-menu",location:"avatar"}),Object(r.g)("i",null,t))}}]),n}(r.a)),Gu=En((function(e){var t,n=e.displayName,o=e.image,i=e.username,a=e.theme,c=e.href,s=Lu(e,["displayName","image","username","theme","href"]);return t=o?Object(r.g)(sn.Consumer,null,(function(e){return Object(r.g)("img",zu({className:"".concat(Us.a.userAvatar," ").concat(a.userAvatar),src:e.image_service_url+o,alt:i},s))})):Object(r.g)(Vs,zu({className:"".concat(Us.a.userAvatar," ").concat(a.userAvatar)},s)),Object(r.g)("div",{className:Us.a.userAvatarContainer},c&&Object(r.g)(Er,{href:c,location:"userMenu",label:n},t),!c&&t)})),Ku=function(e){var t=e.notifMenuEnabled,n=e.userOptionsEnabled,o=e.signInPromptEnabled,i=e.isEditionSupported,a=e.image,c=e.displayName,s=e.showNotifs,u=e.username,l=e.isCommunityUser,f=e.isContributorUser,p=e.onLogoutClicked,d=e.logoutXsrf,h=e.onUserMenuClicked,g=e.unreadNotifications,m=e.readNotifications,b=e.totalUnreadCount,y=e.isVisible,_=e.onMarkAsReadClicked,v=function(e){return l?e.community_url:f?e.contributors_url:e.cms_url},w=Object(r.g)(qu,{onClick:h,"aria-expanded":String(y),"aria-label":"close"},Object(r.g)(wc,{width:22,height:22,"aria-hidden":!0}));return Object(r.g)(sn.Consumer,null,(function(e){return Object(r.g)("div",null,n&&Object(r.g)("div",{className:Us.a.userOptionsProfile},Object(r.g)(Gu,{className:"".concat(Us.a.userAvatar),image:a,username:u,displayName:c,href:e.bf_url+"/"+u}),Object(r.g)("p",{className:Us.a.userProfileName},Object(r.g)(Er,{href:e.bf_url+"/"+u,location:"userMenu",label:c},c))),Object(r.g)(cn.Consumer,null,(function(a){return Object(r.g)("div",null,n&&Object(r.g)("div",{className:Us.a.userOptions},Object(r.g)("ul",{className:Us.a.links},Object(r.g)("li",null,Object(r.g)(Er,{href:v(e)+e.new_post_path,location:"userMenu",label:"create new post",className:Us.a.newPost},"Create New Post")),Object(r.g)("li",null,Object(r.g)(Er,{href:v(e),location:"userMenu",label:"posts"},"Posts")),Object(r.g)("li",null,Object(r.g)(Er,{href:e.settings_path,location:"userMenu",label:"settings"},"Account Settings")),Object(r.g)("li",null,Object(r.g)(Er,{href:e.membership_path,location:"userMenu",label:"membership"},"Membership"))),t&&s&&Object(r.g)(wu,{unreadNotifications:g,readNotifications:m,totalUnreadCount:b,onMarkAsReadClicked:_}),Object(r.g)("div",{className:Us.a.footer},Object(r.g)("form",{className:Us.a.logout,action:"".concat(e.bf_url,"/auth/signout"),method:"post"},Object(r.g)("input",{type:"hidden",name:"_xsrf",value:d}),Object(r.g)(Fr,{onClick:p,location:"userMenu",label:"logout"},a.logout)))),o&&Object(r.g)(Su,{closeButton:w,showRandomLoginCta:i}))})))}))},Zu=function(e){Ru(n,e);var t=Fu(n);function n(){return Mu(this,n),t.apply(this,arguments)}return Du(n,[{key:"componentDidUpdate",value:function(){if(this.props.isVisible&&window.innerWidth>=parseInt(vi.breakpointSticky,10)){var e=this.container.getBoundingClientRect().top;this.container.style.maxHeight="".concat(document.documentElement.clientHeight-e,"px")}}},{key:"render",value:function(e){var t=this,n=e.isVisible,o=e.userInfo,i=e.onMarkAsReadClicked,a=Lu(e,["isVisible","userInfo","onMarkAsReadClicked"]);return Object(r.g)("section",{className:"".concat(Us.a.userMenuContainer,"  ").concat(this.props.isVisible?Us.a.visible:""),id:"js-user-menu",ref:function(e){return t.container=e}},this.props.isVisible&&Object(r.g)(Cu,{focusTrapOptions:{clickOutsideDeactivates:!0}},Object(r.g)(Ku,zu({},o,a,{isVisible:n,onMarkAsReadClicked:i}))))}}]),n}(r.a),Xu=En(function(e){Ru(n,e);var t=Fu(n);function n(e){var r;return Mu(this,n),(r=t.call(this,e)).onMarkAsReadClicked=r.onMarkAsReadClicked.bind(Hu(r)),r.onMarkTeaserAsReadClicked=r.onMarkTeaserAsReadClicked.bind(Hu(r)),r.handleVisibiltyChange=r.handleVisibiltyChange.bind(Hu(r)),r.longPollNotifications=r.longPollNotifications.bind(Hu(r)),r.initNotifications=r.initNotifications.bind(Hu(r)),r.state={enabled:Vu.includes(r.props.edition),unreadNotifications:[],readNotifications:[],totalUnreadCount:0,authenticated:!0,pageVisible:!0,hidden:"",visibilityChange:"",documentTitle:""},r}return Du(n,[{key:"componentDidUpdate",value:function(e){if(this.state.enabled&&this.props.userInfo){e.userInfo?!1===e.isVisible&&!0===this.props.isVisible&&this.updateNotifications():this.initNotifications();var t=this.state.totalUnreadCount;document.title=t>0?"(".concat(t,") ").concat(this.state.documentTitle):this.state.documentTitle}}},{key:"componentDidMount",value:function(){var e=this.props.teaserNotifications;e&&e.length>0&&!this.props.userInfo?this.setState({unreadNotifications:e,readNotifications:[],totalUnreadCount:e.length}):(this.setState({documentTitle:document.title}),this.state.enabled&&this.props.userInfo&&this.initNotifications())}},{key:"initNotifications",value:function(){this.updateNotifications(),this.setupPageVisibilityListener(),this.timer=setInterval(this.longPollNotifications,6e4)}},{key:"setupPageVisibilityListener",value:function(){var e,t,n=this;void 0!==document.hidden?(e="hidden",t="visibilitychange"):void 0!==document.msHidden?(e="msHidden",t="msvisibilitychange"):void 0!==document.webkitHidden&&(e="webkitHidden",t="webkitvisibilitychange"),this.setState({hidden:e,removeHandleVisbilityChange:function(){return document.removeEventListener(t,n.handleVisibiltyChange)}}),document.addEventListener(t,this.handleVisibiltyChange,!1)}},{key:"fetchNotifications",value:function(e){var t=this;return fetch("".concat(this.props.bfURL,"/notification-api/v1/notifications/summary?user_id=").concat(e),{credentials:"include"}).then((function(e){return e.status>=400&&e.status<500?(t.setState({authenticated:!1}),null):(t.setState({authenticated:!0}),304===e.status?null:e.json())}))}},{key:"handleVisibiltyChange",value:function(){document[this.state.hidden]?this.setState({pageVisible:!1}):this.setState({pageVisible:!0})}},{key:"longPollNotifications",value:function(){this.state.authenticated&&this.state.pageVisible&&this.updateNotifications()}},{key:"componentWillUnmount",value:function(){clearInterval(this.timer),this.state.removeHandleVisbilityChange()}},{key:"updateNotifications",value:function(){var e=this;this.props.userInfo&&this.fetchNotifications(this.props.userInfo.id).then((function(t){if(t&&(t.results_unread||t.results_read)){var n=t.results_unread,r=Math.max(10-n.length,0),o=t.results_read.slice(0,r);e.setState({unreadNotifications:n,readNotifications:o,totalUnreadCount:t.unread_count||0})}})).catch((function(e){return console.error(e)}))}},{key:"onMarkAsReadClicked",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=e.id;return fetch("".concat(this.props.bfURL,"/notification-api/v1/notifications/").concat(e.id),{method:"PATCH",body:JSON.stringify({read:!0,mark_previous_read:n}),credentials:"include"}).then((function(e){return e.json()})).then((function(e){e.success&&(n?t.updateMarkAllAsReadState():t.updateMarkAsReadState(r))})).catch((function(e){console.log(e)}))}},{key:"onMarkTeaserAsReadClicked",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t&&(ss(),this.updateMarkAllAsReadState())}},{key:"updateMarkAllAsReadState",value:function(){this.setState({unreadNotifications:[],readNotifications:[].concat(Pu(this.state.unreadNotifications),Pu(this.state.readNotifications)),totalUnreadCount:0})}},{key:"updateMarkAsReadState",value:function(e){var t=this.state.unreadNotifications.filter((function(t){return t.id!==e})),n=this.state.unreadNotifications.find((function(t){return t.id===e}));this.setState({unreadNotifications:t,readNotifications:[n].concat(Pu(this.state.readNotifications)),totalUnreadCount:Math.max(this.state.totalUnreadCount-1,0)})}},{key:"render",value:function(e){var t=e.notifMenuEnabled,n=e.userOptionsEnabled,o=e.signInPromptEnabled,i=e.teaserNotifications,a=e.isVisible,c=e.userInfo,s=e.onLogoutClicked,u=e.onUserMenuClicked,l=e.setShowNotifToolotip,f=e.onNotifTooltipClicked,p=e.logoutXsrf,d=e.edition,h=e.tooltipDismissed,g=Vu.includes(d),m=!!c,b=null===c||c&&c.isCommunityUser;return Object(r.g)("div",null,Object(r.g)(qu,{onClick:u,"aria-expanded":String(a),"aria-label":"avatar"},Object(r.g)(Eu,{onNotifTooltipClicked:f,setShowNotifToolotip:l,tooltipDismissed:h,teaserNotifications:i,isNewOrCommunityUser:b,isLoggedInUser:m},!!this.state.totalUnreadCount&&Object(r.g)("div",{className:Us.a.blink}),m&&Object(r.g)(Gu,{image:c&&c.image,username:c&&c.username,onTouchStart:this.onTouchStart,teaserNotifications:i,unreadNotificationsCount:this.state.totalUnreadCount}),!m&&Object(r.g)("div",{className:Us.a.signIn},"Sign In"))),Object(r.g)(Zu,{bfURL:this.props.bfURL,edition:d,isVisible:a,userInfo:c,onMarkAsReadClicked:c?this.onMarkAsReadClicked:this.onMarkTeaserAsReadClicked,onLogoutClicked:s,logoutXsrf:p,onUserMenuClicked:u,unreadNotifications:this.state.unreadNotifications,readNotifications:this.state.readNotifications,totalUnreadCount:this.state.totalUnreadCount,teaserNotifications:i,notifMenuEnabled:t,userOptionsEnabled:n,signInPromptEnabled:o,showNotifs:this.state.enabled,isEditionSupported:g}))}}]),n}(r.a));function Yu(e){return(Yu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function $u(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Ju(e,t){return(Ju=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Qu(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=tl(e);if(t){var o=tl(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return el(this,n)}}function el(e,t){return!t||"object"!==Yu(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function tl(e){return(tl=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var nl=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Ju(e,t)}(a,e);var t,n,o,i=Qu(a);function a(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),(t=i.call(this,e)).state={googleOneTapEnabled:!1,loginUri:"",xsrf:"",redirectUrl:""},t}return t=a,(n=[{key:"componentDidMount",value:function(){var e=this,t=!1;if(window.location.pathname.startsWith("/bpage-preview")||"jp"===this.props.edition||(t=!0,this.setState({googleOneTapEnabled:t})),t){var n=document.createElement("script");n.src="https://accounts.google.com/gsi/client",n.async=!0,n.defer=!0,document.body.appendChild(n);var r="";document.cookie.split("; ").forEach((function(t){t.startsWith("_xsrf")&&(r=t.split("=")[1],e.setState({xsrf:r}))})),""===r&&(r=Math.random().toString(36).substring(2),document.cookie="_xsrf="+r+"; path=/",this.setState({xsrf:r})),this.setState({loginUri:document.location.origin+"/auth/signin/google-one-tap",redirectUrl:document.location.href})}}},{key:"componentDidUpdate",value:function(e){this.props.isLoggedIn&&!e.isLoggedIn&&"function"==typeof window.__googleOneTapMomentCallback&&window.__googleOneTapMomentCallback({isSkippedMoment:function(){return!0},isNotDisplayed:function(){return!0}})}},{key:"render",value:function(){return this.state.xsrf&&this.state.googleOneTapEnabled&&Object(r.g)(r.b,null,Object(r.g)("script",{dangerouslySetInnerHTML:{__html:"\n  window.__googleOneTapStatus = {};\n  window.__googleOneTapMomentCallback = function(notification) {\n    window.__googleOneTapStatus.skipped = (\n      notification.isSkippedMoment() || notification.isNotDisplayed()\n    );\n    window.dispatchEvent(\n      new CustomEvent('googleOneTapStatus', {\n        detail: window.__googleOneTapStatus\n      })\n    );\n  };\n"}}),Object(r.g)("div",{id:"g_id_onload","data-client_id":"761451303051-cptuhojsap0trrfcapamqqj860t3cshu.apps.googleusercontent.com","data-context":"signin","data-login_uri":this.state.loginUri,"data-skip_prompt_cookie":"bf2-b_info","data-_xsrf":this.state.xsrf,"data-itp_support":"true","data-use_fedcm_for_prompt":"true","data-redirect_url":this.state.redirectUrl,"data-moment_callback":"__googleOneTapMomentCallback"}))}}])&&$u(t.prototype,n),o&&$u(t,o),a}(r.a);function rl(e){return(rl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ol(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function il(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ol(Object(n),!0).forEach((function(t){al(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ol(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function al(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function cl(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function sl(e,t){return(sl=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function ul(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pl(e);if(t){var o=pl(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return ll(this,n)}}function ll(e,t){return!t||"object"!==rl(t)&&"function"!=typeof t?fl(e):t}function fl(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function pl(e){return(pl=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var dl=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&sl(e,t)}(a,e);var t,n,o,i=ul(a);function a(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),(e=i.call(this)).state={isSticking:!1,logoutXsrf:null,notifMenuEnabled:!1,overlayOffset:0,redirectParam:"",modalIsOpen:!1,signInPromptEnabled:!1,showPromoBtn:!1,showMoreNav:!1,showNewsletterSignup:!1,showNotifMenu:!1,showNotifTooltip:!1,teaserNotifications:null,tooltipDismissed:null,userInfo:null,userMenuEnabled:!1,userOptionsEnabled:!1},e.setShowNewsletterSignup=e.setShowNewsletterSignup.bind(fl(e)),e.closeModal=e.closeModal.bind(fl(e)),e.onLogout=e.onLogout.bind(fl(e)),e.openModal=e.openModal.bind(fl(e)),e.setShowNotifToolotip=e.setShowNotifToolotip.bind(fl(e)),e.setShowPromoBtn=e.setShowPromoBtn.bind(fl(e)),e.toggleMoreNav=e.toggleMoreNav.bind(fl(e)),e.toggleNotifMenu=e.toggleNotifMenu.bind(fl(e)),e.scrollObserver=null,e}return t=a,(n=[{key:"overlayTopOffset",value:function(){var e=this.stickyContainer.clientHeight,t=this.stickyContainer.getBoundingClientRect().y;return this.state.isSticking?e-56:e+t-56}},{key:"setShowNewsletterSignup",value:function(e){this.setState(il(il({},this.state),{},{showNewsletterSignup:e}))}},{key:"toggleMoreNav",value:function(e){e.preventDefault(),this.setState(il(il({},this.state),{},{showMoreNav:!this.state.showMoreNav,showNotifMenu:!1}))}},{key:"setShowNotifToolotip",value:function(e){e!==this.state.showNotifTooltip&&this.setState({showNotifTooltip:e})}},{key:"toggleNotifMenu",value:function(e){var t=e.forceVisible,n=void 0===t?!this.state.showNotifMenu:t;window&&window.localStorage&&window.localStorage.setItem("header_notif_tooltip_dismissed",Date.now()),this.state.showNotifMenu||_o({location:this.state.userInfo?"notificationMenu":"teaserNotificationMenu",action_value:"show"}),this.setState(il(il({},this.state),{},{showNotifMenu:n,showMoreNav:!1,tooltipDismissed:!0}))}},{key:"onLogout",value:function(e){var t=e.target.form;this.setLogoutXsrfFromCookies(),this.state.logoutXsrf&&t.submit()}},{key:"handleLogoutFormState",value:function(){var e=this;Re.isLoggedIn()&&(this.setLogoutXsrfFromCookies(),this.state.logoutXsrf||fetch("/auth/signin").then((function(){e.setLogoutXsrfFromCookies()})))}},{key:"setLogoutXsrfFromCookies",value:function(){var e=this;document.cookie.split("; ").forEach((function(t){t.startsWith("_xsrf")&&e.setState({logoutXsrf:t.split("=")[1]})}))}},{key:"setShowPromoBtn",value:function(e){"show-membership-promo-button"===e.data.type&&this.setState({showPromoBtn:!0})}},{key:"createScrollObserver",value:function(){var e=this,t=new IntersectionObserver((function(t){t.forEach((function(t){e.setState({isSticking:!t.isIntersecting})}))}),{threshold:1});return t.observe(this.stickyContainer),t}},{key:"componentDidMount",value:function(){var e=this,t=function(){if(!Re.isLoggedIn())return null;var e=Re.getUserInfo();return{id:e.userid,displayName:e.display_name,image:e.image,username:e.username,isCommunityUser:!Re.userCan("general_admin")&&!Re.userCan("freelance_contributors"),isContributorUser:Re.userCan("freelance_contributors")}}(),n=[],r=hs(),o=!!t,i="true"===new URLSearchParams(window.location.search).get("newsletter_modal"),a=n.length>0;this.setState(il(il({},this.state),{},{userInfo:t,teaserNotifications:n,userMenuEnabled:o||a,notifMenuEnabled:o||a,userOptionsEnabled:o,showNewsletterSignup:i,signInPromptEnabled:!o&&a,tooltipDismissed:r,redirectParam:"?redirect=".concat(encodeURIComponent(window.location.href))})),this.handleLogoutFormState(),window.innerHeight>3*this.stickyContainer.clientHeight&&(this.stickyContainer.style.height="".concat(this.stickyContainer.clientHeight,"px"),this.scrollObserver=this.createScrollObserver(),window&&window.matchMedia&&window.matchMedia("(min-width: ".concat(vi.breakpointLarge,")")).addListener((function(){e.stickyContainer.style.height="",window.requestAnimationFrame((function(){e.stickyContainer.style.height="".concat(e.stickyContainer.clientHeight,"px")}))})));var c=Qt();He({userId:c,experimentConfig:Bs,abeagleHost:this.props.config.abeagle_url,data:{},source:"buzz_web"}).then((function(e){vo(e)})).catch((function(e){return console.error(e)})),window.addEventListener("message",this.setShowPromoBtn)}},{key:"componentWillUnmount",value:function(){this.scrollObserver.unobserve(this.stickyContainer),window.removeEventListener("message",this.setShowPromoBtn)}},{key:"componentDidUpdate",value:function(){this.state.showMoreNav?(this.state.overlayOffset!==this.overlayTopOffset()&&this.setState(il(il({},this.state),{},{overlayOffset:this.overlayTopOffset()})),this.checkElementPosition(this.stickyContainer,function(){this.setOverlayPosition()}.bind(this))):clearTimeout(this.stickyContainer.onElementPositionChangeTimer)}},{key:"setOverlayPosition",value:function(){this.setState(il(il({},this.state),{},{overlayOffset:this.overlayTopOffset()}))}},{key:"checkElementPosition",value:function(e,t){var n=e.getBoundingClientRect().bottom;!function r(){var o=e.getBoundingClientRect();n!==o.bottom&&t(),n=o.bottom,e.onElementPositionChangeTimer&&clearTimeout(e.onElementPositionChangeTimer),e.onElementPositionChangeTimer=setTimeout(r,200)}()}},{key:"openModal",value:function(){this.setState({modalIsOpen:!0})}},{key:"closeModal",value:function(){this.setState({modalIsOpen:!1})}},{key:"render",value:function(e,t){var n=this,o=e.navItems,i=e.config,a=e.edition,c=e.i18n,s=e.theme,u=e.gated;return Object(r.g)(cn.Provider,{value:c},Object(r.g)(sn.Provider,{value:i},Object(r.g)(un.Provider,{value:s},Object(r.g)(ln.Provider,{value:{createImpressionHandler:wo,trackNavAction:_o,trackNavClick:yo}},!("undefined"==typeof window)&&Object(r.g)(Rt,{isOpen:this.state.modalIsOpen,onClose:this.closeModal,track:{contentAction:function(e){var t=e.action_type,n=e.action_value;return _o({label:"signInModal",location:"signInModal",action_type:t,action_value:n})},externalLink:function(e){return function(e){var t=Oo();void 0!==t&&ho({linkData:so({click_type:"left"},e),layers:[t()]})}(e)}}}),Object(r.g)("header",{className:tn.a.header},Object(r.g)(yc,null),Object(r.g)("div",{className:"js-sticky-container bf-sticky-container",ref:function(e){return n.stickyContainer=e}},Object(r.g)(sc,{navItems:o,showMoreNav:t.showMoreNav,isSticking:t.isSticking,allTopics:o.topics},Object(r.g)(Fo,{onClick:this.toggleMoreNav,"aria-label":c.hamburger},t.showMoreNav?Object(r.g)(wc,{width:22,height:22,"aria-hidden":!0}):Object(r.g)(jc,{width:16,height:12,"aria-hidden":!0})),Object(r.g)("div",{className:"".concat(tn.a.logoWrapper," ").concat(t.isSticking?tn.a.isSticking:"")},Object(r.g)(zc,{edition:a})),Object(r.g)("div",{className:"".concat(tn.a.navIconToggles," ").concat(t.userInfo?tn.a.hasUserInfo:"")},!u&&Object(r.g)(nl,{isLoggedIn:!!t.userInfo,edition:a}),t.showPromoBtn&&Object(r.g)(Er,{id:"nav-membership-btn",href:"".concat(i.bf_url,"/buzzfeedstaff/buzzfeed-membership?utm_source=buzzfeed&utm_medium=web&utm_campaign=nav"),className:"".concat(tn.a.navMembershipLink," ").concat(t.isSticking?tn.a.isSticking:""),label:"membership",location:"membership"},Object(r.g)("span",{className:tn.a.wavingHand,role:"img","aria-label":"Waving Hand Sign"},"ğŸ‘‹"),"got a sec?"),Object(r.g)("div",{className:tn.a.newslettersWrapper},Object(r.g)(Ac,null)),Object(r.g)("div",{className:tn.a.searchWrapper},Object(r.g)(as,null)),t.userMenuEnabled?Object(r.g)(Xu,{isVisible:t.showNotifMenu,notifMenuEnabled:t.notifMenuEnabled,userOptionsEnabled:t.userOptionsEnabled,tooltipDismissed:t.tooltipDismissed,userInfo:t.userInfo,signInPromptEnabled:t.signInPromptEnabled,teaserNotifications:t.teaserNotifications,onUserMenuClicked:this.toggleNotifMenu,setShowNotifToolotip:function(e){return n.setShowNotifToolotip(e)},onNotifTooltipClicked:function(){return n.toggleNotifMenu({forceVisible:!0})},onLogoutClicked:this.onLogout,bfURL:i.bf_url,edition:a,logoutXsrf:t.logoutXsrf}):Object(r.g)(Fr,{id:"nav-login-btn",onClick:this.openModal,className:tn.a.navLoginLink},c.log_in)))),Object(r.g)(Ja,{isSticking:t.isSticking,topics:o.topics,isSecondNav:!0}),t.showNewsletterSignup&&Object(r.g)(Rs,{edition:a,onHide:function(){return n.setShowNewsletterSignup(!1)},track:{contentAction:_o}}),t.showMoreNav&&Object(r.g)(Uo,{top:t.overlayOffset,onHide:this.toggleMoreNav}),t.showNotifMenu&&Object(r.g)(Uo,{top:t.overlayOffset,onHide:this.toggleNotifMenu}))))))}}])&&cl(t.prototype,n),o&&cl(t,o),a}(r.a);function hl(e){Object(r.j)(Object(r.g)(dl,window.BZFD.__HEADER_STATE__),e,e.firstElementChild),delete window.BZFD.__HEADER_STATE__}if("undefined"!=typeof window){document.cookie="bfauth_session=;Max-Age=-99999";var gl=document.querySelector("#".concat("js-header-container"));gl&&window.BZFD&&window.BZFD.__HEADER_STATE__?hl(gl):window.addEventListener("DOMContentLoaded",(function(){hl(gl=document.querySelector("#".concat("js-header-container")))}))}},function(e,t,n){"use strict";n.r(t),n.d(t,"externalClick",(function(){return De})),n.d(t,"internalClick",(function(){return Re})),n.d(t,"contentAction",(function(){return Be})),n.d(t,"impression",(function(){return Fe})),n.d(t,"abTest",(function(){return Ue}));var r=n(10),o=n.n(r),i=n(15),a=n.n(i),c=n(16),s=n.n(c),u=n(50),l=n.n(u),f=n(51),p=n.n(f),d=n(24),h=n.n(d),g=n(52),m=n.n(g),b=n(18),y=n.n(b),_=n(14),v=n.n(_),w=n(22),O=n.n(w);function k(e,t){var n=e.match(t);return n&&n.length?n[0]:null}var j={getBuzzfeedSubdomainOrWildcard:function(e){var t=k(e,"(dev|stage|www).(buzzfeed|buzzfeednews).(com|io)$");return t||k(e,".?[a-z]+.[a-z]+$")},get:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n="".concat(e,"=");if("undefined"==typeof document)return t;for(var r=document.cookie.split(";"),o=0;o<r.length;o++){for(var i=r[o];" "===i.charAt(0);)i=i.substring(1,i.length);if(0===i.indexOf(n))return i.substring(n.length,i.length)}return t},set:function(e){var t=e.name,n=e.value,r=e.days,o=e.domain,i="";if(r){var a=new Date;a.setTime(a.getTime()+24*r*60*60*1e3),i="; expires=".concat(a.toGMTString())}var c="";return void 0!==o&&(c="; domain=".concat(o)),document.cookie="".concat(t,"=").concat(n).concat(i).concat(c,"; path=/")},remove:function(e,t){return this.set({name:e,value:"",days:-1,domain:t})}};var C=function(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(function(e){return(e^function(){try{return crypto.getRandomValues(new Uint8Array(1))[0]}catch(e){return 255*Math.random()}}()&15>>e/4).toString(16)}))};function S(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=h()(e);if(t){var o=h()(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return p()(this,n)}}function x(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function N(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?x(Object(n),!0).forEach((function(t){v()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):x(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var E=function(e){return String(e).replace(/[^\w\s-_/]/g,"").replace(/\s+/g,"_").toLowerCase()},P=function(e){return e.map((function(e){return"undefined"===e?"":e}))},T=function(e){return null===e||isNaN(Number(e))?null:Number(e)},I=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e&&Array.isArray(e)?e:t},z=function(){var e=O()(y.a.mark((function e(t){var n,r,o,i,a,c;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=t.context,r=t.layers,o=void 0===r?[]:r,i={},a=0;case 3:if(!(a<o.length)){e.next=13;break}if("function"!=typeof(c=o[a])){e.next=9;break}return e.next=8,c(n);case 8:c=e.sent;case 9:i=N(N({},i),c);case 10:a++,e.next=3;break;case 13:return e.abrupt("return",i);case 14:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),L=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.client_id_cookie_name,n=void 0===t?"_amp_pd":t,r=e.expires,o=void 0===r?365:r,i=j.get(n);if(!i){var a=window.location.hostname.split(".").splice(-2,2).join(".");i="amp-".concat(C()),j.set({name:n,value:i,days:o,domain:a})}return i},M=function(e){l()(n,e);var t=S(n);function n(e){var r;return s()(this,n),(r=t.call(this,e)).name="ClientEventSchemaLayerError",r}return a()(n)}(m()(Error)),A=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return new M('Missing required field: "'.concat(e,'"'))},D=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.debug,r=void 0!==n&&n,i=Object.entries(e).filter((function(e){var t=o()(e,2)[1];return!(t instanceof M)||(r&&console.warn(t),!1)})),a=i.reduce((function(e,t){var n=o()(t,2),r=n[0],i=n[1];return e[r]=i,e}),{});return a},R=function(e){var t=e.context_page_id,n=void 0===t?A("context_page_id"):t,r=e.context_page_type,o=void 0===r?A("context_page_type"):r,i=e.destination,a=void 0===i?A("destination"):i;return function(){return{context_page_id:String(n),context_page_type:o,destination:a}}},B=function(e){var t=e.item_name,n=void 0===t?A("item_name"):t,r=e.item_type,o=void 0===r?A("item_type"):r,i=e.position_in_subunit,a=e.position_in_unit;return function(){return{item_name:String(n),item_type:o,position_in_subunit:T(i),position_in_unit:T(a)}}},F=function(e){var t=e.subunit_name,n=void 0===t?"":t,r=e.subunit_type,o=void 0===r?"":r;return function(){return{subunit_name:E(n.toString()),subunit_type:o}}},U=function(e){var t=e.unit_name,n=void 0===t?A("unit_name"):t,r=e.unit_type,o=void 0===r?A("unit_type"):r;return function(){return{unit_type:o,unit_name:E(n)}}},H={flush:!0,required_layers:[R,function(e){var t=e.target_content_url,n=void 0===t?A("target_content_url"):t;return function(){return{target_content_url:n}}},B,F,U],type:"web_external_link"},W=function(e){var t=e.data_source_algorithm,n=e.data_source_algorithm_version,r=e.data_source_name,o=void 0===r?"":r;return function(){return{data_source_algorithm:P(I(t)),data_source_algorithm_version:P(I(n)),data_source_name:decodeURIComponent(o)}}},V=function(e){var t=e.item_name,n=void 0===t?A("item_name"):t,r=e.target_content_id,o=void 0===r?n:r,i=e.target_content_type,a=void 0===i?A("target_content_type"):i;return function(){return{target_content_id:String(o),target_content_type:a}}},q={flush:!0,required_layers:[R,W,V,B,F,U],type:"web_internal_link"},G={flush:!0,required_layers:[function(e){var t=e.action_type,n=void 0===t?A("action_type"):t,r=e.action_value,o=void 0===r?A("action_value"):r;return function(){return{action_type:n,action_value:o.toString()}}},R,B,F,U],type:"web_content_action"},K={required_layers:[R,W,V,B,F,U],type:"web_impression"},Z={required_layers:[R,function(e){var t=e.experiment_id,n=void 0===t?A("experiment_id"):t;return{experiment_id:I(n)}}],type:"web_ab_test"},X={isEqual:!0,isMatchingKey:!0,isPromise:!0,maxSize:!0,onCacheAdd:!0,onCacheChange:!0,onCacheHit:!0,transformKey:!0},Y=Array.prototype.slice;function $(e){var t=e.length;return t?1===t?[e[0]]:2===t?[e[0],e[1]]:3===t?[e[0],e[1],e[2]]:Y.call(e,0):[]}function J(e,t){return e===t||e!=e&&t!=t}function Q(e,t){var n={};for(var r in e)n[r]=e[r];for(var r in t)n[r]=t[r];return n}var ee=function(){function e(e){this.keys=[],this.values=[],this.options=e;var t="function"==typeof e.isMatchingKey;t?this.getKeyIndex=this._getKeyIndexFromMatchingKey:e.maxSize>1?this.getKeyIndex=this._getKeyIndexForMany:this.getKeyIndex=this._getKeyIndexForSingle,this.canTransformKey="function"==typeof e.transformKey,this.shouldCloneArguments=this.canTransformKey||t,this.shouldUpdateOnAdd="function"==typeof e.onCacheAdd,this.shouldUpdateOnChange="function"==typeof e.onCacheChange,this.shouldUpdateOnHit="function"==typeof e.onCacheHit}return Object.defineProperty(e.prototype,"size",{get:function(){return this.keys.length},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"snapshot",{get:function(){return{keys:$(this.keys),size:this.size,values:$(this.values)}},enumerable:!0,configurable:!0}),e.prototype._getKeyIndexFromMatchingKey=function(e){var t=this.options,n=t.isMatchingKey,r=t.maxSize,o=this.keys,i=o.length;if(!i)return-1;if(n(o[0],e))return 0;if(r>1)for(var a=1;a<i;a++)if(n(o[a],e))return a;return-1},e.prototype._getKeyIndexForMany=function(e){var t=this.options.isEqual,n=this.keys,r=n.length;if(!r)return-1;if(1===r)return this._getKeyIndexForSingle(e);var o,i,a=e.length;if(a>1){for(var c=0;c<r;c++)if((o=n[c]).length===a){for(i=0;i<a&&t(o[i],e[i]);i++);if(i===a)return c}}else for(c=0;c<r;c++)if((o=n[c]).length===a&&t(o[0],e[0]))return c;return-1},e.prototype._getKeyIndexForSingle=function(e){var t=this.keys;if(!t.length)return-1;var n=t[0],r=n.length;if(e.length!==r)return-1;var o=this.options.isEqual;if(r>1){for(var i=0;i<r;i++)if(!o(n[i],e[i]))return-1;return 0}return o(n[0],e[0])?0:-1},e.prototype.orderByLru=function(e,t,n){for(var r=this.keys,o=this.values,i=r.length,a=n;a--;)r[a+1]=r[a],o[a+1]=o[a];r[0]=e,o[0]=t;var c=this.options.maxSize;i===c&&n===i?(r.pop(),o.pop()):n>=c&&(r.length=o.length=c)},e.prototype.updateAsyncCache=function(e){var t=this,n=this.options,r=n.onCacheChange,o=n.onCacheHit,i=this.keys[0],a=this.values[0];this.values[0]=a.then((function(n){return t.shouldUpdateOnHit&&o(t,t.options,e),t.shouldUpdateOnChange&&r(t,t.options,e),n}),(function(e){var n=t.getKeyIndex(i);throw-1!==n&&(t.keys.splice(n,1),t.values.splice(n,1)),e}))},e}();var te=function e(t,n){if(void 0===n&&(n={}),function(e){return"function"==typeof e&&e.isMemoized}(t))return e(t.fn,Q(t.options,n));if("function"!=typeof t)throw new TypeError("You must pass a function to `memoize`.");var r=n.isEqual,o=void 0===r?J:r,i=n.isMatchingKey,a=n.isPromise,c=void 0!==a&&a,s=n.maxSize,u=void 0===s?1:s,l=n.onCacheAdd,f=n.onCacheChange,p=n.onCacheHit,d=n.transformKey,h=Q({isEqual:o,isMatchingKey:i,isPromise:c,maxSize:u,onCacheAdd:l,onCacheChange:f,onCacheHit:p,transformKey:d},function(e){var t={};for(var n in e)X[n]||(t[n]=e[n]);return t}(n)),g=new ee(h),m=g.keys,b=g.values,y=g.canTransformKey,_=g.shouldCloneArguments,v=g.shouldUpdateOnAdd,w=g.shouldUpdateOnChange,O=g.shouldUpdateOnHit,k=function e(){var n=_?$(arguments):arguments;y&&(n=d(n));var r=m.length?g.getKeyIndex(n):-1;if(-1!==r)O&&p(g,h,e),r&&(g.orderByLru(m[r],b[r],r),w&&f(g,h,e));else{var o=t.apply(this,arguments),i=_?n:$(arguments);g.orderByLru(i,o,m.length),c&&g.updateAsyncCache(e),v&&l(g,h,e),w&&f(g,h,e)}return b[0]};return k.cache=g,k.fn=t,k.isMemoized=!0,k.options=h,k};function ne(e){return(e+="").indexOf("#")>-1?e.substr(e.indexOf("#"),e.length):""}function re(e){return(e+="").indexOf("#")>-1?e.substr(0,e.indexOf("#")):e}function oe(e){return e.indexOf("?")>-1}function ie(e){return e=function(e){if(!oe(e))return e;var t=ne(e);return(e=re(e)).substr(0,e.indexOf("?"))+t}(e=re(e))}var ae=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window,t=e.location.hostname,n=["buzzfeed","buzzfeednews","tasty","huffpost","huffingtonpost"],r=t.split(".")[0];return r&&"stage"!==r&&-1===n.indexOf(r)&&t.split(".").length>=3&&(t=t.substring(r.length+1)),t};function ce(e,t,n){var r,o,i,a,c,s,u;for(this.version="1.0.1",this.minAlphabetLength=16,this.sepDiv=3.5,this.guardDiv=12,this.errorAlphabetLength="error: alphabet must contain at least X unique characters",this.errorAlphabetSpace="error: alphabet cannot contain spaces",this.alphabet="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",this.seps="cfhistuCFHISTU",this.minHashLength=parseInt(t,10)>0?t:0,this.salt="string"==typeof e?e:"","string"==typeof n&&(this.alphabet=n),r="",o=0,a=this.alphabet.length;o!==a;o++)-1===r.indexOf(this.alphabet[o])&&(r+=this.alphabet[o]);if(this.alphabet=r,this.alphabet.length<this.minAlphabetLength)throw this.errorAlphabetLength.replace("X",this.minAlphabetLength);if(-1!==this.alphabet.search(" "))throw this.errorAlphabetSpace;for(o=0,a=this.seps.length;o!==a;o++)-1===(i=this.alphabet.indexOf(this.seps[o]))?this.seps=this.seps.substr(0,o)+" "+this.seps.substr(o+1):this.alphabet=this.alphabet.substr(0,i)+" "+this.alphabet.substr(i+1);this.alphabet=this.alphabet.replace(/ /g,""),this.seps=this.seps.replace(/ /g,""),this.seps=this.consistentShuffle(this.seps,this.salt),(!this.seps.length||this.alphabet.length/this.seps.length>this.sepDiv)&&(1===(c=Math.ceil(this.alphabet.length/this.sepDiv))&&c++,c>this.seps.length?(s=c-this.seps.length,this.seps+=this.alphabet.substr(0,s),this.alphabet=this.alphabet.substr(s)):this.seps=this.seps.substr(0,c)),this.alphabet=this.consistentShuffle(this.alphabet,this.salt),u=Math.ceil(this.alphabet.length/this.guardDiv),this.alphabet.length<3?(this.guards=this.seps.substr(0,u),this.seps=this.seps.substr(u)):(this.guards=this.alphabet.substr(0,u),this.alphabet=this.alphabet.substr(u))}ce.prototype.encode=function(){var e,t,n="",r=Array.prototype.slice.call(arguments);if(!r.length)return n;for(r[0]instanceof Array&&(r=r[0]),e=0,t=r.length;e!==t;e++)if("number"!=typeof r[e]||r[e]%1!=0||r[e]<0)return n;return this._encode(r)},ce.prototype.decode=function(e){return e.length&&"string"==typeof e?this._decode(e,this.alphabet):[]},ce.prototype.encodeHex=function(e){var t,n,r;if(e=e.toString(),!/^[0-9a-fA-F]+$/.test(e))return"";for(t=0,n=(r=e.match(/[\w\W]{1,12}/g)).length;t!==n;t++)r[t]=parseInt("1"+r[t],16);return this.encode.apply(this,r)},ce.prototype.decodeHex=function(e){var t,n,r=[],o=this.decode(e);for(t=0,n=o.length;t!==n;t++)r+=o[t].toString(16).substr(1);return r},ce.prototype._encode=function(e){var t,n,r,o,i,a,c,s,u,l,f,p=this.alphabet,d=e.length,h=0;for(r=0,o=e.length;r!==o;r++)h+=e[r]%(r+100);for(n=t=p[h%p.length],r=0,o=e.length;r!==o;r++)i=e[r],a=n+this.salt+p,p=this.consistentShuffle(p,a.substr(0,p.length)),t+=c=this.hash(i,p),r+1<d&&(s=(i%=c.charCodeAt(0)+r)%this.seps.length,t+=this.seps[s]);for(t.length<this.minHashLength&&(u=(h+t[0].charCodeAt(0))%this.guards.length,(t=this.guards[u]+t).length<this.minHashLength&&(u=(h+t[2].charCodeAt(0))%this.guards.length,t+=this.guards[u])),l=parseInt(p.length/2,10);t.length<this.minHashLength;)(f=(t=(p=this.consistentShuffle(p,p)).substr(l)+t+p.substr(0,l)).length-this.minHashLength)>0&&(t=t.substr(f/2,this.minHashLength));return t},ce.prototype._decode=function(e,t){var n,r,o,i,a=[],c=0,s=new RegExp("["+this.guards+"]","g"),u=e.replace(s," "),l=u.split(" ");if(3!==l.length&&2!==l.length||(c=1),void 0!==(u=l[c])[0]){for(n=u[0],u=u.substr(1),s=new RegExp("["+this.seps+"]","g"),c=0,r=(l=(u=u.replace(s," ")).split(" ")).length;c!==r;c++)o=l[c],i=n+this.salt+t,t=this.consistentShuffle(t,i.substr(0,t.length)),a.push(this.unhash(o,t));this._encode(a)!==e&&(a=[])}return a},ce.prototype.consistentShuffle=function(e,t){var n,r,o,i,a,c;if(!t.length)return e;for(i=e.length-1,a=0,c=0;i>0;i--,a++)c+=n=t[a%=t.length].charCodeAt(0),o=e[r=(n+a+c)%i],e=(e=e.substr(0,r)+e[i]+e.substr(r+1)).substr(0,i)+o+e.substr(i+1);return e},ce.prototype.hash=function(e,t){var n="",r=t.length;do{n=t[e%r]+n,e=parseInt(e/r,10)}while(e);return n},ce.prototype.unhash=function(e,t){var n,r=0;for(n=0;n<e.length;n++)r+=t.indexOf(e[n])*Math.pow(t.length,e.length-n-1);return r};function se(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ue(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?se(Object(n),!0).forEach((function(t){v()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):se(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var le=parseInt(1e10*Math.random(),10),fe=C(),pe=function(e){if(0!==e.indexOf(".")){var t=/[0-9A-Za-z]+/.exec(e);return null!==t&&t[0]===e&&parseInt(e,36)}var n=e.substr(1,2);return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.salt,r=void 0===n?null:n;return new ce(r).decode(e)[0]}(e.substr(3),{salt:n})},de=function(e){var t=parseInt(25*Math.random()+10,10).toString(36)+parseInt(25*Math.random()+10,10).toString(36);return".".concat(t).concat(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.salt,r=void 0===n?null:n,o=t.length,i=void 0===o?32:o;return new ce(r,i).encode(e)}(e,{salt:t,length:0}))},he=function(e){var t=decodeURIComponent(e).split("&").map((function(e){return e.split("=")})).reduce((function(e,t){var n=o()(t,2),r=n[0],i=n[1];return ue(ue({},e),{},v()({},r,i))}),{}),n=t.u,r=t.uuid;return{legacyIdentifier:pe(n||""),identifier:r}},ge=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.legacy,n=void 0!==t&&t,r={name:"bf_visit",days:1e4,domain:ae()},o=j.get(r.name),i=he(o),a=i.legacyIdentifier,c=i.identifier,s=de(le);return n?a||(j.set(ue(ue({},r),{},{value:encodeURIComponent("u=".concat(s,"&uuid=").concat(c||fe,"&v=2"))})),le):c||a?c||String(a):(j.set(ue(ue({},r),{},{value:encodeURIComponent("u=".concat(s,"&uuid=").concat(fe,"&v=2"))})),fe)};function me(e){return e+"|expiration"}var be=function(){try{return localStorage.setItem("ls_test",!0),localStorage.removeItem("ls_test"),!0}catch(e){return!1}}(),ye={set:function(e){be&&void 0!==e&&(localStorage.setItem(e.key,e.value),e.expires&&localStorage.setItem(me(e.key),Date.now()+e.expires))},get:function(e){return be?function(e){var t=localStorage.getItem(me(e));return t&&t<=Date.now()}(e)?(this.remove(e),null):localStorage.getItem(e):null},sessionSet:function(e){be&&void 0!==e&&sessionStorage.setItem(e.key,e.value)},sessionGet:function(e){return be?sessionStorage.getItem(e):null},remove:function(e){be&&(localStorage.removeItem(me(e)),localStorage.removeItem(e))},clear:function(){be&&localStorage.clear()}},_e={dev:{tracking_url:"https://nsq-api-public.dev.buzzfeed.io",debug:!0},test:{tracking_url:"https://nsq-api-public.dev.buzzfeed.io",debug:!0},stage:{tracking_url:"https://pixiedust-stage.buzzfeed.com",debug:!0},prod:{tracking_url:"https://pixiedust.buzzfeed.com",debug:!1},"app-west":{tracking_url:"https://pixiedust.buzzfeed.com",debug:!1}},ve=function(e){var t=ye.get("cet-page_session_id");if(window.CLIENT_EVENT_TRACKING=window.CLIENT_EVENT_TRACKING||{},window.CLIENT_EVENT_TRACKING.current_page_session_url===e&&t)return{page_session_id:t,previous_page_session_id:ye.get("pdv3-previous_page_session_id")||""};window.CLIENT_EVENT_TRACKING.current_page_session_url=e,t=C()||"00000000-0000-0000-0000-000000000000";var n=ye.get("cet-page_session_id")||"";return ye.set({key:"cet-page_session_id",value:t}),ye.set({expires:18e5,key:"pdv3-previous_page_session_id",value:n}),{page_session_id:t,previous_page_session_id:n}};function we(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Oe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?we(Object(n),!0).forEach((function(t){v()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):we(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ke=te((function(e){var t=e.url,n={name:"bf-xdomain-session-uuid",days:.5/24,domain:ae()};return te((function(){var e=String(ge()),r=j.get(n.name,C());return j.set(Oe(Oe({},n),{},{value:r})),Oe({client_uuid:e,client_session_id:r,random_user_uuid:j.get("user_uuid","unauthenticated"),referrer_uri:document.referrer},ve(ie(t)))}),{transformKey:JSON.stringify})}),{transformKey:function(e){return ie(o()(e,1)[0].url)}}),je=function(){return function(){return{event_uri:document.URL,event_ts:Math.round(Date.now()/1e3),event_uuid:C()}}},Ce=n(23),Se=n.n(Ce),xe=["track_amp"],Ne=te((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.track_amp,n=void 0!==t&&t,r=Se()(e,xe);return n?te((function(){return{amp_id:L(r)}}),{transformKey:JSON.stringify}):{}}),{transformKey:JSON.stringify}),Ee=function(e){var t=e.page_edition,n=void 0===t?A("page_edition"):t;return function(){return{mode:window.matchMedia("screen and (max-width:51.9rem)").matches?"mobile":"desktop",page_edition:n,viewport_size:{width:Number(window.screen.width),height:Number(window.screen.height)}}}},Pe=function(e){var t=e.type,n=void 0===t?A("type"):t,r=e.source,o=void 0===r?A("source"):r;return function(){return{type:n,source:o}}};function Te(e){var t=e.env,n=void 0===t?"dev":t,r=function(e){var t,n=e.debug,r=e.tracking_url,o=[],i=function(){if(o.length&&"undefined"!=typeof window){var e=JSON.stringify(o),i=document.createEvent("CustomEvent");if(i.initCustomEvent("cet-event",!1,!1,o),dispatchEvent(i),n)window.fetch("".concat(r,"/events"),{method:"POST",body:e,keepalive:!0}).then((function(e){return e.json()})).then((function(e){e.errors&&e.debug&&(console.group("%c ğŸš¨ Rejected client events ğŸš¨","background-color: #250201; color: #ee8783; border: 1px solid #540b06"),e.debug.forEach((function(e){return console.table(e.validation)})),console.groupEnd())})).catch((function(){var t=JSON.parse(e);console.group("%cClient Event Tracking: %crun nsq_api_public to verify events","font-weight: bold;","color: gray; font-size: .5rem;"),t.forEach((function(e){console.groupCollapsed('"%c'.concat(e.type,'"'),"font-weight: bold; font-family: monospace;"),console.table(e),console.groupEnd()})),console.groupEnd()}));else{var a;if(navigator&&navigator.sendBeacon)a=navigator.sendBeacon("".concat(r,"/events"),e);else{var c=new XMLHttpRequest;c.open("POST","".concat(r,"/events"),!1),c.onerror=function(){},c.setRequestHeader("Accept","*/*"),c.setRequestHeader("Content-Type","text/plain;charset=UTF-8"),a=c.send(e)}!a&&window.raven&&Math.random()<.1&&window.raven.captureException("Client Event Tracking: sendBeacon could not process a queue.")}clearTimeout(t),t=null,o=[]}};return[function(e){o.push(e),10===o.length&&i(),t||(t=setTimeout(i,200))},i]}(_e[n]),i=o()(r,2),a=i[0],c=i[1];return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.flush,o=void 0!==r&&r,i=D(e,{debug:_e[n].debug});a(i),o&&c()}}var Ie=n(49),ze=n.n(Ie),Le=["page_edition"];function Me(){var e="true"===j.get("is_bot");if(window.location.search.includes("e2e_test"))try{return{e2e_test:new URLSearchParams(window.location.search).get("e2e_test"),is_bot:!0}}catch(e){}return e?{is_bot:!0}:{}}var Ae=function(e){var t=e.amp_options,n=void 0===t?{}:t,r=e.env,o=e.source;if("undefined"==typeof window)return function(){};var i=Te({env:r});return function(){var e=O()(y.a.mark((function e(t){var a,c,s,u,l,f,p,d,h,g,m,b,_,v,w,O=arguments;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(a=t.flush,c=void 0!==a&&a,s=t.required_layers,u=void 0===s?[]:s,l=t.sample_rate,f=void 0===l?1:l,p=t.type,d=O.length,h=new Array(d>1?d-1:0),g=1;g<d;g++)h[g-1]=O[g];return e.next=4,z({context:{env:r},layers:h});case 4:return m=e.sent,b=m.page_edition,_=Se()(m,Le),v=[Pe({source:o,type:p}),je(),ke({url:document.URL}),Ne(n),Ee({page_edition:b})].concat(h,ze()(u.map((function(e){return e(_)}))),[Me()]),e.next=10,z({context:{env:r},layers:v});case 10:if(w=e.sent,!(window.location.search.includes("e2e_test")||Math.random()<=f)){e.next=14;break}return e.next=14,i(w,{flush:c});case 14:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()}({env:window&&window.clientEventTracking&&window.clientEventTracking.env?window.clientEventTracking.env:"dev",source:"web_bf"}),De=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];Ae.apply(void 0,[H].concat(t))},Re=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];Ae.apply(void 0,[q].concat(t))},Be=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];Ae.apply(void 0,[G].concat(t))},Fe=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];Ae.apply(void 0,[K].concat(t))},Ue=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];Ae.apply(void 0,[Z].concat(t))}},function(e,t,n){"use strict";var r;n.r(t),n.d(t,"Node",(function(){return o})),n.d(t,"DataNode",(function(){return i})),n.d(t,"Text",(function(){return a})),n.d(t,"Comment",(function(){return c})),n.d(t,"ProcessingInstruction",(function(){return s})),n.d(t,"NodeWithChildren",(function(){return u})),n.d(t,"CDATA",(function(){return l})),n.d(t,"Document",(function(){return f})),n.d(t,"Element",(function(){return p})),n.d(t,"isTag",(function(){return d})),n.d(t,"isCDATA",(function(){return h})),n.d(t,"isText",(function(){return g})),n.d(t,"isComment",(function(){return m})),n.d(t,"isDirective",(function(){return b})),n.d(t,"isDocument",(function(){return y})),n.d(t,"hasChildren",(function(){return _})),n.d(t,"cloneNode",(function(){return v})),n.d(t,"DomHandler",(function(){return k})),function(e){e.Root="root",e.Text="text",e.Directive="directive",e.Comment="comment",e.Script="script",e.Style="style",e.Tag="tag",e.CDATA="cdata",e.Doctype="doctype"}(r||(r={}));r.Root,r.Text,r.Directive,r.Comment,r.Script,r.Style,r.Tag,r.CDATA,r.Doctype;class o{constructor(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}get parentNode(){return this.parent}set parentNode(e){this.parent=e}get previousSibling(){return this.prev}set previousSibling(e){this.prev=e}get nextSibling(){return this.next}set nextSibling(e){this.next=e}cloneNode(e=!1){return v(this,e)}}class i extends o{constructor(e){super(),this.data=e}get nodeValue(){return this.data}set nodeValue(e){this.data=e}}class a extends i{constructor(){super(...arguments),this.type=r.Text}get nodeType(){return 3}}class c extends i{constructor(){super(...arguments),this.type=r.Comment}get nodeType(){return 8}}class s extends i{constructor(e,t){super(t),this.name=e,this.type=r.Directive}get nodeType(){return 1}}class u extends o{constructor(e){super(),this.children=e}get firstChild(){var e;return null!==(e=this.children[0])&&void 0!==e?e:null}get lastChild(){return this.children.length>0?this.children[this.children.length-1]:null}get childNodes(){return this.children}set childNodes(e){this.children=e}}class l extends u{constructor(){super(...arguments),this.type=r.CDATA}get nodeType(){return 4}}class f extends u{constructor(){super(...arguments),this.type=r.Root}get nodeType(){return 9}}class p extends u{constructor(e,t,n=[],o=("script"===e?r.Script:"style"===e?r.Style:r.Tag)){super(n),this.name=e,this.attribs=t,this.type=o}get nodeType(){return 1}get tagName(){return this.name}set tagName(e){this.name=e}get attributes(){return Object.keys(this.attribs).map(e=>{var t,n;return{name:e,value:this.attribs[e],namespace:null===(t=this["x-attribsNamespace"])||void 0===t?void 0:t[e],prefix:null===(n=this["x-attribsPrefix"])||void 0===n?void 0:n[e]}})}}function d(e){return(t=e).type===r.Tag||t.type===r.Script||t.type===r.Style;var t}function h(e){return e.type===r.CDATA}function g(e){return e.type===r.Text}function m(e){return e.type===r.Comment}function b(e){return e.type===r.Directive}function y(e){return e.type===r.Root}function _(e){return Object.prototype.hasOwnProperty.call(e,"children")}function v(e,t=!1){let n;if(g(e))n=new a(e.data);else if(m(e))n=new c(e.data);else if(d(e)){const r=t?w(e.children):[],o=new p(e.name,{...e.attribs},r);r.forEach(e=>e.parent=o),null!=e.namespace&&(o.namespace=e.namespace),e["x-attribsNamespace"]&&(o["x-attribsNamespace"]={...e["x-attribsNamespace"]}),e["x-attribsPrefix"]&&(o["x-attribsPrefix"]={...e["x-attribsPrefix"]}),n=o}else if(h(e)){const r=t?w(e.children):[],o=new l(r);r.forEach(e=>e.parent=o),n=o}else if(y(e)){const r=t?w(e.children):[],o=new f(r);r.forEach(e=>e.parent=o),e["x-mode"]&&(o["x-mode"]=e["x-mode"]),n=o}else{if(!b(e))throw new Error("Not implemented yet: "+e.type);{const t=new s(e.name,e.data);null!=e["x-name"]&&(t["x-name"]=e["x-name"],t["x-publicId"]=e["x-publicId"],t["x-systemId"]=e["x-systemId"]),n=t}}return n.startIndex=e.startIndex,n.endIndex=e.endIndex,null!=e.sourceCodeLocation&&(n.sourceCodeLocation=e.sourceCodeLocation),n}function w(e){const t=e.map(e=>v(e,!0));for(let e=1;e<t.length;e++)t[e].prev=t[e-1],t[e-1].next=t[e];return t}const O={withStartIndices:!1,withEndIndices:!1,xmlMode:!1};class k{constructor(e,t,n){this.dom=[],this.root=new f(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof t&&(n=t,t=O),"object"==typeof e&&(t=e,e=void 0),this.callback=null!=e?e:null,this.options=null!=t?t:O,this.elementCB=null!=n?n:null}onparserinit(e){this.parser=e}onreset(){this.dom=[],this.root=new f(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null}onend(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))}onerror(e){this.handleCallback(e)}onclosetag(){this.lastNode=null;const e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)}onopentag(e,t){const n=this.options.xmlMode?r.Tag:void 0,o=new p(e,t,void 0,n);this.addNode(o),this.tagStack.push(o)}ontext(e){const{lastNode:t}=this;if(t&&t.type===r.Text)t.data+=e,this.options.withEndIndices&&(t.endIndex=this.parser.endIndex);else{const t=new a(e);this.addNode(t),this.lastNode=t}}oncomment(e){if(this.lastNode&&this.lastNode.type===r.Comment)return void(this.lastNode.data+=e);const t=new c(e);this.addNode(t),this.lastNode=t}oncommentend(){this.lastNode=null}oncdatastart(){const e=new a(""),t=new l([e]);this.addNode(t),e.parent=t,this.lastNode=e}oncdataend(){this.lastNode=null}onprocessinginstruction(e,t){const n=new s(e,t);this.addNode(n)}handleCallback(e){if("function"==typeof this.callback)this.callback(e,this.dom);else if(e)throw e}addNode(e){const t=this.tagStack[this.tagStack.length-1],n=t.children[t.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),t.children.push(e),n&&(e.prev=n,n.next=e),e.parent=t,this.lastNode=null}}t.default=k},function(e,t,n){"use strict";var r;n.r(t),n.d(t,"Node",(function(){return o})),n.d(t,"DataNode",(function(){return i})),n.d(t,"Text",(function(){return a})),n.d(t,"Comment",(function(){return c})),n.d(t,"ProcessingInstruction",(function(){return s})),n.d(t,"NodeWithChildren",(function(){return u})),n.d(t,"CDATA",(function(){return l})),n.d(t,"Document",(function(){return f})),n.d(t,"Element",(function(){return p})),n.d(t,"isTag",(function(){return d})),n.d(t,"isCDATA",(function(){return h})),n.d(t,"isText",(function(){return g})),n.d(t,"isComment",(function(){return m})),n.d(t,"isDirective",(function(){return b})),n.d(t,"isDocument",(function(){return y})),n.d(t,"hasChildren",(function(){return _})),n.d(t,"cloneNode",(function(){return v})),n.d(t,"DomHandler",(function(){return k})),function(e){e.Root="root",e.Text="text",e.Directive="directive",e.Comment="comment",e.Script="script",e.Style="style",e.Tag="tag",e.CDATA="cdata",e.Doctype="doctype"}(r||(r={}));r.Root,r.Text,r.Directive,r.Comment,r.Script,r.Style,r.Tag,r.CDATA,r.Doctype;class o{constructor(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}get parentNode(){return this.parent}set parentNode(e){this.parent=e}get previousSibling(){return this.prev}set previousSibling(e){this.prev=e}get nextSibling(){return this.next}set nextSibling(e){this.next=e}cloneNode(e=!1){return v(this,e)}}class i extends o{constructor(e){super(),this.data=e}get nodeValue(){return this.data}set nodeValue(e){this.data=e}}class a extends i{constructor(){super(...arguments),this.type=r.Text}get nodeType(){return 3}}class c extends i{constructor(){super(...arguments),this.type=r.Comment}get nodeType(){return 8}}class s extends i{constructor(e,t){super(t),this.name=e,this.type=r.Directive}get nodeType(){return 1}}class u extends o{constructor(e){super(),this.children=e}get firstChild(){var e;return null!==(e=this.children[0])&&void 0!==e?e:null}get lastChild(){return this.children.length>0?this.children[this.children.length-1]:null}get childNodes(){return this.children}set childNodes(e){this.children=e}}class l extends u{constructor(){super(...arguments),this.type=r.CDATA}get nodeType(){return 4}}class f extends u{constructor(){super(...arguments),this.type=r.Root}get nodeType(){return 9}}class p extends u{constructor(e,t,n=[],o=("script"===e?r.Script:"style"===e?r.Style:r.Tag)){super(n),this.name=e,this.attribs=t,this.type=o}get nodeType(){return 1}get tagName(){return this.name}set tagName(e){this.name=e}get attributes(){return Object.keys(this.attribs).map(e=>{var t,n;return{name:e,value:this.attribs[e],namespace:null===(t=this["x-attribsNamespace"])||void 0===t?void 0:t[e],prefix:null===(n=this["x-attribsPrefix"])||void 0===n?void 0:n[e]}})}}function d(e){return(t=e).type===r.Tag||t.type===r.Script||t.type===r.Style;var t}function h(e){return e.type===r.CDATA}function g(e){return e.type===r.Text}function m(e){return e.type===r.Comment}function b(e){return e.type===r.Directive}function y(e){return e.type===r.Root}function _(e){return Object.prototype.hasOwnProperty.call(e,"children")}function v(e,t=!1){let n;if(g(e))n=new a(e.data);else if(m(e))n=new c(e.data);else if(d(e)){const r=t?w(e.children):[],o=new p(e.name,{...e.attribs},r);r.forEach(e=>e.parent=o),null!=e.namespace&&(o.namespace=e.namespace),e["x-attribsNamespace"]&&(o["x-attribsNamespace"]={...e["x-attribsNamespace"]}),e["x-attribsPrefix"]&&(o["x-attribsPrefix"]={...e["x-attribsPrefix"]}),n=o}else if(h(e)){const r=t?w(e.children):[],o=new l(r);r.forEach(e=>e.parent=o),n=o}else if(y(e)){const r=t?w(e.children):[],o=new f(r);r.forEach(e=>e.parent=o),e["x-mode"]&&(o["x-mode"]=e["x-mode"]),n=o}else{if(!b(e))throw new Error("Not implemented yet: "+e.type);{const t=new s(e.name,e.data);null!=e["x-name"]&&(t["x-name"]=e["x-name"],t["x-publicId"]=e["x-publicId"],t["x-systemId"]=e["x-systemId"]),n=t}}return n.startIndex=e.startIndex,n.endIndex=e.endIndex,null!=e.sourceCodeLocation&&(n.sourceCodeLocation=e.sourceCodeLocation),n}function w(e){const t=e.map(e=>v(e,!0));for(let e=1;e<t.length;e++)t[e].prev=t[e-1],t[e-1].next=t[e];return t}const O={withStartIndices:!1,withEndIndices:!1,xmlMode:!1};class k{constructor(e,t,n){this.dom=[],this.root=new f(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof t&&(n=t,t=O),"object"==typeof e&&(t=e,e=void 0),this.callback=null!=e?e:null,this.options=null!=t?t:O,this.elementCB=null!=n?n:null}onparserinit(e){this.parser=e}onreset(){this.dom=[],this.root=new f(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null}onend(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))}onerror(e){this.handleCallback(e)}onclosetag(){this.lastNode=null;const e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)}onopentag(e,t){const n=this.options.xmlMode?r.Tag:void 0,o=new p(e,t,void 0,n);this.addNode(o),this.tagStack.push(o)}ontext(e){const{lastNode:t}=this;if(t&&t.type===r.Text)t.data+=e,this.options.withEndIndices&&(t.endIndex=this.parser.endIndex);else{const t=new a(e);this.addNode(t),this.lastNode=t}}oncomment(e){if(this.lastNode&&this.lastNode.type===r.Comment)return void(this.lastNode.data+=e);const t=new c(e);this.addNode(t),this.lastNode=t}oncommentend(){this.lastNode=null}oncdatastart(){const e=new a(""),t=new l([e]);this.addNode(t),e.parent=t,this.lastNode=e}oncdataend(){this.lastNode=null}onprocessinginstruction(e,t){const n=new s(e,t);this.addNode(n)}handleCallback(e){if("function"==typeof this.callback)this.callback(e,this.dom);else if(e)throw e}addNode(e){const t=this.tagStack[this.tagStack.length-1],n=t.children[t.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),t.children.push(e),n&&(e.prev=n,n.next=e),e.parent=t,this.lastNode=null}}t.default=k}]);
//# sourceMappingURL=app.601f2d19b55ed40fb3a8.js.map