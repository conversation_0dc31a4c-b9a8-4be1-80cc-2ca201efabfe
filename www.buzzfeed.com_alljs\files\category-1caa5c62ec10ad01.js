(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[303],{38078:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/shopping/category",function(){return r(3492)}])},3933:function(e,t,r){"use strict";var n=r(52322),o=r(95363),a=r.n(o),i=r(51762),c=r(66896);function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"===typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){s(e,t,r[t])}))}return e}t.Z=function(e){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:a().featuredPinnedPost,children:(0,n.jsx)("div",{className:a().wrapper,children:(0,n.jsx)(i.t$,u({},e))})}),e.showGoldDivider&&(0,n.jsx)(c.Z,{type:e.dividerType})]})}},78983:function(e,t,r){"use strict";r.d(t,{Z:function(){return v}});var n=r(94776),o=r.n(n),a=r(52322),i=r(2784),c=r(85181),s=r(20969),u=r.n(s),l=r(49277),d=r(50240);var p=r(1642),f=r(51760);function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function _(e,t,r,n,o,a,i){try{var c=e[a](i),s=c.value}catch(u){return void r(u)}c.done?t(s):Promise.resolve(s).then(n,o)}function m(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(e){return function(e){if(Array.isArray(e))return g(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return g(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var v=function(e){var t=e.endpoint,n=e.page,s=void 0===n?1:n,g=e.pageSize,v=void 0===g?36:g,y=e.offset,b=void 0===y?0:y,x=e.origin,w=void 0===x?"":x,j=e.trackingLayer,C=void 0===j?{}:j,O=e.excludeIds,k=void 0===O?[]:O,P=e.hasAds,S=void 0!==P&&P,N=e.filterContent,A=void 0===N?"":N,E=e.setPostsCount,I=e.setProductsCount,W=r(31965),z=W.isWeaverPostsAndSearchProductsEndpoint,T=W.isWeaverEndpoint,Z=W.isCommerceSearchEndpoint,D=W.buildApiUrl,U=W.fetchContent,F=(0,i.useState)(!1),L=F[0],M=F[1],B=(0,i.useState)(s),G=B[0],q=B[1],H=(0,i.useState)([]),R=H[0],V=H[1],Y=(0,i.useState)([]),K=Y[0],Q=Y[1],X=(0,i.useState)(0),$=X[0],J=X[1],ee=(0,i.useState)(!0),te=ee[0],re=ee[1],ne=(0,i.useMemo)((function(){return function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"===typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){m(e,t,r[t])}))}return e}({},C,{subunit_name:"load_more",subunit_type:"component",position_in_subunit:$+1,item_name:"load_more",item_type:"button",action_type:"show",action_value:"more_posts",target_content_type:"submission",target_content_id:"more_posts"})}),[$,C]),oe=function(e){(0,l.Z)("tracking");var t=(0,i.useRef)(null);return(0,i.useEffect)((function(){var r=t.current;if(!r)return function(){};var n=(0,d.aF)(r,{},e);return function(){n()}}),[e]),t}(ne),ae=(0,p.Y)(ne);(0,i.useEffect)((function(){E&&R.length&&E((function(e){return e+R.length}))}),[R]),(0,i.useEffect)((function(){I&&K.length&&I((function(e){return e+K.length}))}),[K]);var ie,ce=(0,i.useCallback)((ie=o().mark((function e(){var r,n,a,i,c,s,u,l,d,p,f;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return M(!0),e.prev=1,r=D({endpoint:t,currentPage:G,pageSize:v}),e.next=5,U(r);case 5:(n=e.sent)?z(t)?((null===n||void 0===n||null===(a=n.posts)||void 0===a||null===(i=a.results)||void 0===i?void 0:i.length)&&V((function(e){return h(e).concat(h(n.posts.results))})),n.products&&Q((function(e){return h(e).concat(h(n.products))})),("posts"===A&&!(null===n||void 0===n||null===(c=n.posts)||void 0===c?void 0:c.next)||"products"===A&&!n.products||!(null===n||void 0===n||null===(s=n.posts)||void 0===s?void 0:s.next)&&!n.products)&&re(!1)):T(t)?(null===n||void 0===n||null===(u=n.results)||void 0===u?void 0:u.length)?(n=n.results.filter((function(e){return!k.includes(e.id)})),V((function(e){return h(e).concat(h(n))}))):re(!1):Z(t)&&((null===n||void 0===n||null===(l=n.results)||void 0===l?void 0:l.buzzes)&&V((function(e){return h(e).concat(h(n.results.buzzes))})),(null===n||void 0===n||null===(d=n.results)||void 0===d?void 0:d.products)&&Q((function(e){return h(e).concat(h(n.results.products))})),(null===n||void 0===n||null===(p=n.results)||void 0===p?void 0:p.buzzes)||(null===n||void 0===n||null===(f=n.results)||void 0===f?void 0:f.products)||re(!1)):re(!1),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),console.log("Error fetching content: ".concat(e.t0)),re(!1);case 13:q(G+1),setTimeout((function(){return M(!1)}),500);case 15:case"end":return e.stop()}}),e,null,[[1,9]])})),function(){var e=this,t=arguments;return new Promise((function(r,n){var o=ie.apply(e,t);function a(e){_(o,r,n,a,i,"next",e)}function i(e){_(o,r,n,a,i,"throw",e)}a(void 0)}))}),[t,G,v,D,U,Z,T,k,z,A]);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.Z,{origin:w,posts:R,products:K,offset:b,trackingLayer:C,hasAds:S,currentPage:G,filterContent:A}),(0,a.jsx)("div",{ref:oe,className:u().wrapper,children:te&&(0,a.jsxs)(f.z,{className:u().loadBtn,onClick:function(){ce(),ae(),J((function(e){return e+1}))},disabled:L,children:["Load More",L&&(0,a.jsx)("span",{className:u().spinner})]})})]})}},1642:function(e,t,r){"use strict";r.d(t,{Y:function(){return s}});var n=r(2784),o=r(49277),a=r(50240);function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function c(e){return function(e){if(Array.isArray(e))return i(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return i(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return i(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.Z)("tracking"),(0,n.useCallback)((function(){a.bC.apply(void 0,[{}].concat(c(t)))}),[t])}},3492:function(e,t,r){"use strict";r.r(t),r.d(t,{Page:function(){return Z},default:function(){return D}});var n=r(94776),o=r.n(n),a=r(52322),i=r(2784),c=r(88306),s=r(30353),u=r(5103),l=r(36595),d=r.n(l),p=r(89835),f=r(3379),g=r(44644),_=r(74577),m=r.n(_),h=function(e){var t=e.className,r=e.title,n=e.color,o=void 0===n?"#323254":n;return(0,a.jsxs)("svg",{className:t,width:"14",height:"8",viewBox:"0 0 14 8",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("title",{children:r}),(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 7.4599L1.20935 1.66925C0.92688 1.38678 0.92688 0.963074 1.20935 0.680603C1.49182 0.398132 1.91553 0.398132 2.198 0.680603L7 5.4826L11.802 0.680603C12.0845 0.398132 12.5082 0.398132 12.7906 0.680603C13.0731 0.963074 13.0731 1.38678 12.7906 1.66925L7 7.4599Z",fill:o})]})},v=r(1642),y=r(72527);function b(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"===typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){b(e,t,r[t])}))}return e}function w(e){var t=e.title,r=e.textColor;return(0,a.jsx)("h1",{className:m().categoryName,style:{color:r},children:t})}function j(e){var t=e.description,r=e.textColor;return(0,a.jsx)("div",{style:{color:r},className:m().categoryDescription,dangerouslySetInnerHTML:{__html:t}})}function C(e){var t=e.showChildren,r=e.children,n=e.parentUrl,o=e.tracking,s=(0,i.useContext)(c.z1).edition,u=r.sort((function(e,t){return e.name<t.name?-1:e.name>t.name?1:0}));return(0,a.jsx)("div",{className:"".concat(m().childrenWrapper," ").concat(t?m().showChildren:""),children:"en-us"===s&&u.map((function(e,t){return(0,a.jsx)(O,{item:e,parentUrl:n,tracking:o,index:t},t)}))})}function O(e){var t=e.item,r=e.parentUrl,n=e.index,o=e.tracking,i="".concat(r,"/").concat(t.link),c=(0,y.v)(x({},o,{subunit_type:"component",subunit_name:"category_sub_categories_list",position_in_subunit:n+1,item_type:"text",item_name:t.name,target_content_type:"url",target_content_id:i,target_content_url:i}));return(0,a.jsx)("div",{className:m().childWrapper,children:(0,a.jsx)("a",{ref:c,href:i,children:t.name})})}function k(e){var t=e.items,r=e.tracking,n=e.textColor,o=(0,i.useContext)(c.z1).baseUrl,s=t.reduce((function(e,t,n){return t&&(n>0&&(o+="/".concat(t.link)),e.push((0,a.jsxs)("nav",{className:m().categoryBreadcrumbs,children:[0===n?"":" / ",(0,a.jsx)(P,{currentUrl:o,index:n,item:t,tracking:r})]},"bc-".concat(n)))),e}),[]);return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:m().breadCrumbs,style:{color:n},children:s})})}function P(e){var t=e.item,r=e.currentUrl,n=e.tracking,o=e.index,i=(0,y.v)(x({},n,{subunit_type:"component",subunit_name:"category_breadcrumbs",position_in_subunit:o+1,item_type:"text",item_name:t.name,target_content_type:"url",target_content_id:r,target_content_url:r}));return(0,a.jsx)("a",{ref:i,href:r,children:t.name})}var S=function(e){var t=e.categories,r=e.setShowOverlay,n=e.showOverlay,o=e.trackingLayer,s=(0,(0,i.useContext)(c.WN).getFeatureFlagValue)("target_shoppy_bot"),u=[{name:"Shopping",link:"shopping"}].concat(t),l=t[t.length-1],d=l.name,p=l.children,f=void 0===p?[]:p,g=l.images,_=l.meta,y=l.text_color,b=l.bg_color,O=(0,i.useState)(!1),P=O[0],S=O[1],N=null===g||void 0===g?void 0:g.header_mobile,A=u.reduce((function(e,t){return t&&(e+="/"+t.link),e}),""),E=x({},o,{subunit_name:"category_header",subunit_type:"component",item_name:"viewAll",item_type:"button",action_type:"show",action_value:"viewAll"}),I=(0,v.Y)(E),W=function(){P?r(!1):(r(!0),I()),S(!P)};return(0,i.useEffect)((function(){!n&&P&&S(!1)}),[n,P]),(0,a.jsx)(a.Fragment,{children:s&&"Target"===d?(0,a.jsx)("div",{style:{height:"650px"},children:(0,a.jsx)("iframe",{border:"0",height:"100%",id:"chatframe",scrolling:"no",src:"/ai-ui/games/targetShoppy-chat",width:"100%"})}):(0,a.jsxs)("div",{className:m().categoryHeader,children:[(0,a.jsxs)("div",{className:m().mainContent,style:{backgroundColor:b},children:[(0,a.jsx)("div",{className:m().bgImageWrapper,children:(0,a.jsx)("img",{className:m().bgImage,src:N})}),(0,a.jsx)("div",{className:m().textWrap,children:(0,a.jsxs)("div",{className:m().text,children:[(0,a.jsx)("div",{className:m().breadCrumbsWrapper,style:{backgroundColor:b},children:(0,a.jsx)(k,{items:u,tracking:o,textColor:y})}),(0,a.jsxs)("div",{className:m().bottomBar,style:{color:y},children:[(0,a.jsx)(w,{title:d,textColor:y}),f.length>0&&(0,a.jsxs)("div",{className:m().showAll,onClick:W,onKeyDown:W,role:"button",tabIndex:0,children:["Showing all ",(0,a.jsx)(h,{color:y,className:"".concat(P?m().rotate:"")})]})]}),(0,a.jsx)(j,{description:_.description,textColor:y})]})})]}),(0,a.jsx)(C,{showChildren:P,parentUrl:A,tracking:o,children:f})]})})},N=r(85181),A=r(78983),E=r(3933),I=r(65786);function W(e,t,r,n,o,a,i){try{var c=e[a](i),s=c.value}catch(u){return void r(u)}c.done?t(s):Promise.resolve(s).then(n,o)}function z(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function T(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"===typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){z(e,t,r[t])}))}return e}function Z(e){var t,r=e.header,n=e.products,o=void 0===n?[]:n,s=e.posts,u=void 0===s?[]:s,l=e.featuredPost,p=e.categories,f=(0,i.useContext)(c.z1),_=f.edition,m=f.tracking,h=p[p.length-1]||{},v=h.origin,y=h.tag,b=h.weaver_feed,x=u.next||o.next,w=(0,i.useState)(!1),j=w[0],C=w[1],O=(0,i.useCallback)((function(){C(!1)}),[C]),k=1,P=(0,i.useMemo)((function(){return T({},m,{unit_name:"shopping_category",unit_type:"feed",position_in_unit:k++})}),[m,k]);return(0,a.jsx)(g.Z,{header:r,setShowOverlay:C,showOverlay:j,children:(0,a.jsxs)("main",{className:d().content,children:[(0,a.jsx)(S,{categories:p,setShowOverlay:C,showOverlay:j,trackingLayer:P}),l&&(0,a.jsx)(E.Z,{buzzId:l.id,href:l.canonical_url,title:l.title,description:l.description,thumbnail:l.images.dblbig,altText:l.altText,showGoldDivider:null===o||void 0===o||null===(t=o.results)||void 0===t?void 0:t.length,tracking:P,origin:"".concat(v,"-fp")}),(0,a.jsx)(N.Z,{isFirst:!0,products:null===o||void 0===o?void 0:o.results,posts:null===u||void 0===u?void 0:u.results,origin:v,trackingLayer:P}),x&&(0,a.jsx)(A.Z,{endpoint:"/shopping/proxy/get-weaver-posts-and-search-products?edition=".concat(_,"&tags=").concat(encodeURIComponent(y),"&feed=").concat(encodeURIComponent(b)),page:2,origin:v,trackingLayer:P}),j?(0,a.jsx)(I.Z,{onOverlayClick:O}):""]})})}Z.getInitialProps=function(){var e,t=(e=o().mark((function e(t){var r,n,a,i,c,u,l,d,g,_,m,h,v,y;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.asPath,n=t.req.get("X-BFMem-Type")===s.ADS_FREE_MEM_TYPE,a=f.Z.parse(t.req)["bf-geo-country"]||"",i=t.query,c=i.pageGroup,u=i.pageType,l=i.edition,d=i.header,g=i.products,_=i.posts,m=i.featuredPost,h=i.categories,v={namespacesRequired:["common"]},y=h[h.length-1],e.abrupt("return",T({},v,{isAdsFreeMember:n,pageConfig:(0,p.getConfig)({pageGroup:c,page:u,edition:l,path:r,pageData:y}),header:d,products:g,posts:_,featuredPost:m,categories:h,userGeo:a}));case 7:case"end":return e.stop()}}),e)})),function(){var t=this,r=arguments;return new Promise((function(n,o){var a=e.apply(t,r);function i(e){W(a,n,o,i,c,"next",e)}function c(e){W(a,n,o,i,c,"throw",e)}i(void 0)}))});return function(e){return t.apply(this,arguments)}}();var D=(0,u.withTranslation)("common")(Z)},74577:function(e){e.exports={categoryHeader:"category_header_categoryHeader__HwOzG",bgImageWrapper:"category_header_bgImageWrapper__iEtEM",bgImage:"category_header_bgImage__C__x0",breadCrumbsWrapper:"category_header_breadCrumbsWrapper__OFUy2",textWrap:"category_header_textWrap__WQfV2",categoryBreadcrumbs:"category_header_categoryBreadcrumbs__6s8VK",breadCrumbs:"category_header_breadCrumbs__hsYqm",categoryName:"category_header_categoryName__DI2zl",categoryDescription:"category_header_categoryDescription__ISTwO",showAll:"category_header_showAll__GT523",bottomBar:"category_header_bottomBar__oaot3",rotate:"category_header_rotate__EoeW5",childrenWrapper:"category_header_childrenWrapper__ld6ss",showChildren:"category_header_showChildren__VOSdf",childWrapper:"category_header_childWrapper__KvGuv",mainContent:"category_header_mainContent__Q0VH1",text:"category_header_text__daKon"}},95363:function(e){e.exports={featuredPinnedPost:"featured_post_featuredPinnedPost__60s_3",wrapper:"featured_post_wrapper__WkS5k"}},20969:function(e){e.exports={wrapper:"load_more_wrapper__woNHu",loadBtn:"load_more_loadBtn__qqSZd",spinner:"load_more_spinner__PaZfN",spin:"load_more_spin__wmxwQ"}}},function(e){e.O(0,[182,201,338,862,939,762,774,888,179],(function(){return t=38078,e(e.s=t);var t}));var t=e.O();_N_E=t}]);
//# sourceMappingURL=category-1caa5c62ec10ad01.js.map