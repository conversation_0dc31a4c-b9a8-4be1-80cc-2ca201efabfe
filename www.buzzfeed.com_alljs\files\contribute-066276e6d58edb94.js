(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[376],{34973:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/community/contribute",function(){return n(95327)}])},19856:function(e,t,n){"use strict";var i=n(52322),r=n(60565),a=n(65246),o=n.n(a);function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),i.forEach((function(t){l(e,t,n[t])}))}return e}t.Z=function(e){var t=e.name,n=e.url,a=e.btnStyles,l=e.icon,s=void 0===l?"":l,d=e.trackingInfo,u=void 0===d?{}:d,m=e.trackingInfoExternalLink,f=void 0===m?{}:m,p=e.target,g=void 0===p?"":p,_=e.onSignInClick,h=(0,r.E)(),L=h.trackInternalLink,x=h.trackExternalLink,v="function"===typeof _;return(0,i.jsxs)("a",c({onClick:function(){v?_(c({item_type:"button"},u)):0!==Object.keys(f).length?x(c({context_page_type:"feed",item_type:"button",unit_name:"main"},f)):L(c({context_page_type:"feed",item_type:"button"},u))},className:"".concat(o().loginBtn," ").concat(o()[a])},!v&&{target:g,href:n,rel:"noreferrer"},{children:[s&&(0,i.jsx)("div",{className:o().loginIcon,children:s}),(0,i.jsx)("p",{className:o().loginBtnText,children:t})]}))}},59424:function(e,t,n){"use strict";var i=n(52322),r=n(63953),a=n.n(r),o=n(65267),l=n.n(o),c=n(19856),s=n(34369),d=n(51383),u=n(91987),m=n(10467);function f(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),i.forEach((function(t){f(e,t,n[t])}))}return e}t.Z=function(e){var t=e.isUserAuth;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:a().footerWrapper,children:(0,i.jsxs)("div",{className:a().footer,children:[!t&&(0,i.jsxs)("div",{className:a().loginCol,children:[(0,i.jsx)("h2",{className:a().sectionTitle,children:"Sign up and start creating"}),(0,i.jsx)("div",{className:a().loginBtns,children:m.f$.map((function(e){return(0,i.jsx)("div",{className:a().btnSize,children:(0,i.jsx)(c.Z,{name:e.name,url:e.url,btnStyles:e.btnStyle,icon:e.icon,trackingInfo:p({},e.trackingInfo,{unit_name:"bottom",target_content_type:"auth",target_content_id:"sign_in"})})},e.name)}))})]}),(0,i.jsxs)("div",{className:a().FAQCol,children:[(0,i.jsxs)("div",{className:a().FAQSection,children:[(0,i.jsx)(u.Z,{title:"FAQs",titleSmall:!0}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:(0,i.jsx)(s.Z,{link:"https://www.buzzfeed.com/annakopsky/internet-points-2019",name:"What are Internet Points?"})}),(0,i.jsx)("li",{children:(0,i.jsx)(s.Z,{link:"https://www.buzzfeed.com/annakopsky/a-teachers-guide-to-using-buzzfeed-community-cmbif6i95p",name:"What if I\u2019m a professor/teacher/student that wants to use Community for educational purposes?"})})]})]}),(0,i.jsxs)("div",{className:a().FAQSection,children:[(0,i.jsx)(u.Z,{title:"Contact us",titleSmall:!0}),(0,i.jsx)(s.Z,{link:"mailto: <EMAIL>",name:"<EMAIL>"}),(0,i.jsx)("br",{}),(0,i.jsx)("div",{className:a().googleFormLink,children:(0,i.jsx)(s.Z,{link:"https://forms.gle/vD3MT2vRdPb7B62b6",name:"Join our user research program to provide your feedback and earn gift cards!"})})]}),(0,i.jsx)("div",{className:a().FAQSection,children:(0,i.jsx)(d.Z,{titleSmall:!0,fillColor:"#C6C8EE",twitterColorDefault:!1})})]})]})}),(0,i.jsx)("div",{className:a().wrapperBg,children:(0,i.jsx)("div",{className:a().background,style:{backgroundImage:"url(".concat(l(),")")}})})]})}},92893:function(e,t,n){"use strict";n.d(t,{L:function(){return a}});var i=n(2784),r=n(60565);function a(e){var t=(0,r.E)().trackTimeSpent;(0,i.useEffect)((function(){return t(e)}),[])}},95327:function(e,t,n){"use strict";n.r(t),n.d(t,{MakeAQuizPage:function(){return ee},__N_SSP:function(){return $},default:function(){return te}});var i=n(52322),r=n(2784),a=n(53407),o=n(3379),l=n(30353),c=n(60565),s=n(92893),d=n(74104),u=n(31066),m=n(37963),f=n(97143),p=n(20708),g=n(19856),_=n(62816),h=n.n(_),L=function(e){var t=e.id,n=e.graphicSvg,r=e.graphicStyles,a=e.borderColor,o=e.sectionWidth,l=e.children;return(0,i.jsx)("div",{id:t,className:h().wrapper,children:(0,i.jsxs)("div",{className:"".concat(h().sectionContainer," ").concat(h()[a]),children:[(0,i.jsx)("div",{className:h()[r],children:n}),(0,i.jsx)("div",{className:"".concat(h().sectionWrapper," ").concat(h()[o]),children:l})]})})},x=n(89663),v=n.n(x),j=function(e){var t=e.id,n=e.children;return(0,i.jsx)("div",{id:t,className:v().wrapperCreateSection,children:(0,i.jsx)("div",{className:v().createSectionContainer,children:n})})},y=n(9217),C=n.n(y),b=n(34369),k=function(e){var t=e.sectionTitle,n=e.orderStyles,r=void 0===n?"":n,a=e.desc,o=e.btnName,l=void 0===o?"":o,c=e.target,s=void 0===c?"":c,d=e.btnUrl,u=void 0===d?"":d,m=e.btnStyles,f=void 0===m?"":m,p=e.link,_=void 0===p?"":p,h=e.linkName,L=void 0===h?"":h,x=e.trackingInfo,v=e.trackingInfoExternalLink,j=void 0===v?{}:v,y=e.linkTrackingInfo,k=void 0===y?{}:y,E=e.onSignInClick;return(0,i.jsxs)("div",{className:"".concat(C().textCol," ").concat(C()[r]),children:[(0,i.jsx)("h2",{className:C().sectionTitle,children:t}),(0,i.jsxs)("div",{className:C().description,children:[a,l&&(0,i.jsxs)("div",{className:C().createSectionButtonWrapper,children:[(0,i.jsx)(g.Z,{name:l,target:s,url:u,btnStyles:f||"loginEmailBtnCreate",trackingInfo:x,trackingInfoExternalLink:j,onSignInClick:E}),(0,i.jsx)(b.Z,{link:_,name:L,linkTrackingInfo:k})]})]})]})},E=n(19188),w=n.n(E),S=function(e){var t=e.orderStyles,n=void 0===t?"":t,r=e.imgSrc,a=e.altText,o=e.imgWrapperStyles,l=void 0===o?"":o,c=e.imgStyles,s=void 0===c?"":c,d=e.children;return(0,i.jsxs)("div",{className:"".concat(w().imgCol," ").concat(w()[n]),children:[(0,i.jsx)("div",{className:"".concat(w().imgWrapper,"  ").concat(w()[l]),children:(0,i.jsx)("img",{className:"".concat(w().img," ").concat(w()[s]),src:r,alt:a})}),d]})},N=n(25514),F=n.n(N),B=n(91987),Z=function(e){var t=e.stepNumber,n=e.img,r=e.title,a=e.desc,o=e.btnUrl,l=e.btnName,c=e.target,s=void 0===c?"":c,d=e.trackingInfo,u=e.trackingInfoExternalLink,m=void 0===u?{}:u,f=e.onSignInClick;return(0,i.jsxs)("div",{className:F().imageContainer,children:[(0,i.jsx)("img",{className:F().image,src:n,alt:"step"}),(0,i.jsxs)("div",{className:F().textWrapper,children:[(0,i.jsx)("div",{className:F().number,children:t}),(0,i.jsx)(B.Z,{title:r,titleSmall:!0})]}),(0,i.jsx)("div",{className:F().desc,children:a}),(0,i.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap",rel:"stylesheet"}),(0,i.jsx)(g.Z,{name:l,target:s,url:o,btnStyles:"loginEmailBtnStep",trackingInfo:d,trackingInfoExternalLink:m,onSignInClick:f})]})},I=n(59424),M=n(10467),P=function(){return(0,i.jsxs)("svg",{width:"483",height:"74",viewBox:"0 0 483 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsxs)("g",{clipPath:"url(#clip0_472_1863)",children:[(0,i.jsx)("path",{d:"M106.27 36.8201L-41 36.8201L-41 73.6401L106.27 73.6401V36.8201Z",fill:"#C6C9ED"}),(0,i.jsx)("path",{d:"M211.27 0L106.27 0V36.82L211.27 36.82V0Z",fill:"#C6C9ED"}),(0,i.jsx)("path",{d:"M287.63 36.8201L211.27 36.8201V73.6401H287.63V36.8201Z",fill:"#C6C9ED"}),(0,i.jsx)("path",{d:"M341.91 0L287.64 0V36.82L341.91 36.82V0Z",fill:"#C6C9ED"}),(0,i.jsx)("path",{d:"M383.19 36.8201L341.91 36.8201V73.6401H383.19V36.8201Z",fill:"#C6C9ED"}),(0,i.jsx)("path",{d:"M413.7 0L383.19 0V36.82L413.7 36.82V0Z",fill:"#C6C9ED"}),(0,i.jsx)("path",{d:"M435.54 36.8201H413.7V73.6401H435.54V36.8201Z",fill:"#C6C9ED"}),(0,i.jsx)("path",{d:"M450.25 0L435.54 0V36.82H450.25V0Z",fill:"#C6C9ED"}),(0,i.jsx)("path",{d:"M460.72 36.8201H450.26V73.6401H460.72V36.8201Z",fill:"#C6C9ED"}),(0,i.jsx)("path",{d:"M468.33 0L460.72 0V36.82H468.33V0Z",fill:"#C6C9ED"}),(0,i.jsx)("path",{d:"M474.04 36.8201H468.33V73.6401H474.04V36.8201Z",fill:"#C6C9ED"}),(0,i.jsx)("path",{d:"M478.71 0L474.04 0V36.82H478.71V0Z",fill:"#C6C9ED"}),(0,i.jsx)("path",{d:"M482.64 36.8201H478.78V73.6401H482.64V36.8201Z",fill:"#C6C9ED"})]}),(0,i.jsx)("defs",{children:(0,i.jsx)("clipPath",{id:"clip0_472_1863",children:(0,i.jsx)("rect",{width:"483",height:"74",fill:"white"})})})]})},W=function(){return(0,i.jsxs)("svg",{width:"482",height:"74",viewBox:"0 0 482 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsxs)("g",{clipPath:"url(#clip0_472_1999)",children:[(0,i.jsx)("path",{d:"M376.37 36.82L523.64 36.82L523.64 3.8147e-05L376.37 2.52722e-05L376.37 36.82Z",fill:"#FFEC35"}),(0,i.jsx)("path",{d:"M271.37 73.64L376.37 73.64L376.37 36.82L271.37 36.82L271.37 73.64Z",fill:"#FFEC35"}),(0,i.jsx)("path",{d:"M195.01 36.82L271.37 36.82L271.37 3.8147e-05L195.01 3.14714e-05L195.01 36.82Z",fill:"#FFEC35"}),(0,i.jsx)("path",{d:"M140.73 73.64L195 73.64L195 36.82L140.73 36.82L140.73 73.64Z",fill:"#FFEC35"}),(0,i.jsx)("path",{d:"M99.4501 36.82L140.73 36.82L140.73 7.62939e-06L99.4501 4.02058e-06L99.4501 36.82Z",fill:"#FFEC35"}),(0,i.jsx)("path",{d:"M68.9401 73.64L99.4501 73.64L99.4501 36.82L68.9401 36.82L68.9401 73.64Z",fill:"#FFEC35"}),(0,i.jsx)("path",{d:"M47.1001 36.82L68.9401 36.82L68.9401 7.62939e-06L47.1001 5.72008e-06L47.1001 36.82Z",fill:"#FFEC35"}),(0,i.jsx)("path",{d:"M32.3901 73.64L47.1001 73.64L47.1001 36.82L32.3901 36.82L32.3901 73.64Z",fill:"#FFEC35"}),(0,i.jsx)("path",{d:"M21.9204 36.82L32.3804 36.82L32.3804 7.62939e-06L21.9204 6.71495e-06L21.9204 36.82Z",fill:"#FFEC35"}),(0,i.jsx)("path",{d:"M14.3102 73.64L21.9201 73.64L21.9201 36.82L14.3102 36.82L14.3102 73.64Z",fill:"#FFEC35"}),(0,i.jsx)("path",{d:"M8.60016 36.82L14.3102 36.82L14.3102 7.62939e-06L8.60016 7.13021e-06L8.60016 36.82Z",fill:"#FFEC35"}),(0,i.jsx)("path",{d:"M3.93011 73.64L8.60016 73.64L8.60016 36.82L3.93012 36.82L3.93011 73.64Z",fill:"#FFEC35"}),(0,i.jsx)("path",{d:"M-0.00012207 36.82L3.85986 36.82L3.85987 7.62939e-06L-0.000118851 7.29194e-06L-0.00012207 36.82Z",fill:"#FFEC35"})]}),(0,i.jsx)("defs",{children:(0,i.jsx)("clipPath",{id:"clip0_472_1999",children:(0,i.jsx)("rect",{width:"482",height:"74",fill:"white",transform:"translate(482 74) rotate(-180)"})})})]})},O=function(e){var t=e.viewBoxWidth;return(0,i.jsxs)("svg",{width:"502",height:"74",viewBox:"0 0 ".concat(t," 74"),fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsxs)("g",{clipPath:"url(#clip0_472_2064)",children:[(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M239.65 35.4198L244.65 -0.000244141L277.82 13.3798L264.67 20.9698L277.09 42.4898L298.61 30.0698L319.8 66.7798L307.93 73.6398L293.59 48.7998L272.08 61.2198L252.8 27.8298L239.65 35.4198Z",fill:"#FFEC35"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M177.82 35.4198L181.67 -0.000244141L207.25 13.3798L197.11 20.9698L206.69 42.4898L223.28 30.0698L239.62 66.7798L230.47 73.6398L219.41 48.7998L202.82 61.2198L187.96 27.8298L177.82 35.4198Z",fill:"#FFEC35"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M130.91 35.4198L133.83 -0.000244141L153.2 13.3798L145.52 20.9698L152.77 42.4898L165.34 30.0698L177.71 66.7798L170.78 73.6398L162.41 48.7998L149.84 61.2198L138.59 27.8298L130.91 35.4198Z",fill:"#FFEC35"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M95.1801 35.4198L97.4001 -0.000244141L112.15 13.3798L106.3 20.9698L111.82 42.4898L121.38 30.0698L130.8 66.7798L125.53 73.6398L119.15 48.7998L109.59 61.2198L101.03 27.8298L95.1801 35.4198Z",fill:"#FFEC35"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M67.6401 35.4198L69.3501 -0.000244141L80.7001 13.3798L76.2001 20.9698L80.4501 42.4898L87.8201 30.0698L95.0801 66.7798L91.0101 73.6398L86.1001 48.7998L78.7401 61.2198L72.1401 27.8298L67.6401 35.4198Z",fill:"#FFEC35"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M46.36 35.4198L47.68 -0.000244141L56.44 13.3798L52.97 20.9698L56.25 42.4898L61.93 30.0698L67.53 66.7798L64.4 73.6398L60.61 48.7998L54.93 61.2198L49.84 27.8298L46.36 35.4198Z",fill:"#FFEC35"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M30.0001 35.4198L31.0101 -0.000244141L37.7401 13.3798L35.0801 20.9698L37.6001 42.4898L41.9601 30.0698L46.2601 66.7798L43.8501 73.6398L40.9401 48.7998L36.5801 61.2198L32.6701 27.8298L30.0001 35.4198Z",fill:"#FFEC35"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.4501 35.4198L18.2301 -0.000244141L23.3801 13.3798L21.3401 20.9698L23.2701 42.4898L26.6101 30.0698L29.9001 66.7798L28.0501 73.6398L25.8301 48.7998L22.4901 61.2198L19.5001 27.8298L17.4501 35.4198Z",fill:"#FFEC35"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.64008 35.4198L8.24008 -0.000244141L12.2601 13.3798L10.6701 20.9698L12.1701 42.4898L14.7801 30.0698L17.3501 66.7798L15.9101 73.6398L14.1701 48.7998L11.5701 61.2198L9.23008 27.8298L7.64008 35.4198Z",fill:"#FFEC35"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.10352e-05 35.4198L0.470061 -0.000244141L3.59006 13.3798L2.35006 20.9698L3.52006 42.4898L5.54006 30.0698L7.53006 66.7798L6.42006 73.6398L5.07006 48.7998L3.05006 61.2198L1.24006 27.8298L6.10352e-05 35.4198Z",fill:"#FFEC35"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M319.83 35.4198L327.3 -0.000244141L376.88 13.3798L357.23 20.9698L375.8 42.4898L407.95 30.0698L439.64 66.7798L421.89 73.6398L400.45 48.7998L368.3 61.2198L339.49 27.8298L319.83 35.4198Z",fill:"#FFEC35"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M439.64 35.4198L449.5 -0.000244141L515.04 13.3798L489.06 20.9698L513.6 42.4898L556.11 30.0698L597.98 66.7798L574.52 73.6398L546.19 48.7998L503.69 61.2198L465.61 27.8298L439.64 35.4198Z",fill:"#FFEC35"})]}),(0,i.jsx)("defs",{children:(0,i.jsx)("clipPath",{id:"clip0_472_2064",children:(0,i.jsx)("rect",{width:"502",height:"74",fill:"white",transform:"translate(6.10352e-05 -0.000244141)"})})})]})},T=function(){return(0,i.jsxs)("svg",{width:"511",height:"74",viewBox:"0 0 511 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsxs)("g",{clipPath:"url(#clip0_472_1882)",children:[(0,i.jsx)("path",{d:"M117.03 73.64V0L199.94 36.82L117.03 73.64Z",fill:"#C6C8EE"}),(0,i.jsx)("path",{d:"M-0.27002 73.64V0L117.03 36.82L-0.27002 73.64Z",fill:"#C6C8EE"}),(0,i.jsx)("path",{d:"M199.94 73.64V0L282.85 36.82L199.94 73.64Z",fill:"#C6C8EE"}),(0,i.jsx)("path",{d:"M282.85 73.64V0L345.03 36.82L282.85 73.64Z",fill:"#C6C8EE"}),(0,i.jsx)("path",{d:"M345.03 73.64V0L391.67 36.82L345.03 73.64Z",fill:"#C6C8EE"}),(0,i.jsx)("path",{d:"M391.67 73.64V0L426.58 36.82L391.67 73.64Z",fill:"#C6C8EE"}),(0,i.jsx)("path",{d:"M426.31 73.64V0L453.85 36.82L426.31 73.64Z",fill:"#C6C8EE"}),(0,i.jsx)("path",{d:"M453.85 73.64V0L474.31 36.82L453.85 73.64Z",fill:"#C6C8EE"}),(0,i.jsx)("path",{d:"M474.31 73.64V0L488.76 36.82L474.31 73.64Z",fill:"#C6C8EE"}),(0,i.jsx)("path",{d:"M488.76 73.64V0L499.12 36.82L488.76 73.64Z",fill:"#C6C8EE"}),(0,i.jsx)("path",{d:"M499.12 73.64V0L506.21 36.82L499.12 73.64Z",fill:"#C6C8EE"}),(0,i.jsx)("path",{d:"M506.21 73.64V0L510.85 36.82L506.21 73.64Z",fill:"#C6C8EE"})]}),(0,i.jsx)("defs",{children:(0,i.jsx)("clipPath",{id:"clip0_472_1882",children:(0,i.jsx)("rect",{width:"511",height:"74",fill:"white"})})})]})},V=n(67895),z=n(66084),R=n.n(z),U=n(44406),D=n.n(U),A=n(3428),G=n.n(A),H=n(8486),Q=n.n(H),q=n(86),Y=n(42377),J=n.n(Y);function X(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function K(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),i.forEach((function(t){X(e,t,n[t])}))}return e}var $=!0;function ee(e){var t=e.header,n=e.pageConfig,_=e.userGeo,h=(0,r.useState)(!1),x=h[0],v=h[1],y=(0,r.useState)(""),C=y[0],E=y[1],w=(0,r.useState)(""),N=w[0],F=w[1],B=(0,r.useState)(!1),z=B[0],U=B[1],A=(0,r.useState)({}),H=A[0],Y=A[1],X=(0,c.E)(),$=X.trackAddressability,ee=X.trackContentAction,te=X.trackExternalLink,ne=X.trackPageview;(0,s.L)({context_page_type:"feed",unit_name:"main"}),(0,d.T)(),(0,r.useEffect)((function(){$({unit_name:"main",context_page_type:"feed"}),ne({unit_name:"main",context_page_type:"feed"}),v(!!o.Z.get("bf2-b_info"));var e=(0,a.bG)();E(null===e||void 0===e?void 0:e.username),F(null===e||void 0===e?void 0:e.userid)}),[]);var ie=x?M.L9:M.mF,re=x?"".concat(l._H,"/").concat(C):M.mF,ae=x?"user":"auth",oe=x?N:"sign_in",le=x?"_blank":"",ce=function(e){U(!0),Y(e)},se=(0,r.useMemo)((function(){var e=H||{},t={context_page_type:"feed",unit_name:"main",item_type:e.item_type,item_name:e.item_name};return{contentAction:function(e){return ee(K({},t,e))},externalLink:function(e){return te(K({},t,e))}}}),[H,ee,te]);return(0,i.jsxs)("div",{children:[(0,i.jsx)(u.Z,K({},t,{communityPage:"true"})),(0,i.jsx)(q.default,{pageConfig:n,page:"community-about",userGeo:_}),(0,i.jsx)(p.Z,{}),(0,i.jsx)(m.Z,{children:(0,i.jsx)(f.Z,{isCommunity:!0})}),(0,i.jsx)(V.f,{isOpen:z,onClose:function(){return U(!1)},redirectUrl:M.fL,track:se}),(0,i.jsxs)("main",{className:J().mainContainer,children:[(0,i.jsx)("div",{className:J().loginContainer,children:(0,i.jsxs)("div",{className:"".concat(""===C?J().hidden:""," ").concat(J().loginWrapper),children:[(0,i.jsxs)("div",{className:"".concat(J().loginLeftCol," ").concat(x?J().loginLeftColFullWidth:void 0),children:["Looking to make ",(0,i.jsx)(b.Z,{link:M.FC,name:"quizzes"})," or"," ",(0,i.jsx)(b.Z,{link:M.Ps,name:"posts"})," about your favorite TV show, fandom, or just your latest obsession?",(0,i.jsx)("br",{})," ",(0,i.jsx)("br",{})," You\u2019re in the right place."," ",!x&&"Create a BuzzFeed account\n              to get started."]}),!x&&(0,i.jsx)("div",{className:J().loginRightCol,children:M.f$.map((function(e){return(0,i.jsx)(g.Z,{name:e.name,url:e.url,btnStyles:e.btnStyle,icon:e.icon,trackingInfo:K({},e.trackingInfo,{unit_name:"main",target_content_type:"auth",target_content_id:"sign_in"})},e.name)}))})]})}),(0,i.jsxs)(L,{id:"get-started",graphicSvg:(0,i.jsx)(P,{}),graphicStyles:"chessGraphic",borderColor:"stepsBorder",sectionWidth:"stepsSectionWidth",children:[(0,i.jsx)("h2",{className:J().sectionTitle,children:"Steps to get started"}),(0,i.jsx)("dev",{className:J().stepsWrapper,children:M.Q9.map((function(e,t){return(0,i.jsx)(Z,K({stepNumber:e.stepNumber,title:0===t?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(b.Z,K({link:re,target:le,name:"Sign up or log in",linkTrackingInfo:{item_name:"user_sign",item_type:"button",target_content_type:ae,target_content_id:oe}},!x&&{onSignInClick:ce}))," ","to your BuzzFeed account"," "]}):e.title,desc:e.desc,img:e.img,btnUrl:0===t?re:1===t?ie:e.url,btnName:e.btnName,target:0===t||1===t?le:"_blank",trackingInfo:K({},e.trackingInfo,{target_content_type:0===t?ae:e.trackingInfo.target_content_type,target_content_id:0===t?oe:e.trackingInfo.target_content_id}),trackingInfoExternalLink:1===t&&x?e.trackingInfoExternalLink:{}},!x&&{onSignInClick:ce}),e.stepNumber)}))}),(0,i.jsx)("div",{className:J().stepsDesc,children:M.Sg})]}),(0,i.jsxs)(j,{id:"create-quiz",children:[(0,i.jsx)(k,K({sectionTitle:"Create a Quiz",desc:(0,i.jsx)(M.a2,{}),btnName:"Make a Quiz",target:le,btnUrl:ie,link:M.vU,linkName:"View top Quizzes",trackingInfo:{unit_name:"main",item_name:"make_quiz",target_content_type:"auth",target_content_id:"sign_in"},trackingInfoExternalLink:x?{item_name:"make_quiz",target_content_url:M.L9,link_id:"community_about"}:{},linkTrackingInfo:{item_name:"top_quizzes",item_type:"text",target_content_type:"url",target_content_id:"https://www.buzzfeed.com/community/leaderboard "}},!x&&{onSignInClick:ce})),(0,i.jsxs)(S,{imgSrc:D(),altTex:"quiz mock",children:[(0,i.jsx)("div",{className:J().arrowsGraphicYellowBig,children:(0,i.jsx)(O,{viewBoxWidth:"502"})}),(0,i.jsx)("div",{className:J().arrowsGraphicYellowSmall,children:(0,i.jsx)(O,{viewBoxWidth:"395"})})]})]}),(0,i.jsxs)(j,{id:"create-post",children:[(0,i.jsx)(S,{orderStyles:"order",imgSrc:G(),altTex:"post mock",children:(0,i.jsx)("div",{className:J().triangleGraphicPurple,children:(0,i.jsx)(T,{})})}),(0,i.jsx)(k,K({sectionTitle:"Create a Post",orderStyles:"order",desc:M.Up,btnName:"Make a Post",target:le,btnUrl:ie,link:M.vU,linkName:"View top Posts",trackingInfo:{unit_name:"main",item_name:"make_post",target_content_type:"auth",target_content_id:"sign_in"},trackingInfoExternalLink:x?{item_name:"make_post",target_content_url:M.L9,link_id:"community_about"}:{},linkTrackingInfo:{item_name:"top_posts",item_type:"text",target_content_type:"url",target_content_id:"https://www.buzzfeed.com/community/leaderboard "}},!x&&{onSignInClick:ce}))]}),(0,i.jsx)("div",{id:"writers-challenges",className:J().wrapperChallengesSection,children:(0,i.jsxs)("div",{className:J().createChallengeContainer,children:[(0,i.jsx)(k,{sectionTitle:"Writers\u2019 Challenges",desc:M.Sp,btnName:"Learn more",target:"_blank",btnUrl:M.df,trackingInfo:{unit_name:"main",item_name:"more_challenge",target_content_type:"url",target_content_id:M.df}}),(0,i.jsx)(S,{imgSrc:Q(),altTex:"writers\u2019 challenges",imgWrapperStyles:"imgWrapperStyles",imgStyles:"imgStyles"})]})}),(0,i.jsx)(L,{id:"community-guidelines",graphicSvg:(0,i.jsx)(W,{}),graphicStyles:"chessGraphicYellow",borderColor:"guidelinesBorder",sectionWidth:"guidelinesSectionWidth",children:(0,i.jsxs)("dev",{className:J().guidelinesWrapper,children:[(0,i.jsx)("div",{className:J().guidelinesLeftCol,children:(0,i.jsx)("img",{src:R(),alt:"guidelines"})}),(0,i.jsx)(k,{sectionTitle:"Community Guidelines",orderStyles:"order",desc:(0,i.jsx)(M.Qn,{}),btnName:"Learn more",target:"_blank",btnUrl:M.cq,btnStyles:"loginEmailBtnGuidelines",trackingInfo:{unit_name:"main",item_name:"more_guidelines",target_content_type:"url",target_content_id:M.cq}})]})})]}),(0,i.jsx)("footer",{children:(0,i.jsx)(I.Z,{isUserAuth:x})})]})}var te=ee},67895:function(e,t,n){"use strict";n.d(t,{f:function(){return S}});var i=n(2784),r=n(13980),a=n.n(r),o=n(11423);var l=n(85953),c=(n(20108),"signInModal__eoWwW"),s="container__2DSNS",d="header__3IJeE",u="socialSigninButton__3NxfE",m="submitButton__3CL8N",f="inputEmail__xzzWI",p="icon__3HKBy",g="errorSection__1gXwu",_="iconError__3evLB",h="errorMessage__3iLMS",L="linkButton__m86IK",x="dividerOr__sCtBz",v="socialSigninText__3yJdx",j="signupSection__TqgIL",y="signupText__2EWvq",C={unit_type:"modal",unit_name:"signin_modal"},b=["google","facebook","apple"];function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function E(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),i.forEach((function(t){k(e,t,n[t])}))}return e}function w(e){var t=e.provider,n=e.onTrackExternalLink,r=e.redirectUrl,a={google:{connection:"google-oauth2",text:"Connect with Google",Icon:function(){return i.createElement(o._8,{className:p,fill:"#DD4B39"})}},facebook:{connection:"facebook",text:"Connect with Facebook",Icon:function(){return i.createElement(o.JT,{className:p,fill:"#3B5998"})}},apple:{connection:"apple",text:"Connect with Apple",Icon:function(){return i.createElement(o.qw,{className:p,fill:"#000"})}}}[t],c=a.connection,s=a.Icon,d=a.text,m=new URL((0,l.ge)()+"/auth/csrf");m.searchParams.set("provider","auth0"),m.searchParams.set("connection",c),m.searchParams.set("redirect",r||window.location.href.toString());return i.createElement("button",{type:"submit",className:u,onClick:function(){n({item_type:"button",item_name:"".concat(t,"_login"),target_content_url:m.toString()}),window.location.href=m.toString()}},i.createElement(s,null),i.createElement("div",{className:v},d))}function S(e){var t=e.isOpen,n=void 0!==t&&t,r=e.onClose,a=e.track,u=void 0===a?{}:a,v=e.redirectUrl,k=void 0===v?"":v,S=(0,i.useRef)(null),N=(0,i.useRef)(null),F=(0,i.useState)(""),B=F[0],Z=F[1],I=(0,i.useState)(""),M=I[0],P=I[1],W=(0,i.useCallback)((function(e,t){"function"===typeof u[e]&&u[e](t)}),[u]),O=function(e){W("externalLink",E({},C,e))},T=function(){if(!(0,l.vV)(B))return P("Email is not valid.");P("");var e=function(e){for(var t=e+"=",n=document.cookie.split(";"),i=0;i<n.length;i++){for(var r=n[i];" "===r.charAt(0);)r=r.substring(1,r.length);if(0===r.indexOf(t))return r.substring(t.length,r.length)}return null}("_xsrf"),t=new URLSearchParams({email:encodeURIComponent(B),_xsrf:encodeURIComponent(e)}).toString();return fetch("".concat((0,l.ge)(),"/auth/ad-track-token/hem?").concat(t)).catch((function(){})).finally((function(){var e=new URL("/auth/csrf",(0,l.ge)());e.searchParams.set("provider","auth0"),e.searchParams.set("connection","email"),e.searchParams.set("login_hint",B),e.searchParams.set("redirect",k||window.location.href.toString()),O({item_type:"button",item_name:"passwordless",target_content_url:e.toString()}),window.location.href=e.toString()})),""},V=function(e){var t=new URL("/auth/csrf",(0,l.ge)());if(t.searchParams.set("provider","auth0"),t.searchParams.set("redirect",k||window.location.href.toString()),"sign_up"===e&&t.searchParams.set("screen_hint","signup"),B){if(!(0,l.vV)(B))return void P("Email is not valid.");t.searchParams.set("login_hint",B)}O({item_type:"text",item_name:e,target_content_url:t.toString()}),window.location.href=t.toString()},z=function(){r(),Z(""),P(""),W("contentAction",E({},C,{subunit_type:"",subunit_name:"",item_type:"button",item_name:"overlay",action_type:"hide",action_value:"signin_modal"}))},R=function(e){S&&S.current&&(S.current.contains(e.target)||z())};(0,i.useEffect)((function(){document.addEventListener("mousedown",R),document.body.style.overflow="hidden"}),[]),(0,i.useEffect)((function(){n?(document.body.style.overflow="hidden",W("contentAction",{action_type:"show",action_value:"signin_modal"})):document.body.style.overflow="unset"}),[n,W]);var U=function(){var e=(0,i.useCallback)((function(e){"Enter"===e.key?T():"Escape"===e.key&&z()}),[]);return(0,i.useEffect)((function(){return n?window.addEventListener("keydown",e):window.removeEventListener("keydown",e),function(){window.removeEventListener("keydown",e)}}),[n,e]),""};return(0,i.useEffect)((function(){n&&N.current&&N.current.focus()}),[n]),n?i.createElement("div",{className:c},i.createElement(U,null),i.createElement("div",{ref:S,className:s},i.createElement("h2",{className:d},"Confirm it's you"),i.createElement("input",{ref:N,type:"text",className:f,value:B,onChange:function(e){return t=e.target.value.trim(),Z(t),void(M&&P(""));var t},placeholder:"Enter your email"}),M&&i.createElement("div",{className:g},i.createElement(o.P7,{className:"".concat(p," ").concat(_),fill:"#d00e17"}),i.createElement("p",{className:h},M)),i.createElement("button",{type:"submit",className:m,onClick:T},"Send me a code"),i.createElement("button",{type:"submit",className:L,onClick:function(){return V("password")}},"Continue with password"),i.createElement("div",{className:x},i.createElement("span",null,"OR")),i.createElement("div",null,b.map((function(e,t){return i.createElement(w,{key:t,provider:e,onTrackExternalLink:O,redirectUrl:k})}))),i.createElement("div",{className:j},i.createElement("p",{className:y},"Don't have an account?",i.createElement("button",{className:L,onClick:function(){return V("sign_up")}},"Sign up"))))):null}S.propTypes={isOpen:a().bool.isRequired,onClose:a().func.isRequired,redirectUrl:a().string,track:a().shape({contentAction:a().func,externalLink:a().func})}},65246:function(e){e.exports={loginBtn:"loginBtn_loginBtn__QeO57",loginBtnText:"loginBtn_loginBtnText__oOXQH",loginIcon:"loginBtn_loginIcon__As08U",loginGoogleBtn:"loginBtn_loginGoogleBtn__8_6gi",loginFbBtn:"loginBtn_loginFbBtn__FM1WB",loginAppleBtn:"loginBtn_loginAppleBtn__8FORH",loginEmailBtn:"loginBtn_loginEmailBtn__1cSkq",loginEmailBtnGuidelines:"loginBtn_loginEmailBtnGuidelines__m_UyT",loginEmailBtnCreate:"loginBtn_loginEmailBtnCreate__zsmsn",loginEmailBtnStep:"loginBtn_loginEmailBtnStep__vZxgz"}},63953:function(e){e.exports={footerWrapper:"footer_footerWrapper__yHY_b",footer:"footer_footer__oomSf",loginCol:"footer_loginCol__p_mMO",sectionTitle:"footer_sectionTitle__Zc9c4",loginBtns:"footer_loginBtns__5C5GK",btnSize:"footer_btnSize__uRtCv",FAQCol:"footer_FAQCol__asESL",FAQSection:"footer_FAQSection__S14ye",googleFormLink:"footer_googleFormLink__Qen2y",wrapperBg:"footer_wrapperBg__WO_FM",background:"footer_background__CLkD8"}},25514:function(e){e.exports={imageContainer:"step_imageContainer__w3tHI",image:"step_image__7ndOb",textWrapper:"step_textWrapper__S2t92",number:"step_number__1FSj_",desc:"step_desc__JGqnG"}},89663:function(e){e.exports={wrapperCreateSection:"layoutCreateSection_wrapperCreateSection__dYTQ4",createSectionContainer:"layoutCreateSection_createSectionContainer__ZKU8M"}},19188:function(e){e.exports={imgCol:"layoutImg_imgCol__1wZji",order:"layoutImg_order__q3XT6",imgWrapper:"layoutImg_imgWrapper__7SXGK",imgWrapperStyles:"layoutImg_imgWrapperStyles__b0nP7",img:"layoutImg_img__73byF",imgStyles:"layoutImg_imgStyles__5X0kJ"}},9217:function(e){e.exports={textCol:"layoutText_textCol__Pa7yE",order:"layoutText_order__Ju40o",sectionTitle:"layoutText_sectionTitle__0sOFh",description:"layoutText_description__Kk5Ut",createSectionButtonWrapper:"layoutText_createSectionButtonWrapper__WavEL"}},62816:function(e){e.exports={wrapper:"layoutWithBg_wrapper__P918I",sectionContainer:"layoutWithBg_sectionContainer__66HLq",stepsBorder:"layoutWithBg_stepsBorder__piMoh",guidelinesBorder:"layoutWithBg_guidelinesBorder__hVVRQ",chessGraphic:"layoutWithBg_chessGraphic__PYn1f",chessGraphicYellow:"layoutWithBg_chessGraphicYellow__RmFQJ",sectionWrapper:"layoutWithBg_sectionWrapper__eUNPd",stepsSectionWidth:"layoutWithBg_stepsSectionWidth__u_50h",guidelinesSectionWidth:"layoutWithBg_guidelinesSectionWidth__gzHGH"}},20108:function(){},8486:function(e){e.exports="/static-assets/feed-ui/_next/static/images/knowItAll-f6f1c2d7dc4060d44d4eecaa26cf1d1e.png"},3428:function(e){e.exports="/static-assets/feed-ui/_next/static/images/post-mock-72f2b9e8f8018b347bef60e8fd82c12c.png"},44406:function(e){e.exports="/static-assets/feed-ui/_next/static/images/quiz-mock-63fbcc790d8af9986a40f92eeb5bf539.png"},66084:function(e){e.exports="/static-assets/feed-ui/_next/static/images/snackOnSnacks-d0dda63ed2c88c54bffec251582ac686.png"}},function(e){e.O(0,[182,167,995,852,86,38,774,888,179],(function(){return t=34973,e(e.s=t);var t}));var t=e.O();_N_E=t}]);
//# sourceMappingURL=contribute-066276e6d58edb94.js.map