_N_E=(window.webpackJsonp_N_E=window.webpackJsonp_N_E||[]).push([[13],{"01xP":function(t,e,n){var r=(0,n("ERkP").createContext)({});t.exports={AuthUserContext:r}},"8ssP":function(t,e,n){var r=(0,n("ERkP").createContext)({});t.exports={StatusBarContext:r}},GgZs:function(t,e){t.exports={COMMUNITY_USER_TYPE:"COMMUNITY",PRESS_USER_TYPE:"PRESS",STAFF_USER_TYPE:"STAFF",ADVERTISER_USER_TYPE:"ADVERTISER"}},Gwrt:function(t,e,n){var r=n("KQpv")("jsonp");t.exports=function(t,e,n){"function"==typeof e&&(n=e,e={});e||(e={});var a,c,s=e.prefix||"__jp",u=e.name||s+i++,l=e.param||"callback",f=null!=e.timeout?e.timeout:6e4,d=encodeURIComponent,h=document.getElementsByTagName("script")[0]||document.head;f&&(c=setTimeout((function(){p(),n&&n(new Error("Timeout"))}),f));function p(){a.parentNode&&a.parentNode.removeChild(a),window[u]=o,c&&clearTimeout(c)}return window[u]=function(t){r("jsonp got",t),p(),n&&n(null,t)},t=(t+=(~t.indexOf("?")?"&":"?")+l+"="+d(u)).replace("?&","?"),r('jsonp req "%s"',t),(a=document.createElement("script")).src=t,h.parentNode.insertBefore(a,h),function(){window[u]&&p()}};var i=0;function o(){}},KQpv:function(t,e,n){(function(r){function i(){var t;try{t=e.storage.debug}catch(n){}return!t&&"undefined"!==typeof r&&"env"in r&&(t=r.env.DEBUG),t}(e=t.exports=n("pEXl")).log=function(){return"object"===typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},e.formatArgs=function(t){var n=this.useColors;if(t[0]=(n?"%c":"")+this.namespace+(n?" %c":" ")+t[0]+(n?"%c ":" ")+"+"+e.humanize(this.diff),!n)return;var r="color: "+this.color;t.splice(1,0,r,"color: inherit");var i=0,o=0;t[0].replace(/%[a-zA-Z%]/g,(function(t){"%%"!==t&&(i++,"%c"===t&&(o=i))})),t.splice(o,0,r)},e.save=function(t){try{null==t?e.storage.removeItem("debug"):e.storage.debug=t}catch(n){}},e.load=i,e.useColors=function(){if("undefined"!==typeof window&&window.process&&"renderer"===window.process.type)return!0;return"undefined"!==typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!==typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},e.storage="undefined"!=typeof chrome&&"undefined"!=typeof chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(t){}}(),e.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],e.formatters.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},e.enable(i())}).call(this,n("F63i"))},Kpow:function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return B})),n.d(e,"b",(function(){return dt})),n.d(e,"c",(function(){return O})),n.d(e,"d",(function(){return w})),n.d(e,"e",(function(){return o})),n.d(e,"f",(function(){return C})),n.d(e,"g",(function(){return nt})),n.d(e,"h",(function(){return ct})),n.d(e,"i",(function(){return h})),n.d(e,"j",(function(){return ut})),n.d(e,"k",(function(){return E})),n.d(e,"l",(function(){return A})),n.d(e,"m",(function(){return _})),n.d(e,"n",(function(){return T})),n.d(e,"o",(function(){return m})),n.d(e,"p",(function(){return v})),n.d(e,"q",(function(){return g})),n.d(e,"r",(function(){return at})),n.d(e,"s",(function(){return ot})),n.d(e,"t",(function(){return et})),n.d(e,"u",(function(){return ft})),n.d(e,"v",(function(){return j})),n.d(e,"w",(function(){return u})),n.d(e,"x",(function(){return k})),n.d(e,"y",(function(){return st})),n.d(e,"z",(function(){return i})),n.d(e,"A",(function(){return F})),n.d(e,"B",(function(){return l})),n.d(e,"C",(function(){return I})),n.d(e,"D",(function(){return z})),n.d(e,"E",(function(){return x})),n.d(e,"F",(function(){return d})),n.d(e,"G",(function(){return P})),n.d(e,"H",(function(){return R})),n.d(e,"I",(function(){return N})),n.d(e,"J",(function(){return rt})),n.d(e,"K",(function(){return it})),n.d(e,"L",(function(){return b})),n.d(e,"M",(function(){return M}));var r=n("jNoi");function i(){}function o(t,e){for(const n in e)t[n]=e[n];return t}function a(t){return t()}function c(){return Object.create(null)}function s(t){t.forEach(a)}function u(t){return"function"===typeof t}function l(t,e){return t!=t?e==e:t!==e||t&&"object"===typeof t||"function"===typeof t}let f;function d(t,e){return f||(f=document.createElement("a")),f.href=e,t===f.href}function h(t,e,n,r){if(t){const i=p(t,e,n,r);return t[0](i)}}function p(t,e,n,r){return t[1]&&r?o(n.ctx.slice(),t[1](r(e))):n.ctx}function g(t,e,n,r){if(t[2]&&r){const i=t[2](r(n));if(void 0===e.dirty)return i;if("object"===typeof i){const t=[],n=Math.max(e.dirty.length,i.length);for(let r=0;r<n;r+=1)t[r]=e.dirty[r]|i[r];return t}return e.dirty|i}return e.dirty}function b(t,e,n,r,i,o){if(i){const a=p(e,n,r,o);t.p(a,i)}}function v(t){if(t.ctx.length>32){const e=[],n=t.ctx.length/32;for(let t=0;t<n;t++)e[t]=-1;return e}return-1}function m(t){const e={};for(const n in t)"$"!==n[0]&&(e[n]=t[n]);return e}const y="undefined"!==typeof window?window:"undefined"!==typeof globalThis?globalThis:t;function O(t,e){t.appendChild(e)}function w(t,e,n){const r=function(t){if(!t)return document;const e=t.getRootNode?t.getRootNode():t.ownerDocument;if(e&&e.host)return e;return t.ownerDocument}(t);if(!r.getElementById(e)){const t=_("style");t.id=e,t.textContent=n,function(t,e){O(t.head||t,e),e.sheet}(r,t)}}function j(t,e,n){t.insertBefore(e,n||null)}function A(t){t.parentNode&&t.parentNode.removeChild(t)}function E(t,e){for(let n=0;n<t.length;n+=1)t[n]&&t[n].d(e)}function _(t){return document.createElement(t)}function P(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function R(t){return document.createTextNode(t)}function x(){return R(" ")}function T(){return R("")}function k(t,e,n,r){return t.addEventListener(e,n,r),function(){return t.removeEventListener(e,n,r)}}function C(t,e,n){null==n?t.removeAttribute(e):t.getAttribute(e)!==n&&t.setAttribute(e,n)}(function(){function t(e){Object(r.b)(this,t),this.options=e,this._listeners="WeakMap"in y?new WeakMap:void 0}return Object(r.c)(t,[{key:"observe",value:function(t,e){var n=this;return this._listeners.set(t,e),this._getObserver().observe(t,this.options),function(){n._listeners.delete(t),n._observer.unobserve(t)}}},{key:"_getObserver",value:function(){var e,n=this;return null!==(e=this._observer)&&void 0!==e?e:this._observer=new ResizeObserver((function(e){var r;for(const i of e)t.entries.set(i.target,i),null===(r=n._listeners.get(i.target))||void 0===r||r(i)}))}}])}()).entries="WeakMap"in y?new WeakMap:void 0;const L=["width","height"];function I(t,e){const n=Object.getOwnPropertyDescriptors(t.__proto__);for(const r in e)null==e[r]?t.removeAttribute(r):"style"===r?t.style.cssText=e[r]:"__value"===r?t.value=t[r]=e[r]:n[r]&&n[r].set&&-1===L.indexOf(r)?t[r]=e[r]:C(t,r,e[r])}function M(t,e,n){t.setAttributeNS("http://www.w3.org/1999/xlink",e,n)}function S(t){return Array.from(t.childNodes)}function z(t,e){e=""+e,t.data!==e&&(t.data=e)}function N(t,e,n){t.classList[n?"add":"remove"](e)}let D,B=function(){return Object(r.c)((function t(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];Object(r.b)(this,t),this.is_svg=!1,this.is_svg=e,this.e=this.n=null}),[{key:"c",value:function(t){this.h(t)}},{key:"m",value:function(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;this.e||(this.is_svg?this.e=P(e.nodeName):this.e=_(11===e.nodeType?"TEMPLATE":e.nodeName),this.t="TEMPLATE"!==e.tagName?e:e.content,this.c(t)),this.i(n)}},{key:"h",value:function(t){this.e.innerHTML=t,this.n=Array.from("TEMPLATE"===this.e.nodeName?this.e.content.childNodes:this.e.childNodes)}},{key:"i",value:function(t){for(let e=0;e<this.n.length;e+=1)j(this.t,this.n[e],t)}},{key:"p",value:function(t){this.d(),this.h(t),this.i(this.a)}},{key:"d",value:function(){this.n.forEach(A)}}])}();function G(t){D=t}function F(t){(function(){if(!D)throw new Error("Function called outside component initialization");return D})().$$.on_mount.push(t)}const U=[],V=[];let H=[];const Z=[],W=Promise.resolve();let q=!1;function Y(t){H.push(t)}const J=new Set;let Q=0;function K(){if(0!==Q)return;const t=D;do{try{for(;Q<U.length;){const t=U[Q];Q++,G(t),X(t.$$)}}catch(e){throw U.length=0,Q=0,e}for(G(null),U.length=0,Q=0;V.length;)V.pop()();for(let t=0;t<H.length;t+=1){const e=H[t];J.has(e)||(J.add(e),e())}H.length=0}while(U.length);for(;Z.length;)Z.pop()();q=!1,J.clear(),G(t)}function X(t){if(null!==t.fragment){t.update(),s(t.before_update);const e=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,e),t.after_update.forEach(Y)}}const $=new Set;let tt;function et(){tt={r:0,c:[],p:tt}}function nt(){tt.r||s(tt.c),tt=tt.p}function rt(t,e){t&&t.i&&($.delete(t),t.i(e))}function it(t,e,n,r){if(t&&t.o){if($.has(t))return;$.add(t),tt.c.push((function(){$.delete(t),r&&(n&&t.d(1),r())})),t.o(e)}else r&&r()}function ot(t,e){const n={},r={},i={$$scope:1};let o=t.length;for(;o--;){const a=t[o],c=e[o];if(c){for(const t in a)t in c||(r[t]=1);for(const t in c)i[t]||(n[t]=c[t],i[t]=1);t[o]=c}else for(const t in a)i[t]=1}for(const a in r)a in n||(n[a]=void 0);return n}function at(t){return"object"===typeof t&&null!==t?t:{}}function ct(t){t&&t.c()}function st(t,e,n,r){const{fragment:i,after_update:o}=t.$$;i&&i.m(e,n),r||Y((function(){const e=t.$$.on_mount.map(a).filter(u);t.$$.on_destroy?t.$$.on_destroy.push(...e):s(e),t.$$.on_mount=[]})),o.forEach(Y)}function ut(t,e){const n=t.$$;null!==n.fragment&&(!function(t){const e=[],n=[];H.forEach((function(r){return-1===t.indexOf(r)?e.push(r):n.push(r)})),n.forEach((function(t){return t()})),H=e}(n.after_update),s(n.on_destroy),n.fragment&&n.fragment.d(e),n.on_destroy=n.fragment=null,n.ctx=[])}function lt(t,e){-1===t.$$.dirty[0]&&(U.push(t),q||(q=!0,W.then(K)),t.$$.dirty.fill(0)),t.$$.dirty[e/31|0]|=1<<e%31}function ft(t,e,n,r,o,a,u){let l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:[-1];const f=D;G(t);const d=t.$$={fragment:null,ctx:[],props:a,update:i,not_equal:o,bound:c(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(f?f.$$.context:[])),callbacks:c(),dirty:l,skip_bound:!1,root:e.target||f.$$.root};u&&u(d.root);let h=!1;if(d.ctx=n?n(t,e.props||{},(function(e,n){const r=!(arguments.length<=2)&&arguments.length-2?arguments.length<=2?void 0:arguments[2]:n;return d.ctx&&o(d.ctx[e],d.ctx[e]=r)&&(!d.skip_bound&&d.bound[e]&&d.bound[e](r),h&&lt(t,e)),n})):[],d.update(),h=!0,s(d.before_update),d.fragment=!!r&&r(d.ctx),e.target){if(e.hydrate){const t=S(e.target);d.fragment&&d.fragment.l(t),t.forEach(A)}else d.fragment&&d.fragment.c();e.intro&&rt(t.$$.fragment),st(t,e.target,e.anchor,e.customElement),K()}G(f)}let dt=function(){return Object(r.c)((function t(){Object(r.b)(this,t)}),[{key:"$destroy",value:function(){ut(this,1),this.$destroy=i}},{key:"$on",value:function(t,e){if(!u(e))return i;const n=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return n.push(e),function(){const t=n.indexOf(e);-1!==t&&n.splice(t,1)}}},{key:"$set",value:function(t){var e;this.$$set&&(e=t,0!==Object.keys(e).length)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}}])}()}).call(this,n("fRV1"))},LaIT:function(t,e,n){var r=n("gUiv");r.Template=n("NA4K").Template,r.template=r.Template,t.exports=r},NA4K:function(t,e,n){!function(t){function e(t,e,n){var r;return e&&"object"==typeof e&&(void 0!==e[t]?r=e[t]:n&&e.get&&"function"==typeof e.get&&(r=e.get(t))),r}t.Template=function(t,e,n,r){t=t||{},this.r=t.code||this.r,this.c=n,this.options=r||{},this.text=e||"",this.partials=t.partials||{},this.subs=t.subs||{},this.buf=""},t.Template.prototype={r:function(t,e,n){return""},v:function(t){return t=s(t),c.test(t)?t.replace(n,"&amp;").replace(r,"&lt;").replace(i,"&gt;").replace(o,"&#39;").replace(a,"&quot;"):t},t:s,render:function(t,e,n){return this.ri([t],e||{},n)},ri:function(t,e,n){return this.r(t,e,n)},ep:function(t,e){var n=this.partials[t],r=e[n.name];if(n.instance&&n.base==r)return n.instance;if("string"==typeof r){if(!this.c)throw new Error("No compiler available.");r=this.c.compile(r,this.options)}if(!r)return null;if(this.partials[t].base=r,n.subs){for(key in e.stackText||(e.stackText={}),n.subs)e.stackText[key]||(e.stackText[key]=void 0!==this.activeSub&&e.stackText[this.activeSub]?e.stackText[this.activeSub]:this.text);r=function(t,e,n,r,i,o){function a(){}function c(){}var s;a.prototype=t,c.prototype=t.subs;var u=new a;for(s in u.subs=new c,u.subsText={},u.buf="",r=r||{},u.stackSubs=r,u.subsText=o,e)r[s]||(r[s]=e[s]);for(s in r)u.subs[s]=r[s];for(s in i=i||{},u.stackPartials=i,n)i[s]||(i[s]=n[s]);for(s in i)u.partials[s]=i[s];return u}(r,n.subs,n.partials,this.stackSubs,this.stackPartials,e.stackText)}return this.partials[t].instance=r,r},rp:function(t,e,n,r){var i=this.ep(t,n);return i?i.ri(e,n,r):""},rs:function(t,e,n){var r=t[t.length-1];if(u(r))for(var i=0;i<r.length;i++)t.push(r[i]),n(t,e,this),t.pop();else n(t,e,this)},s:function(t,e,n,r,i,o,a){var c;return(!u(t)||0!==t.length)&&("function"==typeof t&&(t=this.ms(t,e,n,r,i,o,a)),c=!!t,!r&&c&&e&&e.push("object"==typeof t?t:e[e.length-1]),c)},d:function(t,n,r,i){var o,a=t.split("."),c=this.f(a[0],n,r,i),s=this.options.modelGet,l=null;if("."===t&&u(n[n.length-2]))c=n[n.length-1];else for(var f=1;f<a.length;f++)void 0!==(o=e(a[f],c,s))?(l=c,c=o):c="";return!(i&&!c)&&(i||"function"!=typeof c||(n.push(l),c=this.mv(c,n,r),n.pop()),c)},f:function(t,n,r,i){for(var o=!1,a=!1,c=this.options.modelGet,s=n.length-1;s>=0;s--)if(void 0!==(o=e(t,n[s],c))){a=!0;break}return a?(i||"function"!=typeof o||(o=this.mv(o,n,r)),o):!i&&""},ls:function(t,e,n,r,i){var o=this.options.delimiters;return this.options.delimiters=i,this.b(this.ct(s(t.call(e,r)),e,n)),this.options.delimiters=o,!1},ct:function(t,e,n){if(this.options.disableLambda)throw new Error("Lambda features disabled.");return this.c.compile(t,this.options).render(e,n)},b:function(t){this.buf+=t},fl:function(){var t=this.buf;return this.buf="",t},ms:function(t,e,n,r,i,o,a){var c,s=e[e.length-1],u=t.call(s);return"function"==typeof u?!!r||(c=this.activeSub&&this.subsText&&this.subsText[this.activeSub]?this.subsText[this.activeSub]:this.text,this.ls(u,s,n,c.substring(i,o),a)):u},mv:function(t,e,n){var r=e[e.length-1],i=t.call(r);return"function"==typeof i?this.ct(s(i.call(r)),r,n):i},sub:function(t,e,n,r){var i=this.subs[t];i&&(this.activeSub=t,i(e,n,this,r),this.activeSub=!1)}};var n=/&/g,r=/</g,i=/>/g,o=/\'/g,a=/\"/g,c=/[&<>\"\']/;function s(t){return String(null===t||void 0===t?"":t)}var u=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)}}(e)},"a+Rm":function(t,e){var n=1e3,r=6e4,i=60*r,o=24*i,a=365.25*o;function c(t,e,n){if(!(t<e))return t<1.5*e?Math.floor(t/e)+" "+n:Math.ceil(t/e)+" "+n+"s"}t.exports=function(t,e){e=e||{};var s,u=typeof t;if("string"===u&&t.length>0)return function(t){if((t=String(t)).length>100)return;var e=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(t);if(!e)return;var c=parseFloat(e[1]);switch((e[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*a;case"days":case"day":case"d":return c*o;case"hours":case"hour":case"hrs":case"hr":case"h":return c*i;case"minutes":case"minute":case"mins":case"min":case"m":return c*r;case"seconds":case"second":case"secs":case"sec":case"s":return c*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(t);if("number"===u&&!1===isNaN(t))return e.long?c(s=t,o,"day")||c(s,i,"hour")||c(s,r,"minute")||c(s,n,"second")||s+" ms":function(t){if(t>=o)return Math.round(t/o)+"d";if(t>=i)return Math.round(t/i)+"h";if(t>=r)return Math.round(t/r)+"m";if(t>=n)return Math.round(t/n)+"s";return t+"ms"}(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}},aYSr:function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}},arqG:function(t,e){function n(t,e){var n="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"===typeof t)return r(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return r(t,e)}(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var i=0,o=function(){};return{s:o,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,c=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return c=t.done,t},e:function(t){s=!0,a=t},f:function(){try{c||null==n.return||n.return()}finally{if(s)throw a}}}}function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var i=function(){if(!arguments.length)return[];var t,e=[],r=n(arguments);try{for(r.s();!(t=r.n()).done;){var i=t.value;if(i&&i.buzzes){var o,a=i.buzzes,c=n(a);try{for(c.s();!(o=c.n()).done;){var s=o.value,u=s.country_code;u&&!e.includes(u)&&e.push(u)}}catch(l){c.e(l)}finally{c.f()}}}}catch(l){r.e(l)}finally{r.f()}return e};t.exports={findMatchingID:function(t){return function(e){return e.id===t.toString()}},getArrayKeyValues:function(t,e){return t.map((function(t){return t[e]}))},getFieldsFromBuzz:function(t){return{id:t.id,name:t.name,url:t.url}},getCountryCodes:i,getLanguageCodes:function(){if(!arguments.length)return[];var t={"en-uk":"en-gb"},e=i.apply(void 0,arguments);return e.map((function(e){return t[e]||e}))}}},b4WN:function(t,e){t.exports={isoTimeToUnix:function(t){return new Date(t).getTime()/1e3},monthAndYear:function(t){return new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short"})}}},cMU6:function(t,e,n){"use strict";n.r(e),n.d(e,"Page",(function(){return nd}));var r=n("HbGN"),i=n("QsI/"),o=n("zjfJ"),a=n("zygG"),c=n("fGyu"),s=n("ERkP"),u=n.n(s),l=n("fsQa"),f=n("O94r"),d=n.n(f),h=n("A8CH"),p=n("QCvc"),g=n("arqG"),b=n("MWCF"),v=n("v/Kl"),m=n("y9gR"),y=n("GgZs"),O=n("wJaV"),w=n("bFlY"),j=n("44S2"),A=n("7kdo"),E=n("Xkeh"),_=n("Atgj"),P=n.n(_),R=n("goQq"),x=n.n(R),T=u.a.createElement;var k=function(t){var e=t.children,n=t.id;return e?T("h2",{id:n,className:x.a.sectionTitle},e):null},C=n("cxan"),L=n("E7fa"),I=n("NdQl"),M=n.n(I),S=n("foZj"),z=["a","strong","i","b"],N={a:["href","name","target"],img:["src","alt"]};function D(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:z,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:N;return t&&""===t.trim()||!t?"":S(t,{allowedTags:e,allowedAttributes:n,nonTextTags:[]})}var B=n("wope");function G(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function F(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?G(Object(n),!0).forEach((function(e){Object(B.a)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):G(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}n("zjms");var U={post:"post__1Y5A7",postImage:"postImage__IX16V",postInfo:"postInfo__10Y_6",postTitle:"postTitle__eNUaO",postDescription:"postDescription__1NBuN",postLink:"postLink__2OkJz",bylineContainer:"bylineContainer__1e1cx",bylineContainer__ad:"bylineContainer__ad__3h7Cs",byline:"byline__3oTAl",bylineImage:"bylineImage__3rYjU",bylineUserlink:"bylineUserlink__37CDq",bylineUsername:"bylineUsername__3z_tA",bylineTimestamp:"bylineTimestamp__1DzuF",middot:"middot__HPcRl",promoted:"promoted__37JDd"};function V(t){var e=t.avatar,n=t.username,r=t.name,i=t.bylineRef;return u.a.createElement("div",{className:d()(U.bylineContainer,U.bylineContainer__ad)},u.a.createElement("div",{className:U.byline},u.a.createElement("img",{className:U.bylineImage,src:e,alt:""}),u.a.createElement("div",null,u.a.createElement("div",{className:U.promoted},"Promoted By"),u.a.createElement("a",{href:"/".concat(n),className:U.bylineUserlink,ref:i},u.a.createElement("span",{className:U.bylineUsername,dangerouslySetInnerHTML:{__html:r}})))))}function H(t){var e=t.timestamp,n=function(t,e){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=Date.now()/1e3-t;if(Intl.RelativeTimeFormat&&i<86400){var o=new Intl.RelativeTimeFormat(e,{localeMatcher:"best fit",numeric:"always",style:"long"});if(i<3600){var a=Math.round(i/60);n=o.format(-a,"minute")}else{var c=Math.round(i/3600);n=o.format(-c,"hour")}return{formattedTimestamp:n,hasTimestampString:!0}}var s=new Date(1e3*t);return{formattedTimestamp:n=new Intl.DateTimeFormat(e,F({year:"numeric",month:"short",day:"numeric"},r)).format(s),hasTimestampString:!1}}(e,"default",{hour:"numeric",minute:"numeric"}).formattedTimestamp,r=new Date(1e3*e).toISOString();return u.a.createElement("time",{className:U.bylineTimestamp,dateTime:r},n)}function Z(t){var e=t.avatar,n=t.username,r=t.name,i=t.bylineRef,o=t.timestamp;return u.a.createElement("div",{className:U.bylineContainer},u.a.createElement("div",{className:U.byline},u.a.createElement("a",{href:"/".concat(n),className:U.bylineUserlink,ref:i},u.a.createElement("img",{className:U.bylineImage,src:e,alt:""}),u.a.createElement("span",{className:U.bylineUsername,dangerouslySetInnerHTML:{__html:r}})),u.a.createElement("span",{className:U.middot,"aria-hidden":"true"},"\u2022"),u.a.createElement(H,{timestamp:o})))}function W(t){var e=t.title,n=void 0===e?"":e,r=t.description,i=void 0===r?"":r,o=t.images,a=void 0===o?{}:o,c=t.imageAlt,l=void 0===c?"":c,f=t.uri,d=void 0===f?"":f,h=t.timestamp,p=void 0===h?"0":h,g=t.bylines,b=void 0===g?[]:g,v=t.className,m=void 0===v?"":v,y=t.tracking,O=void 0===y?function(){}:y,w=t.titleTagName,j=void 0===w?"h2":w,A=t.flags,E=void 0===A?{}:A,_=t.language,P=void 0===_?null:_,R=Object(s.useRef)(null),x=j,T=Object(L.a)(b,1)[0],k=null;if(T){var C=T.username,I=T.display_name,S=1===E.ad,z=S?I:C;k=S?u.a.createElement(V,M()({},T,{name:z,bylineRef:R})):u.a.createElement(Z,M()({timestamp:p},T,{name:z,bylineRef:R}))}var N=Object(s.useMemo)((function(){if(!a.big)return"";var t=new URL(a.big);return t.searchParams.set("output-format","auto"),t.searchParams.set("output-quality","auto"),t.searchParams.set("resize","300:*"),t.toString()}),[a.big]);return u.a.createElement("article",{className:"".concat(U.post," ").concat(m),onClick:function(t){var e=t.target;if(e===R.current||e.parentElement===R.current)return t.stopPropagation(),void(location.href=d);O(),location.href=d},lang:P},u.a.createElement("div",{className:U.imageContainer},u.a.createElement("img",{src:N,alt:l,className:U.postImage})),u.a.createElement("div",{className:U.postInfo},u.a.createElement(x,{className:U.postTitle},u.a.createElement("a",{className:U.postLink,href:d,dangerouslySetInnerHTML:{__html:D(n)}})),u.a.createElement("p",{className:U.postDescription,dangerouslySetInnerHTML:{__html:D(i)}}),k,!T&&u.a.createElement(H,{timestamp:p})))}var q=n("dVT/"),Y=n("Zxt8"),J=n("0Pzx"),Q=n.n(J),K=u.a.createElement;function X(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function $(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?X(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):X(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var tt=function(t){var e=t.index,n=t.id,r=t.profileUserUUID,i=Object(s.useMemo)(function(t){var e=t.index,n=t.id,r=t.profileUserUUID;return function(){return{unit_type:"profile_body",unit_name:r,subunit_type:"package",subunit_name:"published_posts",item_type:"card",item_name:n,position_in_unit:0,position_in_subunit:e,target_content_type:"buzz",target_content_id:n.toString()}}}({index:e,id:n,profileUserUUID:r}),[e,n,r]),o=Object(Y.a)(i),a=[];return t.flags&&1===t.flags.ad&&t.bylines.length>0&&(a=t.bylines.map((function(t){return $($({},t),{},{display_name:Object(q.a)(t.display_name)})}))),K("li",{ref:o},K(W,Object(C.a)({},t,{uri:t.url,bylines:a,language:"en"===t.language?null:t.language,className:Q.a.postItem})))},et=n("pir2"),nt=n("xJ4j"),rt=n.n(nt),it=u.a.createElement,ot=function(t){var e=t.emoji,n=t.label,r=t.text,i=t.className,o=t.children;return it("div",null,it("div",{className:d()(i,rt.a.container)},it("span",{className:rt.a.emoji,role:"img","aria-label":n},e),it("span",{className:rt.a.text},"\xa0",r)),o)},at=n("AC7c"),ct=n("0ogm"),st=n.n(ct),ut=u.a.createElement;function lt(){var t=Object(s.useContext)(et.ProfileUserContext).uuid,e=Object(s.useMemo)(function(t){var e=t.uuid;return function(){return{unit_type:"profile_body",unit_name:e,item_type:"text",item_name:"publish something",subunit_type:"package",subunit_name:"",position_in_unit:0,position_in_subunit:0,target_content_type:"url",target_content_id:"https://www.buzzfeed.com/community"}}}({uuid:t}),[t]),n=Object(Y.a)(e);return ut("div",{className:st.a.cta},ut("img",{className:st.a.trophy,src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/Iq453amA.png?resize=75:75",alt:"A wooden post sticking out of the grass"}),ut("span",null,ut("a",{className:st.a.link,href:"https://community.buzzfeed.com",ref:n},"Publish something")," ","to earn a few Internet Points and your",ut("i",null," First Post")," trophy!"))}var ft=n("01xP");function dt(){var t=Object(s.useContext)(et.ProfileUserContext).username,e=Object(s.useContext)(ft.AuthUserContext),n=e.username,r=e.loaded,i=Object(s.useState)(void 0),o=i[0],a=i[1];return Object(s.useEffect)((function(){r&&a(!(!t||!n||t!==n))}),[t,n,r]),o}var ht=n("haWY"),pt=n.n(ht),gt=n("RDm1"),bt=n.n(gt),vt=u.a.createElement,mt={prev:"M368.619 16.892l24.485 24.449c2.938 2.934 4.897 7.335 4.897 11.246 0 3.912-1.959 8.313-4.897 11.246l-192.448 192.168 192.448 192.168c2.938 2.934 4.897 7.335 4.897 11.246 0 4.401-1.959 8.313-4.897 11.246l-24.485 24.449c-2.938 2.934-7.345 4.89-11.263 4.89s-8.325-1.956-11.263-4.89l-228.196-227.864c-2.938-2.934-4.897-7.335-4.897-11.246 0-3.912 1.959-8.313 4.897-11.246l228.196-227.864c2.938-2.934 7.345-4.89 11.263-4.89s8.325 1.956 11.263 4.89v.002z",next:"M142.382 16.892l-24.485 24.449c-2.938 2.934-4.897 7.335-4.897 11.246 0 3.912 1.959 8.313 4.897 11.246l192.448 192.168-192.448 192.168c-2.938 2.934-4.897 7.335-4.897 11.246 0 4.401 1.959 8.313 4.897 11.246l24.485 24.449c2.938 2.934 7.345 4.89 11.263 4.89s8.325-1.956 11.263-4.89l228.196-227.864c2.938-2.934 4.897-7.335 4.897-11.246 0-3.912-1.959-8.313-4.897-11.246l-228.196-227.864c-2.938-2.934-7.345-4.89-11.263-4.89s-8.325 1.956-11.263 4.89v.002z"};function yt(t){var e=t.ariaLabel,n=t.disabled,r=t.onClick,i=t.direction,a=void 0===i?"next":i,c=d()(bt.a.button,bt.a["button__".concat(a)],Object(o.default)({},bt.a.disabled,n)),s=d()(bt.a["".concat(a,"Icon")]);return vt("button",{"aria-label":e,className:c,disabled:n,onClick:r},vt("svg",{className:s,viewBox:"0 0 512 512",xmlns:"http://www.w3.org/2000/svg"},vt("path",{d:mt[a]})))}function Ot(t){var e=t.canPrev,n=void 0!==e&&e,r=t.canNext,i=void 0!==r&&r,o=t.onNext,a=t.onPrev,c=t.currentMin,s=t.currentMax,u=t.totalItems;return vt("div",{className:bt.a.pagination},vt("span",{className:bt.a.text},"Posts",vt("strong",{className:bt.a.range},c," - ",s),u&&" of ".concat(u)),vt(yt,{ariaLabel:"previous",direction:"prev",disabled:!n,onClick:a}),vt(yt,{ariaLabel:"next",direction:"next",disabled:!i,onClick:o}))}var wt=["error"],jt=["error"],At=u.a.createElement;function Et(){Et=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),c=new T(r||[]);return i(a,"_invoke",{value:_(t,n,c)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="executing",p="completed",g={};function b(){}function v(){}function m(){}var y={};u(y,a,(function(){return this}));var O=Object.getPrototypeOf,w=O&&O(O(k([])));w&&w!==n&&r.call(w,a)&&(y=w);var j=m.prototype=b.prototype=Object.create(y);function A(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function n(i,o,a,c){var s=f(t[i],t,o);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(l).then((function(t){u.value=t,a(u)}),(function(t){return n("throw",t,a,c)}))}c(s.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function _(e,n,r){var i=d;return function(o,a){if(i===h)throw Error("Generator is already running");if(i===p){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var c=r.delegate;if(c){var s=P(c,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===d)throw i=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=h;var u=f(e,n,r);if("normal"===u.type){if(i=r.done?p:"suspendedYield",u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=p,r.method="throw",r.arg=u.arg)}}}function P(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var o=f(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,g;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function R(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(R,this),this.reset(!0)}function k(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(typeof e+" is not iterable")}return v.prototype=m,i(j,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:v,configurable:!0}),v.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},A(E.prototype),u(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new E(l(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},A(j),u(j,s,"Generator"),u(j,a,(function(){return this})),u(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=k,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(x),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return c.type="throw",c.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),x(n),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;x(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:k(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}function _t(t){var e=t.activeFeed,n=t.setActiveFeed,r=t.t,i=[{name:"views",label:r("top_posts"),value:"top",checked:"top"===e},{name:"date-published",label:r("all_posts"),value:"latest",checked:"latest"===e}];return At("nav",{className:pt.a.feedHeader},At(k,null,r("published_posts")),At(at.a,{name:"feed-select",label:r("show"),options:i,onChange:n}))}function Pt(t){var e=t.posts,n=Object(s.useContext)(et.ProfileUserContext).uuid;return At("ul",null,e.map((function(t,e){return At(tt,{index:e,id:t.id,title:t.name,description:t.description,images:t.images,imageAlt:t.imageAlt,url:t.url,timestamp:t.timestamp.toString(),key:t.id,profileUserUUID:n,bylines:t.bylines,flags:t.flags,language:t.language,titleTagName:"h3"})})))}function Rt(t){var e=t.posts,n=t.buzzCount,o=Object(s.useContext)(et.ProfileUserContext),a=o.username,l=o.isCommunityUser,f=Object(s.useState)(!1),d=f[0],h=f[1],p=Object(s.useState)(!1),g=p[0],v=p[1],m=Object(s.useState)(0),y=m[0],O=m[1],w=Object(s.useState)([e]),j=w[0],A=w[1];function E(t){return _.apply(this,arguments)}function _(){return(_=Object(i.a)(Et().mark((function t(e){var n,i,o,c;return Et().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!j[n=y+1]||!j[n].buzzes){t.next=3;break}return t.abrupt("return",{});case 3:return t.next=5,Object(b.a)("/".concat(a,".json").concat(e,"&include=latest_posts"));case 5:if(i=t.sent,o=i.error,c=Object(r.a)(i,wt),!o){t.next=10;break}return t.abrupt("return",o);case 10:return t.abrupt("return",c);case 11:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function P(){return(P=Object(i.a)(Et().mark((function t(){var e,n,i,o;return Et().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!j[y].next){t.next=16;break}return h(!0),t.next=4,E(j[y].next);case 4:if(e=t.sent,n=e.error,i=void 0===n?null:n,o=Object(r.a)(e,jt),!i){t.next=12;break}return v(i),h(!1),t.abrupt("return");case 12:o&&o.latest_posts&&A((function(t){return[].concat(Object(c.a)(t),[{buzzes:o.latest_posts.buzzes,next:o.latest_posts.next,prev:!0}])})),v(null),h(!1),O((function(t){return t+1}));case 16:case"end":return t.stop()}}),t)})))).apply(this,arguments)}var R=j[y],x=R.buzzes,T=R.next,k=R.prev,L=10*y,I=l?{totalItems:n}:{};return At(u.a.Fragment,null,At(Pt,{posts:x}),At(Ot,Object(C.a)({onNext:function(){return P.apply(this,arguments)},onPrev:function(){return O((function(t){return t-1}))},canNext:!d&&T,canPrev:k,currentMin:L+1,currentMax:L+x.length},I)),g&&"Oh boy... Something went wrong :/")}function xt(t){var e=t.posts,n=t.t;return At("div",{className:pt.a.welcome},At(k,null,n("published_posts")),At(ot,{emoji:"\ud83d\ude36",label:"Face Without Mouth",text:"You haven\u2019t published any posts!",className:d()(pt.a.emptyPostsBold,pt.a.fallbackHeader)}),At(lt,null),At(ot,{emoji:"\ud83d\ude05",label:"Face With Nervous Laugh",text:"Don't know where to start?",className:d()(pt.a.emptyPostsBold,pt.a.fallbackHeader)},At("div",{className:pt.a.fallbackText},"Learn how to make an awesome post with these guides written by BuzzFeed staff:")),At(Pt,{posts:e}))}var Tt=Object(l.withTranslation)("common")((function(t){var e=t.posts,n=t.buzzCount,r=void 0===n?0:n,i=t.t,o=dt(),a=Object(s.useContext)(et.ProfileUserContext),c=a.isCommunityUser,u=a.displayName,l=Object(s.useState)("latest"),f=l[0],d=l[1],h=Boolean(e.latest.buzzes.length);return!h&&c&&o?At(xt,{t:i,posts:e.welcome}):h?At("div",null,At(_t,{t:i,activeFeed:f,setActiveFeed:d}),At("div",{className:pt.a.postListWrapper},"latest"===f?At(Rt,{t:i,buzzCount:r,posts:e[f]}):At(Pt,{t:i,posts:e[f].buzzes}))):At("div",{className:pt.a.userFeed},At(k,null,i("published_posts")),At(ot,{emoji:"\ud83d\ude36",label:"Face Without Mouth",text:"".concat(u," hasn\u2019t published anything yet."),className:pt.a.emptyPosts}))})),kt=u.a.createElement,Ct=function(t){var e=t.posts,n=t.buzzCount;return kt("div",{className:P.a.backgroundWhite},kt("div",{className:d()(P.a.content,P.a.feeds,P.a.feeds__sideBySide)},kt("div",null,kt(k,null,"Latest Posts"),kt(Rt,{posts:e.latest,buzzCount:n})),kt("div",null,kt(k,null,"Top Posts"),kt(Pt,{posts:e.top.buzzes}))))},Lt=u.a.createElement,It=function(t){var e=t.posts,n=t.buzzCount;return Lt("div",{className:P.a.backgroundWhite},Lt("div",{className:d()(P.a.content,P.a.feeds,P.a.feeds__single)},Lt("div",null,Lt(k,null,"Latest Posts"),Lt(Rt,{posts:e.latest,buzzCount:n}))))},Mt=n("aWzz"),St=n.n(Mt),zt=n("GkCM");function Nt(t){if(!t)throw new Error("`timestamp` is required");var e=Math.round(Date.now()/1e3)-t;return e<60?Math.round(e)+" seconds ago":e<3600?Math.round(e/60)+" minutes ago":e<=86400?Math.round(e/3600)+" hours ago":e<=2592e3?Math.round(e/86400)+" days ago":new Date(1e3*t).toLocaleDateString()}var Dt=n("8FTv"),Bt=n.n(Dt),Gt=u.a.createElement,Ft=function(t){var e=t.children,n=t.truncateAt,r=void 0===n?280:n,i=t.maxLength,o=t.className,a=Object(s.useState)(!0),c=a[0],u=a[1];if(!e)return null;var l=e.replace(/(\n\n\n+)/gi,"\n\n").trim(),f=function(t,e){var n=(t.match(/\n/g)||[]).length;return t.length<e&&n<=10}(l,r),d=i||1.2*r;if(!l)return null;if(f||!c)return Gt("div",{className:o},l);var h=function(t,e,n){var r=t.indexOf(" ",e),i=-1===r?n:Math.min(r,n),o=t.substr(0,i).trim();return(o.match(/\n/g)||[]).length>10&&(o=o.split("\n").slice(0,10).join("\n").trim()),o}(l,r,d);return Gt("div",{className:o},h,"\u2026\xa0",Gt("button",{className:"".concat(Bt.a.buttonReset," ").concat(Bt.a.readMore),onClick:function(){u(!1)}},"Read More"))},Ut=n("wbFr"),Vt=n.n(Ut),Ht=n("dPol"),Zt=n.n(Ht),Wt=u.a.createElement,qt=function(t){var e=t.username,n=t.avatar;return Wt("img",{className:Zt.a.avatar,src:n||Vt.a,alt:"".concat(e,"'s avatar")})},Yt=function(t){var e=t.image;return e?Wt("img",{className:Zt.a.image,src:e,alt:""}):null},Jt=function(t){var e=t.url,n=t.id,r=t.parentId,i="".concat(e,"?comment_id=").concat(n);return r&&(i+="_".concat(r)),i},Qt=function(t){var e=t.id,n=t.url,r=t.body,i=t.image,o=t.title,a=t.index,c=t.likes,u=t.avatar,l=t.username,f=t.postDate,d=t.profileUserUUID,h=t.parentId,p=t.contentId,g=t.contentType,b=t.t,v=Object(s.useMemo)(function(t){var e=t.index,n=t.id,r=t.profileUserUUID,i=t.contentId,o=t.contentType;return function(){return{unit_type:"profile_body",unit_name:r,subunit_type:"package",subunit_name:"recent_comments",item_type:"text",item_name:n,position_in_unit:0,position_in_subunit:e,target_content_type:o,target_content_id:i.toString()}}}({id:e,index:a,profileUserUUID:d,contentId:p,contentType:g}),[a,e,d,p,g]),m=Object(Y.a)(v),y=Nt(f);return Wt("li",{className:Zt.a.commentContainer},Wt("div",{className:Zt.a.avatarContainer},Wt(qt,{avatar:u,username:l})),Wt("div",{className:Zt.a.content},Wt("p",{className:Zt.a.commentContext},Wt("span",{className:Zt.a.author},l),"\xa0",b("commented_on"),"\xa0",Wt("span",{className:Zt.a.postTitle},Wt("a",{className:Zt.a.postLink,href:Jt({url:n,id:e,parentId:h}),ref:m,dangerouslySetInnerHTML:{__html:o}}))),Wt(Ft,{className:Zt.a.commentText},r),Wt(Yt,{image:i}),Wt("div",{className:Zt.a.info},Wt("p",{className:Zt.a.time},y),Wt("div",{className:Zt.a.hearts},Wt(zt.e,{className:Zt.a.heartIcon}),Wt("p",null,c)))))},Kt=(St.a.string,St.a.string,St.a.string,St.a.number,St.a.string,St.a.string,St.a.string,St.a.string,St.a.number,n("Kdvo")),Xt=n.n(Kt),$t=u.a.createElement,te=function(t){var e=t.comments,n=void 0===e?[]:e,r=t.t,i=Object(s.useContext)(et.ProfileUserContext).uuid;return $t("div",{className:Xt.a.commentsList},$t("div",{className:Xt.a.header},$t(k,null,r("recent_comments"))),n.length?$t("ul",null,n.map((function(t,e){return $t(Qt,{key:t.id,index:e,id:t.id,title:t.buzz.name,image:t.picture_url,username:t.user.display_name,avatar:t.user.image,body:t.blurb,likes:t.love_count,postDate:t.created_at,url:t.buzz.url,profileUserUUID:i,parentId:t.parent_id,contentId:t.content_id,contentType:t.content_type,t:r})}))):$t(ot,{emoji:"\ud83d\ude36",label:"Face Without Mouth",text:"No recent comments found.",className:Xt.a.emptyComments}))},ee=te=Object(l.withTranslation)("common")(te),ne=n("TWeG"),re=n.n(ne),ie=n("v0uu"),oe="user_auth_passthrough",ae=function(t){return new Date(t).getTime()},ce=function(t,e){return ae(e.created_at)-ae(t.created_at)},se=function(t){return function(t){return"type"in t&&t.type}(t)&&function(t){return"created_at"in t&&t.created_at}(t)&&function(t){return t instanceof Date&&!isNaN(t)}(new Date(t.created_at))},ue=function(){var t=dt(),e=Object(s.useContext)(et.ProfileUserContext).isCommunityUser,n=Object(s.useState)(null),r=n[0],i=n[1],o=Object(s.useCallback)(function(t){return function(e,n){if(t&&t.length){var r=t.filter((function(t){return t.type!==e&&t.created_at!==n}));t.length!==r.length&&localStorage.setItem(oe,JSON.stringify(r))}}}(r),[r]);return Object(s.useEffect)((function(){if(window.localStorage&&t){var n=window.localStorage.getItem(oe);if(n)if(e){var r;try{r=JSON.parse(n)}catch(a){return void console.error("Error reading ".concat(oe," from LocalStorage"))}if(!function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Array.isArray(t)&&t.length}(r))try{window.localStorage.setItem(oe,JSON.stringify([]))}catch(c){return void console.error("Error resetting invalid ".concat(oe," in LocalStorage"))}var o=r.filter(se).sort(ce);try{window.localStorage.setItem(oe,JSON.stringify(o))}catch(s){console.error("Error writing ".concat(oe," in LocalStorage"))}i(o)}else window.localStorage.setItem(oe,JSON.stringify([]))}}),[t,e]),[r,o]},le=n("9fIP"),fe=n("MMYH"),de=n("K/z8"),he=n("sRHE"),pe=n("8K1b"),ge=function(){return Object(fe.a)((function t(e){Object(le.a)(this,t),this.consumed=!1,this.type=e.type,this.created_at=e.created_at,this.removeUserAuthPassthroughAction=e.removeUserAuthPassthroughAction}),[{key:"valid",value:function(){return!1}},{key:"delete",value:function(){return this.removeUserAuthPassthroughAction(this.type,this.created_at),!0}},{key:"run",value:function(){return!1}}])}();function be(){be=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),c=new T(r||[]);return i(a,"_invoke",{value:_(t,n,c)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="executing",p="completed",g={};function b(){}function v(){}function m(){}var y={};u(y,a,(function(){return this}));var O=Object.getPrototypeOf,w=O&&O(O(k([])));w&&w!==n&&r.call(w,a)&&(y=w);var j=m.prototype=b.prototype=Object.create(y);function A(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function n(i,o,a,c){var s=f(t[i],t,o);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(l).then((function(t){u.value=t,a(u)}),(function(t){return n("throw",t,a,c)}))}c(s.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function _(e,n,r){var i=d;return function(o,a){if(i===h)throw Error("Generator is already running");if(i===p){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var c=r.delegate;if(c){var s=P(c,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===d)throw i=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=h;var u=f(e,n,r);if("normal"===u.type){if(i=r.done?p:"suspendedYield",u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=p,r.method="throw",r.arg=u.arg)}}}function P(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var o=f(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,g;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function R(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(R,this),this.reset(!0)}function k(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(typeof e+" is not iterable")}return v.prototype=m,i(j,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:v,configurable:!0}),v.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},A(E.prototype),u(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new E(l(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},A(j),u(j,s,"Generator"),u(j,a,(function(){return this})),u(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=k,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(x),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return c.type="throw",c.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),x(n),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;x(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:k(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}function ve(t,e,n){return e=Object(he.a)(e),Object(de.a)(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],Object(he.a)(t).constructor):e.apply(t,n))}Object(o.default)(ge,"Type","action");function me(t){return ye.apply(this,arguments)}function ye(){return(ye=Object(i.a)(be().mark((function t(e){var n,r,i,o,a,c,s,u;return be().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.buzzId,r=e.resultId,i=e.userId,o=e.force,a=void 0!==o&&o,c={user_id:parseInt(i,10),buzz_id:parseInt(n,10),result_id:r,format_type:"quiz",force:a},t.next=4,fetch("".concat(ie.bf_url).concat("/user-profile-api/pinned-quiz-results"),{method:"POST",body:JSON.stringify(c),credentials:"include"});case 4:return s=t.sent,t.next=7,s.json();case 7:if(u=t.sent,s.ok){t.next=10;break}throw new Error(u.message||s.statusText);case 10:return t.abrupt("return",u);case 11:case"end":return t.stop()}}),t)})))).apply(this,arguments)}var Oe=function(t){function e(t){var n;Object(le.a)(this,e),n=ve(this,e,[t]);var r=t.data;return n.buzz_id=r.buzz_id,n.result_id=r.result_id,n}return Object(pe.a)(e,t),Object(fe.a)(e,[{key:"valid",value:function(){return Boolean(this.buzz_id&&this.result_id)}},{key:"toString",value:function(){return"{ buzz_id: '".concat(this.buzz_id,"', result_id: ").concat(this.result_id," }")}},{key:"run",value:function(){var t=Object(i.a)(be().mark((function t(n){var r,i;return be().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=n.setStatusBarStatus,i=n.userId,!this.consumed){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,this.consumed=!0,this.removeUserAuthPassthroughAction(e.Type,this.created_at),t.next=8,me({buzzId:this.buzz_id,resultId:this.result_id,userId:i,force:!1});case 8:if(!t.sent){t.next=12;break}return r("success",e.SuccessMessage),t.abrupt("return",!0);case 12:t.next=17;break;case 14:t.prev=14,t.t0=t.catch(3),r("error",e.ErrorMessage);case 17:return t.abrupt("return",!1);case 18:case"end":return t.stop()}}),t,this,[[3,14]])})));return function(e){return t.apply(this,arguments)}}()}])}(ge);Object(o.default)(Oe,"Type","pinned_quiz_result"),Object(o.default)(Oe,"SuccessMessage","Nice! This result has been pinned to your profile. It may take a few seconds to load."),Object(o.default)(Oe,"ErrorMessage","There was an issue pinning this result. Please return to the quiz and try again.");var we=n("jfkt"),je=n.n(we),Ae=u.a.createElement,Ee=function(){return Ae("span",null,"Welcome to BuzzFeed Community! Check out"," ",Ae("a",{className:je.a.link,href:"#trophy-case"},"your trophy case")," ","to see your new prize! It may take a few seconds to appear.")},_e=u.a.createElement;function Pe(){Pe=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),c=new T(r||[]);return i(a,"_invoke",{value:_(t,n,c)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="executing",p="completed",g={};function b(){}function v(){}function m(){}var y={};u(y,a,(function(){return this}));var O=Object.getPrototypeOf,w=O&&O(O(k([])));w&&w!==n&&r.call(w,a)&&(y=w);var j=m.prototype=b.prototype=Object.create(y);function A(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function n(i,o,a,c){var s=f(t[i],t,o);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(l).then((function(t){u.value=t,a(u)}),(function(t){return n("throw",t,a,c)}))}c(s.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function _(e,n,r){var i=d;return function(o,a){if(i===h)throw Error("Generator is already running");if(i===p){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var c=r.delegate;if(c){var s=P(c,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===d)throw i=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=h;var u=f(e,n,r);if("normal"===u.type){if(i=r.done?p:"suspendedYield",u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=p,r.method="throw",r.arg=u.arg)}}}function P(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var o=f(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,g;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function R(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(R,this),this.reset(!0)}function k(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(typeof e+" is not iterable")}return v.prototype=m,i(j,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:v,configurable:!0}),v.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},A(E.prototype),u(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new E(l(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},A(j),u(j,s,"Generator"),u(j,a,(function(){return this})),u(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=k,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(x),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return c.type="throw",c.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),x(n),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;x(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:k(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}function Re(t,e,n){return e=Object(he.a)(e),Object(de.a)(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],Object(he.a)(t).constructor):e.apply(t,n))}var xe=function(t){function e(t){var n;return Object(le.a)(this,e),(n=Re(this,e,[t])).trophyId=t.data&&t.data.trophyId,n.expire_at=t.expire_at,n.trophyName=e.TrophyIdMap[n.trophyId],n}return Object(pe.a)(e,t),Object(fe.a)(e,[{key:"valid",value:function(){return!!this.trophyName}},{key:"run",value:function(){var t=Object(i.a)(Pe().mark((function t(n){var r;return Pe().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=n.setStatusBarStatus,!this.consumed){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,this.consumed=!0,t.next=7,this.assignTrophy();case 7:return r("success",_e(Ee,null)),this.removeUserAuthPassthroughAction(e.Type,this.created_at),t.abrupt("return",!0);case 12:t.prev=12,t.t0=t.catch(3),r("error",e.ErrorMessage);case 15:return t.abrupt("return",!1);case 16:case"end":return t.stop()}}),t,this,[[3,12]])})));return function(e){return t.apply(this,arguments)}}()},{key:"assignTrophy",value:function(){var t=Object(i.a)(Pe().mark((function t(){var n,r;return Pe().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,fetch("".concat(ie.bf_url).concat(e.APIPath,"/").concat(this.trophyName),{method:"POST",credentials:"include"});case 2:return n=t.sent,t.next=5,n.json();case 5:if(r=t.sent,n.ok){t.next=8;break}throw new Error(r.message||n.statusText);case 8:return t.abrupt("return",r);case 9:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}()}])}(ge);Object(o.default)(xe,"Type","trophy_earned"),Object(o.default)(xe,"ErrorMessage","There was an issue assigning your trophy! Please try reloading the page."),Object(o.default)(xe,"TrophyIdMap",{29:"five_day_homepage_streak"}),Object(o.default)(xe,"APIPath","/internet-points/award");var Te=Object(o.default)(Object(o.default)({},Oe.Type,Oe),xe.Type,xe),ke=["type","created_at"];function Ce(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Le(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Ce(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ce(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var Ie=function(t){return null!==t},Me=function(t){return"type"in t&&t.type in Te},Se=function(t){var e=Object(s.useState)([]),n=e[0],i=e[1],o=ue(),c=Object(a.a)(o,2),u=c[0],l=c[1];return Object(s.useEffect)((function(){var e=function(t){return Array.isArray(t)?t.filter(Me):[]}(u).filter(function(t){return function(e){return e&&e.type===t}}(t));i(e.map(function(t){return function(e){var n=e.type,i=e.created_at,o=Object(r.a)(e,ke);if(!(n in Te))return null;var a=new(0,Te[n])(Le(Le({type:n,created_at:i},o),{},{removeUserAuthPassthroughAction:t}));return a.valid()?a:(t(n,i),null)}}(l)).filter(Ie))}),[u,t,l]),n},ze=n("8ssP"),Ne=n("cdiT"),De=n.n(Ne),Be=u.a.createElement,Ge=function(t){var e,n=t.media;return n&&n.url?Be("img",{className:De.a.image,src:(e=n.url,e.includes("unsplash")?"".concat(e,"&auto=format&fit=crop&w=640&h=460&crop=edges"):"".concat(e,"?output-format=jpg&fill=640:460")),alt:"licensed by ".concat(n.credit)}):Be("div",{className:De.a.placeholder})},Fe=n("iJs8"),Ue=n.n(Fe),Ve=u.a.createElement,He=function(t){var e=t.id,n=t.index,r=t.title,i=t.description,o=t.media,a=t.buzz,c=t.buzzId,u=t.onDelete,l=t.isProfileOwner,f=t.profileUserUUID,d=t.introClassName,h=t.contentClassName,p=t.introHeight,g=t.contentHeight,b=Object(s.useMemo)(function(t){var e=t.index,n=t.id,r=t.profileUserUUID,i=t.buzzId;return function(){return{unit_type:"profile_body",unit_name:r,subunit_type:"package",subunit_name:"pinned_quiz_results",item_type:"text",item_name:n,position_in_unit:0,position_in_subunit:e,target_content_type:"buzz",target_content_id:i.toString()}}}({id:e,index:n,profileUserUUID:f,buzzId:c}),[n,e,f,c]),v=Object(Y.a)(b);return Ve("li",{className:Ue.a.pinnedQuizResult},Ve("div",{className:"".concat(Ue.a.intro," ").concat(d),style:{minHeight:p}},Ve("h3",{className:Ue.a.title},r),Ve(Ft,{className:Ue.a.description,truncateAt:75},i)),Ve("div",{className:Ue.a.imageRatio},Ve("div",{className:Ue.a.imageWrapper},Ve(Ge,{media:o}))),Ve("div",{className:"".concat(Ue.a.content," ").concat(h),style:{minHeight:g}},a&&Ve("h2",{className:Ue.a.post},Ve("a",{className:Ue.a.postLink,href:a.url,ref:v},a.name)),l&&Ve("button",{className:Ue.a.deleteButton,onClick:u},Ve(zt.i,{className:Ue.a.trashIcon}))))},Ze=n("lOqH"),We=n("kZhk"),qe=n("O6my");var Ye=n("WmYZ"),Je=n.n(Ye),Qe=u.a.createElement,Ke=function(t){var e=t.onCancel,n=t.onConfirm,r=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];Object(Ze.a)("tracking");var r=Object(qe.a)();return Object(s.useCallback)((function(){We.g.apply(void 0,[r()].concat(e))}),[r,e])}(t.deleteTrackingData);return Qe("div",{className:Je.a.lightbox},Qe("div",{className:Je.a.modal},Qe(zt.k,{className:Je.a.closeIcon,onClick:e}),Qe("h2",{className:Je.a.title},"Delete Pinned Result?"),Qe("p",{className:Je.a.content},"This result will no longer be visible on",Qe("br",null)," your profile."),Qe("button",{className:"".concat(Je.a.button," ").concat(Je.a.confirmButton),onClick:function(){r(),n()}},"Confirm"),Qe("button",{className:"".concat(Je.a.button," ").concat(Je.a.cancelButton),onClick:e},"Cancel")))},Xe=n("z0iq"),$e=n.n(Xe),tn=u.a.createElement,en=function(t){var e=t.isProfileOwner,n=Object(s.useContext)(et.ProfileUserContext),r=n.displayName,i=n.uuid,o=Object(s.useMemo)(function(t){var e=t.uuid;return function(){return{unit_type:"profile_body",unit_name:e,item_type:"text",item_name:"take a quiz",subunit_type:"package",subunit_name:"pinned_quiz_results",position_in_unit:0,position_in_subunit:0,target_content_type:"feed",target_content_id:"quizzes"}}}({uuid:i}),[i]),a=Object(Y.a)(o);return e?tn(ot,{emoji:"\ud83d\udccc",label:"Pushpin",text:"You haven't pinned any quiz results."},tn("div",{className:$e.a.cta},tn("img",{className:$e.a.trophy,src:"https://img.buzzfeed.com/store-an-image-prod-us-east-1/s7mMzFBO4.png?resize=75:75",alt:"A wooden post sticking out of the grass"}),tn("span",null,tn("a",{className:$e.a.link,href:"https://www.buzzfeed.com/community",ref:a},"Take a quiz")," ","and pin the result to your profile to receive a trophy! You can have up to 3 results at a time."))):tn(ot,{emoji:"\ud83d\udccc",label:"Pushpin",text:"".concat(r," hasn't pinned any quiz results.")})},nn=n("2mi5"),rn=n.n(nn),on=u.a.createElement;function an(){an=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),c=new T(r||[]);return i(a,"_invoke",{value:_(t,n,c)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="executing",p="completed",g={};function b(){}function v(){}function m(){}var y={};u(y,a,(function(){return this}));var O=Object.getPrototypeOf,w=O&&O(O(k([])));w&&w!==n&&r.call(w,a)&&(y=w);var j=m.prototype=b.prototype=Object.create(y);function A(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function n(i,o,a,c){var s=f(t[i],t,o);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(l).then((function(t){u.value=t,a(u)}),(function(t){return n("throw",t,a,c)}))}c(s.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function _(e,n,r){var i=d;return function(o,a){if(i===h)throw Error("Generator is already running");if(i===p){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var c=r.delegate;if(c){var s=P(c,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===d)throw i=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=h;var u=f(e,n,r);if("normal"===u.type){if(i=r.done?p:"suspendedYield",u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=p,r.method="throw",r.arg=u.arg)}}}function P(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var o=f(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,g;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function R(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(R,this),this.reset(!0)}function k(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(typeof e+" is not iterable")}return v.prototype=m,i(j,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:v,configurable:!0}),v.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},A(E.prototype),u(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new E(l(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},A(j),u(j,s,"Generator"),u(j,a,(function(){return this})),u(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=k,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(x),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return c.type="throw",c.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),x(n),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;x(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:k(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}var cn=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return e.offsetHeight>t&&(t=e.offsetHeight),t},sn=function(t){var e=".".concat(t);return Object(c.a)(document.querySelectorAll(e)).reduce(cn,0)},un=function(){var t=Object(i.a)(an().mark((function t(e){var n;return an().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,re()("/".concat(e,".json?include=pinned_quiz_results"));case 3:return n=t.sent,t.next=6,n.json().then((function(t){return t.pinned_quiz_results}));case 6:return t.abrupt("return",t.sent);case 9:t.prev=9,t.t0=t.catch(0),console.error("Error fetching new pinned quiz results after pinning some from userAuthPassthrough"),console.error(t.t0);case 13:return t.abrupt("return",null);case 14:case"end":return t.stop()}}),t,null,[[0,9]])})));return function(e){return t.apply(this,arguments)}}(),ln=function(t){var e=t.pinnedQuizResults,n=null,r=dt(),o=Object(s.useContext)(et.ProfileUserContext),a=o.id,c=o.uuid,u=o.username,l=Object(s.useContext)(ze.StatusBarContext).setStatusBarStatus,f=Object(s.useState)(0),d=f[0],h=f[1],p=Object(s.useState)(0),g=p[0],b=p[1],v=Object(s.useState)(0),m=v[0],y=v[1],O=Object(s.useState)(e),w=O[0],j=O[1],A=Se("pinned_quiz_result");if(d){var E=w.findIndex((function(t){return t.id===d}));n=function(t){var e=t.id,n=t.index,r=t.buzzId;return{unit_type:"profile_body",unit_name:t.uuid,item_type:"button",item_name:e,subunit_type:"package",subunit_name:"pinned_quiz_results",position_in_unit:0,position_in_subunit:n,action_type:"remove",action_value:r}}({id:w[E].id,index:E,buzzId:w[E].buzzId,uuid:c})}Object(s.useEffect)((function(){b(sn("pqr-intro")),y(sn("pqr-content"))}),[b,y]),Object(s.useEffect)((function(){if(a&&l&&A.length){var t=3-w.length,e=A.map((function(e,n){return n<t?e.run({setStatusBarStatus:l,userId:a}):e.delete()}));Promise.all(e).then(function(){var t=Object(i.a)(an().mark((function t(e){var n;return an().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.every((function(t){return t}))){t.next=5;break}return t.next=3,un(u);case 3:n=t.sent,j(n);case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){console.error("Pinned quiz action handlers failed\n",t)}))}}),[A,a,u,l]);var _=Object(s.useCallback)(function(t,e,n,r){return Object(i.a)(an().mark((function i(){var o,a;return an().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.prev=0,n(0),i.next=4,re()("".concat(ie.bf_url).concat("/user-profile-api/pinned-quiz-results","/").concat(t),{method:"DELETE",credentials:"include"});case 4:return o=i.sent,i.next=7,o.json();case 7:(a=i.sent).success?r(e.filter((function(e){return e.id!==t}))):console.error(a),i.next=14;break;case 11:i.prev=11,i.t0=i.catch(0),console.log(i.t0);case 14:case"end":return i.stop()}}),i,null,[[0,11]])})))}(d,w,h,j),[d,w,h,j]);return on("div",{className:rn.a.pinnedQuizResults},Boolean(d)&&on(Ke,{onCancel:function(){h(0)},onConfirm:_,deleteTrackingData:n}),on(k,null,"Pinned Quiz Results"),w.length?on("ul",{className:rn.a.resultsFeed},w.map((function(t,e){return on(He,{index:e,key:t.id,id:t.id,buzz:t.buzz,title:t.title,media:t.media,buzzId:t.buzzId,description:t.description,onDelete:(n=t.id,function(){h(n)}),profileUserUUID:c,introClassName:"pqr-intro",introHeight:g,contentClassName:"pqr-content",contentHeight:m,isProfileOwner:r});var n}))):on(en,{isProfileOwner:r}))},fn=u.a.createElement,dn=function(t){var e=t.comments,n=t.posts,r=t.buzzCount,i=t.pinnedQuizResults;return fn("div",{className:P.a.backgroundWhite},fn("div",{className:P.a.content},fn(ln,{pinnedQuizResults:i})),fn("div",{className:d()(P.a.content,P.a.feeds,P.a.feeds__withSidebar)},fn(Tt,{posts:n,buzzCount:r}),fn("div",{className:P.a.sidebarWrapper},fn(ee,{comments:e}))))};function hn(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;Object(s.useEffect)((function(){var n=document.createElement("script");(n.setAttribute("src",t),e&&e.current)?e.current.appendChild(n):document.querySelector("head").appendChild(n)}),[e,t])}var pn=n("seaZ"),gn=n.n(pn),bn=u.a.createElement;function vn(t){var e=t.url,n=t.displayName,r=Object(s.useRef)(null);return hn("//connect.facebook.net/en_US/sdk.js",r),function(t){var e=t.version,n=t.appId;if(!e||!n)throw new Error("Ensure version and appId params are supplied");Object(s.useEffect)((function(){window.fbAsyncInit=function(){window.FB.init({version:e,appId:n,status:!0,cookie:!0,xfbml:!0})}}),[e,n])}({version:"v2.9",appId:"45075597673"}),bn("div",{className:gn.a.facebookContainer},bn("div",{className:"fb-page","data-href":e,"data-tabs":"timeline","data-width":"410","data-height":"600","data-small-header":"false","data-adapt-container-width":"true","data-hide-cover":"false","data-show-facepile":"true"},bn("blockquote",{cite:e,className:"fb-xfbml-parse-ignore"},bn("a",{href:e},n))),bn("div",{ref:r}))}var mn=n("KEb4"),yn=n.n(mn),On=u.a.createElement;function wn(t){var e=t.twitterWidget,n=Object(s.useRef)(null);hn("https://platform.twitter.com/widgets.js",n);var r=Object(q.a)(e,["a"],{a:["href","class","data-widget-id"]});return On("section",{className:yn.a.twitterContainer},On("div",{className:yn.a.twitterEmbed,dangerouslySetInnerHTML:{__html:r}}),On("div",{ref:n}))}var jn=n("umW/"),An=n.n(jn),En=u.a.createElement;function _n(t){var e=t.twitterWidget,n=t.facebookWidget,r=t.displayName;return e||n?En("div",{className:An.a.socialWrapper},En(k,null,"Social Feed"),e&&En(wn,{twitterWidget:e}),n&&En(vn,{url:n,name:r})):null}function Pn(t){var e=Object(s.useState)(window.matchMedia(t).matches),n=e[0],r=e[1];function i(t){r(t.matches)}return Object(s.useEffect)((function(){var e=window.matchMedia(t);return e.addListener(i),i(e),function(){return e.removeListener(i)}}),[t]),n}var Rn=n("7JvT"),xn={NATIVE:[5,5],NATIVE_COMPLEX_100:[100,100],NATIVE_COMPLEX_6:[6,6],NATIVE_COMPLEX_RECTANGLE:[1020,400],FLUID:"fluid",RESEARCH_PIXEL:[1,1],RESEARCH_SURVEY:[2,2],PROGRAMMATIC_PREBID:[1,1],PROGRAMMATIC_BILLBOARD:[970,250],PROGRAMMATIC_HORIZONTAL_4to1:[970,250],PROGRAMMATIC_SMARTPHONE_BANNER:[320,50],PROGRAMMATIC_HORIZONTAL_6to1:[320,50],PROGRAMMATIC_LEADERBOARD:[728,90],PROGRAMMATIC_HORIZONTAL_8to1:[728,90],PROGRAMMATIC_SUPER_LEADERBOARD:[970,90],PROGRAMMATIC_HORIZONTAL_10to1:[970,90],PROGRAMMATIC_VERTICAL:[300,600],PROGRAMMATIC_VERTICAL_1to2:[300,600],PROGRAMMATIC_MEDIUM_RECTANGLE:[300,250],PROGRAMMATIC_TILE_1to1:[300,250],PROGRAMMATIC_SMARTPHONE_BANNER_WIDE:[320,51],COMPLEX_XS_BANNER:[300,50],AMAZON_OUTSTREAM:[400,300]},Tn=Object.entries(xn).filter((function(t){return Object(a.a)(t,1)[0].startsWith("PROGRAMMATIC")})).map((function(t){return Object(a.a)(t,2)[1]})),kn={awareness:{adType:"awareness",adPos:"awareness-bp",wid:42,size:[xn.NATIVE,xn.NATIVE_COMPLEX_6,xn.NATIVE_COMPLEX_RECTANGLE,xn.FLUID,xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD],viewability:"low"},bigstory:{adType:"post",adPos:"bigstory-bp",wid:13,size:[xn.NATIVE,xn.FLUID,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_VERTICAL],viewability:"low"},pixel:{adType:"post",adPos:"pixel",wid:0,size:[xn.RESEARCH_PIXEL]},popular_pixel:{adType:"post",adPos:"popular_pixel",wid:15,size:[xn.NATIVE]},"promo-bottom1":{adType:"ex",adPos:"promo-bottom1",wid:200,renderLookahead:"x0.175",size:[xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.FLUID],viewability:"low"},"promo-bottom2":{adType:"ex",adPos:"promo-bottom2",wid:201,renderLookahead:"x0.175",size:[xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.FLUID],viewability:"low"},"promo-bottom3":{adType:"ex",adPos:"promo-bottom3",wid:202,renderLookahead:"x0.175",size:[xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.FLUID],viewability:"low"},"promo-bottom4":{adType:"ex",adPos:"promo-bottom4",wid:203,renderLookahead:"x0.175",size:[xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.FLUID],viewability:"low"},"promo-bottom5":{adType:"ex",adPos:"promo-bottom5",wid:204,renderLookahead:"x0.175",size:[xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.FLUID],viewability:"low"},"promo-bottom6":{adType:"ex",adPos:"promo-bottom6",wid:205,renderLookahead:"x0.175",size:[xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.FLUID],viewability:"low"},"promo-bottom7":{adType:"ex",adPos:"promo-bottom7",wid:206,renderLookahead:"x0.175",size:[xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.FLUID],viewability:"low"},"promo-bottom8":{adType:"ex",adPos:"promo-bottom8",wid:207,renderLookahead:"x0.175",size:[xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.FLUID],viewability:"low"},"promo-bottom9":{adType:"ex",adPos:"promo-bottom9",wid:208,renderLookahead:"x0.175",size:[xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.FLUID],viewability:"low"},"promo-bottom10":{adType:"ex",adPos:"promo-bottom10",wid:209,renderLookahead:"x0.175",size:[xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.FLUID],viewability:"low"},"promo-bottom-infinite":{adType:"ex",adPos:"promo-bottom-infinite",wid:2e3,renderLookahead:"x0.175",size:[xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.FLUID],isInfinite:!0,viewability:"low"},"promo-inline1":{adType:"ex",adPos:"promo-inline1",wid:210,size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.NATIVE,xn.FLUID,xn.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline2":{adType:"ex",adPos:"promo-inline2",wid:211,renderLookahead:"x2",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.NATIVE,xn.FLUID,xn.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline3":{adType:"ex",adPos:"promo-inline3",wid:212,renderLookahead:"x2",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.NATIVE,xn.FLUID,xn.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline4":{adType:"ex",adPos:"promo-inline4",wid:213,renderLookahead:"x2",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.NATIVE,xn.FLUID,xn.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline5":{adType:"ex",adPos:"promo-inline5",wid:214,renderLookahead:"x2",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.NATIVE,xn.FLUID,xn.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline6":{adType:"ex",adPos:"promo-inline6",wid:215,renderLookahead:"x3",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.NATIVE,xn.FLUID,xn.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline7":{adType:"ex",adPos:"promo-inline7",wid:216,renderLookahead:"x3",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.NATIVE,xn.FLUID,xn.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline8":{adType:"ex",adPos:"promo-inline8",wid:217,renderLookahead:"x3",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.NATIVE,xn.FLUID,xn.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline9":{adType:"ex",adPos:"promo-inline9",wid:218,renderLookahead:"x3",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.NATIVE,xn.FLUID,xn.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline10":{adType:"ex",adPos:"promo-inline10",wid:219,renderLookahead:"x3",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.NATIVE,xn.FLUID,xn.AMAZON_OUTSTREAM],viewability:"high"},"promo-inline-infinite":{adType:"ex",adPos:"promo-inline-infinite",wid:2100,renderLookahead:"x3",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE_COMPLEX_100,xn.NATIVE,xn.FLUID,xn.AMAZON_OUTSTREAM],isInfinite:!0,viewability:"high"},"promo-quiz-inline1":{adType:"ex",adPos:"promo-inline1",wid:210,size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD,xn.NATIVE,xn.FLUID],viewability:"high"},"promo-quiz-inline2":{adType:"ex",adPos:"promo-inline2",wid:211,renderLookahead:"x2",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline3":{adType:"ex",adPos:"promo-inline3",wid:212,renderLookahead:"x2",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline4":{adType:"ex",adPos:"promo-inline4",wid:213,renderLookahead:"x2",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline5":{adType:"ex",adPos:"promo-inline5",wid:214,renderLookahead:"x2",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline6":{adType:"ex",adPos:"promo-inline6",wid:215,renderLookahead:"x3",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline7":{adType:"ex",adPos:"promo-inline7",wid:216,renderLookahead:"x3",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline8":{adType:"ex",adPos:"promo-inline8",wid:217,renderLookahead:"x3",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline9":{adType:"ex",adPos:"promo-inline9",wid:218,renderLookahead:"x3",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline10":{adType:"ex",adPos:"promo-inline10",wid:219,renderLookahead:"x3",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD],viewability:"high"},"promo-quiz-inline-infinite":{adType:"ex",adPos:"promo-inline-infinite",wid:2100,renderLookahead:"x3",size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_LEADERBOARD],isInfinite:!0,viewability:"high"},"promo-ai-quiz":{adType:"post",adPos:"aiquizzes",wid:2101,renderLookahead:"x1",size:[xn.PROGRAMMATIC_MEDIUM_RECTANGLE,[400,300]],viewability:"high"},"promo1-wide":{adType:"ex",adPos:"promo1-wide",wid:220,size:[xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.FLUID],viewability:"high"},"promo2-wide":{adType:"ex",adPos:"promo2-wide",wid:221,size:[xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.FLUID],viewability:"high"},"promo3-wide":{adType:"ex",adPos:"promo3-wide",wid:222,size:[xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.FLUID],viewability:"high"},"promo4-wide":{adType:"ex",adPos:"promo4-wide",wid:223,size:[xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.FLUID],viewability:"high"},"promo5-wide":{adType:"ex",adPos:"promo5-wide",wid:224,size:[xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.FLUID],viewability:"high"},"promo6-wide":{adType:"ex",adPos:"promo6-wide",wid:225,size:[xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.FLUID],viewability:"high"},"promo7-wide":{adType:"ex",adPos:"promo7-wide",wid:226,size:[xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.FLUID],viewability:"high"},"promo8-wide":{adType:"ex",adPos:"promo8-wide",wid:227,size:[xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.FLUID],viewability:"high"},"promo9-wide":{adType:"ex",adPos:"promo9-wide",wid:228,size:[xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.FLUID],viewability:"high"},"promo10-wide":{adType:"ex",adPos:"promo10-wide",wid:229,size:[xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.FLUID],viewability:"high"},"promo-wide-infinite":{adType:"ex",adPos:"promo-wide-infinite",wid:2200,size:[xn.PROGRAMMATIC_SUPER_LEADERBOARD,xn.PROGRAMMATIC_BILLBOARD,xn.PROGRAMMATIC_LEADERBOARD,xn.FLUID],isInfinite:!0,viewability:"high"},"sidebar1-bp":{adType:"post",adPos:"sidebar1-bp",wid:1301,size:[xn.NATIVE,xn.FLUID,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_VERTICAL,xn.AMAZON_OUTSTREAM],viewability:"high"},"sidebar2-bp":{adType:"post",adPos:"sidebar2-bp",wid:1302,size:[xn.NATIVE,xn.FLUID,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_VERTICAL,xn.AMAZON_OUTSTREAM],viewability:"high"},"sidebar3-bp":{adType:"post",adPos:"sidebar3-bp",wid:1303,size:[xn.NATIVE,xn.FLUID,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_VERTICAL,xn.AMAZON_OUTSTREAM],viewability:"high"},"sidebar4-bp":{adType:"post",adPos:"sidebar4-bp",wid:1304,size:[xn.NATIVE,xn.FLUID,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_VERTICAL,xn.AMAZON_OUTSTREAM],viewability:"high"},"sidebar5-bp":{adType:"post",adPos:"sidebar5-bp",wid:1305,size:[xn.NATIVE,xn.FLUID,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_VERTICAL,xn.AMAZON_OUTSTREAM],viewability:"high"},"sidebar6-bp":{adType:"post",adPos:"sidebar6-bp",wid:1306,size:[xn.NATIVE,xn.FLUID,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_VERTICAL,xn.AMAZON_OUTSTREAM],viewability:"high"},"sidebar7-bp":{adType:"post",adPos:"sidebar7-bp",wid:1307,size:[xn.NATIVE,xn.FLUID,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_VERTICAL,xn.AMAZON_OUTSTREAM],viewability:"high"},"sidebar8-bp":{adType:"post",adPos:"sidebar8-bp",wid:1308,size:[xn.NATIVE,xn.FLUID,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_VERTICAL,xn.AMAZON_OUTSTREAM],viewability:"high"},"sidebar9-bp":{adType:"post",adPos:"sidebar9-bp",wid:1309,size:[xn.NATIVE,xn.FLUID,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_VERTICAL,xn.AMAZON_OUTSTREAM],viewability:"high"},"sidebar10-bp":{adType:"post",adPos:"sidebar10-bp",wid:1310,size:[xn.NATIVE,xn.FLUID,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_VERTICAL,xn.AMAZON_OUTSTREAM],viewability:"high"},"sidebar-bp-infinite":{adType:"post",adPos:"sidebar-bp-infinite",wid:13e3,size:[xn.FLUID,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_VERTICAL,xn.AMAZON_OUTSTREAM],viewability:"high",isInfinite:!0},"story-bpage":{adType:"post",adPos:"story-bpage",wid:1,size:[xn.NATIVE,xn.FLUID,xn.PROGRAMMATIC_MEDIUM_RECTANGLE,xn.PROGRAMMATIC_SMARTPHONE_BANNER],viewability:"low",isMobile:!0},"story-bpage-desktop":{adType:"post",adPos:"story-bpage",wid:9,size:[xn.NATIVE,xn.FLUID,xn.PROGRAMMATIC_LEADERBOARD],viewability:"low"},subbuzz:{adType:"awareness",adPos:"subbuzz",wid:420,size:[xn.NATIVE]},toolbar:{adType:"toolbar",adPos:"tb",wid:52,size:[xn.PROGRAMMATIC_SMARTPHONE_BANNER,xn.PROGRAMMATIC_SMARTPHONE_BANNER_WIDE,xn.COMPLEX_XS_BANNER],viewability:"low"}},Cn=Object.freeze(kn),Ln=n("rdyg");function In(){In=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),c=new T(r||[]);return i(a,"_invoke",{value:_(t,n,c)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="executing",p="completed",g={};function b(){}function v(){}function m(){}var y={};u(y,a,(function(){return this}));var O=Object.getPrototypeOf,w=O&&O(O(k([])));w&&w!==n&&r.call(w,a)&&(y=w);var j=m.prototype=b.prototype=Object.create(y);function A(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function n(i,o,a,c){var s=f(t[i],t,o);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(l).then((function(t){u.value=t,a(u)}),(function(t){return n("throw",t,a,c)}))}c(s.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function _(e,n,r){var i=d;return function(o,a){if(i===h)throw Error("Generator is already running");if(i===p){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var c=r.delegate;if(c){var s=P(c,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===d)throw i=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=h;var u=f(e,n,r);if("normal"===u.type){if(i=r.done?p:"suspendedYield",u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=p,r.method="throw",r.arg=u.arg)}}}function P(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var o=f(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,g;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function R(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(R,this),this.reset(!0)}function k(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(typeof e+" is not iterable")}return v.prototype=m,i(j,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:v,configurable:!0}),v.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},A(E.prototype),u(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new E(l(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},A(j),u(j,s,"Generator"),u(j,a,(function(){return this})),u(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=k,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(x),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return c.type="throw",c.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),x(n),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;x(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:k(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}var Mn={adSizes:xn,programmaticSizes:Tn,destination:"buzzfeed",hasQuiz:!1,isAsIs:!1,isBFN:!1,isBFO:!1,isBPage:!1,isDev:"dev"===ie.CLUSTER,isE2ETest:!1,isFeed:!1,isFeedPage:!1,isFeedpager:!1,isHomePage:!1,isNewsPost:!1,isPharmaceutical:!1,isProd:"prod"===ie.CLUSTER,isWidePost:!1,pageFilter:null,pageFilters:{},pageMainFilter:null,type:ie.CLUSTER,webRoot:ie.bf_url};function Sn(){return(Sn=Object(i.a)(In().mark((function t(e){var n,r,i,o;return In().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.isAdvertizer,r=e.abeagle,i=Object(p.d)(),o=Object(p.c)(),Object.assign(Mn,{allPageSections:["user"],author:null,cmsTags:[],localization:i,locale:i.locale,localizationCountry:i.country,userCountry:o,pageSection:"user",pageCategory:"user",pageName:"user",pageId:Ln.b.USER.toString(),analyticsPageType:Ln.c.USER,isAdPost:function(){return n}}),t.abrupt("return",{env:Mn,abeagle:r,localization:i,stickyRegistry:Rn.a});case 5:case"end":return t.stop()}}),t)})))).apply(this,arguments)}var zn=n("JNgv");function Nn(){this._listening={}}Nn.prototype.on=function(t,e){var n=this;return t in this._listening||(this._listening[t]=new Set),this._listening[t].add(e),function(){return n.off(t,e)}},Nn.prototype.once=function(t,e){var n=this;return this.on(t,(function r(){n.off(t,r),e.apply(void 0,arguments)}))},Nn.prototype.off=function(t,e){t in this._listening&&(this._listening[t].delete(e),0===this._listening[t].size&&delete this._listening[t])},Nn.prototype.trigger=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];t in this._listening&&this._listening[t].forEach((function(t){try{t.apply(void 0,n)}catch(e){console.error(e)}}));var i=t.replace(/:[^:]*$/,"");i!==t&&this.trigger.apply(this,[i].concat(n))},Nn.prototype.fire=Nn.prototype.trigger,Nn.prototype.destroy=function(){delete this._listening},Nn.mixInto=function(t){Object.assign(t,Nn.prototype),Nn.call(t)};var Dn=n("bUN3"),Bn=n("iCr9");function Gn(t){return(t+="").indexOf("#")>-1?t.substr(0,t.indexOf("#")):t}function Fn(t){if(""===t||void 0===t||null===t)return{};t.indexOf("?")>-1&&(t=t.substr(t.indexOf("?")+1,t.length));var e=(t=Gn(t)).split("&"),n={};return e.forEach((function(t){var e=t.split("="),r=Object(a.a)(e,2),i=r[0],o=r[1],c=void 0===o?null:o;n[i]=c})),n}function Un(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"?",n=[];return Object.keys(t).forEach((function(e){n.push("".concat(e,"=").concat(encodeURIComponent(t[e])))})),(e||"")+n.join("&")}function Vn(t,e,n){var r,i,o,a,c,s,u;for(this.version="1.0.1",this.minAlphabetLength=16,this.sepDiv=3.5,this.guardDiv=12,this.errorAlphabetLength="error: alphabet must contain at least X unique characters",this.errorAlphabetSpace="error: alphabet cannot contain spaces",this.alphabet="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",this.seps="cfhistuCFHISTU",this.minHashLength=parseInt(e,10)>0?e:0,this.salt="string"===typeof t?t:"","string"===typeof n&&(this.alphabet=n),r="",i=0,a=this.alphabet.length;i!==a;i++)-1===r.indexOf(this.alphabet[i])&&(r+=this.alphabet[i]);if(this.alphabet=r,this.alphabet.length<this.minAlphabetLength)throw this.errorAlphabetLength.replace("X",this.minAlphabetLength);if(-1!==this.alphabet.search(" "))throw this.errorAlphabetSpace;for(i=0,a=this.seps.length;i!==a;i++)-1===(o=this.alphabet.indexOf(this.seps[i]))?this.seps=this.seps.substr(0,i)+" "+this.seps.substr(i+1):this.alphabet=this.alphabet.substr(0,o)+" "+this.alphabet.substr(o+1);this.alphabet=this.alphabet.replace(/ /g,""),this.seps=this.seps.replace(/ /g,""),this.seps=this.consistentShuffle(this.seps,this.salt),(!this.seps.length||this.alphabet.length/this.seps.length>this.sepDiv)&&(1===(c=Math.ceil(this.alphabet.length/this.sepDiv))&&c++,c>this.seps.length?(s=c-this.seps.length,this.seps+=this.alphabet.substr(0,s),this.alphabet=this.alphabet.substr(s)):this.seps=this.seps.substr(0,c)),this.alphabet=this.consistentShuffle(this.alphabet,this.salt),u=Math.ceil(this.alphabet.length/this.guardDiv),this.alphabet.length<3?(this.guards=this.seps.substr(0,u),this.seps=this.seps.substr(u)):(this.guards=this.alphabet.substr(0,u),this.alphabet=this.alphabet.substr(u))}Vn.prototype.encode=function(){var t,e,n="",r=Array.prototype.slice.call(arguments);if(!r.length)return n;for(r[0]instanceof Array&&(r=r[0]),t=0,e=r.length;t!==e;t++)if("number"!==typeof r[t]||r[t]%1!==0||r[t]<0)return n;return this._encode(r)},Vn.prototype.decode=function(t){return t.length&&"string"===typeof t?this._decode(t,this.alphabet):[]},Vn.prototype.encodeHex=function(t){var e,n,r;if(t=t.toString(),!/^[0-9a-fA-F]+$/.test(t))return"";for(e=0,n=(r=t.match(/[\w\W]{1,12}/g)).length;e!==n;e++)r[e]=parseInt("1"+r[e],16);return this.encode.apply(this,r)},Vn.prototype.decodeHex=function(t){var e,n,r=[],i=this.decode(t);for(e=0,n=i.length;e!==n;e++)r+=i[e].toString(16).substr(1);return r},Vn.prototype._encode=function(t){var e,n,r,i,o,a,c,s,u,l,f,d=this.alphabet,h=t.length,p=0;for(r=0,i=t.length;r!==i;r++)p+=t[r]%(r+100);for(n=e=d[p%d.length],r=0,i=t.length;r!==i;r++)o=t[r],a=n+this.salt+d,d=this.consistentShuffle(d,a.substr(0,d.length)),e+=c=this.hash(o,d),r+1<h&&(s=(o%=c.charCodeAt(0)+r)%this.seps.length,e+=this.seps[s]);for(e.length<this.minHashLength&&(u=(p+e[0].charCodeAt(0))%this.guards.length,(e=this.guards[u]+e).length<this.minHashLength&&(u=(p+e[2].charCodeAt(0))%this.guards.length,e+=this.guards[u])),l=parseInt(d.length/2,10);e.length<this.minHashLength;)(f=(e=(d=this.consistentShuffle(d,d)).substr(l)+e+d.substr(0,l)).length-this.minHashLength)>0&&(e=e.substr(f/2,this.minHashLength));return e},Vn.prototype._decode=function(t,e){var n,r,i,o,a=[],c=0,s=new RegExp("["+this.guards+"]","g"),u=t.replace(s," "),l=u.split(" ");if(3!==l.length&&2!==l.length||(c=1),"undefined"!==typeof(u=l[c])[0]){for(n=u[0],u=u.substr(1),s=new RegExp("["+this.seps+"]","g"),c=0,r=(l=(u=u.replace(s," ")).split(" ")).length;c!==r;c++)i=l[c],o=n+this.salt+e,e=this.consistentShuffle(e,o.substr(0,e.length)),a.push(this.unhash(i,e));this._encode(a)!==t&&(a=[])}return a},Vn.prototype.consistentShuffle=function(t,e){var n,r,i,o,a,c;if(!e.length)return t;for(o=t.length-1,a=0,c=0;o>0;o--,a++)c+=n=e[a%=e.length].charCodeAt(0),i=t[r=(n+a+c)%o],t=(t=t.substr(0,r)+t[o]+t.substr(r+1)).substr(0,o)+i+t.substr(o+1);return t},Vn.prototype.hash=function(t,e){var n="",r=e.length;do{n=e[t%r]+n,t=parseInt(t/r,10)}while(t);return n},Vn.prototype.unhash=function(t,e){var n,r=0;for(n=0;n<t.length;n++)r+=e.indexOf(t[n])*Math.pow(e.length,t.length-n-1);return r};parseInt(1e10*Math.random(),10),([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(function(t){return(t^function(){try{return crypto.getRandomValues(new Uint8Array(1))[0]}catch(t){return 255*Math.random()}}()&15>>t/4).toString(16)}));var Hn=n("nsO7"),Zn="track/website/instrumentation";var Wn=["samplingRate"];function qn(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if("function"===typeof window.bfa){Wn.some((function(t){return n.hasOwnProperty(t)}))&&(r=n,n={});var i=r,o=i.samplingRate,a=i.platform,c={data:{target:t,value:e,tags:n}};a&&(c.data.platform=r.platform),o&&(o>1?"dev"===window.BZFD.Config.env&&console.error("Your sampling rate is above 100%."):Object.assign(c,{opts:{samplingRate:o}})),window.bfa(Zn,c)}else"undefined"!==typeof window.raven&&window.raven.captureException(new Error("Instrumentation tracking issue: BFA is not available"))}var Yn=n("Gwrt"),Jn=n.n(Yn);function Qn(t){var e=[];for(var n in t)if(t.hasOwnProperty(n)){var r=t[n];null!==r&&""!==r&&"undefined"!==typeof r&&e.push(n+"="+r)}return e.join("&")}function Kn(t){return new Promise((function(e,n){setTimeout((function(){return n({type:"timeout",msg:"".concat(t,"ms timeout exceeded")})}),t)}))}function Xn(t){var e=t.url;return function(t){return qn("xhr",t.type||"error",{url:e,status:t.status||0}),Promise.reject(t)}}function $n(t){return new Promise((function(e,n){Jn()(t,(function(t,r){return t?n(t):e(r)}))}))}function tr(t){return t.ok?Promise.resolve(t):Promise.reject({type:"error",status:t.status,statusText:t.statusText,response:t})}function er(t){return t.json()}function nr(t){return t.text()}function rr(t){if("dev"!==BZFD.Config.env)return t;var e=t.indexOf("?")>-1?"&":"?";return t+e+"aW50ZWdyYWxpc3RfZGlkX3RoaXNfb25fMjAxOV8xMV8xMQ=="}var ir=function(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.type,i=void 0===r?"json":r,o=n.data,a=void 0===o?{}:o,c=n.params,s=void 0===c?{}:c,u=n.skipAuth,l=n.timeout;if(!t)return Promise.reject("URL parameter is required");var f=Object.assign({credentials:"same-origin"},s),d=t,h=Qn(a);switch(h&&(d+=(d.indexOf("?")>-1?"&":"?")+h),u&&(d=rr(d)),i){case"json":e=re()(d,f).then(tr).then(er);break;case"text":e=re()(d,f).then(tr).then(nr);break;case"jsonp":e=$n(d);break;default:e=Promise.reject("Unsupported type ".concat(i))}return(l?Promise.race([Kn(l),e]):e).catch(Xn({url:t}))};function or(t,e){var n=t.match(e);return n&&n.length?n[0]:null}function ar(){return"prod"===window.BZFD.Config.env?"buzzfeed.com":window.location.hostname}var cr={getBuzzfeedSubdomainOrWildcard:function(t){var e=or(t,"(dev|stage|www).(buzzfeed|buzzfeednews).(com|io)$");return e||or(t,".?[a-z]+.[a-z]+$")},get:function(t){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n="".concat(t,"="),r=document.cookie.split(";"),i=0;i<r.length;i++){for(var o=r[i];" "===o.charAt(0);)o=o.substring(1,o.length);if(0===o.indexOf(n))return o.substring(n.length,o.length)}return e},set:function(t){var e=t.name,n=t.value,r=t.days,i=t.domain;i=i||ar();var o="";if(r){var a=new Date;a.setTime(a.getTime()+24*r*60*60*1e3),o="; expires=".concat(a.toGMTString())}return document.cookie="".concat(e,"=").concat(n).concat(o,"; path=/; domain=").concat(i)},remove:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ar();return this.set({name:t,value:"",days:-1,domain:e})}};var sr={};sr.isMobileApp=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.CDN_URL?window.CDN_URL:window.location.href;return-1!==t.search("[?&]?s=mobile_app([&#]|$)")};sr.isNewsApp=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.CDN_URL?window.CDN_URL:window.location.href;return-1!==t.search("[?&]?app=news([&#]|$)")};sr.isNews2App=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.CDN_URL?window.CDN_URL:window.location.href;return-1!==t.search("[?&]?app=buzznews([&#]|$)")};sr.isIOS=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.CDN_URL?window.CDN_URL:window.location.href;return-1!==t.search("[?&]?os=ios([&#]|$)")};sr.isAndroid=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.CDN_URL?window.CDN_URL:window.location.href;return-1!==t.search("[?&]?os=android([&#]|$)")};var ur=sr;function lr(){lr=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),c=new T(r||[]);return i(a,"_invoke",{value:_(t,n,c)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="executing",p="completed",g={};function b(){}function v(){}function m(){}var y={};u(y,a,(function(){return this}));var O=Object.getPrototypeOf,w=O&&O(O(k([])));w&&w!==n&&r.call(w,a)&&(y=w);var j=m.prototype=b.prototype=Object.create(y);function A(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function n(i,o,a,c){var s=f(t[i],t,o);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(l).then((function(t){u.value=t,a(u)}),(function(t){return n("throw",t,a,c)}))}c(s.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function _(e,n,r){var i=d;return function(o,a){if(i===h)throw Error("Generator is already running");if(i===p){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var c=r.delegate;if(c){var s=P(c,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===d)throw i=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=h;var u=f(e,n,r);if("normal"===u.type){if(i=r.done?p:"suspendedYield",u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=p,r.method="throw",r.arg=u.arg)}}}function P(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var o=f(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,g;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function R(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(R,this),this.reset(!0)}function k(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(typeof e+" is not iterable")}return v.prototype=m,i(j,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:v,configurable:!0}),v.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},A(E.prototype),u(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new E(l(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},A(j),u(j,s,"Generator"),u(j,a,(function(){return this})),u(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=k,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(x),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return c.type="throw",c.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),x(n),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;x(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:k(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}var fr="permutive-test"in({queryStringToObject:Fn}.queryStringToObject(window.location.search)||{});function dr(t){return hr.apply(this,arguments)}function hr(){return(hr=Object(i.a)(lr().mark((function t(e){var n;return lr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.abeagle,!fr){t.next=3;break}return t.abrupt("return",Promise.resolve(!0));case 3:if(!zn.e.needsConsent()){t.next=9;break}return t.next=6,zn.f.hasConsented("permutive");case 6:if(t.sent&&!ur.isMobileApp()){t.next=9;break}return t.abrupt("return",Promise.resolve(!1));case 9:return t.abrupt("return",n.getExperimentVariant("ADSGROUP-442-permutive").catch((function(t){return t&&"ExperimentNotFound"===t.type?"on":null})).then((function(t){return Promise.resolve("on"===t)})));case 10:case"end":return t.stop()}}),t)})))).apply(this,arguments)}var pr,gr=window,br={xs:"(max-width:39.9rem)",sm:"(min-width:40rem) and (max-width:51.9rem)",md:"(min-width:52rem) and (max-width:63.9rem)",lg:"(min-width:64rem)"};function vr(t){return"screen and ".concat(br[t])}var mr={observers:new Set,init:function(){var t=this;"matchMedia"in gr&&Object.keys(br).forEach((function(e){var n=gr.matchMedia(vr(e));n.addListener((function(n){n.matches&&t.observers.forEach((function(t){t.fire(e),t.fire("match",{breakpoint:e})}))})),t[e]=n}))}};mr.init();var yr=pr={_getWindowWidth:function(){return Number(gr.innerWidth||0)},_isRetinaDevice:function(){return"matchMedia"in gr&&gr.matchMedia("(-webkit-min-device-pixel-ratio: 2)").matches},_makeSizeMediaQuery:vr,_mediaMatches:function(t){return t in br&&mr[t].matches},isBreakpoint:function(t){return pr._mediaMatches(t)},isXsmall:function(){return pr._mediaMatches("xs")},isSmall:function(){return pr._mediaMatches("sm")},isMedium:function(){return pr._mediaMatches("md")},isLarge:function(){return pr._mediaMatches("lg")},isAny:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(Hn.some)(t,pr._mediaMatches)},isSmallerThan:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.inclusive,r=void 0===n||n;return"xs"===t?!!r&&pr.isXsmall():"sm"===t?r?pr.isAny(["xs","sm"]):pr.isXsmall():"md"===t?r?pr.isAny(["xs","sm","md"]):pr.isAny(["xs","sm"]):"lg"===t&&(!!r||pr.isAny(["xs","sm","md"]))},isLargerThan:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.inclusive,r=void 0===n||n;return"xs"===t?!!r||pr.isAny(["sm","md","lg"]):"sm"===t?r?pr.isAny(["sm","md","lg"]):pr.isAny(["md","lg"]):"md"===t?r?pr.isAny(["md","lg"]):pr.isLarge():"lg"===t&&(!!r&&pr.isLarge())},breakPoint:function(t){return pr._mediaMatches(t)},getBreakPoint:function(){var t;for(var e in br)if(pr.breakPoint(e)){t=e;break}return t},getWindowWidth:function(){return pr._isRetinaDevice()?2*pr._getWindowWidth():pr._getWindowWidth()},get cssBreakpoints(){return{xs:null,sm:"40rem",md:"52rem",lg:"64rem"}},breakpointObserver:{subscribe:function(t){return mr.observers.add(t)},unsubscribe:function(t){return mr.observers.delete(t)}}};function Or(t,e,n){return e=Object(he.a)(e),Object(de.a)(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],Object(he.a)(t).constructor):e.apply(t,n))}var wr={};function jr(t,e){return t+"-"+e.id}wr.getKeys=function(t){var e;if(Object.keys)return Object.keys(t);for(var n in e=[],t)t.hasOwnProperty(n)&&e.push(n);return e},wr.getUniqueEventName=jr,wr.capitalize=function(t){return"string"!==typeof t?t:t.replace(/\w+/g,(function(t){return t.charAt(0).toUpperCase()+t.substr(1).toLowerCase()}))},wr.hasQueryString=function(t){return t.indexOf("?")>-1},wr.removeHash=function(t){return(t+="").indexOf("#")>-1?t.substr(0,t.indexOf("#")):t},wr.removeQueryString=function(t){return(t+="").indexOf("?")>-1?-1===t.indexOf("#")?t.substr(0,t.indexOf("?")):t.substr(0,t.indexOf("?"))+t.substr("#",t.length):t},wr.getQueryString=function(t){var e="";return(t=wr.removeHash(t)).indexOf("?")>-1&&(e=t.substr(t.indexOf("?"),t.length)),e},wr.getHash=function(t){return(t+="").indexOf("#")>-1?t.substr(t.indexOf("#"),t.length):""},wr.queryStringToObject=function(t){if(""===t||void 0===t||null===t)return{};var e,n=t.split("&"),r={};return wr.each(n,(function(t,n){""===(e=n.split("="))[1]&&(e[1]=null),r[e[0]]=e[1]})),r},wr.objectToQueryString=function(t){var e=[];return wr.each(t,(function(t,n){e.push(t+"="+n)})),"?"+e.join("&")},wr.getBaseUrl=function(t){return t=wr.removeHash(t),t=wr.removeQueryString(t)},wr.addParams=function(t,e){var n,r,i=wr.getQueryString(t),o=wr.getHash(t);return t=wr.removeHash(t),t=wr.removeQueryString(t),i=wr.queryStringToObject(i.substr(1,i.length)),wr.each(e,(function(t,e){n=e[0],r=e[1],i[n]=r})),t+wr.objectToQueryString(i)+o},wr.removeParams=function(t,e){var n=wr.getBaseUrl(t),r=wr.getQueryString(t),i=wr.getHash(t),o=wr.queryStringToObject(r.substr(1,r.length));return wr.each(e,(function(t,e){delete o[e]})),n+wr.objectToQueryString(o)+i},wr.prepareUrl=function(t,e){var n=[];return wr.each(e,(function(t,e){"undefined"!==typeof e&&n.push([t,encodeURIComponent(e)])})),wr.addParams(t,n)},wr.redirect=function(t){"string"===typeof t&&t&&(window.location.href=t)},wr.openPopup=function(t,e){if("string"===typeof t&&t){var n="_blank",r="";if(e&&e.height&&e.width){var i=window.screen.height/2-e.height/2,o=window.screen.width/2-e.width/2;r="scrollbars=yes, toolbar=0, status=0, width="+e.width+", height="+e.height+", top="+i+", left="+o,n=e.name?e.name:n}window.open(t,n,r)}},wr.urlToId=function(t){var e=t;try{e=new URL(t).pathname}catch(n){}return"/"===e.charAt(0)&&(e=e.slice(1,e.length)),e.replace(/\//g,"_")},wr.each=function(t,e){if(Array.isArray(t))t.forEach((function(t,n){return e(n,t)}));else for(var n in t)t.hasOwnProperty(n)&&e(n,t[n])},wr.extend=Hn.merge,wr.getQueryParams=function(){var t=window.location.search.substring(1),e={};return t&&t.split("&").forEach((function(t){var n=t.split("="),r=decodeURIComponent(n[0]),i=decodeURIComponent(n.slice(1).join("="));e[r]=i})),e},wr.addQueryParam=function(t,e,n){return(t=t||window.location.href)+(t.split("?")[1]?"&":"?")+e+"="+n},wr.largeNumberNotation=function(t){return t<1e3?t:t<1e4?t.toString().charAt(0)+","+t.toString().substring(1):t>=1e6?(t/1e6).toFixed(t%1e6!==0)+"M":(t/1e3).toFixed(t%1e3!==0)+"K"},wr.numberWithCommas=function(t){return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")},wr.min=function(){return Math.min.apply(Math,Array.prototype.filter.call(arguments,(function(t){return!isNaN(+t)})))},wr.toQueryString=function(t){var e=[];return this.each(t,(function(t,n){null!==n&&""!==n&&"undefined"!==typeof n&&e.push(t+"="+n)})),e.join("&")},wr.getMessageHandler=function(t){return function(e,n){var r=t[e];"function"===typeof r&&r.call(this,n)}},wr.getEventHandler=function(t){return function(e,n,r){var i=t[r];"function"===typeof i&&i.call(this,e,n)}},wr.padLeft=function(t,e,n){for(var r=n-t.toString().length,i="",o=0;o<r;o++)i+=e;return i+t},wr.freeFormFormat=function(t){return t.replace(/["'"\u201c\u2019 ]/g,"").toLowerCase()},wr.secondsToTimeStamp=function(t){var e=new Date(1e3*t).toISOString().substr(11,8);return t<3600&&(e=e.substr(3,5)),e.replace(/^0+(\d:)/,"$1")},wr.timeSince=function(t){var e,n=["year","years"],r=["month","months"],i=["day","days"],o=["hour","hours"],a=["minute","minutes"],c=["just now"],s=Math.floor(((new Date).getTime()-new Date(t).getTime())/1e3);return(e=Math.floor(s/31536e3))>=1?{type:"year",value:e,text:e+" "+(1===e?n[0]:n[1])+" ago"}:(e=Math.floor(s/2592e3))>=1?{type:"month",value:e,text:e+" "+(1===e?r[0]:r[1])+" ago"}:(e=Math.floor(s/86400))>=1?{type:"day",value:e,text:e+" "+(1===e?i[0]:i[1])+" ago"}:(e=Math.floor(s/3600))>=1?{type:"hour",value:e,text:e+" "+(1===e?o[0]:o[1])+" ago"}:(e=Math.floor(s/60))>1?{type:"minute",value:e,text:e+" "+(1===e?a[0]:a[1])+" ago"}:{type:"now",value:"",text:c[0]}},wr.bulkDelete=function(t,e){wr.each(t,(function(t,n){delete e[n]}))},wr.bfaTrack=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};window.bfa&&window.bfa instanceof Function&&window.bfaBinder&&window.bfa(t,e)},wr.getScreenOrientation=function(){if(screen.orientation&&screen.orientation.type)return screen.orientation.type.replace("-primary","");if(window.orientation)return 90===Math.abs(window.orientation)?"landscape":"portrait";if(window.matchMedia){if(window.matchMedia("(orientation: portrait)").matches)return"portrait";if(window.matchMedia("(orientation: landscape)").matches)return"landscape"}return"landscape"},wr.isIOSIPad="undefined"!==typeof navigator&&!!navigator.userAgent.toLowerCase().match(/ipad/),wr.isIOSMobile="undefined"!==typeof navigator&&!!navigator.userAgent.toLowerCase().match(/iphone|ipod/);var Ar=wr.isIOSIPad||wr.isIOSMobile;wr.isIOS=Ar;var Er=/constructor/i.test(window.HTMLElement)||"[object SafariRemoteNotification]"===(!window.safari||"undefined"!==typeof safari&&safari.pushNotification).toString();function _r(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=t;return function(){return e++}}wr.isSafari=Er,wr.createScript=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.head,n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return new Promise((function(r,i){var o=document.createElement("script");o.onload=function(){return r(o)},o.onerror=function(t){qn("script","error",{url:Object(Hn.get)(t,"currentTarget.src","").split("?")[0]}),i("Script failed to load")},o.src=t,o.async=n,e.appendChild(o)}))},wr.createStyleSheet=function(t){return new Promise((function(e,n){var r=document.createElement("link");r.rel="stylesheet",r.href=t,r.onload=function(){return e(r)},r.onerror=function(){return n("".concat(t," failed to load"))},document.head.appendChild(r)}))},wr.stripHTML=function(t){var e=Array.isArray(t)?[]:{},n=document.createElement("div");switch(typeof t){case"string":return n.innerHTML=t,n.textContent||"";case"object":return wr.each(t,(function(t,n){return e[t]=wr.stripHTML(n)})),e}return t},wr.truncate=function(t,e){if(!t||!t.textContent||!t.textContent.length)return 0;var n=t.textContent,r=t.textContent.substring(0,e),i=t.innerHTML?t.innerHTML.substring(0,e):null;if(!t.innerHTML||r===i)return t.textContent=n===r?r:r.replace(/\s*$/,"")+"...",r.length;var o=0;return wr.each(t.childNodes,(function(n,r){o>=e?t.removeChild(r):o+=wr.truncate(r,e-o)})),t.textContent.length},wr.decorateWithMixins=function(t){if(!Object(Hn.isFunction)(t))throw new TypeError("".concat(t," is not a function"));for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];n.forEach((function(t,e){if(void 0===t)throw new TypeError("Mixin at position ".concat(e," is undefined"))}));var i=[t];return n.forEach((function(t){var e=i[0],n=function(t){function e(){return Object(le.a)(this,e),Or(this,e,arguments)}return Object(pe.a)(e,t),Object(fe.a)(e)}(e);Object(Hn.isFunction)(t)&&(t=t(e.prototype)),Object.assign(n.prototype,t),i.unshift(n)})),i[0]},wr.idGenerator=_r;var Pr=Object(fe.a)((function t(){var e,n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=r.unsubscribe,o=void 0===i?function(){}:i;Object(le.a)(this,t);var a=new Promise((function(t,r){e=t,n=r}));return a.resolve=e,a.reject=n,a.unsubscribe=o,a}));wr.Deferred=Pr,wr.moduleIsInContext=function(){return!0};var Rr,xr=wr,Tr=!1;function kr(t,e){var n=t.data||{},r=n.source,i=n.action,o=n.data;if("dfpNativeTemplate"===r&&i&&"object"===typeof o&&null!==o){var a=function(t){for(var e in t){if(/Encoded$/.test(e))t[e.replace("Encoded","")]=decodeURIComponent(t[e])}for(var n in t){var r=t[n];"string"!==typeof r||/Encoded$/.test(n)||(t[n]=r.trim().replace(/\\('|\u2019)/g,"$1").replace(/\s*\n\s*/g,"\n").replace(/\s{2,}/g," "))}return t}(o);e.trigger("post-message:".concat(a.wid),{action:i,ad:a})}else"adlibSnippet"===r?e.trigger("post-message:creativeSnippet",t.data):"fan"===r&&"error"===i&&"object"===typeof o&&null!==o?e.trigger("post-message:".concat(o.wid),{action:"error",error:"FAN"}):"jumbotron"===r&&e.trigger("post-message:jumbotron:".concat(o.wid),{action:i,data:o})}Tr=!1,Rr=void 0;var Cr={start:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Tr||(Tr=!0,Rr=function(e){return Cr.handleMessageEvent(e,t.eventBus)},window.addEventListener("message",Rr))},handleMessageEvent:function(t,e){(function(t){var e=t.origin;if(!e)return!1;var n=navigator.userAgent.match(/Edge/i)&&navigator.userAgent.match(/Windows/i);return/(?:buzzfeed(?:news)?\.(?:com|io)|tasty\.co|googlesyndication\.com)/.test(e)||n&&/^javascript:/.test(e)})(t)&&kr(t,e)},stop:function(){window.removeEventListener("message",Rr)}},Lr=function(t){return t&&t[Math.floor(Math.random()*t.length)]},Ir=n("Ntd4"),Mr=n("hSXu"),Sr=n("p5SE"),zr={adcall:"".concat("\ud83d\udcb8 >>"," [ad call]"),amazonbidder:"".concat("\ud83d\udcb8 >>"," [amazonBidder]"),consent:"".concat("\ud83d\udcb8 >>"," [consent]"),general:"".concat("\ud83d\udcb8 >>"," [adlib]"),lifecycle:"".concat("\ud83d\udcb8 >>"," [ad lifecycle]"),performance:"".concat("\ud83d\udcb8 >>"," [performance]"),placementlogic:"".concat("\ud83d\udcb8 >>"," [placement logic]"),plugins:"".concat("\ud83d\udcb8 >>"," [plugins] "),prebid:"".concat("\ud83d\udcb8 >>"," [prebid]"),targeting:"".concat("\ud83d\udcb8 >>"," [targeting]"),thirdparty:"".concat("\ud83d\udcb8 >>"," [thirdparty]")},Nr=Object.keys(zr).filter((function(t){return"general"!==t})).map((function(t){return t.toLowerCase()}));function Dr(t,e,n){for(var r=arguments.length,i=new Array(r>3?r-3:0),o=3;o<r;o++)i[o-3]=arguments[o];try{var a=Object(Bn.d)(window.location.search),c=Object.keys(a).includes("adlib-debug-mode"),s=a["adlib-debug-mode"];if(c&&!s)return Br.apply(void 0,[t,e.toLowerCase(),n].concat(i));if(c&&s){var u=s.toLowerCase().split(",");u.forEach((function(r){return Nr.includes(r)&&e.toLowerCase()===r?Br.apply(void 0,[t,e.toLowerCase(),n].concat(i)):null}))}}catch(l){console.log(l)}return null}function Br(t,e,n){for(var r,i=arguments.length,o=new Array(i>3?i-3:0),a=3;a<i;a++)o[a-3]=arguments[a];return window.console&&window.console.log?(r=window.console)[t].apply(r,[zr[e]+": "+n].concat(o)):""}var Gr=n("xwdf"),Fr="image",Ur="embed",Vr="video (GAM)",Hr={queryStringToObject:Bn.c};function Zr(){var t={adtest:"test",giraffe_test:"giraffe_test",adops_giraffe:"adops_giraffe",ads_qa:"ads_qa"},e=Hr.queryStringToObject(window.location.search);for(var n in t)if(n in e)return t[n];return""}function Wr(t,e){var n=e.env,r=e.localization,i=void 0===r?{}:r,o=e.bidder,a=void 0===o?"":o,c=(n.userCountry||"us").toLowerCase(),s=Gr.a.get(zn.b);s&&(c=s.toLowerCase());var u,l,f=t.adPos,d=t.adType,h=t.zone1,p="/".concat("6556","/");Dr("info","adcall","buildAdCall -> userCountry",c),"au"!==c&&"nz"!==c||"bfnews"===h||(p+="au."),"gb"!==c&&"ie"!==c&&"uk"!==c||"bfnews"===h||(p+="uk."),p+=h||"bfd",u=n.localization.language?n.localization.language:"getRawPageLanguage"in i?i.getRawPageLanguage():"en",l=n.localization.edition?n.localization.edition:n.localization.country?n.localization.country:"getEdition"in i?i.getEdition():"en-us","es"===u&&("es-es"===l?u="sp":"es-mx"===l&&(u="mx"));var g=Yr(t,{env:n}),b=qr(t),v=Zr(),m="";t.advertiserContext&&t.useNewAdCallStructure?m="partner":/^awareness/.test(f)||/^awareness/.test(d)?m="awareness":/^infinite_post/.test(f)?(m="recirc",t.advertiserContext=!1):"ex"===d&&"bfnews"!==h&&"tasty"!==h&&"giftguide"!==g&&(m="ex"),t.advertiserContext&&!t.useNewAdCallStructure&&(m="partnerpost",ur.isMobileApp()&&(b="mobile"),"ja"===u&&(m="ja.".concat(m)));var y=[v,m,b].filter((function(t){return t})).join(".");if(t.advertiserContext&&!t.useNewAdCallStructure)return"".concat(p,".").concat(y,"/").concat(g);var O="amazon"===a?"".concat(p,".").concat(y,"/").concat(g,"/").concat(f):"aiquizzes"===f?"".concat(p,".").concat(y,"/").concat(u,"/").concat(f):"".concat(p,".").concat(y,"/").concat(u,"/").concat(g,"/").concat(f);return Dr("info","adCall","is bidder amazon?","amazon"===a),Dr("info","adCall","Ad is built:",O),O}function qr(t){var e=yr.isAny(["xs","sm"])?"mobileweb":"desktop";return ur.isMobileApp()&&(e="bfapp_ios",ur.isAndroid()&&(e="bfapp_android")),"thumb-header"===t.adPos&&(e="desktop"),t.advertiserContext&&"desktop"===e&&!t.useNewAdCallStructure&&(e=""),e}function Yr(t,e){var n=e.env,r=t.adPos,i=t.zone1,o=(n.pageSection||n.pageCategory||n.pageName||"home").toLowerCase();return t.advertiserContext?t.useNewAdCallStructure?o="partner":(o="advertiser",/^(bigstory|subbuzz|quiz)/.test(r)&&Dr("info","adCall",o="".concat(o,"/").concat(r),"is an advertiser page")):"bfd.bio"!==n.destination&&("bringme"===o||/travel-/.test(o)?o="travel":Jr(n)?o=n.pageName:/news/.test(o)&&"bfnews"!==i?o="news":/(As\/?Is)/i.test(n.pageSection)?o="asis":n.pageFilter?Object.keys(n.pageFilters||{}).length>0?o="".concat(o,"-").concat(n.pageFilter):"health"===n.pageSection?o="health":/^(archive|badge)$/.test(o)||(o="ai-quiz"===n.pageFilter?"aiquizzes":n.pageFilter):/section/.test(o)&&(o=n.pageSection)),Dr("info","adCall",o=o.replace(/&/g,"").replace(/\s+/g,"-"),"is a non advertiser page"),o}function Jr(t){return Dr("info","adCall","cms tags:",t.cmsTags),!(!t.pageFilters||!Object.keys(t.pageFilters).length)}var Qr={pinterest:"pinterest",twitter:"twitter","t.co":"twitter",facebook:"facebook","m.facebook":"facebook",fban:"facebook",google:"google",youtube:"youtube",yahoo:"yahoo"},Kr=["animals","arts-entertainment","asis","books","bringme","business","buzz","celebrity","community","culture","entertainment","food","giftguide","goodful","inequality","internet-culture","investigations","jpg","lgbtq","life","music","nifty","opinion","parents","politics","quizzes","reader","reviews","rewind","science","shopping","tech","travel-budget-trips","travel-destinations","travel-food","travel-hacks","travel-products","travel-unique-experiences","tvandmovies","unsolved","videos","weddings","world"],Xr=["/travel-destinations/australia","/travel-destinations/canada","/travel-destinations/japan","/travel-destinations/london","/travel-destinations/los-angeles","/travel-destinations/mexico","/travel-destinations/new-york-city","/travel-destinations/paris","/travel-destinations/united-states"];function $r(t){var e=t.env,n=function(t){if(!t)return null;try{var e=new URL(t);if(!/buzzfeed(news)?.com/i.test(e.host))return null;var n=e.pathname;if(Xr.includes(n))return n.slice(1).replace(/\//g,"-")}catch(i){}var r=t.match(/buzzfeed(news)?.com\/(section\/)?(\w*)/i)||[];return(r=r.reverse()[0])&&Kr.includes(r)?r=r.toLowerCase():null}(document.referrer);return(e.isBPage&&n?n:null)||function(t){if(!t||""===t)return null;var e=new URL(t),n=null,r=(t.match(/(m\.)?facebook|t\.co|pinterest|google|youtube|yahoo/i)||[])[0];r&&(r=r.toLowerCase());/buzzfeed(news)?.com/i.test(e.host)||(n=e.host.replace("www.",""));return Qr[r]||n}(document.referrer)||function(t){var e=(t.match(/fban|twitter|pinterest|google|youtube/i)||[])[0];e&&(e=e.toLowerCase());return Qr[e]}(navigator.userAgent)||function(t){var e=(t.match(/referrer=(\w+)/)||[])[1];e&&(e=e.toLowerCase());return Qr[e]}(window.location.search)||""}function ti(t){if(t.length<=40)return t;var e=t.lastIndexOf("-"),n=t.substring(e),r=t.length-40,i=t.substring(0,e-r);return(i=i.replace(/(-|_){1}$/,""))+n}var ei={prebidBidCache:function(t){return t.isOn("ads_bid_cache").then((function(t){return"".concat("ads_bid_cache","|").concat(t)}))},swapRefresh:function(t){return t.getExperimentVariant("RT-994-swap-refresh").then((function(t){return"".concat("RT-994-swap-refresh","|").concat(t)}))}};function ni(){var t=Object(Bn.c)(window.location.search),e=decodeURIComponent(t["dfp-keyword"]||"").split(",");return/^([a-zA-Z0-9]|-|_|,)*$/.test(e)?e:""}function ri(t){var e=t.env;if(e.isFeedPage){if(e.isBFO&&e.pageName)return[e.pageName];if(e.isBFN&&e.pageSection)return[e.pageSection]}return e.allPageSections.map((function(t){return t=(t=t.toLowerCase().replace(/\/+/g,"")).replace(/ & /g,"and")}))}function ii(t){return t.env.cmsTags.map((function(t){return t.toLowerCase().replace(/\s+/g,"_")}))}var oi={get poe(){return ur.isMobileApp()?ur.isIOS()?"bfapp_ios.fallback":"bfapp_android.fallback":ur.isNewsApp()||ur.isNews2App()?ur.isIOS()?"newsapp_ios.fallback":"newsapp_android.fallback":null}};function ai(t){var e=t.env,n=t.abeagle,r=t.gdpr,i=Object(Hn.values)(ei).map((function(t){return t(n)}));return Promise.all(i).then((function(t){var n,r={abtest:(n=[]).concat.apply(n,Object(c.a)(t)).filter((function(t){return t})).map(ti)},i=function(t){var e=t.env;try{if(e.isFeedPage)return"A";if(e.isBPage)return"B"}catch(n){}return null}({env:e});i&&(r.pagetype=i),r.poe=function(t){var e=t.env;Dr("info","targeting","getPoe",[oi.poe,$r({env:e})]);var n=$r({env:e});return oi.poe||n?[oi.poe,n].filter((function(t){return null!==t})):[]}({env:e});var o=ni();o.length>0&&(r["dfp-keyword"]=o),r.creativeSet=Lr(["A","B","C","D"]);var a=ri({env:e});a.length&&(r.section=a);var s=ii({env:e});return s.length&&(r.cms_tag=s),r.destination=function(t){var e=t.env,n=[e.destination];if(e.isHomePage)n.push("".concat(e.destination,"homepage"));else if(e.isFeedpager||e.isFeed){var r=e.pageName||e.pageSection||e.pageCategory,i=/section/.test(r),o=/tag/.test(r),a=/vertical/.test(r),c=/badge/.test(r);if(i?r=e.pageSection:(o||a||c)&&(r=e.pageFilter,c&&n.push("badgefeed")),"buzz"===r&&(r="buzztag"),r){var s=e.pageFilter?"".concat(e.pageName,"-").concat(e.pageFilter):e.pageName,u=Jr(e)?s:"".concat(r.toLowerCase());n.push("".concat(u,"feed"))}}return n}({env:e}),r})).then((function(t){return Promise.all([(i={gdpr:r},i.gdpr.hasConsented().then((function(t){return t?Object(Mr.a)():"000000-noconsent"}))),(n={gdpr:r},n.gdpr.hasConsented().then((function(t){return t?Object(Ir.a)():"000000-noconsent"}))),(e={gdpr:r},e.gdpr.hasConsented().then((function(t){var e=Sr.a.get("cet-page_session_id")||"";return t?e:"000000-noconsent"})))]).then((function(e){var n=Object(a.a)(e,3),r=n[0],i=n[1],o=n[2];return r&&(t.cuid=r.toString()),i&&(t.cvid=i),o&&(t.cpid=o),t}));var e,n,i}))}var ci={getPageTargeting:function(t){var e=t.env,n=t.abeagle,r=t.gdpr,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Promise.resolve(ai({env:e,abeagle:n,gdpr:r})).then((function(t){return Promise.all([t,i])})).then((function(t){var e=Object(a.a)(t,2),n=e[0],r=e[1];return Object(Hn.mergeWith)(n,r,(function(t,e){if(Object(Hn.isArray)(t))return t.concat(e)}))}))},getDFPKeyword:ni,trimExperimentKey:ti},si=["2162785669"],ui=["10226773"],li=["20072413","102536773","35253013","495329533","12342133","263657533","12340813","135123253","235801453"],fi=n("jNoi");function di(t){Error.apply(this,arguments),this.message=t}di.prototype=Object.create(Error.prototype),di.prototype.constructor=di;var hi=function(t){function e(){return Object(fi.b)(this,e),Object(fi.a)(this,e,arguments)}return Object(fi.g)(e,t),Object(fi.c)(e)}(di),pi=function(t){function e(){return Object(fi.b)(this,e),Object(fi.a)(this,e,arguments)}return Object(fi.g)(e,t),Object(fi.c)(e)}(di);function gi(t,e,n){var r=t.eventName,i=t.unitOptions,o=void 0===i?{}:i,a=t.tagOptions,c=void 0===a?{}:a,s=e.env,u=e.localization,l={};if("adPos"in o){l.pos=o.adPos;var f=Wr(o,{env:s,localization:u}).match(/6556\/([^/]+)/)[1].split(".").filter((function(t){return["bfd","desktop","mobileweb","partnerpost"].indexOf(t)>-1}));l.dfp_platform=f.join(".")}l.pos&&(l.pos=l.pos.replace(/-bp$/,"").replace(/[0-9]/g,"")),l.edition=s.localization.edition,Object.assign(l,c);var d,h=(d={samplingRate:bi(r)},Object(Hn.pickBy)(d,(function(t){return void 0!==t})));n?n({eventName:r,tags:l,bfaConfig:h}):window.bfa&&qn("ads",r,l,h)}function bi(t){return/bid/.test(t)?.01:/click/.test(t)?1:void 0}var vi,mi,yi={};function Oi(t){mi=t||[]}function wi(){mi&&mi.length&&(mi=[])}var ji=function(t){var e=t.pbjs,n=t.env,r=t.localization,i=t.tracking;function o(t,e,o){return gi({eventName:t,unitOptions:e,tagOptions:o},{env:n,localization:r},i&&i.trackPrebidEvents?i.trackPrebidEvents:null)}yi={bidTimeout:Oi,bidResponse:function(t){return function(t,e){if(e){var n=mi&&mi.includes(e.bidder);if(n){var r=e.bidder,i=e.adUnitCode;window.raven&&window.raven.captureMessage("bidTimeout",{bidder:r,adUnitCode:i})}t("bid",vi,{bidder:e.bidder,bidTimeToRespond:e.timeToRespond,bidTimeout:n?1:0})}}(o,t)},bidWon:function(t){return function(t,e){var n;e&&t("winningbid",vi,{bidder:e.bidder,cpm:(n=e.cpm,Math.ceil(20*n)/20)})}(o,t)},auctionEnd:wi},Object.keys(yi).forEach((function(t){e.onEvent(t,yi[t])}))},Ai=function(t){vi=t},Ei="web_performance_metric",_i="ad_slot_render",Pi="ad_slot_request",Ri="gpt_tag_load_time",xi="adlib_init_time",Ti="prebid_bid_request",ki="prebid_duration",Ci="tam_bid_request",Li="tam_duration",Ii=n("ja9M"),Mi=function(){var t=window;return"function"===typeof t.describe&&"function"===typeof t.expect},Si=function(){return window._isAdsServiceTest},zi=function(){var t="undefined"!==typeof navigator&&(navigator.connection||navigator.mozConnection||navigator.webkitConnection);return t?t.effectiveType:""},Ni=function(){return{connection_type:zi()}},Di=function(t){var e=t.destination,n=t.localization,r=t.pageId,i=t.analyticsPageType,o=t.pageCategory,a=t.webRoot,c=t.isBFO,s=t.isBFN,u=t.isBPage,l=t.isFeedPage,f=t.isHomePage,d="web_".concat(e);(c||s)&&(d="web_bf");var h=i||o;u&&(h="buzz"),(l||f)&&(h="feed");var p=["bfnews","buzzfeednews","buzzfeed_news"],g=e;return Array.isArray(g)&&g.some((function(t){return p.includes(t)}))&&(g="buzzfeed_news"),{destination:g,source:d,context_page_id:r,context_page_type:h,page_edition:n.country,referrer_uri:a||document.referrer||""}},Bi=function(){if(window.performance&&"function"===typeof window.performance.mark){for(var t,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];Dr("info","performance","performance.mark",n),(t=performance).mark.apply(t,n)}else Dr("info","performance","performance.mark not supported")},Gi=Object(o.default)(Object(o.default)(Object(o.default)(Object(o.default)(Object(o.default)(Object(o.default)(Object(o.default)(Object(o.default)({},Ri,(function(t){var e=!1;return function(){if(!e){for(var n=performance.getEntriesByName("gpt_tag_end")[0],r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];t.apply(void 0,[{type:Ei},{metric_name:Ri,metric_value:n.startTime,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:n?JSON.stringify(n):null}].concat(i)),e=!0}}})),xi,(function(t){return function(){for(var e=performance.measure(xi,"adlib_init_start","adlib_init_end"),n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];t.apply(void 0,[{type:Ei},{metric_name:xi,metric_value:e.duration,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:e?JSON.stringify(e):null}].concat(r))}})),_i,(function(t){var e=!1;return function(){if(!e){for(var n=performance.getEntriesByName(_i)[0],r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];t.apply(void 0,[{type:Ei},{metric_name:_i,metric_value:n.startTime,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:n.detail?JSON.stringify(n.detail):null}].concat(i)),e=!0}}})),Pi,(function(t){var e=!1;return function(){if(!e){for(var n=performance.getEntriesByName(Pi)[0],r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];t.apply(void 0,[{type:Ei},{metric_name:Pi,metric_value:n.startTime,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:n.detail?JSON.stringify(n.detail):null}].concat(i)),e=!0}}})),Ti,(function(t){var e=!1;return function(){if(!e){for(var n=performance.getEntriesByName(Ti)[0],r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];t.apply(void 0,[{type:Ei},{metric_name:Ti,metric_value:n.startTime,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:n.detail?JSON.stringify(n.detail):null}].concat(i)),e=!0}}})),ki,(function(t){return function(){for(var e=performance.measure(ki,Ti,"prebid_bid_response"),n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];t.apply(void 0,[{type:Ei},{metric_name:ki,metric_value:e.duration,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:e?JSON.stringify(e):null}].concat(r))}})),Ci,(function(t){var e=!1;return function(){if(!e){for(var n=performance.getEntriesByName(Ci)[0],r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];t.apply(void 0,[{type:Ei},{metric_name:Ci,metric_value:n.startTime,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:n.detail?JSON.stringify(n.detail):null}].concat(i)),e=!0}}})),Li,(function(t){return function(){for(var e=performance.measure(Li,Ci,"tam_bid_response"),n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];t.apply(void 0,[{type:Ei},{metric_name:Li,metric_value:e.duration,metric_type:"custom",metric_metadata_type:"json",metric_metadata_value:e?JSON.stringify(e):null}].concat(r))}})),Fi=function(){var t,e="stage",n={};return function(r){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!Mi()&&!Si()){i.isProd?e="prod":i.isDev&&(e="dev"),t||(t=Object(Ii.a)({env:e,source:"adlib"}),Dr("info","performance","trackPerformanceEvent: env: ".concat(e)));var o=Di(i),a=Ni();Dr("info","performance","trackPerformanceEvent: ".concat(r),o),Gi[r]?(n[r]||(n[r]=Gi[r](t)),n[r](o,a)):Dr("warn","performance","trackPerformanceEvent: ".concat(r," not found"))}}}();function Ui(){var t=Gr.a.get("hem");if(t)return Vi(t);var e=Gr.a.get("bf2-b_info");return Dr("info","general","user info (bf2-b_info cookie)",e),e&&fetch("/auth/ad-track-token/hem",{credentials:"same-origin"}).then((function(e){return e.ok?Vi(t=Gr.a.get("hem")):(console.error("Failed to fetch HEM"),null)})).catch((function(t){return console.error(t)})),null}function Vi(t){return t=t.replace(/"/g,""),atob(t)}var Hi,Zi={loadScript:Dn.a},Wi=["subbuzz","pixel"],qi=new Pr;function Yi(t){var e=t.abeagle;return/\bs=mobile_app\b/.test(window.location.search)?Promise.reject(new hi):e.isOn("ads_prebid").then((function(t){return t?Promise.resolve(!0):Promise.reject(new hi)}))}var Ji=function(){return Object(fi.c)((function t(e){var n=e.env,r=e.localization,i=e.abeagle,o=e.gdpr,a=e.ccpa,c=e.tracking;Object(fi.b)(this,t),this._env=n,this._localization=r,this._abeagle=i,this._gdpr=o,this._ccpa=a,this._tracking=c}),[{key:"init",value:function(){var t=this;return Hi||(Ui(),Hi=Yi({abeagle:this._abeagle}).then((function(){return t._loadLib()})).then((function(){return qi})).then((function(e){var n=e.getConfig();n.ortb2={site:{ext:{data:{cms_tags:ii({env:t._env}),section:ri({env:t._env})}}}},e.setConfig(n),Dr("info","prebid","_prebidConfig:",e.getConfig()),Dr("info","prebid","pbjs:",e),ji({pbjs:e,env:t._env,localization:t._localization,tracking:t._tracking})})).catch((function(t){return t instanceof hi?null:(console.error(t),Promise.reject(t))})))}},{key:"_loadLib",value:function(){return Zi.loadScript("https://micro.rubiconproject.com/prebid/dynamic/13062.js").then((function(){return window.pbjs=window.pbjs||{},window.pbjs.que=window.pbjs.que||[],new Promise((function(t){window.pbjs.que.push((function(){qi.resolve(window.pbjs),t()}))}))}))}}])}(),Qi=function(){return Object(fi.c)((function t(e){var n=e.env,r=e.gdpr,i=e.ccpa,o=e.abeagle;Object(fi.b)(this,t),this._env=n,this._gdpr=r,this._ccpa=i,this._abeagle=o}),[{key:"requestBid",value:function(t,e){var n=this,r=e.adPos,i=e.path,o=e.slot;return Wi.includes(r)?Promise.resolve(null):Yi({abeagle:this._abeagle}).then((function(){return qi})).then((function(t){var a=new Pr,c=n._env,s=function(){Bi("prebid_bid_response",{detail:{slot:i}}),Fi(ki,c),a.resolve(null)};return Ai(e),setTimeout((function(){Bi(Ti,{detail:{slot:i}}),Fi(Ti,n._env),t.rp.requestBids({callback:s,data:{adserver:{adslot:i,name:"gam"},pbadslot:i,cmsTags:ii({env:n._env}),section:ri({env:n._env}),zone3:Yr({adPos:r},{env:n._env}),visitor:{permutive:JSON.parse(window.localStorage._prubicons||"[]").slice(0,250)}},gptSlotObjects:[o]})}),0),a})).catch((function(t){return t instanceof hi||t instanceof pi?null:(console.error(t),Promise.reject(t))}))}}])}(),Ki=n("6S4M"),Xi={add:function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return Object(Hn.unionBy)(t,n,JSON.stringify)},exclude:function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return Object(Hn.differenceBy)(t,n,JSON.stringify)},_filterProgrammatic:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return!0};return t=t.map(JSON.stringify),e.filter((function(e){return-1===t.indexOf(JSON.stringify(e))||n(e)}))},filterProgrammatic:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.min,i=void 0===r?null:r,o=n.max,a=void 0===o?null:o;return this._filterProgrammatic(t,e,(function(t){return!!(i&&t[0]>=i[0]||a&&t[0]<=a[0])}))},excludeProgrammatic:function(t,e){return this._filterProgrammatic(t,e,(function(){return!1}))},getProgrammatic:function(t,e){return Object(Hn.differenceBy)(e,this.excludeProgrammatic(t,e),JSON.stringify)},isProgrammatic:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.strict,i=void 0===r||r,o=this.getProgrammatic(t,[e]),c=1===o.length;if(c)return!0;if(!i)try{var s=Object(a.a)(e,2),u=s[0],l=s[1];return u>15&&l>15}catch(f){return!1}return!1},isEqual:function(t,e){return t===e||JSON.stringify(t)===JSON.stringify(e)},contains:function(t,e){var n=this;return t.filter((function(t){return n.isEqual(t,e)})).length>0}},$i={automotive_and_vehicles:[{id:"1",name:"Automotive"}],books_and_literature:[{id:"42",name:"Books and Literature"}],business_and_industrial:[{id:"52",name:"Business and Finance"}],careers:[{id:"123",name:"Careers"}],education:[{id:"132",name:"Education"}],shows_and_events:[{id:"150",name:"Events and Attractions"}],family_and_parenting:[{id:"186",name:"Family and Relationships"}],art_and_entertainment:[{id:"201",name:"Fine Art"}],food_and_drink:[{id:"210",name:"Food & Drink"}],health_and_fitness:[{id:"223",name:"Healthy Living"},{id:"286",name:"Medical Health"}],hobbies_and_interests:[{id:"239",name:"Hobbies & Interests"}],home_and_garden:[{id:"274",name:"Home & Garden"}],science_medicine_medical_research:[{id:"286",name:"Medical Health"}],movies:[{id:"324",name:"Movies"}],music:[{id:"338",name:"Music and Audio"}],law_govt_and_politics:[{id:"379",name:"News and Politics"}],finance:[{id:"391",name:"Personal Finance"}],pets:[{id:"422",name:"Pets"}],art_and_entertainment_movies_and_tv_television:[{id:"432",name:"Pop Culture"},{id:"640",name:"Television"}],real_estate:[{id:"441",name:"Real Estate"}],religion_and_spirituality:[{id:"453",name:"Religion & Spirituality"}],science:[{id:"464",name:"Science"}],shopping:[{id:"473",name:"Shopping"}],sports:[{id:"483",name:"Sports"}],style_and_fashion:[{id:"552",name:"Style & Fashion"}],technology_and_computing:[{id:"596",name:"Technology & Computing"}],travel:[{id:"653",name:"Travel"}],games:[{id:"680",name:"Video Gaming"}]},to=function(t){var e;return(null===(e=$i[t])||void 0===e?void 0:e.map((function(t){return t.id})))||[]},eo=function(t){var e;return(null===(e=$i[t])||void 0===e?void 0:e.map((function(t){return t.name})))||[]},no=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"id",n={id:to,name:eo};return t.map((function(t){return n[e](t)})).reduce((function(t,e){return t.concat(e)}),[]).filter((function(t,e,n){return t&&n.indexOf(t)===e}))},ro=function(t){return Object.values((null===t||void 0===t?void 0:t.watson)||[]).reduce((function(t,e){return t.concat(e.map((function(t){return t.tag_name})))}),[])},io=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"id";return no(ro(t),e)};function oo(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function ao(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?oo(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):oo(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var co,so;function uo(t){var e=t.abeagle;return/\bs=mobile_app\b/.test(window.location.search)?Promise.reject(new hi):e.isOn("ads_amazon_tam").then((function(t){return!!t||Promise.reject(new hi)}))}co=void 0,so=new Pr;var lo=function(){return Object(fi.c)((function t(e){var n=e.env,r=e.localization,i=e.abeagle,o=e.gdpr,a=e.ccpa;Object(fi.b)(this,t),this._env=n,this._localization=r,this._abeagle=i,this._gdpr=o,this._ccpa=a,this._AIGCC=!1,this._amazonSignals=!1}),[{key:"init",value:function(){var t=this;return co||(co=uo({abeagle:this._abeagle}).then((function(){return Promise.all([t._abeagle.isOn("RT-1053-Amazon_Signals")])})).then((function(e){return t._env.hasAigccTag?(t._AIGCC=!0,Object(Dn.a)("https://c.aps.amazon-adsystem.com/apstag.js")):(t._amazonSignals=e[0],Object(Dn.a)("https://c.amazon-adsystem.com/aax2/apstag.js"))})).then((function(){return t._configure()})).catch((function(t){return t instanceof hi?null:(console.error(t),Promise.reject(t))})))}},{key:"_updateAMZNToken",value:function(t,e,n){var r={optOut:null!==t&&"Y"===t.charAt(2)||null!==e&&!e,hashedRecords:new Array({type:"email",record:n})};Dr("info","amazonBidder","token config",r),window.apstag.rpa(r,(function(t){Dr("info","amazonBidder","token:",t)}))}},{key:"_formatSection",value:function(t){return t||(t="NoSection"),t.match("/")&&(t=t.replace("/","")),Dr("info","amazonBidder","section",t),t}},{key:"_configure",value:function(){var t=this;if(!window.apstag){var e=new Error("No `apstag` global");throw so.reject(e),e}var n=Object(Ki.b)(),r=this._env.pageSection||"",i=Ui();i||(Dr("info","amazonBidder","no hem cookie - user is not logged in"),Gr.a.get("AMZN-Token")&&(Dr("info","amazonBidder","found AMZN-Token, deleting"),window.apstag.dpa()));return Promise.all([this._ccpa.getConsentValue(),this._gdpr.fetchAdPurposeConsent()]).then((function(e){var o,c=Object(a.a)(e,2),s=c[0],u=c[1],l=t._env.pageId,f=(null===(o=t._env)||void 0===o?void 0:o.asins)||{buzzAsins:[],sourceAsins:[]},d=ri({env:t._env}),h={pubID:"JP"===n?"3675":"3713",adServer:"googletag",params:{cms_tags:ii({env:t._env}),section:d,si_section:t._formatSection(r),us_privacy:s||"1---"},gdpr:{cmpTimeout:50},deals:!0};t._amazonSignals?(h.videoServer="DFP",h.signals={ortb2:{site:{cat:io(t._env.laserTags,"id"),cattax:6,ext:{sitetaxonomy:ro(t._env.laserTags).join()},kwarray:io(t._env.laserTags,"name"),page:window.location.origin+window.location.pathname,pagecat:no([t._env.pageCategory.toLowerCase()],"id"),publisher:{id:"1978e933-c244-42df-b00f-66a0b717c789"},sectioncat:no(d.map((function(t){return t.toLowerCase()})),"id")}}},Dr("info","amazonBidder","apstag.init params for _amazonSignals",ao({},h)),window.apstag.init(h)):t._AIGCC?(h.videoServer="DFP",h.signals={ortb2:{site:{page:window.location.origin+window.location.pathname,publisher:{id:"1978e933-c244-42df-b00f-66a0b717c789"}}}},Dr("info","amazonBidder","apstag.init params for _AIGCC",ao({},h)),window.apstag.init(h),window.apstag.queue.push((function(){window.apstag.nativeContent().enable({sourceAsins:f.sourceAsins,pageAsins:f.buzzAsins,pageId:l})}))):(Dr("info","amazonBidder","apstag.init params for default",ao({},h)),window.apstag.init(h)),so.resolve(window.apstag),t._abeagle.isOn("ads_tam_hem").then((function(e){Dr("info","amazonBidder","ads_tam_hem feature flag on?",e),i&&e&&t._updateAMZNToken(s,u,i)}))})),this._ccpa.getConsentValue()}}])}(),fo=function(){return Object(fi.c)((function t(e){var n=e.env,r=e.googletagReady,i=e.abeagle;Object(fi.b)(this,t),this._env=n,this._localization=n.localization,this._googletagReady=r,this._abeagle=i,Dr("info","amazonBidder","BidRequester env:",n)}),[{key:"requestBid",value:function(t,e){var n=this;Dr("info","amazonBidder","requestBid options:",e);var r=this._env;return uo({abeagle:this._abeagle}).then((function(){return Promise.all([so,n._getBidSlot(t,e)])})).then((function(t){var e=Object(a.a)(t,2),n=e[0],r=e[1];return r?(Dr("info","amazonBidder","slot:",r),[n,r]):Promise.reject(new pi)})).then((function(t){var e=Object(a.a)(t,2),n=e[0],i=e[1];return new Promise((function(t,e){var o=function(){Bi("tam_bid_response",{detail:{slot:i.slotName}}),Fi(Li,r),t()};setTimeout((function(){Bi(Ci,{detail:{slot:i.slotName}}),Fi(Ci,r);try{var t=r.pageId,a=(null===r||void 0===r?void 0:r.asins)||{buzzAsins:[],sourceAsins:[]},c={slots:[i],timeout:800,sourceAsins:a.sourceAsins,pageAsins:a.buzzAsins,pageId:t};Dr("info","amazonBidder","fetchBidsParams",ao({},c)),n.fetchBids(c,o)}catch(s){console.error(s),e()}}),0)}))})).then((function(){return new ho({options:e,googletagReady:n._googletagReady})})).catch((function(t){return t instanceof hi||t instanceof pi?null:(console.error(t),Promise.reject(t))}))}},{key:"_isPageMultiFormatEligible",value:function(t){var e=(t.pageSection||t.pageCategory||t.pageName||"home").toLowerCase();return!t.hasQuiz&&"quizzes"!==e&&(t.isBFO||t.isBFN)}},{key:"_getBidSlot",value:function(t,e){var n=e.adPos,r=e.wid,i=e.size,o=Xi.getProgrammatic(this._env.programmaticSizes,i);if(0===o.length)return null;var a={slotID:t.getSlotContainerId(r),slotName:t.buildAdCall(e,{env:this._env,localization:this._localization,bidder:"amazon"})},c=/(^promo-inline.+)|(^sidebar.+)/.test(n);return this._isPageMultiFormatEligible(this._env)&&c?(a.mediaType="multi-format",a.multiFormatProperties={display:{sizes:o},video:{sizes:[[400,300]]}}):a.sizes=o,a}}])}(),ho=function(){return Object(fi.c)((function t(e){var n=e.options,r=e.googletagReady;Object(fi.b)(this,t),this.options=n,this._googletagReady=r}),[{key:"resetTargeting",value:function(){var t=this;return this._googletagReady.then((function(e){e.pubads().getSlots().filter((function(e){return String(e.getTargeting("wid"))===String(t.options.wid)})).forEach((function(t){t.getTargetingKeys().filter((function(t){return/^amzn/.test(t)})).forEach((function(e){return t.clearTargeting(e)}))}))}))}},{key:"setResponseTargeting",value:function(){return Promise.all([so,this._googletagReady]).then((function(t){var e=Object(a.a)(t,2),n=e[0];Dr("info","amazonBidder","#setResponseTargeting - apstag, googletag:",n,e[1]);try{n.setDisplayBids()}catch(r){Dr("error","amazonBidder","#setResponsTargeting - apstag error",r)}}))}},{key:"setTargeting",value:function(){var t=this;return this.resetTargeting().then((function(){return t.setResponseTargeting()}))}}])}(),po=[".js-ad-thumbnail",".bf-image",".bf-image-big",".bf-image-dblbig",".bf-mobile-image",".bf-bg-image",".bf-image-bigstory",".bf-image-dblwidestory",".bf-image-dblbigstory",".bf-image-widestory"].join(",");function go(){var t=Promise.resolve();return t.unsubscribe=function(){},t}function bo(t,e){if(t.querySelectorAll(po).forEach((function(t){if(!(e.thumbnails.indexOf(t)>-1)){var n;if("IMG"===t.nodeName)n=function(t){if(!t.src||/^data:image/.test(t.src))return null;var e,n=[],r=Object(Bn.a)(t.src).split(".").pop(),i=new Promise((function i(o){if(t.complete)o();else if(/^gif$/i.test(r))if(t.naturalHeight)o();else{var a=setTimeout((function(){i(o)}),100);n.push((function(){return clearTimeout(a)}))}else e||(e=function(){o(),t.removeEventListener("load",e),t.removeEventListener("error",e)},t.addEventListener("load",e),t.addEventListener("error",e),n.push((function(){t.removeEventListener("load",e),t.removeEventListener("error",e)})))}));return i.unsubscribe=function(){n.forEach((function(t){return t()})),n=[]},i}(t);else if("VIDEO"===t.nodeName)n=function(t){var e,n=new Promise((function(n){t.readyState>=HTMLVideoElement.HAVE_METADATA?n():(e=function(){n(),t.removeEventListener("loadedmetadata",e)},t.addEventListener("loadedmetadata",e))}));return n.unsubscribe=function(){return t.removeEventListener("loadedmetadata",e)},n}(t);else if("IFRAME"===t.nodeName)n=go();else{var r=getComputedStyle(t).backgroundImage;/^url/.test(r)&&(n=go())}n&&(e.thumbnails.push(t),e.thumbnailsReady.push(n))}})),0===e.thumbnailsReady.length)return null;var n=Promise.all(e.thumbnailsReady);return n.unsubscribe=function(){e.thumbnails=[],e.thumbnailsReady.forEach((function(t){t.unsubscribe()})),e.thumbnailsReady=[]},n}function vo(t){var e,n={thumbnails:[],thumbnailsReady:[]},r=new Promise((function(r){var i,o;e=function(){i&&i.disconnect(),o&&o.unsubscribe()},(o=bo(t,n))?o.then(r):(i=new MutationObserver((function(){(o=bo(t,n))&&(o.then(r),i.disconnect())}))).observe(t,{subtree:!0,attributes:!0,childList:!0})}));return r.unsubscribe=e,r.then(r.unsubscribe),r}var mo,yo={loadScript:Dn.a},Oo={init:function(t){var e=t.env,n=t.abeagle;return mo||(mo=n.isOn("ads_ad_lightning").then((function(t){return t?Promise.resolve(!0):Promise.reject(new hi)})).then((function(){var t=e.isDev||e.isStage?"buzzfeed-staging":"buzzfeed";return yo.loadScript("".concat("https://tagan.adlightning.com","/").concat(t,"/").concat("op.js"))})).catch((function(t){return t instanceof hi?null:Promise.reject(t)})))}};function wo(t){var e=t.trackFn,n=t.eventName,r=t.eventData,i=t.hasEventUrl,o=void 0!==i&&i;if((e=e||window.bfa)&&n&&r&&"object"===typeof r&&r.l){var a;if(o)a=n;else if("click"===n)a="track/click/".concat(r.l);else{if("scroll"!==n)return;a="track/scroll/impression"}e(a,r)}}var jo,Ao;var Eo=function(t){var e=t.abeagle;return jo||(jo=e.isOn("ads_blockthrough").then((function(t){return t?Promise.resolve(!0):Promise.reject(new hi)})).then((function(){return new Promise((function(t,e){var n=document.createElement("script");n.onload=function(){return t(n)},n.onerror=function(){e("Blockthrough: Script failed to load")},n.src="//buzzfeed-com.videoplayerhub.com/galleryplayer.js",n.async=!0,n.dataset.domain="buzzfeed.com",n.id="BLOCKTHROUGH",document.head.appendChild(n)}))})).catch((function(t){return t instanceof hi?null:Promise.reject(t)})))},_o=function(){return Ao||(Ao=new Promise((function(t){var e=setTimeout((function(){return t(!1)}),3e3);window.addEventListener("AcceptableAdsInit",(function(n){clearTimeout(e),n.detail?t(!0):t(!1)}))})))},Po={loadScript:Dn.a};function Ro(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}}var xo,To=["ids","bsc","vlp","tvp","abs"],ko="https://pub.doubleverify.com/signals/pub.js#ctx=".concat("24202625","&cmp=").concat("DV734342"),Co=new Pr,Lo={buzzfeed:"bfd",buzzfeed_news:"bfnews",bfnews:"bfnews",buzzfeednews:"bfnews",tasty:"tasty"},Io={isOn:Ro((function(t){return t.abeagle.isOn("ads_doubleverify")})),tagReady:xo||Co,init:function(t){var e=t.abeagle,n=t.env;return xo||(window.PQ=window.PQ||{cmd:[]},xo=Io.isOn({abeagle:e}).then((function(){return Po.loadScript(ko)})).then((function(){return new Promise((function(t){window.PQ.cmd.push((function(){Co.resolve(window.PQ),t(window.PQ)}));var e="";"object"===typeof(null===n||void 0===n?void 0:n.destination)?e=Lo[n.destination.find((function(t){return Lo[t]}))]:"string"===typeof(null===n||void 0===n?void 0:n.destination)&&(e=null===Lo||void 0===Lo?void 0:Lo[null===n||void 0===n?void 0:n.destination]),e||(e="bfd");var r=qr({});window.PQ.cmd.push((function(){window.PQ.getTargeting({signals:To,adUnits:[{adUnitPath:"/".concat("6556","/").concat(e,".").concat(r,"/connatix"),sizes:"480x360,640x480,640x360"}]},(function(t,e){t&&console.error(t,e)}))}))}))})).catch((function(t){return t instanceof hi?null:Promise.reject(t)})))},loadSignals:Ro((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"page",e=arguments.length>1?arguments[1]:void 0;return Io.tagReady.then((function(n){try{"page"===t?n.loadSignals(To,e):n.loadSignalsForSlots(t,e)}catch(r){return console.error(r),Promise.reject()}return Promise.resolve()}))})),runOnce:function(t){for(var e=this,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];var o=!1;return function(){o||(o=!0,t.apply(e,[].concat(r)))}}};var Mo;var So={init:function(){return Mo||(Mo=new Promise((function(t,e){var n=document.createElement("script");n.onload=function(){return t(n)},n.onerror=function(){e("Teads: Script failed to load")},n.src="https://a.teads.tv/analytics/tag.js",n.async=!0,document.head.appendChild(n)})).then((t=function(){return t=new Promise((function(t){window.teads_analytics=window.teads_analytics||{},t(window.teads_analytics)})),e=function(){window.teads_analytics.analytics_tag_id="PUB_14692",window.teads_analytics.share=window.teads_analytics.share||function(){(window.teads_analytics.shared_data=window.teads_analytics.shared_data||[]).push(arguments)}},n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t);var t,e,n},function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}),(function(){return console.error("Teads failed to initialize"),Promise.reject(new di("Teads failed to initialize"))})));var t}},zo={de:{ADVERTISE_WITH_BUZZFEED:"Wirb mit BuzzFeed",ADVERTISE_WITH_URL:"https://advertise.buzzfeed.com/bfdeutschland",ADVERTISEMENT:"Anzeige",PAID_POST:"Anzeige",PROMOTED_BY:"Pr\xe4sentiert von",PROMOTED:"Anzeige",SPONSORED_BY:""},en:{ADVERTISE_WITH_BUZZFEED:"Advertise with BuzzFeed",ADVERTISE_WITH_URL:"https://advertise.buzzfeed.com",ADVERTISEMENT:"Advertisement",PAID_POST:"Paid Post",PROMOTED_BY:"Promoted By",PROMOTED:"Promoted",SPONSORED_BY:"Sponsored By"},es:{ADVERTISE_WITH_BUZZFEED:"An\xfanciate en BuzzFeed",ADVERTISE_WITH_URL:"https://advertise.buzzfeed.com/bfespana",ADVERTISEMENT:"Publicidad",PAID_POST:"Patrocinado",PROMOTED_BY:"Patrocinado por",PROMOTED:"Patrocinado",SPONSORED_BY:"Patrocinado por"},fr:{ADVERTISE_WITH_BUZZFEED:"Annoncer sur BuzzFeed",ADVERTISE_WITH_URL:"https://advertise.buzzfeed.com/bffrance",ADVERTISEMENT:"Publicit\xe9",PAID_POST:"Sponsoris\xe9",PROMOTED_BY:"Sponsoris\xe9 par",PROMOTED:"Sponsoris\xe9",SPONSORED_BY:""},ja:{ADVERTISE_WITH_BUZZFEED:"",ADVERTISE_WITH_URL:"",ADVERTISEMENT:"\u5e83\u544a",PAID_POST:"Sponsored",PROMOTED_BY:"Sponsored by",PROMOTED:"Sponsored",SPONSORED_BY:"Sponsored by"},pt:{ADVERTISE_WITH_BUZZFEED:"",ADVERTISE_WITH_URL:"",ADVERTISEMENT:"Publicidade",PAID_POST:"Conte\xfado Pago",PROMOTED_BY:"Em parceria com",PROMOTED:"Patrocinado",SPONSORED_BY:""},test:{FOO:"Localized foo",ADVERTISEMENT:"Test Advertisement",PROMOTED_BY:"Test Promoted By"}},No={_curr:"en",_translation:{},set translation(t){"string"!==typeof t?(this._translation=t,this._curr=""):zo[t]?(this._translation=zo[t],this._curr=t):(this._translation=zo.en||{},this._curr=t)},get translation(){return this._translation}};function Do(t){No.translation=t||"en"}var Bo,Go={getTranslationStr:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return No.translation[t]||e||zo.en[t]||t}};var Fo={init:function(t){var e=t.env;return Bo||(Bo=function(t){var e=t.isBFO||t.isBFN,n=Gr.a.get("bf2-b_info");return e&&n&&!zn.e.needsGDPRConsent()?Promise.resolve():(Dr("info","general","LiveRamp is not enabled",""),Promise.reject(new hi))}(e).then((function(){if(!document.cookie.match(/_lr_env=/)&&document.cookie.match(/hem=/)){Dr("info","general","Sending hashed email to LiveRamp","");var t=atob(Gr.a.get("hem").replace(/"/g,""));window.addEventListener("envelopeModuleReady",(function(){atsenvelopemodule.setAdditionalData({type:"emailHashes",id:[t]})}))}})).catch((function(t){return t instanceof hi?null:Promise.reject(t)})))}};function Uo(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Vo(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Uo(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Uo(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Ho(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}var Zo=Wo((function(){return Ho(aa.get("env","abeagle","gdpr","ccpa"),(function(t){var e=Object(a.a)(t,4),n=e[0],r=e[1],i=e[2],o=e[3];return Ho(Promise.all([Qo,o.getConsentValue(),i.hasConsentedGoogle(),ia.checkIsOn({abeagle:r,gdpr:i}),qo({env:n,abeagle:r,gdpr:i})]),(function(t){var e=Object(a.a)(t,5),i=e[0],o=e[1],c=e[2],s=e[3],u=e[4];return Dr("info","consent","_configureGPT -> dependencies RESOLVED"),Dr("info","consent","ccpaConsent",o),Dr("info","consent","personalizationConsent",c),Dr("info","adCall","permutiveOn",s),Ho(Yo(s),(function(){i.cmd.push(Wo((function(){if(pa({googletag:i,targeting:u}),ba({googletag:i,settings:ga({env:n})}),i.pubads().setPrivacySettings({nonPersonalizedAds:da(c),restrictDataProcessing:ha(o)}),i.pubads().disableInitialLoad(),i.pubads().enableAsyncRendering(),i.pubads().enableSingleRequest(),i.pubads().addEventListener("slotOnload",ta),i.pubads().addEventListener("slotRenderEnded",na),i.pubads().addEventListener("slotRequested",ea),i.pubads().addEventListener("slotVisibilityChanged",$o),i.pubads().addEventListener("impressionViewable",ra),s){var t=i.pubads().getTargeting("permutive");if(0===(null===t||void 0===t?void 0:t.length)){var e=localStorage.getItem("_pdfps");i.pubads().setTargeting("permutive",e?JSON.parse(e):[])}}return i.enableServices(),Ho(Io.isOn({abeagle:r}),(function(t){Dr("info","thirdparty","doubleverify #configureGPT: is on?",t),t&&Io.loadSignals("page")}))})))}))}))}))}));function Wo(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}}var qo=Wo((function(t){var e=t.env,n=t.abeagle,r=t.gdpr;return Ho(ci.getPageTargeting({env:e,abeagle:n,gdpr:r},Xo.getPageTargeting()),(function(t){return t=Vo({},t),Object.keys(t).forEach((function(e){var n=t[e];t[e]=[].concat(n).map((function(t){return"string"===typeof t?t.substring(0,40):t}))})),t}))}));var Yo=Wo((function(t){return Dr("info","adCall","permutiveOn:",t),new Promise((function(e){var n=/\bpermutive_timeout=true\b/.test(window.location.search);if(Dr("info","adCall","permutiveTimeout",n),!t||!n)return Dr("info","adCall","permutiveTimeout or permutiveOn is false"),void e();var r=0,i=setInterval((function(){if(r>=10||window&&window.permutive)return clearInterval(i),void e();r++}),50);Dr("info","adCall","permutive defined ,interval:",i)}))}));var Jo,Qo,Ko,Xo,$o=Wo((function(t){var e=String(t.slot.getTargeting("wid"));return Ho(aa.get("eventBus"),(function(n){Object(a.a)(n,1)[0].trigger("gpt:slotVisibilityChanged:".concat(e),t)}))})),ta=Wo((function(t){var e=String(t.slot.getTargeting("wid"));if(la(e)&&e)return Ho(aa.get("eventBus"),(function(n){Object(a.a)(n,1)[0].trigger("gpt:slotOnload:".concat(e),t)}));window.raven&&window.raven.captureMessage("onSlotLoad error",{tags:{gptEvent:t}})})),ea=Wo((function(t){return Ho(aa.get("env"),(function(e){var n=Object(a.a)(e,1)[0],r=String(t.slot.getTargeting("wid")),i=ca[r].getAdUnitPath(),o=i.split("/"),c=Object(a.a)(o,6),s=c[2],u=c[3],l=c[4],f=c[5];Bi(Pi,{detail:{wid:r,unitPath:i,zone1:s,edition:u,vertical:l,pos:f}}),Fi(Pi,n)}))})),na=Wo((function(t){var e=String(t.slot.getTargeting("wid"));delete sa[e];var n=la(e);if(Dr("info","adCall","slot is",n),n&&e)return Ho(aa.get("eventBus"),(function(n){Object(a.a)(n,1)[0].trigger("gpt:slotRenderEnded:".concat(e),t)}));window.raven&&window.raven.captureMessage("onSlotRenderEnded error",{tags:{gptEvent:t}})})),ra=Wo((function(t){var e=String(t.slot.getTargeting("wid"));if(la(e)&&e)return Ho(aa.get("eventBus","env"),(function(n){var r=Object(a.a)(n,2),i=r[0],o=r[1];i.trigger("gpt:impressionViewable:".concat(e),t);var c=ca[e].getAdUnitPath(),s=c.split("/"),u=Object(a.a)(s,6),l=u[2],f=u[3],d=u[4],h=u[5];Bi(_i,{detail:{wid:e,unitPath:c,zone1:l,edition:f,vertical:d,pos:h}}),Fi(_i,o)}));window.raven&&window.raven.captureMessage("impressionViewable error",{tags:{gptEvent:t}})})),ia={checkIsOn:dr},oa="https://securepubads.g.doubleclick.net/tag/js/gpt.js",aa={_eventBusGetter:null,_envGetter:null,_localizationGetter:null,_abeagleGetter:null,_gdprGetter:null,_ccpaGetter:null,_trackingGetter:null,get:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return Promise.all(e.map((function(t){return aa["_".concat(t,"Getter")]})))}},ca={},sa={firstAdCallMade:!1};function ua(t){return"div-gpt-ad-".concat(t)}function la(t){return document.getElementById(ua(t))}function fa(){var t=document.querySelector('script[src*="'.concat(oa,'"]'));Dr("info","lifecycle","GPT loaded statically?",!!t);var e=[aa.get("env")];return t||e.push(Object(Dn.a)(oa)),Promise.all(e).then((function(t){var e=Object(a.a)(t,1),n=Object(a.a)(e[0],1)[0];window.googletag=window.googletag||{},window.googletag.cmd=window.googletag.cmd||[];var r=window.googletag;return new Promise((function(t){r.cmd.push((function(){Qo.resolve(r),t(r),Bi("gpt_tag_end"),Fi(Ri,n)}))})).then(Zo)}),(function(t){return Dr("error","lifecycle",t),console.error("GPT failed to initialize"),Promise.reject(new di("GPT failed to initialize"))}))}function da(t){var e=!0,n=Object(Bn.d)(window.location.search)["ads-debug"];return Dr("info","consent","non-personalization =",(e=/googleconsent(-on|-off)/.test(n)?!/-on/.test(n):!t)?"YES":"NO (personalized ads)"),e}function ha(t){return!!(t&&t.length>=3)&&"Y"===t.charAt(2)}function pa(t){var e=t.googletag,n=t.targeting,r=void 0===n?{}:n;e.pubads().clearTargeting(),Object.keys(r).forEach((function(t){e.pubads().setTargeting(t,r[t])}))}function ga(t){var e=t.env;return e.isAdPost()||e.cmsTags.includes("commerce-partnership")?{fetchMarginPercent:-1,renderMarginPercent:-1,mobileScaling:-1}:yr.isAny(["md","lg"])?Promise.resolve({fetchMarginPercent:1e3,renderMarginPercent:200}):null}function ba(t){var e=t.googletag,n=t.settings;e.pubads().enableLazyLoad(n)}Jo=null,Qo=new Pr,aa._eventBusGetter=new Pr,aa._envGetter=new Pr,aa._localizationGetter=new Pr,aa._abeagleGetter=new Pr,aa._gdprGetter=new Pr,aa._ccpaGetter=new Pr,aa._trackingGetter=new Pr,ca={},sa={},Xo={getPageTargeting:function(){return Promise.resolve(null)},getSlotTargeting:function(){return Promise.resolve(null)}};var va=function(){var t=window;return"function"===typeof t.describe&&"function"===typeof t.expect};Promise.allSettled=Promise.allSettled||function(t){return Promise.all(t.map((function(t){return t.then((function(t){return{status:"fulfilled",value:t}})).catch((function(t){return{status:"rejected",reason:t}}))})))};var ma={AdError:di,thumbnailReady:vo,buildAdCall:Wr,getAdCallCategory:Yr,inject:function(t){"eventBus"in t&&aa._eventBusGetter.resolve(t.eventBus),"env"in t&&aa._envGetter.resolve(t.env),"localization"in t&&aa._localizationGetter.resolve(t.localization),"abeagle"in t&&aa._abeagleGetter.resolve(t.abeagle),"gdpr"in t&&aa._gdprGetter.resolve(t.gdpr),"ccpa"in t&&aa._ccpaGetter.resolve(t.ccpa),"tracking"in t&&aa._trackingGetter.resolve(t.tracking)},configure:function(t){var e=t.customTargetingPage,n=t.customTargetingSlot;function r(t){return function(){var e=arguments;return Promise.resolve().then((function(){return t.apply(void 0,Object(c.a)(e))})).catch((function(){return{}}))}}"function"===typeof e&&(Xo.getPageTargeting=r(e)),"function"===typeof n&&(Xo.getSlotTargeting=r(n))},init:function(){if(va()&&!window._isAdsServiceTest)throw new Error("Unit tests should not make real ad calls! Make sure mock `ads` methods");if(Jo)return Jo;Bi("adlib_init_start");var t=Zr();function e(t,e,n){return n.init(t).then((function(t){return t}),(function(t){return console.error(t),null}))}return(t+=(t?" + ":"")+ci.getDFPKeyword())&&document.body.insertAdjacentHTML("beforeend",'\n        <div class="ad-test-label fill-yellow-lighter xs-text-5 bold xs-p1 xs-ml1 xs-t0 xs-z4 xs-fixed">\n          '.concat(t,"\n        </div>\n      ")),Jo=aa.get("eventBus","env","localization","abeagle","gdpr","ccpa","tracking").then((function(t){var n=Object(a.a)(t,7),r=n[0],i=n[1],o=n[2],c=n[3],s=n[4],u=n[5],l=n[6];Cr.start({eventBus:r});var f,d={env:i,localization:o,abeagle:c,gdpr:s,ccpa:u,tracking:l,googletagReady:Qo},h=e.bind(null,d);Dr("info","adCall","language set",f=i.localization.language?i.localization.language:"getRawPageLanguage"in o?o.getRawPageLanguage():"en"),Do(f);var p,g=Promise.all([fa(),(p={gdpr:s},p.gdpr.fetchAdPurposeConsent().then((function(t){if(!t)throw new di("Did not consent, not loading ads.")}))),h("prebid",new Ji(d)),h("amazon",new lo(d)),h("ad_lightning",Oo)]);return h("doubleverify",Io),h("teads",So),h("liveRamp",Fo),g.then((function(){r.trigger("ads:initialized"),Bi("adlib_init_end"),Fi(xi,i)}))}))},start:function(){return this.init().catch((function(t){return t instanceof di?Promise.resolve():Promise.reject(t)}))},reset:Wo((function(){return ca={},sa={},Ho(aa.get("env","abeagle","gdpr"),(function(t){var e=Object(a.a)(t,3),n=e[0],r=e[1],i=e[2];return Promise.all([Qo,qo({env:n,abeagle:r,gdpr:i})]).then((function(t){var e=Object(a.a)(t,2),r=e[0],i=e[1];r.destroySlots(),pa({googletag:r,targeting:i}),ba({googletag:r,settings:ga({env:n})})}))}))})),clearTargeting:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(ca[t]){var n=ca[t];e.forEach((function(t){return n.clearTargeting(t)}))}},renderWidget:function(t){var e=this,n=t.wid;return sa[n]?sa[n]:(this.createAdSlotContainer(n),sa[n]=aa.get("env","gdpr","ccpa","abeagle","localization").then((function(n){var r=Object(a.a)(n,5),i=r[0],o=r[1],c=r[2],s=r[3],u=r[4],l={env:i,gdpr:o,ccpa:c,abeagle:s,googletagReady:Qo},f=Wr(t,{env:i,localization:u}),d=[new Qi(l),new fo(l)];return Promise.all([e.init(),e.defineSlot(t)]).then((function(n){var r=Object(a.a)(n,2)[1];return e.requestHeaderBid(d,Vo(Vo({},t),{},{path:f,slot:r}))}))})).then((function(){return e.display(t)})))},refreshWidget:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.wid;if(sa[r])return sa[r];if(!ca[r]||!ca[r].getSlotElementId())return this.renderWidget(t,!1,n);Dr("info","lifecycle","renderWidget ".concat(r," refresh"),{slotStatus:sa[r],slot:ca[r]});var i=ca[r].getTargetingKeys().filter((function(t){return/^hb_|amzn/.test(t)}));return ma.clearTargeting(r,i),Object.keys(n).forEach((function(t){ca[r].setTargeting(t,n[t])})),sa[r]=aa.get("env","gdpr","ccpa","abeagle","localization").then((function(n){var i=Object(a.a)(n,5),o=i[0],c=i[1],s=i[2],u=i[3],l=i[4],f={env:o,gdpr:c,ccpa:s,abeagle:u,googletagReady:Qo},d=Wr(t,{env:o,localization:l}),h=[new Qi(f),new fo(f)];return Promise.all([e.requestHeaderBid(h,Vo(Vo({},t),{},{path:d,slot:ca[r]})),Qo])})).then((function(t){Object(a.a)(t,2)[1].pubads().refresh([ca[r]])}))},defineSlot:function(t){var e=t.wid,n=t.size,r=void 0===n?"fluid":n,i=t.targeting,o=void 0===i?{}:i,c=ca[e];return c?Promise.resolve(c):Promise.all([aa.get("env","localization"),Qo,Xo.getSlotTargeting(t),new Promise((function(t){"cookieDeprecationLabel"in navigator?navigator.cookieDeprecationLabel.getValue().then((function(e){t(void 0!==e)})):t(!1)}))]).then((function(n){var i=Object(a.a)(n,4),c=Object(a.a)(i[0],2),s=c[0],u=c[1],l=i[1],f=i[2],d=i[3];Object.assign(o,f);var h=l.defineSlot(Wr(Vo(Vo({},t),{},{useNewAdCallStructure:false}),{env:s,localization:u}),r,ua(e));Object.keys(o).forEach((function(t){h.setTargeting(t,[].concat(o[t]))}));var p=ma.getAdCallCategory(t,{env:s});return p&&h.setTargeting("zone3",p),h.setTargeting("addressable",Gr.a.get("hem")?"true":"false"),h.setTargeting("cookieDeprecation",d?"true":"false"),h.addService(l.pubads()),ca[e]=h,h}))},display:Wo((function(t){var e=t.wid,n=t.isGAMNativeVideo,r=void 0!==n&&n,i=[ca[e]],o=!1;return Ho(aa.get("abeagle"),(function(t){var n,c,s=Object(a.a)(t,1)[0];return n=function(t,e){try{var n=t()}catch(r){return e(r)}return n&&n.then?n.then(void 0,e):n}((function(){return Ho(Io.isOn({abeagle:s}),(function(t){Dr("info","thirdparty","doubleverify #display: is on?",o=t||!1)}))}),(function(t){Dr("error","thirdparty","doubleverify #display error",t)})),c=function(){return r&&Dr("info","thirdparty","doubleverify #display: isGAMNativeVid",r),Qo.then((function(t){!function(t,e){if(!o||r)t.call(null,e);else{var n=Io.runOnce(t,e);Io.loadSignals(e,n),setTimeout(n,750)}}((function(e){return t.pubads().refresh(e,{changeCorrelator:!1})}),i),t.display(ua(e))}))},n&&n.then?n.then(c):c(n)}))})),destroySlot:function(t){var e=t.wid,n=ca[e];return n?(delete ca[e],delete sa[e],Qo.then((function(t){t.destroySlots([n])}))):Promise.resolve()},createAdSlotContainer:function(t){var e=ua(t);document.querySelectorAll(".js-ad-".concat(t)).forEach((function(n){if(n&&!n.querySelector("#".concat(e))){var r='\n        <div id="'.concat(e,'" class="js-ad-slot js-ad-slot-').concat(t,' ad-slot ad-slot-invisible"></div>\n      '),i=n.querySelector("script:first-child");i?i.insertAdjacentHTML("afterend",r):n.insertAdjacentHTML("afterbegin",r)}}))},isEmpty:function(t){var e=t.size,n=t.isEmpty,r=t.campaignId,i=t.creativeTemplateId;return n||null===e||void 0===e||si.indexOf(String(r))>-1||ui.indexOf(String(i))>-1},isBackfillSlot:function(t){if(ma.isEmpty(t))return!1;var e=t.advertiserId;return!(e&&!t.isBackfill)||li.indexOf(String(e))>-1},isProgrammaticSlot:Wo((function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(ma.isEmpty(t))return!1;var n=t.size,r=Object(a.a)(n,2),i=r[0],o=r[1];return Ho(aa.get("env"),(function(t){var r=Object(a.a)(t,1)[0];return(!Xi.isEqual(n,r.adSizes.RESEARCH_PIXEL)||!Xi.contains(e,n))&&(1===i&&1===o||Xi.isProgrammatic(r.programmaticSizes,n,{strict:!1}))}))})),isIframeContent:Wo((function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(ma.isEmpty(t))return!1;var n=t.size,r=Object(a.a)(n,2),i=r[0],o=r[1];return 0===i&&0===o||ma.isProgrammaticSlot(t,e)})),getRenderedAdSize:function(t,e){if(ma.isEmpty(t))return Dr("warn","adCall","ad is empty"),{};Dr("info","adCall","gptEv:",t),Dr("info","adCall","options",e);var n=t.size||[1,1],r=Object(a.a)(n,2),i=r[0],o=r[1];if(1===i&&i===o){var c=e.wid,s=document.getElementById(ua(c)).querySelector("iframe");i=s.offsetWidth,o=s.offsetHeight}return{width:i,height:o}},detectAdBlock:function(){return Ko?(Dr("info","adCall","ad block enabled. Please disable to see the ads"),Ko):Ko=new Promise(Wo((function(t){return Object(Dn.a)("//www.buzzfeed.com/static/js/ad-detection/ads.js").then((function(){return t(!1)})).catch((function(){return t(!0)})),Ho()})))},notifyBFA:Wo((function(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return Ho(aa.get("tracking"),(function(r){var i=Object(a.a)(r,1)[0];wo({trackFn:i&&i.track?i.track:window.bfa,eventName:t,eventData:e,hasEventUrl:n})}))})),requestHeaderBid:function(t,e){var n=t.filter((function(t){return t})).map((function(t){return t.requestBid(ma,e)}));return Promise.allSettled(n).then((function(t){return t.filter((function(t){var e=t.status,n=t.value;return"fulfilled"===e&&Boolean(n)})).map((function(t){return t.value})).map((function(t){return t.setTargeting()}))})).then((function(t){return Promise.all(t)}))},getSlotContainer:function(t){return la(t)},getSlotContainerId:function(t){return ua(t)},isGPTPrefetch:Wo((function(){return Ho(aa.get("env","abeagle"),(function(t){var e=Object(a.a)(t,2),n=e[0],r=e[1];return Ho(Promise.all([r.isOn("ADS-1791-new-bpage-gpt-lazyload"),ga({env:n})]),(function(t){var e=Object(a.a)(t,2),n=e[0],r=e[1];return Dr("info","adCall","isGPTLazyLoad",n),Dr("info","adCall","lazyLoadSettings",r),n&&null!==r}))}))}))};function ya(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}Wo((function(){if(!va())return Ho(aa.get("abeagle"),(function(t){var e=Object(a.a)(t,1)[0];ma.detectAdBlock().then((function(t){t&&(Eo({abeagle:e}),document.body.classList.add("has-lego"))})),_o()}))}))();var Oa=new Pr;function wa(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}}zn.e.needsGDPRConsent()?zn.c.getTCData().then((function(t){t.tcData.gdprApplies||(Dr("info","consent","consentIsKnown resolved, there is a gdpr cookie but gdpr does not apply"),Oa.resolve())})):(Dr("info","consent","consentIsKnown resolved, no need for consent"),Oa.resolve()),zn.c.setTCFListener((function(t){var e=t.tcData;Dr("info","consent","tcf event:",e.eventStatus),"tcloaded"!==e.eventStatus&&"useractioncomplete"!==e.eventStatus||(Oa.resolve(),Dr("info","consent","consentIsKnown resolved, user action complete")),zn.f.hasConsented("ads").then((function(t){"useractioncomplete"===e.eventStatus&&e.gdprApplies&&!t&&Gr.a.remove("AMZN-Token","")}))}));var ja={fetchAdPurposeConsent:wa((function(){return zn.e.needsGDPRConsent()?ya(Oa,(function(){return zn.f.hasConsented("ads")})):Promise.resolve(!0)})),hasConsented:function(){return ja.fetchAdPurposeConsent()},hasConsentedGoogle:wa((function(){return zn.e.needsGDPRConsent()?ya(Oa,(function(){return zn.f.hasConsented("google")})):Promise.resolve(!0)})),hasOptedOutCCPA:function(){return zn.e.needsCCPAConsent()?zn.f.fetchCCPAOptOut():Promise.resolve(!1)},needsConsent:zn.e.needsConsent()},Aa={};Object.defineProperties(Aa,Object.getOwnPropertyDescriptors(ja));var Ea=u.a.createContext();function _a(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Pa(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?_a(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):_a(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var Ra={eventBus:new Nn,env:{},localization:{edition:"en-us",language:"en",country:"us"},abeagle:{getExperimentVariant:function(){return Promise.resolve("control")},isOn:function(){return Promise.resolve(!1)}},tracking:{track:function(){},trackPrebidEvents:function(){}},gdpr:Pa({},Aa),ccpa:{getConsentValue:function(){return zn.d.framework.needsConsent()?zn.d.rules.fetchCCPAValue():Promise.resolve(null)}}};function xa(t){var e=t.pageId,n=t.getPageContext,r=t.pageLanguage,i=void 0===r?"en":r,o=t.adsEnabled,a=void 0===o||o,c=t.getPageTargeting,l=t.getSlotTargeting,f=t.children,d=Object(s.useState)({status:"loading"}),h=d[0],p=d[1],g=Object(s.useRef)(!1),b=Object(s.useRef)();return b.current&&e===b.current||(a&&"loading"!==h.status?p({status:"loading"}):a||"disabled"===h.status||p({status:"disabled"}),Do(i)),Object(s.useEffect)((function(){e!==b.current&&(b.current=e,a&&Promise.resolve(n()).then((function(t){Object.keys(Ra).forEach((function(e){e in t&&"eventBus"!==e&&Object.assign(Ra[e],t[e])})),ma.configure({customTargetingPage:c,customTargetingSlot:l}),g.current?ma.reset():(ma.inject(Ra),ma.start().then((function(){g.current=!0}))),p(Pa(Pa(Pa({},Ra),t),{},{pageId:e,status:"loaded"}))})))}),[e,a,n,c,l]),u.a.createElement(Ea.Provider,{value:h},f)}var Ta=n("ds4U"),ka=n("SE2S");function Ca(t){var e=Object(s.useContext)(ka.a).experiments,n=Object(s.useRef)(),r=Object(s.useRef)({});if(!n.current||t!==n.current){var i=function(){var t;return[new Promise((function(e){return t=e})),t]}(),o=Object(a.a)(i,2);r.current.loaded=o[0],r.current.resolve=o[1]}n.current=t;var u=Object(s.useRef)({experiments:r.current.loaded,isOn:function(){var t=arguments;return r.current.loaded.then((function(e){return Ta.b.apply(void 0,[e].concat(Object(c.a)(t)))}))},getExperimentVariant:function(){var t=arguments;return r.current.loaded.then((function(e){return Ta.a.apply(void 0,[e].concat(Object(c.a)(t)))}))}});return Object(s.useEffect)((function(){e&&e.loaded&&!e.stale&&r.current.resolve(e)}),[e]),u.current}var La=u.a.createElement;function Ia(){Ia=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),c=new T(r||[]);return i(a,"_invoke",{value:_(t,n,c)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="executing",p="completed",g={};function b(){}function v(){}function m(){}var y={};u(y,a,(function(){return this}));var O=Object.getPrototypeOf,w=O&&O(O(k([])));w&&w!==n&&r.call(w,a)&&(y=w);var j=m.prototype=b.prototype=Object.create(y);function A(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function n(i,o,a,c){var s=f(t[i],t,o);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(l).then((function(t){u.value=t,a(u)}),(function(t){return n("throw",t,a,c)}))}c(s.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function _(e,n,r){var i=d;return function(o,a){if(i===h)throw Error("Generator is already running");if(i===p){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var c=r.delegate;if(c){var s=P(c,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===d)throw i=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=h;var u=f(e,n,r);if("normal"===u.type){if(i=r.done?p:"suspendedYield",u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=p,r.method="throw",r.arg=u.arg)}}}function P(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var o=f(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,g;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function R(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(R,this),this.reset(!0)}function k(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(typeof e+" is not iterable")}return v.prototype=m,i(j,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:v,configurable:!0}),v.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},A(E.prototype),u(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new E(l(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},A(j),u(j,s,"Generator"),u(j,a,(function(){return this})),u(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=k,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(x),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return c.type="throw",c.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),x(n),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;x(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:k(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}function Ma(){return(Ma=Object(i.a)(Ia().mark((function t(e){var n,r,i,o,a;return Ia().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.abeagle,r=e.props,i={pagetype:"A"},r.isAdvertizer&&(i.user=r.user.username),o=[],t.next=6,Promise.all(o.map((function(t){return n.getExperimentVariant(t,{rejectErrors:!1,defaultVariantIfUnbucketed:null})})));case 6:return a=t.sent,i.abtest=o.map((function(t,e){return"".concat(t,"|").concat(a[e])})),t.abrupt("return",i);case 9:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function Sa(t){var e=Ca(t.user.id),n=Object(s.useCallback)((function(){return function(t){return Sn.apply(this,arguments)}({isAdvertizer:t.isAdvertizer,abeagle:e})}),[t.isAdvertizer,e]),r=Object(s.useCallback)((function(){return function(t){return Ma.apply(this,arguments)}({abeagle:e,props:t})}),[e,t]);return La(xa,{pageId:t.user.id,pageLanguage:t.pageLang,adsEnabled:!0,getPageContext:n,getPageTargeting:r},t.children)}var za=n("7nmT"),Na=n.n(za),Da=n("LaIT"),Ba=n.n(Da),Ga=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Ba.a.compile(t,n).render(e)},Fa=function(){try{return window.BZFD.Context.page.localization}catch(t){return{}}},Ua=function(){return Fa().translations||{}},Va={de:["de"],en:["au","ca","in","uk","us"],es:["es","mx"],fr:["fr"],ja:["jp"],pt:["br"]};function Ha(t){return Ua()[t]||""}var Za={getEdition:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={useCountryCookie:!0};Object(Hn.isString)(t)&&(t="feedpager"===t?{useCountryCookie:!1}:{}),t=Object.assign({},e,t);var n=Fa().country,r=n.split("-"),i=Object(a.a)(r,2),o=i[0],c=i[1],s=void 0===c?"":c;if("en"===Fa().language&&t.useCountryCookie){var u=cr.get("country");u||(u=s||"us");var l=u.split("-"),f=Object(a.a)(l,2);o=f[0],(s=f[1])||"es"===o||(s=o,o=Object.keys(Va).filter((function(t){return-1!==Va[t].indexOf(o)}))[0])}return Va[o]&&-1!==Va[o].indexOf(s)?"".concat(o,"-").concat(s):"es"===o?"es":"en-us"},getRawPageLanguage:function(){return-1!==Object.keys(Va).indexOf(Fa().language)?Fa().language:"en"},getPageLocale:function(){return Fa().locale},getUserCountry:function(){return cr.get("country")||"us"},getUserGeoCountry:function(){return cr.get("bf-geo-country")||"US"},getTranslation:function(t,e,n){if(!Ua()[t])throw new Error("Missing translation "+t+" for "+this.getRawPageLanguage());var r=n?{delimiters:n}:{};return Ga(Ua()[t],e,r)},getTranslationStr:Ha,getDateFormatTemplate:function(){switch(Fa().language){case"de":return"{{D}}. {{MM}} {{Y}}, {{H}}:{{m}} Uhr";case"es":return"{{D}} de {{MM}} de {{Y}}, {{h}}:{{m}} {{a}}";case"fr":return"{{D}} {{MM}}, {{Y}} &agrave; {{H}} h {{m}}";case"ja":return"{{Y}}/{{M}}/{{DD}} {{H}}:{{m}}";case"pt":return"{{D}} de {{MM}} de {{Y}}, {{h}}:{{m}} {{a}}";default:return"{{MM}} {{D}}, {{Y}}, at {{h}}:{{m}} {{a}}"}}};function Wa(t){return t.env?(t.env.addFilter("tojson",t.env.filters.dump),t.env.addFilter("prependClickTracker",(function(t,e){return function(t,e){return!t||t.length<0?e:t+encodeURIComponent(e)}(e,t)})),t.env.addFilter("l10n",(function(t,e){return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";try{var n=Ha(t);return n.length<=0?e:n}catch(r){console.error("error fetching translation: "+r)}return e}(e,t)})),t.env.addFilter("regex_match",(function(t,e){return t.match(new RegExp(e))})),t):t}function qa(t){return Wa(t),function(t){t.env.addGlobal("i18n",window.BZFD.Context.page.localization.translations),t.env.addGlobal("utils",{transGif1x1Base64:"data:image/gif;base64,R0lGODlhAQABAPAAAAAAAAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==",transGif3x2Base64:"data:image/gif;base64,R0lGODlhAwACAPAAAAAAAAAAACH5BAEAAAAALAAAAAADAAIAAAIChF8AOw=="}),t.env.addGlobal("config",window.BZFD.Config)}(t),t}function Ya(t){var e=t.template,n=t.props,r=t.target,i=t.anchor;if("function"===typeof e&&"$set"in e.prototype)return new e({props:n,target:r,anchor:i});if("env"in e){var o=qa(e).render(n);if(i){if(!(i instanceof Node))throw new TypeError("`anchor` is not of type `Node`");if(i.parentElement!==r)throw new Error("`anchor` is not a child of `target`");i.insertAdjacentHTML("beforebegin",o)}else r.insertAdjacentHTML("beforeend",o);return null}return null}var Ja={CREATED:"CREATED",INITIALIZING:"INITIALIZING",INITIALIZED:"INITIALIZED",DESTROYED:"DESTROYED"};Object.freeze(Ja);var Qa=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=t;return function(){return e++}}();function Ka(t){var e=this;this.context=t,this.eventBus=t.eventBus,this.element=t.element,this.config=t.config,this.id=Qa(),this.privateEvents=new Nn,this.plugins=new Set,this.lifecycleState=Ja.CREATED;var n=this.destroy;this.destroy=function(){e.lifecycleState!==Ja.DESTROYED&&n.call(e)}}function Xa(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e&&t.setAttribute("aria-hidden",!0),(t.tabIndex>=0||t.shadowRoot)&&(t.tabIndex=-1),Array.prototype.forEach.call(t.children,Xa)}function $a(t){Xa(t,!0)}Ka.prototype.init=function(){var t=this;return this.initializing?this.initializing:(this.lifecycleState=Ja.INITIALIZING,this.initializing=Promise.resolve().then((function(){return t.isEnabled()})).then((function(t){if(!t||Array.isArray(t)&&t.some((function(t){return!t})))throw new hi})).then((function(){if(t.lifecycleState===Ja.DESTROYED)throw new pi})).then((function(){return t.setup()})).then((function(){return t.lifecycleState=Ja.INITIALIZED})).catch((function(e){return t.destroy(),Promise.reject(e)})))},Ka.prototype.isEnabled=function(){return!0},Ka.prototype.setup=function(){return this.initPlugins()},Ka.prototype.onPublicEvent=function(){var t;if(!this.eventBus)return function(){};var e=(t=this.eventBus).on.apply(t,arguments);return this.addDestroyAction(e),e},Ka.prototype.onDOMEvent=function(t,e,n){t.addEventListener(e,n);var r=function(){return t.removeEventListener(e,n)};return this.addDestroyAction(r),r},Ka.prototype.initPlugins=function(){var t=this,e=[];return this.plugins.forEach((function(n){e.push(new Promise((function(e,r){var i=new n(t.context);t.addDestroyAction((function(){return i.destroy()})),i.init().then(e,r)})).catch((function(t){return t})))})),Promise.all(e)},Ka.prototype.addDestroyAction=function(t){this.privateEvents.on("destroying",t)},Ka.prototype.destroy=function(){this.lifecycleState!==Ja.DESTROYED&&(this.privateEvents.trigger("destroying"),this.privateEvents.destroy(),delete this.privateEvents,delete this.eventBus,this.lifecycleState=Ja.DESTROYED)},Ka.withMixins=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.reduce((function(t,e){return e(t)}),this)};var tc=function(){if("undefined"===typeof document||!document.body)return 0;var t=document.createElement("div");t.style.overflowY="scroll",t.style.width="100px",t.style.height="100px",t.style.zIndex="-1",t.style.visibility="hidden",document.body.appendChild(t);var e=t.offsetWidth-t.clientWidth;return document.body.removeChild(t),e}();function ec(t){t&&(0===tc?t.classList.add("scrollbar--overlay"):t.classList.add("scrollbar--".concat(Math.ceil(tc),"px")))}ec("undefined"!==typeof document?document.documentElement:null);var nc=function(t){var e=t.shareUrl,n=void 0===e?"":e,r=t.platform,i=t.utmCampaign,o=t.utmSource;if(!r)return n;var s=null!==i&&void 0!==i?i:"bfshare".concat(r),u=null!==o&&void 0!==o?o:"dynamic",l=n.split("?"),f=Object(a.a)(l,2),d=f[0],h=f[1],p=void 0===h?"":h;return d+"?"+["utm_source=".concat(u),"utm_campaign=".concat(s)].concat(Object(c.a)(p.split("&"))).filter((function(t){return!!t})).join("&")},rc="undefined"!==typeof document?document.documentElement:null,ic="undefined"!==typeof document&&!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect;!function(){try{localStorage.setItem("ls_test",!0),localStorage.removeItem("ls_test")}catch(t){return!1}}();rc&&!ic&&rc.classList.add("no-svg"),"undefined"!==typeof document&&(navigator.userAgent.match(/Pinterest/i)||document.referrer.match(/pinterest.com/i))&&rc.classList.add("pinterest");(function(){var t="ontouchstart"in window;t&&rc.classList.add("has-touch")})(),"undefined"===typeof document||(navigator.userAgent.match(/android/i)?rc.classList.add("is-android"):navigator.userAgent.match(/iphone|ipad|ipod/i)&&rc.classList.add("is-ios")),function(){var t=document.referrer,e=window.location.search,n=navigator.userAgent.match(/fban|twitter|pinterest/i),r=n?n[0]:"";e.match("referrer=pinterest")||t.match("pinterest")||"pinterest"===r||(e.match("referrer=twitter")||t.match("t.co")||"twitter"===r||(e.match("referrer=facebook")||t.match("m.facebook")))}();var oc=function(){return window.innerHeight||rc.clientHeight},ac=function(t,e,n){for(var r=n?t:t.parentNode;r&&r!==document;){if(r.matches(e))return r;r=r.parentNode}return null};function cc(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.properties,r=void 0===n?["any"]:n,i=["transitionend","webkitTransitionEnd","mozTransitionEnd","oTransitionEnd"],o={},a=[];function c(t){"any"in o?o.any(t):t.propertyName in o&&o[t.propertyName](t)}r.forEach((function(t){var e,n=new Promise((function(t){return e=t}));a.push(n),o[t]=e})),i.forEach((function(e){return t.addEventListener(e,c)}));var s=Promise.all(a);return s.unsubscribe=function(){i.forEach((function(e){return t.removeEventListener(e,c)}))},s.then(s.unsubscribe),s}"undefined"!==typeof document&&(function(){if(!(document.createElementNS("http://www.w3.org/2000/svg","g").classList instanceof DOMTokenList))try{Object.defineProperty(SVGElement.prototype,"classList",Object.getOwnPropertyDescriptor(HTMLElement.prototype,"classList")||Object.getOwnPropertyDescriptor(Element.prototype,"classList"))}catch(t){}}(),function(){try{var t=document.createElement("div");if(t.classList.add("foo","bar"),t.classList.contains("bar"))return;var e=DOMTokenList.prototype.add,n=DOMTokenList.prototype.remove;DOMTokenList.prototype.add=function(){for(var t=this,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];r.forEach((function(n){return e.call(t,n)}))},DOMTokenList.prototype.remove=function(){for(var t=this,e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];r.forEach((function(e){return n.call(t,e)}))}}catch(r){}}()),"undefined"!==typeof NodeList&&(NodeList.prototype.forEach||(NodeList.prototype.forEach=Array.prototype.forEach));var sc={stringToHTML:function(t){var e=document.createElement("div");return e.innerHTML=t,e.childNodes},arrayFromNodeList:function(t){return[].slice.call(t)},withNodeList:function(t,e){return"string"===typeof t&&(t=this.stringToHTML(t)),t instanceof HTMLElement&&(t=[t]),(t instanceof NodeList||Array.isArray(t))&&Array.prototype.slice.call(t).forEach((function(t){return e(t)})),this},append:function(t,e){"undefined"===typeof e&&(e=t,t=document.body);return this.withNodeList(e,(function(e){return t.appendChild(e)}))},prepend:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;"undefined"===typeof e&&(e=t,t=document.body);var r=t.children[n],i=function(e){return t.insertBefore(e,r)};return this.withNodeList(e,i)},remove:function(){for(var t=[],e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return n.forEach((function(e){e instanceof Element&&e.parentNode&&t.push(e.parentNode.removeChild(e))})),t},find:function(t,e){return Array.prototype.slice.call(t.querySelectorAll(e))},findOne:function(t,e){return t.querySelector(e)},closest:function(t,e){return ac(t,e,!0)},addClass:function(t,e){Array.isArray(t)||(t=[t]);var n=e.split(" ");t.forEach((function(t){return n.forEach((function(e){return t.classList.add(e)}))}))},removeClass:function(t,e){Array.isArray(t)||(t=[t]);var n=e.split(" ");t.forEach((function(t){return n.forEach((function(e){return t.classList.remove(e)}))}))},hasClass:function(t,e){return Array.isArray(t)||(t=[t]),t.some((function(t){return t.classList.contains(e)}))},toggleClass:function(t,e){Array.isArray(t)||(t=[t]),"string"===typeof t&&(t=this.find(t)),e.split(" ").forEach((function(e){return t.forEach((function(t){return t.classList.toggle(e)}))}))},matches:function(){if("undefined"===typeof Element)return function(){return!1};var t=Element.prototype,e=t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.msMatchesSelector||function(t){return[].indexOf.call(document.querySelectorAll(t),this)>-1};return function(t,n){return e.call(t,n)}}(),getViewPosition:function(t,e,n){var r=t.getBoundingClientRect(),i=oc();n=n||0;var o=0-(e=e||0),a=i+e,c={top:r.top-n,bottom:r.bottom-n};return{isHidden:function(){return!t.offsetParent},isWithinViewport:function(){return c.top>=0&&c.bottom<=i},isWithinLookahead:function(){return c.top>=o&&c.bottom<=a},isPartiallyInViewport:function(){return c.top>=0&&c.top<=i||c.bottom>0&&c.bottom<i},isPartiallyInLookahead:function(){return c.top>0?c.top<e+i:c.bottom>0||Math.abs(c.bottom)<e},isNearBottom:function(){return parseInt(r.bottom,10)<=e}}},on:function(t,e,n,r){"function"===typeof n&&"undefined"===typeof r&&(r=n,n=null),e.split(" ").forEach((function(e){return t.addEventListener(e,r)}))},one:function(t,e,n,r){var i=this;"function"===typeof n&&(r=n,n=null);var o=function n(o){r.call(this,o),i.off(t,e,n)};return i.on(t,e,o),o},off:function(t,e,n){e.split(" ").forEach((function(e){return t.removeEventListener(e,n)}))},trigger:function(t,e){document.createEvent&&e.split(" ").forEach((function(e){var n=document.createEvent("Event");n.initEvent&&(n.initEvent(e,!1,!0),t.dispatchEvent(n))}))},hide:function(t,e){var n=e?this.find(t,e):t;return this.addClass(n,"js-hidden"),this},show:function(t,e){var n=this,r=e?this.find(t,e):t;return this.hasClass(r,"js-hidden")?this.removeClass(r,"js-hidden"):(Array.isArray(r)||(r=[r]),r.forEach((function(t){return n.setStyle(t,{display:"block"})}))),this},getData:function(t,e){return"string"===typeof t&&(t=this.stringToHTML(t)[0]),t.getAttribute("data-".concat(e))},setData:function(t,e,n){return"string"===typeof t&&(t=this.stringToHTML(t)[0]),t.setAttribute("data-".concat(e),n),!0},getStyle:function(t,e){return t.style[e]},setStyle:function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t.style[n]=e[n]);return this},getAttr:function(t,e){return t.getAttribute(e)},setAttr:function(t,e){for(var n in e)e.hasOwnProperty(n)&&t.setAttribute(n,e[n]);return this},removeAttr:function(t,e){return t.removeAttribute(e),this},hasAttr:function(t,e){return t.hasAttribute(e)},getText:function(t,e){var n=e?this.findOne(t,e):t;return n?n.textContent:""},setText:function(t,e,n){var r=n?this.findOne(t,n):t;return r&&(r.textContent=e),this},setHTML:function(t,e,n){var r=n?this.findOne(t,n):t;return r&&(r.innerHTML=e),this},parent:function(t,e){return ac(t,e)},getModuleConfig:function(t,e){var n=t.querySelector("script");return e?n:n?JSON.parse(n.text):null},createElement:function(t,e){var n=document.createElement(t);return this.setAttr(n,e),n},scrollIntoView:function(t,e){var n=this;e?setTimeout((function(){n.scrollIntoView(t)}),e):t.scrollIntoView?t.scrollIntoView():window.scrollTo(0,t.offsetTop)},offset:function(t,e){var n=e?this.findOne(t,e):t;return n?{top:n.offsetTop,left:n.offsetLeft}:{}},size:function(t){return{width:t.offsetWidth,height:t.offsetHeight}},detectTransitionEnd:cc},uc=function(){var t=xr.getQueryParams().sub;return t?parseInt(t.split("_")[1],10):null}();var lc={getUrl:function(){var t;"canonical_path"in window.BZFD.Context.page?t=window.BZFD.Context.page.canonical_path:window.BZFD.Context.buzz&&(t=window.BZFD.Context.buzz.canonical_path),t=t||"";var e=location.origin+t,n=function(t){if(""===t)return{};for(var e={},n=0;n<t.length;++n){var r=t[n].split("=",2);1===r.length?e[r[0]]="":e[r[0]]=decodeURIComponent(r[1].replace(/\+/g," "))}return e}(window.location.search.substr(1).split("&"));if(n.bfsource){var r="?";e.includes("?")&&(r="&"),e="".concat(e).concat(r,"bfsource=").concat(n.bfsource)}return e},getCanonicalUrl:function(){var t=document.querySelector('link[rel="canonical"]');return t?t.href:this.getUrl()},getAuthor:function(){return function(t){var e,n="",r=sc.find(document.head,'meta[property="'.concat(t,'"]'));return r.length>0&&(e=sc.getAttr(r[0],"content"))&&(n=e),n}("author").replace(/,$/g,"")},getTitle:function(t){var e=window.BZFD.Context.page.title||document.title;return t&&window.BZFD.Context.page.promotions[t]&&window.BZFD.Context.page.promotions[t].title||e},getDescription:function(t){var e=window.BZFD.Context.page.description;return t&&window.BZFD.Context.page.promotions[t]&&window.BZFD.Context.page.promotions[t].description||e},getCaption:function(){var t=document.domain,e=this.getAuthor();return e&&(t+=" | By ".concat(e)),t},getPicture:function(t){var e=window.BZFD.Context.page.picture;return t&&window.BZFD.Context.page.promotions[t]&&window.BZFD.Context.page.promotions[t].picture||e},getAll:function(){return{url:this.getUrl(),author:this.getAuthor(),title:this.getTitle(),description:this.getDescription(),caption:this.getCaption(),picture:this.getPicture()}},renderString:function(t,e,n){var r=xr.extend({},this.getAll(),e||{});return Ga(t,r,n)},isLinkedSubbuzz:function(t){return null!==uc&&uc===t},getSubbuzzShareData:function(t,e){var n=xr.removeHash(this.getUrl()),r=xr.addQueryParam(n,"sub","0_"+e)+"#"+e,i=sc.getText(t,".js-subbuzz__title-text").trim();return{text:i||this.getTitle().trim(),url:n+"#"+e,shareMetaUpdatedUrl:r,fbUrl:r,media:this.getSubbuzzMedia(t)||this.getPicture(),subbuzzTitle:i}},getQuizResultShareData:function(t,e){var n=xr.removeHash(this.getUrl()),r=xr.addQueryParam(n,"quiz_result",t+"_"+e)+"#"+t;return{url:n+"#"+t,shareMetaUpdatedUrl:r,fbUrl:r}},getSubbuzzMedia:function(t){var e=sc.findOne(t,".js-subbuzz__media");if(!e)return"";var n=sc.getAttr(e,"data-src"),r=sc.getAttr(e,"data-gif-src");return yr.isAny(["xs","sm"])&&(n=sc.getAttr(e,"data-mobile-src")||n,r=sc.getAttr(e,"data-mobile-gif-src")||r),r||n||sc.getAttr(e,"src")}},fc={},dc=function(t){var e=document.createElement("iframe");e.style.cssText="position:absolute; width:1px; height:1px; opacity:0;",e.setAttribute("src",t),document.body.appendChild(e)};fc.iosOpenUrl=dc;var hc=function(t){return encodeURIComponent(JSON.stringify(t))};fc.encodePixiedustData=hc;var pc=function(t){var e="";return Object.keys(t).forEach((function(n){e&&(e+="&"),e+=n+"="+encodeURIComponent(t[n])})),e};fc.encodeData=pc;fc.share=function(t,e){var n=e;if("pinterest"===t&&(n=function(t){return{media:t.media,url:nc({shareUrl:t.shareMetaUpdatedUrl||t.url,platform:"pinterest"}),description:t.text}}(e)),"facebook"===t&&(n=function(t){return{name:t.text,caption:lc.getCaption(),description:lc.getDescription("facebook"),link:nc({shareUrl:t.shareMetaUpdatedUrl||t.url,platform:"facebook"}),picture:t.picture}}(e)),"twitter"===t&&(n=function(t){return{text:t.text,url:nc({shareUrl:t.shareMetaUpdatedUrl||t.url,platform:"twitter"})}}(e)),"email"===t&&(n=function(t){return{subject:t.text,body:nc({shareUrl:t.shareMetaUpdatedUrl||t.url,platform:"email"})}}(e)),"copy"===t&&(n=function(t){return{url:nc({shareUrl:t.shareMetaUpdatedUrl||t.url,platform:"copy"})}}(e)),n=function(t,e){return t.quizId&&t.itemId&&(e.ga_label=t.quizId+"="+t.itemId,e.result_id=t.itemId),e}(e,n),ur.isIOS()){var r="bf://share/"+t+"?"+pc(n);dc(r)}else ur.isAndroid()?window.bf&&window.bf.share&&window.bf.share(t,JSON.stringify(n)):console.log("share() error: cannot determine device os, bailing.")};fc.analytics=function(t,e){var n;if(ur.isIOS())"pixiedust"!==t||ur.isNewsApp()?"google"===t?n="bf://analytics?"+pc(e):console.log('analytics() ios "platform" is neither "pixiedust" nor "google", is this OK?'):n="bf://pixiedust?event="+hc(e),dc(n);else if(ur.isAndroid())if("pixiedust"===t)window.bf&&window.bf.pixiedust&&window.bf.pixiedust(JSON.stringify(e));else if("google"===t){window.bf&&window.bf.analytics&&window.bf.analytics("",JSON.stringify(e))}else console.log('analytics() android "platform" is neither "pixiedust" nor "google", is this OK?');else console.log("analytics() error: cannot determine device os, bailing.")};var gc=function(){var t=!1;return window.bf&&window.bf.optOut&&(t=window.bf.optOut()),t};fc.optOut=gc;var bc,vc=n("4HaG");bc={};var mc=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.startFrom,r=void 0===n?1:n;if(null===t||void 0===t||""===String(t)||/\[object Object\]/.test(String(t)))throw new TypeError("The argument should be a primitive non-empty value");t in bc||(bc[t]=_r(r));var i=bc[t];return i()};function yc(t,e,n,r){Object(Hn.forEach)(t,(function(i,o){"wid"===o&&String(i)===String(e)?t[o]=n:"object"===typeof i&&null!==i&&yc(i,e,n,r),"targeting"===o&&Object.assign(i,r)}))}function Oc(t,e,n){t.id=t.id.replace(e,n)}function wc(t,e,n,r){e.forEach((function(e){var i=t.classList.contains(e);t.classList.remove(e),i&&t.classList.add(e.replace(n,r))}))}function jc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Ac(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?jc(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):jc(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Ec(t){return function(t){function e(){return Object(fi.b)(this,e),Object(fi.a)(this,e,arguments)}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"setup",value:function(){return this.onPublicEvent("post-message:".concat(this.config.wid),this.onPostMessage.bind(this)),Object(fi.e)(Object(fi.f)(e.prototype),"setup",this).call(this)}},{key:"onPostMessage",value:function(t){var e=t.action,n=t.ad;"error"!==e?"force-collapse"!==e?this.eventBus.trigger("ad-native--".concat(e,":").concat(this.config.wid),n):this.eventBus.trigger("ad-native--loaded:".concat(this.config.wid),Ac(Ac({},n),{},{type:"empty"})):this.destroy()}}])}(t)}var _c=0,Pc={};Pc={};var Rc=function(){return Object(fi.c)((function t(e){var n=this,r=e.throttleTimeout,i=e.parent;Object(fi.b)(this,t),this.callbacks={},this.target=i||window,this._triggerThrottled=Object(Hn.throttle)((function(){return n.trigger()}),r),this.target.addEventListener("resize",this._triggerThrottled)}),[{key:"add",value:function(t){return this.callbacks[++_c]=t,_c}},{key:"remove",value:function(t){delete this.callbacks[t]}},{key:"trigger",value:function(t){if(t in this.callbacks)this.callbacks[t]();else for(var e in this.callbacks)e in this.callbacks&&this.callbacks[e]()}},{key:"destroy",value:function(){this.target.removeEventListener("resize",this._triggerThrottled),delete this.target,delete this.callbacks,delete this._triggerThrottled}},{key:"isEmpty",get:function(){return 0===Object.keys(this.callbacks).length}}])}(),xc=function(t){var e=t.throttleTimeout,n=void 0===e?350:e,r=t.parent,i=t.callback,o=Pc[n];o||(o=Pc[n]=new Rc({throttleTimeout:n,parent:r}));var a=o.add(i);return o.trigger(a),a},Tc=function(t){Object.keys(Pc).forEach((function(e){var n=Pc[e];n.remove(t),n.isEmpty&&(n.destroy(),delete Pc[e])}))};function kc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Cc(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?kc(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):kc(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var Lc=function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments)).formatType=t.constructor.formatType,t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"skin",get:function(){return this.config.skin||this.config.zone1||null}},{key:"setup",value:function(){var t=this;return Object(fi.e)(Object(fi.f)(e.prototype),"setup",this).call(this).then((function(){return t.buildFormat()}))}},{key:"buildFormat",value:function(){var t=this.formatContainer||this.element,e=Ya({template:this.template,props:Cc(Cc({},this.getTemplateData()),{},{context:this.context}),target:t});e&&this.addDestroyAction((function(){return e.$destroy()})),this.element.classList.add("ad-flexible--".concat(this.formatType)),"creativeId"in this.context.ad&&this.element.classList.add("ad-flexible--".concat(this.context.ad.creativeId)),this.context.env.isE2ETest&&(this.element.dataset.dfpClickTracker=this.context.ad.dfpClickTracker)}},{key:"addClassesToParent",value:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];this.eventBus.trigger("ad-unit-tpl:".concat(this.config.wid),{moreClasses:e})}},{key:"getTemplateData",value:function(){return this.context.ad}}],[{key:"getFormatDefinition",value:function(){return[this.formatType,this]}}])}(Ka),Ic=n("Kpow");function Mc(t,e){return!e||e.length<0?t:e+encodeURIComponent(t)}function Sc(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return Go.getTranslationStr(t,e)}Object.defineProperties(Sc,{ADVERTISEMENT:{get:function(){return Sc("ADVERTISEMENT","Advertisement")}},PROMOTED_BY:{get:function(){return Sc("PROMOTED_BY","Promoted by")}}});var zc={"1:1":"data:image/gif;base64,R0lGODlhAQABAIAAAP///////yH5BAEAAAEALAAAAAABAAEAAAICTAEAOw==","4:3":"data:image/gif;base64,R0lGODlhBAADAIAAAP///////yH5BAEAAAEALAAAAAAEAAMAAAIDjI9WADs=","16:9":"data:image/gif;base64,R0lGODlhEAAJAIAAAP///////yH5BAEAAAEALAAAAAAQAAkAAAIKjI+py+0Po5yUFQA7"};function Nc(t){var e;return{c:function(){(e=Object(Ic.m)("h2")).textContent="".concat(Sc.ADVERTISEMENT),Object(Ic.f)(e,"class","ad-animated ad-fade ad__disclosure--ex ad__disclosure--programmatic js-ad-disclosure")},m:function(t,n){Object(Ic.v)(t,e,n)},p:Ic.z,d:function(t){t&&Object(Ic.l)(e)}}}function Dc(t){var e,n=t[0]&&Nc();return{c:function(){n&&n.c(),e=Object(Ic.n)()},m:function(t,r){n&&n.m(t,r),Object(Ic.v)(t,e,r)},p:function(t,r){var i=Object(a.a)(r,1)[0];t[0]?n?n.p(t,i):((n=Nc()).c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null)},i:Ic.z,o:Ic.z,d:function(t){n&&n.d(t),t&&Object(Ic.l)(e)}}}function Bc(t,e,n){var r=e.needsDisclosure,i=void 0===r||r;return t.$$set=function(t){"needsDisclosure"in t&&n(0,i=t.needsDisclosure)},[i]}var Gc=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,Bc,Dc,Ic.B,{needsDisclosure:0}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b),Fc=function(t){function e(){return Object(fi.b)(this,e),Object(fi.a)(this,e,arguments)}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"template",get:function(){return Gc}},{key:"getTemplateData",value:function(){return{needsDisclosure:!this.element.querySelector(".js-ad-disclosure")}}},{key:"buildFormat",value:function(){var t=this,n=this.config.wid,r=ma.getSlotContainer(n);r?(Object(fi.e)(Object(fi.f)(e.prototype),"buildFormat",this).call(this),r.parentElement.insertBefore(this.element.querySelector(".js-ad-disclosure"),r),requestAnimationFrame((function(){return t.eventBus.trigger("ad-content-rendered:".concat(n))}))):window.raven&&window.raven.captureMessage("buildFormat error: slot does not exist",{tags:{wid:n}})}}])}(Lc);Object(fi.d)(Fc,"formatType","programmatic");var Uc=function(t){function e(){return Object(fi.b)(this,e),Object(fi.a)(this,e,arguments)}return Object(fi.g)(e,t),Object(fi.c)(e)}(Fc);Object(fi.d)(Uc,"formatType","programmatic_responsive");var Vc={get isBPage(){try{return"buzz"in window.BZFD.Context}catch(t){return!1}},get hasQuizBadge(){try{return window.BZFD.Context.buzz.badges.filter((function(t){return"quiz"===t.badge_type})).length>0}catch(t){return!1}},get hasQuiz(){try{return window.BZFD.Context.page.hasQuiz||this.hasQuizBadge}catch(t){return!1}},get hasList(){try{return window.BZFD.Context.buzz.format.type.match(/list/)}catch(t){return!1}},get isFeedPage(){return!!window.FEEDPAGER||!!window.BZFD.Context.feedpager},get isFeedpager(){return Vc.isFeedPage},get isFeed(){return!!window.BZFD.Context.isFeed},get isHomePage(){return!!BZFD.Context.homepage||"home"===Vc.pageName},get isBuzzblocks(){try{return"buzzblocks"===BZFD.Config.service}catch(t){return!1}},get isDev(){try{return"dev"===BZFD.Config.env}catch(t){return!1}},get isStage(){try{return"stage"===BZFD.Config.env}catch(t){return!1}},get isProd(){try{return"prod"===BZFD.Config.env}catch(t){return!1}},get isPharmaceutical(){return"emdserono"===Vc.author},get author(){try{return Vc.isBPage?BZFD.Context.buzz.username:BZFD.Context.page.username}catch(t){return null}},get pageName(){try{return BZFD.Context.page.name}catch(t){return null}},get pageCategory(){try{return BZFD.Context.page.category}catch(t){return null}},get pageVertical(){try{return BZFD.Context.page.vertical}catch(t){return null}},get pageClassification(){try{return BZFD.Context.page.classification||{}}catch(t){return{}}},get localization(){try{return Object(Hn.pick)(BZFD.Context.page.localization,["country","language","locale"])}catch(t){return{}}},get allPageClassifications(){try{return BZFD.Context.page.allClassifications||{}}catch(t){return{}}},get allPageSections(){try{return BZFD.Context.page.allClassifications.sections||[]}catch(t){}try{return BZFD.Context.page.sections||[]}catch(t){return[]}},get allPageEditions(){try{return BZFD.Context.page.allClassifications.editions||[]}catch(t){return[]}},get pageFilter(){try{return BZFD.Context.page.filter}catch(t){return null}},get pageFilters(){try{return BZFD.Context.page.filters||{}}catch(t){return{}}},get pageMainFilter(){try{var t,e=BZFD.Context.page.filters;for(var n in e)if(e[n].is_main){t=n;break}return t||null}catch(r){return null}},get isWidePost(){try{return"buzz"in BZFD.Context&&"wide"===BZFD.Context.page.width}catch(t){return!1}},get facebookApiAppId(){try{return BZFD.Config.facebookApi.appId}catch(t){return null}},get facebookApiVersion(){try{return BZFD.Config.facebookApi.version}catch(t){return"v2.9"}},get isNewsPost(){try{return"news"===BZFD.Context.page.vertical}catch(t){return!1}},get buzzType(){var t="article";return Vc.hasQuiz?t="quiz":Vc.hasList&&(t="list"),t},get buzzTags(){try{return BZFD.Context.buzz.tags}catch(t){return[]}},get buzzFlags(){try{return BZFD.Context.buzz.flags}catch(t){return{}}},get locale(){try{return BZFD.Context.page.localization.locale}catch(t){return"en_US"}},get hasTranslations(){try{return!!BZFD.Context.page.localization.translations}catch(t){return!1}},get webRoot(){try{return BZFD.Config.webRoot}catch(t){return""}},get prebidUnits(){try{BZFD.Config.ads.prebidUnits.length}catch(t){Object(Hn.set)(window,"BZFD.Config.ads.prebidUnits",[])}return BZFD.Config.ads.prebidUnits},get programmaticSizes(){try{return BZFD.Config.ads.programmaticSizes}catch(t){throw new Error("`BZFD.Config.ads` is not defined, which may cause ads to work incorrectly; use `bf_ads.get_ads_config` to make the config")}},get contextDestination(){var t,e;try{t=BZFD.Context.buzz.destination}catch(n){t=null}try{e=BZFD.Context.page.destination}catch(n){e=null}return t||e||"buzzfeed"},get isShopping(){try{return"Shopping"===Vc.pageCategory||-1!==Vc.allPageSections.indexOf("Shopping")}catch(t){return!1}},get isCommerce(){try{return!(!this.isShopping&&!Vc.buzzTags.some((function(t){return"intlcomtent"===t||"comtent"===t})))}catch(t){return!1}},get isCommunity(){return"Community"===Vc.pageCategory},get isAsIs(){try{return Vc.isBPage&&/^(As\/?Is)/im.test(Vc.pageClassification.section)||"asis"===Vc.pageName}catch(t){return!1}},get isBFO(){try{return"buzzfeed"===Vc.contextDestination}catch(t){return!1}},get isBFN(){try{return"buzzfeed_news"===Vc.contextDestination}catch(t){return!1}},get buzzId(){try{return BZFD.Context.buzz.id}catch(t){return""}},isAdPost:function(t){try{return!!function(t){var e=null;try{var n=ac(t,".js-buzz",!0);n&&(e=JSON.parse(n.querySelector("script").textContent).context)}catch(r){}return e}(t).buzz.flags.ad}catch(e){}try{return!!BZFD.Context.buzz.flags.ad}catch(e){return!1}}},Hc=Vc,Zc=function(t){var e={buzzIds:"buzz_ids",page:"page",pageSize:"page_size",imageCrop:"image_crop",advertiserId:"advertiser_id",showName:"show_name",keywords:"keywords",fields:"fields",q:"q",qFuzzy:"q__fuzzy",username:"username",areaId:"area_id",startId:"start_id",startFrom:"start_from",convertLinks:"convert_links",platform:"platform"};return Object.keys(e).reduce((function(n,r){var i=e[r],o=t[r];return o&&(n[i]=o),n}),{})};function Wc(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"v1",i=n&&"control"!==n?"".concat(t.uri,"/").concat(n):t.uri,o=e.origin||Hc.webRoot,a="".concat(o).concat("/site-component","/").concat(r),c=e.localization&&e.localization.getEdition?e.localization.getEdition():Za.getEdition(e.service),s=t.edition(c,n);s&&(a="".concat(a,"/").concat(s));var u=Zc(e);Object.assign(u,e.extraParams);var l=Object.keys(u).length>0?Un(u):"";return ir("".concat(a,"/").concat(i).concat(l),{skipAuth:!0}).then((function(e){return Promise.resolve(t.view(e))}))}function qc(t,e){var n=e.env,r=e.localization;return Wc({uri:"branded-buzz",view:function(t){return t.buzzes[0].buzz},edition:function(){return""}},{origin:n.webRoot,extraParams:{u:"buzz-mobile"},localization:r},t)}function Yc(t){if("Failed to fetch"!==t.message)throw t}var Jc=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.buzzId,r=e.buzz,i=void 0===r?null:r,o=e.buzzIds,a=void 0===o?[]:o,c=e.buzzes,s=void 0===c?[]:c,u=e.creativeId,l=arguments.length>1?arguments[1]:void 0,f=l.env,d=l.localization;if(i)t=Promise.resolve([i]);else if(s.length>0)t=Promise.resolve(s);else if(n||0!==a.length){n&&(a=[n]);var h="en-us";d?h=d.edition:f.localization&&(h=f.localization.country);var p=function(t){return qc(t,{env:f,localization:{getEdition:function(){return h}}})};t=Promise.all(a.map(p))}else t=Promise.reject(new ma.AdError("Missing buzz id for creative #".concat(u)));return t.catch(Yc),t};var Qc=function(t){function e(){var t;Object(fi.b)(this,e);var n=(t=Object(fi.a)(this,e,arguments)).onPublicEvent("ad-native--loaded:".concat(t.config.wid),t.handleNativeAdLoad.bind(t)),r=new Pr({unsubscribe:n}),i=new Pr;return t.contentPending={native:r,other:i},t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"handleNativeAdLoad",value:function(t){var e=this;/^buzz/.test(t.type)?Jc(t,{env:this.context.env,localization:this.context.localization}).then((function(n){n.length>1?t.buzzes=n:t.buzz=n[0],e.contentPending.native.resolve(t)}),(function(n){return e.contentPending.native.reject([t,n])})):this.contentPending.native.resolve(t),this.stopLoadedListening()}},{key:"setup",value:function(){var t,n=this,r=this,i=this.config.wid;return this._onSlotRenderEnded=(t=function(t){return e=ma.isProgrammaticSlot(t,r.config.size),n=function(e){ma.isEmpty(t)?r.contentPending.other.resolve({type:"empty"}):e&&r.contentPending.other.resolve({type:"programmatic"})},i?n?n(e):e:(e&&e.then||(e=Promise.resolve(e)),n?e.then(n):e);var e,n,i},function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}),this.onPublicEvent("gpt:slotRenderEnded:".concat(i),this._onSlotRenderEnded),Promise.race(Object(Hn.values)(this.contentPending)).then((function(t){n.lifecycleState!==Ja.DESTROYED&&(n.eventBus.trigger("ad-content-ready:".concat(i),t),n.element.classList.add("ad-content-ready","ad-flexible--".concat(t.type)))}),(function(t){var e=Object(a.a)(t,2),r=e[0],o=e[1];n.eventBus.trigger("ad-content-error:".concat(i),[r,o])})).then((function(){return n.stopLoadedListening()})),Object(fi.e)(Object(fi.f)(e.prototype),"setup",this).call(this)}},{key:"stopLoadedListening",value:function(){Object(Hn.values)(this.contentPending).forEach((function(t){return t.unsubscribe()})),this.eventBus&&this.eventBus.off("gpt:slotRenderEnded:".concat(this.config.wid),this._onSlotRenderEnded)}},{key:"destroy",value:function(){this.stopLoadedListening(),Object(fi.e)(Object(fi.f)(e.prototype),"destroy",this).call(this)}}])}(Ka);var Kc=function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments)).gptEv=null,t.ad=null,t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"setup",value:function(){this.trackingReady=new Pr;var t=this.config.wid;return this.onPublicEvent("ad-content-ready:".concat(t),this.trackingReady.resolve),this.onPublicEvent("ad-impression:".concat(t),this.trackImpression.bind(this)),this.onPublicEvent("ad-click:".concat(t),this.trackClick.bind(this)),this.onPublicEvent("advertise-click:".concat(t),this.trackAdvertiseClick.bind(this)),this.onPublicEvent("gpt:slotRenderEnded:".concat(t),this.handleSlotRender.bind(this)),Object(fi.e)(Object(fi.f)(e.prototype),"setup",this).call(this)}},{key:"handleSlotRender",value:function(t){var e=this;t.isEmpty||(this.gptEv=t,this.trackingReady.then((function(n){Dr("info","plugins","core-tracking","#handleSlotRender",{lifecycleState:e.lifecycleState,ad:n,gptEv:t}),e.lifecycleState!==Ja.DESTROYED&&(e.ad=n,e.normalize3rdPartyTrackers(),e.setUpImpressionTracking(),e.setUpClickTracking())})))}},{key:"setUpClickTracking",value:function(){var t=this;this._trackClicksBound=function(e){var n=ac(e.target,"a",!0);n&&!("adsNoTrack"in n.dataset)&&t.eventBus.trigger("ad-click:".concat(t.config.wid),{link:n})},this.element.addEventListener("click",this._trackClicksBound),this.setUpProgrammaticTracking()}},{key:"setUpProgrammaticTracking",value:function(){var t=this;if("programmatic"===this.ad.type){var e=this.element.querySelector("iframe");this._detectProgrammaticClick=function(){window.setTimeout((function(){document.activeElement===e&&t.eventBus.trigger("ad-click:".concat(t.config.wid))}),0)},window.addEventListener("blur",this._detectProgrammaticClick)}}},{key:"setUpImpressionTracking",value:function(){this.eventBus.trigger("ad-impression:".concat(this.config.wid))}},{key:"trackClick",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.link;e&&e.dataset.bfa||this.notifyBFA("click"),this.notify3rdparty("click")}},{key:"trackAdvertiseClick",value:function(t){var e={t:"click:advertise",n:"Advertise",l:this.config.adPos,d:"edition",data:{url:t.url},opt:{dimension2:this.context.env.localizationCountry,dimension7:this.config.adPos}};wo({trackFn:(this.context.tracking||{}).track,eventName:"click",eventData:e})}},{key:"trackImpression",value:function(){this.notify3rdparty("impression"),this.notifyBFA("scroll")}},{key:"notifyBFA",value:function(t){if(t&&this.config&&this.config.adPos){var e=this.config.adPos,n="programmatic"===this.ad.type,r=this.gptEv.creativeId;!r&&n&&(r="adxbackfill");var i={t:t,n:"".concat(e,"-dfp"),l:e,d:n?"programmatic":"creative",data:{obj_id:0,type:this.ad.type,creativeId:r},opt:{dimension13:r}};wo({trackFn:(this.context.tracking||{}).track,eventName:t,eventData:i})}}},{key:"notify3rdparty",value:function(t){var e=this;(this.ad["".concat(t,"Trackers")]||[]).forEach((function(n){return e.create3rdPartyPixel(n,t)}))}},{key:"normalize3rdPartyTrackers",value:function(){var t=this.ad;if("programmatic"!==t.type){var e=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter((function(t){return!!t}))};t.clickTrackers||(t.clickTrackers=[(window.BF_DFP_CLICKS||{})[this.config.wid]]),t.clickTrackers=e(t.clickTrackers),t.impressionTrackers=e(t.impressionTrackers),t.customTrackers="string"===typeof t.customTrackers?e(t.customTrackers.split(/\s+/)):[],this.init3rdPartyScripts()}}},{key:"create3rdPartyPixel",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=new Image;n.src=t,this.context.env.isE2ETest&&(n.classList.add("ad-slot-invisible"),n.classList.add("ad-e2e--pixel-".concat(e)),this.element.appendChild(n))}},{key:"init3rdPartyScripts",value:function(){var t=this;this.ad.customTrackers.forEach((function(e){return Object(Dn.a)(e,t.element)}))}},{key:"destroy",value:function(){this.element.removeEventListener("click",this._trackClicksBound),delete this._trackClicksBound,this._detectProgrammaticClick&&(window.removeEventListener("blur",this._detectProgrammaticClick),delete this._detectProgrammaticClick),Object(fi.e)(Object(fi.f)(e.prototype),"destroy",this).call(this)}}])}(Ka),Xc=function(t){function e(){var t;Object(fi.b)(this,e);var n=(t=Object(fi.a)(this,e,arguments)).wid=t.config.wid;if(t.onPublicEvent("ad-wireframe-no-collapse:".concat(n),t._onNoCollapse.bind(t)),t.onPublicEvent("ad-content-rendered:".concat(n),t._loadAd.bind(t)),t.wireframeElement=ac(t.element,".ad-wireframe-wrapper")||null,(!t.config.platform||"autodetect"===t.config.platform)&&t.wireframeElement){var r=Math.max(document.documentElement.clientWidth,window.innerWidth||0),i=t.config.size.filter((function(t){return Array.isArray(t)&&t[0]<=r}));t._setWireframeSizes({width:Math.max.apply(null,i.map((function(t){return t[0]}))),height:Math.max.apply(null,i.map((function(t){return t[1]})))})}return t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"_setWireframeSizes",value:function(t){var e=t.width,n=t.height;(1!==e||1!==n)&&Number.isInteger(e)&&Number.isInteger(n)&&(this.wireframeElement.dataset.wireframeWidth=e,this.wireframeElement.dataset.wireframeHeight=n)}},{key:"_onNoCollapse",value:function(){var t=this;this._thumbnailReady=vo(this.element),this._thumbnailReady.then((function(){return t._loadAd()})),this.addDestroyAction((function(){return t._thumbnailReady.unsubscribe()}))}},{key:"_loadAd",value:function(){var t=this;this._thumbnailReady&&this._thumbnailReady.unsubscribe(),this._loadAdProgress=cc(this.element),Promise.race([this._loadAdProgress,new Promise((function(t){return setTimeout(t,750)}))]).then((function(){t.lifecycleState!==Ja.DESTROYED&&t.eventBus.trigger("ad-wireframe-fadein-finish:".concat(t.wid))})),this.addDestroyAction((function(){return t._loadAdProgress.unsubscribe()})),this._adFadeIn(),this._removeWireframe(),this.element.classList.add("ad--loaded")}},{key:"_adFadeIn",value:function(){this.element.querySelectorAll(".ad-wireframe-text").forEach((function(t){t.style.display="none"}));var t=this.element.querySelectorAll(".ad-fade");t.length<=0&&this.element.classList.contains("ad-fade")&&(t=[this.element]),t.forEach((function(t){if(null===t.offsetParent)t.classList.remove("ad-fade");else{var e=cc(t,{properties:["opacity"]}),n=function(){t.classList.remove("ad-fade"),e.unsubscribe()};e.then(n),t.style.opacity=1,setTimeout(n,500)}}))}},{key:"_detectCollapseEnd",value:function(){var t=this;cc(this.wireframeElement||this.element,{properties:["opacity"]}).then((function(){t._removeWireframeAndHide()}))}},{key:"_removeWireframeAndHide",value:function(){this._removeWireframe(!0)}},{key:"_removeWireframe",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this._removeWireframeClasses(this.element,t),t&&(this.element.classList.add("js-hidden"),this.eventBus.trigger("ad-wireframe-collapse-finish:".concat(this.wid)))}},{key:"_removeWireframeClasses",value:function(t,e){if(t&&"string"===typeof t.className&&(t.className=t.className.replace(/\bad-wireframe[-\S]*/g,""),t.className=t.className.replace(/\bad-fade[-\S]+/g,""),e&&(t.className=t.className.replace(/\bad-fade/g,""))),null!==t&&void 0!==t&&t.children)for(var n=0;n<t.children.length;n++){var r=t.children[n];this._removeWireframeClasses(r,e)}}},{key:"_broadcastOnRender",value:function(t){var e=this;_o().then((function(n){if(!n){var r=e.wid;t.isEmpty||e.eventBus.trigger("ad-wireframe-no-collapse:".concat(r))}}))}},{key:"setup",value:function(){return this.onPublicEvent("gpt:slotRenderEnded:".concat(this.wid),this._broadcastOnRender.bind(this)),Object(fi.e)(Object(fi.f)(e.prototype),"setup",this).call(this)}},{key:"destroy",value:function(){Object(fi.e)(Object(fi.f)(e.prototype),"destroy",this).call(this),delete this._thumbnailReady,delete this._loadAdProgress,this.element=null,this.config=null,this.wid=null}}])}(Ka);function $c(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function ts(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?$c(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):$c(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var es=gc,ns=function(t){function e(t){var n;Object(fi.b)(this,e);var r=(t=ts(ts({},t),{},{config:Object(Hn.cloneDeep)(t.config)})).element;r&&r.classList.contains("ad-wireframe-wrapper")&&(r=r.firstElementChild);var i=(n=Object(fi.a)(this,e,[ts(ts({},t),{},{element:r})])).config;"advertiserContext"in i||(i.advertiserContext=n.context.env.isAdPost(n.element));var o=i.targeting||{};return"wid"in o||(o.wid=i.wid),"pos"in o||(o.pos=[i.adPos]),i.targeting=o,n.plugins=new Set([Qc,Kc]),"defaultWireframes"in t&&!t.defaultWireframes||n.plugins.add(Xc),n.isRendered=!1,n.instanceId=n.id,i.isInfinite&&n.makeInfiniteWid(),n.setupSizes(),n.addA11yMarkup(),n.addCssClasses(),n.renderQueue=[],n.reset(),n}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"skin",get:function(){return this.config.skin||this.config.zone1||null}},{key:"getEventId",value:function(){return"".concat(this.config.adPos,"-").concat(this.id)}},{key:"setup",value:function(){var t=this,n=this.config.wid;this.element.dataset.instanceId=this.instanceId,this.onPublicEvent("ad-wireframe-collapse-finish:".concat(n),this.destroy.bind(this)),this.onPublicEvent("ad-content-error:".concat(n),this.handleContentError.bind(this)),this.onPublicEvent("gpt:slotRenderEnded:".concat(n),this.handleSlotRenderEnded.bind(this)),this.onPublicEvent("ad-unit-tpl:".concat(n),(function(e){var n=e.moreClasses;return t.addCssClasses(n)}));var r=this.getEventId();return this.onPublicEvent("gpt:slotRenderEnded:".concat(n),(function(e){t.eventBus.trigger("gpt:slotRenderEnded:".concat(r),e)})),this.onPublicEvent("ad-content-ready:".concat(n),(function(e){var n=e.type;t.eventBus.trigger("ad-content-ready:".concat(r),{type:n})})),this.render(),Object(fi.e)(Object(fi.f)(e.prototype),"setup",this).call(this)}},{key:"isFirstInit",value:function(){return!this.element.hasAttribute("data-instance-id")}},{key:"isEnabled",value:function(){return Promise.all([this.isFirstInit(),!es(),this.isPlatformCorrect()])}},{key:"setupSizes",value:function(){var t=yr.isXsmall(),e=yr.isSmall(),n=Object(vc.b)(),r=this.context.env.adSizes;e?this.filterProgrammaticSizes({max:r.PROGRAMMATIC_LEADERBOARD}):t&&(this.filterProgrammaticSizes({max:r.PROGRAMMATIC_SMARTPHONE_BANNER}),this.excludeSize(r.PROGRAMMATIC_VERTICAL),n&&this.excludeSize(r.FLUID))}},{key:"deferRender",value:function(){var t;this.renderQueue=this.renderQueue||[],(t=this.renderQueue).push.apply(t,arguments)}},{key:"render",value:function(){var t=this;return this.isRendered?Promise.resolve():(this.isRendered=!0,this.init().then((function(){return Promise.all(t.renderQueue).then((function(){return t.requestAd()}))}),(function(){})))}},{key:"requestAd",value:function(){var t=this;return ma.renderWidget(this.config).catch((function(e){return t.eventBus&&t.eventBus.trigger("ad-request-failure:".concat(t.config.wid)),e instanceof di?Promise.resolve(e):Promise.reject(e)}))}},{key:"addA11yMarkup",value:function(){this.element.setAttribute("role","complementary"),this.element.setAttribute("aria-label","Advertisement")}},{key:"addCssClasses",value:function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;null!==e&&(t=this.element.classList).add.apply(t,Object(c.a)(e));var n=this.config.wid;this.element.classList.add("js-ad","js-ad-".concat(n));var r=this.skin;r&&this.element.classList.add("ad--".concat(r)),this.config.advertiserContext&&this.element.classList.add("ad--partner");var i=ma.getSlotContainer(n);i&&i.classList.add("ad-slot","js-ad-slot","js-ad-slot-".concat(n))}},{key:"isPlatformCorrect",value:function(){var t,e=this.config.platform||null,n=this.config.supports||null;return e&&("desktop"===e?t=["md","lg"]:"mobileweb"===e&&(t=["xs","sm"])),n&&(t=n),!Array.isArray(t)||yr.isAny(t)}},{key:"handleSlotRenderEnded",value:function(t){try{var e=ma.getSlotContainer(this.config.wid);return n=ma.isIframeContent(t,this.config.size),r=function(t){t?e.classList.remove("ad-slot-invisible"):(e.classList.add("ad-slot-invisible"),$a(e))},i?r?r(n):n:(n&&n.then||(n=Promise.resolve(n)),r?n.then(r):n)}catch(o){return Promise.reject(o)}var n,r,i}},{key:"handleContentError",value:function(){this.destroy()}},{key:"addSize",value:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=yr.isXsmall(),i=Object(vc.b)();r&&i&&(e=e.filter((function(t){return"fluid"!==t}))),this.config.size=Xi.add.apply(Xi,[this.config.size].concat(Object(c.a)(e)))}},{key:"excludeSize",value:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];this.config.size=Xi.exclude.apply(Xi,[this.config.size].concat(e))}},{key:"filterProgrammaticSizes",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.config.size=Xi.filterProgrammatic(this.context.env.programmaticSizes,this.config.size,t)}},{key:"reset",value:function(){ma.destroySlot(this.config);try{delete this.element.dataset.instanceId}catch(t){console.debug("Unable to delete instanceId from element in destroySlot"),console.debug("Instance ID that was not removed:",this.element.dataset.instanceId)}this.element.classList.remove("js-hidden")}},{key:"destroy",value:function(){ma.destroySlot(this.config),Object(fi.e)(Object(fi.f)(e.prototype),"destroy",this).call(this)}}])}(Ka.withMixins(Ec,(function(t){return function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments)).formats=t.formats||new Map,t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"setup",value:function(){return this.element.classList.add("ad-flexible"),this.stopContentListening=this.onPublicEvent("ad-content-ready:".concat(this.config.wid),this.handleAdContentLoaded.bind(this)),this.onPublicEvent("ad-request-failure:".concat(this.config.wid),this.handleAdRequestFailure.bind(this)),Object(fi.e)(Object(fi.f)(e.prototype),"setup",this).call(this)}},{key:"handleSlotRenderEnded",value:function(t){Object(fi.e)(Object(fi.f)(e.prototype),"handleSlotRenderEnded",this).apply(this,arguments),ma.isEmpty(t)&&this.handleAdContentLoaded({type:"empty"})}},{key:"handleAdContentLoaded",value:function(t){var e=this;this.context.ad=t,this.stopContentListening();var n=new Set;this.formats.forEach((function(e,r){("string"===typeof r&&r===t.type||r instanceof RegExp&&r.test(t.type))&&e.forEach((function(t){return n.add(t)}))})),n.forEach((function(t){try{var n=new t(e.context);e.addDestroyAction((function(){return n.destroy()})),n.init()}catch(r){console.error(r)}}))}},{key:"handleAdRequestFailure",value:function(t){t instanceof di&&this.formats.has("empty")&&(this.context.isAdBlocked=!0,this.handleAdContentLoaded({type:"empty"}))}},{key:"addFormat",value:function(t,e){this.formats.has(t)||this.formats.set(t,new Set),this.formats.get(t).add(e)}}])}(t)}),(function(t){return function(t){function e(){return Object(fi.b)(this,e),Object(fi.a)(this,e,arguments)}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"makeInfiniteWid",value:function(){if(this.config.isInfinite&&this.isFirstInit()){var t=this.config.wid,e="infiniteIdx"in this.element.dataset?Number(this.element.dataset.infiniteIdx):mc(t);if(0!==e){var n="".concat(t,"-").concat(e),r=Object(Hn.partialRight)(yc,t,n,{infinite_index:e}),i=Object(Hn.partialRight)(Oc,t,n),o=Object(Hn.partialRight)(wc,["js-ad-".concat(t),"js-ad-format-".concat(t),"js-ad-slot-".concat(t),"ad-wireframe-".concat(t),"ad-fade-".concat(t)],t,n);r(this.config),this.element.querySelectorAll('script[type="text/x-config"]').forEach((function(t){var e=JSON.parse(t.textContent);r(e),t.textContent=JSON.stringify(e)}));var a=/^BF_WIDGET_/.test(this.element.id)?this.element:this.element.querySelector("#BF_WIDGET_".concat(t));a&&i(a);var c=this.element.querySelector("#".concat(ma.getSlotContainerId(t)));c&&i(c);var s=this.element.querySelector("#bf-item-".concat(t,"-1"));s&&i(s),o(this.element),this.element.querySelectorAll("*").forEach((function(t){return o(t)})),this.element.dataset.infiniteIdx=e}}}}])}(t)}))),rs=Object(fi.c)((function t(){Object(fi.b)(this,t)}));function is(t){return t instanceof rs}var os=function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments))._state={},t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"init",value:function(){return this.resolveConfig(),Object(fi.e)(Object(fi.f)(e.prototype),"init",this).call(this)}},{key:"setup",value:function(){return Promise.all([Object(fi.e)(Object(fi.f)(e.prototype),"setup",this).call(this),this.configure()])}},{key:"destroy",value:function(){Object(fi.e)(Object(fi.f)(e.prototype),"destroy",this).call(this),delete this._state}},{key:"isEnabled",value:function(){var t=function(t){return t};return this.units.filter(t).length>0||this.unitsRepeated.filter(t).length>0}},{key:"configure",value:function(){}},{key:"resolveConfig",value:function(){var t,e=this,n=this.context.solid;if(n){var r=Object.keys(n.cssBreakpoints),i=n.getBreakPoint();t=function(t){var n=e["".concat(t,"_").concat(i)]||e.config["".concat(t,"_").concat(i)];return void 0!==n?n:(n=e[t]||e.config[t],Object(Hn.isObject)(n)&&(r.some((function(t){return t in n}))||"default"in n)?n=i in n?n[i]:n.default:n)}}else t=function(t){return e[t]||e.config[t]};this.units=t("units")||[],this.unitsRepeated=t("unitsRepeated")||[],this.pattern=t("pattern")||[],this.placements=t("placements")||[],this.density=Number(t("density"))||0}},{key:"reset",value:function(){delete this._state.currAd,delete this._state.currPlacement}},{key:"getNextAd",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{pause:!1},e=t.pause;"currAd"in this._state||(this._state.currAd={idx:-1,usePrev:!1});var n,r=this._state.currAd;return is(r.ad)?r.ad:((r.usePrev||e)&&r.idx>-1?n=r.ad:(r.idx++,n=this.getAd(r.idx)),r.ad=n,r.usePrev=e,n||null)}},{key:"getAd",value:function(t){var e,n=!1;return t<this.units.length?e=this.units[t]:this.unitsRepeated.length>0?(e=this.unitsRepeated[(t-this.units.length)%this.unitsRepeated.length],n=!0):e=new rs,!e||is(e)||"slot"in e||(e={slot:e}),n&&(e.slot.isInfinite=!0),e}},{key:"getNextPlacement",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{pause:!1},e=t.pause;"currPlacement"in this._state||(this._state.currPlacement={idx:-1,placement:-1,usePrev:!1});var n,r=this._state.currPlacement;if(is(r.placement))return r.placement;if((r.usePrev||e)&&r.idx>-1)n=r.placement;else{var i=this.pattern,o=i.length,a=this.placements,c=this.density,s=++r.idx;if(o>0)if(-1===r.placement)-1===(n=i.findIndex((function(t){return t})))&&(n=new rs);else{var u,l=r.placement%o,f=i.findIndex((function(t,e){return t&&e>l}));f>-1?u=f-l:(f=i.findIndex((function(t,e){return t&&e<=l})),u=o-l+f),n=r.placement+u}else n=s<a.length?a[s]-1:c>0?r.placement+c:new rs}return r.placement=n,r.usePrev=e,n}},{key:"getAdForPlacement",value:function(t){var e=this.getNextPlacement();return is(e)?new rs:t===e?this.getNextAd():(this.getNextPlacement({pause:!0}),null)}},{key:"prepareAd",value:function(t,e){return(t=Object(Hn.merge)({},t)).slot.position=e+1,t}},{key:"getAdModuleTemplate",value:function(){throw new di("The method should be implemented in derived classes")}}])}(Ka);os.Done=rs,os.isDone=is,(function(t){function e(){return Object(fi.b)(this,e),Object(fi.a)(this,e,arguments)}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"getBuzzData",value:function(){var t=this,e=this.context.ad.buzz,n=e.header.users.filter((function(t){return t.username===e.username})).map((function(e){return{displayName:e.display_name,url:"".concat(t.context.env.webRoot,"/").concat(e.username),avatar:e.user_image}}))[0],r=this.context.ad.clickThroughUrl?this.context.ad.clickThroughUrl:e.url,i=this.context.ad.dfpClickTracker||(window.BF_DFP_REDIRECTS||{})[this.config.wid];i&&(r="".concat(i).concat(encodeURIComponent(r)));var o={id:e.id,title:e.name,titleOriginal:e.name,description:e.ad_blurb,url:r,category:e.category,images:e.images,promotionType:e.user_type.replace(/^f_/,""),advertiser:n},a=this.pickVariation(e.active_experiment);return Object(Hn.merge)(o,a),o}},{key:"pickVariation",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=(t||{}).variations||[];if(0===e.length)return{};var n=Object(Hn.sample)(e),r=n.id,i=n.title,o=n.thumbnail,a={};return a.flexproVariation=r,i&&(a.title=i),o&&(a.images={big:o.url,dblbig:o.url,wide:o.wide_url,dblwide:o.wide_url}),a}},{key:"getTemplateData",value:function(){var t=Object(Hn.merge)({},this.context.ad);return t.buzz=this.getBuzzData(),t.slot=this.config,t}}])}(Lc)).formatType="buzz";var as=function(t){return function(t){function e(){return Object(fi.b)(this,e),Object(fi.a)(this,e,arguments)}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"setup",value:function(){Object(fi.e)(Object(fi.f)(e.prototype),"setup",this).call(this);var t=this.config.wid;this.config.isGAMNativeVideo=!0,this.onPublicEvent("ad-native--video-quartile:".concat(t),this.reportQuartile.bind(this)),this.onPublicEvent("ad-native--video-view:".concat(t),this.reportPlayback.bind(this)),this.onPublicEvent("ad-native--video-replay:".concat(t),this.reportReplay.bind(this)),this.onPublicEvent("ad-native--video-pause:".concat(t),this.reportPause.bind(this)),this.onPublicEvent("ad-native--video-volumechange:".concat(t),this.reportVolumeChange.bind(this)),this.onPublicEvent("ad-native--video-loadedmeta:".concat(t),this.reportLoadedMetaData.bind(this))}},{key:"reportLoadedMetaData",value:function(t){var e=t.duration,n=this.config.wid;this.eventBus.trigger("ad-native-video-ready:".concat(n),{duration:e}),this.eventBus.trigger("ad-content-rendered:".concat(n)),this.eventBus.trigger("dfp-native-video:loaded-metadata:".concat(n),t)}},{key:"reportQuartile",value:function(t){var e=this.config.wid;this.eventBus.trigger("ad-native-video-quartile:".concat(e)),4===t.quartile&&this.eventBus.trigger("ad-native-video-ended:".concat(e)),this.eventBus.trigger("native-video-embed:playback-quartile:".concat(e),t)}},{key:"reportReplay",value:function(t){this.eventBus.trigger("dfp-native-video:replay:".concat(this.config.wid),t)}},{key:"reportPlayback",value:function(t){this.eventBus.trigger("dfp-native-video:play:".concat(this.config.wid),t)}},{key:"reportPause",value:function(t){this.eventBus.trigger("ad-native-video-pause:".concat(this.config.wid)),this.eventBus.trigger("dfp-native-video:pause:".concat(this.config.wid),t)}},{key:"reportVolumeChange",value:function(t){this.eventBus.trigger("dfp-native-video:volumechange:".concat(this.config.wid),t)}},{key:"getTemplateData",value:function(){var t=Object.assign({},this.context.ad);return t.adPos=this.config.adPos,t}}])}(t.withMixins(Ec))},cs=function(t){function e(){return Object(fi.b)(this,e),Object(fi.a)(this,e,arguments)}return Object(fi.g)(e,t),Object(fi.c)(e)}(Lc.withMixins(as));Object(fi.d)(cs,"formatType","dfp_native_video");var ss=function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments)).template=null,t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"buildFormat",value:function(){this.element.classList.remove("js-hidden"),this.element.classList.add("ad-flexible--".concat(this.formatType),"ad-flexible--".concat(this.context.ad.creativeId)),this.eventBus.trigger("ad-content-rendered:".concat(this.config.wid))}}])}(Lc);Object(fi.d)(ss,"formatType","impression_pixel");var us=function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments)).noLazyRendering=!0,t.addFormat(ss.formatType,ss),t}return Object(fi.g)(e,t),Object(fi.c)(e)}(ns);function ls(t){if(t&&t.renderLookahead&&t.renderLookahead.toString().match(/^x/)){var e=parseFloat(t.renderLookahead.slice(1));return parseInt(window.innerHeight*e,10)}return t&&t.renderLookahead?t.renderLookahead:window.innerHeight}function fs(t){return function(t){function e(){return Object(fi.b)(this,e),Object(fi.a)(this,e,arguments)}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"setup",value:function(){var t=this;if(this.noLazyRendering)return Object(fi.e)(Object(fi.f)(e.prototype),"setup",this).call(this);var n=Promise.all([ma.isGPTPrefetch(),ma.detectAdBlock()]).then((function(e){var n=Object(a.a)(e,2),r=n[0],i=n[1];if(!r&&!i){var o="viewport-active:".concat(t.config.wid);t.deferRender(new Promise((function(e){t.onPublicEvent(o,e),t.viewable=new IntersectionObserver((function(e,n){e.forEach((function(e){e.isIntersecting&&(n.disconnect(),t.eventBus.trigger(o))}))}),{rootMargin:"".concat(ls(t.config),"px"),threshold:0}),t.viewable.observe(t.element)})))}}));return Promise.all([n,Object(fi.e)(Object(fi.f)(e.prototype),"setup",this).call(this)])}},{key:"destroy",value:function(){this.viewable&&(this.viewable.disconnect(),delete this.viewable),Object(fi.e)(Object(fi.f)(e.prototype),"destroy",this).call(this)}}])}(t)}function ds(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}var hs={default:{inViewSeconds:32,maxRefreshes:9999,infinite:!0},globalOverride:{edition:{"ja-jp":{inViewSeconds:30}}}},ps={infinite:!0,inViewSeconds:32,pages:[{adPos:"awareness1",wid:"42-1",targeting:{wid:"42-1",pos:["awareness1"]}},{adPos:"awareness2",wid:"42-2",targeting:{wid:"42-2",pos:["awareness2"]}},{adPos:"awareness3",wid:"42-3",targeting:{wid:"42-3",pos:["awareness3"]}}]},gs={maxRefreshes:9999,inViewSeconds:32,pages:[{adType:"toolbar",adPos:"tb1",wid:"52-1",targeting:{wid:"52-1",pos:["tb1"]}},{adType:"toolbar",adPos:"tb2",wid:"52-2",targeting:{wid:"52-2",pos:["tb2"]}},{adType:"toolbar",adPos:"tb3",wid:"52-3",targeting:{wid:"52-3",pos:["tb3"]}}]};function bs(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function vs(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?bs(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):bs(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function ms(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}var ys=hs.default;function Os(){}var ws=hs.globalOverride;function js(t){var e=t();if(e&&e.then)return e.then(Os)}var As=function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments)).refreshConfig={},t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"setup",value:function(){try{var t=this;return ms(t.isEnabled(),(function(n){if(n){var r,i,o=t.config.wid;t.complete=!1,t.creativeId=new Pr,t.emptyCount=0,t.impressionCount=0,t.impressionViewHit=!1,t.inViewPercent=0,t.isCurrentlyEmpty=!1,t.paused=!1,t.refreshCount=0,t.timeout=null,t.config.targeting.refreshable=!0,t.config.targeting.tbr=0,t.context.element.classList.add("ad--refreshable"),t.refreshConfig=vs(vs({},ys),t.config.refreshOptions),Dr("info","lifecycle","InViewRefresh ".concat(o," : AD UNIT OVERRIDES -> updated config ->"),t.refreshConfig);var a=((null===(r=t.context)||void 0===r?void 0:r.localization)||(null===(i=t.context)||void 0===i||null===(i=i.env)||void 0===i?void 0:i.localization)||{}).edition;ws.edition[a]&&(t.refreshConfig=vs(vs({},t.refreshConfig),ws.edition[a]),Dr("info","lifecycle","InViewRefresh ".concat(o," : EDITION OVERRIDES -> updated config ->"),t.refreshConfig)),t.onPublicEvent("ad-content-ready:".concat(o),t.handleAdContentReadyForRefresh.bind(t)),t.onPublicEvent("post-message:creativeSnippet",t.handleSnippetMessage.bind(t)),t.onPublicEvent("gpt:slotRenderEnded:".concat(o),t.handleSlotRenderEndedRefresh.bind(t)),t.onPublicEvent("gpt:impressionViewable:".concat(o),t.handleImpressionViewableForRefresh.bind(t)),t.onPublicEvent("gpt:slotVisibilityChanged:".concat(o),t.handSlotVisChangeForRefresh.bind(t))}else t.destroy();Object(fi.e)(Object(fi.f)(e.prototype),"setup",t).call(t)}))}catch(n){return Promise.reject(n)}}},{key:"isViewable",value:function(){return this.inViewPercent>=50}},{key:"refreshAd",value:function(){try{var t=this,e=t.config.wid;return ms(js((function(){if(t.isViewable())return js((function(){var n;if(null===(n=t.scrollFetchInfo)||void 0===n||!n.isFast)return ms(ma.refreshWidget(t.config,{tbr:1}),(function(){t.eventBus.trigger("ad-refresh:".concat(e)),t.refreshCount++,Dr("info","lifecycle","InViewRefresh ".concat(e," :"),"Refreshed!",t.debugInfo)}));Dr("info","lifecycle","InViewRefresh ".concat(e," :"),"Did not refresh (users scroll too fast, speed is ".concat(t.scrollFetchInfo.speed),t.debugInfo)}))})))}catch(n){return Promise.reject(n)}}},{key:"refreshEmptyAd",value:function(){var t,e=this,n=this.config.wid;this.emptyCount++,Dr("info","lifecycle","InViewRefresh ".concat(n," : ad came up empty"),vs({emptyCount:this.emptyCount},this.debugInfo)),this.eventBus.trigger("ad-refresh-reset:".concat(n)),this.timeout=setTimeout((t=function(){return ma.refreshWidget(e.config),e.timeout=null,ms()},function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}),1e4),this.emptyCount>5&&(Dr("info","lifecycle","InViewRefresh ".concat(n," : Reached max number of empty refesh attempts. Stopping."),this.debugInfo),this.destroy())}},{key:"handleAdContentReadyForRefresh",value:function(t){var e=t.type,n=this.config.wid;Dr("info","lifecycle","InViewRefresh ".concat(n," : type = ").concat(e)),"empty"===e?this.refreshEmptyAd():"programmatic"!==e?(Dr("info","lifecycle","InViewRefresh ".concat(n," : Destroy refresh b/c ad type ").concat(e," is not eligible")),this.destroy()):Dr("info","lifecycle","InViewRefresh ".concat(n," : enabled!"),vs({config:this.refreshConfig},this.debugInfo))}},{key:"handleImpressionViewableForRefresh",value:function(t){var e=this,n=this.config.wid;this.impressionViewHit=!0,this.impressionCount++;var r=this.refreshConfig.inViewSeconds,i=this.refreshConfig.maxRefreshes;this.refreshConfig.infinite&&(i=9999),this.paused?Dr("info","lifecycle","InViewRefresh ".concat(n," Impression viewed, but paused, will refresh later.")):(Dr("info","lifecycle","InViewRefresh ".concat(n," :"),"Impression Viewable",vs(vs({},this.debugInfo),{},{gptEv:t})),this.refreshCount<i&&!this.timeout?(Dr("info","lifecycle","InViewRefresh ".concat(n," :"),"Timer started... (".concat(r," seconds)")),this.timeout=setTimeout((function(){e.timeout=null,e.refreshAd()}),1e3*r)):this.refreshCount>=i&&(this.complete=!0,Dr("info","lifecycle","InViewRefresh ".concat(n," :"),"Max refreshes hit. Stopping."),this.destroy()))}},{key:"handleSlotRenderEndedRefresh",value:function(t){var e=this.config.wid;t.isEmpty?(Dr("info","lifecycle","InViewRefresh ".concat(e," : empty ad trying again"),this.debugInfo),this.isCurrentlyEmpty=!0,this.refreshEmptyAd()):this.context.element&&(Dr("info","lifecycle","InViewRefresh ".concat(e," : SlotRenderEnded -> Updating creativeId = ").concat(t.creativeId)),this.creativeId=Promise.resolve(t.creativeId),this.emptyCount=0,this.isCurrentlyEmpty=!1,this.context.element.classList.remove("ad-flexible--empty"),this.context.element.classList.add("ad-flexible--programmatic"),this.context.parentElement&&this.context.element.parentElement.classList.remove("Ad--unfilled"))}},{key:"handSlotVisChangeForRefresh",value:function(t){var e=this.config.wid,n=t.inViewPercentage;this.inViewPercent=n,Dr("info","lifecycle","InViewRefresh ".concat(e," :"),"SlotVisibilityChanged",this.debugInfo),!this.timeout&&this.isViewable()&&this.impressionCount>this.refreshCount&&!this.isCurrentlyEmpty?(Dr("info","lifecycle","InViewRefresh ".concat(e," :"),"Impression/Refresh Count mismatch due to ad moving out of view. Attempting...",this.debugInfo),this.refreshAd()):!this.timeout&&this.isCurrentlyEmpty&&this.refreshEmptyAd()}},{key:"handleSnippetMessage",value:function(t){try{var e=this,n=e.config.wid,r=t.action,i=t.data.creativeId;return ms(e.creativeId,(function(o){"".concat(o)===i&&"stopRefresh"===r?(Dr("info","lifecycle","InViewRefresh ".concat(n," -> Disabled by creative: ").concat(i),e.debugInfo),e.destroy()):"".concat(o)===i&&"pauseRefresh"===r&&(Dr("info","lifecycle","InViewRefresh ".concat(n," -> Paused by creative; waiting ").concat(t.data.seconds," seconds")),e.paused=!0,e.timeout=setTimeout((function(){e.paused=!1,e.timeout=null,Dr("info","lifecycle","InViewRefresh ".concat(n," -> Pause ended, refreshing now!")),e.refreshAd()}),1e3*(t.data.seconds||30)))}))}catch(o){return Promise.reject(o)}}},{key:"isEnabled",value:function(){return Promise.all([Object(fi.e)(Object(fi.f)(e.prototype),"isEnabled",this).call(this),this.config.refreshOptions,!this.complete])}},{key:"removeListeners",value:function(){var t=this.config.wid;this.eventBus.off("ad-content-ready:".concat(t)),this.eventBus.off("gpt:impressionViewable:".concat(t)),this.eventBus.off("gpt:slotRenderEnded:".concat(t)),this.eventBus.off("gpt:slotVisibilityChanged:".concat(t)),this.eventBus.off("post-message:creativeSnippet")}},{key:"destroy",value:function(){var t=this.config.wid;ma.clearTargeting(t,["refreshable"]),Dr("info","lifecycle","InViewRefresh ".concat(t," :"),"Destroying listeners/timers & disabling refresh."),delete this.config.refreshOptions,this.removeListeners(),Object(fi.e)(Object(fi.f)(e.prototype),"destroy",this).call(this)}},{key:"debugInfo",get:function(){return{creativeId:this.creativeId,inViewPercent:this.inViewPercent,impressionCount:this.impressionCount,refreshCount:this.refreshCount,timeout:this.timeout}}}])}(Ka);function Es(t){var e,n;return{c:function(){e=new Ic.a(!1),n=Object(Ic.n)(),e.a=n},m:function(r,i){e.m(t[1],r,i),Object(Ic.v)(r,n,i)},p:function(t,n){2&n&&e.p(t[1])},d:function(t){t&&Object(Ic.l)(n),t&&e.d()}}}function _s(t){var e,n=t[0]&&Es(t);return{c:function(){n&&n.c(),e=Object(Ic.n)()},m:function(t,r){n&&n.m(t,r),Object(Ic.v)(t,e,r)},p:function(t,r){var i=Object(a.a)(r,1)[0];t[0]?n?n.p(t,i):((n=Es(t)).c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null)},i:Ic.z,o:Ic.z,d:function(t){n&&n.d(t),t&&Object(Ic.l)(e)}}}function Ps(t,e,n){var r,i=e.isT3Env,o=void 0===i||i,a=e.config,c=void 0===a?{}:a;return t.$$set=function(t){"isT3Env"in t&&n(0,o=t.isT3Env),"config"in t&&n(2,c=t.config)},t.$$.update=function(){4&t.$$.dirty&&n(1,r='<script type="text/x-config">'.concat(JSON.stringify(c),"</")+"script>")},[o,r,c]}var Rs=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,Ps,_s,Ic.B,{isT3Env:0,config:2}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function xs(t){var e,n,r,i,o,c,s,u,l;return{c:function(){e=Object(Ic.G)("svg"),n=Object(Ic.G)("path"),r=Object(Ic.G)("path"),i=Object(Ic.G)("path"),o=Object(Ic.G)("path"),c=Object(Ic.G)("path"),s=Object(Ic.G)("path"),u=Object(Ic.G)("path"),l=Object(Ic.G)("path"),Object(Ic.f)(n,"d","M7.35058 6.29136C7.94146 6.33848 8.4378 6.57412 8.86324 7.02182C9.28867 7.46951 9.50139 8.08216 9.50139 8.85974C9.50139 9.80227 9.19413 10.5799 8.55598 11.1454C7.91783 11.7109 7.01968 11.9936 5.81428 11.9936H0V0.824707H5.79065C6.80697 0.824707 7.6342 1.10746 8.24872 1.64942C8.86324 2.21493 9.1705 2.94539 9.1705 3.86435C9.1705 4.57125 8.98141 5.1132 8.57961 5.53734C8.17781 5.96147 7.77601 6.17354 7.32694 6.22067L7.35058 6.29136ZM2.76533 5.44308H5.17613C5.57793 5.44308 5.90882 5.32527 6.16881 5.08964C6.40517 4.85401 6.54698 4.57125 6.54698 4.17067C6.54698 3.81723 6.4288 3.55803 6.19245 3.34596C5.9561 3.1339 5.64884 3.03964 5.29431 3.03964H2.76533V5.44308ZM5.31794 9.7787C5.74338 9.7787 6.09791 9.66089 6.33426 9.44882C6.57061 9.23675 6.68879 8.93043 6.68879 8.52986C6.68879 8.15285 6.57061 7.87009 6.31062 7.65802C6.07427 7.44595 5.74338 7.32814 5.36521 7.32814H2.76533V9.7787H5.31794Z"),Object(Ic.f)(n,"fill","#EE3322"),Object(Ic.f)(r,"d","M19.0737 11.9937H16.4029V11.1926C15.7411 11.8995 14.8666 12.2529 13.8267 12.2529C12.834 12.2529 12.054 11.923 11.4631 11.2868C10.8723 10.6506 10.565 9.80233 10.565 8.76555V3.62878H13.2122V8.20004C13.2122 8.69486 13.354 9.09544 13.614 9.40176C13.8739 9.70808 14.2285 9.84946 14.6539 9.84946C15.1975 9.84946 15.623 9.66095 15.9302 9.28394C16.2375 8.90693 16.3793 8.31785 16.3793 7.5167V3.62878H19.0501L19.0737 11.9937Z"),Object(Ic.f)(r,"fill","#EE3322"),Object(Ic.f)(i,"d","M27.3225 11.9937H20.2319V11.4989L23.5645 5.74947H20.3265V3.62878H27.2752V4.12361L23.9427 9.87302H27.2989V11.9937H27.3225Z"),Object(Ic.f)(i,"fill","#EE3322"),Object(Ic.f)(o,"d","M35.5475 11.9937H28.4805V11.4989L31.7894 5.74947H28.5514V3.62878H35.5002V4.12361L32.1676 9.87302H35.5238L35.5475 11.9937Z"),Object(Ic.f)(o,"fill","#EE3322"),Object(Ic.f)(c,"d","M39.5182 5.60803H44.3634V8.01147H39.5182V12.0172H36.7528V0.824707H44.7888V3.22815H39.5182V5.60803Z"),Object(Ic.f)(c,"fill","#EE3322"),Object(Ic.f)(s,"d","M63.9572 8.50627H57.812C57.9774 9.54305 58.7574 10.1321 59.9155 10.1321C60.8373 10.1321 61.7591 9.77868 62.4445 9.23673L63.4608 11.004C62.5154 11.8051 61.31 12.2528 59.9155 12.2528C57.1029 12.2528 55.1885 10.5563 55.1885 7.82294C55.1885 6.52697 55.6139 5.46662 56.4648 4.64191C57.3156 3.8172 58.3792 3.39307 59.6319 3.39307C60.8373 3.39307 61.8536 3.79364 62.6809 4.61835C63.5081 5.44306 63.9335 6.5034 63.9572 7.8465V8.50627ZM58.5447 5.89076C58.2138 6.12639 57.9774 6.45628 57.8593 6.85685H61.31C61.1918 6.43271 60.9791 6.10283 60.6719 5.8672C60.3646 5.65513 60.0101 5.53731 59.6319 5.53731C59.2301 5.53731 58.8756 5.65513 58.5447 5.89076Z"),Object(Ic.f)(s,"fill","#EE3322"),Object(Ic.f)(u,"d","M54.1721 8.50627H48.0269C48.1923 9.54305 48.9723 10.1321 50.1304 10.1321C51.0522 10.1321 51.974 9.77868 52.6594 9.23673L53.6757 11.004C52.7303 11.8051 51.5249 12.2528 50.1304 12.2528C47.3178 12.2528 45.4034 10.5563 45.4034 7.82294C45.4034 6.52697 45.8288 5.46662 46.6797 4.64191C47.5305 3.8172 48.5941 3.39307 49.8468 3.39307C51.0522 3.39307 52.0685 3.79364 52.8958 4.61835C53.723 5.44306 54.1484 6.5034 54.1721 7.8465V8.50627ZM48.7596 5.89076C48.4287 6.12639 48.1923 6.45628 48.0742 6.85685H51.5249C51.4067 6.43271 51.194 6.10283 50.8868 5.8672C50.5795 5.65513 50.225 5.53731 49.8468 5.53731C49.445 5.53731 49.0905 5.65513 48.7596 5.89076Z"),Object(Ic.f)(u,"fill","#EE3322"),Object(Ic.f)(l,"d","M74.2857 11.9936H71.615V11.3339C70.9295 11.9465 70.0787 12.2528 69.0151 12.2528C67.8806 12.2528 66.9115 11.8287 66.1079 11.004C65.3043 10.1793 64.9025 9.11894 64.9025 7.82297C64.9025 6.52699 65.3043 5.46665 66.1079 4.64194C66.9115 3.81723 67.8806 3.39309 69.0151 3.39309C70.0787 3.39309 70.9532 3.69941 71.615 4.31206V0H74.2857V11.9936ZM71.0241 9.37813C71.4259 8.97756 71.6386 8.45917 71.6386 7.82297C71.6386 7.21032 71.4259 6.69193 71.0241 6.2678C70.6223 5.86722 70.1259 5.65516 69.5823 5.65516C68.9914 5.65516 68.5187 5.86722 68.1406 6.2678C67.7624 6.66837 67.5733 7.18676 67.5733 7.82297C67.5733 8.45917 67.7624 9.00112 68.1406 9.4017C68.5187 9.80227 68.9914 9.99078 69.5823 9.99078C70.1259 9.99078 70.5986 9.77871 71.0241 9.37813Z"),Object(Ic.f)(l,"fill","#EE3322"),Object(Ic.f)(e,"title",t[0]),Object(Ic.f)(e,"extraclasses",t[1]),Object(Ic.f)(e,"width","75"),Object(Ic.f)(e,"height","13"),Object(Ic.f)(e,"viewBox","0 0 75 13"),Object(Ic.f)(e,"fill","none")},m:function(t,a){Object(Ic.v)(t,e,a),Object(Ic.c)(e,n),Object(Ic.c)(e,r),Object(Ic.c)(e,i),Object(Ic.c)(e,o),Object(Ic.c)(e,c),Object(Ic.c)(e,s),Object(Ic.c)(e,u),Object(Ic.c)(e,l)},p:function(t,n){var r=Object(a.a)(n,1)[0];1&r&&Object(Ic.f)(e,"title",t[0]),2&r&&Object(Ic.f)(e,"extraclasses",t[1])},i:Ic.z,o:Ic.z,d:function(t){t&&Object(Ic.l)(e)}}}function Ts(t,e,n){var r=e.title,i=e.extraClasses;return t.$$set=function(t){"title"in t&&n(0,r=t.title),"extraClasses"in t&&n(1,i=t.extraClasses)},[r,i]}var ks=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,Ts,xs,Ic.B,{title:0,extraClasses:1}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function Cs(t){var e,n,r,i,o,a;return{c:function(){e=Object(Ic.G)("svg"),n=Object(Ic.G)("title"),r=Object(Ic.H)("Tasty Logo"),i=Object(Ic.G)("g"),o=Object(Ic.G)("path"),a=Object(Ic.G)("path"),Object(Ic.f)(o,"fill","#fefefe"),Object(Ic.f)(o,"d","M113.9 40.23l-1.44.66-.46.2c-.64.3-1.3.6-1.97.97-.75.42-1.34.88-1.86 1.3-.15.1-.3.2-.45.33-.77.58-2 1.65-2.73 2.45-.03 0-.04.03-.06.05.02-.4.03-.83.02-1.3l-.08-4.83c0-.45-.02-.95-.04-1.42s-.04-.96-.04-1.35c0-.33.02-.74.04-1.18.02-.5.05-1.02.05-1.54v-1.1l.02-1.26c0-.84-.05-3.7-.07-4.53 0-.5-.04-1.13-.06-1.72l-.04-1.06-.07-1.36c-.03-.36-.05-.7-.06-1.07l-.12-2c-.04-.7-.1-1.4-.1-1.77 0-.32-.04-.88-.08-1.53l-.1-1.54v-.34l.7-.08.76-.07c.2 0 .37 0 .55-.02.78 0 1.85-.02 2.94-.5-.22 1.3-.52 3.3-.6 3.93-.1.68-.1 1.3-.08 1.8v.72l-.1.8c-.08.56-.15 1.14-.2 1.72l-.12.9c-.07.5-.15 1.04-.2 1.62-.03.64-.16 4.34-.06 5.42.13 1.46.5 2.76 1.06 3.76.44.8 1 1.7 1.7 2.5.8.87 1.54 1.38 2.4 1.86.28.16.62.34 1 .5l-.1.05zM90.08 21.6c0 .4-.05.88-.1 1.35l-.1 1.26c-.02.4-.13 1.63-.22 2.62l-.17 2.02-.08 1.3-.08 1.2c-.03.5-.04 1-.04 1.42 0 .35 0 .7-.04 1l-.1 1.1c-.08.53-.15 1.08-.18 1.6l-.25 2.85c-.2 2.33-.33 3.77-.35 4.27-.02.48 0 1.33 0 2.66 0 .8.03 1.8 0 2.1 0 .43 0 .9-.02 1.38 0 .43-.02.84-.03 1.22-.02.38-.1 1.56-.18 2.5l-.16 2.35c0 .1 0 .25-.02.43-.05-.4-.1-.82-.17-1.2v-.07c-.13-.85-.4-1.54-.63-2.1-.08-.2-.18-.44-.2-.56-.2-.82-.44-1.66-.72-2.58-.23-.74-.48-1.43-.73-2.1l-.15-.38c-.23-.62-.47-1.2-.7-1.78-.1-.2-.18-.4-.26-.6-.22-.5-1.57-3.76-1.97-4.65l-1.05-2.26-1.06-2.23c-.07-.14-.16-.42-.24-.7-.15-.45-.32-1-.6-1.6l-.48-1-.57-1.2c-.2-.43-.42-.84-.6-1.2-.2-.33-.36-.65-.5-.94l-.4-1c-.13-.38-.3-.8-.47-1.24-.15-.33-.3-.7-.44-1.1.02 0 .03.02.04.03.72.47 1.6 1.05 2.8 1.14h.44c2.03 0 3.4-1.03 3.84-1.37 1.13-.86 1.95-2.04 2.37-3.42l.08-.2c.23-.74.55-1.75.55-2.9v-1.15l.02-1.23c0-.38-.02-.75-.05-1.1.6.17 1.26.26 1.98.26.44 0 .9-.03 1.48-.08l.5-.05c0 .25 0 .53.02.83 0 .16 0 .34.02.5.02.65 0 3.87-.03 4.63zm59.07 10l-.42-2.36c-.46-1.26-1.08-2.3-1.87-3.14-1.15-1.23-2.53-1.85-4.1-1.85-.26 0-.52.02-.8.05-1.44.18-2.62.83-3.5 1.37l-.47.3c-.52.3-1.18.68-1.84 1.23l-.4.32c-.32.26-.78.73-1.1 1 .03-.17.2-.55.24-.74l.4-1.88.16-.73c.13-.63.23-1.2.3-1.64l.16-.85c.06-.23.1-.5.17-.8l.32-1.46.3-1.43.2-1c.2-.77.36-1.62.53-2.52.15-.76.23-1.52.3-2.18l.05-.48c.12-.97.16-1.93.15-2.85v-.52c0-.9 0-2.03-.46-3.15-.8-1.94-2.5-3.3-4.6-3.6-.83-.13-1.5-.2-2.1-.2-.57 0-1.1.06-1.58.17-1.87.4-2.97 1.64-3.55 2.3l-.17.18-.23.22c-.15.14-.34.3-.53.52-.12-.68-.4-1.32-.9-1.9-1.05-1.16-2.65-1.9-4.5-2.1-.2-.02-.42-.03-.63-.03-1.17 0-2.17.33-2.9.57l-.3.1-.15.04c-.83.26-2.08.65-3.07 1.62-.25-.5-.54-.95-.87-1.33-1.35-1.56-3.54-1.9-4.73-1.98-.17-.03-.35-.03-.54-.03-.64 0-1.2.06-1.66.1l-.43.06c-.32 0-.7-.04-1.12-.08-.5-.05-1.06-.1-1.65-.12-.93 0-1.85-.03-2.77-.04-.12-.02-.24-.02-.36-.02-.4 0-.8 0-1.18.02l-1 .02h-.4c-.6 0-1.12.05-1.6.1-.36.03-.7.06-1 .06h-.03l-.66-.05C91.8.9 91.23.83 90.6.83h-.47c-.93 0-1.78.06-2.54.2-.78.13-3.14.54-4.38 2.76l-.08.13c-.14.23-.32.53-.5.9-.75-.76-1.56-1.38-2.44-1.85-.74-.4-1.48-.62-2.06-.8-.24-.06-.47-.12-.65-.2-.44-.15-4.4-1.45-5.76-1.63-.5-.07-1-.1-1.47-.1-.9 0-1.8.12-2.6.35-1.6.46-2.7 1.44-3.56 2.23v.02c-.1.1-.2.17-.3.25-.55.47-1.3 1.12-2 2.32-.5.88-.88 1.8-1.2 2.6l-.1.18c-.32.82-1.55 4.38-1.8 5.53-.17.87-.35 1.8-.44 2.8-.1 1.05-.02 4.55.1 5.56.1.75.26 1.55.4 2.25l.15.6c.23 1.08 1.15 3.82 1.72 5.22.2.5.43.93.65 1.35.2.36.37.7.5 1.03l.35.96c.16.5.34 1.04.57 1.6.2.5.75 1.7 1.28 2.87l.86 1.88c.24.57.5 1.1.73 1.55.16.3.3.6.42.86.2.47.58 1.27 1 2.1-.4-.06-.83-.1-1.24-.1-1 0-1.8.2-2.4.35l-.25.07c-1.14.27-2.82.8-4.1 2.33l-.33.4-.15-.77c-.04-.3-.07-.66-.1-1.03-.04-.44-.08-.9-.15-1.37l-.26-1.6c-.15-1-.42-2.67-.46-3.1-.07-.7-.14-1.46-.2-2.4-.04-.3-.1-.92-.15-1.64l-.28-3.3-.1-1.72c-.1-1.6-.18-2.76-.23-3.28l-.07-.97c-.02-.47-.05-1-.1-1.57-.1-.87-.23-1.72-.35-2.55-.1-.6-.2-1.15-.32-1.65l-.18-.9c-.16-.9-.88-4.2-1.14-5.13-.26-.95-1.5-4.44-2.03-5.45-.5-.97-1.1-2.18-2.24-3.13-1.22-1.02-2.67-1.52-4.45-1.52h-.16c-1.25.02-2.5.34-3.83.98-1.02.5-2.33 1.25-3.35 2.65-.68.9-1 1.85-1.24 2.6l-.2.62-.35.76c-.27.58-.57 1.23-.8 2-.27.87-.53 1.68-.78 2.38l-.16.43c-.2.6-.5 1.4-.77 2.37-.13.5-.24 1.05-.35 1.56l-.25 1.12c-.2.82-.37 1.53-.57 2.45l-.4 1.84-.2.95-.37 1.95-.27 1.45c-.2.95-.72 3.8-.85 4.52l-.24 1.17c-.1.5-.22 1-.3 1.5-.13.76-.3 2.26-.45 3.6-.1.75-.17 1.48-.22 1.78l-.12.92c-.08.63-.17 1.28-.26 1.82l-.2 1.24c-.05.42-.1.8-.18 1.23-.16.87-.77 4.5-.9 5.45-.06.37-.3 1.58-.5 2.47-.24 1.15-.4 1.88-.45 2.3-.1.54-.2 1.26-.34 2l-.3 1.54c-.02-.6-.06-1.16-.1-1.68l-.38-3.32-.17-1.45c-.12-1.08-.38-4.04-.4-4.6 0-.77.08-6.57.1-7.36l.13-2.57c.13-2.57.2-4.42.22-4.96 0-.27 0-.84.02-1.5 0-1.28.03-3.02.05-3.55 0-.55 0-1.06-.03-1.5 0-.4-.03-.75 0-1.05 0-.5.07-1.88.13-3.1.05-.86.1-1.65.1-2 .03-.52.02-1 .02-1.47v-1.1c.03-.6 0-1.33-.03-2.2 0-.05 0-.12-.02-.2l2.13-.12 1.3-.06.4-.04c.9-.07 1.8-.15 2.68-.48 1.28-.5 2.9-1.73 3.42-4.88.18-1.17.18-2.37-.03-3.6-.27-2.3-1.32-3.7-2.16-4.45-.98-.9-2.24-1.48-3.96-1.84-.94-.2-2.05-.42-3.26-.42h-.08c-.8 0-1.56.12-2.23.22-.24.04-.5.08-.74.1-.74.1-4.5.6-5.5.84l-1.6.38c-1.13.27-2.86.7-3.48.8-.63.1-1.2.27-1.72.42-.38.1-.73.22-1.02.27-.68.12-4.85.98-5.9 1.33C2.06 7.52.63 9.37.3 11.87c-.15 1.24-.26 3.1.55 4.9 1.1 2.44 3.58 2.84 4.63 3l.3.05c.6.1 1.35.23 2.2.23.24 0 .46 0 .67-.02.27-.02.6-.03.93-.03h.34c.03 1.3.04 3.78.03 4.2-.02.54-.02 1.8-.02 3v1.97c0 .35-.04.82-.07 1.28-.04.56-.08 1.13-.1 1.67v5.24l-.07 2.58-.03.43L9.55 45c-.02.54-.02 1.85-.03 3.13v1.93l-.04 2.24c-.02.78-.12 3.97-.16 4.72l-.03.66-.1 1.65-.1.53c-.1.5-.2 1.2-.27 1.97-.05.7-.02 1.38 0 1.97l.03.78c0 1.63.4 2.93.76 3.73 1.3 3.02 3.6 3.34 4.54 3.34.43 0 .86-.06 1.3-.18l.37-.1c1.48-.43 2.25-.65 2.8-.94l.18-.08h.46l.44.02c.22.02.42.02.6.02 3.16 0 4.3-2.37 4.93-3.64.13-.26.3-.6.47-1.03-.04.68 0 1.33.17 1.95.5 2 1.25 2.86 1.97 3.58 1.58 1.6 4.1 1.7 4.84 1.7.32 0 .62 0 .9-.05 1.45-.22 6.32-1.14 8.2-3.92.9-1.36 1.18-4.25 1.25-6.44v-.13h.07c.5.03 1.2.08 1.98.1-.02 1.18-.06 4.42-.04 5.18v.45c-.02.73-.04 1.74.33 2.8.54 1.54 1.57 2.76 3 3.53 1.36.73 3.05.8 3.7.8.37 0 .72 0 1.07-.04 1.7-.2 3.42-.87 4.47-1.76 1.37-1.17 1.9-2.4 2.26-3.53.24-.76.47-2.57.6-3.98.4.76.82 1.44 1.12 1.82.64.84 1.3 1.58 2.04 2.26.82.77 1.72 1.35 2.44 1.82.78.5 4.3 2.17 6 2.4.58.06 1.27.13 2.03.13.47 0 .92-.03 1.37-.1 1.25-.15 2.36-.55 3.3-.92 1.22-.5 2.2-1.2 2.94-1.75 1.1-.82 3.55-3.85 4.17-5.13.1-.17.18-.35.27-.52.35-.7.75-1.46 1-2.36.1-.33.27-1.08.44-1.96.04.65.14 1.22.23 1.7.14.76.37 2.03 1.23 3.35.56.85 1.93 2.3 4.84 2.3.34 0 .7-.03 1.04-.07.98-.1 1.87-.37 2.65-.6l.5-.14c.5-.15.93-.3 1.3-.43l.64-.2c1.7-.1 2.9-.68 3.7-1.37l.15.3c.5 1 1.1 2.04 1.68 2.84.66.94 1.38 1.62 2.02 2.22l.15.15c.82.78 1.72 1.58 2.78 2.17 1.68.9 4.74 1.94 6.2 2.06.4.04.85.06 1.33.06 1.64 0 4.02-.24 5.2-.65 2.5-.86 4.33-2.77 5.12-3.6l.07-.07c.45-.47 1.33-1.4 2.03-2.33.8-1.06 2.42-4.28 2.7-4.93.5-1.1.8-2.28 1.05-3.28.27-1.1.45-2.13.55-3.17.1-.9.13-1.84.16-2.97 0-.55 0-1.1-.03-1.6l-.03-1.2c0-.24 0-.48.02-.73.02-.63.05-1.33 0-2.1-.04-.43-.1-.84-.14-1.2-.05-.4-.1-.77-.1-1 .02-.42 0-.87-.02-1.3.02 0 .03 0 .05-.02l.67-.48c.4-.3 2.28-1.36 3.2-1.87.6-.34 1.06-.6 1.33-.78.9-.53 1.6-1.13 2.24-1.65l.37-.3c.13-.12.35-.27.57-.44.42-.3.95-.68 1.48-1.14.26-.23 3.45-3.28 4.2-4.87.2-.48.35-.95.43-1.4V31.6z"),Object(Ic.f)(a,"fill","#333"),Object(Ic.f)(a,"d","M32.46 9.64c-.1-1-.43-1.82-1.04-2.37-.6-.54-1.47-.84-2.4-1.04-.8-.17-1.67-.34-2.6-.34-.83 0-1.7.2-2.6.3-.86.1-4.37.6-5.14.77-1.06.24-4.17 1.02-5.25 1.2-.87.15-1.77.53-2.72.7-.93.16-4.72.98-5.4 1.2-.94.32-1.43 1.08-1.58 2.24-.12.95-.18 2.06.26 3.03.33.73 1.16.9 2.04 1.04.72.1 1.52.3 2.36.22.8-.08 1.66-.02 2.38-.1 1.14-.1 2.22-.34 2.37-.08.15.25.13 1.53.2 2.65.05.84.07 4.64.05 5.22-.02.86 0 4.05 0 4.93-.02.87-.16 2.06-.17 2.94v5.25c-.02.88-.06 2.18-.1 3.05 0 .88-.1 3.82-.1 4.63-.03.8-.04 4.03-.05 4.84l-.03 2.42c-.02.8-.12 4.04-.16 4.84-.05.8-.07 1.62-.14 2.42-.04.55-.28 1.45-.36 2.46-.05.8.04 1.7.04 2.5 0 1 .25 1.84.48 2.37.35.83.9 1.42 1.74 1.2.82-.25 2.2-.62 2.55-.8 1.04-.53 1.4-.46 2.24-.45 1.55.02 1.86.35 2.84-1.68.32-.64.67-1.4.72-2.26.05-.75 0-1.58-.06-2.4-.07-.78-.47-4-.54-4.68-.1-.83-.4-4.1-.4-4.94-.02-.83.06-6.7.1-7.52.02-.83.3-6.6.32-7.47.02-.85.04-4.27.07-5.12.03-.85-.06-1.7-.03-2.57.03-.85.2-4.26.25-5.12.03-.85-.02-1.7.02-2.55.04-1.17-.2-3.9-.03-4.98.08-.5 1.83-.45 2.16-.57.5-.2 3.64-.3 4.34-.35.86-.08 1.68-.12 2.2-.3.7-.28 1.06-1.18 1.23-2.22.12-.73.14-1.6-.04-2.54M57.14 64.2c.04-.84-.23-1.66-.26-2.47-.04-.84 0-1.67-.08-2.46-.1-.86-.35-4.2-.48-4.92-.18-.98-.65-4.03-.75-4.83-.1-.8-.33-1.6-.45-2.4-.12-.8-.14-1.6-.25-2.4-.12-.8-.68-4.02-.75-4.85l-.22-2.47c-.07-.82-.35-4.1-.4-4.93-.07-.82-.25-4.12-.33-4.94-.08-.82-.08-1.65-.17-2.47-.1-.82-.2-1.64-.33-2.46-.12-.82-.33-1.62-.48-2.43-.15-.8-.85-4.03-1.07-4.83-.23-.83-1.4-4.06-1.77-4.78-.43-.82-.82-1.58-1.42-2.08-.65-.55-1.4-.72-2.34-.7-.72 0-1.47.2-2.4.63-.8.4-1.55.87-2.1 1.6-.5.7-.68 1.6-1.02 2.46-.34.82-.78 1.62-1.04 2.47-.27.9-.55 1.76-.83 2.55-.2.6-.56 1.46-.84 2.5-.2.78-.36 1.66-.58 2.6-.2.8-.37 1.5-.57 2.4l-.57 2.76c-.2.87-.46 2.5-.63 3.37-.18.9-.7 3.63-.84 4.46-.17.95-.4 1.8-.53 2.6-.18 1.12-.52 4.4-.64 5.3-.14.9-.26 1.95-.4 2.84-.16.9-.24 1.63-.4 2.5-.16.9-.75 4.46-.88 5.34-.13.9-.85 4.07-.95 4.78-.1.72-.22 1.42-.36 2.13-.12.7-.27 1.4-.42 2.13-.18.9-.63 2.67-.4 3.6.33 1.24.68 1.6 1.1 2 .58.6 2.13.8 2.8.7 1.34-.22 4.9-1.07 5.84-2.45.43-.65.65-3.86.68-4.62.02-.82.13-3.1.28-3.32.2-.26 1.23-.18 2.88-.17.36.02 1.5.15 2.65.14 1.18 0 2.92-.03 3.02.13.14.25.12 2.05.1 3.22-.02 1.18-.06 4.52-.05 5.24.02.77-.07 1.52.15 2.16.24.68.67 1.24 1.38 1.62.53.3 1.66.5 2.76.37 1.08-.13 2.14-.57 2.6-.97.77-.65 1-1.25 1.22-1.95.2-.6.54-3.74.56-4.68m-9.4-11.98c-.77.2-3.8.1-4.6.02-1.12-.1-2.35.05-2.47-.27-.14-.38.26-2.02.3-3 .03-.9.22-3.16.37-4.37.1-.76.1-1.64.22-2.58.1-.8.4-4.3.54-5.16.12-.86.35-1.7.5-2.55.12-.86.26-1.72.4-2.55.15-.9.8-4.25 1-4.98.33-1.38.76-2.8 1.04-2.77.23.02.44 1.43.6 2.96l.68 8.14.4 5.28c.07.87.3 1.75.35 2.63.07.9.2 1.76.26 2.6.07.97.16 1.78.2 2.56.08 1.23.6 3.96.2 4.06m23.7-28.57c-.23-.62-.67-1.76-.8-2.83-.07-.8-.03-3.4.23-4.27.25-.84.83-2.54 1.33-3.28.47-.7.97-1.24 1.73-1.76.87-.58 1.8-.16 2.2.57.53 1 .18 3.83 0 4.67-.17.8-.7 1.55-.78 2.32-.08.87.34 1.62.7 2.3.18.3.7.77 1.38 1.17.64.38 1.26.88 1.67.9.85.08 1.46-.27 1.96-.66.55-.4.95-.97 1.17-1.7.2-.64.46-1.37.46-2.08V16.6c0-.8-.1-1.6-.15-2.37-.05-.8 0-1.62-.14-2.38-.12-.8-.38-1.57-.65-2.3-.32-.84-.8-1.5-1.3-2.03-.55-.6-1.17-1.14-1.87-1.52-.7-.37-1.5-.5-2.27-.8-.73-.26-4.17-1.34-5-1.45-.94-.12-1.86-.1-2.7.15-.83.24-1.5.84-2.2 1.5-.6.53-1.12.87-1.63 1.74-.42.73-.74 1.54-1.07 2.35-.33.8-1.45 4.12-1.62 4.94-.17.8-.32 1.6-.4 2.4-.07.8 0 4.05.1 4.85.1.78.33 1.76.5 2.55.15.8.98 3.27 1.53 4.6.3.72.78 1.48 1.15 2.37.32.77.56 1.7.9 2.53.35.8 1.8 3.95 2.14 4.72.37.87.8 1.65 1.12 2.36.48 1.06 2 4.12 2.43 4.96.46.86.75 1.8 1.12 2.68.38.9.7 1.8.93 2.75.14.6.96 3.75 1.03 4.53.07.8-.1 3.4-.86 4.48-.56.83-.93 1.2-1.86 1.13-1.13-.1-1.83-.94-2.15-1.86-.4-1.12-.25-2.48-.25-2.9 0-.6.13-1.5.1-2.5 0-.8-.23-1.64-.38-2.46-.16-.86-.43-1.63-.85-2.3-.44-.7-.97-1.34-1.85-1.54-1-.23-1.85.05-2.63.23-.94.22-1.7.53-2.27 1.2-.56.66-1.07 1.45-1.2 2.4 0 0 .12 4.05.24 4.97.1.77.4 1.55.6 2.4.18.78.45 1.55.75 2.35.28.75 1.78 3.66 2.3 4.33.48.64 1 1.24 1.63 1.82.6.54 1.26 1 1.97 1.45.67.43 3.66 1.75 4.57 1.86.8.1 1.65.18 2.53.07.83-.1 1.62-.38 2.44-.72.77-.3 1.45-.75 2.16-1.3.65-.5 2.75-3.07 3.15-3.9.37-.75.84-1.55 1.07-2.32.22-.8.8-3.92.77-4.74-.02-.82-.14-1.65-.26-2.48-.1-.8-.58-1.53-.77-2.35-.2-.77-.42-1.55-.66-2.35-.24-.76-.53-1.52-.82-2.3-.27-.76-.6-1.5-.9-2.27-.32-.75-1.57-3.76-1.92-4.52l-1.03-2.22-1.05-2.2c-.35-.74-.5-1.57-.85-2.32-.34-.73-.7-1.46-1.06-2.2-.33-.75-.76-1.45-1.1-2.2-.33-.74-.57-1.53-.9-2.3-.32-.73-.58-1.5-.9-2.27-.3-.75-.67-1.48-.96-2.25M109.2 9.5c.23-.54.5-1.4.44-2.22-.07-.85-.44-1.7-.82-2.14-.4-.46-1.4-.75-2.36-.8-.92-.07-1.86.15-2.43.14-.94 0-1.9-.18-2.8-.2-.94 0-1.86-.03-2.8-.04-.9-.02-1.84.05-2.77.03-.9 0-1.83.17-2.78.16-.54 0-1.42-.13-2.36-.15-.78-.02-1.6 0-2.34.14-.9.16-1.6.43-1.95 1.05-.23.4-.76 1.15-.9 2.07-.1.7.16 1.55.25 2.28.1.8.3 1.47.77 2 .5.58 1.66.56 2.87.44 1.14-.1 3.5-.36 3.84-.14.26.16.13 1.18.32 2.3.12.7.12 1.52.15 2.46.03.74.02 4.04-.02 4.9-.03.78-.14 1.8-.2 2.65-.04.8-.33 3.84-.4 4.67-.05.8-.08 1.67-.14 2.5-.07.8-.03 1.58-.08 2.4-.06.8-.24 1.87-.3 2.7-.04.8-.56 6.2-.6 7.02-.03.82.05 3.93.02 4.72l-.07 2.6c-.03.84-.3 4.1-.33 4.87-.04.87-.32 3.98-.38 4.6-.08.8.05 1.52.17 2.22.1.63.22 1.35.7 2.08.44.67 1.54.82 2.63.7.84-.1 1.73-.4 2.6-.64 1.1-.3 2.03-.7 2.55-.73 1.22-.04 1.92-.47 2.27-1.14.34-.67.45-1.54.4-2.6-.06-.8-.28-1.7-.35-2.64l-.17-2.55-.12-2.54c-.03-.86-.1-1.7-.14-2.56l-.08-2.55c-.03-.85.06-1.7.05-2.56-.02-.85-.1-4.04-.1-4.9 0-.84-.06-1.92-.07-2.8 0-.82.1-1.85.1-2.72l.02-2.36c0-.8-.05-3.66-.07-4.45 0-.8-.06-1.94-.08-2.74-.03-.8-.1-1.6-.12-2.42-.03-.78-.2-2.93-.22-3.74l-.16-3.06c-.04-.76-.26-2.92-.26-3.5 0-.48 1.2-.33 2.6-.36.85-.03 1.65-.18 2.36-.22.9-.05 1.73.05 2.35-.26.65-.32 1.05-1.03 1.33-1.95m36.4 20.94c-.35-.92-.74-1.52-1.16-1.97-.63-.68-1.2-.84-1.97-.74-.68.08-1.33.4-2.08.87-.63.4-1.35.74-2 1.27l-1.8 1.5c-.62.53-1.12 1.2-1.72 1.65-.7.53-1.44.88-2.08 1.16-1.1.48-2.27 1.16-2.27 1.16-.3-.2.02-2.34.13-3.06.1-.62.23-1.68.43-2.76.15-.82.33-1.53.5-2.43l.57-2.65c.22-1.07.34-1.9.48-2.53.13-.6.27-1.42.5-2.35.18-.72.32-1.53.5-2.36.2-.76.35-1.56.5-2.36.15-.8.22-1.63.3-2.4.1-.86.15-1.65.13-2.4-.02-.9.06-1.8-.2-2.44-.33-.82-1.06-1.36-1.92-1.5-.94-.13-1.78-.2-2.45-.06-.87.2-1.38.9-1.9 1.45-.52.56-1.04.82-1.3 1.63-.23.74-.1 1.28-.2 2.1-.1.8-.24 1.58-.28 2.36-.05.88-.17 2.66-.26 3.48-.08.8-.5 3.02-.65 3.82-.13.8-.25 3.24-.44 4.35-.12.64-.2 3.04-.83 3.88-.54.74-1.46 1.1-2.55 1.13-1.04.05-1.8-.5-2.54-1.06-.57-.43-.58-2.2-.63-3.4-.02-.86.18-1.8.3-2.8.1-.94.1-1.92.27-2.78.22-1.2.34-2.23.43-2.77.1-.52.7-3.68.88-4.46.16-.74.52-1.46.66-2.18.16-.8.23-1.63.33-2.3.18-1.16.27-2 .1-2.2-.48-.52-1.35-.88-2.3-.98-.83-.1-1.67.24-2.45.5-.87.27-1.77.5-2.13 1.22-.16.3-.5 1.13-.77 2.34-.15.7-.75 3.96-.9 4.8-.12.78-.63 4.02-.74 4.84-.1.8.02 1.65-.08 2.45-.1.8-.2 1.64-.28 2.43-.07.84-.25 1.63-.3 2.42-.05.85-.15 4.18-.08 4.9.1 1.04.35 1.87.63 2.38.37.67.78 1.35 1.24 1.85.5.56.93.85 1.54 1.2.63.35 1.6.75 2.33.88l2.33.42c.8.13 1.5.14 2.3.27 1.35.22 3.54-.1 3.54-.12.02.13-1.6 1.23-2.75 1.83-.64.33-1.36.7-2.1 1.04-.72.36-1.46.7-2.1 1-1.04.48-1.85.9-2.1 1.02-.74.33-1.48.66-2.2 1-.75.34-1.52.68-2.22 1.07-.72.38-1.28.88-1.93 1.37-.64.48-1.7 1.4-2.25 2-.58.64-1.8 3.14-2.02 4-.22.82-.38 1.67-.45 2.54-.06.86-.27 1.75-.2 2.62.06.86.24 1.73.4 2.58.2.85 1.28 4 1.67 4.76.4.78.92 1.7 1.4 2.38.52.72 1.14 1.3 1.75 1.87.64.62 1.32 1.23 2.06 1.64 1.37.75 3.98 1.58 4.84 1.65 1.7.14 4.35-.15 5.1-.4 1.8-.63 3.28-2.23 3.85-2.82.57-.6 1.26-1.36 1.76-2 .5-.67 1.98-3.52 2.3-4.25.38-.87.64-1.85.85-2.7.22-.87.38-1.75.47-2.7.08-.84.12-1.74.15-2.72.02-.83-.06-1.74-.05-2.72 0-.83.08-1.7.02-2.56-.06-.86-.25-1.72-.23-2.56 0-.9-.18-2.34-.1-2.66.13-.46 1.43-1.25 2.17-1.8.75-.56 4.03-2.37 4.8-2.84.83-.5 1.52-1.1 2.23-1.68.5-.4 1.27-.9 1.98-1.5.6-.53 3-3.02 3.3-3.7.4-.8.17-1.65-.1-2.44m-20.3 25.9c-.1 1.05-.2 1.92-.43 2.68-.25.82-.6 1.6-1.05 2.52-.4.8-.96 1.5-1.55 1.95-.65.5-1.5.66-2.34.75-.73.07-1.53-.05-2.4-.3-.83-.27-1.63-.56-2.12-1.08-.54-.6-.8-1.42-.96-2.2-.15-.74-.34-1.57-.14-2.37.42-1.6 1.65-3.85 2.2-4.42.38-.42 1.04-1.14 1.78-1.72.6-.45 1.12-.87 1.78-1.32.7-.45 1.43-.9 2.06-1.24.9-.5 2.95-2.03 3.1-1.6.32 1-.1 1.16.07 3.6.05.63.05 1.33 0 2.38-.03.72.12 1.4.02 2.35"),Object(Ic.f)(i,"fill","none"),Object(Ic.f)(i,"fillrule","evenodd"),Object(Ic.f)(e,"width","75"),Object(Ic.f)(e,"height","38"),Object(Ic.f)(e,"fill","none"),Object(Ic.f)(e,"viewBox","0 0 150 76")},m:function(t,c){Object(Ic.v)(t,e,c),Object(Ic.c)(e,n),Object(Ic.c)(n,r),Object(Ic.c)(e,i),Object(Ic.c)(i,o),Object(Ic.c)(i,a)},p:Ic.z,i:Ic.z,o:Ic.z,d:function(t){t&&Object(Ic.l)(e)}}}var Ls=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,null,Cs,Ic.B,{}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function Is(t){var e,n,r,i,o,c;return{c:function(){e=Object(Ic.G)("svg"),n=Object(Ic.G)("title"),r=Object(Ic.H)("BuzzFeed News"),i=Object(Ic.G)("g"),o=Object(Ic.G)("path"),c=Object(Ic.G)("path"),Object(Ic.f)(o,"d","M1 15.22A1.31 1.31 0 0 0 1.5 14V3A1.31 1.31 0 0 0 1 1.74L0 1V.68h7.08c4.18 0 5.4 1.54 5.4 3.76v.1c0 1.78-1.36 3.2-4.62 3.54 3.86.14 5.42 1.6 5.42 4v.12c0 2.84-2.08 4.08-6 4.08H0V16zm4-7.34h1.68c1.62 0 2.22-.54 2.22-3.1v-.3c0-2.42-.52-3-2.22-3H5zm4.48 4.46v-.42c0-2.48-.72-3.24-2.82-3.24H5v6c0 .56.2.8 1.1.8h.6c2.06 0 2.82-.6 2.82-3.14zm4.92-5.22a1.31 1.31 0 0 0-.52-1.22l-.66-.52v-.3h4.46v7.8c0 .92.36 1.4 1.36 1.4a3.72 3.72 0 0 0 1.46-.34V7.36a1.31 1.31 0 0 0-.5-1.22l-1-.76v-.3h4.76v9.16a1.4 1.4 0 0 0 .52 1.22l.56.52v.3H21l-.26-1.82a4.09 4.09 0 0 1-3.44 2c-1.84 0-2.9-1.28-2.9-3.32zm17-1.24h-1.63c-.8 0-1 .08-1.46.84L26 10.22h-.3V5.08h8.92v1.6l-5.72 8.8h1.52c.8 0 1-.08 1.46-.84l2.4-3.7h.3v5.34h-8.89v-1.6zm10.38 0h-1.63c-.8 0-1 .08-1.46.84l-2.28 3.5h-.3V5.08H45v1.6l-5.72 8.8h1.52c.8 0 1-.08 1.46-.84l2.4-3.7H45v5.34h-8.93v-1.6zm5.16 9.34a1.31 1.31 0 0 0 .53-1.22V3a1.31 1.31 0 0 0-.52-1.22L46 1V.68h12v4.84h-.3L55.31 2.4a1.77 1.77 0 0 0-1.78-.92H51V8h1.48c.38 0 .6-.08 1-.56l2.08-2.7h.3V12h-.3l-2.08-2.7c-.36-.48-.58-.56-1-.56H51V14a1 1 0 0 0 .66 1l1.94 1v.3H46V16zm15.28 1.26c-2.88 0-4.92-2.24-4.92-5.62v-.06c0-3.38 2.44-5.92 5-5.92a3.61 3.61 0 0 1 4 3.9V10h-5.7c.2 3.06 1.6 4.3 3.4 4.3a4.62 4.62 0 0 0 2.22-.66h.3v.88a5.59 5.59 0 0 1-4.29 1.96zm-1.7-7.26h2.94V7.84c0-1.68-.32-2.2-1.22-2.2-1.1 0-1.72 1-1.72 3.2zm11.34 7.26C69 16.48 67 14.24 67 10.86v-.06c0-3.38 2.44-5.92 5-5.92a3.61 3.61 0 0 1 4 3.9V10h-5.7c.2 3.06 1.6 4.3 3.4 4.3a4.62 4.62 0 0 0 2.22-.66h.3v.88a5.59 5.59 0 0 1-4.34 1.96zm-1.7-7.26h2.94V7.84c0-1.68-.32-2.2-1.22-2.2-1.1 0-1.72 1-1.72 3.2zm6.42 1.42C76.6 6.9 79 4.88 82 4.88a6.25 6.25 0 0 1 2.06.4v-2c0-.58-.1-.72-.48-.94L82 1.4v-.3L87 0h.3v14.24a1.27 1.27 0 0 0 .56 1.18l.72.56v.3h-4l-.26-1.82a4.07 4.07 0 0 1-3.52 2c-2.48 0-4.18-2.12-4.18-5.74zm5.86 3.64a3.5 3.5 0 0 0 1.6-.34V9.12c0-2.6-.86-3.44-2.06-3.44s-2.12.92-2.12 3.66v.3c.02 3.48 1.14 4.64 2.6 4.64zM92 1.66l-.52-.48v-.3h4.84L103 11.56V6.22a3.5 3.5 0 0 0-.64-2.06l-1.84-3V.88h4.88v.3l-1.24 3.2a4.13 4.13 0 0 0-.36 1.84v10.06h-2.2L94.1 4.22A11.25 11.25 0 0 0 92 1.66zm18 14.82c-2.88 0-4.92-2.24-4.92-5.62v-.06c0-3.38 2.44-5.92 5-5.92a3.61 3.61 0 0 1 4 3.9V10h-5.7c.2 3.06 1.6 4.3 3.4 4.3a4.62 4.62 0 0 0 2.22-.66h.3v.88a5.59 5.59 0 0 1-4.3 1.96zm-1.7-7.26h2.94V7.84c0-1.68-.32-2.2-1.22-2.2-1.1 0-1.72 1-1.72 3.2zm5.56-3.68l-.16-.16v-.3h5.26v.3l-.42.4a1 1 0 0 0-.32 1.1l1.62 5.86 1.36-5-.72-2.38v-.3h3.32l2.2 7.76.9-3.1a2.51 2.51 0 0 0 0-1.44l-.86-2.9v-.3H130v.3l-.4.52a9.85 9.85 0 0 0-1.86 3.72l-1.92 6.66h-2.06l-2.16-7.2-1.94 7.2h-2.06l-2.4-8.36a4.17 4.17 0 0 0-1.36-2.38zm15.66 6.22h.3l1.76 2.72c.62 1 1.08 1.24 1.86 1.24 1 0 1.64-.5 1.64-1.16a1.86 1.86 0 0 0-1-1.62l-2.34-1.54a3.75 3.75 0 0 1-1.9-3.16v-.06c0-1.8 1.52-3.3 4.1-3.3a14.36 14.36 0 0 1 3.26.4v4h-.3L135.37 7c-.7-1.08-1.2-1.32-1.88-1.32-.92 0-1.44.48-1.44 1.14s.22 1 1 1.44l2.2 1.32c1.74 1 2.28 2.12 2.28 3.34V13c0 2.08-2.08 3.5-4.3 3.5a18.07 18.07 0 0 1-3.72-.44z"),Object(Ic.f)(c,"d","M93.66 16.48a2.67 2.67 0 0 1-2.72-2.74 2.72 2.72 0 1 1 5.44 0 2.67 2.67 0 0 1-2.72 2.74z"),Object(Ic.f)(c,"fill","#f4392f"),Object(Ic.f)(e,"viewBox","0 0 137.53 16.48"),Object(Ic.f)(e,"role","img"),Object(Ic.f)(e,"width",t[0]),Object(Ic.f)(e,"height",t[1])},m:function(t,a){Object(Ic.v)(t,e,a),Object(Ic.c)(e,n),Object(Ic.c)(n,r),Object(Ic.c)(e,i),Object(Ic.c)(i,o),Object(Ic.c)(i,c)},p:function(t,n){var r=Object(a.a)(n,1)[0];1&r&&Object(Ic.f)(e,"width",t[0]),2&r&&Object(Ic.f)(e,"height",t[1])},i:Ic.z,o:Ic.z,d:function(t){t&&Object(Ic.l)(e)}}}function Ms(t,e,n){var r=e.width,i=e.height;return t.$$set=function(t){"width"in t&&n(0,r=t.width),"height"in t&&n(1,i=t.height)},[r,i]}var Ss=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,Ms,Is,Ic.B,{width:0,height:1}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function zs(t){var e,n,r,i,o,c;return{c:function(){e=Object(Ic.G)("svg"),n=Object(Ic.G)("rect"),r=Object(Ic.G)("defs"),i=Object(Ic.G)("pattern"),o=Object(Ic.G)("use"),c=Object(Ic.G)("image"),Object(Ic.f)(n,"width","81"),Object(Ic.f)(n,"height","33"),Object(Ic.f)(n,"fill","url(#pattern0)"),Object(Ic.M)(o,"xlink:href","#image0_677_111"),Object(Ic.f)(o,"transform","matrix(0.00378788 0 0 0.00925926 -0.0681818 0)"),Object(Ic.f)(i,"id","pattern0"),Object(Ic.f)(i,"patternContentUnits","objectBoundingBox"),Object(Ic.f)(i,"width","1"),Object(Ic.f)(i,"height","1"),Object(Ic.f)(c,"id","image0_677_111"),Object(Ic.f)(c,"width","282"),Object(Ic.f)(c,"height","108"),Object(Ic.M)(c,"xlink:href","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAARoAAABsCAYAAAC1v33SAAAKrWlDQ1BJQ0MgUHJvZmlsZQAASImVlwdUU9kWhs+96SGhJCEUKaE3QToBpITQAkjvohKSAKGEGAgqdkQcwRFFRAQVQQdEFByVIoOoiGIbFHsfkEFEGQcLNlTeBRZhZt56762319rrfNnZ5z/7nHXPXfsCQFbkisVpsCIA6aIsSaiPByM6JpaBGwIQwAMFQAFOXF6mmBUcHAAQmxn/bh/uItmI3TKf1Pr3//+rKfEFmTwAoGCEE/iZvHSETyL+kieWZAGA2o/E9ZZliSe5C2GaBCkQ4fuTnDTNI5OcMMVoMJUTHspGmAYAnsTlSpIAIDGQOCObl4TokNwRthTxhSKExQi7pqdn8BE+hrAxkoPESJP6zIS/6CT9TTNBpsnlJsl4ei9ThvcUZorTuCv+z+P435aeJp1ZwxBxUrLENxQZKciZ3U/N8JexKCEwaIaF/Kn8KU6W+kbMMC+THTvDfK6nv2xuWmDADCcKvTkynSxO+AwLMr3CZliSESpbK1HCZs0wVzK7rjQ1QhZPFnBk+jnJ4VEznC2MDJzhzNQw/9kctiwukYbK6heIfDxm1/WW7T098y/7FXJkc7OSw31le+fO1i8QsWY1M6NltfEFnl6zORGyfHGWh2wtcVqwLF+Q5iOLZ2aHyeZmIQ/k7Nxg2RmmcP2CZxiwQQZIQ1wCGCAA+eUJQJZgedbkRtgZ4hUSYVJyFoOF3DABgyPiWcxlWFta2wAweV+nH4d39Kl7CNGvzMZy3wPgwp+YmGifjQXoA3ByIwDE57Mxow4A5FUAuFTIk0qyp2NTdwkDiMh7gAbUgBbQA8bAHFgDe+AM3IEX8ANBIBzEgMWAB5JBOlL5MrAKrAf5oBBsAztBOagEB8AhcBQcBy2gHZwDF8FVcAPcAY9AHxgEr8Ao+ADGIQjCQWSICqlB2pABZAZZQ0zIFfKCAqBQKAaKh5IgESSFVkEboEKoGCqHqqA66GfoFHQOugz1Qg+gfmgYegt9gVEwCabBmrAhPA9mwizYHw6HF8FJ8FI4B86Dt8JlcDV8BG6Gz8FX4TtwH/wKHkMBlByKjtJBmaOYKDYqCBWLSkRJUGtQBahSVDWqAdWG6kbdQvWhRlCf0Vg0Fc1Am6Od0b7oCDQPvRS9Br0FXY4+hG5Gd6FvofvRo+jvGDJGA2OGccJwMNGYJMwyTD6mFFODacJcwNzBDGI+YLFYOtYI64D1xcZgU7ArsVuwe7GN2LPYXuwAdgyHw6nhzHAuuCAcF5eFy8ftxh3BncHdxA3iPuHl8Np4a7w3PhYvwufiS/GH8R34m/gh/DhBkWBAcCIEEfiEFYQiwkFCG+E6YZAwTlQiGhFdiOHEFOJ6YhmxgXiB+Jj4Tk5OTlfOUS5ETii3Tq5M7pjcJbl+uc8kCsmUxCbFkaSkraRa0lnSA9I7MplsSHYnx5KzyFvJdeTz5KfkT/JUeQt5jjxffq18hXyz/E351woEBQMFlsJihRyFUoUTCtcVRhQJioaKbEWu4hrFCsVTivcUx5SoSlZKQUrpSluUDitdVnpBwVEMKV4UPiWPcoBynjJARVH1qGwqj7qBepB6gTpIw9KMaBxaCq2QdpTWQxtVpijbKkcqL1euUD6t3EdH0Q3pHHoavYh+nH6X/kVFU4WlIlDZrNKgclPlo+ocVXdVgWqBaqPqHdUvagw1L7VUte1qLWpP1NHqpuoh6svU96lfUB+ZQ5vjPIc3p2DO8TkPNWANU41QjZUaBzSuaYxpamn6aIo1d2ue1xzRomu5a6VolWh1aA1rU7VdtYXaJdpntF8ylBksRhqjjNHFGNXR0PHVkepU6fTojOsa6Ubo5uo26j7RI+ox9RL1SvQ69Ub1tfUX6K/Sr9d/aEAwYBokG+wy6Db4aGhkGGW4ybDF8IWRqhHHKMeo3uixMdnYzXipcbXxbROsCdMk1WSvyQ1T2NTONNm0wvS6GWxmbyY022vWOxcz13GuaG713HvmJHOWebZ5vXm/Bd0iwCLXosXi9Tz9ebHzts/rnvfd0s4yzfKg5SMripWfVa5Vm9Vba1NrnnWF9W0bso23zVqbVps3tma2Att9tvftqHYL7DbZddp9s3ewl9g32A876DvEO+xxuMekMYOZW5iXHDGOHo5rHdsdPzvZO2U5HXf609ncOdX5sPOL+UbzBfMPzh9w0XXhulS59LkyXONd97v2uem4cd2q3Z6567nz3Wvch1gmrBTWEdZrD0sPiUeTx0e2E3s1+6wnytPHs8Czx4viFeFV7vXUW9c7ybvee9THzmelz1lfjK+/73bfexxNDo9Txxn1c/Bb7dflT/IP8y/3fxZgGiAJaFsAL/BbsGPB40CDQFFgSxAI4gTtCHoSbBS8NPiXEGxIcEhFyPNQq9BVod1h1LAlYYfDPoR7hBeFP4owjpBGdEYqRMZF1kV+jPKMKo7qi54XvTr6aox6jDCmNRYXGxlbEzu20GvhzoWDcXZx+XF3FxktWr7o8mL1xWmLTy9RWMJdciIeEx8Vfzj+KzeIW80dS+Ak7EkY5bF5u3iv+O78Ev6wwEVQLBhKdEksTnyR5JK0I2k42S25NHlEyBaWC9+k+KZUpnxMDUqtTZ1Ii0prTMenx6efElFEqaKuDK2M5Rm9YjNxvrhvqdPSnUtHJf6Smkwoc1FmaxYNaYyuSY2lG6X92a7ZFdmflkUuO7Fcablo+bUVpis2rxjK8c75aSV6JW9l5yqdVetX9a9mra5aA61JWNO5Vm9t3trBdT7rDq0nrk9d/2uuZW5x7vsNURva8jTz1uUNbPTZWJ8vny/Jv7fJeVPlD+gfhD/0bLbZvHvz9wJ+wZVCy8LSwq9beFuu/Gj1Y9mPE1sTt/YU2Rft24bdJtp2d7vb9kPFSsU5xQM7FuxoLmGUFJS837lk5+VS29LKXcRd0l19ZQFlrbv1d2/b/bU8ufxOhUdF4x6NPZv3fNzL33tzn/u+hkrNysLKL/uF++9X+VQ1VxtWlx7AHsg+8Pxg5MHun5g/1dWo1xTWfKsV1fYdCj3UVedQV3dY43BRPVwvrR8+EnfkxlHPo60N5g1VjfTGwmPgmPTYy5/jf7573P945wnmiYaTBif3NFGbCpqh5hXNoy3JLX2tMa29p/xOdbY5tzX9YvFLbbtOe8Vp5dNFHcSOvI6JMzlnxs6Kz46cSzo30Lmk89H56PO3u0K6ei74X7h00fvi+W5W95lLLpfaLztdPnWFeaXlqv3V5mt215p+tfu1qce+p/m6w/XWG4432nrn93bcdLt57pbnrYu3Obev3gm803s34u79e3H3+u7z7794kPbgzcPsh+OP1j3GPC54ovik9KnG0+rfTH5r7LPvO93v2X/tWdizRwO8gVe/Z/7+dTDvOfl56ZD2UN0L6xftw97DN14ufDn4SvxqfCT/D6U/9rw2fn3yT/c/r41Gjw6+kbyZeLvlndq72ve27zvHgseefkj/MP6x4JPap0OfmZ+7v0R9GRpf9hX3teybybe27/7fH0+kT0yIuRLuVCuAQhxOTATgbS0A5BgAqDeQ/mHhdD89ZdD0N8AUgf/E0z33lNkD0IAMk20R+ywAxxA3XIdouwMw2RKFuwPYxkbmM73vVJ8+aVjki2W/5yQ92LFoHfiHTffwf6n7nyOYVLUF/xz/BVBSBueiaNbnAAAAimVYSWZNTQAqAAAACAAEARoABQAAAAEAAAA+ARsABQAAAAEAAABGASgAAwAAAAEAAgAAh2kABAAAAAEAAABOAAAAAAAAAJAAAAABAAAAkAAAAAEAA5KGAAcAAAASAAAAeKACAAQAAAABAAABGqADAAQAAAABAAAAbAAAAABBU0NJSQAAAFNjcmVlbnNob3SOJjelAAAACXBIWXMAABYlAAAWJQFJUiTwAAAB1mlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iWE1QIENvcmUgNi4wLjAiPgogICA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPgogICAgICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIgogICAgICAgICAgICB4bWxuczpleGlmPSJodHRwOi8vbnMuYWRvYmUuY29tL2V4aWYvMS4wLyI+CiAgICAgICAgIDxleGlmOlBpeGVsWURpbWVuc2lvbj4xMDg8L2V4aWY6UGl4ZWxZRGltZW5zaW9uPgogICAgICAgICA8ZXhpZjpQaXhlbFhEaW1lbnNpb24+MjgyPC9leGlmOlBpeGVsWERpbWVuc2lvbj4KICAgICAgICAgPGV4aWY6VXNlckNvbW1lbnQ+U2NyZWVuc2hvdDwvZXhpZjpVc2VyQ29tbWVudD4KICAgICAgPC9yZGY6RGVzY3JpcHRpb24+CiAgIDwvcmRmOlJERj4KPC94OnhtcG1ldGE+CjS2ZikAAAAcaURPVAAAAAIAAAAAAAAANgAAACgAAAA2AAAANgAAFtBMyjjnAAAWnElEQVR4AexdCXgUxbb+Z8vOZCOQQBJkExW9vief4nXHHUHgssi+CAICghsgKCCLCLhwEUElQgADSACVCAoqiAteFX1yFRQQCJAEAklIJvtktn6nOkwyM+mefUvS5/vm6+nqquqqv7v/PnXOqWoZRwJJJAQkBCQEfIiATCIaH6IrVS0hICHAIyARjXQjSAhICPgcAYlofA6xdAIJAQkBiWike0BCQELA5whIRONziKUTSAhICEhEI90DEgISAj5HQCIan0MsnUBCQEKg+RENVw7OcB4w/k3bfIArBkylkHEV4LhquiP09GOhRQpAngioroFM0Yl+KZTUEpBF0DEZ/ZqZMHx43E5IuDl56Tm9HsaSy9BfyIMu5wz9sqHPOwf9xQswlRTDVF3F1yQPj4A8Ng6qxDZQJbdDSLsOCElpD1WbZChi4yFTqZw8Y/Bma+JEQ4RhKgKnPw5O9xNxyGG6ErUX1/1LEgIou0IW0o2210OmJAJipNSkhOFWTIRy8gpuvxD3VnrYw6aPGyMWRiZVh35A2RefwnA+10PMaosr26ZA/VAfRNxyO0JSOzRK4mmaRGPIhUn3BaD9mq6Up8Ti6F4JgyysNxHP3UQ8yZS5EWs7ZtxqDniBWJoHbpzRgJrjR6HJ2obKA3TP+UEiezyEmL6PIfQaetEplH44o+enaEJEo6e3729AdSY4Y7bnyLhRg0zeGggfRKTTnfimhRs1BKKIhJs7qBs1xSjbsxPF6avdKe61MnFjp0Ddsx8UMXFeq9MXFTV+omG2g5pvwFXvoLewxhcYuVdnyH2QRwwlOw/ZdYJRJNzcuiqGgosozliD8r2fulXeV4VaPNwXcSMnQNmK7IpBKI2XaLgycNrPiWC2BiGs9U2ShY+godWjpOGE1icG8p+Em1voG8tKUfzBeyijIVIwi5qGVHGjnoRCHR1UzWyURMPpfgRXsSyogLTbGFkMZFHPkBHvf+xm8/VBCTc3EOZMZNjdhcI3F7pROHBFEp6fRwZk9oKTB64RFmduXERDb2NTxTvkPSIPUmMU1T8hj5xAw6lY/7Zews0tvPX5ebgw+ymveY/caoQHhZTJKWjz6mqoktp6UIt3ijYaouF0R0iLWUK99rUXyTvA2q0lcibkobfZzeKtgxJu7iFZtv9zFC6d617hICuVMGsR1Pc9EtBWNQKiMfKGXq76w4AC5fWTh/UlY/FoqtZXqq2EmzvXjMXCFK58NeiMve70xbIMMxYnTJsdsBicoCYazqQBV05ajPGEJWZN57/qZsgip9Mw2ruGYgk3924RQ6kG558e02iHSo56zYZSbVdsgDI6xlFWrx8PWqLhjBSZWjqeOmz0eqeDqUKZoj3QYhGRTZRXmsWxiF6NhJurYOov5SNnRG9XizXK/KmbdkPVOsmvbQ9KouHfyKVTKS6m3K9gBOpkfKCfehmRjWdvGo7mbHGlT0m4uXghdTQXKXd0XxdLNe7sKRuzEEJzqfwlQUc0nKkCKHsenOmSvzAIjvNQJLFM/RaFlLsX4cmZaC4Sj9vF4OiPv1rhIW7NSZOxvST+1GyCimg4Uw1QPoumEJyxxaSZ7Csgi1nnsmbDcYRb2eyATb0I/MVxDzdmkzk38L7ANz+ALWi3Y79fbDZBQzQcR16SiqUUI0MzhZuzKLqQZrMYMplzk+VqcaPgRf2h5owaTaB3DTeTTofcCY81WcOvszcDmxmekrYN8hCaXe9DCQqiYZ+W4qo2ADVZPuxqI6o6bAhk4YOJbOzPBOdxqybctBJu/NV1AbeC5QtREWTzlQJ1h0Y93Aetnpvn8H7zpH0BJxr+YaEpBah8zZN+NLmysqhFtOgWLQMgQjYSbsKX3Bncyr/e02SC8YRRcD2VBfW1uLen6P3meo3WJQJPNKYScsc+bt0qaY8QiCB7zXtkr1ELosFJuAni4gi35uhhEgGqQbIvPVEBJRr+rVxBmoyeNBpJGiKgvJWWtXmhwVtGwq0hVFYpYriZjMgZO6DZ22WssLLYYfaa1PSP6OXm/RUjA0Y0tao/La1Z2bhmxVpcF//8jZhBK0zcVkc2Em5Owi6AW9nenSha/oqTFTTPbC2fmwP1w/3q7jdvoRAwojEZaSHwsknBtViVt1D1dj3RGyBX1AbzSbi5AK4FbnpNCXIG3e9C4eabNXX7PqhivLvCQECIhn8rsxXxtJub79V0pedh5IEKH8KX4FcSbCa4Ze4shtEEtGqpxA3XRaA1bV0SC9wKVy1D+afbXSreXDO36DMICU81HLJ7gkdAiMakL6DAPFqXRRLnEYjOoC8TkBbYxHErKTUiI7MImTtLUFDIPn1TK60SVBjcLxbTJtC6zK4I4WYoLEfuSFoEqolKxB09UHP0d7B1jL0lKRm7EEKff/GW+J1oTCYTfbljFWR69oWCxi3bPy3BoD7eVTHFEDGFjQUMZyE3NH7cxPqYtVeD99YX4lS2ViwLJo5OwPSnnF8Xl+FW9M5hVNLnT5qSKGmeUov7H6FfL1rYKhm5U0ZA9/cxr3Uxij7vkvDcXMjl3lnGxO9EYzRoICsb4zVAAlHRvu/KsGFzEX7+rRLL5iejfy8/kI0sGkZjGRRyLhBdFjxn3gUdzl+s1Tq63xQpmMfZxHTCc8mKfKeyv7EwBX17OjkBlVMje+RxerE1jVUAmPaiJnKJvL2HFVb5C6aj6uABqzRPd1K27kVIfIKn1fDl/Uo0zDZj0n4FWTUtx9kIhRHLlu2X8fm+0rrW33BdODasbg91lPddgnUnufLHZJKRN4Cjn+0R/+6fyq7BrEV5+P1o/WqHA0mzWzLXvdnA3/ynHOOfPut0Jzq2D0Xm2o6IVjuHef4bxaj+tYq+Rho8JO10ZymjWXth3iBlgvDQsfCd11H2yVZXqnWYN2bMJMQNG+cVD5RficZIH9uSl09plDOz5yw+T3YD4THws5NbY/LjrRxeOG9kMBplUCgC+8D8a+QpHD1O9iIb6XlfNFYuTbVJFd49faYGx05Wo/eDMXhhQR4+3l0inFEk9UnC+3nC3RnRndEjd3YB5LTQeGMSXnuhlfEiu9/psNma7Rm4nLbCYT5XM7T77D9Qhni+MJvfiIZpM0bdOcgrn3G1r0GR/xBpM8MnZgu2hXlFNq7ugE4dPL8ggiewSGREI6fhU6C0mq2fFGPuq+ctWmT9d+1bV+Hu21pYJ1rsFWuMSN9ciPU0VOrUIQyrX0tFj76ur6AYopJh27qO6HptuEXt4n9zZhTAkKOj74gGlqTFW1h7hGkvaiIX9SP9oXBhJbyKb7/EpVdmO6re5eOJb6Yh4oabPNZq/EY0zAjMu7O1H7nc2WApMIcesEx60IRk2IB4LJjlPSu90DlYGsfRo0LPCiObQIg9DFh7Zk5LxPiRwuP69C1F2LTtMnLP6+qa3r93rMvajLlwL9KGVixOMe/a3Wr2VuDyBg1pNYHBzW7j6GDkPQ8gmsgl/H9vcZRV8Lj2rz9oGVLvT+WJ7PEgWs1a7LFR2C9Ew2szRh3kZaMJJHGPgiCCQZSYk6dD3xEnUVEprIKnr2qPO7t7Z0lOe902kFajDNDwyRHRpK24Cj1ut9Zodn9Vis1EML/+lxbnckKYYfmWbpE0PNPiwPdldkusXJKKnvc7/liaqYpD9hMXoGCBOUEkyjZtee0luld/yKOE57U501xD4SVa9mIwfY7I+6tSpn56EMow+sa8B2q034jGUHMaiqrpzmAW1HneXluAlWuEV/+790411ixv5/P2Gw1yihQ2BWT4dOKUFr2HnhTsY3i4HIe+uhZhodYu0akv5GDv1/UGdMHCVxKnUpzMtPH19q4Fr1/gtSCxMv8gY/xHGzuJHbZKPzv1EkwF+qAcPoXddAuiew1A1F3uRy/nTBwCfbbwtbECwsWdxFUfIOLq64KfaNiwyaTdDbk23cUu+j57VbUJx/7W4shfVcg+W4O8fD2iIuRIbK3C7aSd2NobanQc+g4/idOUV0heJc+Lr2NrmJ2GvVwCNXza8GERFi9v6Ipm8S0szoUJ80jdeH0E/5/h24cwcyRTiWBsA/KYTWfo+NP8tRErP2NqIiaMEh6uWZa5+HYxKn+oCtrhE2tri559Ef3oIIR2vtay6U79z5/zNKp+PuhUXlcyMe9T7NCxHg2f/KLRGAzkbap6hQLO/utK/3ya99hJLbI+L0HWHg2KLhtEz9UuJQQD+8Rh7LCWCAmp9StvyyrGS68IG0Svu4bc3TSEio2273otKjagZZxS9Lx2D5CZgR8+KQNnb9j9pQZbP6q1VzGDLCPlu/7Zgtdc0jcVITU5BCzexSxvrL6INRsKzbuC24N7rhWcZsACI18kd7qYxMYosDOjM9okqsSy8OmVP1cjf0UxFMxeGMSiiImDut9gxNB3tF0ZTrHvUZXt8r4NlM3qbrt2B5RKN+9XwtrnRMPsMwaDHoryEXS6eiNgIK/zWnoQlr3V8I1sr02MQOZNb4NuN9a+pUdNPoMff6GF1AVk2sTWmPpEvfpvm2U9aQQHf6rAOvLQuCt6vZwufGCGT0Jt/uVwJdaTsferb+ptKivIftLriv2krNyIfuQWtzQE29Yzb2YbjBwUb5vM7z/5/Dnsp0BJMRnSPw6LZrcVO8ynMzvN6XH5RDRGGj4Fv6jZnKOps5xuaMmH6ShOX+10flcypmR9D1V4uNvDJ78QjV6ngbLS+xZxV4Ay553+ci5pMhrzrkvbFhSUt/TlZDx4jxrf/liOJ6adFSwfT5rKRtJqunQOszrO7BRp9FY/cqw2BmUuEdeowcIPllVBgR2dTg6VKvBEw4abGWTo3USBjLbS7cZIbHqvPRFi7WO9Y1cJZi8U10xYvqMHu1KcUEMaYEQ2bEK27Sms9jet6QBHEcqnJ16CrJTsNEHqfTJ3SEGzp1PSMqGIdf7+KN/3GQqWzTNX4dVt0oadCCfXu7sGYZ8TDbPPGGuyoagOvCF42qwc7NnvnFFS7CqxodSWtI78jOIZ8/Ow8zPhQLPB/4rDKy/WvmH/+KuaJ5gvDlifm9W1fX0nh8MsobYwjYYF7gXKTmNuU//Rp8i+1TB4z3zc1n7y+NQzvDZnPm67fYJc4y+Qi1xIXnv7It7/QHz4dQcN39YTwduTnPmXoadgw2B1c5vb3pI0mWjSaFyR6t9/xYXpE10p4nTehCWrEHVTd7ftND4nGqPRSIbg76GoWeF0p3yRccFr5L0QeOu6c66RpIWwYdQJsvMMIUOlmLv7fRoa/UBDJGY8FZPxZMScScZMV4UZhJkEIkqYzfV6Z10BPiZvD/s/iYY1YpLYSoWMd9vjqtTaYMYff63AqElnxLLz6V/tuBpXtavNb5kx/5IeY546Y9cwvGReMgY+GmtZzOp/4aYylO0uD+oo4bAbu6HtG2lW7bbc0Z74E2Fdulom8f/19CG8PPI8mbTixN+gkJMJsZOnE/E9RvebfdujWHU+Jxo9fTRdVpMBuS5ws2c/Ia1jJmkfYsK8Iw/2UKNdcijO5+vojVuO74kgxERFUam7NncGm3Njz8jJhlCXyejrSHZv6dxgmOWoDJv3xH7MTuMvYXOcVqcXYPcXtUPP37/tigjy0D03Jxe7rqQJtWXogDgsnFVvP3FE+iwuhsXHCMmWj4vx8pKGhng2TBs+KA6PPhQjVKwuTbOvCkXrNLydpi7RB39U7TtCf+a0WzUnLV2NiG63ipa99No86PPzEDtoFCJvu6cuH0e20NwJ5OLOPVuX5q0/UY8ORNyk6TRct29wFzufT4mGGYIZ0Siq50JmdD3MXKzRrqbbM9yK2Um2ZZWQZ0mcnMxzbdj6KUOfOC3q7rbX1vvvVmMEaUe33+x6kB+LENYbZAghO42vRW/g8HZaAd5dT+sIWcjby1Lx8L3R/LIOvSi2xp4zxzKY8RxNBRg07jRKNOIkvHYlTWUgL5aQTHj2LA4cLOcPMU/TuBEJGD4wTtC2Y1u+8lctLiwvhpI0bV8Ji4lpu+xdFG9+H5rNa8Hpxftp2wZHBuDKHw7g4vx6M0TkHfciZvBohF1zPV/VhdlTaALpT7bVerzP+pSw6C2eaNyx0/icaHT0oS5VFY0budq3oMc9drGC3/+sxsAxpwRLzacpA8Np6oCYsFnaT8/OETzc4apQfLH9av5YBg3JFtLQzFnpSh4sNvwaQOH3bgsRTQ3ZaUJU5EFpaDt1u1rbgmxu05vkmtYQodrKY/3isPilWk3lrbRLWPW+NRFZ5r+TSCOdyMMsjjx/TMvcsb6jObvVlmmbY8nWw2J2Rg9tiYR4pdVxezvaEzqcW3gZKgq58BVsifPfqFvGQZ93jiecin177DWLP8YMwG3/vQ6qZJGgT3pxn39hErSHf2lQl7rPQMQOfhzFm9JQvierwXFPE5iLO3FNJoV4hLhlEPY50dTU1CCkenzAPjzP5tcs+Xe+IM7/d+A6h8s7zCM1/UNS14Vk/ydd+HgRdsye1mQuGxUpB7PJjCAXrrqFe2Ndc11sbqBWp0BoiG+J5vVVF5G2UdgAm0TaxHe7ruGbxOxUA4jQmRdKTBaRcXwIGcmZsMDHkU9m4/CRKrHseJnc3QwrIWERyl06WXv1hPLZpunzDMieVQQVaRm+mGAZ9UAvtJ650Pa0qPzpOyKctdAd/7PBMXNC/IRnEDNopHm3wbY0ayuKVr3eIN2cICMSiBkyBiUfiNt3zHld3cojIpGU+SVCQ0ODk2i0Wi1CtUyjEY+BcLXTruR/luwHZpuCZTnmBmXuUEeykwL6ZszLFcxm6U79kuJHpswQN4qyt/+YIfHo3NH1h0Pw5JRYrVVQuL9viYYZvHsPOynWBPx96IY6jcoeVqwCpgV+vKETIolwmXxGc6CeeVFYY2THW5Km8nnm1W555Vh5ITFcNuLk04UIZRqNl13c8ogIJNGQyTyMETq/ZkcGSjathanS2gYY9o+b0GbJajCyEBI2l+n8zEkwkIYUECG1uQ3F0oS5OefJ5xpNdXU1wmqmEtE0jLPwB2AjaGkHtmCVrThLNKwsq0NILImGHRdyd3enyYETxiTgrluF7Q1C9TqbVlWtRHgYPTC+GgNcaUjnm4+INmkdDYdYRLBZLO0n5jTL7UTCYvqUei+b2IvAXGbs8JaY/UySedfjrbHMhL8nM6LxfiwN0ybix9G97kAYaTD7Tflnn9TlbD13md15TpfXroQmc2Ndfr//oZssaed3CHczaM+nRMNiaJhGE6abSQtrixtWXQGNBb0V0pQBsQhS27rEiCaVYlj2f9zFNnuDfXtEk5XRCSxi2CzMHjRmSjbv7mZu3cnjWmEoRaz6RGjoVKVlRMM0GtrxQD6iRaeYjakTedGEJigyrYNpH0IyhqZmvPRsPRHYw4uVZ9M4mFZjDmb87Y8qfgil0zfsAwuQHPFYPIaRoTeRFif3hpgqTDjxpPeJRkl2leTl77sUYFd1+BBKiHCUrZIEh1vm/mqPHUE+aTO+cFubz+Foy4ZOrT/cy2s07qwj7BeiCdUvJK/TcUd9sXv8TwqyYgZENgxiKjhbPjOJHmZHYu+NyYy5rC57IjaBkKn1P+5tOPGNubv19NAwr5Sj+U72zuvwmBeI5g8ixsXLL4A97GZJbkMEvLML5BZakj2jOCOnPdtqjeLmOtiES3uxQ70eiMaKV+vd10KBeGxKwbCB8bjWJrrafA53t8ZS0mimeJ9oWk6Zgeh+Q9xqlvb4UbvDrUtL56Biv2Njslsnd7IQMwYnvLPFbaL5fwAAAP//HVTk5QAAFmhJREFU7V0JmBNVtv6zr530BjRLs4m4iyMyuKGO2wzgCjOAigI6IqAyKsqmg8rjoYIITwFBR0FUZERxG0VRx1HxjQ6fTxE3FFFpuum9O71lT9491V6sJFWdpJLuJFD1fflu1d1yzl+3/rr33HNvacLsQCcdVLXb7YbJvwKa4H8U/Ut9QwBPP1+HtU/VwO//VdSpk7rhjptK4tb55MZa3Lf8gGS+y0cXYMk9fSTTeOQlV32Pb77z8MuD4WWs7FKJspXVfpR0NxzM11kn4bAGbo8OFnMAGk1y/+J2h3Df/xzAcy/WSxa0WrV4Z8tR6FakF9IPMJ3OGv2tZF6K/H7HCRFp+/b7cMXUvaiu8UfEiy8eXdYP55/lEKIqqvyYctOP2PuTF+eckYcpE4tx+il2cfa0nXu+8+HHhQ0wBfzQpKnpm4ecgt4Prk2bjOKKWj58B1UL54ijMnJu+s1vUXjPMlgsFtbekmxwTGKGdZrQllCfqvZ4PND710EX2CaRo+Oo9z5qxkOrK/GtxINOD8NTKwfgpBOsHVby5TduXH7NHtk8HRHWzLn7sPVdl2TZVUv74cJz2h8UyQydHBkMauHza2A2BZMmmudfacCdi/bHlfAfG4/EUUeahXx/mvIDPv+yTbLMxscGYthvbBFpHRE8ZRxyvBUvrDviYBl6mTidOlzy+/yDcZ1xUv96K6qea2ZEwwg6TU2/hD2AtjPOSbu44WAQFXOmw7Pz07TXnWyFtovGwjn1VpjN5uwkGq/XC43vdRgCTyerm5D/1jvL8I9tjZJlLx2ZjwcXlkqmiSPvWlyOv78k/famfMNPtmHyVcXIs+uEYtv/3QwiqO2ftIirOXh+2jA7NqwecPA6Eyc+nw4h1sEzGZMnmj17vRg5/ruExF7P9DyD6fvI36rx8NoqyTIzruuOW6f1iEij3uekG3/Ejs9aI+LFF3fO6onJE4rFUZ1+Xr7ShZaP3TD4GdHg1x6y0j+2nzcSPeYuki3uLy+Dv6oC1pOHy+aRS2h8aSPqVi+TS+7SeMe025A3eixMJlN2Eo3P50PA8zUswXsVAfP1bjcunSjfI3nkgb74w7nODuumOqgr39YW6jBfIol2mxbrVw3EkOMsiWTvtDxtbj10ujCMhuSJhoQ6ctiuhGV7aFEpCvP1mMyGN1LHicda8OJTg2KStr7jwsx5+2LieQQNk+5b0AfFhe1DNB7fmeEP8xsQ+NnDXnxENKkdGvZ2771iHUxHDJatqGb1UjS9tAl5v78E+eMnwVjaXzavOCHAyKl8zgwEGFFlw5G/cDnsjCyNRmN2Eo3f74fHXQt7cIZivO5n9oQnnqmVLD98qA1PrRrAHrqOm82j62qEYZhkJUlEPsDsMmOYfSbTh6vFCIspwIhGmjzJrrLxhTrsL/dj+X/H9vpGjv8ee/bG2p7k9KLex9r1NaitC0hmibbT8Eyz/lqGV9+M7JGecIwFE8cXZQTHr6fXweDyQs+GJake+RMmoei6mbLVtO34CAfm/5quMRgY2UxGASMcjbnjF1XtYyvg2qxsFCArUAoJhWs2wVbaDwamQ9bZaEgvIpqWlhY4cRMQlu9Gd4QBGYQvYb2aKvbwSB2JdsFvv7sMr7wR2eil6pOLu50Zn29gRuhsOOpdRjhsAej1sUTz8tZGbNhUi11fuwVRrxhbiIVze0eIPX9ROTa/Ij+cjMj8y8VFF+bLDmNf3jAIxzECiT4+29UmGHpbWW+yiPVcrr+mGyb+sYh1wTt+MUTXk47rsC+MXVPrYWXDeV0oNaLR9y5F6apnoLXJG60r7pwJ938+ihFd36sPI5vJcIy6PCaNIjxffY6K2TMQ9nkl0zMRWbhxK+z5BQLRKPn/TjUGk0BB9uZoamqCQ7cemsD7SmQUyqxjs0eLZWaP+pUa2XBmAPr0NMat/8FVlcKbOW5GUYZTTrJh2pRuOPv0PFFs5k5pxqmxyQCH3S8Mn7gkZFda+kgl/ndHrG0p2uj9wmsNmLcw1iB85nC7rG2K/49UOOvGEkybLE3ChLnXG8a1zA7Ws0fnz8hJyUdxbTu92PNQC2xeD7ThWIKWKycVX/yX+XAyA6nc4Xp5E2pXLZVLFuLNQ4YKhGMddnpEvqrF89Hy3lsRcZm8IFJ1LF8Hh8PB2lu7HTNZeTqdaEKhEFpbW2EI/x+MgeXJyheRf+ykPfjil7d0RAK7uO7qbpg7s+Pp7qaWIKwWLT7d2YZ1z9bi3Q+aoquJuKaH7tJRBbhsVOfOhET8aQIXPp8WHr8OdksAWi1zIfCEcPuCMmx7r2N9xD2ySjb1PGJU7JT1yPOd7OUAVpf0bJuceDSEfWbNQMnkrpryl/xzUWTl0y2of5u5W7CegjaFGSfziSej97LHRTVHnpIBuOKumxHYn5h9xX7+KGE4Zew/CC3vb0PVonmRFWb4yj7xeljHXAWbzcbam1aRNJ1ONNyXxu+pRB5uUyQkL/QGMy7+Rca4aDKx6W7Wqxk6RHq6+9kX6/AYszFMvrIYU65on+n4ucyHV7Y2oLI6gMoqH3ysa03HCNZzIZI57ujYoQCXJZOhq8UAIxsyiae2Vz9ZjeWPVsUV665ZvTBpQpGQT8og3LPEgDc2DcbCpRV46fWGuPWJM8jZacR5Mnn+/V9dCPzoSdmHpud9K2E95TRZVbgBWDaDVALzTcmfMBmeLz+HZ9dnUjkyFudYsga2o45T7ENDgncJ0ZCdxuVqRJH+VsV2Go7yjDt+xtv/kn5zkw0h2vBJvjhrmSH4053t9iEaZj3/xBEoLOi6mQ4ue7rCmnoTnGzYZGCGYLHv1ONP12DJw5Vx/+aeOb1wFbOTyBmEn2T+SSMY0d67pALPbK6LWx/P8NbmwRjY38QvsyoMe8LYeWMjLMyvK5UZJ/uFF6HHHffK6hZtAJbNmEMJjg2vwVlUrNgQTKp2OtHQnwTYVKLL5YLDsJk57r1JUYoPGvZM+PMPsuVXLO6L0Rc4sXuPR7DFvPZWrPGXbAlkU8jFI8Ac9ZqayT7jY4bgWD+QpzbVYdGyiriqLbqzN77e7RFmpqIzz5zaAzdf312IXrqyEo8xr+xEjrtuZ70lNpuUjUftq60o3+Jl9hk3MwQrs89o2NRu6dpNMPTpJ6uinAFYtkCWJ1jOOh+mGbOZM6WTtTflL+cuIRpupwn5fkae5q6Uoe3oTUuGWxo+0ZIFuYO8immWZEC/7Hz7yslN8Y3NRsEuw+0zUnk3bqnH3feVSyUdjCNb1YLZvTD33liD8Fmn5eGJh/sfzLuGDTmXMYOu3HHEABNmzSjBBRn0lJaTjcfvnudCsNyXkn2m4JqpcF42Abo8ab+tRAzAXJ5cCfMWroDl+JNSss+Qrl1CNGSnIQ/hxsZG9LAsQTi4NyWcyyrYWprr98pOdydS+RVj2JTvvMgp30TKZTpPZY0F+Q72wMTxCJabVRLL34OtyZJyGXDk6fDpP48VZ8VzjLwWRJGXxazF3Ft64ko2fZ7NR6A6iJ3zWmBnvZlUPYKNRx2L/DFXIu/ckREqJ2sAjiicxRe29a8iv4jcEZR5BHPVuoxo+PDJavgS5uAK/v+Kw3hvWbmKuxfrMXFcMa4eVwTy8s2lw8MWUbYwj+D8PL+k/0y0LvE8c6Pzi6+l1i+JjfHTr+2O26ZHLjsQl8+m8/InmlGz3Q87s8+k6j/D9bKdea7g6Ws++nghSpEBmFeWpaHtqj9Df9GfDg6blDjqcdW6hGjoz/jwqa3VhW4mNnwKx9pOuFCJhP5AGBOu+0F2uluqjiuZAZQIZhDr6ufiUVVXDJvZxabo26e1E9HhI7ZeS27pQEflb2Z2mpm/2GnE+ai+Ywabc8qYvnNmEwyNbLaJLYdJZVpbjAM/d469Etahp0Z4APO0XA+tq56FvWfvlIdNhEOXEQ0Nn2jdU319PQpt/2aW/w0p3weafp19T6yNIbri352Zh2vY4j2ass7Vo8U7HI2tfdHNviXp9U3kyDeOkbJ4m414OJCt67nHpf1i4pXNpnTC7af1bMj0wVuszbF1YWlYSBmtny6/EMHG5Lyso+vItmvLBaOhuXo6CgsLFa9vEuvUZURDf0pews3NzWzriDp0N84Sy6H4vKPpbjL2kkfsHy/O/NokxQr+UvCn+vkIhk3on39PhDdwovXSHjG0tw4tBUjkoLVj337cPixIJH+25iHc/PUhaJkDXarewNmqY2fIZWGewNY+fZGXl6fYG1gsV5cSDd+fhno1xfZ3YQhuEcui6Jzc7SfNiFxVTM6LtHXBJNaLyXcoc5lWJEwnFfJpx6Gi/lSh9l6F22EMKcOtwRXEhWN3o5GFiRzbXhickzNzXDcxbqZXNsLzxks8SQ07QMDC1mCFx00RejNK95+Jrr5LiYb+nIzC1Ktxt1WhxDI3Wh5F1+Lp7ovZxkk3MD+Zowa1b9ikqMJsKqTJx4HWBTCZHYJUPm9tSriRC8mI0d+gulZ6FTb9yW/ZJlZ3sOUcJ7HNqXL2iMLNXVsD380Tc1adrhTc+MgzsPcoEXozqfjOiGXucqIhozBNdVOvxmH5CjasFsuj6Jy2gKSV2dSL4dtDKqooCwu1hGeh2TMQ+fn5wvL8hoaGtOB2ziW7UX7AF6GxjfkXkW9NNmyDESGYggsp3LQfv4+2NdmxkZQClbqkiHXaLIROPVvozdCUttK1TdHCdjnRkADUq6GFlkQ2fYs2sVXdn0TLpV4zBML6U7Gvbrxw02lBGx3pxO1iZrPh26TSyup5zCfmUDhkcaurg3bhbVmzmVS2YU2rtEMLHkIh85uh9pau3gzpmRGi4TNQ5MAXDDSyoQCtHZHejzbbbkZXylPpXgydvkjozdDOZnTQzF06cZt4w1781/zeOW2Lib4nHeHmqyiDb/YN0UXUa4aAcclaGHuVHmxvqfjNRAOaEaIhIWgGir6QUMfeMk7bfji0He/dES34oX7dEpqOhtajUcTeLrTzPN8HRMWt4zufCG6Gzz+Be+UDHVd0mKVabpoD/0nDY9pbumDIGNFQr4aGULT7Xm1tLfp2/5jNQr2YLr1yuh6/bhT2Vf8OxcXFsNvtQheWv11U3ORvbTK4WbdsgOedN+QrO4xSzGw/nLYx10i2t3TBkDGiIQX4EIpWdjc3NaJ/8d+gCSW2O3+6AMi2esK6YfixZhzbzcwpuH5LbQat4hZ718K6Uxhu4xPGrYnZBw33zz3s7TVkl/HPvR8O5phHK7Sl2lss2snHZJRoSFyahaJvP9FsSsBfh1IH2WsS8/NIXt3sLqHRDcRP9Tewm21HQUGB8A0dOau/ituv91KjG8Bwm5Y0bt76WoRvu/bXig7DM81DT8JUWBy3vaUKTcaJht7O9NC0tbUJs1BauNDbwew14eZUdcup8hptCcpctzAHeYswy2S1WoWpRT5kilZGxa0dEY22xy+4WRXhFq6rQXDO4Wkc1j2wFpqibgnhFt3+kr3OONGQwPTQkL2Gk41O24pS50qEQ/J7oCSraFbn1+Rhf9McBEO2gzedphblSIbrouKWHtxQW43QfOWfA+L3I5dC7WLmv1bcPan2lop+WUE0pAB/aMg4TMMondaLvgWPp7x3TSrgdE1ZHSOZBQiE8oTua7TxN54MKm7pwU3TUIfQ3Gnx4D4k0rX3r0G4oEhRe1MKQNYQDSlADw3tL0xOaUQ2oZAXA7ptgSa4Q6l+WV0urB2Mn+uuYN5MTuGmk5OUkg90qbilB7cgm5Awrlh4yBqIyfDru+Vu6NhEA9kAlbY3JQ9VVhENKSB+aMgxzeNxY0BPWkh4aE1F+rRj8NOBoWwNk1VwkEr1pqu4aZS0/9j2xnrUjtf/Ds+7WxXVl62FzOeNQtPocTAzdwlazpJqe0tWz6wjGlKAHhpusyGyoV+/nhVw6Ncmq18W5reiMTAdZQccwg2nm06G30RsMvGUUXGLh5B0uhRuRXu/gZd9N/tQOEwz7kDdwGPS3t6SwSYriYYU4Defpr7pS5dENjarH6WFb7Kh1MfJ6Jg1eUO64SirG402t0646fTlP1qGnw6S4UqquHEkkgulcDO3uKB79AG2qXn8zdWS+7euya1jQ6Xg9Nnw2J2d1t4S1SRriYYUoJvPV3uTkZgc+2jZQmnPejj161mG1LYDTRSkdORrCk7DvgO9hOUE5BhFRl++Ojbe7FKy/6/ilixi7fklcWNuF0XffQHPY6nvc61MKmWlzFNvQd3gE2FhveXObm+JSJjVREMK0M2nHxmJafqbejf0MxnDKO2xC6bQ5kT0zFgen/ZylFUfD4/XKHy7mHoxNFTiRt90kwxXVMWNI5FcKIebgX1GN+/9rfBsfTm5Crs4t3nkZWg+eyT8RlOXtrd4amY90XAFqAGQ3Yb2sqFZKdo8i3o5TvZFgF5FH8IQ+oBnzXzINl3yai9GRc2RaG4JCb0X2hKRDHDUi0nnUCmesipu8RCSTpfDjT5AZ33v9awzFpOxt+3cUWg1WjLa3qTRzNA2EXLCxIunm09DKSIcst0Q0RDp0M9uC6OkaB/suq3M0S/+N6jj/ZeSdFpC0BwYhQO1Jaz35ReIhciFhkncFkNLCjqrFyMns4qbHDIdx3eEmyXgQ94XO+DZtK7jSjo51TxhCppPHAa33pg17U1K5Zzp0YiFpwZA2yXQcIoIh4ZURDYU0kPcrcCDIudXbDX4NlbMIy6a/nONDX7NmahrOhk1DWZhmEdDIyIYColgaJhE2zx0NcFEK5tVuMEKv3ZE7uPGXnzOhmrot/8T3u3vRkPeKdemM89DgP1cBd0QZi+ubG1vYuVzkmi4AtS7oR8RDg2pyFDMf3St12tQ6GhlH1wrh1n/JbTBb1lRPy+uLGTEEtINRZv/BDQ0F7ONvjWM9ELCkIj2jeE/GiIRwVAPRm5hpDIBUi+VEdwYsYR0Q+AOnMhw644Gl+6Qw03HVqrlNTfCtOcb+D98O22zVbrefWAYcQG8g45FM/scL300htoXb2sUZnN7oxab00TDHzlxF5d2oCOSoZ4ODymOhlsGg54NsQCbhY2zTXUw6fZDr9kLLapZVfQZEnL6MrDAwpoMIxQ42PqjPPiDxWj19kGz28F6ToyqGLGRnYWW1FOPhW4yDymO0jIxROJ4JBp2DW692Nc1nWhp1Rx+uGk1sPi9MDGPY31tFTSV5QhV7EewthJh8nx3t+8qqbVYoWGeurriEmh79UG4pDcCxT3gdeTDbTDBHwrnfHs7JIiGP1j04PDhAR9aEcnwHxEE/Yh0KJ2/2SmkcuKDhjm8N0IhDX2IQKiXQj8iFP7jQyM+PMr0EEmsRyLnKm6JoBSbR8UtFhO5mEOKaLiSnDQo5GTCyYWHRDR0ztN5GV6HmGiIYIhEONnwkBMRJxYe8jpyLeQYqLgld+dU3OLjdUgSTbTa4obAHyIKo3/ickQa0T8+HOKEwkNxuUPpXMVN2d1UcYvF7bAgmli12x0Bo+N5A5EiEKm46PKHwzXHSKwrj5PCSCpOXPZwOecYifXlcVIYScWJy+ba+WFLNLl2o1R5VQRyGQGVaHL57qmyqwjkCAIq0eTIjVLFVBHIZQRUosnlu6fKriKQIwioRJMjN0oVU0UglxFQiSaX754qu4pAjiCgEk2O3ChVTBWBXEZAJZpcvnuq7CoCOYKASjQ5cqNUMVUEchkBlWhy+e6psqsI5AgC/w/Uxe/47mFkDwAAAABJRU5ErkJggg=="),Object(Ic.f)(e,"title",t[0]),Object(Ic.f)(e,"extraclasses",t[1]),Object(Ic.f)(e,"width","81"),Object(Ic.f)(e,"height","33"),Object(Ic.f)(e,"viewBox","0 0 81 33")},m:function(t,a){Object(Ic.v)(t,e,a),Object(Ic.c)(e,n),Object(Ic.c)(e,r),Object(Ic.c)(r,i),Object(Ic.c)(i,o),Object(Ic.c)(r,c)},p:function(t,n){var r=Object(a.a)(n,1)[0];1&r&&Object(Ic.f)(e,"title",t[0]),2&r&&Object(Ic.f)(e,"extraclasses",t[1])},i:Ic.z,o:Ic.z,d:function(t){t&&Object(Ic.l)(e)}}}function Ns(t,e,n){var r=e.title,i=e.extraClasses;return t.$$set=function(t){"title"in t&&n(0,r=t.title),"extraClasses"in t&&n(1,i=t.extraClasses)},[r,i]}var Ds=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,Ns,zs,Ic.B,{title:0,extraClasses:1}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function Bs(t){var e,n,r,i;return{c:function(){e=Object(Ic.G)("svg"),n=Object(Ic.G)("title"),r=Object(Ic.H)(t[0]),i=Object(Ic.G)("path"),Object(Ic.f)(i,"d","M11.5 36c-.5 0-1-.2-1.4-.6-.8-.8-.8-2 0-2.8L23.7 19 10.1 5.4c-.8-.8-.8-2 0-2.8.8-.8 2-.8 2.8 0L29.3 19 12.9 35.4c-.4.4-.9.6-1.4.6z"),Object(Ic.f)(e,"extraclasses",t[1]),Object(Ic.f)(e,"width","15px"),Object(Ic.f)(e,"viewBox","0 0 38 38")},m:function(t,o){Object(Ic.v)(t,e,o),Object(Ic.c)(e,n),Object(Ic.c)(n,r),Object(Ic.c)(e,i)},p:function(t,n){var i=Object(a.a)(n,1)[0];1&i&&Object(Ic.D)(r,t[0]),2&i&&Object(Ic.f)(e,"extraclasses",t[1])},i:Ic.z,o:Ic.z,d:function(t){t&&Object(Ic.l)(e)}}}function Gs(t,e,n){var r=e.title,i=e.extraClasses;return t.$$set=function(t){"title"in t&&n(0,r=t.title),"extraClasses"in t&&n(1,i=t.extraClasses)},[r,i]}var Fs=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,Gs,Bs,Ic.B,{title:0,extraClasses:1}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function Us(t){Object(Ic.d)(t,"svelte-1e19no9",".ad-toolbar.loading.svelte-1e19no9{position:fixed;right:-500px}")}function Vs(t){var e,n,r,i,o,a,c;return r=new ks({}),a=new Ds({}),{c:function(){e=Object(Ic.m)("a"),n=Object(Ic.m)("div"),Object(Ic.h)(r.$$.fragment),i=Object(Ic.E)(),o=Object(Ic.m)("div"),Object(Ic.h)(a.$$.fragment),Object(Ic.f)(n,"class","ad-toolbar__bflogo"),Object(Ic.f)(o,"class","ad-toolbar__badges"),Object(Ic.f)(e,"href","https://www.buzzfeed.com?origin=tb"),Object(Ic.f)(e,"class","ad-toolbar__images")},m:function(t,s){Object(Ic.v)(t,e,s),Object(Ic.c)(e,n),Object(Ic.y)(r,n,null),Object(Ic.c)(e,i),Object(Ic.c)(e,o),Object(Ic.y)(a,o,null),c=!0},i:function(t){c||(Object(Ic.J)(r.$$.fragment,t),Object(Ic.J)(a.$$.fragment,t),c=!0)},o:function(t){Object(Ic.K)(r.$$.fragment,t),Object(Ic.K)(a.$$.fragment,t),c=!1},d:function(t){t&&Object(Ic.l)(e),Object(Ic.j)(r),Object(Ic.j)(a)}}}function Hs(t){var e,n,r;return n=new Ss({props:{width:"82",height:"52"}}),{c:function(){e=Object(Ic.m)("a"),Object(Ic.h)(n.$$.fragment),Object(Ic.f)(e,"href","https://www.buzzfeednews.com?origin=tb"),Object(Ic.f)(e,"class","ad-toolbar__images--bfn")},m:function(t,i){Object(Ic.v)(t,e,i),Object(Ic.y)(n,e,null),r=!0},i:function(t){r||(Object(Ic.J)(n.$$.fragment,t),r=!0)},o:function(t){Object(Ic.K)(n.$$.fragment,t),r=!1},d:function(t){t&&Object(Ic.l)(e),Object(Ic.j)(n)}}}function Zs(t){var e,n,r;return n=new Ls({}),{c:function(){e=Object(Ic.m)("a"),Object(Ic.h)(n.$$.fragment),Object(Ic.f)(e,"href","https://tasty.co?origin=tb"),Object(Ic.f)(e,"class","ad-toolbar__images--tasty")},m:function(t,i){Object(Ic.v)(t,e,i),Object(Ic.y)(n,e,null),r=!0},i:function(t){r||(Object(Ic.J)(n.$$.fragment,t),r=!0)},o:function(t){Object(Ic.K)(n.$$.fragment,t),r=!1},d:function(t){t&&Object(Ic.l)(e),Object(Ic.j)(n)}}}function Ws(t){var e,n,r,i,o,c,s,u,l,f,d,h,p,g,b,v,m,y;n=new Rs({props:{config:t[0]}}),c=new Fs({props:{title:"Right Arrow"}});var O=[Zs,Hs,Vs],w=[];function j(t,e){return"tasty"===t[0].zone1?0:"bfnews"===t[0].zone1?1:2}return u=j(t),l=w[u]=O[u](t),{c:function(){e=Object(Ic.m)("div"),Object(Ic.h)(n.$$.fragment),r=Object(Ic.E)(),i=Object(Ic.m)("div"),o=Object(Ic.m)("div"),Object(Ic.h)(c.$$.fragment),s=Object(Ic.E)(),l.c(),f=Object(Ic.E)(),d=Object(Ic.m)("div"),Object(Ic.f)(o,"class","ad-toolbar__arrow"),Object(Ic.f)(d,"id",h="div-gpt-ad-"+t[0].wid),Object(Ic.f)(d,"class",p="ad-slot js-ad-slot js-ad-slot-"+t[0].wid+" ad-toolbar__ad svelte-1e19no9"),Object(Ic.f)(i,"class",g="ad-toolbar loading "+("tasty"===t[0].zone1?"ad-toolbar--tasty":"")+" svelte-1e19no9"),Object(Ic.f)(e,"id",b="BF_WIDGET_"+t[0].wid),Object(Ic.f)(e,"data-module","ad-toolbar"),Object(Ic.f)(e,"data-bfa","@l:Toolbar;")},m:function(t,a){Object(Ic.v)(t,e,a),Object(Ic.y)(n,e,null),Object(Ic.c)(e,r),Object(Ic.c)(e,i),Object(Ic.c)(i,o),Object(Ic.y)(c,o,null),Object(Ic.c)(i,s),w[u].m(i,null),Object(Ic.c)(i,f),Object(Ic.c)(i,d),v=!0,m||(y=Object(Ic.x)(o,"click",qs),m=!0)},p:function(t,r){var o=Object(a.a)(r,1)[0],c={};1&o&&(c.config=t[0]),n.$set(c);var s=u;(u=j(t))!==s&&(Object(Ic.t)(),Object(Ic.K)(w[s],1,1,(function(){w[s]=null})),Object(Ic.g)(),(l=w[u])||(l=w[u]=O[u](t)).c(),Object(Ic.J)(l,1),l.m(i,f)),(!v||1&o&&h!==(h="div-gpt-ad-"+t[0].wid))&&Object(Ic.f)(d,"id",h),(!v||1&o&&p!==(p="ad-slot js-ad-slot js-ad-slot-"+t[0].wid+" ad-toolbar__ad svelte-1e19no9"))&&Object(Ic.f)(d,"class",p),(!v||1&o&&g!==(g="ad-toolbar loading "+("tasty"===t[0].zone1?"ad-toolbar--tasty":"")+" svelte-1e19no9"))&&Object(Ic.f)(i,"class",g),(!v||1&o&&b!==(b="BF_WIDGET_"+t[0].wid))&&Object(Ic.f)(e,"id",b)},i:function(t){v||(Object(Ic.J)(n.$$.fragment,t),Object(Ic.J)(c.$$.fragment,t),Object(Ic.J)(l),v=!0)},o:function(t){Object(Ic.K)(n.$$.fragment,t),Object(Ic.K)(c.$$.fragment,t),Object(Ic.K)(l),v=!1},d:function(t){t&&Object(Ic.l)(e),Object(Ic.j)(n),Object(Ic.j)(c),w[u].d(),m=!1,y()}}}function qs(t){if(t&&t.target){var e=t.target.closest(".ad-toolbar");e.classList.contains("open")?e.classList.replace("open","close"):e.classList.contains("open--no-animate")?e.classList.replace("open--no-animate","close"):e.classList.contains("close")?e.classList.replace("close","open"):e.classList.add("close")}}function Ys(t,e,n){var r=e.slot;return window.document.body.classList.add("has-tb-ads"),t.$$set=function(t){"slot"in t&&n(0,r=t.slot)},[r]}var Js=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,Ys,Ws,Ic.B,{slot:0},Us),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function Qs(t){Object(Ic.d)(t,"svelte-b70k0l",".ad-big-toolbar.loading.svelte-b70k0l{position:fixed;right:-950px}")}function Ks(t){var e,n,r,i,o,a,c;return r=new ks({}),a=new Ds({}),{c:function(){e=Object(Ic.m)("a"),n=Object(Ic.m)("div"),Object(Ic.h)(r.$$.fragment),i=Object(Ic.E)(),o=Object(Ic.m)("div"),Object(Ic.h)(a.$$.fragment),Object(Ic.f)(n,"class","ad-toolbar__bflogo"),Object(Ic.f)(o,"class","ad-toolbar__badges"),Object(Ic.f)(e,"href","https://www.buzzfeed.com?origin=tb"),Object(Ic.f)(e,"class","ad-toolbar__images")},m:function(t,s){Object(Ic.v)(t,e,s),Object(Ic.c)(e,n),Object(Ic.y)(r,n,null),Object(Ic.c)(e,i),Object(Ic.c)(e,o),Object(Ic.y)(a,o,null),c=!0},i:function(t){c||(Object(Ic.J)(r.$$.fragment,t),Object(Ic.J)(a.$$.fragment,t),c=!0)},o:function(t){Object(Ic.K)(r.$$.fragment,t),Object(Ic.K)(a.$$.fragment,t),c=!1},d:function(t){t&&Object(Ic.l)(e),Object(Ic.j)(r),Object(Ic.j)(a)}}}function Xs(t){var e,n,r;return n=new Ss({props:{width:"145",height:"92"}}),{c:function(){e=Object(Ic.m)("a"),Object(Ic.h)(n.$$.fragment),Object(Ic.f)(e,"href","https://www.buzzfeednews.com?origin=tb"),Object(Ic.f)(e,"class","ad-toolbar__images--bfn")},m:function(t,i){Object(Ic.v)(t,e,i),Object(Ic.y)(n,e,null),r=!0},i:function(t){r||(Object(Ic.J)(n.$$.fragment,t),r=!0)},o:function(t){Object(Ic.K)(n.$$.fragment,t),r=!1},d:function(t){t&&Object(Ic.l)(e),Object(Ic.j)(n)}}}function $s(t){var e,n,r;return n=new Ls({}),{c:function(){e=Object(Ic.m)("a"),Object(Ic.h)(n.$$.fragment),Object(Ic.f)(e,"href","https://tasty.co?origin=tb"),Object(Ic.f)(e,"class","ad-toolbar__images--tasty")},m:function(t,i){Object(Ic.v)(t,e,i),Object(Ic.y)(n,e,null),r=!0},i:function(t){r||(Object(Ic.J)(n.$$.fragment,t),r=!0)},o:function(t){Object(Ic.K)(n.$$.fragment,t),r=!1},d:function(t){t&&Object(Ic.l)(e),Object(Ic.j)(n)}}}function tu(t){var e,n,r,i,o,c,s,u,l,f,d,h,p,g,b,v,m,y;n=new Rs({props:{config:t[0]}}),c=new Fs({props:{title:"Right Arrow"}});var O=[$s,Xs,Ks],w=[];function j(t,e){return"tasty"===t[0].zone1?0:"bfnews"===t[0].zone1?1:2}return u=j(t),l=w[u]=O[u](t),{c:function(){e=Object(Ic.m)("div"),Object(Ic.h)(n.$$.fragment),r=Object(Ic.E)(),i=Object(Ic.m)("div"),o=Object(Ic.m)("div"),Object(Ic.h)(c.$$.fragment),s=Object(Ic.E)(),l.c(),f=Object(Ic.E)(),d=Object(Ic.m)("div"),Object(Ic.f)(o,"class","ad-toolbar__arrow"),Object(Ic.f)(d,"id",h="div-gpt-ad-"+t[0].wid),Object(Ic.f)(d,"class",p="ad-slot js-ad-slot js-ad-slot-"+t[0].wid+" ad-toolbar__ad svelte-b70k0l"),Object(Ic.f)(i,"class",g="ad-toolbar ad-big-toolbar loading "+("tasty"===t[0].zone1?"ad-toolbar--tasty":"")+" svelte-b70k0l"),Object(Ic.f)(e,"id",b="BF_WIDGET_"+t[0].wid),Object(Ic.f)(e,"data-module","ad-toolbar"),Object(Ic.f)(e,"data-bfa","@l:Toolbar;")},m:function(t,a){Object(Ic.v)(t,e,a),Object(Ic.y)(n,e,null),Object(Ic.c)(e,r),Object(Ic.c)(e,i),Object(Ic.c)(i,o),Object(Ic.y)(c,o,null),Object(Ic.c)(i,s),w[u].m(i,null),Object(Ic.c)(i,f),Object(Ic.c)(i,d),v=!0,m||(y=Object(Ic.x)(o,"click",eu),m=!0)},p:function(t,r){var o=Object(a.a)(r,1)[0],c={};1&o&&(c.config=t[0]),n.$set(c);var s=u;(u=j(t))!==s&&(Object(Ic.t)(),Object(Ic.K)(w[s],1,1,(function(){w[s]=null})),Object(Ic.g)(),(l=w[u])||(l=w[u]=O[u](t)).c(),Object(Ic.J)(l,1),l.m(i,f)),(!v||1&o&&h!==(h="div-gpt-ad-"+t[0].wid))&&Object(Ic.f)(d,"id",h),(!v||1&o&&p!==(p="ad-slot js-ad-slot js-ad-slot-"+t[0].wid+" ad-toolbar__ad svelte-b70k0l"))&&Object(Ic.f)(d,"class",p),(!v||1&o&&g!==(g="ad-toolbar ad-big-toolbar loading "+("tasty"===t[0].zone1?"ad-toolbar--tasty":"")+" svelte-b70k0l"))&&Object(Ic.f)(i,"class",g),(!v||1&o&&b!==(b="BF_WIDGET_"+t[0].wid))&&Object(Ic.f)(e,"id",b)},i:function(t){v||(Object(Ic.J)(n.$$.fragment,t),Object(Ic.J)(c.$$.fragment,t),Object(Ic.J)(l),v=!0)},o:function(t){Object(Ic.K)(n.$$.fragment,t),Object(Ic.K)(c.$$.fragment,t),Object(Ic.K)(l),v=!1},d:function(t){t&&Object(Ic.l)(e),Object(Ic.j)(n),Object(Ic.j)(c),w[u].d(),m=!1,y()}}}function eu(t){if(t&&t.target){var e=t.target.closest(".ad-toolbar");e.classList.contains("open")?e.classList.replace("open","close"):e.classList.contains("open--no-animate")?e.classList.replace("open--no-animate","close"):e.classList.contains("close")?e.classList.replace("close","open"):e.classList.add("close")}}function nu(t,e,n){var r=e.slot;return window.document.body.classList.add("has-tb-ads"),t.$$set=function(t){"slot"in t&&n(0,r=t.slot)},[r]}var ru=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,nu,tu,Ic.B,{slot:0},Qs),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function iu(t){for(var e,n,r=t[3].default,i=Object(Ic.i)(r,t,t[2],null),o=[t[1]],a={},c=0;c<o.length;c+=1)a=Object(Ic.e)(a,o[c]);return{c:function(){e=Object(Ic.m)("li"),i&&i.c(),Object(Ic.C)(e,a)},m:function(t,r){Object(Ic.v)(t,e,r),i&&i.m(e,null),n=!0},p:function(t,c){i&&i.p&&(!n||4&c)&&Object(Ic.L)(i,r,t,t[2],n?Object(Ic.q)(r,t[2],c,null):Object(Ic.p)(t[2]),null),Object(Ic.C)(e,a=Object(Ic.s)(o,[2&c&&t[1]]))},i:function(t){n||(Object(Ic.J)(i,t),n=!0)},o:function(t){Object(Ic.K)(i,t),n=!1},d:function(t){t&&Object(Ic.l)(e),i&&i.d(t)}}}function ou(t){for(var e,n,r=t[3].default,i=Object(Ic.i)(r,t,t[2],null),o=[t[1]],a={},c=0;c<o.length;c+=1)a=Object(Ic.e)(a,o[c]);return{c:function(){e=Object(Ic.m)("div"),i&&i.c(),Object(Ic.C)(e,a)},m:function(t,r){Object(Ic.v)(t,e,r),i&&i.m(e,null),n=!0},p:function(t,c){i&&i.p&&(!n||4&c)&&Object(Ic.L)(i,r,t,t[2],n?Object(Ic.q)(r,t[2],c,null):Object(Ic.p)(t[2]),null),Object(Ic.C)(e,a=Object(Ic.s)(o,[2&c&&t[1]]))},i:function(t){n||(Object(Ic.J)(i,t),n=!0)},o:function(t){Object(Ic.K)(i,t),n=!1},d:function(t){t&&Object(Ic.l)(e),i&&i.d(t)}}}function au(t){var e,n,r,i,o=[ou,iu],c=[];function s(t,e){return"div"===t[0]?0:"li"===t[0]?1:-1}return~(e=s(t))&&(n=c[e]=o[e](t)),{c:function(){n&&n.c(),r=Object(Ic.n)()},m:function(t,n){~e&&c[e].m(t,n),Object(Ic.v)(t,r,n),i=!0},p:function(t,i){var u=Object(a.a)(i,1)[0],l=e;(e=s(t))===l?~e&&c[e].p(t,u):(n&&(Object(Ic.t)(),Object(Ic.K)(c[l],1,1,(function(){c[l]=null})),Object(Ic.g)()),~e?((n=c[e])?n.p(t,u):(n=c[e]=o[e](t)).c(),Object(Ic.J)(n,1),n.m(r.parentNode,r)):n=null)},i:function(t){i||(Object(Ic.J)(n),i=!0)},o:function(t){Object(Ic.K)(n),i=!1},d:function(t){~e&&c[e].d(t),t&&Object(Ic.l)(r)}}}function cu(t,e,n){var r=e.$$slots,i=void 0===r?{}:r,o=e.$$scope,a=e.tag,c=void 0===a?"div":a,s=e.attributes,u=void 0===s?null:s;return t.$$set=function(t){"tag"in t&&n(0,c=t.tag),"attributes"in t&&n(1,u=t.attributes),"$$scope"in t&&n(2,o=t.$$scope)},[c,u,o,i]}var su=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,cu,au,Ic.B,{tag:0,attributes:1}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function uu(t){var e,n=t[8].default,r=Object(Ic.i)(n,t,t[9],null);return{c:function(){r&&r.c()},m:function(t,n){r&&r.m(t,n),e=!0},p:function(t,i){r&&r.p&&(!e||512&i)&&Object(Ic.L)(r,n,t,t[9],e?Object(Ic.q)(n,t[9],i,null):Object(Ic.p)(t[9]),null)},i:function(t){e||(Object(Ic.J)(r,t),e=!0)},o:function(t){Object(Ic.K)(r,t),e=!1},d:function(t){r&&r.d(t)}}}function lu(t){var e,n;return e=new su({props:{tag:t[1],attributes:t[2],$$slots:{default:[fu]},$$scope:{ctx:t}}}),{c:function(){Object(Ic.h)(e.$$.fragment)},m:function(t,r){Object(Ic.y)(e,t,r),n=!0},p:function(t,n){var r={};2&n&&(r.tag=t[1]),4&n&&(r.attributes=t[2]),512&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i:function(t){n||(Object(Ic.J)(e.$$.fragment,t),n=!0)},o:function(t){Object(Ic.K)(e.$$.fragment,t),n=!1},d:function(t){Object(Ic.j)(e,t)}}}function fu(t){var e,n=t[8].default,r=Object(Ic.i)(n,t,t[9],null);return{c:function(){r&&r.c()},m:function(t,n){r&&r.m(t,n),e=!0},p:function(t,i){r&&r.p&&(!e||512&i)&&Object(Ic.L)(r,n,t,t[9],e?Object(Ic.q)(n,t[9],i,null):Object(Ic.p)(t[9]),null)},i:function(t){e||(Object(Ic.J)(r,t),e=!0)},o:function(t){Object(Ic.K)(r,t),e=!1},d:function(t){r&&r.d(t)}}}function du(t){var e,n,r,i,o=[lu,uu],c=[];function s(t,e){return t[0]?0:1}return e=s(t),n=c[e]=o[e](t),{c:function(){n.c(),r=Object(Ic.n)()},m:function(t,n){c[e].m(t,n),Object(Ic.v)(t,r,n),i=!0},p:function(t,i){var u=Object(a.a)(i,1)[0],l=e;(e=s(t))===l?c[e].p(t,u):(Object(Ic.t)(),Object(Ic.K)(c[l],1,1,(function(){c[l]=null})),Object(Ic.g)(),(n=c[e])?n.p(t,u):(n=c[e]=o[e](t)).c(),Object(Ic.J)(n,1),n.m(r.parentNode,r))},i:function(t){i||(Object(Ic.J)(n),i=!0)},o:function(t){Object(Ic.K)(n),i=!1},d:function(t){c[e].d(t),t&&Object(Ic.l)(r)}}}function hu(t,e,n){var r,i,o=e.$$slots,a=void 0===o?{}:o,c=e.$$scope,s=e.hasWireframe,u=void 0!==s&&s,l=e.width,f=e.height,d=e.page,h=void 0===d?null:d,p=e.isListItem,g=void 0!==p&&p,b=e.cssClasses,v=void 0===b?function(t){return t}:b;return t.$$set=function(t){"hasWireframe"in t&&n(0,u=t.hasWireframe),"width"in t&&n(3,l=t.width),"height"in t&&n(4,f=t.height),"page"in t&&n(5,h=t.page),"isListItem"in t&&n(6,g=t.isListItem),"cssClasses"in t&&n(7,v=t.cssClasses),"$$scope"in t&&n(9,c=t.$$scope)},t.$$.update=function(){248&t.$$.dirty&&(n(1,r=g?"li":"div"),n(2,i={"data-wireframe-width":l,"data-wireframe-height":f,class:"ad-wireframe-wrapper ".concat(h?"ad-wireframe-wrapper--"+h:""," ").concat(v("ad-wireframe-wrapper--labeled"))}))},[u,r,i,l,f,h,g,v,a,c]}var pu=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,hu,du,Ic.B,{hasWireframe:0,width:3,height:4,page:5,isListItem:6,cssClasses:7}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function gu(t){var e,n,r,i,o,a,c;return n=new Rs({props:{config:t[0]}}),{c:function(){e=Object(Ic.m)("div"),Object(Ic.h)(n.$$.fragment),r=Object(Ic.E)(),i=Object(Ic.m)("div"),Object(Ic.f)(i,"class","awareness-bg-animator"),Object(Ic.f)(e,"id",o="BF_WIDGET_"+t[0].wid),Object(Ic.f)(e,"data-module","ad-awareness"),Object(Ic.f)(e,"class",a="ad-awareness ad-flexible js-ad ad-awareness--full-width "+(t[1]?"":"ad-fade ad-animated ad-fadedown ad-awareness--legacy-wf")+" "+(t[4]?"ad-awareness--".concat(t[4]):"")+" "+(t[0].zone1?"ad-dest--".concat(t[0].zone1):"")),Object(Ic.f)(e,"data-bfa","@l:Awareness;")},m:function(t,o){Object(Ic.v)(t,e,o),Object(Ic.y)(n,e,null),Object(Ic.c)(e,r),Object(Ic.c)(e,i),c=!0},p:function(t,r){var i={};1&r&&(i.config=t[0]),n.$set(i),(!c||1&r&&o!==(o="BF_WIDGET_"+t[0].wid))&&Object(Ic.f)(e,"id",o),(!c||19&r&&a!==(a="ad-awareness ad-flexible js-ad ad-awareness--full-width "+(t[1]?"":"ad-fade ad-animated ad-fadedown ad-awareness--legacy-wf")+" "+(t[4]?"ad-awareness--".concat(t[4]):"")+" "+(t[0].zone1?"ad-dest--".concat(t[0].zone1):"")))&&Object(Ic.f)(e,"class",a)},i:function(t){c||(Object(Ic.J)(n.$$.fragment,t),c=!0)},o:function(t){Object(Ic.K)(n.$$.fragment,t),c=!1},d:function(t){t&&Object(Ic.l)(e),Object(Ic.j)(n)}}}function bu(t){var e,n;return e=new pu({props:{hasWireframe:t[1],width:t[2],height:t[3],cssClasses:vu,$$slots:{default:[gu]},$$scope:{ctx:t}}}),{c:function(){Object(Ic.h)(e.$$.fragment)},m:function(t,r){Object(Ic.y)(e,t,r),n=!0},p:function(t,n){var r=Object(a.a)(n,1)[0],i={};2&r&&(i.hasWireframe=t[1]),4&r&&(i.width=t[2]),8&r&&(i.height=t[3]),51&r&&(i.$$scope={dirty:r,ctx:t}),e.$set(i)},i:function(t){n||(Object(Ic.J)(e.$$.fragment,t),n=!0)},o:function(t){Object(Ic.K)(e.$$.fragment,t),n=!1},d:function(t){Object(Ic.j)(e,t)}}}var vu=function(){return"ad-wireframe-wrapper--nostick ad-wireframe-wrapper--awareness"};function mu(t,e,n){var r=e.slot,i=e.programmaticWireframes,o=e.wireframeWidth,a=e.wireframeHeight,c=e.page;return t.$$set=function(t){"slot"in t&&n(0,r=t.slot),"programmaticWireframes"in t&&n(1,i=t.programmaticWireframes),"wireframeWidth"in t&&n(2,o=t.wireframeWidth),"wireframeHeight"in t&&n(3,a=t.wireframeHeight),"page"in t&&n(4,c=t.page)},[r,i,o,a,c]}var yu=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,mu,bu,Ic.B,{slot:0,programmaticWireframes:1,wireframeWidth:2,wireframeHeight:3,page:4}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function Ou(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function wu(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Ou(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ou(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function ju(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}var Au=function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments)).config.refreshOptions=wu({},gs),t.context.abeagle.getExperimentVariant("RT-994-swap-refresh",{rejectErrors:!1,defaultVariantIfUnbucketed:"control"}).then((function(e){"swap-refresh"===e?(t.config.refreshOptions.inViewSeconds=15,t.plugins.add(Lu)):t.plugins.add(As)})),t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"setupSizes",value:function(){try{var t=this,n=yr.isAny(["md","lg"]);return ju(function(t,e){var n=t();if(n&&n.then)return n.then(e);return e(n)}((function(){if(n&&t.context.env.isBFN&&!t.context.env.isBPage)return ju(t.context.abeagle.getExperimentVariant("RT-945-tb-size-bfn-desktop",{rejectErrors:!1,defaultVariantIfUnbucketed:"control"}),(function(e){"tb_728x90"===e&&(t.excludeSize(t.context.env.adSizes.PROGRAMMATIC_SMARTPHONE_BANNER_SHORT,t.context.env.adSizes.PROGRAMMATIC_SMARTPHONE_BANNER),t.addSize(t.context.env.adSizes.PROGRAMMATIC_LEADERBOARD),t.element.querySelector(".ad-toolbar").classList.add("ad-big-toolbar"))}))}),(function(){Object(fi.e)(Object(fi.f)(e.prototype),"setupSizes",t).call(t)})))}catch(r){return Promise.reject(r)}}},{key:"handleSlotRenderEnded",value:function(){var t=this.config.wid;if(this.element&&"object"===typeof this.element){var e=this.element.querySelector(".ad-toolbar.loading"),n=this.element.querySelector(".ad-toolbar.close"),r="string"===typeof t?"open--no-animate":"open";e&&"object"===typeof e&&e.classList.replace("loading",r),e&&"object"===typeof n&&e.classList.replace("close",r)}}},{key:"removeToolbarAd",value:function(){if(this.element&&"object"===typeof this.element){var t=this.element.querySelector(".ad-toolbar");t&&"object"===typeof t&&t.remove()}}},{key:"isEnabled",value:function(){try{var t=this;return yr.isAny(["md","lg"])?ju(t.context.abeagle.isOn("ads_toolbar_bpages"),(function(e){return ju(t.context.abeagle.isOn("ads_toolbar_feeds"),(function(n){return ju(t.context.abeagle.isOn("ads_toolbar_tasty"),(function(r){return ju(t.context.abeagle.isOn("ads_toolbar_bfn"),(function(i){return e||n||r||i||t.removeToolbarAd(),e||n||r||i}))}))}))})):(t.removeToolbarAd(),ju(!1))}catch(e){return Promise.reject(e)}}}])}(ns.withMixins(fs));function Eu(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function _u(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Eu(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Eu(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Pu(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}var Ru={awareness:yu,toolbar:Js};function xu(){}var Tu=hs.default;var ku=hs.globalOverride;function Cu(t){var e=t();if(e&&e.then)return e.then(xu)}var Lu=function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments)).refreshConfig={},t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"setup",value:function(){try{var t=this;return Pu(t.isEnabled(),(function(n){if(n){var r,i,o=t.config.wid;t.complete=!1,t.creativeId=new Pr,t.emptyCount=0,t.impressionCount=0,t.impressionViewHit=!1,t.inViewPercent=0,t.isCurrentlyEmpty=!1,t.paused=!1,t.refreshCount=0,t.timeout=null,t.currentPage=-1,t.pages=[],t.elements=[],t.context.abeagle.getExperimentVariant("RT-945-tb-size-bfn-desktop",{rejectErrors:!1,defaultVariantIfUnbucketed:"control"}).then((function(t){"tb_728x90"===t&&(Ru.toolbar=ru)})),t.refreshConfig=_u(_u({},Tu),t.config.refreshOptions);var a=((null===(r=t.context)||void 0===r?void 0:r.localization)||(null===(i=t.context)||void 0===i||null===(i=i.env)||void 0===i?void 0:i.localization)||{}).edition;ku.edition[a]&&(t.refreshConfig=_u(_u({},t.refreshConfig),ku.edition[a]),Dr("info","lifecycle","SwapRefresh ".concat(o," : EDITION OVERRIDES -> updated config ->"),t.refreshConfig)),t.refreshConfig.pages.forEach((function(e,n){t.pages[n]=_u(_u({},t.config),t.config.refreshOptions.pages[n]),"awareness"===t.config.adType&&t.context.env.isBPage&&(t.pages[n].adPos+="-bp",t.pages[n].targeting.pos+="-bp"),t.pages[n].targeting.refreshable=!0,t.pages[n].targeting.tbr=0,t.addListeners(e.wid)})),t.addListeners(o),t.onPublicEvent("post-message:creativeSnippet",t.handleSnippetMessage.bind(t)),Dr("info","lifecycle","SwapRefresh ".concat(o," : Pages:"),t.pages)}else t.destroy();Object(fi.e)(Object(fi.f)(e.prototype),"setup",t).call(t)}))}catch(n){return Promise.reject(n)}}},{key:"isViewable",value:function(){return this.inViewPercent>=50}},{key:"refreshAd",value:function(){try{var t=this,e=(-1===t.currentPage?t.config:t.pages[t.currentPage]).wid;return Pu(Cu((function(){if(t.isViewable())return Cu((function(){var n;if(null===(n=t.scrollFetchInfo)||void 0===n||!n.isFast){var r=t.currentPage;return t.currentPage=t.currentPage===t.pages.length-1?0:t.currentPage+1,Dr("info","lifecycle","SwapRefresh ".concat(e," :"),"Page ".concat(t.currentPage+1,"/").concat(t.pages.length),t.debugInfo),function(t,e){var n=t();return n&&n.then?n.then(e):e(n)}((function(){if(!t.elements[t.currentPage]){var e={template:Ru[t.config.adType],props:{slot:t.pages[t.currentPage],isListItem:!1}},n=-1===r?t.element:t.elements[r];Ya(_u(_u({},e),{},{target:n.parentElement,anchor:n.nextElementSibling}));var i=n.nextElementSibling;return t.elements[t.currentPage]=i,i.classList.add("ad--refreshable"),new("awareness"===(o=t.config.adType)?Fl:"toolbar"===o?Au:null)(_u(_u({},t.context),{},{config:t.pages[t.currentPage],element:i})).init(),function(t,e){if(!e)return t&&t.then?t.then(xu):Promise.resolve()}(ma.renderWidget(_u({},t.pages[t.currentPage])))}var o;ma.refreshWidget(t.pages[t.currentPage]),t.elements[t.currentPage].classList.remove("ad--collapse-vertical")}),(function(){-1===r?t.element.remove():t.elements[r].classList.add("ad--collapse-vertical"),t.eventBus.trigger("ad-refresh:".concat(e)),t.refreshCount++,Dr("info","lifecycle","SwapRefresh ".concat(e," :"),"Refreshed!",t.debugInfo)}))}Dr("info","lifecycle","SwapRefresh ".concat(e," :"),"Did not refresh (users scroll too fast, speed is ".concat(t.scrollFetchInfo.speed),t.debugInfo)}))})))}catch(n){return Promise.reject(n)}}},{key:"refreshEmptyAd",value:function(){var t,e=this,n=-1===this.currentPage?this.config:this.pages[this.currentPage],r=n.wid;this.emptyCount++,Dr("info","lifecycle","SwapRefresh ".concat(r," : ad came up empty"),_u({emptyCount:this.emptyCount},this.debugInfo)),this.eventBus.trigger("ad-refresh-reset:".concat(r)),this.timeout=setTimeout((t=function(){return ma.refreshWidget(n),e.timeout=null,Pu()},function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(t.apply(this,e))}catch(r){return Promise.reject(r)}}),1e4),this.emptyCount>5&&(Dr("info","lifecycle","SwapRefresh ".concat(r," : Reached max number of empty refesh attempts. Stopping."),this.debugInfo),this.destroy())}},{key:"handleAdContentReadyForRefresh",value:function(t){var e=t.type,n=(-1===this.currentPage?this.config:this.pages[this.currentPage]).wid;Dr("info","lifecycle","SwapRefresh ".concat(n," : type = ").concat(e)),"empty"===e?this.refreshEmptyAd():"programmatic"!==e?(Dr("info","lifecycle","SwapRefresh ".concat(n," : Destroy refresh b/c ad type ").concat(e," is not eligible")),this.destroy()):Dr("info","lifecycle","SwapRefresh ".concat(n," : enabled!"),_u({config:this.refreshConfig},this.debugInfo))}},{key:"handleImpressionViewableForRefresh",value:function(t){var e=this,n=(-1===this.currentPage?this.config:this.pages[this.currentPage]).wid;this.impressionViewHit=!0,this.impressionCount++;var r=this.refreshConfig.inViewSeconds,i=this.refreshConfig.maxRefreshes;this.refreshConfig.infinite&&(i=9999),this.paused?Dr("info","lifecycle","SwapRefresh ".concat(n," Impression viewed, but paused, will refresh later.")):(Dr("info","lifecycle","SwapRefresh ".concat(n," :"),"Impression Viewable",_u(_u({},this.debugInfo),{},{gptEv:t})),this.refreshCount<i&&!this.timeout?(Dr("info","lifecycle","SwapRefresh ".concat(n," :"),"Timer started... (".concat(r," seconds)")),this.timeout=setTimeout((function(){e.timeout=null,e.refreshAd()}),1e3*r)):this.refreshCount>=i&&(this.complete=!0,Dr("info","lifecycle","SwapRefresh ".concat(n," :"),"Max refreshes hit. Stopping."),this.destroy()))}},{key:"handleSlotRenderEndedRefresh",value:function(t){var e=(-1===this.currentPage?this.config:this.pages[this.currentPage]).wid;t.isEmpty?(Dr("info","lifecycle","SwapRefresh ".concat(e," : empty ad trying again"),this.debugInfo),this.isCurrentlyEmpty=!0,this.refreshEmptyAd()):this.context.element&&(Dr("info","lifecycle","SwapRefresh ".concat(e," : SlotRenderEnded -> Updating creativeId = ").concat(t.creativeId)),this.creativeId=Promise.resolve(t.creativeId),this.emptyCount=0,this.isCurrentlyEmpty=!1,this.context.element.classList.remove("ad-flexible--empty"),this.context.element.classList.add("ad-flexible--programmatic"),this.context.parentElement&&this.context.element.parentElement.classList.remove("Ad--unfilled"))}},{key:"handSlotVisChangeForRefresh",value:function(t){var e=(-1===this.currentPage?this.config:this.pages[this.currentPage]).wid,n=t.inViewPercentage;this.inViewPercent=n,Dr("info","lifecycle","SwapRefresh ".concat(e," :"),"SlotVisibilityChanged",this.debugInfo),!this.timeout&&this.isViewable()&&this.impressionCount>this.refreshCount&&!this.isCurrentlyEmpty?(Dr("info","lifecycle","SwapRefresh ".concat(e," :"),"Impression/Refresh Count mismatch due to ad moving out of view. Attempting...",this.debugInfo),this.refreshAd()):!this.timeout&&this.isCurrentlyEmpty&&this.refreshEmptyAd()}},{key:"handleSnippetMessage",value:function(t){try{var e=this,n=(-1===e.currentPage?e.config:e.pages[e.currentPage]).wid,r=t.action,i=t.data.creativeId;return Pu(e.creativeId,(function(o){"".concat(o)===i&&"stopRefresh"===r?(Dr("info","lifecycle","SwapRefresh ".concat(n," -> Disabled by creative: ").concat(i),e.debugInfo),e.destroy()):"".concat(o)===i&&"pauseRefresh"===r&&(Dr("info","lifecycle","SwapRefresh ".concat(n," -> Paused by creative; waiting ").concat(t.data.seconds," seconds")),e.paused=!0,e.timeout=setTimeout((function(){e.paused=!1,e.timeout=null,Dr("info","lifecycle","SwapRefresh ".concat(n," -> Pause ended, refreshing now!")),e.refreshAd()}),1e3*(t.data.seconds||30)))}))}catch(o){return Promise.reject(o)}}},{key:"isEnabled",value:function(){var t=this,n=!0;return this.config.refreshOptions.pages.forEach((function(e){var r=e.wid;t.config.wid===r&&(n=!1)})),Promise.all([n,Object(fi.e)(Object(fi.f)(e.prototype),"isEnabled",this).call(this),this.config.refreshOptions,!this.complete])}},{key:"addListeners",value:function(t){this.onPublicEvent("ad-content-ready:".concat(t),this.handleAdContentReadyForRefresh.bind(this)),this.onPublicEvent("gpt:slotRenderEnded:".concat(t),this.handleSlotRenderEndedRefresh.bind(this)),this.onPublicEvent("gpt:impressionViewable:".concat(t),this.handleImpressionViewableForRefresh.bind(this)),this.onPublicEvent("gpt:slotVisibilityChanged:".concat(t),this.handSlotVisChangeForRefresh.bind(this))}},{key:"removeListeners",value:function(t){this.eventBus.off("ad-content-ready:".concat(t)),this.eventBus.off("gpt:impressionViewable:".concat(t)),this.eventBus.off("gpt:slotRenderEnded:".concat(t)),this.eventBus.off("gpt:slotVisibilityChanged:".concat(t))}},{key:"destroy",value:function(){var t=this,n=this.config.wid;ma.clearTargeting(n,["refreshable"]),Dr("info","lifecycle","SwapRefresh ".concat(n," :"),"Destroying listeners/timers & disabling refresh."),this.removeListeners(n),this.pages.forEach((function(e){t.removeListeners(e.wid)})),this.eventBus.off("post-message:creativeSnippet"),delete this.config.refreshOptions,Object(fi.e)(Object(fi.f)(e.prototype),"destroy",this).call(this)}},{key:"debugInfo",get:function(){return{currentPage:"".concat(this.currentPage+1),creativeId:this.creativeId,inViewPercent:this.inViewPercent,impressionCount:this.impressionCount,refreshCount:this.refreshCount,timeout:this.timeout}}}])}(Ka);function Iu(t){var e,n,r;return{c:function(){e=Object(Ic.m)("div"),(n=Object(Ic.m)("h2")).textContent="".concat(Sc.ADVERTISEMENT),Object(Ic.f)(n,"class","ad__disclosure--bfp js-ad-disclosure"),Object(Ic.f)(e,"id",r="ad-bfp-promo-"+t[0]),Object(Ic.f)(e,"class","ad-bfp-promo-container")},m:function(t,r){Object(Ic.v)(t,e,r),Object(Ic.c)(e,n)},p:function(t,n){1&Object(a.a)(n,1)[0]&&r!==(r="ad-bfp-promo-"+t[0])&&Object(Ic.f)(e,"id",r)},i:Ic.z,o:Ic.z,d:function(t){t&&Object(Ic.l)(e)}}}function Mu(t,e,n){var r=e.creativeId;return t.$$set=function(t){"creativeId"in t&&n(0,r=t.creativeId)},[r]}var Su=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,Mu,Iu,Ic.B,{creativeId:0}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function zu(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Nu(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?zu(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):zu(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var Du={loadScript:Dn.a},Bu=function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments)).template=Su,t.setDebugSettings(),t.eligibleFormats=["bfp_ad_display_card_embed","bfp_display_card_quiz","bfp_product_carousel","bfp_shopping_showcase","bfp_spotlight_ad","bfp_spotlight_unit","bfp_spotlight_turnkey","bfp_spotlight_newsletter","bfp_trending_products","bfp_shoppable_video_ad","bfp_bright_playlist"],t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"formatContainer",get:function(){return"bfp_spotlight_unit"===this.context.ad.bfpFormatName||"bfp_spotlight_ad"===this.context.ad.bfpFormatName?this.element.querySelector(".js-format-container"):this.element}},{key:"setDebugSettings",value:function(){if(this.debug={on:/\bads-debug-bfp\b/.test(window.location.search)},this.debug.on){var t=Object(Bn.c)(window.location.search);this.debug.format=t["ads-debug-bfp"],this.debug.scriptHash=t.hash}}},{key:"getRenderKitURL",value:function(t,e){if(!this.debug||t!==this.debug.format)return e;if("dev"===this.debug.scriptHash){var n=t.replace(/_/g,"-"),r=t.replace(/^bfp_/,"");return"https://".concat(n,".dev.buzzfeed.io/static-assets/buzz-format-platform/").concat(r,"/js/render_kit.js")}return e.replace(/(render_kit.)([a-f0-9]{10,})(.js)/,"$1".concat(this.debug.scriptHash,"$3"))}},{key:"loadRenderKit",value:function(t,e){return Du.loadScript(this.getRenderKitURL(t,e))}},{key:"buildFormat",value:function(){var t=arguments,n=this,r=this.context.ad,i=r.bfpFormatName,o=r.bfpScript,a=this.eligibleFormats.indexOf(i)>-1;return o&&i&&a?(this.bfpFormatName=i,this.loadRenderKit(i,o).then((function(){return Object(fi.e)(Object(fi.f)(e.prototype),"buildFormat",n).apply(n,t)})).then((function(){return n.initBFP()})).catch((function(t){console.error(t),n.destroy()}))):(this.destroy(),null)}},{key:"initBFP",value:function(){this.element.classList.add("ad-flexible--".concat(this.context.ad.bfpFormatName));var t=window[this.bfpFormatName],e={eventBus:this.context.eventBus,tracking:this.context.tracking,trackingData:this.context.trackingData||{}},n=this.bfpInstance=t.init({element:this.element.querySelector("#ad-bfp-promo-".concat(this.context.ad.creativeId)),rootElement:this.element,context:e,layout:this.context.getBFPLayout?this.context.getBFPLayout(this.bfpFormatName,this.config):null,slotConfig:this.config,gamData:this.context.ad,bfpData:this.context.ad.bfpData,config:{data:this.context.ad.bfpData,context:Nu({ad:this.context.ad},e)}});this.setupDFPClickTracker(),this.eventBus.trigger("ad-content-rendered:".concat(this.config.wid)),this.eventBus.trigger("bfp:init-done:".concat(this.config.wid),n)}},{key:"setupDFPClickTracker",value:function(){var t=this.context.ad,e=t.dfpClickTracker,n=t.clickThroughUrl;this.element.querySelectorAll("a").forEach((function(t){var r=t.getAttribute("href")?t.href:n;r.startsWith(e)||(t.href=Mc(r,e),t.target="_blank")}))}},{key:"destroy",value:function(){this.eventBus.trigger("ad-content-error:".concat(this.config.wid)),Object(fi.e)(Object(fi.f)(e.prototype),"destroy",this).call(this),this.bfpInstance&&this.bfpInstance.destroy&&(this.bfpInstance.destroy(),delete this.bfpInstance)}}])}(Lc);Object(fi.d)(Bu,"formatType","bfp_promo");var Gu=function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments)).nativeVideoEnded=!1,t.eligibleFormats=["bfp_native_instream_video","bfp_spotlight_unit"],t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"buildFormat",value:function(){this.context.ad.type="video",Object(fi.e)(Object(fi.f)(e.prototype),"buildFormat",this).call(this)}},{key:"initBFP",value:function(){Object(fi.e)(Object(fi.f)(e.prototype),"initBFP",this).call(this),this.eventBus.trigger("ad-tpl:".concat(this.config.wid),{type:Vr})}},{key:"blocksSidebar",value:function(){var t="bfp_native_instream_video"===this.bfpFormatName,e=this.config.adPos.match(/sidebar/);return t&&e}},{key:"reportQuartile",value:function(t){if(!this.nativeVideoEnded){var e=this.config.wid;this.eventBus.trigger("ad-native-video-quartile:".concat(e)),4===t.quartile&&(this.blocksSidebar()||this.eventBus.trigger("ad-native-video-ended:".concat(e)),this.nativeVideoEnded=!0),this.eventBus.trigger("native-video-embed:playback-quartile:".concat(e),t)}}},{key:"reportPause",value:function(t){this.nativeVideoEnded||(this.blocksSidebar()||this.eventBus.trigger("ad-native-video-pause:".concat(this.config.wid)),this.eventBus.trigger("dfp-native-video:pause:".concat(this.config.wid),t))}}])}(Bu.withMixins(as));Object(fi.d)(Gu,"formatType","bfp_video");var Fu=function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments)).eligibleFormats=["bfp_spotlight_unit","bfp_spotlight_ad","bfp_spotlight_turnkey","bfp_spotlight_newsletter","bfp_display_card_quiz","bfp_spotlight_poll"],t}return Object(fi.g)(e,t),Object(fi.c)(e)}(Bu),Uu=0,Vu={},Hu=function(){return Object(fe.a)((function t(e){var n=this,r=e.throttleTimeout,i=e.parent,o=e.initialTrigger;Object(le.a)(this,t),this.callbacks={},this.add=this.add.bind(this),this.remove=this.remove.bind(this),this.triggerEvents=["scroll","resize"],o&&this.triggerEvents.push("load"),this.target=i||window,this._triggerThrottled=Object(Hn.throttle)((function(){return n.trigger()}),r),this.triggerEvents.forEach((function(t){return n.target.addEventListener(t,n._triggerThrottled)}))}),[{key:"add",value:function(t){return this.callbacks[++Uu]=t,Uu}},{key:"remove",value:function(t){delete this.callbacks[t]}},{key:"trigger",value:function(t){if(this.callbacks.hasOwnProperty(t))this.callbacks[t]();else for(var e in this.callbacks)this.callbacks.hasOwnProperty(e)&&this.callbacks[e]()}},{key:"destroy",value:function(){var t=this;this.triggerEvents.forEach((function(e){return t.target.removeEventListener(e,t._triggerThrottled)})),delete this.target,delete this.callbacks,delete this._triggerThrottled,delete this.add,delete this.remove}}])}(),Zu=function(t){var e=t.throttleTimeout,n=void 0===e?350:e,r=t.parent,i=t.callback,o=t.initialTrigger,a=void 0===o||o,c=Vu[n];c||(c=new Hu({throttleTimeout:n,parent:r,initialTrigger:a}),Vu[n]=c);var s=c.add(i);return a&&c.trigger(s),s},Wu=function(t){for(var e in Vu)Vu.hasOwnProperty(e)&&Vu[e].remove(t)};function qu(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Yu(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?qu(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):qu(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var Ju="".concat("stick-to-top","--init"),Qu=("".concat("stick-to-top","--state"),function(){return Object(fe.a)((function t(e){Object(le.a)(this,t),this.context=e,this.element=e.getElement(),this.stickyConfig=Yu(Yu({},{breakpoints:["ALL"],positionMode:"top",directions:["up","down"]}),(e.getConfig()||{}).stickToTop),this.isInitialized=!1,this.canStick=!0,this.isFixed=!1,this.pageScrollPosition=window.scrollY,this.onmessage=Object(o.default)({},jr(Ju,this.element),this.initSticky)}),[{key:"stickyRegistryOpts",get:function(){return{priority:"priority"in this.stickyConfig?this.stickyConfig.priority:this.context.stickyRegistry.defaultPriorities.normal}}},{key:"init",value:function(){this.stickyConfig.initOnMsg||this.initSticky()}},{key:"initSticky",value:function(t){if(!this.isInitialized){this.isInitialized=!0,this.stickyConfig=Yu(Yu({},this.stickyConfig),t),this.element.classList.add("xs-relative","sticky");var e=this.filler=document.createElement("div");e.style.height="".concat(this.element.offsetHeight,"px"),e.className="sticky-filler xs-static xs-hide",this.element.insertAdjacentElement("afterend",e),this._checkEligibilityBound=this.checkEligibility.bind(this),-1===this.stickyConfig.breakpoints.indexOf("ALL")&&(this.context.eventManager.on("match",this._checkEligibilityBound),yr.breakpointObserver.subscribe(this.context.eventManager)),this.checkEligibility(),this._adjustFixedPositionBound=this.adjustFixedPosition.bind(this),this.context.stickyRegistry.subscribe(this._adjustFixedPositionBound)}}},{key:"manageSticky",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.force,n=void 0!==e&&e,r=t.stickMod,i=void 0===r?0:r,o=t.unstickMod,a=void 0===o?0:o;if(this.canStick){var c=this.getPosition(),s=c.inViewport,u=window.scrollY,l=u-this.pageScrollPosition>=10,f=this.pageScrollPosition-u>=10,d=!1;(l&&this.stickyConfig.directions.includes("down")||f&&this.stickyConfig.directions.includes("up"))&&(d=!0),(!this.isFixed||n)&&s<this.fixAt+i&&d?this.stick():(this.isFixed||n)&&s>=this.fixAt+a?this.unstick():!this.isFixed&&!n||d||!f&&!l||this.unstick(),this.pageScrollPosition=u}}},{key:"adjustFixedPosition",value:function(t){if(!(t.priority<this.stickyRegistryOpts.priority)&&this.canStick){var e=this.context.stickyRegistry.getAvailableTop(this.element,this.stickyRegistryOpts);e!==this.fixAt&&(this.fixAt=e,this.manageSticky({force:!0}))}}},{key:"checkEligibility",value:function(){var t,e=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.breakpoint;this.stickyConfig.breakpoints.indexOf("ALL")>-1?t=!0:(r||(r=yr.getBreakPoint()),t=this.stickyConfig.breakpoints.indexOf(r)>-1),t?(this.canStick=!0,this.fixAt=this.context.stickyRegistry.getAvailableTop(this.element,this.stickyRegistryOpts),this.scrollListenerId=Zu({throttleTimeout:150,callback:function(){return e.manageSticky()}})):(this.canStick=!1,Wu(this.scrollListenerId),this.unstick())}},{key:"getPosition",value:function(){var t=(this.isFixed?this.filler:this.element).getBoundingClientRect().top;return{inViewport:t,inDocument:t+window.pageYOffset}}},{key:"setPosition",value:function(t){if("translate"===this.stickyConfig.positionMode){var e="translateY(".concat(t,"px)");this.element.style.WebkitTransform=e,this.element.style.transform=e}else this.element.style.top="".concat(t,"px")}},{key:"toggleFiller",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"stick";"stick"===t&&this.filler?this.filler.classList.remove("xs-hide"):"unstick"===t&&this.filler&&this.filler.classList.add("xs-hide")}},{key:"stick",value:function(){this.isFixed=!0,this.toggleFiller("stick"),this.element.classList.add("xs-fixed","sticky--fixed"),this.setPosition(this.fixAt),this.context.stickyRegistry.add(this.element,this.stickyRegistryOpts)}},{key:"unstick",value:function(){this.isFixed=!1,this.toggleFiller("unstick"),this.element.classList.remove("xs-fixed","sticky--fixed"),this.setPosition(0),this.context.stickyRegistry.remove(this.element)}},{key:"destroy",value:function(){this.unstick(),this.context.stickyRegistry.remove(this.element),this.context.stickyRegistry.unsubscribe(this._adjustFixedPositionBound),Wu(this.scrollListenerId),this.context.eventManager.off("match",this._checkEligibilityBound),yr.breakpointObserver.unsubscribe(this.context.eventManager),this.filler&&this.filler.parentElement.removeChild(this.filler),delete this.onmessage,delete this.element,delete this.filler}}])}());function Ku(t){var e,n,r,i,o=t[5].default,c=Object(Ic.i)(o,t,t[4],null);return{c:function(){e=Object(Ic.G)("svg"),n=Object(Ic.G)("title"),r=Object(Ic.H)(t[0]),c&&c.c(),Object(Ic.f)(n,"id",t[1]),Object(Ic.f)(e,"aria-labelledby",t[1]),Object(Ic.f)(e,"class",t[2]),Object(Ic.f)(e,"role","img"),Object(Ic.f)(e,"viewBox","0 0 512 512"),Object(Ic.f)(e,"style",t[3])},m:function(t,o){Object(Ic.v)(t,e,o),Object(Ic.c)(e,n),Object(Ic.c)(n,r),c&&c.m(e,null),i=!0},p:function(t,s){var u=Object(a.a)(s,1)[0];(!i||1&u)&&Object(Ic.D)(r,t[0]),(!i||2&u)&&Object(Ic.f)(n,"id",t[1]),c&&c.p&&(!i||16&u)&&Object(Ic.L)(c,o,t,t[4],i?Object(Ic.q)(o,t[4],u,null):Object(Ic.p)(t[4]),null),(!i||2&u)&&Object(Ic.f)(e,"aria-labelledby",t[1]),(!i||4&u)&&Object(Ic.f)(e,"class",t[2]),(!i||8&u)&&Object(Ic.f)(e,"style",t[3])},i:function(t){i||(Object(Ic.J)(c,t),i=!0)},o:function(t){Object(Ic.K)(c,t),i=!1},d:function(t){t&&Object(Ic.l)(e),c&&c.d(t)}}}function Xu(t,e,n){var r=e.$$slots,i=void 0===r?{}:r,o=e.$$scope,a=e.title,c=e.a11yLabel,s=e.extraClasses,u=e.inlineStyles;return t.$$set=function(t){"title"in t&&n(0,a=t.title),"a11yLabel"in t&&n(1,c=t.a11yLabel),"extraClasses"in t&&n(2,s=t.extraClasses),"inlineStyles"in t&&n(3,u=t.inlineStyles),"$$scope"in t&&n(4,o=t.$$scope)},[a,c,s,u,o,i]}var $u=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,Xu,Ku,Ic.B,{title:0,a11yLabel:1,extraClasses:2,inlineStyles:3}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function tl(t){var e;return{c:function(){e=Object(Ic.G)("path"),Object(Ic.f)(e,"d","M374 243.082l-211.818-211.542c-2.727-2.724-6.818-4.54-10.455-4.54-3.636 0-7.727 1.816-10.455 4.54l-22.727 22.698c-2.727 2.724-4.545 6.809-4.545 10.441 0 3.632 1.818 7.717 4.545 10.441l178.637 178.404-178.637 178.404c-2.727 2.724-4.545 6.809-4.545 10.441 0 4.086 1.818 7.717 4.545 10.441l22.727 22.698c2.727 2.724 6.818 4.54 10.455 4.54 3.636 0 7.727-1.816 10.455-4.54l211.818-211.542c2.727-2.724 4.545-6.809 4.545-10.441 0-3.632-1.818-7.717-4.545-10.441z")},m:function(t,n){Object(Ic.v)(t,e,n)},p:Ic.z,d:function(t){t&&Object(Ic.l)(e)}}}function el(t){var e,n;return e=new $u({props:{title:t[0],extraClasses:t[1],$$slots:{default:[tl]},$$scope:{ctx:t}}}),{c:function(){Object(Ic.h)(e.$$.fragment)},m:function(t,r){Object(Ic.y)(e,t,r),n=!0},p:function(t,n){var r=Object(a.a)(n,1)[0],i={};1&r&&(i.title=t[0]),2&r&&(i.extraClasses=t[1]),4&r&&(i.$$scope={dirty:r,ctx:t}),e.$set(i)},i:function(t){n||(Object(Ic.J)(e.$$.fragment,t),n=!0)},o:function(t){Object(Ic.K)(e.$$.fragment,t),n=!1},d:function(t){Object(Ic.j)(e,t)}}}function nl(t,e,n){var r=e.title,i=e.extraClasses;return t.$$set=function(t){"title"in t&&n(0,r=t.title),"extraClasses"in t&&n(1,i=t.extraClasses)},[r,i]}var rl=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,nl,el,Ic.B,{title:0,extraClasses:1}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function il(t){for(var e,n,r=[t[0]],i={},o=0;o<r.length;o+=1)i=Object(Ic.e)(i,r[o]);return e=new rl({props:i}),{c:function(){Object(Ic.h)(e.$$.fragment)},m:function(t,r){Object(Ic.y)(e,t,r),n=!0},p:function(t,n){var i=1&Object(a.a)(n,1)[0]?Object(Ic.s)(r,[Object(Ic.r)(t[0])]):{};e.$set(i)},i:function(t){n||(Object(Ic.J)(e.$$.fragment,t),n=!0)},o:function(t){Object(Ic.K)(e.$$.fragment,t),n=!1},d:function(t){Object(Ic.j)(e,t)}}}function ol(t,e,n){return t.$$set=function(t){n(0,e=Object(Ic.e)(Object(Ic.e)({},e),Object(Ic.o)(t)))},[e=Object(Ic.o)(e)]}var al=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,ol,il,Ic.B,{}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function cl(t){var e,n;return e=new al({}),{c:function(){Object(Ic.h)(e.$$.fragment)},m:function(t,r){Object(Ic.y)(e,t,r),n=!0},p:Ic.z,i:function(t){n||(Object(Ic.J)(e.$$.fragment,t),n=!0)},o:function(t){Object(Ic.K)(e.$$.fragment,t),n=!1},d:function(t){Object(Ic.j)(e,t)}}}function sl(t){var e,n,r,i,o,a;return{c:function(){e=Object(Ic.G)("svg"),n=Object(Ic.G)("title"),r=Object(Ic.H)("Close"),i=Object(Ic.G)("path"),o=Object(Ic.E)(),(a=Object(Ic.m)("div")).textContent="".concat(Sc("CLOSE")),Object(Ic.f)(i,"d","M30.3 10.5l-2.8-2.8-8.5 8.5-8.5-8.5-2.8 2.8 8.5 8.5-8.5 8.5 2.8 2.8 8.5-8.5 8.5 8.5 2.8-2.8-8.5-8.5z"),Object(Ic.f)(e,"viewBox","0 0 38 38"),Object(Ic.f)(e,"xmlns","http://www.w3.org/2000/svg"),Object(Ic.f)(a,"class","mobile-close")},m:function(t,c){Object(Ic.v)(t,e,c),Object(Ic.c)(e,n),Object(Ic.c)(n,r),Object(Ic.c)(e,i),Object(Ic.v)(t,o,c),Object(Ic.v)(t,a,c)},p:Ic.z,i:Ic.z,o:Ic.z,d:function(t){t&&Object(Ic.l)(e),t&&Object(Ic.l)(o),t&&Object(Ic.l)(a)}}}function ul(t){var e,n,r,i,o,c,s,u=[sl,cl],l=[];function f(t,e){return"x"===t[2]?0:1}return n=f(t),r=l[n]=u[n](t),{c:function(){e=Object(Ic.m)("div"),r.c(),Object(Ic.f)(e,"role","button"),Object(Ic.f)(e,"aria-label","Dismiss sticky ad"),Object(Ic.f)(e,"aria-controls",t[1]),Object(Ic.f)(e,"class",i="ad-awareness__dismiss ad-awareness__dismiss--"+t[2])},m:function(r,i){Object(Ic.v)(r,e,i),l[n].m(e,null),o=!0,c||(s=Object(Ic.x)(e,"click",(function(){Object(Ic.w)(t[0])&&t[0].apply(this,arguments)})),c=!0)},p:function(c,s){var d=Object(a.a)(s,1)[0],h=n;(n=f(t=c))===h?l[n].p(t,d):(Object(Ic.t)(),Object(Ic.K)(l[h],1,1,(function(){l[h]=null})),Object(Ic.g)(),(r=l[n])?r.p(t,d):(r=l[n]=u[n](t)).c(),Object(Ic.J)(r,1),r.m(e,null)),(!o||2&d)&&Object(Ic.f)(e,"aria-controls",t[1]),(!o||4&d&&i!==(i="ad-awareness__dismiss ad-awareness__dismiss--"+t[2]))&&Object(Ic.f)(e,"class",i)},i:function(t){o||(Object(Ic.J)(r),o=!0)},o:function(t){Object(Ic.K)(r),o=!1},d:function(t){t&&Object(Ic.l)(e),l[n].d(),c=!1,s()}}}function ll(t,e,n){var r=e.onClick,i=e.owner,o=e.style,a=void 0===o?"x":o;return t.$$set=function(t){"onClick"in t&&n(0,r=t.onClick),"owner"in t&&n(1,i=t.owner),"style"in t&&n(2,a=t.style)},[r,i,a]}var fl=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,ll,ul,Ic.B,{onClick:0,owner:1,style:2}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function dl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function hl(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?dl(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):dl(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var pl={detectTransitionEnd:cc},gl={default:{maxScrollDepth:3e3,positionMode:"translate",priority:"medium",breakpoints:["ALL"]}},bl=function(t){function e(t){var n,r=t.element,i=t.stickyRegistry;return Object(fi.b)(this,e),n=Object(fi.a)(this,e,[{getElement:function(){return r},getConfig:function(){return{stickToTop:hl({},gl.default)}},eventManager:null,stickyRegistry:i}]),Ka.apply(n,arguments),n.context.eventManager=n.privateEvents,n._fixAt=0,n.mediaHeight=0,n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Qu);Object.assign(bl.prototype,Ka.prototype),bl.prototype.constructor=bl;var vl=function(t){function e(){var t;Object(fi.b)(this,e);for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return t=Object(fi.a)(this,e,[].concat(r)),Object(fi.d)(t,"_onDismiss",(function(){t.userDismissed=!0,setTimeout((function(){return t.stickyHide()}),500)})),t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"setup",value:function(){var t=this;Object.keys(this.onmessage||{}).forEach((function(e){var n=t.onmessage[e];t.onPublicEvent(e,n.bind(t))})),delete this.onmessage,this.reset(),this.dismissTimer=0,this.unstickAfter=this.getPosition().inDocument+this.stickyConfig.maxScrollDepth;var n=this.onPublicEvent("ad-reveal:".concat(this.config.wid),(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=e.type,i=e.isSticky,o=void 0===i||i;n(),"spotlight"===r&&o?(Ya({template:fl,target:t.element,props:{style:"x",onClick:t._onDismiss,owner:t.element.id}}),t.stickyConfig.breakpoints=["md","lg"],t.initSticky()):t.cleanup()}));return Object(fi.e)(Object(fi.f)(e.prototype),"setup",this).call(this)}},{key:"initSticky",value:function(){Object(fi.e)(Object(fi.f)(e.prototype),"initSticky",this).apply(this,arguments),this.mediaHeight=this.element.querySelector(".js-ad-media").clientHeight,this.filler.className="sticky-filler xs-fixed",this.filler.style.height=this.mediaHeight+90}},{key:"getPosition",value:function(){var t=Object(fi.e)(Object(fi.f)(e.prototype),"getPosition",this).call(this);return t.inViewport=t.inViewport+.5*this.mediaHeight,t}},{key:"cleanup",value:function(){this.destroy()}},{key:"reset",value:function(){this.isHidden=!1,this.userDismissed=!1,this.element.classList.remove("sticky--show","sticky--hide"),this.element.classList.add("sticky--first-time")}},{key:"manageSticky",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.force,r=void 0!==n&&n;Object(fi.e)(Object(fi.f)(e.prototype),"manageSticky",this).call(this,{force:r,stickMod:45,unstickMod:0}),this.isFixed&&!this.userDismissed&&(document.querySelector(".scroll-up-mobile-nav--visible")?this.element.classList.add("mobile-share--adjust"):this.element.classList.remove("mobile-share--adjust"),window.pageYOffset<=this.unstickAfter?this.stickyShow():this.stickyHide())}},{key:"toggleFiller",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"stick";"stick"===t&&this.filler?(this.filler.classList.add("xs-static"),this.filler.classList.remove("xs-fixed")):"unstick"===t&&this.filler&&(this.filler.classList.remove("xs-static"),this.filler.classList.add("xs-fixed"))}},{key:"stick",value:function(){Object(fi.e)(Object(fi.f)(e.prototype),"stick",this).apply(this,arguments),this.isHidden=!0,this.element.querySelector(".js-ad-media").style.minHeight="";var t=document.querySelector("#js-header-container .js-sticky-container");if(t){var n="fixed"===getComputedStyle(t.firstElementChild).position||null;this.fixCheck=n?t.clientHeight:0}else this.fixCheck=0;this.context.stickyRegistry.remove(this.element),this.eventBus.trigger("ad-stick-".concat(this.config.wid))}},{key:"unstick",value:function(){Object(fi.e)(Object(fi.f)(e.prototype),"unstick",this).apply(this,arguments),this.eventBus.trigger("ad-unstick-".concat(this.config.wid)),this.reset()}},{key:"stickyShow",value:function(){var t=this;this.isFixed&&this.isHidden&&(this.isHidden=!1,this.element.classList.remove("sticky--hide"),pl.detectTransitionEnd(this.element,{properties:["any"]}).then((function(){t.context.stickyRegistry.add(t.element,t.stickyRegistryOpts)})),this.context.env.isBFN&&pl.detectTransitionEnd(this.element,{properties:["height"]}).then((function(){t.context.stickyRegistry.add(t.element,t.stickyRegistryOpts)})),this.element.classList.add("sticky--show"))}},{key:"stickyHide",value:function(){var t=this;this.isFixed&&!this.isHidden&&(this.isHidden=!0,this.element.classList.remove("sticky--show"),pl.detectTransitionEnd(this.element,{properties:["any"]}).then((function(){t.context.stickyRegistry.remove(t.element)})),this.element.classList.add("sticky--hide"))}},{key:"destroy",value:function(){Qu.prototype.destroy.call(this),Object(fi.e)(Object(fi.f)(e.prototype),"destroy",this).call(this)}}])}(bl),ml=function(t){function e(){var t,n,r,i;return Object(fi.b)(this,e),i=Object(fi.a)(this,e,arguments),Object(fi.d)(i,"onStickyManagerEvent",(function(t){var e=t.shouldStick;return i.isSticky=!1,i.lifecycleState!==Ja.DESTROYED&&(i.isDismissed?(i.stickyElement.classList.remove("sticky"),!1):e?(i.isSticky=!0,i.stickyElement.classList.add("sticky"),i.stickyElement.classList.remove("sticky--dismissed"),i.context.stickyRegistry.add(i.stickyElement,i.stickyRegistryOpts),!0):(i.dismiss(),!1))})),Object(fi.d)(i,"adjustTopPos",(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{priority:i.stickyRegistryOpts.priority},e=t.priority;if(!(e<i.stickyRegistryOpts.priority)){var n=i.context.stickyRegistry.getAvailableTop(i.stickyElement,i.stickyRegistryOpts);i.stickyHelper.style.top="".concat(n-1,"px"),i.stickyElement.style.top="".concat(n,"px"),i.stickyObserver&&i.stickyObserver.disconnect();var r=i.stickyObserver=new IntersectionObserver((function(t){var e=Object(a.a)(t,1)[0];i.onStickyChange({isStatic:1===e.intersectionRatio})}),{rootMargin:"-".concat(n,"px 0px 0px 0px"),threshold:1});r.observe(i.stickyHelper)}})),Object(fi.d)(i,"onUserDismiss",(function(){i.isDismissed=!0,i.stickToBottom?i.eventBus.trigger("sticky-ad-user-dismiss-bottom:".concat(i.config.wid)):i.eventBus.trigger("sticky-ad-user-dismiss-top:".concat(i.config.wid)),i.dismiss().then((function(){i.notifyStickyManager()}))})),i.isSticky=!1,i.isDismissed=!1,i.stickToBottom=(null===(t=i.config)||void 0===t||null===(t=t.stickyOptions)||void 0===t?void 0:t.stickToBottom)||!1,i.alwaysStickToBottom=(null===(n=i.config)||void 0===n||null===(n=n.stickyOptions)||void 0===n?void 0:n.alwaysStickToBottom)||!1,i.pageHasBottomFixedItems=(null===(r=i.config)||void 0===r||null===(r=r.stickyOptions)||void 0===r?void 0:r.pageHasBottomFixedItems)||!1,i.stickyRegistryOpts={priority:i.context.stickyRegistry.defaultPriorities.medium},i}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"isEnabled",value:function(){var t,e,n="tasty"===(null===this||void 0===this||null===(t=this.context)||void 0===t||null===(t=t.env)||void 0===t?void 0:t.destination)&&"home"===(null===this||void 0===this||null===(e=this.context)||void 0===e||null===(e=e.env)||void 0===e?void 0:e.pageSection);return yr.isAny(["xs"])&&this.context.stickyManager&&!n}},{key:"setup",value:function(){var t=this,n=this.onPublicEvent("ad-reveal:".concat(this.config.wid),(function(){var e;n(),Ya({template:fl,target:t.element,props:{style:"caret",onClick:t.onUserDismiss,owner:t.element.id}});var r=t.stickyElement=(!t.stickToBottom||null!==t&&void 0!==t&&null!==(e=t.context)&&void 0!==e&&null!==(e=e.env)&&void 0!==e&&e.isBPage)&&(t.element.closest(".Ad")||t.element.closest(".ad-wireframe-wrapper"))||t.element,i=t.stickyHelper=document.createElement("div");i.classList.add("ad-awareness-anchor","js-ad-awareness-anchor"),r.insertAdjacentElement("beforebegin",i),t.adjustTopPos(),t.context.stickyRegistry.subscribe(t.adjustTopPos),t.notifyStickyManager()}));return this.onPublicEvent("post-message:jumbotron:".concat(this.config.wid),(function(e){t.element.classList.add("ad-jumbotron"),"inited"===e.action&&n()})),Object(fi.e)(Object(fi.f)(e.prototype),"setup",this).call(this)}},{key:"onStickyChange",value:function(t){var e;t.isStatic?(this.isDismissed=!1,null!==(e=this.stickyElement.classList)&&void 0!==e&&e.remove&&this.stickyElement.classList.remove("sticky--fixed","sticky--dismissed","sticky--bottom","sticky--bottom-offset"),this.notifyStickyManager()):this.isSticky&&(this.stickyElement.classList.add("sticky--fixed"),this.stickToBottom&&this.stickyElement.classList.add("sticky--bottom","Ad--awareness--sticky"),this.pageHasBottomFixedItems&&this.stickyElement.classList.add("sticky--bottom-offset"))}},{key:"notifyStickyManager",value:function(){this.context.stickyManager.notify("top",{canStick:!this.isDismissed,callback:this.onStickyManagerEvent})}},{key:"dismiss",value:function(){var t=this;return new Promise((function(e){pl.detectTransitionEnd(t.stickyElement,{properties:[t.alwaysStickToBottom?"bottom":"top"]}).then(e),t.stickyElement.classList.remove("sticky--bottom","sticky--bottom-offset"),t.stickyElement.classList.add("sticky--dismissed"),t.context.stickyRegistry.remove(t.stickyElement),t.isSticky=!1}))}},{key:"destroy",value:function(){this.isDismissed=!0,this.stickyElement&&(this.stickyElement.classList.remove("sticky","sticky--fixed","sticky--dismissed","sticky--bottom","sticky--bottom-offset"),this.stickyElement.style.top="",delete this.stickyElement),this.stickyHelper&&(this.stickyHelper.remove(),delete this.stickyHelper),this.stickyObserver&&(this.stickyObserver.disconnect(),delete this.stickyObserver),Object(fi.e)(Object(fi.f)(e.prototype),"destroy",this).call(this),this.notifyStickyManager()}}])}(Ka);function yl(t){var e,n=t[7].default,r=Object(Ic.i)(n,t,t[6],null);return{c:function(){r&&r.c()},m:function(t,n){r&&r.m(t,n),e=!0},p:function(t,i){r&&r.p&&(!e||64&i)&&Object(Ic.L)(r,n,t,t[6],e?Object(Ic.q)(n,t[6],i,null):Object(Ic.p)(t[6]),null)},i:function(t){e||(Object(Ic.J)(r,t),e=!0)},o:function(t){Object(Ic.K)(r,t),e=!1},d:function(t){r&&r.d(t)}}}function Ol(t){var e,n,r=t[7].default,i=Object(Ic.i)(r,t,t[6],null);return{c:function(){e=Object(Ic.m)("a"),i&&i.c(),Object(Ic.f)(e,"href",t[2]),Object(Ic.f)(e,"target",t[0]),Object(Ic.f)(e,"class",t[1])},m:function(t,r){Object(Ic.v)(t,e,r),i&&i.m(e,null),n=!0},p:function(t,o){i&&i.p&&(!n||64&o)&&Object(Ic.L)(i,r,t,t[6],n?Object(Ic.q)(r,t[6],o,null):Object(Ic.p)(t[6]),null),(!n||4&o)&&Object(Ic.f)(e,"href",t[2]),(!n||1&o)&&Object(Ic.f)(e,"target",t[0]),(!n||2&o)&&Object(Ic.f)(e,"class",t[1])},i:function(t){n||(Object(Ic.J)(i,t),n=!0)},o:function(t){Object(Ic.K)(i,t),n=!1},d:function(t){t&&Object(Ic.l)(e),i&&i.d(t)}}}function wl(t){var e,n,r,i,o=[Ol,yl],c=[];function s(t,e){return t[2]?0:1}return e=s(t),n=c[e]=o[e](t),{c:function(){n.c(),r=Object(Ic.n)()},m:function(t,n){c[e].m(t,n),Object(Ic.v)(t,r,n),i=!0},p:function(t,i){var u=Object(a.a)(i,1)[0],l=e;(e=s(t))===l?c[e].p(t,u):(Object(Ic.t)(),Object(Ic.K)(c[l],1,1,(function(){c[l]=null})),Object(Ic.g)(),(n=c[e])?n.p(t,u):(n=c[e]=o[e](t)).c(),Object(Ic.J)(n,1),n.m(r.parentNode,r))},i:function(t){i||(Object(Ic.J)(n),i=!0)},o:function(t){Object(Ic.K)(n),i=!1},d:function(t){c[e].d(t),t&&Object(Ic.l)(r)}}}function jl(t,e,n){var r,i=e.$$slots,o=void 0===i?{}:i,a=e.$$scope,c=e.advertiserUrl,s=e.clickThroughUrl,u=e.dfpClickTracker,l=e.target,f=e.linkClasses,d=c&&c.length>0,h=s&&s.length>0;return(d||h)&&(!c&&s&&(c=s),r=Mc(c,u||"")),t.$$set=function(t){"advertiserUrl"in t&&n(3,c=t.advertiserUrl),"clickThroughUrl"in t&&n(4,s=t.clickThroughUrl),"dfpClickTracker"in t&&n(5,u=t.dfpClickTracker),"target"in t&&n(0,l=t.target),"linkClasses"in t&&n(1,f=t.linkClasses),"$$scope"in t&&n(6,a=t.$$scope)},[l,f,r,c,s,u,a,o]}var Al=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,jl,wl,Ic.B,{advertiserUrl:3,clickThroughUrl:4,dfpClickTracker:5,target:0,linkClasses:1}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function El(t,e,n){var r=t.slice();return r[9]=e[n],r}function _l(t){var e,n,r;return{c:function(){e=Object(Ic.m)("div"),n=Object(Ic.m)("iframe"),Object(Ic.f)(n,"title","Advertisement media"),Object(Ic.F)(n.src,r=t[3].src)||Object(Ic.f)(n,"src",r),Object(Ic.f)(n,"class","js-ad-thumbnail"),Object(Ic.f)(e,"class","ad-slot-media-embed-wrapper")},m:function(t,r){Object(Ic.v)(t,e,r),Object(Ic.c)(e,n)},p:function(t,e){8&e&&!Object(Ic.F)(n.src,r=t[3].src)&&Object(Ic.f)(n,"src",r)},i:Ic.z,o:Ic.z,d:function(t){t&&Object(Ic.l)(e)}}}function Pl(t){for(var e,n,r=[t[4],{target:"advertiser"}],i={$$slots:{default:[xl]},$$scope:{ctx:t}},o=0;o<r.length;o+=1)i=Object(Ic.e)(i,r[o]);return e=new Al({props:i}),{c:function(){Object(Ic.h)(e.$$.fragment)},m:function(t,r){Object(Ic.y)(e,t,r),n=!0},p:function(t,n){var i=16&n?Object(Ic.s)(r,[Object(Ic.r)(t[4]),r[1]]):{};136&n&&(i.$$scope={dirty:n,ctx:t}),e.$set(i)},i:function(t){n||(Object(Ic.J)(e.$$.fragment,t),n=!0)},o:function(t){Object(Ic.K)(e.$$.fragment,t),n=!1},d:function(t){Object(Ic.j)(e,t)}}}function Rl(t){for(var e,n=["1:1","4:3","16:9"],r=[],i=0;i<3;i+=1)r[i]=Tl(El(t,n,i));return{c:function(){for(var t=0;t<3;t+=1)r[t].c();e=Object(Ic.n)()},m:function(t,n){for(var i=0;i<3;i+=1)r[i]&&r[i].m(t,n);Object(Ic.v)(t,e,n)},p:function(t,i){if(0&i){var o;for(n=["1:1","4:3","16:9"],o=0;o<3;o+=1){var a=El(t,n,o);r[o]?r[o].p(a,i):(r[o]=Tl(a),r[o].c(),r[o].m(e.parentNode,e))}for(;o<3;o+=1)r[o].d(1)}},i:Ic.z,o:Ic.z,d:function(t){Object(Ic.k)(r,t),t&&Object(Ic.l)(e)}}}function xl(t){var e,n;return{c:function(){e=Object(Ic.m)("img"),Object(Ic.f)(e,"alt","Advertisement media"),Object(Ic.F)(e.src,n=t[3].src)||Object(Ic.f)(e,"src",n),Object(Ic.f)(e,"class","js-ad-thumbnail ad-image-thumbnail")},m:function(t,n){Object(Ic.v)(t,e,n)},p:function(t,r){8&r&&!Object(Ic.F)(e.src,n=t[3].src)&&Object(Ic.f)(e,"src",n)},d:function(t){t&&Object(Ic.l)(e)}}}function Tl(t){var e,n;return{c:function(){e=Object(Ic.m)("img"),Object(Ic.F)(e.src,n=zc[t[9]])||Object(Ic.f)(e,"src",n),Object(Ic.f)(e,"alt",""),Object(Ic.f)(e,"aria-hidden","true"),Object(Ic.f)(e,"class","ad-hidden ratio--"+t[9])},m:function(t,n){Object(Ic.v)(t,e,n)},p:Ic.z,d:function(t){t&&Object(Ic.l)(e)}}}function kl(t){var e,n,r,i,o,c,s,u,l,f=[Rl,Pl,_l],d=[];function h(t,e){return t[3].type===Vr?0:t[3].type===Fr?1:t[3].type===Ur?2:-1}~(c=h(t))&&(s=d[c]=f[c](t));var p=t[6].default,g=Object(Ic.i)(p,t,t[7],null);return{c:function(){e=Object(Ic.m)("div"),n=Object(Ic.m)("div"),r=Object(Ic.m)("div"),o=Object(Ic.E)(),s&&s.c(),u=Object(Ic.E)(),g&&g.c(),Object(Ic.f)(r,"id",i="div-gpt-ad-"+t[0]),Object(Ic.f)(r,"class","ad-slot"),Object(Ic.f)(n,"class","ad-slot-media-inner js-ad-media"),Object(Ic.f)(e,"class","ad-slot-media js-slot-media"),Object(Ic.I)(e,"ad-slot-sticky",t[1]),Object(Ic.I)(e,"debug-pixel",t[2])},m:function(t,i){Object(Ic.v)(t,e,i),Object(Ic.c)(e,n),Object(Ic.c)(n,r),Object(Ic.c)(n,o),~c&&d[c].m(n,null),Object(Ic.c)(e,u),g&&g.m(e,null),l=!0},p:function(t,o){var u=Object(a.a)(o,1)[0];(!l||1&u&&i!==(i="div-gpt-ad-"+t[0]))&&Object(Ic.f)(r,"id",i);var b=c;(c=h(t))===b?~c&&d[c].p(t,u):(s&&(Object(Ic.t)(),Object(Ic.K)(d[b],1,1,(function(){d[b]=null})),Object(Ic.g)()),~c?((s=d[c])?s.p(t,u):(s=d[c]=f[c](t)).c(),Object(Ic.J)(s,1),s.m(n,null)):s=null),g&&g.p&&(!l||128&u)&&Object(Ic.L)(g,p,t,t[7],l?Object(Ic.q)(p,t[7],u,null):Object(Ic.p)(t[7]),null),(!l||2&u)&&Object(Ic.I)(e,"ad-slot-sticky",t[1]),(!l||4&u)&&Object(Ic.I)(e,"debug-pixel",t[2])},i:function(t){l||(Object(Ic.J)(s),Object(Ic.J)(g,t),l=!0)},o:function(t){Object(Ic.K)(s),Object(Ic.K)(g,t),l=!1},d:function(t){t&&Object(Ic.l)(e),~c&&d[c].d(),g&&g.d(t)}}}function Cl(t,e,n){var r=e,i=r.$$slots,o=void 0===i?{}:i,a=r.$$scope,c=e.context,s=e.wid,u=e.stickyViewPixel,l=void 0!==u&&u,f=c.eventBus,d=!1,h={};return Object(Ic.A)((function(){f.once("ad-tpl:".concat(s),(function(t){n(3,h=t)}));var t=window.location.search;n(2,d=t.indexOf("adlib-debug-mode")>-1&&t.indexOf("pixel")>-1)})),t.$$set=function(t){n(4,e=Object(Ic.e)(Object(Ic.e)({},e),Object(Ic.o)(t))),"context"in t&&n(5,c=t.context),"wid"in t&&n(0,s=t.wid),"stickyViewPixel"in t&&n(1,l=t.stickyViewPixel),"$$scope"in t&&n(7,a=t.$$scope)},e=Object(Ic.o)(e),[s,l,d,h,e,c,o,a]}var Ll=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,Cl,kl,Ic.B,{context:5,wid:0,stickyViewPixel:1}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function Il(t){for(var e,n,r,i=[t[0]],o={},c=0;c<i.length;c+=1)o=Object(Ic.e)(o,i[c]);return n=new Ll({props:o}),{c:function(){e=Object(Ic.m)("div"),Object(Ic.h)(n.$$.fragment),Object(Ic.f)(e,"class","awareness-inner")},m:function(t,i){Object(Ic.v)(t,e,i),Object(Ic.y)(n,e,null),r=!0},p:function(t,e){var r=1&Object(a.a)(e,1)[0]?Object(Ic.s)(i,[Object(Ic.r)(t[0])]):{};n.$set(r)},i:function(t){r||(Object(Ic.J)(n.$$.fragment,t),r=!0)},o:function(t){Object(Ic.K)(n.$$.fragment,t),r=!1},d:function(t){t&&Object(Ic.l)(e),Object(Ic.j)(n)}}}function Ml(t,e,n){return t.$$set=function(t){n(0,e=Object(Ic.e)(Object(Ic.e)({},e),Object(Ic.o)(t)))},[e=Object(Ic.o)(e)]}var Sl=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,Ml,Il,Ic.B,{}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function zl(t){for(var e,n,r=[t[0]],i={},o=0;o<r.length;o+=1)i=Object(Ic.e)(i,r[o]);return e=new Sl({props:i}),{c:function(){Object(Ic.h)(e.$$.fragment)},m:function(t,r){Object(Ic.y)(e,t,r),n=!0},p:function(t,n){var i=1&Object(a.a)(n,1)[0]?Object(Ic.s)(r,[Object(Ic.r)(t[0])]):{};e.$set(i)},i:function(t){n||(Object(Ic.J)(e.$$.fragment,t),n=!0)},o:function(t){Object(Ic.K)(e.$$.fragment,t),n=!1},d:function(t){Object(Ic.j)(e,t)}}}function Nl(t,e,n){return t.$$set=function(t){n(0,e=Object(Ic.e)(Object(Ic.e)({},e),Object(Ic.o)(t)))},[e=Object(Ic.o)(e)]}var Dl=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,Nl,zl,Ic.B,{}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b);function Bl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Gl(t,e,n){return n?e?e(t):t:(t&&t.then||(t=Promise.resolve(t)),e?t.then(e):t)}var Fl=function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments)).config.stickyOptions={stickToBottom:!1,alwaysStickToBottom:!1,pageHasBottomFixedItems:!1},t.plugins.add(vl),t.plugins.add(ml),t.context.abeagle.getExperimentVariant("RT-994-swap-refresh",{rejectErrors:!1,defaultVariantIfUnbucketed:"control"}).then((function(e){"swap-refresh"===e?t.plugins.add(Lu):t.plugins.add(As)})),t.context.env.isAdPost()&&t.addFormat(ss.formatType,ss),t.addFormat(Fu.formatType,Fu),t.isInView=!0,t.refreshTimer=null,t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"unitTemplate",get:function(){return Dl}},{key:"isMwSticky",get:function(){return yr.isXsmall()&&this.context.stickyManager}},{key:"setupSizes",value:function(){Object(fi.e)(Object(fi.f)(e.prototype),"setupSizes",this).call(this);var t=yr.getBreakPoint(),n=this.context.env.adSizes;this.isMwSticky&&this.excludeSize(this.context.env.adSizes.NATIVE,this.context.env.adSizes.FLUID,this.context.env.adSizes.NATIVE_COMPLEX_6),(this.context.env.hasQuiz||"xs"===t||"sm"===t)&&this.excludeSize(n.NATIVE_COMPLEX_RECTANGLE),"sm"===t?this.filterProgrammaticSizes({max:n.PROGRAMMATIC_LEADERBOARD}):"md"!==t&&"lg"!==t||(this.filterProgrammaticSizes({min:n.PROGRAMMATIC_LEADERBOARD}),this.excludeSize(n.NATIVE_COMPLEX_6))}},{key:"setup",value:function(){try{var t=this,n=t.config.wid;t.onPublicEvent("ad-content-ready:".concat(n),(function(e){var n;(t.element.classList.add("ad--rendered"),"spotlight"===e.type||"bfp_spotlight_unit"===e.bfpFormatName)&&((e.isJumbo||null!==(n=e.bfpData)&&void 0!==n&&null!==(n=n.content)&&void 0!==n&&n.isJumbo)&&t.element.classList.add("ad-spotlight--jumbo"))})),t.onPublicEvent("ad-wireframe-fadein-finish:".concat(n),(function(){t.eventBus.trigger("ad-reveal:".concat(n),{type:t.context.ad.type,isSticky:t.context.ad.isSticky||!1})})),t.onPublicEvent("ad-refresh:".concat(n),(function(){t.element.classList.contains("ad-jumbotron")&&t.element.classList.remove("ad-jumbotron")}));var r,i=yr.isXsmall(),a=t.context.env.hasQuiz,c=t.isMwSticky||a&&i;if(t.isMwSticky)t.noLazyRendering=!0,t.config.stickyOptions.stickToBottom="tasty"===t.context.env.destination&&(null===t||void 0===t||null===(r=t.context)||void 0===r||null===(r=r.env)||void 0===r?void 0:r.isVideoRecipePage),t.context.env.isBPage&&t.context.env.hasConnatixVideo&&(t.config.stickyOptions.stickToBottom=t.config.stickyOptions.alwaysStickToBottom=!0);return Dr("info","lifecycle","awareness isRefreshable = ".concat(c)),Gl(function(t,e){var n=t();return n&&n.then?n.then(e):e(n)}((function(){if(c)return t.config.refreshOptions=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Bl(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Bl(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},ps),Gl(t.context.abeagle.getExperimentVariant("RT-994-swap-refresh",{rejectErrors:!1,defaultVariantIfUnbucketed:"control"}),(function(e){"swap-refresh"===e&&(t.config.refreshOptions.inViewSeconds=15)}))}),(function(){return Object(fi.e)(Object(fi.f)(e.prototype),"setup",t).call(t)})))}catch(s){return Promise.reject(s)}}}])}(ns.withMixins(fs,(function(t){return function(t){function e(){var t;return Object(fi.b)(this,e),(t=Object(fi.a)(this,e,arguments)).addFormat(Fc.formatType,Fc),t.wasResizeHidden=!1,t}return Object(fi.g)(e,t),Object(fi.c)(e,[{key:"destroy",value:function(){this.resizeListenerId&&Tc(this.resizeListenerId),Object(fi.e)(Object(fi.f)(e.prototype),"destroy",this).call(this)}},{key:"handleSlotRenderEnded",value:function(t){try{var n=this,r=arguments;return Object(fi.e)(Object(fi.f)(e.prototype),"handleSlotRenderEnded",n).apply(n,r),ds(ma.isProgrammaticSlot(t,n.config.size),(function(e){e&&(n.eventBus.trigger("ad-content-rendered:".concat(n.config.wid)),n.slotSize=ma.getRenderedAdSize(t,{wid:n.config.wid}))}))}catch(i){return Promise.reject(i)}}},{key:"doesAdFit",value:function(){if(!this.slotSize)return!1;var t=(this.parent||this.element).getBoundingClientRect().width;return this.slotSize.width<=t}},{key:"resizeHandler",value:function(){var t=this.element;this.element.parentElement.classList.contains("ad-wireframe-wrapper")&&(t=this.element.parentElement),this.doesAdFit()?(t.classList.remove("ad--collapse-vertical","card"),this.wasResizeHidden&&(this.wasResizeHidden=!1,ma.renderWidget(this.config))):(t.classList.add("ad--collapse-vertical"),this.wasResizeHidden=!0)}},{key:"listenForResize",value:function(){var t=this;(this.refreshLayout?this.refreshLayout:Promise.resolve()).then((function(){t._resizeHandlerBound=t.resizeHandler.bind(t),t.resizeListenerId=xc({throttleTimeout:300,callback:t._resizeHandlerBound})}))}}])}(t)})));function Ul(t){var e,n,r,i;return n=new Rs({props:{config:t[0]}}),{c:function(){e=Object(Ic.m)("div"),Object(Ic.h)(n.$$.fragment),Object(Ic.f)(e,"id",r="BF_WIDGET_"+t[0].wid),Object(Ic.f)(e,"data-module","ad-affiliate-pixel"),Object(Ic.f)(e,"class","ad-affiliate-pixel ad-native xs-absolute")},m:function(t,r){Object(Ic.v)(t,e,r),Object(Ic.y)(n,e,null),i=!0},p:function(t,o){var c=Object(a.a)(o,1)[0],s={};1&c&&(s.config=t[0]),n.$set(s),(!i||1&c&&r!==(r="BF_WIDGET_"+t[0].wid))&&Object(Ic.f)(e,"id",r)},i:function(t){i||(Object(Ic.J)(n.$$.fragment,t),i=!0)},o:function(t){Object(Ic.K)(n.$$.fragment,t),i=!1},d:function(t){t&&Object(Ic.l)(e),Object(Ic.j)(n)}}}function Vl(t,e,n){var r=e.slot;return t.$$set=function(t){"slot"in t&&n(0,r=t.slot)},[r]}var Hl=function(t){function e(t){var n;return Object(fi.b)(this,e),n=Object(fi.a)(this,e),Object(Ic.u)(n,t,Vl,Ul,Ic.B,{slot:0}),n}return Object(fi.g)(e,t),Object(fi.c)(e)}(Ic.b),Zl={AdAffiliatePixel:us,AdAwareness:Fl},Wl={AdAffiliatePixel:Hl,AdAwareness:yu},ql=(n("kIik"),u.a.createElement);function Yl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Jl(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Yl(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Yl(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Ql(t,e,n){return e=Object(he.a)(e),Object(de.a)(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],Object(he.a)(t).constructor):e.apply(t,n))}var Kl=function(t){function e(t){var n;return Object(le.a)(this,e),(n=Ql(this,e,[t])).state={adConfig:null},n}return Object(pe.a)(e,t),Object(fe.a)(e,[{key:"render",value:function(){var t=this,e=ql(Ea.Consumer,null,(function(e){return ql(Xl,{adConfig:t.state.adConfig,context:e,className:t.props.className})}));return"awareness"===this.props.type?Na.a.createPortal(e,document.getElementById("portal-awareness")):e}}],[{key:"getDerivedStateFromProps",value:function(t){return{adConfig:Object(Hn.cloneDeep)(Cn[t.type])}}}])}(u.a.Component);Kl.defaultProps={className:""};var Xl=function(t){function e(t){var n;return Object(le.a)(this,e),(n=Ql(this,e,[t])).container=u.a.createRef(),n.adWireframe=null,n.nativeAd=null,n}return Object(pe.a)(e,t),Object(fe.a)(e,[{key:"renderAdWireframe",value:function(){this.adWireframe&&this.adWireframe.$destroy();var t=this.props.adConfig;this.adWireframe=Ya({template:e.getTemplate(t),target:this.container.current,props:{slot:t}})}},{key:"createAd",value:function(){var t=this.props.adConfig;this.adWireframe||this.renderAdWireframe();var n=new(e.getComponent(t))(Jl(Jl({},this.props.context),{},{element:this.container.current.firstElementChild,config:t}));n.init().catch((function(t){return t instanceof hi?t:Promise.reject(t)})),this.nativeAd=n}},{key:"destroyAd",value:function(){this.nativeAd&&(this.nativeAd.destroy(),delete this.nativeAd),this.adWireframe&&(this.adWireframe.$destroy(),delete this.adWireframe),this.container.current.innerHTML=""}},{key:"componentDidMount",value:function(){var t,e;this.renderAdWireframe(),"loaded"===(null===(t=this.props)||void 0===t||null===(e=t.context)||void 0===e?void 0:e.status)&&this.createAd()}},{key:"componentDidUpdate",value:function(t){var e,n=this.props;"loaded"===(null===n||void 0===n||null===(e=n.context)||void 0===e?void 0:e.status)&&(this.nativeAd&&Object(Hn.isEqual)(t.adConfig,n.adConfig)||(this.nativeAd&&this.destroyAd(),this.createAd()))}},{key:"componentWillUnmount",value:function(){this.destroyAd()}},{key:"render",value:function(){var t=this.props.adConfig;return ql("div",{ref:this.container,className:"Ad Ad--".concat(t.adPos," ").concat(this.props.className)})}}],[{key:"getTemplate",value:function(t){var e=t.adPos;return/^awareness/.test(e)?Wl.AdAwareness:/pixel$/.test(e)?Wl.AdAffiliatePixel:null}},{key:"getComponent",value:function(t){var e=t.adPos;return/^awareness/.test(e)?Zl.AdAwareness:/pixel$/.test(e)?Zl.AdAffiliatePixel:null}}])}(u.a.Component),$l=u.a.createElement,tf=function(t){var e=t.user,n=t.posts,r=t.buzzCount,i=t.isAdvertizer,o=t.pageLang,a=Pn("(min-width: 52rem)");return $l(Sa,{user:e,isAdvertizer:i,pageLang:o},$l("div",{className:P.a.backgroundWhite},$l(Kl,{type:"pixel"}),$l(Kl,{type:"awareness"}),$l("div",{className:d()(P.a.content,P.a.feeds,P.a.feeds__withSidebar)},$l(Tt,{posts:n,buzzCount:r}),a&&$l("div",{className:P.a.sidebarWrapper},$l(_n,e)))))},ef=n("YRcd"),nf=n.n(ef),rf=u.a.createElement;var of=Object(l.withTranslation)("common")((function(t){var e=t.t,n=t.pageLang,r=Object(s.useContext)(et.ProfileUserContext).uuid,i=Object(s.useMemo)(function(t){var e=t.uuid;return function(){return{unit_type:"profile_body",unit_name:e,item_type:"text",item_name:"make your own post",subunit_type:"package",subunit_name:"",position_in_unit:0,position_in_subunit:0,target_content_type:"url",target_content_id:"https://www.buzzfeed.com/community"}}}({uuid:r}),[r]),o=Object(Y.a)(i);return rf("div",{className:nf.a.cta__wrapper,ref:o},rf("div",{className:nf.a.cta__text},"en"===n&&rf(u.a.Fragment,null,rf("a",{className:nf.a.cta__link,href:"https://www.buzzfeed.com/community/about"},"BuzzFeed Community")," "),e("community_cta")," ",rf("a",{className:nf.a.cta__link,href:"https://www.buzzfeed.com/community"},e("community_cta_link"))),rf("a",{className:nf.a.cta__button,href:"https://community.buzzfeed.com"},e("create_post_cta")," ",rf(zt.c,{className:nf.a.cta__button_svg})))})),af=n("T8Fm"),cf=n("K2uR"),sf=n("kgCe"),uf=n.n(sf),lf=u.a.createElement;function ff(){ff=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),c=new T(r||[]);return i(a,"_invoke",{value:_(t,n,c)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="executing",p="completed",g={};function b(){}function v(){}function m(){}var y={};u(y,a,(function(){return this}));var O=Object.getPrototypeOf,w=O&&O(O(k([])));w&&w!==n&&r.call(w,a)&&(y=w);var j=m.prototype=b.prototype=Object.create(y);function A(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function n(i,o,a,c){var s=f(t[i],t,o);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(l).then((function(t){u.value=t,a(u)}),(function(t){return n("throw",t,a,c)}))}c(s.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function _(e,n,r){var i=d;return function(o,a){if(i===h)throw Error("Generator is already running");if(i===p){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var c=r.delegate;if(c){var s=P(c,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===d)throw i=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=h;var u=f(e,n,r);if("normal"===u.type){if(i=r.done?p:"suspendedYield",u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=p,r.method="throw",r.arg=u.arg)}}}function P(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var o=f(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,g;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function R(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(R,this),this.reset(!0)}function k(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(typeof e+" is not iterable")}return v.prototype=m,i(j,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:v,configurable:!0}),v.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},A(E.prototype),u(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new E(l(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},A(j),u(j,s,"Generator"),u(j,a,(function(){return this})),u(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=k,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(x),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return c.type="throw",c.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),x(n),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;x(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:k(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}var df=function(t){return"/".concat(t,"/trophies")},hf=function(t,e){return"/".concat(t,"/trophies#trophy-").concat(e)},pf=function(t,e){return"".concat(e?"":"Unearned ","Trophy: ").concat(t)},gf=function(){var t=Object(i.a)(ff().mark((function t(e){var n;return ff().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("/".concat(e,".json?include=trophies"));case 3:return n=t.sent,t.next=6,n.json().then((function(t){return t.trophies}));case 6:return t.abrupt("return",t.sent);case 9:t.prev=9,t.t0=t.catch(0),console.error("Error fetching new trophies after awarding from userAuthPassthrough",t.t0);case 12:return t.abrupt("return",null);case 13:case"end":return t.stop()}}),t,null,[[0,9]])})));return function(e){return t.apply(this,arguments)}}();function bf(t){var e=t.trophy,n=t.username,r=e.name,i=e.image,o=e.imageAlt,a=e.earned,c=e.id,s=a?uf.a.iconEarned:uf.a.iconUnearned;return lf("li",{className:s,key:c},lf(af.a,{href:hf(n,c),className:uf.a.link,"aria-label":pf(r,a)},lf(cf.a,{src:"".concat(i,"?resize=100:100&output-format=auto&output-quality=auto"),alt:o})))}var vf=Object(l.withTranslation)("common")((function(t){var e=t.trophies,n=t.t,r=Object(s.useContext)(et.ProfileUserContext),o=r.username,a=r.uuid,c=Object(s.useContext)(ze.StatusBarContext).setStatusBarStatus,u=Object(s.useState)(e),l=u[0],f=u[1],d=Object(s.useMemo)(function(t){var e=t.username,n=t.uuid;return function(){return{unit_type:"profile_head",unit_name:n,item_type:"text",item_name:"see all trophies",position_in_unit:0,position_in_subunit:0,target_content_type:"info",target_content_id:df(e)}}}({username:o,uuid:a}),[o,a]),h=Object(Y.a)(d),p=Pn("(max-width: 40rem)")?12:20,g=l.length?l.slice(0,p):[],b=Se("trophy_earned");return Object(s.useEffect)((function(){if(o&&c&&b.length){var t=e.filter((function(t){return t.earned})).map((function(t){return t.id}));Promise.all(b.map((function(e){return t.includes(e.trophyId)?e.delete():e.run({setStatusBarStatus:c})}))).then(function(){var t=Object(i.a)(ff().mark((function t(e){var n;return ff().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.length||!e.every((function(t){return t}))){t.next=5;break}return t.next=3,gf(o);case 3:(n=t.sent)&&f(n);case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}),[b,e,f,c,o]),lf("div",{className:uf.a.case,id:"trophy-case"},lf("div",{className:uf.a.header},lf("h2",{className:uf.a.title},n("trophies")),lf(af.a,{href:df(o),"aria-label":"See All Trophies",ref:h,rel:"nofollow"},lf("div",{className:uf.a.forwardButton},n("see_all_trophies"),lf(zt.c,{className:uf.a.forwardButtonIcon})))),lf("div",{className:uf.a.trophies},lf("ul",{className:uf.a.iconList},g.map((function(t){return lf(bf,{key:t.id,trophy:t,username:o})})))))})),mf=n("b4WN"),yf=u.a.createElement,Of=function(t){var e=t.className;return yf("svg",{viewBox:"0 0 512 512",id:"default-avatar",className:e},yf("path",{d:"M256.271 12C121.181 12 12 121.182 12 256.271c0 135.09 109.182 244.271 244.271 244.271 135.09 0 244.271-109.182 244.271-244.271C500.542 121.181 391.36 12 256.271 12zM424.67 172.997c-12.954 0-27.758-3.701-40.712-14.804-16.655-12.954-22.206-31.459-18.505-55.516 24.057 18.505 44.413 42.562 59.217 70.321zM256.271 67.516c25.908 0 49.965 5.552 74.022 14.804-18.505 37.011-53.666 62.918-96.228 75.872-44.413 12.954-90.677 5.552-125.837-16.655 33.31-44.413 86.975-74.022 148.043-74.022zm0 377.51c-103.63 0-188.755-85.125-188.755-188.755 0-29.609 7.402-57.367 18.505-81.424 29.609 18.505 64.769 27.758 99.929 27.758 18.505 0 37.011-1.851 55.516-7.402 35.16-9.253 64.769-27.758 86.975-51.815 5.552 16.655 14.804 31.459 29.609 44.413 18.505 14.804 42.562 22.206 64.769 22.206 3.701 0 9.253 0 12.954-1.851 3.701 14.804 5.552 31.459 5.552 48.114 3.701 103.63-81.424 188.755-185.054 188.755zm0-59.217c35.16 0 62.918-27.758 62.918-62.918H191.502c1.851 33.31 29.609 62.918 64.769 62.918zM147.09 230.364h-33.31v31.459c0 18.505 14.804 31.459 31.459 31.459s31.459-14.804 31.459-31.459-12.954-31.459-29.609-31.459zm186.904 31.459c0 18.505 14.804 31.459 31.459 31.459s31.459-14.804 31.459-31.459v-31.459h-31.459c-16.655 0-31.459 12.954-31.459 31.459z"}))},wf=n("CXGn"),jf=n.n(wf),Af=u.a.createElement,Ef=function(t){return"".concat(ie.image_service_url).concat(t,"?resize=100:100&quality=auto")},_f=function(t){var e=t.imageUrl,n=t.userName,r=t.isAdvertizer,i=void 0!==r&&r;return e?Af("img",{className:d()(jf.a.avatar,Object(o.default)({},jf.a.avatar__advertizer,i)),src:Ef(e),alt:n}):Af(Of,{className:d()(jf.a.avatar,jf.a.avatar__default)})},Pf=n("CHHm"),Rf=n.n(Pf),xf=u.a.createElement,Tf=function(t){var e,n=t.url,r=t.name,i=Object(s.useContext)(et.ProfileUserContext).uuid,o=Object(s.useMemo)(function(t){var e=t.url,n=t.uuid;return function(){return{unit_type:"profile_head",unit_name:n,item_type:"text",item_name:e,position_in_unit:0,position_in_subunit:0,target_content_url:e,target_content_type:"url"}}}({url:n,uuid:i}),[n,i]),a=Object(Y.a)(o,!0);switch(r){case"facebook":e=xf(zt.d,{className:Rf.a.icon,"aria-hidden":"true"});break;case"twitter":e=xf(zt.j,{width:18,height:18,className:Rf.a.icon,"aria-hidden":"true"});break;case"instagram":e=xf(zt.f,{width:18,height:18,className:Rf.a.icon,"aria-hidden":"true"});break;case"tiktok":e=xf(zt.h,{width:18,height:18,className:Rf.a.icon,"aria-hidden":"true"});break;default:r="Website",e=xf(zt.g,{className:Rf.a.icon,"aria-hidden":"true"})}var c=d()(Rf.a.link,Rf.a["link__".concat(r)]);return xf("a",{className:c,href:n,ref:a,target:"_blank"},e,r)},kf=n("mcQc"),Cf=n("V2I+"),Lf=n.n(Cf),If=u.a.createElement;function Mf(t){var e=t.userData,n=e.bio,r=e.social;return If("div",null,If("div",{className:Lf.a.bio,dangerouslySetInnerHTML:{__html:n}}),r&&r.length>0&&If("ul",{className:Lf.a.userSocial},r.map((function(t,e){var n=t.url,r=t.name;return If("li",{key:e},If(Tf,{key:r,name:r,url:n}))}))))}function Sf(t){var e=t.isCommunityUser,n=t.isAdvertizer,r=t.internetPoints,i=t.joinedDate,o=t.trophyCount,a=t.buzzCount,c=t.commentCount,s=t.t,l=null;return l=e||n?If(u.a.Fragment,null,If("div",{className:Lf.a.metaRowOne},e&&If("div",{className:Lf.a.internetPointsContainer},If(kf.a,{internetPoints:r})),If("div",{className:Lf.a.joinedDate},If("dt",null,s("joined")),If("dd",null,i))),If("div",{className:Lf.a.metaRowTwo},e&&If("div",{className:Lf.a.invertDef},If("dt",null,s("trophies")),If("dd",null,o)),If("div",{className:Lf.a.invertDef},If("dt",null,s("posts")),If("dd",null,a)),!n&&If("div",{className:Lf.a.invertDef},If("dt",null,s("comments")),If("dd",null,c)))):If("div",{className:Lf.a.metaRowOne},If("div",{className:d()(Lf.a.joinedDate,Lf.a.joinedDate__editorial)},If("dt",null,s("joined")),If("dd",null,i)),If("div",{className:Lf.a.invertDef},If("dt",null,s("posts")),If("dd",null,a))),If("dl",{className:Lf.a.userMetaList},l)}var zf=Object(l.withTranslation)("common")((function(t){var e=t.userData,n=t.internetPoints,r=t.trophyCount,i=t.buzzCount,o=t.commentCount,a=t.isAdvertizer,c=t.headerImage,s=t.headerAltText,u=void 0===s?"":s,l=t.t,f=Pn("(max-width: 52rem)"),d=If(Mf,{userData:e}),h=e.image,p=e.displayName,g=e.username,b=e.memberSince,v=Object(mf.monthAndYear)(1e3*b);return If("div",{className:Lf.a.header},c&&If("img",{className:Lf.a.headerImage,alt:u,src:"https://img.buzzfeed.com/buzzfeed-static".concat(c)}),If("div",{className:Lf.a.userInfoContainer},If("div",{className:Lf.a.userInfo},If(_f,{imageUrl:h,userName:g,isAdvertizer:a}),If("div",{className:Lf.a.userNameContainer},If("h1",{className:Lf.a.displayName,dangerouslySetInnerHTML:{__html:p},id:"buzz-content"}),!f&&d)),f&&d,If(Sf,{joinedDate:v,internetPoints:n,trophyCount:r,buzzCount:i,commentCount:o,isCommunityUser:e.isCommunityUser,isAdvertizer:a,t:l})))})),Nf=n("kxKB"),Df=n.n(Nf),Bf=n("DMnb"),Gf=n.n(Bf),Ff=u.a.createElement;function Uf(){var t=Object(s.useState)(!1),e=t[0],n=t[1],r=Object(s.useCallback)((function(){window.localStorage.setItem("brazilCookieBanner",!0),n(!1)}),[]);return Object(s.useEffect)((function(){window.localStorage.getItem("brazilCookieBanner")||n(!0)}),[]),Ff("div",{className:"".concat(e?Gf.a.cookieBanner:Gf.a.hidden)},Ff("div",{className:Gf.a.textContainer},Ff("p",{className:Gf.a.text},"Utilizamos cookies, pr\xf3prios e de terceiros, que o reconhecem e identificam como um usu\xe1rio \xfanico, para garantir a melhor experi\xeancia de navega\xe7\xe3o, personalizar conte\xfado e an\xfancios, e melhorar o desempenho do nosso site e servi\xe7os. Esses Cookies nos permitem coletar alguns dados pessoais sobre voc\xea, como sua ID exclusiva atribu\xedda ao seu dispositivo, endere\xe7o de IP, tipo de dispositivo e navegador, conte\xfados visualizados ou outras a\xe7\xf5es realizadas usando nossos servi\xe7os, pa\xeds e idioma selecionados, entre outros. Para saber mais sobre nossa pol\xedtica de cookies, acesse"," ",Ff("a",{className:"bold",href:"https://www.buzzfeed.com/about/privacy?country=pt-br"}," link"),"."),Ff("p",{className:Gf.a.text},"Caso n\xe3o concorde com o uso cookies dessa forma, voc\xea dever\xe1 ajustar as configura\xe7\xf5es de seu navegador ou deixar de acessar o nosso site e servi\xe7os. Ao continuar com a navega\xe7\xe3o em nosso site, voc\xea aceita o uso de cookies.")),Ff("div",null,Ff("button",{className:"".concat(Gf.a.button," button button--small"),onClick:r},"Aceito")))}var Vf=n("fq6u"),Hf=n("FR8R"),Zf=["error"],Wf=["welcome_posts","latest_posts","top_posts"],qf=u.a.createElement;function Yf(){Yf=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),c=new T(r||[]);return i(a,"_invoke",{value:_(t,n,c)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="executing",p="completed",g={};function b(){}function v(){}function m(){}var y={};u(y,a,(function(){return this}));var O=Object.getPrototypeOf,w=O&&O(O(k([])));w&&w!==n&&r.call(w,a)&&(y=w);var j=m.prototype=b.prototype=Object.create(y);function A(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function n(i,o,a,c){var s=f(t[i],t,o);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(l).then((function(t){u.value=t,a(u)}),(function(t){return n("throw",t,a,c)}))}c(s.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function _(e,n,r){var i=d;return function(o,a){if(i===h)throw Error("Generator is already running");if(i===p){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var c=r.delegate;if(c){var s=P(c,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===d)throw i=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=h;var u=f(e,n,r);if("normal"===u.type){if(i=r.done?p:"suspendedYield",u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=p,r.method="throw",r.arg=u.arg)}}}function P(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var o=f(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,g;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function R(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(R,this),this.reset(!0)}function k(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(typeof e+" is not iterable")}return v.prototype=m,i(j,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:v,configurable:!0}),v.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},A(E.prototype),u(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new E(l(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},A(j),u(j,s,"Generator"),u(j,a,(function(){return this})),u(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=k,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(x),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return c.type="throw",c.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),x(n),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;x(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:k(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}function Jf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Qf(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Jf(Object(n),!0).forEach((function(e){Object(o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Jf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Kf(t,e){var n="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"===typeof t)return Xf(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Xf(t,e)}(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,c=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){c=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(c)throw o}}}}function Xf(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var $f=function(t){return/^en/.test(t)};function td(t){var e=t.edition,n=t.latest_posts,r=t.top_posts,i=Object(p.b)(e),o=null,a=[],s=Object(g.getLanguageCodes)(n,r);if(s.length){var u=s.filter((function(t){return!$f(t)})),l=s.filter($f),f=1===l.length&&"en-us"!==l[0]?l[0]:"en";"en"===i&&(i=f),(a=[].concat(Object(c.a)(u),[f]).sort()).length>1&&(o=l.length?f:u[0])}else a.push("en");return{main:i,alternate:a,defaultLang:o}}function ed(t){var e=t.edition,n=t.pageLanguages,r=n.alternate,i=n.defaultLang,o=t.username,c=function(t){return"".concat(m.CANONICAL_ROOT,"/").concat(t&&!$f(t)?"".concat(t,"/"):"").concat(o)},s=[];s.push({rel:"canonical",href:c(e)});var u,l=Kf(r);try{for(l.s();!(u=l.n()).done;){var f=u.value.match(/\w+-(\w+)/)||["en",""],d=Object(a.a)(f,2),h=d[0],g=d[1];s.push({rel:"alternate",href:$f(h)?c():c(g),hreflang:Object(p.a)(h)})}}catch(b){l.e(b)}finally{l.f()}return i&&s.push({rel:"alternate",href:$f(i)?c():c(i.replace(/[^-]*-/,"")),hreflang:"x-default"}),s}function nd(t){var e=t.user,n=t.points,r=t.trophies,i=t.comments,a=t.posts,c=t.buzz_count,s=t.pinned_quiz_results,u=t.pageLang,l=t.header;if(Object(O.a)({}),Object(w.a)({}),Object(j.a)({}),Object(A.a)(),!e)return"";var f=e.type,p=e.isCommunityUser,g=f===y.ADVERTISER_USER_TYPE,b=f===y.PRESS_USER_TYPE,v=f===y.STAFF_USER_TYPE,m=r?r.filter((function(t){return t.earned})).length:0,_=i.comment_count,P=void 0===_?0:_,R=i.items,x=void 0===R?[]:R,T=r&&qf(vf,{trophies:r}),k=null;return k=p?qf("div",null,qf(of,{pageLang:u}),qf(dn,{comments:x,posts:a,buzzCount:c,pinnedQuizResults:s})):g?qf(tf,{posts:a,user:e,isAdvertizer:g,pageLang:u}):b?qf(It,{posts:a,user:e}):qf(Ct,{posts:a}),qf(Vf.a,{pageProps:e,edition:"en-us"},qf(E.a,null),qf(Hf.a,null),qf(h.a,l),qf("main",null,qf("div",{className:Df.a.backgroundGray},qf("div",{className:d()(Df.a.content,Object(o.default)({},Df.a.content__editorial,v))},qf(zf,{userData:e,internetPoints:n,commentCount:P,trophyCount:m,buzzCount:c,isAdvertizer:g,headerImage:(v||g)&&e.headerImage,headerAltText:e.headerAltText}),T)),"tastydemais"===e.username&&qf(Uf,null),k))}nd.getInitialProps=function(){var t=Object(i.a)(Yf().mark((function t(e){var n,i,o,a,c,s,u,l,f,d,h,p,g,m,y,O;return Yf().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.req,i=e.query,o={namespacesRequired:["common"]},a=i.username,t.next=5,Object(b.a)("/".concat(a,".json"),{req:n});case 5:if(c=t.sent,s=c.error,u=Object(r.a)(c,Zf),!s){t.next=10;break}return t.abrupt("return",s);case 10:return l=u.welcome_posts,f=void 0===l?[]:l,d=u.latest_posts,h=u.top_posts,p=Object(r.a)(u,Wf),g=n.params.edition,m=td({edition:g,latest_posts:d,top_posts:h}),y=ed({edition:g,pageLanguages:m,username:a}),O=Object(v.a)(n)["bf-geo-country"]||"",t.abrupt("return",Qf(Qf(Qf({},o),p),{},{posts:{welcome:f,latest:d,top:h},pageTitle:"".concat(p.user.displayName," on BuzzFeed"),pageLang:m.main,pageLinks:y,header:i.header,userGeo:O}));case 16:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();e.default=Object(l.withTranslation)("common")(nd)},d75D:function(t,e,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return n("cMU6")}])},"dVT/":function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));var r=n("foZj"),i=["a","strong","i","b"],o={a:["href","name","target"],img:["src","alt"]};function a(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:o;return t&&""===t.trim()||!t?"":r(t,{allowedTags:e,allowedAttributes:n,nonTextTags:[]})}},gUiv:function(t,e,n){!function(t){var e=/\S/,n=/\"/g,r=/\n/g,i=/\r/g,o=/\\/g,a=/\u2028/,c=/\u2029/;function s(t){"}"===t.n.substr(t.n.length-1)&&(t.n=t.n.substring(0,t.n.length-1))}function u(t){return t.trim?t.trim():t.replace(/^\s*|\s*$/g,"")}function l(t,e,n){if(e.charAt(n)!=t.charAt(0))return!1;for(var r=1,i=t.length;r<i;r++)if(e.charAt(n+r)!=t.charAt(r))return!1;return!0}t.tags={"#":1,"^":2,"<":3,$:4,"/":5,"!":6,">":7,"=":8,_v:9,"{":10,"&":11,_t:12},t.scan=function(n,r){var i=n.length,o=0,a=null,c=null,f="",d=[],h=!1,p=0,g=0,b="{{",v="}}";function m(){f.length>0&&(d.push({tag:"_t",text:new String(f)}),f="")}function y(n,r){if(m(),n&&function(){for(var n=!0,r=g;r<d.length;r++)if(!(n=t.tags[d[r].tag]<t.tags._v||"_t"==d[r].tag&&null===d[r].text.match(e)))return!1;return n}())for(var i,o=g;o<d.length;o++)d[o].text&&((i=d[o+1])&&">"==i.tag&&(i.indent=d[o].text.toString()),d.splice(o,1));else r||d.push({tag:"\n"});h=!1,g=d.length}function O(t,e){var n="="+v,r=t.indexOf(n,e),i=u(t.substring(t.indexOf("=",e)+1,r)).split(" ");return b=i[0],v=i[i.length-1],r+n.length-1}for(r&&(r=r.split(" "),b=r[0],v=r[1]),p=0;p<i;p++)0==o?l(b,n,p)?(--p,m(),o=1):"\n"==n.charAt(p)?y(h):f+=n.charAt(p):1==o?(p+=b.length-1,"="==(a=(c=t.tags[n.charAt(p+1)])?n.charAt(p+1):"_v")?(p=O(n,p),o=0):(c&&p++,o=2),h=p):l(v,n,p)?(d.push({tag:a,n:u(f),otag:b,ctag:v,i:"/"==a?h-b.length:p+v.length}),f="",p+=v.length-1,o=0,"{"==a&&("}}"==v?p++:s(d[d.length-1]))):f+=n.charAt(p);return y(h,!0),d};var f={_t:!0,"\n":!0,$:!0,"/":!0};function d(t,e){for(var n=0,r=e.length;n<r;n++)if(e[n].o==t.n)return t.tag="#",!0}function h(t,e,n){for(var r=0,i=n.length;r<i;r++)if(n[r].c==t&&n[r].o==e)return!0}function p(t){var e=[];for(var n in t.partials)e.push('"'+b(n)+'":{name:"'+b(t.partials[n].name)+'", '+p(t.partials[n])+"}");return"partials: {"+e.join(",")+"}, subs: "+function(t){var e=[];for(var n in t)e.push('"'+b(n)+'": function(c,p,t,i) {'+t[n]+"}");return"{ "+e.join(",")+" }"}(t.subs)}t.stringify=function(e,n,r){return"{code: function (c,p,i) { "+t.wrapMain(e.code)+" },"+p(e)+"}"};var g=0;function b(t){return t.replace(o,"\\\\").replace(n,'\\"').replace(r,"\\n").replace(i,"\\r").replace(a,"\\u2028").replace(c,"\\u2029")}function v(t){return~t.indexOf(".")?"d":"f"}function m(t,e){var n="<"+(e.prefix||"")+t.n+g++;return e.partials[n]={name:t.n,partials:{}},e.code+='t.b(t.rp("'+b(n)+'",c,p,"'+(t.indent||"")+'"));',n}function y(t,e){e.code+="t.b(t.t(t."+v(t.n)+'("'+b(t.n)+'",c,p,0)));'}function O(t){return"t.b("+t+");"}t.generate=function(e,n,r){g=0;var i={code:"",subs:{},partials:{}};return t.walk(e,i),r.asString?this.stringify(i,n,r):this.makeTemplate(i,n,r)},t.wrapMain=function(t){return'var t=this;t.b(i=i||"");'+t+"return t.fl();"},t.template=t.Template,t.makeTemplate=function(t,e,n){var r=this.makePartials(t);return r.code=new Function("c","p","i",this.wrapMain(t.code)),new this.template(r,e,this,n)},t.makePartials=function(t){var e,n={subs:{},partials:t.partials,name:t.name};for(e in n.partials)n.partials[e]=this.makePartials(n.partials[e]);for(e in t.subs)n.subs[e]=new Function("c","p","t","i",t.subs[e]);return n},t.codegen={"#":function(e,n){n.code+="if(t.s(t."+v(e.n)+'("'+b(e.n)+'",c,p,1),c,p,0,'+e.i+","+e.end+',"'+e.otag+" "+e.ctag+'")){t.rs(c,p,function(c,p,t){',t.walk(e.nodes,n),n.code+="});c.pop();}"},"^":function(e,n){n.code+="if(!t.s(t."+v(e.n)+'("'+b(e.n)+'",c,p,1),c,p,1,0,0,"")){',t.walk(e.nodes,n),n.code+="};"},">":m,"<":function(e,n){var r={partials:{},code:"",subs:{},inPartial:!0};t.walk(e.nodes,r);var i=n.partials[m(e,n)];i.subs=r.subs,i.partials=r.partials},$:function(e,n){var r={subs:{},code:"",partials:n.partials,prefix:e.n};t.walk(e.nodes,r),n.subs[e.n]=r.code,n.inPartial||(n.code+='t.sub("'+b(e.n)+'",c,p,i);')},"\n":function(t,e){e.code+=O('"\\n"'+(t.last?"":" + i"))},_v:function(t,e){e.code+="t.b(t.v(t."+v(t.n)+'("'+b(t.n)+'",c,p,0)));'},_t:function(t,e){e.code+=O('"'+b(t.text)+'"')},"{":y,"&":y},t.walk=function(e,n){for(var r,i=0,o=e.length;i<o;i++)(r=t.codegen[e[i].tag])&&r(e[i],n);return n},t.parse=function(e,n,r){return function e(n,r,i,o){var a,c=[],s=null,u=null;for(a=i[i.length-1];n.length>0;){if(u=n.shift(),a&&"<"==a.tag&&!(u.tag in f))throw new Error("Illegal content in < super tag.");if(t.tags[u.tag]<=t.tags.$||d(u,o))i.push(u),u.nodes=e(n,u.tag,i,o);else{if("/"==u.tag){if(0===i.length)throw new Error("Closing tag without opener: /"+u.n);if(s=i.pop(),u.n!=s.n&&!h(u.n,s.n,o))throw new Error("Nesting error: "+s.n+" vs. "+u.n);return s.end=u.i,c}"\n"==u.tag&&(u.last=0==n.length||"\n"==n[0].tag)}c.push(u)}if(i.length>0)throw new Error("missing closing tag: "+i.pop().n);return c}(e,0,[],(r=r||{}).sectionTags||[])},t.cache={},t.cacheKey=function(t,e){return[t,!!e.asString,!!e.disableLambda,e.delimiters,!!e.modelGet].join("||")},t.compile=function(e,n){n=n||{};var r=t.cacheKey(e,n),i=this.cache[r];if(i){var o=i.partials;for(var a in o)delete o[a].instance;return i}return i=this.generate(this.parse(this.scan(e,n.delimiters),e,n),e,n),this.cache[r]=i}}(e)},jNoi:function(t,e,n){"use strict";function r(t,e,n){return e=l(e),function(t,e){if(e&&("object"===typeof e||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],l(t).constructor):e.apply(t,n))}function i(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,i(r.key),r)}}function c(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function s(t,e,n){return(e=i(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function u(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&f(t,e)}function l(t){return(l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f(t,e){return(f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function d(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=l(t)););return t}function h(){return(h="undefined"!==typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=d(t,e);if(r){var i=Object.getOwnPropertyDescriptor(r,e);return i.get?i.get.call(arguments.length<3?t:n):i.value}}).apply(this,arguments)}n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return c})),n.d(e,"d",(function(){return s})),n.d(e,"e",(function(){return h})),n.d(e,"f",(function(){return l})),n.d(e,"g",(function(){return u}))},pEXl:function(t,e,n){var r;function i(t){function n(){if(n.enabled){var t=n,i=+new Date,o=i-(r||i);t.diff=o,t.prev=r,t.curr=i,r=i;for(var a=new Array(arguments.length),c=0;c<a.length;c++)a[c]=arguments[c];a[0]=e.coerce(a[0]),"string"!==typeof a[0]&&a.unshift("%O");var s=0;a[0]=a[0].replace(/%([a-zA-Z%])/g,(function(n,r){if("%%"===n)return n;s++;var i=e.formatters[r];if("function"===typeof i){var o=a[s];n=i.call(t,o),a.splice(s,1),s--}return n})),e.formatArgs.call(t,a);var u=n.log||e.log||console.log.bind(console);u.apply(t,a)}}return n.namespace=t,n.enabled=e.enabled(t),n.useColors=e.useColors(),n.color=function(t){var n,r=0;for(n in t)r=(r<<5)-r+t.charCodeAt(n),r|=0;return e.colors[Math.abs(r)%e.colors.length]}(t),"function"===typeof e.init&&e.init(n),n}(e=t.exports=i.debug=i.default=i).coerce=function(t){return t instanceof Error?t.stack||t.message:t},e.disable=function(){e.enable("")},e.enable=function(t){e.save(t),e.names=[],e.skips=[];for(var n=("string"===typeof t?t:"").split(/[\s,]+/),r=n.length,i=0;i<r;i++)n[i]&&("-"===(t=n[i].replace(/\*/g,".*?"))[0]?e.skips.push(new RegExp("^"+t.substr(1)+"$")):e.names.push(new RegExp("^"+t+"$")))},e.enabled=function(t){var n,r;for(n=0,r=e.skips.length;n<r;n++)if(e.skips[n].test(t))return!1;for(n=0,r=e.names.length;n<r;n++)if(e.names[n].test(t))return!0;return!1},e.humanize=n("a+Rm"),e.names=[],e.skips=[],e.formatters={}},wbFr:function(t,e){t.exports="https://www.buzzfeed.com/static-assets/bf-user-profile-ui/_next/static/img/user.1616f008b47870c830fc4b2a30139fa6.jpg"}},[["d75D",0,2,6,8,1,3,4,7,5]]]);
//# sourceMappingURL=index-1e889153513f7c2100e3.js.map