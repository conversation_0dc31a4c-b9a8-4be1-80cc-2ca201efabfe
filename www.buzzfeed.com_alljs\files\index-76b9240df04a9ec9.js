(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[405],{28492:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return t(38258)}])},10543:function(e,n,t){"use strict";var r=t(52322),s=(t(2784),t(43997)),i=t(5632),a=t(30353),u=t(71177),c=t.n(u),o=t(50214),l=t(29199),b=t(86596),d=t(68758),f=t(19414),h=t(65831),p={huffpost:o.Z,buzzfeed:l.Z};n.Z=function(e){var n=e.brand,t=e.children,u=e.components,o=e.featuredNewsletter,l=e.showImage,_=void 0!==l&&l,m=e.type,v=void 0===m?"signup":m,x=(0,i.useRouter)().query.name,j=p[n],g="huffpost"===n,y=function(){var e=(0,s.v9)(b.R).email;return(0,h.P)(e),(0,r.jsx)(r.Fragment,{})};return"buzzfeed"===n?(0,r.jsxs)(d.Z,{children:[(0,r.jsx)(f.Z,{}),"authUser"===v&&(0,r.jsx)(y,{}),(0,r.jsx)("div",{className:"".concat(c().container," ").concat(c()[n]),children:(0,r.jsx)(j,{components:u,brand:n,featuredNewsletter:o,children:t})})]}):(0,r.jsxs)(r.Fragment,{children:[_&&g&&(0,r.jsx)("img",{className:c().newsletterImage,src:a.Dx[x],alt:""}),(0,r.jsx)("div",{className:"".concat(c().container," ").concat(c()[n]),children:(0,r.jsx)(j,{components:u,brand:n,featuredNewsletter:o,children:t})})]})}},38258:function(e,n,t){"use strict";t.r(n),t.d(n,{Page:function(){return G},__N_SSP:function(){return Q},default:function(){return J}});var r=t(52322),s=t(2784),i=t(5103),a=t(43997),u=t(87957),c=t(94285),o=t.n(c),l=function(){return(0,r.jsx)("div",{className:o().spinner})},b=t(22224),d=t.n(b),f=function(){return(0,r.jsx)("div",{className:d().wrapper,children:(0,r.jsx)("div",{className:d().spinner})})},h=t(45780),p=t(49896),_=t(33983),m=t(86596),v=t(74441),x=t(91819),j=t(68590),g=t(31336),y=t(36101),w=t(73178),N=t(15596),S=t(7762),z=t.n(S),A=t(12524),O=t.n(A);function C(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var E=function(e){var n,t=e.userSubscriptions,s=e.email,i=e.handleUnsubscribeFromAll,a=e.disableSubscribe,u=e.handleSubmit,c=e.brand,o=O()((C(n={},z().unsubscribeBtn,!0),C(n,z().unsubscribeDisabled,0===t.length),n));return(0,r.jsxs)("div",{className:z().container,children:[(0,r.jsxs)("div",{className:z().unsubscribeWrapper,children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:z().unsubscribeSubtitle,children:["Manage newsletters for ",(0,r.jsx)("span",{className:z().email,children:s})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:z().unsubscribeBtnLabel,children:"No longer interested?"}),(0,r.jsx)("button",{type:"button",className:"".concat(o," ").concat(z()[c]),onClick:i,disabled:0===t.length,children:"Unsubscribe from all."})]})]}),(0,r.jsx)("button",{type:"button",className:"".concat(z().saveButton," ").concat(z()[c]),disabled:a,"aria-disabled":a,onClick:u,children:"save changes"})]})},Z=t(13999),I=t.n(Z),T=t(52390);function k(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var B=function(e){var n,t=e.userSubscriptions,s=e.email,i=e.handleUnsubscribeFromAll,a=e.disableSubscribe,u=e.handleSubmit,c=e.notificationText,o=e.onCloseNotification,l=e.brand,b=O()((k(n={},I().unsubscribeAll,!0),k(n,I().unsubscribeDisabled,0===t.length),n));return(0,r.jsxs)("div",{className:I().container,children:[c&&(0,r.jsx)(T.Z,{notificationText:c,onCloseHandle:o,brand:l}),(0,r.jsxs)("div",{className:I().headerWrapper,children:[(0,r.jsxs)("div",{className:I().textWrapper,children:[(0,r.jsx)("h1",{id:"".concat(l,"-header"),className:I().unsubscribeTitleHuffpost,children:"Your Newsletters"}),(0,r.jsxs)("p",{className:I().unsubscribeSubtitle,children:["Manage newsletters for ",(0,r.jsx)("span",{className:I().email,children:s}),". No longer interested?",(0,r.jsx)("button",{type:"button",className:b,onClick:i,disabled:0===t.length,children:"Unsubscribe from all."})]})]}),(0,r.jsx)("button",{disabled:a,type:"button","aria-disabled":a,className:I().manageButton,onClick:u,children:"save changes"})]})]})},P=t(62646),W=t(4298);function D(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function U(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function M(e){return function(e){if(Array.isArray(e))return D(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,n){if(!e)return;if("string"===typeof e)return D(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return D(e,n)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var F={huffpost:l,buzzfeed:f},H={context_page_type:"feed",context_page_id:"newsletters",unit_type:"feed",unit_name:"main",subunit_type:"component",subunit_name:"save",item_type:"submission",item_name:"email",position_in_unit:0},Y=function(e){var n=e.type,t=(0,W.y)(),c=(0,a.I0)(),o=(0,i.useTranslation)("common").t,l=(0,a.v9)(_.cM),b=(0,s.useState)(null),d=b[0],f=b[1],S=(0,s.useState)(!1),z=S[0],A=S[1],O=(0,s.useState)([]),C=O[0],Z=O[1],I=(0,s.useState)([]),T=I[0],k=I[1],D=(0,s.useState)([]),Y=D[0],L=D[1],R=(0,s.useState)([]),X=R[0],q=R[1],V=(0,a.v9)(_.Or),Q=(0,a.v9)(g.H),G=(0,a.v9)(y.a),J=(0,a.v9)(y.w),K=(0,a.v9)(g.M),$=(0,a.v9)(v.Y).newsletters,ee=(0,a.v9)(m.R),ne=ee.subscriptions,te=ee.email,re=ee.status,se=ee.error,ie=G||te,ae="huffpost"===V,ue="buzzfeed"===V,ce=F[V],oe=(0,w.L)(n,V),le=$.find((function(e){return e.name===K})),be=!((null===Y||void 0===Y?void 0:Y.length)||(null===X||void 0===X?void 0:X.length));(0,s.useEffect)((function(){ne&&(k(ne),Z((function(e){return M(e).concat(M(ne))})))}),[ne,K]),(0,s.useEffect)((function(){if(K){f({title:o("unsubscribe_success",U({},"newsletter_name",le.title)),description:"",error:!1})}}),[]),(0,s.useEffect)((function(){if("resolved"===re){if(z)f((0,j.uy)()),k([]),Z([]);else{f((0,j.wv)());var e=M(Y).concat(M(T)).filter((function(e){return!X.includes(e)}));k(M(e))}q([]),L([])}if(se)if(f((0,j.A)(se)),z)q([]),L([]);else{var n=T.filter((function(e){return!Y.includes(e)}));k(M(n).concat(M(X))),Z(M(n).concat(M(X))),q([]),L([])}}),[re,se,z]);var de=function(e){f(null);var n=e.target.checked,t=e.target.name;n?(Z((function(e){return M(e).concat([t])})),T.includes(t)||L((function(e){return M(e).concat([t])})),T.includes(t)&&q((function(e){return e.filter((function(e){return e!==t}))}))):(Z((function(e){return e.filter((function(e){return e!==t}))})),T.includes(t)&&q((function(e){return M(e).concat([t])})),T.includes(t)||L((function(e){return e.filter((function(e){return e!==t}))})))},fe=function(e){(0,P.l6)({edition:"en-us",brand:V},function(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},r=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),r.forEach((function(n){U(e,n,t[n])}))}return e}({},H,{action_type:"save",action_value:Y,mode:t})),e.preventDefault();var n={cid:Q,subscriptions:Y,unsubscriptions:X,brand:V,source:oe};c((0,u.M_)(n)),A(!1),window.scrollTo({top:0,left:0,behavior:"smooth"})},he=function(e){e.preventDefault(),f(null);var n={cid:Q,subscriptions:[],unsubscriptions:T,source:oe,brand:V};c((0,u.M_)(n)),A(!0)};return(0,r.jsx)(r.Fragment,{children:""===ie?(0,r.jsx)(ce,{}):(0,r.jsxs)(r.Fragment,{children:["en-us"===l&&ae&&(0,r.jsx)(B,{userSubscriptions:T,email:ie,handleUnsubscribeFromAll:he,disableSubscribe:be,handleSubmit:fe,notificationText:d,onCloseNotification:function(){return f(null)},brand:V}),ue&&(0,r.jsx)(N.Z,{title:"Your Newsletters",hideSubOnMobile:!0,notificationText:d,onCloseNotification:function(){return f(null)},edition:l,type:n,brand:V,children:(0,r.jsx)(E,{userSubscriptions:T,email:ie,handleUnsubscribeFromAll:he,disableSubscribe:be,handleSubmit:fe,brand:V})}),$.length>1&&(0,r.jsx)(h.Z,{brand:V,notification:d,children:"buzzfeed"===V?(0,r.jsx)(p.Z,{newsletters:$,checkedItems:C,handleChange:de,brand:V}):$.map((function(e){return(0,r.jsx)(x.Z,{newsletter:e,checked:C.includes(e.name),onChange:de,brand:V,isHuffPostContributor:J},e.name)}))})]})})},L=t(10543),R=t(7189),X=t(36595),q=t.n(X),V={signup:R.Z,authUser:Y},Q=!0;function G(e){var n=e.brand,t=e.components,s=e.type,i=void 0===s?"signup":s,a=V[i];return(0,r.jsx)(L.Z,{brand:n,components:t,type:i,children:(0,r.jsx)("div",{className:"".concat(q().mainContent," ").concat(q()[n]),children:(0,r.jsx)(a,{type:i})})})}var J=(0,i.withTranslation)("common")(G)},7762:function(e){e.exports={container:"auth-header_container__I0cWm",unsubscribeWrapper:"auth-header_unsubscribeWrapper__UExAD",unsubscribeSubtitle:"auth-header_unsubscribeSubtitle__5kZpQ",saveButton:"auth-header_saveButton__VHOA0",buzzfeed:"auth-header_buzzfeed___JCqt",unsubscribeBtnLabel:"auth-header_unsubscribeBtnLabel__tsUY7",email:"auth-header_email__l8uK8",unsubscribeBtn:"auth-header_unsubscribeBtn___AEWI",unsubscribeDisabled:"auth-header_unsubscribeDisabled__96dVM"}},22224:function(e){e.exports={wrapper:"loader_wrapper__ts9Fy",spinner:"loader_spinner__azjTY","spinner-c7wet2":"loader_spinner-c7wet2__xzZih"}},71177:function(e){e.exports={container:"main-layout_container__EWpyI",buzzfeed:"main-layout_buzzfeed__rdj5_",huffpost:"main-layout_huffpost__efsVA",newsletterImage:"main-layout_newsletterImage__CN0rq"}},13999:function(e){e.exports={container:"huffpost-auth-header_container__N8SBI",headerWrapper:"huffpost-auth-header_headerWrapper__XRlXb",textWrapper:"huffpost-auth-header_textWrapper__PNE97",unsubscribeTitleHuffpost:"huffpost-auth-header_unsubscribeTitleHuffpost__0gOSG",unsubscribeSubtitle:"huffpost-auth-header_unsubscribeSubtitle__LAnQi",email:"huffpost-auth-header_email__teMZN",unsubscribeAll:"huffpost-auth-header_unsubscribeAll__rY0ej",manageButton:"huffpost-auth-header_manageButton__9i6E_",unsubscribeDisabled:"huffpost-auth-header_unsubscribeDisabled__6IsXs"}},94285:function(e){e.exports={spinner:"huffpost-loader_spinner__aR8hO","spinner-c7wet2":"huffpost-loader_spinner-c7wet2__Vq2uw"}}},function(e){e.O(0,[661,452,554,167,774,888,179],(function(){return n=28492,e(e.s=n);var n}));var n=e.O();_N_E=n}]);
//# sourceMappingURL=index-76b9240df04a9ec9.js.map