(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[405],{45081:function(t,n,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return e(28766)}])},28766:function(t,n,e){"use strict";e.r(n),e.d(n,{Page:function(){return it},__N_SSP:function(){return rt},default:function(){return ot}});var r=e(52322),i=e(22887),o=e(50142),a=e(75592),u=e(13269),s=e(2784),l=e(82391),c=e(12524),d=e.n(c),_=e(13980),f=e.n(_),m=(e(80700),{button:"button__1cArD",button__primary:"button__primary__N3io0",button__secondary:"button__secondary__dGLBO",button__warning:"button__warning__MtBMf",button__icon:"button__icon__3iK3P",padLeft:"padLeft__BK3FR",padRight:"padRight__2msRM",button__disabled:"button__disabled__GBde4",button__sm:"button__sm__1WK4C",button__md:"button__md__26Hgo",button__xl:"button__xl__3TK9c"});function b(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function p(t,n){if(null==t)return{};var e,r,i=function(t,n){if(null==t)return{};var e,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)e=o[r],n.indexOf(e)>=0||(i[e]=t[e]);return i}(t,n);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)e=o[r],n.indexOf(e)>=0||Object.prototype.propertyIsEnumerable.call(t,e)&&(i[e]=t[e])}return i}var h={PRIMARY:"primary",SECONDARY:"secondary",WARNING:"warning"},g="md",v=s.forwardRef((function(t,n){var e=t.children,r=void 0===e?null:e,i=t.className,o=void 0===i?null:i,a=t.disabled,u=void 0!==a&&a,c=t.onClick,_=void 0===c?function(){}:c,f=t.size,v=void 0===f?g:f,y=t.type,j=void 0===y?"button":y,x=t.variant,w=void 0===x?h.PRIMARY:x,N=p(t,["children","className","disabled","onClick","size","type","variant"]),C=d()(m.button,m["button__".concat(w)],m["button__".concat(v)],b({},m.button__disabled,!!u),o);return s.createElement("button",(0,l.Z)({type:j,disabled:u,className:C,ref:n,onClick:_},N),r)}));v.displayName="Button";v.propTypes={variant:f().oneOf(Object.values(h))};var y=e(34133),j=e(64203),x=e(63808);function w(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function N(t){return function(t){if(Array.isArray(t))return w(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(!t)return;if("string"===typeof t)return w(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return w(t,n)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function C(t){for(var n=N(t),e=t.length-1;e>0;e--){var r,i=Math.floor(Math.random()*(e+1));r=[n[i],n[e]],n[e]=r[0],n[i]=r[1]}return n}var O="trending_cache",S=function(t){var n=function(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:20;if(n){var r=n.map((function(t){return t.id}));t=t.filter((function(t){return!r.includes(t.id)}))}return C(t).slice(0,e)}(t,function(){var t=window.localStorage.getItem(O),n={};try{n=JSON.parse(t)||{}}catch(e){console.debug(e)}return n}().items);return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};window.localStorage.setItem(O,JSON.stringify(t))}({items:n}),n},P=e(35173),z=e(32510),k=e(55821),E=e.n(k),M=function(){return Array(10).fill(1).map((function(t,n){return(0,r.jsxs)("div",{className:E().postContainer,children:[(0,r.jsx)("a",{className:E().thumbnailContainer,disabled:!0,children:(0,r.jsx)(z.Z,{})}),(0,r.jsx)("div",{className:E().postContent,children:(0,r.jsxs)("a",{className:E().postTitle,disabled:!0,children:[(0,r.jsx)("div",{className:E().titlePlaceholder}),(0,r.jsx)("div",{className:E().titlePlaceholder}),(0,r.jsx)("div",{className:E().titlePlaceholder})]})})]},n)}))},T=e(33176),A=e(34050),R=e.n(A),Z=e(64731),I=e(99376);function B(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}var q=function(t){var n=t.id,e=t.tracking;return(0,s.useMemo)((function(){return function(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{},r=Object.keys(e);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(e).filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})))),r.forEach((function(n){B(t,n,e[n])}))}return t}({subunit_type:"component",subunit_name:"trending_quizzes",item_type:"card",item_name:n,target_content_type:"buzz",target_content_id:n,position_in_unit:0},e)}),[n,e])},D=function(t){var n=t.item,e=n.id,i=n.tracking,o=n.images,a=q({id:e,tracking:i}),u=(0,I.Z)(a),s=u.ref,l=u.trackClick;return(0,r.jsxs)("div",{className:R().container,ref:s,children:[(0,r.jsx)("a",{className:R().thumbnailContainer,href:n.url,onClick:l,children:(0,r.jsx)(z.Z,{images:o,size:"big"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{children:(0,r.jsx)("a",{className:R().title,href:n.url,onClick:l,children:null===n||void 0===n?void 0:n.title})}),(0,r.jsx)(Z.S,{author:null===n||void 0===n?void 0:n.author,className:R().authorContainer,trending:!0})]})]})},G=e(37301),K=e.n(G),U=(0,x.J)((function(t){var n=t.trending,e=(0,s.useState)(!1),o=e[0],a=e[1],u=(0,s.useState)([]),l=u[0],c=u[1],d=o?20:10,_=(0,j.Z)({subunit_type:"component",subunit_name:"trending_quizzes",item_type:"button",item_name:"Show More Trending Quizzes",position_in_unit:0,position_in_subunit:d,action_type:"show",action_value:"show_more_quizzes"}),f=(0,P.Z)(),m=(f.isXS,f.isSM,(0,i.useTranslation)("common").t);return(0,s.useEffect)((function(){c(S(n))}),[n]),(0,r.jsxs)("div",{className:K().outerContainer,children:[(0,r.jsx)(T.Z,{title:m("trending")},"trending-title"),(0,r.jsx)("div",{className:K().container,children:(null===l||void 0===l?void 0:l.length)?l.map((function(t,n){return n<d&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(D,{item:t},t.id)})})):(0,r.jsx)(M,{})},"trending-items"),(0,r.jsx)("div",{className:K().buttonContainer,children:!o&&(0,r.jsx)(v,{className:K().showMoreButton,onClick:function(){_(),a(!0)},children:m("show_more_trending")})})]})}),y.GG),W=e(35193),F=e(21451),J=e(45626),L=e(1272),X=e(13993),Y=e(50433),H=e(64057);var V=function(){var t="adshield"===(0,(0,s.useContext)(Y.WN).getExperimentValue)("RT-1710-adshield-experiment",{rejectErrors:!1}),n=(0,s.useState)(!1),e=n[0],i=n[1];return(0,s.useEffect)((function(){t&&!e&&(i(!0),H._c.init({isShopping:!1,destination:"buzzfeed"}))}),[t]),(0,r.jsx)(r.Fragment,{})},Q=e(56392),$=e(50232),tt=e.n($);function nt(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function et(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{},r=Object.keys(e);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(e).filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})))),r.forEach((function(n){nt(t,n,e[n])}))}return t}var rt=!0;function it(t){var n=t.adsDisabled,e=t.header,i=t.feed,s=t.trending,l=t.dailyTrivia,c=t.pageConfig,d=(null===i||void 0===i?void 0:i.next)||"",_=(null===i||void 0===i?void 0:i.items)||[],f=_.find((function(t){return"featured"===t.type})),m=(null===s||void 0===s?void 0:s.items)||[];return(0,Q.T)(),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.default,{pageConfig:c}),(0,r.jsx)(L.Z,et({},e)),!n&&(0,r.jsx)(V,{}),(0,r.jsxs)("main",{className:tt().content,children:[(0,r.jsx)(W.Z,{}),(0,r.jsxs)("div",{className:tt().body,children:[(0,r.jsx)(X.Z,{item:f}),(0,r.jsx)("div",{className:tt().newsletterHomepage,children:(0,r.jsx)(J.Z,{})})]}),(0,r.jsx)(U,{trending:m}),(0,r.jsxs)("div",{className:tt().body,children:[(0,r.jsx)(u.ZP,{items:_,nextUrl:d,dailyTrivia:l}),(0,r.jsx)(F.Z,{})]}),(0,r.jsx)(o.z,{})]})]})}var ot=(0,i.withTranslation)("common")(it)},37301:function(t){t.exports={outerContainer:"trending_outerContainer__UIrwu",container:"trending_container__BwYa5",buttonContainer:"trending_buttonContainer__3M_bD",showMoreButton:"trending_showMoreButton__r6NKO"}},55821:function(t){t.exports={container:"trending-items-placeholder_container__c_eum",title:"trending-items-placeholder_title__Up61a",titlePlaceholder:"trending-items-placeholder_titlePlaceholder__tEiBA",postTitle:"trending-items-placeholder_postTitle__i3_Rq",postContent:"trending-items-placeholder_postContent__bVLUe",postContainer:"trending-items-placeholder_postContainer__bPubp",thumbnailContainer:"trending-items-placeholder_thumbnailContainer__6G1_X"}},34050:function(t){t.exports={container:"trendingquizitem_container__9IuNr",thumbnailContainer:"trendingquizitem_thumbnailContainer__aKWvW",title:"trendingquizitem_title__ke0rH",authorContainer:"trendingquizitem_authorContainer__MJofo"}},80700:function(){}},function(t){t.O(0,[297,592,727,774,888,179],(function(){return n=45081,t(t.s=n);var n}));var n=t.O();_N_E=n}]);
//# sourceMappingURL=index-a24ecfbf331a17c6.js.map