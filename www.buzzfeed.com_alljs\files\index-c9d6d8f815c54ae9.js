(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[405],{28492:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return t(11083)}])},11083:function(e,n,t){"use strict";t.r(n),t.d(n,{Page:function(){return Jn},__N_SSP:function(){return Qn},default:function(){return Yn}});var o=t(52322),r=t(2784),a=t(28964),s=function(e){return!!e&&!e.toLowerCase().match(/\bbuzzfeed(?:news)?\.(?:com|io)(?:\/.*)?$|(?:^\/.*)/)},l=t(31066),c=t(13980),u=t.n(c),d=t(6294),p=t(2706),m=t(25969),f=function(e){var n=e.className,t=void 0===n?"":n;return(0,o.jsxs)("svg",{"aria-labelledby":"svgTrendingTitle",className:t,fill:"none",height:"92",role:"img",width:"92",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("title",{id:"svgTrendingTitle",children:"Trending"}),(0,o.jsxs)("g",{children:[(0,o.jsx)("path",{fill:"#5246F5",d:"M80.82 50.95 81.1 48l7.75.75.2-2.13 2.55.25-.7 7.17-2.54-.24.2-2.12-7.74-.74Zm-2.71 9.95 1.03-3.08 3.57-.04.23-.7-3.2-1.07.92-2.78 9.81 3.27-1.44 4.32a3.32 3.32 0 0 1-4.36 2.2c-1.64-.54-2.2-1.64-2.36-2.41l-4.2.28Zm7.47-.7a.92.92 0 0 0 1.23-.61l.37-1.1-1.87-.63-.36 1.1a.93.93 0 0 0 .63 1.23Zm-11.3 7.01 3.4-5.57 8.82 5.4-3.4 5.56-2.13-1.3 1.88-3.07-1.17-.71-1.82 2.98-2.13-1.3 1.82-2.98-1.27-.78-1.87 3.07-2.13-1.3Zm-6.08 6.43 1.94-1.98 5.74 1.74-3.8-3.72 2.02-2.07 7.4 7.23-2.09 2.12-5.39-1.65 3.58 3.5-2.02 2.06-7.38-7.23Zm-3.63 2.26 3.35-2.04 5.39 8.83-3.33 2.03c-2.6 1.59-5.2.76-6.82-1.88-1.63-2.66-1.18-5.35 1.41-6.93Zm4.9 6.13-2.72-4.46-.83.51c-.98.6-1.08 1.8-.22 3.2.91 1.5 1.91 1.89 2.94 1.26l.84-.5ZM57.3 79.36l2.74-1.03 3.63 9.68-2.74 1.03-3.63-9.68Zm-9.36 2.02 2.73-.48 3.65 4.76-.92-5.23 2.86-.5 1.78 10.18-2.93.51-3.41-4.48.86 4.93-2.84.5-1.78-10.19Zm-.44 5.19c-.27 3.24-2.32 5.18-5.16 4.95a3.92 3.92 0 0 1-3.57-3.01l2.48-.88c.16.67.58 1.25 1.3 1.3 1 .09 1.8-.69 1.96-2.6.16-1.9-.51-2.81-1.52-2.9a1.9 1.9 0 0 0-1.25.3l-.06.74 1.47.11-.2 2.48-4.17-.34.35-4.39a5.03 5.03 0 0 1 4.07-1.47c2.86.23 4.56 2.5 4.3 5.71ZM33.86 13.35l-2.72 1.14-3.02-7.17-1.97.83-.99-2.36 6.64-2.8 1 2.36-1.96.82 3.02 7.18ZM43.9 11l-3.2.57-1.72-3.13-.74.13.59 3.33-2.89.5-1.78-10.18 4.49-.79c2.1-.37 3.68.91 4.01 2.8.3 1.7-.4 2.72-1 3.22l2.24 3.56Zm-4.17-6.23a.92.92 0 0 0-1.11-.8l-1.15.2.34 1.94 1.15-.2c.52-.1.88-.53.77-1.14Zm11.55 6.61-6.52-.35.56-10.32 6.51.35-.13 2.5-3.6-.2-.06 1.36 3.48.19-.14 2.5-3.48-.2-.08 1.49 3.6.2-.14 2.48Zm8.55 2.31-2.67-.77-1.2-5.88-1.47 5.11-2.78-.8 2.85-9.93 2.86.82 1.1 5.53 1.4-4.82 2.76.8-2.85 9.94Zm3.71 2.11-3.39-1.98 5.2-8.93 3.38 1.97c2.63 1.53 3.14 4.22 1.58 6.9-1.57 2.69-4.15 3.57-6.77 2.04Zm3.06-7.22-2.63 4.51.84.5c1 .57 2.1.09 2.93-1.33.88-1.52.75-2.58-.3-3.19l-.84-.5Zm3.43 11.98-2.2-1.92 6.79-7.8 2.2 1.93-6.79 7.8Zm6.22 7.29-1.71-2.18 2.45-5.47-4.17 3.29-1.8-2.28 8.12-6.4 1.84 2.34-2.32 5.13 3.93-3.1 1.78 2.26-8.12 6.4Zm4.78-2.08c2.98-1.3 5.66-.42 6.8 2.2a3.92 3.92 0 0 1-.96 4.56l-1.94-1.77c.5-.46.82-1.1.53-1.75-.4-.93-1.47-1.27-3.23-.5-1.74.77-2.23 1.79-1.82 2.71.2.47.5.8.85.97l.68-.3-.6-1.35 2.28-1 1.68 3.84-4.03 1.77a5.03 5.03 0 0 1-3.23-2.89c-1.15-2.62.04-5.2 2.99-6.49ZM24.62 73.83l2.4 1.7-4.52 6.34 1.74 1.24-1.49 2.08-5.86-4.18 1.48-2.09 1.73 1.24 4.52-6.33Zm-7.26-7.33 2.15 2.43-1.75 3.11.5.56 2.52-2.24 1.94 2.19L15 79.4 11.98 76a3.32 3.32 0 0 1 .26-4.89c1.3-1.15 2.54-1.09 3.28-.84l1.85-3.77Zm-3.13 6.83a.92.92 0 0 0-.09 1.36l.77.87 1.47-1.3-.77-.87c-.35-.4-.91-.48-1.38-.06Zm-.43-13.3 3.13 5.73-9.08 4.94-3.12-5.73 2.19-1.19 1.72 3.16 1.2-.66-1.67-3.06 2.19-1.2 1.67 3.07 1.3-.71-1.71-3.16 2.19-1.2Zm-2.52-8.49.75 2.68-4.38 4.1 5.12-1.43.78 2.79-9.95 2.78-.8-2.86 4.12-3.84L2.1 57.1l-.78-2.77 9.96-2.79Zm-.15-4.27.1 3.93-10.34.25-.1-3.9c-.07-3.04 1.94-4.89 5.04-4.97 3.12-.07 5.22 1.66 5.3 4.7Zm-7.76 1.19 5.23-.13-.03-.98c-.03-1.14-1.02-1.83-2.66-1.8-1.75.05-2.59.72-2.56 1.93l.02.98Zm8.4-9.21-.48 2.89-10.2-1.7.49-2.89 10.2 1.7Zm2.93-9.12-.95 2.6-5.94.79 5 1.82-1 2.72-9.71-3.55 1.02-2.8 5.59-.7L4 29.29l1-2.71 9.7 3.55Zm-4.27-2.98c-2.67-1.85-3.32-4.6-1.7-6.94a3.92 3.92 0 0 1 4.39-1.58l-.49 2.58c-.65-.19-1.37-.11-1.77.47-.57.83-.3 1.92 1.27 3.01 1.57 1.09 2.7.96 3.27.13.29-.42.41-.85.38-1.23l-.62-.42-.83 1.21-2.04-1.4 2.38-3.45 3.62 2.5a5.03 5.03 0 0 1-.76 4.26c-1.63 2.36-4.44 2.7-7.1.86Z"}),(0,o.jsx)("path",{fill:"#CDC9FF",d:"M21.3 16.8a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm64.25 26.4a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM31.6 84.94a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"})]}),(0,o.jsx)("path",{fill:"#5246F5",fillRule:"evenodd",d:"M39.54 44.9c2.27-1.76 4.02-4.6 1.95-9.86a.34.34 0 0 1 .33-.47c5.62.19 7.47 3.38 7.87 **********.*********.57-.65.85-1.29.97-2.18.03-.2.24-.35.44-.28 5.23 1.87 9.97 13.53 1.58 19.06-2.1 1.38-4.68 2.28-8.47 2.28-12.39 0-12.88-14.02-7.01-16.94.21-.**********.3.02 1.18.17 1.82.99 *********.22.02.3-.05Zm3.5 6.88c1.26-.46 1.64-1.24 1.36-3.56-.03-.22.12-.43.34-.44 1.66-.1 2.8 1.22 3.1 ********.76.75 **********-.3.6-.63.78-.92.49-.8.76-.98 1-.87 2.41 1.12 2.93 6.26-1.22 9a7.5 7.5 0 0 1-4.34 1.17c-6.13 0-6.57-6.7-3.89-8.5.24-.***********.**********.**********.*********** 0Z",clipRule:"evenodd"})]})},v=t(36774),h=t(1013),L=t(58599),b=t(23107),g=t.n(b),_=t(5819);function C(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,o=new Array(n);t<n;t++)o[t]=e[t];return o}function x(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function y(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){x(e,n,t[n])}))}return e}function j(e){return function(e){if(Array.isArray(e))return e}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,n){if(!e)return;if("string"===typeof e)return C(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return C(e,n)}(e,i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var w="/trending",N=function(e){var n,t=e.className,r=void 0===t?"":t,i=e.data,a=void 0===i?{items:[]}:i,s=(e.hasSponsoredTabEnabled,e.trackingData),l=j(a.items.map((function(e){var n;return(null===e||void 0===e||null===(n=e.content)||void 0===n?void 0:n.url)?y({},e,{content:y({},e.content,{url:(0,_.b)(e.content.url,"hfspl")})}):e}))),c=l[0],u=l.slice(1),b=!(0,p.isServer)()&&(0,d.tq)(),C=y({subunit_name:"trending",subunit_type:"package"},s),x=y({},s,{item_type:"text",target_content_type:"feed",target_content_id:"trending"});return(null===c||void 0===c?void 0:c.content)?(0,o.jsxs)("section",{className:"".concat(r),children:[(0,o.jsxs)("header",{className:g().headline,children:[(0,o.jsx)("h2",{children:"Trending Now"}),(0,o.jsxs)(L.Z,{className:g().cta,href:w,commonTrackingData:y({},x,{item_name:"see_all"}),children:["See All",(0,o.jsx)(m._,{})]}),(0,o.jsx)(f,{className:g().spinner})]}),(0,o.jsxs)("ol",{className:g().trendingPosts,children:[(0,o.jsx)("li",{className:"".concat(g().primaryCard),children:(0,o.jsx)(v.Z,{className:"primaryCard",item:c,index:1,imageRatioTablet:"1/1",imageRatioDesktop:"1/1",isTrackable:!0,trackingData:y({},C,null===c||void 0===c||null===(n=c.content)||void 0===n?void 0:n.trackingData,{position_in_subunit:0})})}),u.map((function(e,n){var t;return(0,o.jsx)("li",{className:"".concat(g().secondaryCard),children:(0,o.jsx)(v.Z,{item:e,index:n+2,isPrimary:!1,imageRatioDesktop:"1/1",isTrackable:!0,trackingData:y({},C,null===e||void 0===e||null===(t=e.content)||void 0===t?void 0:t.trackingData,{position_in_subunit:n+1})})},e.id)}))]}),(0,o.jsxs)(L.Z,{className:g().ctaBottom,href:w,commonTrackingData:y({},x,{item_name:"see_all_trending",position_in_subunit:a.items.length}),children:["See All Trending",(0,o.jsx)(m._,{})]}),b&&(0,o.jsx)(h.B,{type:"story1"})]}):null};N.propTypes={className:u().string,data:u().object.isRequired};var Z=N,O=t(34783),k=t(39852),P=t(80713),M=t(52003),T=t.n(M),S=t(9052),D=t.n(S);function E(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function F(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){E(e,n,t[n])}))}return e}var R=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.className,t=void 0===n?"":n,r=e.item,i=void 0===r?{}:r,a=e.trackingData,l=void 0===a?{}:a,c=i||{},u=c.content,d=c.id,p=c.object_type,m=null===i||void 0===i?void 0:i.sponsorship,f=null===m||void 0===m?void 0:m.sponsor;if(!u)return null;var v=u.url,h=u.headline,L=u.images,b=u.content_reactions,g=void 0===b?{}:b,C=u.trackingData,x=(0,_.b)(v,"hfmotd"),y=s(x),j={subunit_type:"component",subunit_name:"".concat(p,"|").concat(d),position_in_subunit:0},w=F({},C,l,j,{item_name:"moment_of_the_day",target_content_type:"url",target_content_id:(null===x||void 0===x?void 0:x.length)?x:h}),N=F({},w,{item_type:"card"}),Z=F({},w,{item_type:"text",item_name:"presented_by"}),M=function(){var e;return(null===L||void 0===L||null===(e=L.standard)||void 0===e?void 0:e.length)?(0,o.jsx)("figure",{children:(0,o.jsx)(O.lS,{src:L.standard,alt:null===L||void 0===L?void 0:L.standard_alt_text})}):(0,o.jsx)("figure",{children:(0,o.jsx)("img",{src:T(),alt:null===L||void 0===L?void 0:L.standard_alt_text})})};return(0,o.jsx)("section",{id:"moment-of-the-day",className:t,children:(0,o.jsxs)(k.Z,{className:D().motd,children:[(null===x||void 0===x?void 0:x.length)?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(k.Z.Link,{url:x,trackingData:N,isTrackable:!0,forceNewBrowserTab:y,children:(0,o.jsx)("h3",{children:h})}),!!f&&(0,o.jsx)(P.Z,{data:f,isTrackable:!0,trackingData:Z,showLogo:!1}),(0,o.jsx)(k.Z.Link,{url:x,trackingData:N,isTrackable:!0,forceNewBrowserTab:y,children:(0,o.jsx)(M,{})})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("h3",{children:h}),!!f&&(0,o.jsx)(P.Z,{data:f,isTrackable:!0,trackingData:Z,showLogo:!1}),(0,o.jsx)(M,{})]}),(0,o.jsx)(k.Z.Reactions,{className:D().emojiReactions,contentId:Number(d),contentType:"content-object",data:g,isTrackable:!0,trackingData:w})]})})};R.propTypes={className:u().string,item:u().object.isRequired,trackingData:u().object};var I=R,B=t(12349),V=t.n(B);function A(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var H=function(e){var n,t,r,i,s=e.item,l=e.index,c=e.trackingData,u=s.content,d=s.id,p=s.object_type,m=u.thumbnail,f=void 0===m?{}:m,v=u.url,h=void 0===v?"":v,L=u.title,b=void 0===L?"":L,g=u.id,_=void 0===g?"":g,C=u.trackingData,x=void 0===C?{}:C,y=(0,a.Xi)(h),j=(null===f||void 0===f||null===(n=f.square)||void 0===n?void 0:n.url)||(null===f||void 0===f||null===(t=f.standard)||void 0===t?void 0:t.url)||T(),w=(null===f||void 0===f||null===(r=f.square)||void 0===r?void 0:r.alt)||(null===f||void 0===f||null===(i=f.standard)||void 0===i?void 0:i.alt)||"",N=function(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){A(e,n,t[n])}))}return e}({},{subunit_type:"component",subunit_name:"".concat(p,"|").concat(d)},x,{item_type:"card",item_name:_,position_in_subunit:l,target_content_type:"buzz",target_content_id:_},c);return(null===_||void 0===_?void 0:_.length)?(0,o.jsxs)(k.Z,{className:V().shoppingPost,trackingData:N,isTrackable:!0,children:[(0,o.jsx)(k.Z.Link,{url:y,isTrackable:!0,trackingData:N,children:(0,o.jsx)("figure",{children:(0,o.jsx)(k.Z.Thumbnail,{thumbnail:j,alt:String(w),lazyLoadThumbnail:!0,ratioTablet:"1/1"})})}),(0,o.jsx)("div",{children:(0,o.jsx)(k.Z.Link,{url:y,isTrackable:!0,trackingData:N,children:(0,o.jsx)("h3",{children:b})})})]}):null};H.propTypes={data:u().shape({category:u().shape({label:u().string.isRequired,url:u().string.isRequired}),thumbnail:u().object.isRequired,title:u().string.isRequired,url:u().string.isRequired})};var z=H,q=t(92977),W=t.n(q),U=function(e){var n=e.className,t=void 0===n?"":n;return(0,o.jsxs)("svg",{className:t,xmlns:"http://www.w3.org/2000/svg",width:"31",height:"33",viewBox:"0 0 31 33",fill:"none",children:[(0,o.jsx)("path",{d:"M8.03384 10.1985C7.7574 10.1776 7.55537 9.93061 7.58305 9.64729L7.82138 7.20739C8.12865 4.06178 11.1656 1.25557 14.5914 0.948742C16.3822 0.791705 18.0345 1.34548 19.1244 2.47256C20.0791 3.46315 20.5284 4.80648 20.3869 6.25566L20.1933 8.23711C20.1657 8.52042 19.9187 8.73355 19.6422 8.71266C19.3658 8.69178 19.1638 8.44474 19.1914 8.16143L19.385 6.17997C19.4967 5.03643 19.146 3.97903 18.3997 3.20483C17.5153 2.28874 16.1462 1.84284 14.6549 1.97407C11.7008 2.23869 9.08525 4.61836 8.82494 7.2832L8.58661 9.7231C8.55893 10.0064 8.31196 10.2195 8.03552 10.1987L8.03384 10.1985Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M2.80922 10.5885L4.56083 11.8095L6.63249 10.0655L8.38411 11.2865L10.4582 9.53566L12.2008 10.763L14.2747 9.01389L16.0239 10.2417L18.0979 8.49086L19.8488 9.71873L21.9212 7.96782L23.2349 9.15906L20.8137 31.4741L1.12034 28.3404L2.80922 10.5885Z",fill:"#F9F7F7"}),(0,o.jsx)("path",{d:"M20.7585 31.9905C20.7437 31.9895 20.7305 31.9886 20.7158 31.9859L1.02021 28.852C0.77071 28.8125 0.600155 28.5805 0.62565 28.3149L2.31637 10.5617C2.33473 10.3773 2.44379 10.2129 2.60556 10.1284C2.76714 10.0456 2.95523 10.0549 3.09864 10.1549L4.55285 11.1683L6.33434 9.67002C6.50796 9.52214 6.7466 9.50884 6.92235 9.63185L8.37656 10.6453L10.1588 9.14005C10.3322 8.99389 10.5729 8.97726 10.7499 9.10383L12.1937 10.1217L13.9741 8.61814C14.1475 8.47198 14.3882 8.45534 14.5637 8.58008L16.0174 9.59867L17.7994 8.09517C17.9729 7.94901 18.2135 7.93238 18.3891 8.05712L19.8427 9.0757L21.6231 7.5721C21.8149 7.40982 22.0819 7.4106 22.2586 7.57013L23.571 8.75962C23.6871 8.86644 23.7462 9.02492 23.7283 9.19032L21.3069 31.5071C21.2912 31.6518 21.2188 31.784 21.106 31.8753C21.0059 31.957 20.8805 31.9971 20.7587 31.9888L20.7585 31.9905ZM1.65569 27.9024L20.3828 30.882L22.7149 9.38878L21.8933 8.64395L20.1496 10.1153C19.9762 10.2615 19.7371 10.2782 19.5599 10.1534L18.1063 9.13481L16.3242 10.6383C16.1508 10.7845 15.9101 10.8011 15.7346 10.6764L14.2809 9.65777L12.5005 11.1614C12.3271 11.3075 12.0864 11.3242 11.9094 11.1976L10.464 10.1796L8.6836 11.6832C8.51016 11.8293 8.27134 11.8444 8.09394 11.7212L6.63973 10.7078L4.85825 12.2062C4.68481 12.3523 4.44598 12.3673 4.27024 12.2443L3.21538 11.5097L1.6555 27.9041L1.65569 27.9024Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M28.6909 10.1372L27.165 10.9957L26.7567 9.37512L25.4791 10.2925L24.9308 8.55908L23.6556 9.45267L21.379 31.5806L26.6638 29.8403L28.6909 10.1372Z",fill:"#FF9C9F"}),(0,o.jsx)("path",{d:"M21.3045 32.0966C21.2098 32.0898 21.1198 32.0526 21.047 31.9874C20.929 31.883 20.8678 31.7209 20.8851 31.5541L23.173 9.41228C23.1893 9.25404 23.2743 9.10926 23.3993 9.02221L24.6844 8.12837C24.8077 8.0429 24.9565 8.01924 25.0931 8.0667C25.2281 8.11406 25.3328 8.22438 25.3784 8.36818L25.7398 9.50299L26.5175 8.94831C26.6447 8.85626 26.8029 8.83328 26.942 8.88778C27.0812 8.94057 27.1864 9.06121 27.2261 9.21659L27.4879 10.2534L28.509 9.68187C28.6629 9.59343 28.8481 9.60152 28.9887 9.70241C29.1294 9.80329 29.2049 9.9801 29.1859 10.1639L27.1487 29.8794C27.1269 30.0904 26.9859 30.2705 26.7966 30.3324L21.4755 32.0728C21.418 32.091 21.3599 32.0988 21.3031 32.0948L21.3045 32.0966ZM24.091 9.73151L21.9054 30.8831L26.2408 29.4631L28.1474 11.0119L27.3811 11.4405C27.2543 11.5137 27.1074 11.5187 26.978 11.4632C26.8503 11.4061 26.7542 11.2895 26.7183 11.1447L26.4732 10.1776L25.7299 10.7073C25.6063 10.7944 25.4525 10.8212 25.319 10.7739C25.1822 10.7282 25.0761 10.616 25.0306 10.4722L24.6667 9.33039L24.0894 9.73139L24.091 9.73151Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M18.6755 20.0802L18.4232 14.607L12.3998 16.278L14.5583 17.5921L12.0222 20.7442L8.48587 18.5995L4.14818 23.973L6.09722 25.1582L9.02887 21.5218L12.5727 23.6597L16.5164 18.772L18.6755 20.0802Z",fill:"#222222"})]})},K=t(97196);function G(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function Q(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){G(e,n,t[n])}))}return e}var J=function(e){var n,t=e.adsDisabled,i=e.className,a=void 0===i?"":i,s=e.data,l=void 0===s?{}:s,c=e.trackingData,u=void 0===c?{}:c,d=(0,K.ac)().breakpoint,p="xs"===d||"sm"===d,m=(0,r.useMemo)((function(){var e;if(!(null===l||void 0===l||null===(e=l.items)||void 0===e?void 0:e.length))return[];var n=p?4:2;return l.items.slice(0,n)}),[l.items,p]);if(!(null===l||void 0===l||null===(n=l.items)||void 0===n?void 0:n.length))return null;var f=l.display_name||"Shopping",v=Q({},u,{item_type:"text",item_name:"shopping_splash_header",target_content_type:"feed",target_content_id:6});return(0,o.jsxs)("section",{id:"shopping-posts",className:"".concat(a," ").concat(W().shoppingPosts),children:[(0,o.jsx)("header",{className:W().header,children:t?(0,o.jsx)("h2",{className:W().title,children:"BuzzFeed+ Exclusive Content"}):(0,o.jsxs)(L.Z,{href:"https://www.buzzfeed.com/shopping",className:W().headerLink,commonTrackingData:v,children:[(0,o.jsx)(U,{className:W().icon}),(0,o.jsx)("h2",{className:W().title,children:f})]})}),(0,o.jsx)("div",{className:W().postsContainer,children:m.map((function(e,n){var t=Q({},e,{content:Q({},e.content,{url:(0,_.b)(e.content.url,"hfshp")})});return(0,o.jsx)(z,{item:t,index:n,trackingData:Q({subunit_type:"package",subunit_name:"shopping"},u)},e.id)}))})]})};J.propTypes={adsDisabled:u().bool,className:u().string,data:u().shape({display_name:u().string,name:u().string,items:u().arrayOf(u().object).isRequired}).isRequired,trackingData:u().object};var Y=J,X=t(50127),$=t(73206),ee=t.n($);function ne(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function te(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){ne(e,n,t[n])}))}return e}var oe=function(e){var n,t,r,i=e.adsDisabled,a=void 0!==i&&i,s=e.isTrackable,l=void 0!==s&&s,c=e.trackingData,u=void 0===c?{}:c,d=e.zones,p=void 0===d?{}:d,m=e.hasSponsoredTabEnabled,f=void 0!==m&&m,v=e.children,h=void 0===v?null:v,L=te({},u,{unit_name:"splash",unit_type:"feed"});return(0,o.jsxs)("header",{className:"feed-content-area ".concat(ee().splash),children:[(0,o.jsx)(Z,{data:p.splash_trending,className:ee().trendingPosts,hasSponsoredTabEnabled:f,trackingData:te({position_in_unit:0},L)}),(0,o.jsx)(Y,{adsDisabled:a,data:p.splash_shopping,className:ee().shoppingPosts,trackingData:te({position_in_unit:1},L)}),h,(0,o.jsx)(I,{item:(null===(n=p.splash_moment)||void 0===n||null===(t=n.items)||void 0===t?void 0:t[0])||{},className:ee().momentOfTheDay,trackingData:te({position_in_unit:2},L)}),(0,o.jsx)(X.Z,{className:ee().funModule,items:(null===(r=p.splash_fun)||void 0===r?void 0:r.items)||[],isTrackable:l,trackingData:te({position_in_unit:3},L)})]})};oe.propTypes={zones:u().object.isRequired};var re=oe,ie=function(e){var n=e.className,t=void 0===n?"":n;return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 33 32",fill:"none",className:t,children:(0,o.jsx)("path",{d:"M8.78492 17.6873L14.9716 6.24196C15.1479 5.91583 15.6429 6.04106 15.6429 6.41179V13.0143C15.6429 13.1248 15.7325 13.2143 15.8429 13.2143H22.8609C23.1403 13.2143 23.3115 13.5207 23.1651 13.7586L15.5899 26.0683C15.4012 26.3749 14.9286 26.2412 14.9286 25.8811V18.4143C14.9286 18.3038 14.8391 18.2143 14.7286 18.2143H9.0991C8.8286 18.2143 8.65629 17.9253 8.78492 17.6873Z",fill:"#D44116",stroke:"#D44116",strokeWidth:"2"})})},ae=t(42184),se=t.n(ae);function le(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var ce=function(e){var n,t,r=e.className,i=void 0===r?"":r,a=e.title,s=e.description,l=e.cta,c=e.trackingData,u=function(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){le(e,n,t[n])}))}return e}({},c,{item_type:"button",item_name:(null===c||void 0===c?void 0:c.item_name)||(null===l||void 0===l?void 0:l.text),target_content_type:"url",target_content_id:null===l||void 0===l?void 0:l.url}),d=(0,K.Si)({trackingData:u}).setObservable;return(null===a||void 0===a?void 0:a.length)&&(null===l||void 0===l||null===(n=l.text)||void 0===n?void 0:n.length)&&(null===l||void 0===l||null===(t=l.url)||void 0===t?void 0:t.length)?(0,o.jsx)("aside",{className:"".concat(se().container," ").concat(i?se()[i]:""),ref:function(e){d(e)},children:(0,o.jsxs)("div",{className:se().content,children:[(0,o.jsx)(ie,{className:se().icon}),(0,o.jsx)("h2",{className:se().title,children:a}),!!(null===s||void 0===s?void 0:s.length)&&(0,o.jsx)("p",{className:se().body,children:s}),(0,o.jsx)(L.Z,{className:se().button,href:l.url,commonTrackingData:u,children:l.text})]})}):null};function ue(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function de(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){ue(e,n,t[n])}))}return e}var pe={manual:ce},me=function(e){var n=e.className,t=void 0===n?"":n,r=e.item,i=e.trackingData,a=r.content,s=r.id,l=r.object_type,c=a.type,u=de({},a.trackingData,i,{subunit_type:"component",subunit_name:"".concat(l,"|").concat(s),position_in_subunit:0}),d=pe[c];return d?(0,o.jsx)(d,de({className:t},a,{trackingData:u})):null};me.propTypes={className:u().string,trackingData:u().object,item:u().shape({content:u().shape({type:u().string,title:u().string,description:u().string,cta:u().shape({text:u().string,url:u().string})})})};var fe=me,ve=t(23796),he=t(11690),Le=t.n(he);function be(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var ge=function(e){var n=e.activeIndex,t=e.className,i=void 0===t?"":t,a=e.contentList,s=void 0===a?[]:a,l=e.idPrefix,c=void 0===l?"":l,u=e.isContentIntersecting,d=e.nextIndex,p=e.onClickCallback,m=e.navRef,f=e.style,v=void 0===f?{}:f,h=(0,r.useRef)(0),L=(0,K.EF)().trackContentAction,b=(0,r.useCallback)((function(e){var t,o,r,i,a,l=Number(null===(t=e.currentTarget)||void 0===t||null===(o=t.dataset)||void 0===o?void 0:o.contentIndex);(p(l),"number"===typeof l&&l!==n)&&L(function(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){be(e,n,t[n])}))}return e}({},null===(r=s[l])||void 0===r?void 0:r.navTrackingData,{unit_name:null===(i=s[n])||void 0===i||null===(a=i.navTrackingData)||void 0===a?void 0:a.unit_name}))}),[n,p]);return(0,o.jsx)("nav",{className:"".concat(Le().nav," ").concat(i),"data-content-transition-state":"number"===typeof n&&"number"===typeof d?"pending":"",ref:m,role:"tablist",style:v,children:(0,o.jsx)("ul",{onKeyDown:function(e){var n=h.current;if(["ArrowRight","ArrowLeft"].includes(e.key)){var t;e.preventDefault(),"ArrowRight"===e.key?++n>=s.length&&(n=0):"ArrowLeft"===e.key&&--n<0&&(n=s.length-1);var o=null===(t=e.currentTarget.children[n])||void 0===t?void 0:t.querySelector("button");o&&(h.current=n,o.focus())}},children:s.map((function(e){var t,r,i=e.label,a=e.index,s=e.children,l=(n===a&&u?"active":d===a&&"next")||"";return(0,o.jsx)("li",{children:(0,o.jsx)("button",{"aria-controls":"".concat(c,"-content-").concat(a),"aria-selected":"active"===l?"true":"false","data-content-index":a,"data-content-nav-state":l,"data-content-nav-name":null===s||void 0===s||null===(t=s.props)||void 0===t||null===(r=t.data)||void 0===r?void 0:r.name,id:"".concat(c,"-nav-").concat(a),onClick:b,role:"tab",tabIndex:"active"===l?"0":"-1",children:i})},a)}))})})};ge.propTypes={activeIndex:u().number.isRequired,className:u().string,contentList:u().arrayOf(u().shape({label:u().oneOfType([u().string,u().node,u().elementType]).isRequired,index:u().number.isRequired})).isRequired,idPrefix:u().string,nextIndex:u().number,onClickCallback:u().func,navRef:u().object,style:u().object};var _e=ge;function Ce(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function xe(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){Ce(e,n,t[n])}))}return e}var ye=function(e){var n=e.children,t=e.className,i=void 0===t?"":t,a=e.ariaLabledByNav,s=void 0===a||a,l=e.idPrefix,c=e.index,u=e.isActive,d=e.isNavShown,p=e.isNext,m=e.name,f=u||p,v=(0,r.useRef)(!1),h=(0,r.useMemo)((function(){return[i,Le().content,u&&Le().active,p&&Le().next].filter(Boolean).join(" ")}),[i,u,p]),L=(0,r.useMemo)((function(){return xe({},f?{}:{hidden:!0},d?{"aria-labelledby":s?"".concat(l,"-nav-").concat(c):null,role:"tabpanel",tabIndex:"0"}:{})}),[s,d,f,c,l]);(0,r.useEffect)((function(){if(v.current&&!f){var e=new CustomEvent(ve.SP,{detail:{index:c,name:n.props.data.name}});window.dispatchEvent(e)}v.current=f}),[f]);var b=(0,r.useMemo)((function(){return f&&r.Children.map(n,(function(e){return r.cloneElement(e,{isNext:p})}))}),[p,f,n]);return(0,o.jsx)("li",xe({className:h,"data-content-transition-status":u?"active":p&&"next","data-content-tab-name":m,id:"".concat(l,"-content-").concat(c)},L,{children:b}))};ye.propTypes={children:u().node.isRequired,className:u().string,idPrefix:u().string,index:u().number,isActive:u().bool,isNavShown:u().bool,isNext:u().bool,name:u().string};var je=ye,we=t(59855),Ne=function(e){var n=e.children,t=e.id,i=void 0===t?"ContentSwitcher":t,a=e.navElementProps,s=void 0===a?{}:a,l=e.unmountOnTransitionEnd,c=void 0!==l&&l,u=(0,r.useState)(0),d=u[0],p=u[1],m=(0,r.useState)(null),f=m[0],v=m[1],h=(0,r.useState)(!1),L=h[0],b=h[1],g=(0,r.useState)(!1),_=g[0],C=g[1],x=(0,r.useRef)(null),y=(0,r.useRef)(null),j=(0,r.useRef)("0px"),w=(0,r.useMemo)((function(){return r.Children.map(r.Children.toArray(n),(function(e,n){return(0,r.isValidElement)(e)&&{children:e.props.children,className:e.props.className,Component:e.type,ariaLabledByNav:e.props.ariaLabledByNav,index:n,label:e.props.label,navTrackingData:e.props.navTrackingData,name:e.props.name}})).filter(Boolean)}),[n]),N=(0,r.useMemo)((function(){return function(){var e=window.scrollY+y.current.getBoundingClientRect().top,n=we.Z.getAvailableTop(y.current);return{transitioningContent:-1*(window.scrollY-e+n),scroll:e-n}}}),[y,we.Z]),Z=(0,r.useMemo)((function(){return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=n.skipTransition,o=void 0!==t&&t;if(L||"number"!==typeof e||e===d){var r=N();"number"===typeof r.scroll&&window.scrollTo({top:r.scroll,behavior:"smooth"})}else!o&&c?v(e):p(e)}}),[d,L,c,N]);(0,r.useEffect)((function(){var e=new IntersectionObserver((function(e){e.forEach((function(e){C(e.isIntersecting)}))}));return y.current&&e.observe(y.current,{threshold:1}),function(){y.current&&e.unobserve(y.current)}}),[y,x]),(0,r.useEffect)((function(){if("number"===typeof f){var e=N(),n="smooth";y.current.getBoundingClientRect().top<=0?(j.current="".concat(e.transitioningContent,"px"),n="instant"):j.current="0px","number"===typeof e.scroll&&window.scrollTo({behavior:n,top:e.scroll}),b(!0)}}),[N,f]);var O=(0,r.useMemo)((function(){return function(e){var n,t;e.target!==e.currentTarget&&"next"!==(null===(n=e.target)||void 0===n||null===(t=n.dataset)||void 0===t?void 0:t.contentTransitionStatus)||(p(f),v(null),b(!1),j.current="0px")}}),[f]),k=(0,r.useMemo)((function(){return[Le().contentList,L&&Le().transitionPending,(f||0)-d>0?Le().transitionRight:Le().transitionLeft].filter(Boolean).join(" ")}),[d,f,L]),P=(0,r.useMemo)((function(){return function(e){var n=e.detail,t=w.findIndex((function(e){return e.children.props.data.name===n}));-1!==t&&t!==d&&(window.removeEventListener(ve.KN,P),Z(t,{skipTransition:!0}));var o=N();"number"===typeof o.scroll&&window.scrollTo({top:o.scroll,behavior:"smooth"})}}),[x,d,w,N]);(0,r.useEffect)((function(){return window.addEventListener(ve.KN,P),function(){window.removeEventListener(ve.KN,P)}}),[P]),(0,r.useEffect)((function(){if(x.current){var e=we.Z.getAvailableTop(x.current),n=x.current.offsetHeight;return document.documentElement.style.setProperty("--sticky-elements-offset-top","".concat(e,"px")),document.documentElement.style.setProperty("--fixed-elements-offset-bottom","".concat(n,"px")),we.Z.add(x.current),function(){we.Z.remove(x.current)}}}),[we.Z,x]);var M=w.length>1;return(0,o.jsxs)(o.Fragment,{children:[M&&(0,o.jsx)(_e,{activeIndex:d,className:s.className,contentList:w,idPrefix:i,isContentIntersecting:_,nextIndex:f,onClickCallback:Z,style:s.style,navRef:x}),(0,o.jsx)("ul",{className:k,onTransitionEnd:c?O:void 0,ref:y,style:{"--content-scroll-offset":j.current},children:w.map((function(e){var n=e.children,t=e.className,r=e.Component,a=e.ariaLabledByNav,s=e.index,l=e.name;return(0,o.jsx)(r,{className:t,ariaLabledByNav:a,idPrefix:i,index:s,isActive:d===s,isNavShown:M,isNext:f===s,name:l,children:n},s)}))})]})};Ne.propTypes={children:u().node.isRequired,id:u().string,navElementProps:u().shape({className:u().string,style:u().object}),unmountOnTransitionEnd:u().bool},(Ne.Content=je).displayName="Content";var Ze=Ne,Oe=t(14847),ke=t(63314),Pe=t.n(ke);function Me(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,o=new Array(n);t<n;t++)o[t]=e[t];return o}function Te(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function Se(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){Te(e,n,t[n])}))}return e}function De(e,n){if(null==e)return{};var t,o,r=function(e,n){if(null==e)return{};var t,o,r={},i=Object.keys(e);for(o=0;o<i.length;o++)t=i[o],n.indexOf(t)>=0||(r[t]=e[t]);return r}(e,n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)t=i[o],n.indexOf(t)>=0||Object.prototype.propertyIsEnumerable.call(e,t)&&(r[t]=e[t])}return r}function Ee(e){return function(e){if(Array.isArray(e))return Me(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,n){if(!e)return;if("string"===typeof e)return Me(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Me(e,n)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Fe=function(e){var n=e.adsStartIndex,t=void 0===n?1:n,i=e.tabs,a=void 0===i?[]:i,s=e.membershipFeedType,l=(0,K.ac)().isMobile,c=(0,r.useMemo)((function(){return a.map((function(e,n){var r,i=e.label,a=e.headline,c=e.extraItems,u=De(e,["label","headline","extraItems"]),d=(null===u||void 0===u||null===(r=u.name)||void 0===r?void 0:r.replace("tab_",""))||"",p={action_type:"select",action_value:d,item_name:d,item_type:"button",position_in_subunit:n,position_in_unit:0,subunit_name:"tabs",subunit_type:"component",unit_name:d},m=function(e){return e.map((function(e){var n;return(null===e||void 0===e||null===(n=e.content)||void 0===n?void 0:n.url)?Se({},e,{content:Se({},e.content,{url:(0,_.b)(e.content.url,"web-hf")})}):e}))},f=!l&&Array.isArray(c)&&c.length?Se({},u,{startIndexLabel:u.startIndexLabel-c.length,items:m(Ee(c).concat(Ee(u.items)))}):Se({},u,{items:m(u.items||[])});return(0,o.jsx)(Ze.Content,{ariaLabledByNav:!a,label:i,navTrackingData:p,name:d,children:(0,o.jsx)(Oe.O,{adsAfterInitialPositions:[5],adsStartIndex:t,data:f,headline:a,isTrackable:!0,maxItemsPerGrouping:5,showNumbering:"trending"===d,trackingData:{unit_name:d,unit_type:"feed"},membershipFeedType:s})},n)}))}),[a,t,l,s]);return(0,o.jsx)(Ze,{navElementProps:{className:Pe().nav},unmountOnTransitionEnd:!0,children:c})};Fe.propTypes={adsStartIndex:u().number,tabs:u().arrayOf(u().shape({label:u().oneOfType([u().string,u().node,u().elementType]).isRequired}))};var Re=Fe,Ie=t(94776),Be=t.n(Ie),Ve=t(9845),Ae=t(48243),He=t(30353);function ze(e,n,t,o,r,i,a){try{var s=e[i](a),l=s.value}catch(c){return void t(c)}s.done?n(l):Promise.resolve(l).then(o,r)}function qe(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var We=function(e){if("client_uuid"===e){var n=new URL(window.location.href).searchParams;return(null===n||void 0===n?void 0:n.get("client_uuid"))?n.get("client_uuid"):(0,Ve.TQ)()}return null},Ue={},Ke={name:"shopping-lite-heavy",type:"dynamic",secondary_bucketing:{params:["client_uuid"],path:{dev:"https://recsys-api.buzzfeed.com/web/home/<USER>",stage:"https://recsys-api.buzzfeed.com/web/home/<USER>",prod:"https://recsys-api.buzzfeed.com/web/home/<USER>"}},mappings:{"shopping-lite":"dynamic_1","shopping-heavy":"dynamic_2"}},Ge=function(e){var n=e.tabs,t=e.additionalZones,o=e.membershipFeedType,i=(0,r.useContext)(Ae.WN),a=i.experiments,s=i.getExperimentValue,l=(0,r.useState)(Ue.name||Ke.name?null:n),c=l[0],u=l[1],d=(0,r.useState)((function(){return function(){var e={};return[Ue,Ke].forEach((function(n){(null===n||void 0===n?void 0:n.secondary_bucketing)&&(e[n.name]={status:"loading"})})),e}()})),p=d[0],m=d[1],f=function(e){m((function(n){return function(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){qe(e,n,t[n])}))}return e}({},n,e)}))},v=Object.keys(p||{}).map((function(e){var n;return null===(n=p[e])||void 0===n?void 0:n.status})).join("-"),h=function(){var e,n=(e=Be().mark((function e(n){var t,o,r,i,a,s;return Be().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,o=(null===Ue||void 0===Ue?void 0:Ue.name)===n?Ue:Ke,r=null===o||void 0===o?void 0:o.secondary_bucketing,i=r.path[He.ov],(null===(t=r.params)||void 0===t?void 0:t.length)&&(i+="?".concat(r.params.map((function(e){return"".concat(e,"=").concat(We(e))})).join("&"))),e.next=8,fetch(i);case 8:if(!(null===(a=e.sent)||void 0===a?void 0:a.ok)){e.next=16;break}return e.next=12,a.json();case 12:s=e.sent,f(qe({},n,{status:"loaded",results:s})),e.next=17;break;case 16:f(qe({},n,{status:"error"}));case 17:e.next=23;break;case 19:e.prev=19,e.t0=e.catch(0),console.error(e.t0),f(qe({},n,{status:"error"}));case 23:case"end":return e.stop()}}),e,null,[[0,19]])})),function(){var n=this,t=arguments;return new Promise((function(o,r){var i=e.apply(n,t);function a(e){ze(i,o,r,a,s,"next",e)}function s(e){ze(i,o,r,a,s,"throw",e)}a(void 0)}))});return function(e){return n.apply(this,arguments)}}();return(0,r.useEffect)((function(){Object.keys(p).forEach((function(e){h(e)}))}),[]),(0,r.useEffect)((function(){var e=!(null===Ue||void 0===Ue?void 0:Ue.name)||(null===a||void 0===a?void 0:a.loaded),o=!v||v.split("-").every((function(e){return"loading"!==e}));if(e&&o){var r=!1,i="control",l=n;if(null===Ue||void 0===Ue?void 0:Ue.name){var c,d=s(Ue.name,{rejectErrors:!1,defaultVariantIfUnbucketed:null});if(d)if(r=!0,"control"!==d)i=null===(c=Ue.mappings)||void 0===c?void 0:c[d]}if(!r&&(null===Ke||void 0===Ke?void 0:Ke.name)&&(i=function(e){var n,t,o,r=e.secondary,i=(e.variant,"control");if("error"===r.status)return i;var a=null===(o=null===(n=r.results)||void 0===n||null===(t=n[0])||void 0===t?void 0:t[0].split(":"))||void 0===o?void 0:o[1];return"nonshopper"===a?i="dynamic_1":"shopper"===a&&(i="dynamic_2"),i}({secondary:p[Ke.name]})),i&&"control"!==i){var m,f=l.find((function(e){return"tab_latest"===e.name})),h=null===t||void 0===t?void 0:t[i];h&&(null===h||void 0===h||null===(m=h.items)||void 0===m?void 0:m.length)&&f&&(f.items=h.items,f.next=h.next)}u(l)}}),[null===a||void 0===a?void 0:a.loaded,v,o]),c};Ge.propTypes={tabs:u().arrayOf(u().shape({name:u().string.isRequired,items:u().array,next:u().string})).isRequired,additionalZones:u().object.isRequired};var Qe=Ge,Je=function(e){var n=e.className,t=void 0===n?"":n;return(0,o.jsx)("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:(0,o.jsx)("path",{d:"M0 6.5c2.5-1 4-3.1 5-5.5 1 2.4 2.5 4.5 5 5.5-2.5 1-4 3.1-5 5.5a9.8 9.8 0 0 0-5-5.5ZM14 4c1-.6 1.6-1.7 2-3 .4 1.3 1 2.4 2 3-1 .6-1.6 1.7-2 3-.4-1.3-1-2.4-2-3ZM10 13.5c1.4-.7 2.4-2 3-3.5a6.1 6.1 0 0 0 3 3.5c-1.4.7-2.4 2-3 3.5a6.1 6.1 0 0 0-3-3.5Z",fill:"#5246F5"})})},Ye=t(64319);function Xe(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,o=new Array(n);t<n;t++)o[t]=e[t];return o}function $e(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function en(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var o,r,i=[],a=!0,s=!1;try{for(t=t.call(e);!(a=(o=t.next()).done)&&(i.push(o.value),!n||i.length!==n);a=!0);}catch(l){s=!0,r=l}finally{try{a||null==t.return||t.return()}finally{if(s)throw r}}return i}}(e,n)||function(e,n){if(!e)return;if("string"===typeof e)return Xe(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Xe(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var nn={tab_sponsored:{buttonLabel:(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(Je,{})," ",(0,o.jsx)("span",{children:"Sponsored"})]}),headline:""},tab_latest:{buttonLabel:(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(Ye.O,{})," ",(0,o.jsx)("span",{children:"Latest"})]}),headline:(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(Ye.O,{})," ",(0,o.jsx)("span",{children:"Catch Up On The Latest"})]})}},tn=["tab_latest","tab_sponsored"],on=function(e){var n,t,i,a,s=e.additionalZones,l=e.membershipFeedType,c=e.tabZones,u=e.sponsorship,d=e.startAdIndexMobile,p=void 0===d?1:d,m=(0,K.ac)().breakpoint,f=(0,r.useState)(p),v=f[0],h=f[1],L=!!(null===c||void 0===c||null===(n=c.tab_sponsored)||void 0===n||null===(t=n.items)||void 0===t?void 0:t.length)&&(null===u||void 0===u||null===(i=u.data)||void 0===i||null===(a=i.tab)||void 0===a?void 0:a.enabled),b=new Map;tn.forEach((function(e,n){return b.set(e,n)}));var g=(0,r.useMemo)((function(){return Object.entries(c).map((function(e,n){var t,r,i=en(e,2),a=i[0],s=i[1];if(("tab_sponsored"!==a||L)&&-1!==tn.indexOf(a)){var l=function(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){$e(e,n,t[n])}))}return e}({},s,{headline:(null===(t=nn[a])||void 0===t?void 0:t.headline)||"",label:(null===(r=nn[a])||void 0===r?void 0:r.buttonLabel)||""});if("tab_sponsored"===a){var c,d,p,m,f,v,h;l.className="isSponsored";var b=(null===u||void 0===u||null===(c=u.data)||void 0===c||null===(d=c.tab)||void 0===d||null===(p=d.tab)||void 0===p?void 0:p.text)||"Sponsored",g=(null===u||void 0===u||null===(m=u.data)||void 0===m||null===(f=m.tab)||void 0===f||null===(v=f.cta)||void 0===v||null===(h=v.icon)||void 0===h?void 0:h.length)?(0,o.jsx)("img",{src:u.data.tab.cta.icon,alt:b,width:14,"aria-hidden":"true"}):(0,o.jsx)(Je,{});l.label=(0,o.jsxs)(o.Fragment,{children:[g,(0,o.jsx)("span",{children:b})]}),l.headline=(0,o.jsxs)(o.Fragment,{children:[g," ",(0,o.jsx)("span",{children:b})]})}return l}})).filter((function(e){var n;return!!(null===e||void 0===e||null===(n=e.items)||void 0===n?void 0:n.length)})).sort((function(e,n){return b.get(e.name)-b.get(n.name)}))}),[c,u,l]),_=Qe({tabs:g,additionalZones:s,membershipFeedType:l});return(0,r.useEffect)((function(){m&&h("xs"===m||"sm"===m?p:1)}),[m]),_?(0,o.jsx)(Re,{tabs:_,adsStartIndex:v,membershipFeedType:l}):null},rn=t(92523),an=function(e){e.className;return(0,o.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M10 1C14.9706 1 19 5.02944 19 10C19 14.9706 14.9706 19 10 19C5.02944 19 1 14.9706 1 10C1 5.02944 5.02944 1 10 1ZM6.37891 15.4521C7.41606 16.1424 8.66076 16.5459 10 16.5459C13.615 16.5459 16.5459 13.615 16.5459 10C16.5459 8.66105 16.1421 7.4169 15.4521 6.37988L6.37891 15.4521ZM10 3.4541C6.38505 3.4541 3.4541 6.38505 3.4541 10C3.4541 11.3886 3.88849 12.6751 4.62598 13.7344L13.7354 4.62598C12.6759 3.88822 11.3889 3.4541 10 3.4541Z",fill:"#F5806E"}),(0,o.jsx)("path",{d:"M10 1V0.7V1ZM19 10H19.3H19ZM10 19V19.3V19ZM1 10H0.7H1ZM6.37891 15.4521L6.16679 15.24L5.90778 15.499L6.2127 15.7019L6.37891 15.4521ZM10 16.5459V16.8459V16.5459ZM16.5459 10H16.8459H16.5459ZM15.4521 6.37988L15.7019 6.21371L15.499 5.90876L15.24 6.16774L15.4521 6.37988ZM10 3.4541V3.1541V3.4541ZM3.4541 10H3.1541H3.4541ZM4.62598 13.7344L4.37977 13.9058L4.5846 14.2L4.8381 13.9465L4.62598 13.7344ZM13.7354 4.62598L13.9475 4.83812L14.201 4.58465L13.9068 4.37979L13.7354 4.62598ZM10 1V1.3C14.8049 1.3 18.7 5.19512 18.7 10H19H19.3C19.3 4.86375 15.1362 0.7 10 0.7V1ZM19 10H18.7C18.7 14.8049 14.8049 18.7 10 18.7V19V19.3C15.1362 19.3 19.3 15.1362 19.3 10H19ZM10 19V18.7C5.19512 18.7 1.3 14.8049 1.3 10H1H0.7C0.7 15.1362 4.86375 19.3 10 19.3V19ZM1 10H1.3C1.3 5.19512 5.19512 1.3 10 1.3V1V0.7C4.86375 0.7 0.7 4.86375 0.7 10H1ZM6.37891 15.4521L6.2127 15.7019C7.29736 16.4237 8.59958 16.8459 10 16.8459V16.5459V16.2459C8.72195 16.2459 7.53475 15.861 6.54511 15.2024L6.37891 15.4521ZM10 16.5459V16.8459C13.7806 16.8459 16.8459 13.7806 16.8459 10H16.5459H16.2459C16.2459 13.4493 13.4493 16.2459 10 16.2459V16.5459ZM16.5459 10H16.8459C16.8459 8.5998 16.4234 7.29815 15.7019 6.21371L15.4521 6.37988L15.2024 6.54606C15.8608 7.53564 16.2459 8.72231 16.2459 10H16.5459ZM15.4521 6.37988L15.24 6.16774L6.16679 15.24L6.37891 15.4521L6.59103 15.6643L15.6643 6.59203L15.4521 6.37988ZM10 3.4541V3.1541C6.21936 3.1541 3.1541 6.21936 3.1541 10H3.4541H3.7541C3.7541 6.55073 6.55073 3.7541 10 3.7541V3.4541ZM3.4541 10H3.1541C3.1541 11.4522 3.60861 12.7981 4.37977 13.9058L4.62598 13.7344L4.87218 13.563C4.16838 12.552 3.7541 11.325 3.7541 10H3.4541ZM4.62598 13.7344L4.8381 13.9465L13.9475 4.83812L13.7354 4.62598L13.5232 4.41383L4.41386 13.5222L4.62598 13.7344ZM13.7354 4.62598L13.9068 4.37979C12.7989 3.60829 11.4524 3.1541 10 3.1541V3.4541V3.7541C11.3253 3.7541 12.5529 4.16814 13.5639 4.87216L13.7354 4.62598Z",fill:"black"})]})},sn=function(e){e.className;return(0,o.jsxs)("svg",{width:"54",height:"31",viewBox:"0 0 54 31",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M33.6328 9.60189C33.3967 9.5621 33.2424 9.33366 33.2886 9.09218L33.6863 7.01246C34.199 4.33123 37.0327 2.15749 40.0034 2.16416C41.5561 2.17054 42.9335 2.77733 43.7819 3.8328C44.5248 4.7602 44.8052 5.95108 44.569 7.18632L44.246 8.87526C44.1998 9.11675 43.9706 9.28055 43.7344 9.24075C43.4983 9.20096 43.3441 8.97252 43.3902 8.73104L43.7132 7.04209C43.8996 6.06736 43.6815 5.13021 43.1007 4.40538C42.4125 3.54761 41.2701 3.05596 39.9771 3.05105C37.4154 3.04533 34.9778 4.88549 34.5435 7.15693L34.1458 9.23664C34.0996 9.47813 33.8704 9.64193 33.6342 9.60213L33.6328 9.60189Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M29.108 9.52426L30.5181 10.7128L32.4376 9.37636L33.8477 10.5649L35.7699 9.22283L37.1718 10.4161L39.0936 9.07549L40.5011 10.2696L42.4233 8.9276L43.8322 10.122L45.753 8.7797L46.7888 9.90803L42.9439 28.9098L26.2533 24.6591L29.108 9.52426Z",fill:"#F9F7F7"}),(0,o.jsx)("path",{d:"M42.8561 29.35C42.8434 29.3479 42.8322 29.3461 42.8198 29.3426L26.1273 25.0916C25.9158 25.0379 25.7874 24.8249 25.8303 24.5985L28.6867 9.4627C28.717 9.3055 28.8238 9.1727 28.9696 9.11282C29.1151 9.05441 29.2762 9.07727 29.3916 9.17459L30.5623 10.1611L32.2129 9.01309C32.3739 8.89961 32.5802 8.90702 32.7217 9.0267L33.8924 10.0132L35.5442 8.85932C35.7049 8.74731 35.9132 8.75201 36.0554 8.87486L37.2168 9.86439L38.8669 8.71176C39.0276 8.59975 39.2359 8.60445 39.377 8.7256L40.5469 9.71649L42.1983 8.5641C42.359 8.45208 42.5673 8.45679 42.7085 8.57794L43.8783 9.56882L45.5284 8.4162C45.7061 8.29178 45.9357 8.31353 46.0751 8.4647L47.1099 9.59143C47.2014 9.69247 47.2397 9.83345 47.2112 9.97429L43.3659 28.9775C43.341 29.1007 43.2683 29.2087 43.1641 29.2783C43.0716 29.3407 42.9605 29.3652 42.8564 29.3485L42.8561 29.35ZM26.7489 24.325L42.6205 28.3669L46.3239 10.0649L45.6761 9.35944L44.0601 10.4872C43.8994 10.5993 43.6925 10.5948 43.5499 10.4734L42.3801 9.48251L40.7287 10.6349C40.5679 10.7469 40.3596 10.7422 40.2185 10.6211L39.0487 9.63018L37.3986 10.7828C37.2379 10.8948 37.0296 10.8901 36.8873 10.7673L35.7246 9.77752L34.0745 10.9301C33.9138 11.0422 33.7072 11.0362 33.5643 10.9163L32.3936 9.92981L30.743 11.0778C30.5823 11.1898 30.3757 11.1839 30.2343 11.0642L29.385 10.349L26.7486 24.3265L26.7489 24.325Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M51.4043 11.1818L50.0241 11.7997L49.8009 10.3736L48.6296 11.0617L48.2948 9.5275L47.1275 10.1953L43.4217 29.0476L48.1046 27.9682L51.4043 11.1818Z",fill:"#F5806E"}),(0,o.jsx)("path",{d:"M43.3175 29.485C43.2366 29.4717 43.1622 29.4325 43.1047 29.3707C43.0115 29.2716 42.9716 29.1274 42.9996 28.9853L46.7162 10.122C46.7428 9.98719 46.8273 9.86938 46.9417 9.80439L48.1176 9.1371C48.2304 9.07333 48.3602 9.06473 48.474 9.11634C48.5864 9.16774 48.6677 9.27089 48.6955 9.39818L48.9167 10.4028L49.6294 9.98711C49.7461 9.91798 49.884 9.91071 49.9993 9.96857C50.1149 10.025 50.1959 10.137 50.2177 10.2738L50.361 11.1863L51.2844 10.7753C51.4237 10.7114 51.5824 10.733 51.6954 10.8309C51.8084 10.9288 51.8594 11.0868 51.8286 11.2433L48.5192 28.0396C48.4838 28.2193 48.3483 28.3631 48.1806 28.4014L43.4665 29.478C43.4156 29.4891 43.365 29.4913 43.3165 29.4833L43.3175 29.485ZM47.4805 10.4691L43.9302 28.4887L47.7712 27.6099L50.8683 11.8907L50.1754 12.1988C50.0605 12.2518 49.9338 12.2445 49.8269 12.1865C49.7216 12.1273 49.6482 12.0194 49.6287 11.892L49.4942 11.0409L48.8131 11.4378C48.7 11.503 48.5656 11.5138 48.4545 11.4626C48.3404 11.4125 48.258 11.3077 48.2302 11.1804L48.0074 10.1695L47.4792 10.4689L47.4805 10.4691Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M6.34987 11.2762C6.11243 11.3072 5.89814 11.1338 5.87171 10.8894L5.6441 8.78425C5.35065 6.07026 7.42517 3.16313 10.268 2.30112C11.7548 1.85334 13.2493 2.03101 14.3692 2.79237C15.3508 3.4621 15.967 4.519 16.1022 5.76933L16.2871 7.47892C16.3135 7.72335 16.1421 7.94701 15.9047 7.97798C15.6673 8.00894 15.453 7.83558 15.4265 7.59114L15.2417 5.88156C15.135 4.89492 14.6525 4.06246 13.8852 3.53905C12.9763 2.91995 11.7401 2.78369 10.5021 3.15697C8.05065 3.90033 6.25746 6.37265 6.50606 8.67185L6.73367 10.777C6.7601 11.0214 6.58874 11.2451 6.35131 11.276L6.34987 11.2762Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M1.99846 12.5254L3.6944 13.2498L5.13946 11.4107L6.8354 12.135L8.28129 10.2898L9.97075 11.0211L11.4168 9.17727L13.1119 9.90781L14.5578 8.06252L16.2543 8.79286L17.6988 6.94776L19.0192 7.72403L20.8968 27.0197L3.69269 27.8337L1.99846 12.5254Z",fill:"#F9F7F7"}),(0,o.jsx)("path",{d:"M20.9412 27.466C20.9285 27.4678 20.9172 27.4693 20.9043 27.4696L3.69825 28.2838C3.48033 28.2942 3.2953 28.1281 3.27015 27.899L1.57726 12.5894C1.56033 12.4302 1.62364 12.2719 1.74558 12.1721C1.86766 12.0737 2.02834 12.0485 2.1672 12.1078L3.57515 12.709L4.81805 11.1286C4.93885 10.973 5.13831 10.9198 5.30856 10.9929L6.71651 11.5941L7.95883 10.0078C8.07978 9.85369 8.28036 9.7973 8.45231 9.87319L9.85222 10.48L11.0933 8.89539C11.2142 8.74129 11.4148 8.6849 11.5852 8.7595L12.9936 9.36515L14.236 7.78033C14.357 7.62623 14.5576 7.56984 14.728 7.64444L16.1364 8.25008L17.3774 6.66547C17.511 6.49452 17.7369 6.44822 17.9145 6.55203L19.2334 7.32705C19.3504 7.39694 19.4283 7.52056 19.4422 7.66358L21.3199 26.9608C21.332 27.0859 21.2941 27.2104 21.2148 27.3074C21.1445 27.3942 21.0455 27.4501 20.941 27.4645L20.9412 27.466ZM4.0686 27.369L20.4285 26.5947L18.6201 8.00965L17.7944 7.52432L16.5787 9.07524C16.4577 9.22934 16.2586 9.28554 16.0868 9.21114L14.6784 8.60549L13.4359 10.1903C13.315 10.3444 13.1144 10.4008 12.944 10.3262L11.5356 9.72055L10.2946 11.3052C10.1736 11.4593 9.97303 11.5157 9.80108 11.4398L8.39975 10.8331L7.1587 12.4178C7.03775 12.5719 6.83844 12.6266 6.66678 12.5537L5.25883 11.9525L4.01593 13.5328C3.89497 13.6869 3.69566 13.7416 3.52541 13.6685L2.5042 13.2328L4.06874 27.3705L4.0686 27.369Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M23.806 7.59124L22.6667 8.58559L22.0363 7.28699L21.1173 8.28747L20.3488 6.91811L19.4276 7.89802L21.3945 27.0101L25.5573 24.6089L23.806 7.59124Z",fill:"#F5806E"}),(0,o.jsx)("path",{d:"M21.4218 27.4589C21.3405 27.4699 21.2578 27.4542 21.1848 27.4118C21.0667 27.3443 20.9864 27.218 20.9717 27.074L19.0119 7.94819C18.9979 7.81151 19.0442 7.67415 19.1347 7.57853L20.0641 6.59667C20.1534 6.5027 20.275 6.45653 20.3989 6.47264C20.5214 6.48893 20.6293 6.5638 20.6932 6.67739L21.1984 7.57343L21.7584 6.9676C21.8498 6.86739 21.9795 6.82013 22.1067 6.84175C22.2338 6.8619 22.344 6.94541 22.4048 7.06983L22.8085 7.90056L23.5715 7.23761C23.686 7.13578 23.8441 7.11003 23.9808 7.17061C24.1174 7.23118 24.2124 7.3674 24.2287 7.52613L25.9737 24.5561C25.9924 24.7383 25.9048 24.9155 25.7557 25.0011L21.5622 27.4087C21.5167 27.4342 21.469 27.4511 21.4203 27.4576L21.4218 27.4589ZM19.8443 8.05668L21.7164 26.3271L25.1328 24.3638L23.4996 8.42589L22.927 8.92314C22.8327 9.00737 22.7094 9.03737 22.5902 9.01319C22.4722 8.98735 22.3704 8.90569 22.3146 8.78953L21.9372 8.01487L21.4018 8.59353C21.3127 8.68897 21.1873 8.73862 21.0661 8.72215C20.9424 8.70752 20.8329 8.63136 20.7691 8.51777L20.2606 7.61621L19.8429 8.05686L19.8443 8.05668Z",fill:"#222222"})]})},ln=function(e){e.className;return(0,o.jsxs)("svg",{width:"54",height:"37",viewBox:"0 0 54 37",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M20.1957 8.47084C19.9563 8.47035 19.7666 8.27035 19.7725 8.02456L19.8233 5.90777C19.8888 3.17874 22.3271 0.569194 25.2585 0.0879613C26.7912 -0.160714 28.2495 0.211674 29.2597 1.1135C30.1448 1.90632 30.6169 3.03499 30.5868 4.29225L30.5455 6.0113C30.5396 6.2571 30.3404 6.45632 30.1009 6.45583C29.8615 6.45535 29.6718 6.25535 29.6777 6.00956L29.719 4.2905C29.7428 3.2984 29.3737 2.40978 28.6818 1.79016C27.862 1.05705 26.6545 0.759658 25.3782 0.967138C22.8503 1.38215 20.748 3.59759 20.6926 5.90952L20.6418 8.02631C20.6359 8.2721 20.4366 8.47132 20.1972 8.47084L20.1957 8.47084Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M15.7195 9.13669L17.3056 10.0775L18.9797 8.44404L20.5658 9.38486L22.2415 7.74541L23.8203 8.69224L25.4959 7.05429L27.0804 8.00109L28.7561 6.36164L30.3421 7.30844L32.0163 5.669L33.2233 6.61193L32.5509 25.9871L15.3889 24.5349L15.7195 9.13669Z",fill:"#F9F7F7"}),(0,o.jsx)("path",{d:"M32.5359 26.4363C32.5231 26.4363 32.5117 26.4364 32.4989 26.435L15.335 24.9827C15.1176 24.9645 14.9559 24.7754 14.9611 24.5451L15.2932 9.14568C15.2974 8.98563 15.3809 8.83709 15.5149 8.75409C15.6488 8.67258 15.8114 8.66868 15.9413 8.74573L17.2581 9.52658L18.6978 8.12313C18.838 7.98475 19.0427 7.95819 19.2019 8.05302L20.5187 8.83388L21.9585 7.42444C22.0987 7.28756 22.3049 7.25799 22.4654 7.35581L23.7735 8.1412L25.2119 6.73327C25.3521 6.59639 25.5583 6.56682 25.7174 6.66315L27.0341 7.44849L28.4739 6.04056C28.614 5.90367 28.8203 5.87411 28.9794 5.97044L30.2961 6.75578L31.7345 5.34785C31.8894 5.19593 32.1194 5.17969 32.2818 5.30591L33.4875 6.24742C33.5943 6.33207 33.6553 6.46485 33.6503 6.60846L32.9777 25.9851C32.9734 26.1108 32.9194 26.2292 32.828 26.315C32.747 26.3917 32.6415 26.4342 32.536 26.4348L32.5359 26.4363ZM15.8222 24.1245L32.1421 25.5052L32.7899 6.84358L32.0351 6.25402L30.6262 7.63187C30.4861 7.76876 30.2813 7.79832 30.1207 7.702L28.8041 6.91665L27.3642 8.32459C27.2241 8.46147 27.0179 8.49104 26.8587 8.39471L25.5421 7.60937L24.1037 9.0173C23.9635 9.15418 23.7573 9.18375 23.5968 9.08593L22.2873 8.30055L20.8489 9.70848C20.7087 9.84536 20.504 9.87342 20.3434 9.7786L19.0266 8.99774L17.5869 10.4012C17.4468 10.5381 17.242 10.5661 17.0828 10.4713L16.1277 9.9053L15.8222 24.126L15.8222 24.1245Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M37.9865 7.11074L36.7265 7.94687L36.2722 6.57674L35.2298 7.44788L34.6476 5.98945L33.6058 6.83993L33.046 26.0447L37.4881 24.211L37.9865 7.11074Z",fill:"#F5806E"}),(0,o.jsx)("path",{d:"M33.0134 26.4931C32.9313 26.4933 32.8515 26.4669 32.7847 26.4153C32.6764 26.3329 32.6134 26.1971 32.6177 26.0524L33.1864 6.83488C33.1904 6.69754 33.2544 6.56745 33.3566 6.48454L34.407 5.63323C34.5078 5.5518 34.6344 5.522 34.7551 5.55423C34.8744 5.58647 34.9716 5.67487 35.02 5.79585L35.4031 6.75048L36.0379 6.22343C36.1416 6.13609 36.2765 6.10627 36.3997 6.14441C36.523 6.18107 36.6213 6.27832 36.6652 6.40966L36.9564 7.28621L37.7998 6.72919C37.9267 6.64328 38.0868 6.63851 38.2143 6.71651C38.3419 6.7945 38.4182 6.94201 38.4134 7.10151L37.9071 24.2131C37.9017 24.3963 37.7916 24.5604 37.6325 24.6257L33.1592 26.4618C33.1108 26.4811 33.0612 26.4915 33.012 26.4916L33.0134 26.4931ZM33.9973 7.05173L33.4541 25.4098L37.0986 23.9121L37.5725 7.89774L36.9396 8.3155C36.835 8.3866 36.7088 8.40016 36.5938 8.36053C36.4802 8.31942 36.3901 8.2251 36.35 8.10262L36.0776 7.2851L35.4708 7.78846C35.37 7.87136 35.2391 7.90413 35.1212 7.87189C35.0004 7.84113 34.902 7.75126 34.8536 7.63027L34.4679 6.66975L33.9959 7.05173L33.9973 7.05173Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M6.12294 17.276C5.8855 17.307 5.67122 17.1336 5.64479 16.8892L5.41717 14.784C5.12372 12.07 7.19824 9.16291 10.0411 8.30091C11.5279 7.85312 13.0224 8.03079 14.1423 8.79216C15.1238 9.46189 15.7401 10.5188 15.8753 11.7691L16.0601 13.4787C16.0866 13.7231 15.9152 13.9468 15.6778 13.9778C15.4403 14.0087 15.226 13.8354 15.1996 13.5909L15.0148 11.8813C14.9081 10.8947 14.4255 10.0622 13.6583 9.53884C12.7493 8.91973 11.5132 8.78348 10.2752 9.15675C7.82372 9.90011 6.03053 12.3724 6.27913 14.6716L6.50674 16.7768C6.53317 17.0212 6.36181 17.2449 6.12438 17.2758L6.12294 17.276Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M1.77296 18.5252L3.4689 19.2496L4.91396 17.4105L6.6099 18.1348L8.05579 16.2895L9.74525 17.0209L11.1913 15.1771L12.8864 15.9076L14.3323 14.0623L16.0288 14.7926L17.4733 12.9475L18.7937 13.7238L20.6713 33.0195L3.46719 33.8335L1.77296 18.5252Z",fill:"#F9F7F7"}),(0,o.jsx)("path",{d:"M20.7158 33.4659C20.7031 33.4677 20.6918 33.4692 20.6789 33.4695L3.47288 34.2836C3.25495 34.2941 3.06992 34.1279 3.04478 33.8989L1.35188 18.5893C1.33496 18.43 1.39827 18.2718 1.52021 18.1719C1.64229 18.0735 1.80297 18.0483 1.94183 18.1077L3.34977 18.7088L4.59267 17.1285C4.71348 16.9729 4.91294 16.9197 5.08319 16.9928L6.49114 17.594L7.73346 16.0077C7.85441 15.8536 8.05499 15.7972 8.22694 15.8731L9.62685 16.4799L10.8679 14.8953C10.9889 14.7412 11.1894 14.6848 11.3598 14.7594L12.7682 15.365L14.0107 13.7802C14.1316 13.6261 14.3322 13.5697 14.5026 13.6443L15.911 14.25L17.152 12.6653C17.2857 12.4944 17.5116 12.4481 17.6891 12.5519L19.008 13.3269C19.125 13.3968 19.2029 13.5204 19.2168 13.6635L21.0945 32.9607C21.1067 33.0858 21.0687 33.2103 20.9894 33.3073C20.9192 33.394 20.8201 33.45 20.7156 33.4644L20.7158 33.4659ZM3.84323 33.3689L20.2031 32.5946L18.3947 14.0095L17.569 13.5242L16.3533 15.0751C16.2324 15.2292 16.0332 15.2854 15.8614 15.211L14.453 14.6054L13.2106 16.1902C13.0896 16.3443 12.889 16.4007 12.7186 16.3261L11.3102 15.7204L10.0692 17.305C9.94824 17.4591 9.74766 17.5155 9.5757 17.4396L8.17438 16.833L6.93333 18.4176C6.81238 18.5717 6.61306 18.6265 6.4414 18.5535L5.03345 17.9524L3.79055 19.5327C3.6696 19.6868 3.47029 19.7415 3.30004 19.6684L2.27883 19.2327L3.84337 33.3704L3.84323 33.3689Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M23.5806 13.5916L22.4412 14.586L21.8109 13.2874L20.8919 14.2879L20.1233 12.9185L19.2022 13.8984L21.1691 33.0104L25.3319 30.6093L23.5806 13.5916Z",fill:"#F5806E"}),(0,o.jsx)("path",{d:"M21.1964 33.4593C21.1151 33.4703 21.0324 33.4546 20.9594 33.4122C20.8413 33.3447 20.761 33.2184 20.7463 33.0744L18.7865 13.9486C18.7725 13.8119 18.8188 13.6745 18.9093 13.5789L19.8387 12.5971C19.928 12.5031 20.0496 12.4569 20.1735 12.473C20.296 12.4893 20.4039 12.5642 20.4677 12.6778L20.973 13.5738L21.533 12.968C21.6244 12.8678 21.7541 12.8205 21.8813 12.8421C22.0084 12.8623 22.1186 12.9458 22.1794 13.0702L22.5831 13.901L23.3461 13.238C23.4606 13.1362 23.6187 13.1104 23.7554 13.171C23.892 13.2316 23.987 13.3678 24.0033 13.5265L25.7483 30.5565C25.767 30.7387 25.6794 30.9159 25.5303 31.0015L21.3368 33.4091C21.2913 33.4346 21.2436 33.4515 21.1949 33.458L21.1964 33.4593ZM19.6189 14.0571L21.491 32.3275L24.9074 30.3642L23.2742 14.4263L22.7016 14.9235C22.6073 15.0078 22.484 15.0378 22.3648 15.0136C22.2468 14.9877 22.145 14.9061 22.0892 14.7899L21.7117 14.0153L21.1764 14.5939C21.0873 14.6894 20.9619 14.739 20.8407 14.7226C20.717 14.7079 20.6075 14.6318 20.5437 14.5182L20.0352 13.6166L19.6175 14.0573L19.6189 14.0571Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M33.4073 15.6014C33.1712 15.5616 33.017 15.3332 33.0631 15.0917L33.4608 13.012C33.9736 10.3307 36.8073 8.157 39.7779 8.16367C41.3307 8.17005 42.708 8.77684 43.5564 9.83231C44.2993 10.7597 44.5797 11.9506 44.3435 13.1858L44.0205 14.8748C43.9743 15.1163 43.7451 15.2801 43.509 15.2403C43.2728 15.2005 43.1186 14.972 43.1648 14.7305L43.4877 13.0416C43.6741 12.0669 43.456 11.1297 42.8752 10.4049C42.187 9.54712 41.0446 9.05547 39.7516 9.05056C37.1899 9.04484 34.7524 10.885 34.318 13.1564L33.9203 15.2362C33.8741 15.4776 33.6449 15.6414 33.4088 15.6016L33.4073 15.6014Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M28.8827 15.5232L30.2928 16.7117L32.2123 15.3753L33.6225 16.5638L35.5446 15.2217L36.9465 16.415L38.8683 15.0744L40.2759 16.2685L42.198 14.9265L43.6069 16.1209L45.5277 14.7786L46.5635 15.9069L42.7186 34.9087L26.028 30.658L28.8827 15.5232Z",fill:"#F9F7F7"}),(0,o.jsx)("path",{d:"M42.6297 35.3494C42.617 35.3474 42.6058 35.3456 42.5934 35.3421L25.9009 31.091C25.6894 31.0374 25.5611 30.8243 25.604 30.598L28.4603 15.4622C28.4906 15.3049 28.5974 15.1721 28.7432 15.1123C28.8887 15.0539 29.0498 15.0767 29.1652 15.174L30.3359 16.1605L31.9865 15.0125C32.1475 14.8991 32.3538 14.9065 32.4953 15.0262L33.666 16.0126L35.3178 14.8588C35.4785 14.7468 35.6868 14.7515 35.829 14.8743L36.9904 15.8638L38.6405 14.7112C38.8012 14.5992 39.0095 14.6039 39.1506 14.7251L40.3205 15.7159L41.9719 14.5635C42.1326 14.4515 42.3409 14.4562 42.4821 14.5774L43.6519 15.5683L45.302 14.4157C45.4797 14.2912 45.7093 14.313 45.8487 14.4642L46.8835 15.5909C46.975 15.6919 47.0133 15.8329 46.9848 15.9737L43.1396 34.9769C43.1146 35.1002 43.0419 35.2082 42.9377 35.2778C42.8452 35.3401 42.7341 35.3647 42.63 35.348L42.6297 35.3494ZM26.5225 30.3245L42.3941 34.3663L46.0975 16.0644L45.4497 15.3589L43.8337 16.4867C43.673 16.5987 43.4661 16.5942 43.3236 16.4729L42.1537 15.482L40.5023 16.6344C40.3416 16.7464 40.1332 16.7417 39.9921 16.6205L38.8223 15.6296L37.1722 16.7823C37.0115 16.8943 36.8032 16.8896 36.6609 16.7667L35.4982 15.777L33.8481 16.9296C33.6874 17.0416 33.4808 17.0357 33.3379 16.9157L32.1672 15.9293L30.5166 17.0773C30.3559 17.1893 30.1493 17.1833 30.0079 17.0636L29.1586 16.3485L26.5222 30.3259L26.5225 30.3245Z",fill:"#222222"}),(0,o.jsx)("path",{d:"M51.179 17.1801L49.7988 17.7979L49.5756 16.3718L48.4043 17.0599L48.0696 15.5257L46.9022 16.1936L43.1965 35.0458L47.8793 33.9664L51.179 17.1801Z",fill:"#F5806E"}),(0,o.jsx)("path",{d:"M43.0911 35.4838C43.0102 35.4705 42.9357 35.4313 42.8783 35.3695C42.785 35.2704 42.7452 35.1262 42.7732 34.9841L46.4898 16.1208C46.5164 15.986 46.6008 15.8682 46.7153 15.8032L47.8911 15.1359C48.004 15.0721 48.1338 15.0635 48.2475 15.1152C48.3599 15.1665 48.4413 15.2697 48.4691 15.397L48.6903 16.4016L49.403 15.9859C49.5197 15.9168 49.6576 15.9095 49.7729 15.9674C49.8885 16.0238 49.9695 16.1359 49.9913 16.2726L50.1345 17.1851L51.058 16.7741C51.1973 16.7102 51.356 16.7318 51.469 16.8297C51.582 16.9276 51.633 17.0856 51.6021 17.2422L48.2928 34.0384C48.2574 34.2181 48.1218 34.3619 47.9541 34.4002L43.2401 35.4769C43.1892 35.4879 43.1386 35.4901 43.09 35.4821L43.0911 35.4838ZM47.2541 16.4679L43.7038 34.4875L47.5448 33.6087L50.6419 17.8895L49.949 18.1977C49.8341 18.2506 49.7074 18.2433 49.6005 18.1853C49.4952 18.1261 49.4217 18.0182 49.4023 17.8908L49.2678 17.0397L48.5867 17.4366C48.4735 17.5018 48.3391 17.5126 48.2281 17.4615C48.114 17.4113 48.0316 17.3065 48.0038 17.1792L47.781 16.1684L47.2528 16.4677L47.2541 16.4679Z",fill:"#222222"})]})},cn=function(e){var n=e.className,t=void 0===n?"":n;return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"30",height:"25",viewBox:"0 0 30 25",fill:"none",className:t,children:(0,o.jsx)("path",{d:"M24.5137 8.59375L15.3696 16.4063L6.22552 8.59375",stroke:"#5246F5",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})},un=t(8324),dn=t.n(un),pn=function(e){var n=e.children,t=e.className,i=e.openerIcon,a=e.onToggle,s=(0,r.useState)(!1),l=s[0],c=s[1],u=(0,r.useRef)(null);return(0,r.useEffect)((function(){var e=function(e){u.current&&!u.current.contains(e.target)&&l&&c(!1)};return document.addEventListener("mousedown",e),function(){document.removeEventListener("mousedown",e)}}),[l]),(0,o.jsxs)("div",{ref:u,className:"".concat(dn().drop," ").concat(t||""," ").concat(l?dn().active:""),children:[(0,o.jsxs)("button",{className:dn().opener,onClick:function(){a&&"function"===typeof a&&a(!l),c(!l)},type:"button",children:[i&&(0,o.jsx)("span",{className:dn().icon,children:i}),(0,o.jsx)("span",{className:dn().openerText,children:"Customize your shopping experience"}),(0,o.jsx)(cn,{className:"".concat(dn().arrow," ").concat(l?dn().active:"")})]}),n]})};pn.DropContent=function(e){var n=e.children;return(0,o.jsx)("div",{className:dn().dropContent,children:n})};var mn=pn,fn=t(17601),vn=function(e){var n=e.className,t=void 0===n?"":n;return(0,o.jsxs)("svg",{className:t,xmlns:"http://www.w3.org/2000/svg",width:"57",height:"15",viewBox:"0 0 57 15",fill:"none",children:[(0,o.jsx)("path",{d:"M49.3144 0.0632324H8.12946C3.9285 0.0632324 0.522949 3.39259 0.522949 7.49956C0.522949 11.6065 3.9285 14.9359 8.12946 14.9359H49.3144C53.5154 14.9359 56.9209 11.6065 56.9209 7.49956C56.9209 3.39259 53.5154 0.0632324 49.3144 0.0632324Z",fill:"#333227"}),(0,o.jsx)("path",{d:"M18.5918 12.343V2.64844H22.8885C25.1627 2.64844 26.2926 4.17422 26.2926 5.93359C26.2926 7.69297 25.1619 9.175 22.8885 9.175H21.4014V12.343H18.5918ZM22.5465 4.98828H21.4014V6.83437H22.5465C23.0667 6.83437 23.4391 6.48594 23.4391 5.90391C23.4391 5.32188 23.0675 4.98828 22.5465 4.98828Z",fill:"white"}),(0,o.jsx)("path",{d:"M27.2884 12.343V2.64844H30.098V9.94453H33.1601V12.343H27.2876H27.2884Z",fill:"white"}),(0,o.jsx)("path",{d:"M34.0076 2.64844H36.8468V8.81094C36.8468 9.61016 37.2184 10.1047 38.0662 10.1047C38.914 10.1047 39.2704 9.61016 39.2704 8.81094V2.64844H42.1096V8.78203C42.1096 11.1367 40.549 12.518 38.0654 12.518C35.5818 12.518 34.0068 11.1367 34.0068 8.79688V2.64844H34.0076Z",fill:"white"}),(0,o.jsx)("path",{d:"M44.2801 9.13052C44.8746 9.74067 45.7816 10.1477 46.7181 10.1477C47.2535 10.1477 47.6546 9.91489 47.6546 9.55161C47.6546 9.13052 47.2088 8.89771 46.4209 8.65005C45.1423 8.2438 43.2693 7.73442 43.2693 5.5688C43.2693 3.98442 44.5182 2.48755 46.9562 2.48755C48.2652 2.48755 49.4542 2.88052 50.3308 3.60708L49.0523 5.64224C48.3986 5.10474 47.5955 4.85786 46.9562 4.85786C46.317 4.85786 46.1388 5.11958 46.1388 5.41021C46.1388 5.81646 46.5695 6.02036 47.4021 6.28208C48.6807 6.68911 50.4938 7.27036 50.4938 9.29067C50.4938 11.311 49.0666 12.5172 46.7629 12.5172C45.0528 12.5172 43.8638 12.0375 42.9712 11.2235L44.2801 9.13052Z",fill:"white"}),(0,o.jsx)("path",{d:"M14.9307 7.50005C14.9307 7.92739 14.5879 8.27896 14.1516 8.30005L13.9174 8.31411C12.7811 8.39771 12.1123 8.68364 11.6976 9.10161C11.2509 9.55239 10.952 10.3055 10.8921 11.611L10.8849 11.6899C10.8274 12.0797 10.4846 12.3758 10.0746 12.3758C9.63673 12.3758 9.27634 12.0391 9.25636 11.611C9.19643 10.3055 8.89837 9.55317 8.45088 9.10161C8.03695 8.68364 7.36732 8.39771 6.23102 8.31411L5.99688 8.30005C5.56058 8.27974 5.21777 7.92739 5.21777 7.50005C5.21777 7.09927 5.51903 6.76489 5.91697 6.70786L5.99768 6.70005C7.27782 6.63989 8.00978 6.34458 8.45168 5.89849C8.8704 5.47583 9.15887 4.78755 9.24278 3.62739L9.25716 3.38911C9.27714 2.96099 9.63753 2.62505 10.0754 2.62427C10.5133 2.62427 10.8737 2.96099 10.8937 3.38911C10.9536 4.69458 11.2517 5.44692 11.6992 5.89849C12.1411 6.34458 12.873 6.63989 14.1532 6.70005L14.2339 6.70786C14.631 6.76489 14.9331 7.09927 14.9331 7.50005H14.9307Z",fill:"#DECA33"})]})},hn=t(61032),Ln=t.n(hn),bn=function(e){var n=e.className;return(0,o.jsxs)("strong",{className:"".concat(Ln().bfPlusLogo," ").concat(n||""),children:["BuzzFeed ",(0,o.jsx)(vn,{})]})},gn=t(86796),_n=t.n(gn);function Cn(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function xn(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){Cn(e,n,t[n])}))}return e}var yn=[{icon:(0,o.jsx)(an,{}),label:"Take a break",value:"no-shopping"},{icon:(0,o.jsx)(sn,{}),label:"Just browsing",value:"shopping-lite"},{icon:(0,o.jsx)(ln,{}),label:"Shopaholic",value:"default"}],jn=function(e){var n=e.adsDisabled,t=e.onChange,i=e.className,a=e.isSplash,s=void 0!==a&&a,l=(0,r.useState)("default"),c=l[0],u=l[1],d=(0,r.useState)(!1),p=d[0],m=d[1],f=(0,r.useState)(!0),v=f[0],h=f[1],L=(0,r.useContext)(Ae.WN),b=L.experiments,g=L.getExperimentValue,_=(0,r.useContext)(Ae.z1),C=_.isSponsored,x=_.membershipAvailable,y=(0,r.useState)(!1),j=y[0],w=y[1],N=(0,K.EF)(),Z=N.trackContentAction,O=N.trackInternalLink,k=N.trackImpression,P=(0,K.ac)().isMobile,M=(0,r.useState)((0,o.jsx)(ln,{})),T=M[0],S=M[1],D={context_page_type:"feed",context_page_id:1,unit_type:"nav",unit_name:"secondary",item_type:"button",item_name:"membership_get_started"},E=xn({},D,{target_content_type:"utility",target_content_id:"membership_products"}),F={"no-shopping":"shopping_toggle_none","shopping-lite":"shopping_toggle_lite",default:"shopping_toggle_normal"},R=function(e){if(e){var t=n?xn({},E,{target_content_type:"feed",target_content_id:1,item_name:F[c]||"shopping_toggle_normal"}):E;k(t)}},I=function(e){var n=e.className;return(0,o.jsxs)("div",{className:"".concat(_n().slider," ").concat(n||""),children:[yn.map((function(e,n){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("input",{type:"radio",name:"control",id:"control-".concat(n),checked:c===e.value,onChange:function(){return function(e){var n;rn.Z.set({key:"member-hp",value:e}),rn.Z.set({key:"member-hp-interacted",value:!0}),u(e),S(null===(n=yn.find((function(n){return n.value===e})))||void 0===n?void 0:n.icon),Z(xn({},D,{item_name:"shopping_toggle",action_type:"select",action_value:e.replace("-","_")})),t(e)}(e.value)}}),(0,o.jsx)("label",{htmlFor:"control-".concat(n),"data-label":e.label,children:e.icon})]})})),(0,o.jsx)("div",{className:_n().pos})]})};return(0,r.useEffect)((function(){var e=rn.Z.get("member-hp-interacted");m(e)}),[]),(0,r.useEffect)((function(){if((null===b||void 0===b?void 0:b.loaded)&&!(null===b||void 0===b?void 0:b.stale)){var e=n||"on"===g("BF-14546-member-shopping-control",{rejectErrors:!1})&&!n&&!C;w(e)}}),[null===b||void 0===b?void 0:b.loaded,null===b||void 0===b?void 0:b.stale,g]),(0,r.useEffect)((function(){if(j&&x&&(!s||P&&!n)&&(s||!P||n))if(n){var e;k(xn({},E,{target_content_type:"feed",target_content_id:1,item_name:F[c]||"shopping_toggle_normal"}));var o=rn.Z.get("member-hp"),r=null===(e=yn.find((function(e){return e.value===o})))||void 0===e?void 0:e.icon;S(r),"default"!==o&&(t(o),u(o))}else{var i=JSON.parse(rn.Z.get("non-member-hp-views"))||[];i.length>=9&&(i=[]),i.push(Date.now()),rn.Z.set({key:"non-member-hp-views",value:JSON.stringify(i)}),1===i.length&&(k(E),h(!1))}}),[j,n]),!j||!x||s&&(!P||n)||!s&&P&&!n?null:(0,o.jsxs)(o.Fragment,{children:[n&&!p&&(0,o.jsx)("div",{className:"".concat(i," ").concat(_n().container," feed-content-area"),children:(0,o.jsxs)("div",{className:_n().controlDefault,children:[(0,o.jsxs)("div",{className:_n().heading,children:[(0,o.jsxs)("span",{className:_n().subTitle,children:["Customize your shopping experience with ",(0,o.jsx)(bn,{className:_n().bfPlussLogo})]}),(0,o.jsx)("strong",{className:_n().title,children:"Select your shopping mood:"})]}),(0,o.jsx)("div",{className:_n().action,children:(0,o.jsx)(I,{})})]})}),n&&p&&(0,o.jsx)("div",{className:"".concat(i," ").concat(_n().container," ").concat(_n().alignRightSm," feed-content-area"),children:(0,o.jsx)(mn,{className:_n().controlDropDown,openerIcon:T,onToggle:R,children:(0,o.jsxs)(mn.DropContent,{children:[(0,o.jsxs)("span",{className:_n().subTitle,children:[(0,o.jsx)(bn,{className:_n().bfPlussLogo})," Exclusive"]}),(0,o.jsx)("strong",{className:_n().title,children:"Select your shopping mood:"}),(0,o.jsx)(I,{className:_n().dropSlider})]})})}),!n&&!v&&(0,o.jsx)("div",{className:"".concat(i," ").concat(_n().container," feed-content-area"),children:(0,o.jsxs)("div",{className:_n().controlDefault,children:[(0,o.jsxs)("div",{className:_n().heading,children:[(0,o.jsx)("strong",{className:_n().title,children:"Customize your shopping experience!"}),(0,o.jsxs)("span",{className:_n().subTitle,children:["Shopaholic? Need a break? Change your feed based on your mood with ",(0,o.jsx)(bn,{className:_n().bfPlussLogo})]})]}),(0,o.jsx)("div",{className:_n().action,children:(0,o.jsx)(fn.Z,{className:_n().btn,onClick:function(){return O(E)},title:"Try for free!",url:"/member-center/signup/products?utm_campaign=hp_shop_promo&utm_medium=web&utm_source=buzzfeed"})})]})}),!n&&v&&(0,o.jsx)("div",{className:"".concat(i," ").concat(_n().container," ").concat(_n().alignCenter," ").concat(_n().alignRightMd," feed-content-area"),children:(0,o.jsx)(mn,{className:_n().controlDropDown,onToggle:R,children:(0,o.jsxs)(mn.DropContent,{children:[(0,o.jsx)(bn,{className:_n().bfPlussLogo}),(0,o.jsx)("p",{className:_n().description,children:"Shopaholic? Need a break? Change your feed based on your mood."}),(0,o.jsx)("div",{className:_n().btnWrap,children:(0,o.jsx)(fn.Z,{className:_n().btn,title:"Try for free!",url:"/member-center/signup/products?utm_campaign=hp_shop_promo&utm_medium=web&utm_source=buzzfeed",onClick:function(){return O(E)}})})]})})})]})},wn=t(86),Nn=t(37963),Zn=t(35464),On=t(29169),kn=function(e){var n=e.position,t=void 0===n?"top":n,i=e.startAdIndexMobile,a=void 0===i?1:i,s=e.hasSponsoredTabEnabled,l=void 0!==s&&s,c=(0,K.ac)(),u=c.breakpoint,d=c.isMobile,p=!u||d,m=l?"Ad--top-awareness":"Ad--bottom-awareness";return(0,r.useEffect)((function(){l&&p&&document.documentElement.style.setProperty("--fixed-elements-offset-bottom","61px")}),[l,p]),p&&"top"===t?(0,o.jsx)(On.Z,{type:"awareness",className:m}):"body"===t?(0,o.jsx)(On.Z,{className:"Ad--section-awareness",stickyWithinPlaceholder:!0,type:d?"story".concat(a):"awareness"}):null},Pn=t(97143),Mn=t(55190),Tn=t(57084),Sn=t(91807),Dn=t(49277);var En=function(e){var n=e.children,t=e.userGeo,i=(0,Dn.Z)("tracking"),a=i.consentValue,s=i.isConsentKnown,l=i.isConsentReady,c=He.cG&&"US"!==t,u=(0,r.useContext)(Ae.Ui).page_edition;return(0,K.cB)(),(0,K.kJ)({}),(0,K.Jb)(),(0,K.uV)(),(0,K.sv)(),(0,o.jsx)(Ae.xr,{value:{tracking:{consentValue:a,isConsentKnown:s,isConsentReady:l}},children:(0,o.jsxs)(Nn.Z,{pageName:"home",children:[(0,o.jsx)(Tn.Z,{}),(0,o.jsx)(Pn.Z,{}),c&&(0,o.jsx)(Mn.Z,{}),(0,o.jsx)(Sn.Z,{children:(0,o.jsxs)(Zn.Z,{edition:u,pagePath:"hp",pageName:"home",children:[(0,o.jsx)(On.Z,{type:"toolbar"}),(0,o.jsx)(kn,{position:"top"}),n]})})]})})},Fn=t(1686),Rn=t.n(Fn);function In(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function Bn(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){In(e,n,t[n])}))}return e}var Vn=function(e){var n,t=e.data,i=void 0===t?{}:t,a=e.sponsor,s=void 0===a?{}:a,l=(e.tabs,e.hasSponsoredTab),c=void 0!==l&&l,u=i.enabled,d=i.cta,p=void 0===d?{}:d,m=i.title,f=i.description,v=(0,K.EF)().trackContentAction,h={unit_type:"feed",unit_name:"splash"},L=(0,r.useCallback)((function(){var e=new CustomEvent(ve.KN,{detail:"tab_sponsored"});window.dispatchEvent(e),v(Bn({},h,{item_name:"go_to_feed",item_type:"button",action_type:"navigate",action_value:"sponsored"}))}),[]);return u&&c?(0,o.jsx)("aside",{className:Rn().sponsoredBar,children:(0,o.jsxs)("div",{className:Rn().container,children:[(0,o.jsx)("h2",{className:Rn().title,children:m}),(0,o.jsx)("p",{className:Rn().description,children:f}),(0,o.jsxs)("div",{className:Rn().action,style:{"--sponsorTextColor":"--sponsorshipHighlightColor"},children:[(0,o.jsxs)("button",{className:Rn().cta,onClick:L,children:[(null===p||void 0===p||null===(n=p.icon)||void 0===n?void 0:n.length)?(0,o.jsx)("img",{className:Rn().icon,src:null===p||void 0===p?void 0:p.icon,alt:"sponsor icon","aria-hidden":"true"}):(0,o.jsx)(Je,{className:Rn().icon}),p.text]}),(0,o.jsx)(P.Z,{data:s,className:Rn().sponsor,label:"Promoted by",isTrackable:!0,showLogo:!1,trackingData:Bn({},h,{tem_name:"promoted_by",item_type:"text",target_content_type:"url"})})]})]})}):null},An=t(27625).Z,Hn=t(29875);var zn=function(){var e="adshield"===(0,(0,r.useContext)(An).getExperimentValue)("RT-1710-adshield-experiment",{rejectErrors:!1}),n=(0,r.useState)(!1),t=n[0],i=n[1];return(0,r.useEffect)((function(){e&&!t&&(i(!0),Hn._c.init({isShopping:!1,destination:"buzzfeed"}))}),[e]),(0,o.jsx)(o.Fragment,{})},qn=t(4083),Wn=t(36595),Un=t.n(Wn);function Kn(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function Gn(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),o.forEach((function(n){Kn(e,n,t[n])}))}return e}var Qn=!0;function Jn(e){var n,t,i,c,u,d,p,m,f,v,h=e.adsDisabled,L=e.header,b=e.isSponsored,g=e.membershipZones,_=e.pageConfig,C=e.previewTimestamp,x=e.sponsorship,y=e.userGeo,j=e.zones,w=void 0===j?{}:j,N=(0,r.useState)("default"),Z=N[0],O=N[1],k=(0,r.useMemo)((function(){return(0,a.pY)(w,"splash_")}),[w]),P=(0,r.useMemo)((function(){return(0,a.pY)(w,"ab_test")}),[w]),M=(0,r.useMemo)((function(){var e,n,t="default"===Z?"tab_latest":"tab_latest_".concat(Z),o=(0,a.pY)(w,"tab_");return(null===Z||void 0===Z?void 0:Z.length)&&(null===(e=g[t])||void 0===e||null===(n=e.items)||void 0===n?void 0:n.length)&&(o.tab_latest=g[t]),o}),[w,Z,g]),T=(0,r.useMemo)((function(){return(0,a.pY)(w,"dynamic_")}),[w]),S=!!(null===M||void 0===M||null===(n=M.tab_sponsored)||void 0===n||null===(t=n.items)||void 0===t?void 0:t.length),D=(0,r.useMemo)((function(){return(0,a.vW)(x)}),[x]),E=(0,a.Oy)(null===w||void 0===w||null===(i=w.fact_of_the_day)||void 0===i?void 0:i.items,C),F=(null===(c=w.promo)||void 0===c||null===(u=c.items)||void 0===u?void 0:u[0])||null,R=(null===F||void 0===F?void 0:F.content)||null,I=function(e){var n;"topic-nav-loaded"===(null===e||void 0===e||null===(n=e.data)||void 0===n?void 0:n.type)&&((null===E||void 0===E?void 0:E.text)&&window.postMessage({type:"fact-of-the-day",message:Gn({},E,{forceNewBrowserTab:s(null===E||void 0===E?void 0:E.url)})},"*"),h||b||!["US","CA"].includes(y)||window.postMessage({type:"show-membership-promo-button"},"*"))};return(0,r.useEffect)((function(){return window.addEventListener("message",I),function(){window.removeEventListener("message",I)}}),[]),(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)(En,{pageName:"home",userGeo:y,children:[!h&&(0,o.jsx)(zn,{}),(0,o.jsx)(l.Z,Gn({},L)),(0,o.jsx)(wn.default,{pageConfig:_,inlineCss:D,userGeo:y}),!h&&(0,o.jsx)(Vn,{data:null===x||void 0===x||null===(d=x.data)||void 0===d?void 0:d.tab,sponsor:null===x||void 0===x?void 0:x.sponsor,hasSponsoredTab:S}),(0,o.jsx)(jn,{adsDisabled:h,className:"".concat(Un().control," ").concat(h?"":Un().nonMember),onChange:function(e){return O(e)}}),(0,o.jsx)(re,{adsDisabled:h,zones:k,isTrackable:!0,hasSponsoredTabEnabled:S&&(null===x||void 0===x||null===(p=x.data)||void 0===p||null===(m=p.tab)||void 0===m?void 0:m.enabled),children:(0,o.jsx)("div",{className:Un().splashControlWrap,children:(0,o.jsx)(jn,{adsDisabled:h,className:"".concat(Un().controlMobile," ").concat(h?"":Un().nonMember),onChange:function(e){return O(e)},isSplash:!0})})}),"manual"===(null===R||void 0===R?void 0:R.type)&&(0,o.jsx)(fe,{item:F,className:"feed-content-area",trackingData:{unit_type:"feed",unit_name:"splash",position_in_unit:4}}),(0,o.jsx)(qn.Z,{className:"Ad--section-awareness-wrapper ".concat(x?"feed-content-area":""),children:(0,o.jsx)(kn,{startAdIndexMobile:2,position:"body",hasSponsoredTabEnabled:S&&(null===x||void 0===x||null===(f=x.data)||void 0===f||null===(v=f.tab)||void 0===v?void 0:v.enabled)})}),(0,o.jsx)("main",{className:"feed-content-area home-page",children:(0,o.jsx)(on,{membershipFeedType:Z,tabZones:M,additionalZones:Gn({},P,T),startAdIndexMobile:3,sponsorship:x})})]})})}var Yn=Jn},61032:function(e){e.exports={bfPlusLogo:"bfPlusLogo_bfPlusLogo___tbWu"}},11690:function(e){e.exports={contentList:"ContentSwitcher_contentList__vDKzo",content:"ContentSwitcher_content__ijQrh",next:"ContentSwitcher_next__sRCcj",transitionPending:"ContentSwitcher_transitionPending__z9HmO",active:"ContentSwitcher_active__UoMCL",transitionLeft:"ContentSwitcher_transitionLeft__AeGFV",transitionRight:"ContentSwitcher_transitionRight__eFuJj"}},63314:function(e){e.exports={nav:"FeedTabs_nav__5rbZN"}},8324:function(e){e.exports={drop:"dropDown_drop__QQjsY",active:"dropDown_active__Gs1lP",dropContent:"dropDown_dropContent__q75f7",arrow:"dropDown_arrow__K1uu1",opener:"dropDown_opener__i9laD",openerText:"dropDown_openerText__gghai",icon:"dropDown_icon__PDiqk"}},86796:function(e){e.exports={container:"control_container__wFz2W",alignRightSm:"control_alignRightSm__pV79U",alignRightMd:"control_alignRightMd__q6HPM",alignRight:"control_alignRight__EVEqQ",controlDefault:"control_controlDefault__dxhCJ",slider:"control_slider__zp23I",pos:"control_pos__2P9Cr",overlay:"control_overlay__B5bvR",title:"control_title__fSU8y",description:"control_description__QjnW_",controlDropDown:"control_controlDropDown__kovUK",subTitle:"control_subTitle__x2VdE",dropSlider:"control_dropSlider__1C43k",bfPlussLogo:"control_bfPlussLogo__fjAnf",btnWrap:"control_btnWrap__YnXyo",btn:"control_btn___UdWg",heading:"control_heading__DON3n",action:"control_action__PE4I_"}},42184:function(e){e.exports={container:"promoBar_container__y_nWx",icon:"promoBar_icon__XRtEj",content:"promoBar_content__hiw_x",button:"promoBar_button__xfKbN",signup:"promoBar_signup__ulWUO",login:"promoBar_login__efHrF",title:"promoBar_title__5CUT1",body:"promoBar_body__Tlmjr",emailInput:"promoBar_emailInput__8ZVfI",signupForm:"promoBar_signupForm__OnTmF",visuallyHidden:"promoBar_visuallyHidden__9IHTP"}},9052:function(e){e.exports={motd:"momentOfTheDay_motd__4qc5i",emojiReactions:"momentOfTheDay_emojiReactions__IrIVc"}},12349:function(e){e.exports={shoppingPost:"shoppingPost_shoppingPost__6ca_6"}},92977:function(e){e.exports={shoppingPosts:"shoppingPosts_shoppingPosts__QFNDu",header:"shoppingPosts_header__Rq6bV",headerLink:"shoppingPosts_headerLink__o8RE9",title:"shoppingPosts_title__opwuV",icon:"shoppingPosts_icon__mFpp3",postsContainer:"shoppingPosts_postsContainer__GOlZ1"}},23107:function(e){e.exports={headline:"trendingPosts_headline__V8a0h",spinner:"trendingPosts_spinner__v3xvg",ticker:"trendingPosts_ticker__nLxbm",cta:"trendingPosts_cta__AWIBM",rotate:"trendingPosts_rotate__HgWPU",trendingPosts:"trendingPosts_trendingPosts__aTdra",secondaryCard:"trendingPosts_secondaryCard__T092l",ctaBottom:"trendingPosts_ctaBottom___TKET"}},73206:function(e){e.exports={splash:"splash_splash__J81HZ",trendingPosts:"splash_trendingPosts__Mvshg",funModule:"splash_funModule__t0832",shoppingPosts:"splash_shoppingPosts__hx5a0",momentOfTheDay:"splash_momentOfTheDay__9bsHV"}},1686:function(e){e.exports={sponsoredBar:"sponsoredBar_sponsoredBar__9_Fqp",container:"sponsoredBar_container__XWuPE",sponsor:"sponsoredBar_sponsor__NCsWf",title:"sponsoredBar_title__EQgUq",description:"sponsoredBar_description__3I5uF",action:"sponsoredBar_action__IJEjN",cta:"sponsoredBar_cta__a_FWn",icon:"sponsoredBar_icon__iG9QI"}},36595:function(e){e.exports={"tk-new-kansas":"index_tk-new-kansas__O9iY8",splashControlWrap:"index_splashControlWrap___kfGP",control:"index_control__lQkxx"}}},function(e){e.O(0,[182,167,995,852,195,407,98,601,86,196,941,847,127,774,888,179],(function(){return n=28492,e(e.s=n);var n}));var n=e.O();_N_E=n}]);
//# sourceMappingURL=index-c9d6d8f815c54ae9.js.map