<!DOCTYPE html>
<html class="enhanced-ux" lang="en-ca">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   23 Movie And TV Show Inaccuracies About Real Events
  </title>
  <link href="https://cdn.permutive.com" rel="preconnect"/>
  <link href="https://img.buzzfeed.com/buzzfeed-static" rel="preconnect"/>
  <link href="https://img.buzzfeed.com/buzzfeed-static" rel="dns-prefetch"/>
  <link crossorigin="" href="https://abeagle-public.buzzfeed.com" rel="preconnect"/>
  <link href="https://abeagle-public.buzzfeed.com" rel="dns-prefetch"/>
  <link href="https://c.amazon-adsystem.com" rel="dns-prefetch"/>
  <link crossorigin="" href="https://c.amazon-adsystem.com" rel="preconnect"/>
  <link href="https://c.aps.amazon-adsystem.com" rel="dns-prefetch"/>
  <link crossorigin="" href="https://c.aps.amazon-adsystem.com" rel="preconnect"/>
  <link href="https://aax.amazon-adsystem.com" rel="dns-prefetch"/>
  <link href="https://aax.amazon-adsystem.com" rel="preconnect"/>
  <link href="https://config.aps.amazon-adsystem.com" rel="dns-prefetch"/>
  <link href="https://config.aps.amazon-adsystem.com" rel="preconnect"/>
  <link href="https://client.aps.amazon-adsystem.com" rel="dns-prefetch"/>
  <link href="https://client.aps.amazon-adsystem.com" rel="preconnect"/>
  <link href="https://c.amazon-adsystem.com" rel="dns-prefetch"/>
  <link href="https://tagan.adlightning.com" rel="dns-prefetch"/>
  <link href="https://cd.connatix.com" rel="dns-prefetch"/>
  <link href="https://cds.connatix.com" rel="dns-prefetch"/>
  <link href="https://cdn.permutive.com" rel="dns-prefetch"/>
  <link href="https://use.typekit.net" rel="dns-prefetch"/>
  <link href="https://p.typekit.net" rel="dns-prefetch"/>
  <link href="https://recsys-api.buzzfeed.com/web" rel="dns-prefetch"/>
  <link href="https://sentry.io" rel="dns-prefetch"/>
  <link href="https://www.google.com" rel="dns-prefetch"/>
  <link href="https://www.gstatic.com" rel="dns-prefetch"/>
  <link href="https://s.pinimg.com" rel="dns-prefetch"/>
  <link href="https://cdn-gl.imrworldwide.com" rel="dns-prefetch"/>
  <link href="https://sb.scorecardresearch.com" rel="dns-prefetch"/>
  <link href="https://amplify.outbrain.com" rel="dns-prefetch"/>
  <link href="https://tr.outbrain.com" rel="dns-prefetch"/>
  <link href="https://cdn-magiclinks.trackonomics.net" rel="dns-prefetch"/>
  <link href="https://js.stripe.com" rel="dns-prefetch"/>
  <script>
   (function(contentLayer) {var d = document,w = window,n = navigator,wp = w.performance,getCookie;if (w.__trackAbandons || !n || !n.sendBeacon || !wp || !wp.now) return;getCookie = function(cookie) {var re = new RegExp('(?:(?:^|.*;\\s*)' + cookie + '\\s*\\=\\s*([^;]*).*$)|^.*$');return d.cookie.replace(re,'$1');};w.__trackAbandons = function() {d.removeEventListener('visibilitychange',w.__trackAbandons);n.sendBeacon('https://pixiedust.buzzfeed.com/events',w.JSON.stringify([ {client_session_id:getCookie('bf-xdomain-session-uuid') || '00000000-0000-0000-0000-000000000000',client_uuid:getCookie('bf_visit') || '00000000-0000-0000-0000-000000000000',context_page_id:contentLayer.context_page_id,context_page_type:contentLayer.context_page_type,destination:contentLayer.destination || 'buzzfeed',event_ts:Math.round(w.Date.now() / 1000),event_uri:w.location.href,event_uuid:'00000000-0000-0000-0000-000000000000',metric_name:'load-abandonment',metric_type:'custom',metric_value:wp.now(),mode:w.matchMedia && w.matchMedia('screen and (max-width:51.9rem)').matches ? 'mobile' :'desktop',page_edition:contentLayer.page_edition,page_session_id:'00000000-0000-0000-0000-000000000000',referrer_uri:d.referrer,source:contentLayer.source,type:'web_performance_metric',viewport_size:{width:w.screen.width,height:w.screen.height }}]));};d.addEventListener('visibilitychange',w.__trackAbandons);})({"context_page_id":"7894981","context_page_type":"buzz","destination":"buzzfeed","page_edition":"en-ca","source":"web_bf"});
  </script>
  <script>
   (function(options) {try {if ('Profiler' in window && 'Scheduler' in window && (window.location.search.includes('e2e_test') || Math.random() <= options.sample_rate)) {window.__jsProfiler = new window.Profiler({sampleInterval:options.profiler_init_options.sampleInterval || 0,maxBufferSize:options.profiler_init_options.maxBufferSize || 10000 });}}catch (err) {}})({"profiler_init_options":{},"sample_rate":0.1});
  </script>
  <script>
   window.BZFD = {
      Config: {
        bfwInfoCookie: 'bf2-b_info'
      },
      Context: { page: {} }
    };
  </script>
  <script>
   ((env)=>{
        window.BFADS = {
            authors: (env === null || env === void 0 ? void 0 : env.authors) || null,
            badges: env.badges || null,
            buzz_id: null,
            compilation_id: null,
            cms_tag: env.cms_tags,
            cms_tags: env.cms_tags,
            cpid: null,
            dfp_keyword: env.dfp_keyword,
            pagetype: env.pagetype,
            recipe_id: null,
            section: env.section,
            title: (env === null || env === void 0 ? void 0 : env.title) || null,
            video_id: null,
            zone3: env.zone3,
            poe: env.poe,
            w_category: null,
            w_keyword: null,
            w_sentiment: null
        };
        if (env.isTasty && env.tastyPageType === 'recipe') {
            window.BFADS.recipe_id = env.id;
        } else if (env.isTasty && env.tastyPageType === 'compilation') {
            window.BFADS.compilation_id = env.id;
        } else {
            window.BFADS.buzz_id = env.id;
        }
        if (env.video_id) {
            window.BFADS.video_id = env.video_id;
        }
        if (env === null || env === void 0 ? void 0 : env.watsonTargeting) {
            var ref, ref1, ref2;
            window.BFADS.w_category = (ref = env.watsonTargeting) === null || ref === void 0 ? void 0 : ref.w_category;
            window.BFADS.w_keyword = (ref1 = env.watsonTargeting) === null || ref1 === void 0 ? void 0 : ref1.w_keyword;
            window.BFADS.w_sentiment = (ref2 = env.watsonTargeting) === null || ref2 === void 0 ? void 0 : ref2.w_sentiment;
        }
        if (env === null || env === void 0 ? void 0 : env.cat) {
            window.BFADS.cat = env.cat;
        }
        if (env === null || env === void 0 ? void 0 : env.cookieAffiliate) {
            window.BFADS.affiliate = env.cookieAffiliate;
        }
    })({"authors":["Abby Zinman"],"badges":[],"cms_tags":["","black content","evergreen","movies","netflix","tv","TV Shows","stranger_things","the_help","argo","hidden_figures","bones","grey_s_anatomy","twister","independence_day","127_hours","13_going_on_30","moneyball","documentary","drama","horror","comedy","romance","paramount_plus","disney_plus","tv_shows","black_content","pagetype_b"],"dfp_keyword":null,"id":"7894981","section":["TVAndMovies","Internet Finds"],"zone3":"tvandmovies","isTasty":false,"pagetype":"B","poe":"","title":"Old People Are Calling Out The Historical Movie And TV Mistakes That Made Them Scream At The Screen, Because They Actually Lived Through That Era","watsonTargeting":{"w_category":"news_and_politics,movies,music_and_audio,family_and_relationships,events_and_attractions,medical_health,law,horror_movies,childrens_music,family_and_children_movies,indie_and_arthouse_movies,documentary_movies,personal_celebrations_life_events,animation_movies,divorce,single_life,surgery","w_sentiment":"negative","w_keyword":"ridiculously_common_movie_mistakes,people,older_people,movie_mistakes,certified_young_person"},"getPageSessionId":{},"cat":"1,2,4,5,9,20,22,23,24,29,36,39,42,44","cookieAffiliate":""})
  </script>
  <script>
  </script>
  <link as="script" crossorigin="anonymous" href="https://cdn.cookielaw.org/scripttemplates/otSDKStub.js" rel="preload"/>
  <link as="script" href="/static-assets/js/vendor.7108a39869c999f3999a.js?brotli=allow" rel="preload"/>
  <link as="script" href="/static-assets/js/subbuzzes.5eb342f74f1c6ba74a8a.js?brotli=allow" rel="preload"/>
  <link as="script" href="https://securepubads.g.doubleclick.net/tag/js/gpt.js" rel="preload"/>
  <meta content="Copyright BuzzFeed, Inc. All rights reserved." name="copyright"/>
  <meta content="yes" name="apple-mobile-web-app-capable"/>
  <meta content="BuzzFeed" name="apple-mobile-web-app-title"/>
  <meta content="#ee3322" name="theme-color"/>
  <link crossorigin="" href="/static-assets/_next/static/images/favicon-496b7cee633e6a7dca162654e1bb39c9.ico" rel="shortcut icon" type="image/x-icon"/>
  <link crossorigin="use-credentials" href="/manifest.json" rel="manifest"/>
  <meta content="23 Movie And TV Show Inaccuracies About Real Events" name="title"/>
  <meta content="I guess we shouldn't trust Hollywood for a history lesson." name="description"/>
  <meta content="7894981" property="bf:buzzid"/>
  <meta content="3647075" property="bf:userid"/>
  <link href="https://www.buzzfeed.com/abbyzinman/movie-mistakes-about-real-events" rel="canonical"/>
  <link href="https://www.buzzfeed.com/abbyzinman/movie-mistakes-about-real-events" hreflang="en-ca" rel="alternate"/>
  <link href="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/3-He8cvGR.jpg?crop=1245:830;3,0&amp;resize=1250:830" rel="image_src"/>
  <meta content="max-snippet:-1,max-image-preview:large,max-video-preview:-1" name="robots"/>
  <meta content="Abby Zinman" property="author"/>
  <meta content="https://img-mkr.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/3-He8cvGR.jpg?crop=1245:830;3,0&amp;resize=1250:830?template=pinterest-vertical-halftone-v1&amp;title=T2xkIFBlb3BsZSBBcmUgQ2FsbGluZyBPdXQgVGhlIEhpc3RvcmljYWwgTW92aWUgQW5kIFRWIE1pc3Rha2VzIFRoYXQgTWFkZSBUaGVtIFNjcmVhbSBBdCBUaGUgU2NyZWVuLCBCZWNhdXNlIFRoZXkgQWN0dWFsbHkgTGl2ZWQgVGhyb3VnaCBUaGF0IEVyYQ==&amp;color1=red" property="pin:media"/>
  <meta content="Old People Are Calling Out The Historical Movie And TV Mistakes That Made Them Scream At The Screen, Because They Actually Lived Through That Era" property="pin:description"/>
  <meta content="noauto" property="fb:smart_publish:robots"/>
  <meta content="45075597673" property="fb:app_id"/>
  <meta property="al:ios:app_store_id"/>
  <meta property="al:ios:app_name"/>
  <meta property="al:ios:url"/>
  <meta content="Canada" property="article:section"/>
  <meta content="https://www.facebook.com/BuzzFeed" property="article:publisher"/>
  <meta content="free" property="article:content_tier"/>
  <meta content="false" property="article:opinion"/>
  <meta content="" property="article:tag"/>
  <meta content="black content" property="article:tag"/>
  <meta content="evergreen" property="article:tag"/>
  <meta content="movies" property="article:tag"/>
  <meta content="netflix" property="article:tag"/>
  <meta content="tv" property="article:tag"/>
  <meta content="TV Shows" property="article:tag"/>
  <meta content="stranger_things" property="article:tag"/>
  <meta content="the_help" property="article:tag"/>
  <meta content="argo" property="article:tag"/>
  <meta content="hidden_figures" property="article:tag"/>
  <meta content="bones" property="article:tag"/>
  <meta content="grey_s_anatomy" property="article:tag"/>
  <meta content="twister" property="article:tag"/>
  <meta content="independence_day" property="article:tag"/>
  <meta content="127_hours" property="article:tag"/>
  <meta content="13_going_on_30" property="article:tag"/>
  <meta content="moneyball" property="article:tag"/>
  <meta content="documentary" property="article:tag"/>
  <meta content="drama" property="article:tag"/>
  <meta content="horror" property="article:tag"/>
  <meta content="comedy" property="article:tag"/>
  <meta content="romance" property="article:tag"/>
  <meta content="paramount_plus" property="article:tag"/>
  <meta content="disney_plus" property="article:tag"/>
  <meta content="tv_shows" property="article:tag"/>
  <meta content="https://www.buzzfeed.com/abbyzinman/movie-mistakes-about-real-events" property="og:url"/>
  <meta content="BuzzFeed" property="og:site_name"/>
  <meta content="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/thumb/WjfFayDO4.jpg?crop=2999:1570;0,0%26downsize=1250:*" property="og:image"/>
  <meta content="Two women in supermarket uniforms working and looking serious. Text criticizes &quot;The Help&quot; for inaccurately portraying Black domestic workers' trauma in the 1950s-60s" property="og:image:alt"/>
  <meta content="Older People Are Sharing The Worst On-Screen Inaccuracies About Real Events" property="og:title"/>
  <meta content="I guess we shouldn't trust Hollywood for a history lesson." property="og:description"/>
  <meta content="article" property="og:type"/>
  <meta content="352969997" name="twitter:app:id:iphone"/>
  <meta content="buzzfeed://buzz/abbyzinman/movie-mistakes-about-real-events" name="twitter:app:url:iphone"/>
  <meta content="352969997" name="twitter:app:id:ipad"/>
  <meta content="buzzfeed://buzz/abbyzinman/movie-mistakes-about-real-events" name="twitter:app:url:ipad"/>
  <meta content="com.buzzfeed.android" name="twitter:app:id:googleplay"/>
  <meta content="https://www.buzzfeed.com/abbyzinman/movie-mistakes-about-real-events" name="twitter:app:url:googleplay"/>
  <meta content="@BuzzFeedCanada" name="twitter:site"/>
  <meta content="@https://x.com/aezinnyy" name="twitter:creator"/>
  <meta content="https://www.buzzfeed.com/abbyzinman/movie-mistakes-about-real-events" name="twitter:url"/>
  <meta content="Older People Are Sharing The &quot;Painfully Obvious&quot; Mistakes They've Seen In Movies About Real-Life Events They Lived Through, And All I Can Say Is WOW" name="twitter:title"/>
  <meta content="I guess we shouldn't trust Hollywood for a history lesson." name="twitter:description"/>
  <meta content="summary_large_image" name="twitter:card"/>
  <meta content="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/thumb/WC2_q0loz.jpg?crop=3000:1500;0,0%26downsize=1250:*" name="twitter:image"/>
  <meta content="Canada" itemprop="articleSection"/>
  <script type="application/ld+json">
   {"@context":"https://schema.org","@type":"NewsArticle","isAccessibleForFree":true,"mainEntityOfPage":"https://www.buzzfeed.com/abbyzinman/movie-mistakes-about-real-events","description":"I guess we shouldn't trust Hollywood for a history lesson.","headline":"23 Movie And TV Show Inaccuracies About Real Events","datePublished":"2025-07-07T18:31:02.000Z","dateModified":"","author":[{"@type":"Person","name":"Abby Zinman","url":"https://www.buzzfeed.com/abbyzinman","jobTitle":"BuzzFeed Staff"}],"keywords":["","black content","evergreen","movies","netflix","tv","TV Shows"],"publisher":{"@type":"Organization","name":"BuzzFeed","url":"https://www.buzzfeed.com","sameAs":["https://www.twitter.com/buzzfeed","https://www.facebook.com/BuzzFeed","https://www.pinterest.com/buzzfeed","https://www.youtube.com/user/buzzfeedvideo"],"logo":{"@type":"ImageObject","url":"https://img.buzzfeed.com/buzzfeed-static/static/images/amp/buzzfeed-60px-high.png","width":341,"height":60}},"image":{"@type":"ImageObject","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/3-He8cvGR.jpg?crop=1245:830;3,0&resize=1250:830&resize=1200:*","width":1200,"representativeOfPage":true},"commentCount":100,"articleSection":"TVAndMovies","comment":[{"@type":"Comment","datePublished":"2025-07-07T19:27:13.000Z","author":{"@type":"Person","name":"LuluPanda57"},"text":"If someone wants to call something a travesty, it would be nice to be able to see the reasons. Just saying something is terrible isn't very interesting to read.","upvoteCount":47,"downvoteCount":0},{"@type":"Comment","datePublished":"2025-07-07T19:17:36.000Z","author":{"@type":"Person","name":"Alchemist1342"},"text":"#3. We used \"douchebag\" all the time in the 70's. And clothes weren't uncomfortable, but it was stylish for certain cliques to go shirtless.","upvoteCount":36,"downvoteCount":0},{"@type":"Comment","datePublished":"2025-07-07T19:31:02.000Z","author":{"@type":"Person","name":"thurstonfluff"},"text":"Every movie where a bell rings to signal the end of class at a college. ","upvoteCount":35,"downvoteCount":0},{"@type":"Comment","datePublished":"2025-07-07T20:07:31.000Z","author":{"@type":"Person","name":"mechanicalman"},"text":"Gotta disagree with #3, although it’s not a topic I’m going to invest a lot of energy in. Douchebag was indeed a very commonly used insult in the 80s. I heard it all the time.","upvoteCount":24,"downvoteCount":0},{"@type":"Comment","datePublished":"2025-07-07T20:31:02.000Z","author":{"@type":"Person","name":"abourque"},"text":"8. Bones is not a medical hospital show. They work on dead people. There is no need of nurses for Bones. I think you probably meant House.","upvoteCount":21,"downvoteCount":0},{"@type":"Comment","datePublished":"2025-07-07T20:58:57.000Z","author":{"@type":"Person","name":"lifetheuniverseandeverything"},"text":"This is post is so lazy. I wanted to see specifics about failings in movies had about real historical events or time periods.\n\nNot just 'I work in X and it's nothing like the movies'","upvoteCount":20,"downvoteCount":0},{"@type":"Comment","datePublished":"2025-07-07T19:34:50.000Z","author":{"@type":"Person","name":"jannar2"},"text":"What the hell is #3 talking about? The word \"douchbag\" has been around forever, and yes, believe it or not, children's clothing in the 1970s and 1980s was comfortable. We weren't all walking around naked.","upvoteCount":20,"downvoteCount":0},{"@type":"Comment","datePublished":"2025-07-07T22:11:12.000Z","author":{"@type":"Person","name":"pamelaehn"},"text":"19. There hasn’t been a parking space in Boston since 1965 so…","upvoteCount":10,"downvoteCount":0},{"@type":"Comment","datePublished":"2025-07-08T02:19:42.000Z","author":{"@type":"Person","name":"wickedwalrus90"},"text":"Wait, Ron Burgundy and the Channel 4 News Team is not based on fact?\nNooooooo!","upvoteCount":8,"downvoteCount":0},{"@type":"Comment","datePublished":"2025-07-07T21:20:08.000Z","author":{"@type":"Person","name":"messyminion34"},"text":"Belker from Hill Street Blues made \"douchebag\" even more of a thing in 1981. Twas slang, for sure.","upvoteCount":8,"downvoteCount":0}]}
  </script>
  <script data-schema="Organization" type="application/ld+json">
   {"@context":"https://schema.org","@type":"Organization","name":"BuzzFeed","url":"https://www.buzzfeed.com","sameAs":["https://www.twitter.com/buzzfeed","https://www.facebook.com/BuzzFeed","https://www.pinterest.com/buzzfeed","https://www.youtube.com/user/buzzfeedvideo"],"logo":{"@type":"ImageObject","url":"https://img.buzzfeed.com/buzzfeed-static/static/images/amp/buzzfeed-60px-high.png","width":341,"height":60}}
  </script>
  <script type="application/ld+json">
   {"@context":"https://schema.org","@type":"ItemList","itemListElement":[]}
  </script>
  <meta content="121" name="next-head-count"/>
  <noscript data-n-css="">
  </noscript>
  <script defer="" nomodule="" src="/static-assets/_next/static/chunks/polyfills-5cd94c89d3acac5f.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/webpack-9de0a42bec0816e1.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/framework-348f86d225132ea3.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/main-9e5b78809fbce3ca.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/pages/_app-9fcdc9e3554f316c.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/673-0a38239c48108ccb.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/553-4fee0a65752085b0.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/548-9bc276dd548f39a1.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/131-b4be32cbf5e7e201.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/542-2f96d7037a2bf128.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/403-6ce5eb073f06f34c.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/884-1e5bb3e5299f3027.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/pages/%5BeditionOrAuthor%5D/%5BauthorOrSlug%5D-8dffd41e9ae38eb4.js">
  </script>
  <script defer="" src="/static-assets/_next/static/P_maViqitDup60wPlQIin/_buildManifest.js">
  </script>
  <script defer="" src="/static-assets/_next/static/P_maViqitDup60wPlQIin/_ssgManifest.js">
  </script>
  <script defer="" src="/static-assets/_next/static/P_maViqitDup60wPlQIin/_middlewareManifest.js">
  </script>
 </head>
 <body>
  <div data-reactroot="" id="__next">
   <script async="" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js">
   </script>
   <div class="DestinationWrapper_buzzfeed__LdORv">
    <div class="Ad Ad--pixel Ad--edit Ad--loading">
    </div>
    <div class="awareness-ad_placeholderAd__l0ClL awareness-ad_buzzfeed__sgVbG">
    </div>
    <div class="Ad Ad--tb Ad--edit Ad--loading">
    </div>
    <div>
     <link as="script" href="https://www.buzzfeed.com/static-assets/bf-header-ui/buzzfeed/app.601f2d19b55ed40fb3a8.js" rel="preload"/>
     <div>
      <div>
       <svg height="0" style="display: block;" width="0">
        <defs>
         <symbol height="18" id="tasty-logo" width="32">
          <path d="M6.49 2.41a.82.82 0 0 0-.23-.54 1.16 1.16 0 0 0-.54-.24 2.64 2.64 0 0 0-.58-.08 5.38 5.38 0 0 0-.58.07c-.2 0-1 .14-1.16.18s-.94.23-1.18.28-.4.12-.61.16-1.07.22-1.22.28A.55.55 0 0 0 0 3a1.32 1.32 0 0 0 .06.69c.**********.46.24a2.14 2.14 0 0 0 .56.07h.53c.16 0 .5-.08.53 0a3.61 3.61 0 0 1 0 .61v9.27c0 .18-.06.33-.08.56s0 .39 0 .57a1.43 1.43 0 0 0 .11.54c.**********.39.27a5.71 5.71 0 0 0 .44-.19.79.79 0 0 1 .5-.1c.35 0 .41.08.64-.38a1.42 1.42 0 0 0 .16-.52 3.85 3.85 0 0 0 0-.55c0-.18-.1-.92-.12-1.08s-.09-.94-.09-1.13v-1.72c0-.19.07-1.51.07-1.71V7.27v-.59c0-.2 0-1 .06-1.17s0-.39 0-.59V3.78c0-.25.41-.1.48-.13s.82-.06 1-.08a2.19 2.19 0 0 0 .49-.07c.16-.06.24-.27.28-.5a1.77 1.77 0 0 0 0-.58">
          </path>
          <path d="M12 14.91c0-.19-.05-.38-.06-.56s0-.38 0-.56-.08-1-.11-1.13-.14-.92-.17-1.11-.07-.36-.1-.55 0-.37-.06-.55-.15-.92-.17-1.11 0-.38 0-.57-.08-.94-.09-1.13-.05-.94-.07-1.13 0-.38 0-.57 0-.38-.07-.56-.07-.37-.11-.56-.19-.92-.24-1.11-.31-.93-.4-1.09a1.53 1.53 0 0 0-.35-.47.74.74 0 0 0-.57-.15 1.23 1.23 0 0 0-.54.15 1.21 1.21 0 0 0-.47.36 2.55 2.55 0 0 0-.23.56c-.07.19-.17.37-.23.57s-.12.4-.19.58-.13.34-.19.58-.08.38-.13.59l-.13.55c0 .19-.09.43-.13.63s-.1.57-.14.77-.15.83-.19 1-.09.42-.12.59-.12 1-.14 1.22-.06.45-.09.65-.05.37-.09.57-.17 1-.2 1.22S6 13.54 6 13.7s0 .32-.08.49-.06.32-.1.49a2.42 2.42 0 0 0-.09.83A.82.82 0 0 0 6 16a.92.92 0 0 0 .63.15c.3 0 1.1-.24 1.31-.56a4 4 0 0 0 .15-1.06 4.85 4.85 0 0 1 .06-.76c0-.06.28 0 .65 0h.6a2.91 2.91 0 0 1 .67 0 4 4 0 0 1 0 .74v1.2a1.67 1.67 0 0 0 0 .49.65.65 0 0 0 .31.37 1.29 1.29 0 0 0 1.21-.14.86.86 0 0 0 .31-.43 8 8 0 0 0 .1-1.09m-2.09-2.74a5.87 5.87 0 0 1-1 0c-.25 0-.53 0-.55-.06a6.17 6.17 0 0 1 .07-.68c0-.21.05-.73.08-1s0-.38 0-.59.09-1 .12-1.18.08-.39.11-.58l.09-.59c0-.21.18-1 .22-1.14.08-.32.17-.64.24-.64a1.71 1.71 0 0 1 .13.68c0 .2.13 1.55.15 1.86s.07 1 .09 1.21.07.4.08.6 0 .4.06.6 0 .41 0 .59.13.91 0 .93">
          </path>
          <path d="M15.23 5.63a2.8 2.8 0 0 1-.17-.63 4.18 4.18 0 0 1 0-1 3.63 3.63 0 0 1 .3-.75 1.39 1.39 0 0 1 .39-.4.33.33 0 0 1 .49.13 2.39 2.39 0 0 1 0 1.07 5.28 5.28 0 0 0-.18.53 1 1 0 0 0 .16.52.93.93 0 0 0 .31.27 1.31 1.31 0 0 0 .38.21.61.61 0 0 0 .44-.15.76.76 0 0 0 .31-.43 1.7 1.7 0 0 0 .1-.48v-.54-.54a5.11 5.11 0 0 0 0-.55 2.91 2.91 0 0 0-.15-.53 1.46 1.46 0 0 0-.29-.47 1.58 1.58 0 0 0-.42-.35 3.93 3.93 0 0 0-.51-.18 11.07 11.07 0 0 0-1.12-.33 1.46 1.46 0 0 0-.6 0 1.32 1.32 0 0 0-.5.34 1.48 1.48 0 0 0-.36.4 4.43 4.43 0 0 0-.24.54c-.07.18-.32.94-.36 1.13a5 5 0 0 0-.09.55 10.86 10.86 0 0 0 0 1.11c0 .18.08.41.11.59a9.54 9.54 0 0 0 .34 1.06c.06.16.17.34.26.54s.13.39.2.58.4.9.48 1.08.18.38.25.54.45.94.55 1.14.17.41.25.62a4 4 0 0 1 .21.63c0 .14.22.86.23 1a2.2 2.2 0 0 1-.19 1c-.13.19-.21.27-.42.26a.57.57 0 0 1-.48-.43 1.94 1.94 0 0 1-.05-.67v-.57a3.8 3.8 0 0 0-.09-.56 1.5 1.5 0 0 0-.19-.52.69.69 0 0 0-.42-.36 1.22 1.22 0 0 0-.59.05.88.88 0 0 0-.51.27 1.1 1.1 0 0 0-.27.55s0 .93.05 1.14.09.36.13.55a4.82 4.82 0 0 0 .17.54 7.54 7.54 0 0 0 .51 1 3.23 3.23 0 0 0 .37.42 3.18 3.18 0 0 0 .44.33 4.66 4.66 0 0 0 1 .43 1.82 1.82 0 0 0 1.12-.15 2.1 2.1 0 0 0 .48-.3 4.79 4.79 0 0 0 .71-.89 3.66 3.66 0 0 0 .24-.53 7.07 7.07 0 0 0 .17-1.09 5 5 0 0 0-.06-.56c0-.18-.13-.35-.17-.54s-.09-.36-.15-.54-.12-.35-.18-.53-.13-.34-.21-.52-.35-.86-.43-1l-.23-.51-.24-.51c-.08-.17-.11-.36-.19-.53l-.24-.51c-.08-.17-.17-.33-.25-.5s-.13-.35-.2-.52-.13-.35-.2-.52-.15-.34-.22-.52">
          </path>
          <path d="M23.71 2.39a1.16 1.16 0 0 0 .1-.51 1 1 0 0 0-.18-.49.82.82 0 0 0-.53-.19h-3.58a2.68 2.68 0 0 0-.52 0 .57.57 0 0 0-.44.24 1.35 1.35 0 0 0-.2.47 2.12 2.12 0 0 0 .05.52.83.83 0 0 0 .17.46c.11.13.37.13.64.1a3.5 3.5 0 0 1 .86 0c.06 0 0 .27.07.53a4.58 4.58 0 0 1 0 .56v1.73c0 .2-.08.88-.09 1.07s0 .38 0 .57V8c0 .19-.05.43-.06.62s-.13 1.43-.14 1.61 0 .9 0 1.08v.59c0 .18-.07.94-.07 1.12s-.07.91-.09 1.05a1.83 1.83 0 0 0 0 .51 1.2 1.2 0 0 0 .16.48.62.62 0 0 0 .59.16 3.76 3.76 0 0 0 .58-.15 4.13 4.13 0 0 1 .57-.17.53.53 0 0 0 .51-.26 1.17 1.17 0 0 0 .09-.59c0-.19-.06-.39-.08-.61s0-.39 0-.58v-.58-.58-.59-.59V9.4v-.64-.63-.54-1-.63-.55-.86-.7c0-.17-.06-.67-.06-.8s.27-.08.58-.09.37 0 .53 0a1.42 1.42 0 0 0 .53-.06.69.69 0 0 0 .3-.45">
          </path>
          <path d="M31.62 6.73a.46.46 0 0 0-.44-.17 1.15 1.15 0 0 0-.47.2 4 4 0 0 0-.44.29l-.41.34c-.14.12-.25.27-.38.38A2.22 2.22 0 0 1 29 8a5.7 5.7 0 0 0-.51.27c-.07 0 0-.54 0-.7s.05-.39.1-.63.07-.35.12-.56l.13-.6c.05-.25.08-.44.11-.58s.06-.33.11-.54.07-.35.11-.54.08-.36.11-.54.05-.38.07-.55a4.12 4.12 0 0 0 0-.55 1.78 1.78 0 0 0 0-.56.55.55 0 0 0-.43-.34 1.57 1.57 0 0 0-.55 0 .85.85 0 0 0-.43.33c-.12.13-.24.19-.29.38a2.37 2.37 0 0 0 0 .48c0 .19-.06.36-.06.54s0 .61-.06.8-.12.69-.15.87-.06.74-.1 1a2.7 2.7 0 0 1-.18.89.71.71 0 0 1-.57.26.86.86 0 0 1-.57-.24c-.13-.1-.13-.51-.14-.78a4.55 4.55 0 0 1 .06-.64c0-.22 0-.44.06-.64s.08-.51.1-.63.16-.84.2-1 .12-.33.15-.5.05-.37.07-.52.06-.46 0-.51a.82.82 0 0 0-.52-.22 1.39 1.39 0 0 0-.55.11c-.2.06-.4.12-.48.28a2.54 2.54 0 0 0-.17.54c0 .16-.17.91-.2 1.1s-.14.92-.17 1.11 0 .38 0 .56 0 .38-.06.56-.06.37-.07.55 0 1 0 1.12a1.46 1.46 0 0 0 .14.54 2.35 2.35 0 0 0 .28.43 1.24 1.24 0 0 0 .35.27 1.81 1.81 0 0 0 .52.2l.52.1.52.06a2.86 2.86 0 0 0 .79 0 2.82 2.82 0 0 1-.62.42l-.47.24-.47.23-.47.23-.5.23c-.17.08-.34.16-.5.25a3.83 3.83 0 0 0-.43.32 4.43 4.43 0 0 0-.51.46 3.71 3.71 0 0 0-.46.91 3.42 3.42 0 0 0-.1.58 4.14 4.14 0 0 0 0 .6 4.69 4.69 0 0 0 .09.59 8.78 8.78 0 0 0 .37 1.09 4.47 4.47 0 0 0 .32.55 3 3 0 0 0 .39.43 2.35 2.35 0 0 0 .46.37 4 4 0 0 0 1.09.38 4 4 0 0 0 1.15-.09 2.43 2.43 0 0 0 .86-.64 5.26 5.26 0 0 0 .39-.46 9.6 9.6 0 0 0 .52-1 3.89 3.89 0 0 0 .19-.62 4.41 4.41 0 0 0 .1-.62v-.62-.63-.59c0-.2-.06-.4-.05-.59s0-.54 0-.61.32-.29.49-.41.9-.54 1.08-.65a4.33 4.33 0 0 0 .5-.38c.11-.09.29-.21.45-.35a5.79 5.79 0 0 0 .74-.85.73.73 0 0 0 0-.55 1.2 1.2 0 0 0-.35-.47zm-4.4 7a3.49 3.49 0 0 1-.24.58 1.32 1.32 0 0 1-.35.45 1 1 0 0 1-.53.17 1.36 1.36 0 0 1-.54-.07 1.07 1.07 0 0 1-.48-.25 1 1 0 0 1-.22-.5 1.24 1.24 0 0 1 0-.54 3.3 3.3 0 0 1 .49-1 3.42 3.42 0 0 1 .4-.39c.13-.1.25-.2.4-.3l.46-.28c.21-.11.66-.47.69-.36s0 .26 0 .82a4.8 4.8 0 0 1 0 .55v.54a3.32 3.32 0 0 1-.08.58z">
          </path>
         </symbol>
         <symbol height="18" id="goodful-logo" width="48">
          <path d="M6.88 5.94c-.26 1.73-2.19 4.45-3.44 4.45-.83 0-1.4-.8-1.4-2C2 5.56 4.14 2.61 5.67 2.61a.92.92 0 0 1 1 .94 2.4 2.4 0 0 1-.14.81c-.24.58 1.31.91 1.43.46a5.69 5.69 0 0 0 .26-1.53 2.16 2.16 0 0 0-2.3-2.12c-3.18 0-5.6 3.89-5.6 7.36 0 1.83 1 3.33 2.82 3.33 2.16 0 4-2.64 4.54-5.22zm22.28 3.4c-.2 1.48-1 2.48-1.46 2.48-.34 0-.48-.7-.5-1.36a45.31 45.31 0 0 1 1.15-8.65c0-.66-1.58-.66-1.61.22a34.24 34.24 0 0 0-1 8.37c.09 1.77.86 2.7 1.85 2.7 1.74 0 2.37-2.5 2.6-3.76zM45 13.25c1 0 1.94-1.25 2.42-3 .11-.3-.38-.59-.74-.47-.52 1.55-1 2-1.39 2s-.58-.66-.55-1.81a32.65 32.65 0 0 1 .5-4.82c.34-1.89.49-2.73.94-2.73.23 0 .45.25.45.83 0 1.75-1.25 4.28-2.9 5l.49 1.19c1.94-1 3.63-4.14 3.63-6.12 0-1.47-.73-2.16-1.69-2.16s-2 .75-2.49 3.62c-.19 1.15-.58 4.56-.58 5.3-.01 2.36.84 3.17 1.91 3.17zm-1.73-4.87c-.27 1-1.42 3.11-2.22 3.11-.38 0-.52-.41-.52-1.64a18.77 18.77 0 0 1 .66-4.07c0-.33-1.47-.31-1.52 0a20.44 20.44 0 0 0-.67 4.1c0 2 .74 2.81 1.85 2.81 2.15 0 3.27-3.33 3.46-4.31zm-3.62-2.49c-.67 2.81-1.57 5-2.33 5-.27 0-.53-.19-.53-.75a20.66 20.66 0 0 1 .66-4.39c0-.33-1.49-.31-1.53 0a23.47 23.47 0 0 0-.67 4.42c0 1.45.83 2.06 1.64 2.06 1.79 0 2.74-2.42 3.43-5.62zm-21.45.52c-1.66 0-2.62 1.77-2.62 3.48s.68 3.45 2.47 3.45c1.33 0 2.58-1.58 2.58-4.44 0-1.06-.5-1.91-1.22-1.91A1.53 1.53 0 0 0 18 8.64a2.5 2.5 0 0 0 2.49 2.66 3.6 3.6 0 0 0 2.61-1.93c.16-.25-.61-.67-.69-.56-.56.91-1.39 1.44-2.07 1.17l-1.07-1.45a2.23 2.23 0 0 1 .11.7c0 1.23-.39 2.91-1.22 2.91-.61 0-1-1-1-2.27s.39-2.37 1.53-2.37a.93.93 0 0 0 .53-.7 1.34 1.34 0 0 0-1.02-.39zm-6.11-.11C10.7 6.29 9.54 8 9.54 10.12c0 1.63 1 3.36 2.58 3.36s2.71-2 2.71-4.44c0-.89-.53-1.89-1.27-1.89a1.29 1.29 0 0 0-1.33 1.42 1.85 1.85 0 0 0 2 1.84c1.58 0 2.6-1.39 3-3.09 0-.14-1 .08-1 .16-.61 1.19-1.53 1.87-2.11 1.69l-1-.64.53.84c0 1.45-.5 2.89-1.49 2.89-.67 0-1.11-.84-1.11-2.23 0-1.15.55-2.56 1.27-2.56a2.93 2.93 0 0 1 .58.16c.26.06.75-.47.64-.62a1.68 1.68 0 0 0-1.45-.72zM6.86 5A30.3 30.3 0 0 1 6 13.16c-.31 1.4-1.42 3.42-2.37 3.42a.54.54 0 0 1-.5-.58c0-1.16 1.72-2.28 3.43-2.8 2.07-.6 3.79-1.58 4.08-3.66L10 8.26a3.91 3.91 0 0 1-3.25 3.54c-2.42.58-5 2-5 4.28a1.72 1.72 0 0 0 1.75 1.77c2.41 0 3.81-2.72 4.31-5.42a35.27 35.27 0 0 0 .63-7.69c0-.29-1.58.01-1.58.26zm18.82 4.64c-.16 1.25-.74 2.69-1.6 2.69-.59 0-.86-.7-.86-1.39 0-1.22.67-3.2 1.64-3.2a1.08 1.08 0 0 1 1 .89l.31-1.15a1.4 1.4 0 0 0-1.32-.89c-2 0-3 2.58-3 4.36 0 1.36.64 2.69 2.08 2.69s2.38-1.85 2.58-3.1c-.04.04-.83-.95-.83-.9zm11.13-3.7l-.9-.14c-.66 2.2-1.72 4.2-3.42 4.35-.11 0 .17 1.33.61 1.17 1.9-.54 3.29-2.74 3.71-5.38zm-4.19 5.5l-.92-1a5.64 5.64 0 0 1 1.17 3.3c0 1.53-.67 2.77-1.24 2.77s-1-.55-1-4.62c0-.52 0-1.37.08-1.94.2-3.11.75-5.48 1.49-5.48.36 0 .*********** 0 1.42-1.46 3.52-2.51 3.52l.53.84C32.38 9.67 34 6.91 34 5.42c0-1.77-1.13-2.33-1.85-2.33-1.75 0-2.82 1.84-3.08 6.67 0 .64-.11 1.55-.11 2.16 0 3.52.49 5.91 2.47 5.91 1.21 0 2.72-1.28 2.72-4.45 0-1.56-1-4.37-2.77-4.37-.67 0-1.11.3-1.11 ********** 1.11 1.44 2.35 1z">
          </path>
         </symbol>
         <symbol height="45" id="huffpost-logo" width="386">
          <g>
           <path clip-rule="evenodd" d="M15.782 44.066H0V0.904007H23.394L15.782 44.066Z" fill="white" fill-rule="evenodd">
           </path>
           <path clip-rule="evenodd" d="M57.299 0.918304L54.544 16.3773H42.345L45.1 0.918304H31.051L23.504 44.0823H37.557L40.388 27.9343H52.586L49.754 44.0823H63.806L71.354 0.918304H57.299Z" fill="white" fill-rule="evenodd">
           </path>
           <path clip-rule="evenodd" d="M79.3404 0.918701L75.0424 24.9267C74.6914 26.8217 74.5214 28.5917 74.5214 30.3447C74.5214 43.0967 85.6704 45.0007 92.3154 45.0007C106.328 45.0007 112.358 40.3407 114.561 27.8067L119.263 0.918701H105.209L101.216 23.4707C99.9404 30.5287 99.0294 34.2097 93.7694 34.2097C90.3454 34.2097 88.6804 32.3947 88.6804 28.6607C88.6804 27.2397 88.8864 25.4787 89.3094 23.2767L93.3874 0.918701H79.3404Z" fill="white" fill-rule="evenodd">
           </path>
           <path clip-rule="evenodd" d="M127.329 0.918701L119.781 44.0817H133.833L136.053 31.3777H147.88L149.842 20.5107H138.028L139.334 12.4747H155.875L157.911 0.918701H127.329Z" fill="white" fill-rule="evenodd">
           </path>
           <path clip-rule="evenodd" d="M164.217 0.918701L156.669 44.0817H170.722L172.941 31.3777H184.775L186.737 20.5107H174.916L176.222 12.4747H192.764L194.799 0.918701H164.217Z" fill="white" fill-rule="evenodd">
           </path>
           <path clip-rule="evenodd" d="M215.532 22.3472H211.474L213.359 12.1682H217.139C220.223 12.1682 221.922 13.7312 221.922 16.5692C221.922 20.1332 219.474 22.3472 215.532 22.3472ZM193.635 44.0822H207.691L209.681 32.9852H216.297C228.561 32.9852 235.315 26.3392 235.315 14.2732C235.315 5.7862 229.415 0.918198 219.129 0.918198H201.183L193.635 44.0822Z" fill="white" fill-rule="evenodd">
           </path>
           <path clip-rule="evenodd" d="M258.543 33.827C254.925 33.827 252.765 31.209 252.765 26.824C252.765 25.149 252.908 23.72 253.243 22.045C254.325 16.402 256.116 11.174 261.834 11.174C265.452 11.174 267.612 13.792 267.612 18.176C267.612 19.851 267.469 21.28 267.135 22.952C266.051 28.601 264.26 33.827 258.543 33.827ZM262.676 0C247.365 0 238.224 9.913 238.224 26.518C238.224 38.091 245.505 45 257.701 45C273.012 45 282.152 35.087 282.152 18.483C282.152 6.91 274.871 0 262.676 0Z" fill="white" fill-rule="evenodd">
           </path>
           <path clip-rule="evenodd" d="M307.754 16.8716C303.323 15.4766 301.491 14.8996 301.491 12.8196C301.491 11.4246 302.423 9.79659 305.05 9.79659C307.031 9.79659 308.664 10.9196 309.34 12.5666L321.767 9.2246C320.332 3.1066 315.067 0.000595093 306.045 0.000595093C289.038 0.000595093 287.716 11.3936 287.716 14.8856C287.716 21.9656 291.48 26.2926 299.563 28.5036C301.718 29.0746 304.17 29.7236 304.17 32.1046C304.17 33.9976 302.754 35.1276 300.382 35.1276C298.185 35.1276 295.854 33.8206 295.011 31.6126L282.745 34.9396C284.098 41.3516 290.141 45.0006 299.616 45.0006C306.833 45.0006 318.94 43.0566 318.94 30.0386C318.94 23.4886 315.281 19.1816 307.754 16.8716Z" fill="white" fill-rule="evenodd">
           </path>
           <path clip-rule="evenodd" d="M331.036 44.0819H345.091L350.677 12.4749H361.289L363.325 0.9189H327.334L325.298 12.4749H336.546L331.036 44.0819Z" fill="white" fill-rule="evenodd">
           </path>
           <path clip-rule="evenodd" d="M369.393 0.904007H385.175V44.066H361.781L369.393 0.904007Z" fill="white" fill-rule="evenodd">
           </path>
          </g>
         </symbol>
         <symbol height="512" id="trending-badge" width="512">
          <circle cx="256" cy="256" r="256">
          </circle>
          <path d="M377.896 247.32l-15.2-107.775-100.934 40.72 40.006 23.1-37.795 65.46-65.46-37.794-64.49 111.71 36.12 20.853 43.638-75.582 65.462 37.795L337.89 224.22l40.006 23.1" fill="#fff">
          </path>
         </symbol>
         <symbol height="12" id="hamburger" width="16">
          <g fill-rule="nonzero">
           <path d="M0 0h16v2H0zM0 5h16v2H0zM0 10h16v2H0z">
           </path>
          </g>
         </symbol>
         <symbol height="38" id="search-icon" width="38">
          <path d="M24.5 1C17.6 1 12 6.6 12 13.5c0 2.7.9 5.2 2.4 7.3L.6 34.6c-.8.8-.8 2 0 ******* 2 .8 2.8 0l13.8-13.8c2.1 1.5 4.6 2.4 7.3 2.4C31.4 26 37 20.4 37 13.5S31.4 1 24.5 1zm0 21c-4.7 0-8.5-3.8-8.5-8.5S19.8 5 24.5 5 33 8.8 33 13.5 29.2 22 24.5 22z">
          </path>
         </symbol>
         <symbol height="38" id="close-icon" width="38">
          <polygon points="30.3,10.5 27.5,7.7 19,16.2 10.5,7.7 7.7,10.5 16.2,19 7.7,27.5 10.5,30.3 19,21.8 27.5,30.3 30.3,27.5 21.8,19 ">
          </polygon>
         </symbol>
         <symbol height="38" id="caret-icon" width="38">
          <path d="M11.5 36c-.5 0-1-.2-1.4-.6-.8-.8-.8-2 0-2.8L23.7 19 10.1 5.4c-.8-.8-.8-2 0-2.8.8-.8 2-.8 2.8 0L29.3 19 12.9 35.4c-.4.4-.9.6-1.4.6z">
          </path>
         </symbol>
         <symbol height="512" id="settings-icon" width="512">
          <path d="M254.993 352.303c-53.193-.41-95.981-43.862-95.571-97.035.41-53.193 43.862-95.981 97.035-95.571 53.193.41 95.981 43.842 95.571 97.035-.39 53.193-43.842 95.981-97.035 95.571m212.965-150.774l21.492-15.792c-11.029-36.698-30.51-69.765-56.14-96.976l-24.4 10.717c-18.739 8.238-40.075 6.364-57.682-4.041-1.347-.8-2.694-1.581-4.041-2.342-17.92-9.994-30.256-27.602-32.501-48l-2.928-26.547c-17.412-4.099-35.527-6.403-54.169-6.539-19.93-.156-39.314 2.147-57.897 6.52l-2.928 26.645c-2.245 20.36-14.543 37.947-32.423 47.961-1.327.742-2.655 1.503-3.963 2.284-17.627 10.443-39.001 12.337-57.76 4.099l-24.498-10.756c-25.61 27.192-45.033 60.259-56.062 97.015l21.414 15.733c16.709 12.259 25.552 32.013 25.357 52.724v.332c-.02 1.542-.02 3.084.02 4.626.371 20.203-9.057 39.314-25.357 51.28l-21.492 15.772c11.029 36.717 30.51 69.765 56.14 96.996l24.4-10.736c18.739-8.218 40.094-6.344 57.702 4.06 1.327.8 2.674 1.562 4.041 2.323 17.9 9.994 30.237 27.621 32.482 48l2.928 26.567c17.412 4.099 35.527 6.422 54.169 6.539 19.95.156 39.314-2.147 57.897-6.52l2.948-26.645c2.225-20.36 14.543-37.947 32.404-47.961 1.327-.742 2.655-1.503 3.963-2.284 17.627-10.443 39.001-12.337 57.76-4.099l24.498 10.756c25.63-27.192 45.053-60.278 56.081-97.035l-21.433-15.714c-16.69-12.278-25.552-32.013-25.337-52.744v-.332c0-1.542 0-3.065-.039-4.607-.371-20.203 9.057-39.314 25.357-51.28">
          </path>
         </symbol>
         <symbol height="53.2" id="bfo-logo" width="315.7">
          <path d="M31.8 27.3c2.5.2 4.6 1.2 6.4 3.1 1.8 1.9 2.7 4.5 2.7 7.8 0 4-1.3 7.3-4 9.7-2.7 2.4-6.5 3.6-11.6 3.6H.7V4.1h24.5c4.3 0 7.8 1.2 10.4 3.5 2.6 2.4 3.9 5.5 3.9 9.4 0 3-.8 5.3-2.5 7.1s-3.4 2.7-5.3 2.9l.1.3zm-19.4-3.6h10.2c1.7 0 3.1-.5 4.2-1.5 1-1 1.6-2.2 1.6-3.9 0-1.5-.5-2.6-1.5-3.5s-2.3-1.3-3.8-1.3H12.4v10.2zm10.8 18.4c1.8 0 3.3-.5 4.3-1.4 1-.9 1.5-2.2 1.5-3.9 0-1.6-.5-2.8-1.6-3.7-1-.9-2.4-1.4-4-1.4h-11v10.4h10.8zM81.4 51.5H70.1v-3.4c-2.8 3-6.5 4.5-10.9 4.5-4.2 0-7.5-1.4-10-4.1-2.5-2.7-3.8-6.3-3.8-10.7V16h11.2v19.4c0 2.1.6 3.8 1.7 5.1 1.1 1.3 2.6 1.9 4.4 1.9 2.3 0 4.1-.8 5.4-2.4 1.3-1.6 1.9-4.1 1.9-7.5V16h11.3l.1 35.5zM116.3 51.5h-30v-2.1L100.4 25H86.7v-9h29.4v2.1L102 42.5h14.2v9zM151.1 51.5h-29.9v-2.1l14-24.4h-13.7v-9h29.4v2.1l-14.1 24.4H151l.1 9zM167.9 24.4h20.5v10.2h-20.5v17h-11.7V4.1h34v10.2h-22.3v10.1zM271.3 36.7h-26c.7 4.4 4 6.9 8.9 6.9 3.9 0 7.8-1.5 10.7-3.8l4.3 7.5c-4 3.4-9.1 5.3-15 5.3-11.9 0-20-7.2-20-18.8 0-5.5 1.8-10 5.4-13.5 3.6-3.5 8.1-5.3 13.4-5.3 5.1 0 9.4 1.7 12.9 5.2s5.3 8 5.4 13.7v2.8zm-22.9-11.1c-1.4 1-2.4 2.4-2.9 4.1h14.6c-.5-1.8-1.4-3.2-2.7-4.2-1.3-.9-2.8-1.4-4.4-1.4-1.7 0-3.2.5-4.6 1.5zM229.9 36.7h-26c.7 4.4 4 6.9 8.9 6.9 3.9 0 7.8-1.5 10.7-3.8l4.3 7.5c-4 3.4-9.1 5.3-15 5.3-11.9 0-20-7.2-20-18.8 0-5.5 1.8-10 5.4-13.5 3.6-3.5 8.1-5.3 13.4-5.3 5.1 0 9.4 1.7 12.9 5.2s5.3 8 5.4 13.7v2.8zM207 25.6c-1.4 1-2.4 2.4-2.9 4.1h14.6c-.5-1.8-1.4-3.2-2.7-4.2-1.3-.9-2.8-1.4-4.4-1.4-1.7 0-3.2.5-4.6 1.5zM315 51.5h-11.3v-2.8c-2.9 2.6-6.5 3.9-11 3.9-4.8 0-8.9-1.8-12.3-5.3-3.4-3.5-5.1-8-5.1-13.5s1.7-10 5.1-13.5 7.5-5.3 12.3-5.3c4.5 0 8.2 1.3 11 3.9V.6H315v50.9zm-13.8-11.1c1.7-1.7 2.6-3.9 2.6-6.6 0-2.6-.9-4.8-2.6-6.6-1.7-1.7-3.8-2.6-6.1-2.6-2.5 0-4.5.9-6.1 2.6s-2.4 3.9-2.4 6.6c0 2.7.8 5 2.4 6.7s3.6 2.5 6.1 2.5c2.3 0 4.3-.9 6.1-2.6z">
          </path>
         </symbol>
         <symbol fill="none" height="162" id="bfj-logo" width="953">
          <path d="M67.26 93.12C72.54 93.53 77.13 95.76 81.04 99.82C84.95 103.88 86.9 109.46 86.9 116.57C86.9 125.3 84.01 132.26 78.22 137.43C72.43 142.61 64.11 145.2 53.25 145.2H0.26001V43.17H53.1C62.44 43.17 69.93 45.71 75.56 50.78C81.19 55.86 84.01 62.61 84.01 71.03C84.01 77.43 82.23 82.5 78.68 86.26C75.13 90.02 71.32 92.1 67.26 92.5V93.12ZM25.38 85.35H47.31C51.07 85.35 54.06 84.31 56.29 82.23C58.52 80.15 59.64 77.38 59.64 73.93C59.64 70.78 58.55 68.24 56.37 66.32C54.19 64.39 51.47 63.43 48.22 63.43H25.38V85.35ZM48.83 124.94C52.79 124.94 55.86 123.93 58.04 121.9C60.22 119.87 61.31 117.08 61.31 113.52C61.31 110.17 60.19 107.51 57.96 105.53C55.73 103.55 52.83 102.56 49.28 102.56H25.38V124.94H48.83Z" fill="#E63423">
          </path>
          <path d="M174.15 145.19H149.79V137.88C143.7 144.27 135.88 147.47 126.34 147.47C117.41 147.47 110.22 144.55 104.79 138.71C99.36 132.87 96.64 125.18 96.64 115.64V68.74H120.85V110.46C120.85 114.93 122.07 118.56 124.5 121.35C126.93 124.14 130.08 125.54 133.94 125.54C138.91 125.54 142.8 123.79 145.59 120.29C148.38 116.79 149.78 111.43 149.78 104.22V68.75H174.14V145.19H174.15Z" fill="#E63423">
          </path>
          <path d="M249.17 145.19H184.67V140.6L215 88.09H185.42V68.75H248.79V73.34L218.47 125.85H249.16V145.19H249.17Z" fill="#E63423">
          </path>
          <path d="M324.19 145.19H259.69V140.6L290.02 88.09H260.44V68.75H323.81V73.34L293.49 125.85H324.18V145.19H324.19Z" fill="#E63423">
          </path>
          <path d="M360.43 86.72H404.49V108.65H360.43V145.2H335.3V43.17H408.54V65.25H360.42V86.72H360.43Z" fill="#E63423">
          </path>
          <path d="M583 113.22H526.96C528.39 122.7 535.48 127.99 546.21 127.99C554.65 127.99 563 124.68 569.24 119.88L578.59 136.07C570.04 143.41 558.95 147.48 546.21 147.48C520.59 147.48 503.05 131.87 503.05 106.97C503.05 95.19 506.96 85.5 514.78 77.88C522.6 70.27 532.19 66.46 543.56 66.46C554.63 66.46 563.91 70.17 571.43 77.58C578.94 84.99 582.8 94.79 583 106.97V113.22ZM533.66 89.46C530.61 91.59 528.53 94.54 527.42 98.29H558.94C557.82 94.33 555.89 91.34 553.15 89.3C550.41 87.27 547.21 86.26 543.56 86.26C540.01 86.26 536.71 87.33 533.66 89.46Z" fill="#E63423">
          </path>
          <path d="M493.9 113.22H437.86C439.29 122.7 446.38 127.99 457.11 127.99C465.55 127.99 473.9 124.68 480.14 119.88L489.49 136.07C480.94 143.41 469.85 147.48 457.11 147.48C431.49 147.48 413.95 131.87 413.95 106.97C413.95 95.19 417.86 85.5 425.68 77.88C433.5 70.27 443.09 66.46 454.46 66.46C465.53 66.46 474.81 70.17 482.33 77.58C489.84 84.99 493.7 94.79 493.9 106.97V113.22ZM444.56 89.46C441.51 91.59 439.43 94.54 438.32 98.29H469.84C468.72 94.33 466.79 91.34 464.05 89.3C461.31 87.27 458.11 86.26 454.46 86.26C450.9 86.26 447.61 87.33 444.56 89.46Z" fill="#E63423">
          </path>
          <path d="M677.27 145.19H652.91V139.1C646.72 144.68 638.8 147.48 629.15 147.48C618.79 147.48 609.99 143.7 602.73 136.13C595.47 128.57 591.84 118.85 591.84 106.97C591.84 95.19 595.47 85.5 602.73 77.88C609.99 70.27 618.8 66.46 629.15 66.46C638.79 66.46 646.71 69.25 652.91 74.83V35.55H677.27V145.19ZM647.35 121.21C651.05 117.51 652.91 112.76 652.91 106.97C652.91 101.28 651.06 96.56 647.35 92.81C643.64 89.05 639.25 87.18 634.18 87.18C628.8 87.18 624.38 89.03 620.93 92.74C617.48 96.45 615.75 101.19 615.75 106.98C615.75 112.87 617.48 117.64 620.93 121.29C624.38 124.94 628.8 126.77 634.18 126.77C639.25 126.77 643.64 124.92 647.35 121.21Z" fill="#E63423">
          </path>
          <path d="M897.99 91.76H733.94V148.46H897.99V91.76Z" fill="white" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="0.2878">
          </path>
          <path d="M763.86 122.86C763.86 122.86 744.87 124.98 727.92 115.33C710.97 105.68 704.68 82 709.11 64.34C715.04 40.7 739.4 30.43 739.4 30.43L746.6 27.55L751.9 13.48C752.66 11.46 755.19 10.79 756.85 12.17L768.56 21.93L781.4 19.01C781.4 19.01 785.4 2.08 788.69 1.34C791.97 0.59 805.56 14.78 805.56 14.78C805.56 14.78 825.14 14.1 841.05 25.87C857.28 37.88 868.02 67.16 857.38 85.28C846.74 103.4 828.5 108.8 828.5 108.8" fill="white">
          </path>
          <mask height="123" id="mask0_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="155" x="707" y="1">
           <path d="M828.49 108.8C828.49 108.8 846.73 103.4 857.37 85.28C868.01 67.16 857.28 37.88 841.04 25.87C825.13 14.1 805.55 14.78 805.55 14.78C805.55 14.78 791.96 0.599999 788.68 1.34C785.4 2.09 781.39 19.01 781.39 19.01L768.55 21.93L756.84 12.17C755.18 10.79 752.65 11.45 751.89 13.48L746.59 27.55L739.39 30.43C739.39 30.43 715.03 40.69 709.1 64.34C704.67 82.01 710.96 105.68 727.91 115.33C744.86 124.98 763.85 122.86 763.85 122.86" fill="white">
           </path>
          </mask>
          <g mask="url(#mask0_1_14)">
           <path d="M773.76 122.22C773.76 122.22 754.77 124.34 737.82 114.69C720.87 105.04 714.58 81.36 719.01 63.7C724.94 40.06 749.3 29.79 749.3 29.79L756.5 26.91L761.8 12.84C762.56 10.82 765.09 10.15 766.75 11.53L778.46 21.29L791.3 18.37C791.3 18.37 795.3 1.44 798.59 0.7C801.87 -0.0499999 815.46 14.14 815.46 14.14C815.46 14.14 835.04 13.46 850.95 25.23C867.18 37.24 877.92 66.52 867.28 84.64C856.64 102.76 838.4 108.16 838.4 108.16" fill="white">
           </path>
          </g>
          <path d="M770.81 124.96C767.48 124.97 764.15 124.82 760.84 124.59C751 123.85 741.09 122.36 731.94 118.5C725.74 115.95 720.25 111.77 716.16 106.45C705.86 93.05 702.64 73.94 709 58.12C714.49 44.85 726.46 35.62 738.93 29.3C741.31 28.2 743.63 26.98 745.73 25.38L744.41 26.73C745.46 23.95 748.17 16.75 749.18 14.05L749.58 12.99L749.63 12.86C750.74 9.79 754.49 8.31 757.4 9.8C757.9 10.04 758.36 10.39 758.78 10.75C761.44 12.97 767.33 17.87 770.06 20.14L768.05 19.66C770.33 20.05 772.53 19.82 774.74 19.37C776.93 18.89 779.1 18.15 780.89 16.74L779.17 18.35C779.66 16.74 780.17 15.21 780.71 13.65C781.87 10.45 783 7.4 784.77 4.38C785.68 2.89 786.92 1.17 788.81 0.810001C794.45 -0.38 803.64 9.34 807.29 13.22L805.65 12.45C807.57 12.74 810.45 13.14 812.39 13.4C825.28 15.25 840.29 19.3 849.71 28.77C863.42 42.73 868.83 68.91 859.39 86.46C857.8 89.15 853.91 87.03 855.27 84.25C857.49 80.82 858.86 76.81 859.48 72.66C860.07 68.55 860 64.34 859.34 60.23C857.52 49.08 851.76 38.45 843.11 31.19C840.77 29.25 838.1 27.7 835.45 26.24C827.13 21.76 818.02 18.32 808.57 17.2C807.69 17.12 806.7 17.07 805.82 17.09C805.28 17.15 804.71 17.13 804.31 16.75L803.82 16.33C803.32 15.78 802.77 15.19 802.23 14.64C799.73 12.09 793.12 5.53 789.72 5.38C789.75 5.37 789.79 5.36 789.82 5.35C789.85 5.34 789.88 5.33 789.9 5.31C789.91 5.31 789.87 5.33 789.81 5.39C788.44 6.9 787.67 8.87 786.79 10.82C785.59 13.68 784.54 16.72 783.64 19.69C783.39 20.52 782.71 21.11 781.92 21.29C780.77 21.02 779.64 21 778.49 21.07C776.27 21.22 774.15 21.77 772.08 22.59C771.05 23.02 770.04 23.5 769.07 24.21C769.03 24.24 768.97 24.25 768.92 24.24L767.06 23.73C764.16 21.32 758.32 16.44 755.46 14.07C755 13.71 754.22 13.92 754.02 14.47L754 14.51L753.95 14.64C752.72 17.91 750.04 25.02 748.78 28.37C748.54 29.01 748.04 29.48 747.46 29.72C745.81 29.89 744.21 30.26 742.63 30.7C741.09 31.14 739.6 31.57 738.16 32.32C732.56 35.25 727.53 39.38 723.44 44.17C713.2 56.03 709.71 72.1 712.06 87.39C713.92 100.1 720.38 111.63 732.78 116.53C743.86 121.07 756.25 121.85 768.07 120.64C768.75 120.54 769.74 120.45 770.38 120.32C771.65 120.09 772.87 120.93 773.11 122.2C773.39 123.62 772.25 124.98 770.81 124.96Z" fill="black">
          </path>
          <path d="M810.728 85.5572C819.549 83.5525 824.649 72.9036 822.12 61.7721C819.59 50.6406 810.388 43.2419 801.567 45.2466C792.746 47.2513 787.646 57.9002 790.175 69.0317C792.705 80.1632 801.907 87.5619 810.728 85.5572Z" fill="white" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3.0322">
          </path>
          <mask height="41" id="mask1_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="35" x="790" y="46">
           <path d="M816.478 85.1559C824.635 81.2464 827.249 69.7324 822.315 59.4387C817.382 49.1449 806.77 43.9695 798.612 47.8791C790.455 51.7886 787.842 63.3026 792.775 73.5964C797.709 83.8901 808.321 89.0655 816.478 85.1559Z" fill="white">
           </path>
          </mask>
          <g mask="url(#mask1_1_14)">
           <path d="M815.881 77.3778C823.669 75.6081 828.171 66.2061 825.937 56.3779C823.704 46.5497 815.58 40.0169 807.793 41.7866C800.006 43.5564 795.504 52.9583 797.737 62.7866C799.971 72.6148 808.094 79.1475 815.881 77.3778Z" fill="#E53825" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3.502">
           </path>
          </g>
          <mask height="42" id="mask2_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="34" x="789" y="44">
           <path d="M810.728 85.5572C819.549 83.5525 824.649 72.9036 822.12 61.7721C819.59 50.6406 810.388 43.2419 801.567 45.2466C792.746 47.2513 787.646 57.9002 790.175 69.0317C792.705 80.1632 801.907 87.5619 810.728 85.5572Z" fill="white">
           </path>
          </mask>
          <g mask="url(#mask2_1_14)">
           <path d="M815.876 77.3695C823.663 75.5998 828.289 66.7391 826.207 57.5787C824.125 48.4182 816.125 42.4268 808.337 44.1965C800.55 45.9663 795.925 54.8269 798.007 63.9873C800.089 73.1478 808.089 79.1392 815.876 77.3695Z" fill="#E53825">
           </path>
           <mask height="34" id="mask3_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="31" x="793" y="43">
            <path d="M816.298 75.6533C823.499 72.202 826.046 62.5371 821.986 54.0661C817.926 45.595 808.797 41.5257 801.596 44.977C794.395 48.4283 791.848 58.0932 795.908 66.5642C799.968 75.0353 809.097 79.1046 816.298 75.6533Z" fill="white">
            </path>
           </mask>
           <g mask="url(#mask3_1_14)">
            <path d="M810.403 83.0437C815.67 81.8468 819.069 77.0434 817.994 72.3151C816.92 67.5868 811.779 64.724 806.512 65.9209C801.245 67.1179 797.847 71.9212 798.921 76.6496C799.996 81.3779 805.137 84.2406 810.403 83.0437Z" fill="#F8CCC2">
            </path>
           </g>
           <mask height="34" id="mask4_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="31" x="796" y="42">
            <path d="M819.016 74.4601C826.218 71.0088 828.764 61.3439 824.704 52.8729C820.644 44.4018 811.515 40.3325 804.314 43.7838C797.113 47.2351 794.567 56.9 798.627 65.371C802.686 73.8421 811.815 77.9114 819.016 74.4601Z" fill="white">
            </path>
           </mask>
           <g mask="url(#mask4_1_14)">
            <path d="M805.181 64.7764C806.683 64.435 807.596 62.8135 807.219 61.1549C806.842 59.4962 805.318 58.4284 803.816 58.7698C802.313 59.1113 801.401 60.7327 801.778 62.3914C802.155 64.0501 803.678 65.1179 805.181 64.7764Z" fill="#F7AC9A">
            </path>
           </g>
           <path d="M815.876 77.3695C823.663 75.5998 828.289 66.7391 826.207 57.5787C824.125 48.4182 816.125 42.4268 808.337 44.1965C800.55 45.9663 795.925 54.8269 798.007 63.9873C800.089 73.1478 808.089 79.1392 815.876 77.3695Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="0.758072">
           </path>
          </g>
          <path d="M810.728 85.5572C819.549 83.5525 824.649 72.9036 822.12 61.7721C819.59 50.6406 810.388 43.2419 801.567 45.2466C792.746 47.2513 787.646 57.9002 790.175 69.0317C792.705 80.1632 801.907 87.5619 810.728 85.5572Z" stroke="black" stroke-miterlimit="10" stroke-width="1.01066">
          </path>
          <mask height="41" id="mask5_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="35" x="734" y="59">
           <path d="M760.499 97.7407C768.656 93.8312 771.269 82.3172 766.336 72.0234C761.402 61.7297 750.79 56.5543 742.633 60.4639C734.476 64.3734 731.862 75.8874 736.796 86.1811C741.729 96.4749 752.341 101.65 760.499 97.7407Z" fill="white">
           </path>
          </mask>
          <g mask="url(#mask5_1_14)">
           <path d="M759.355 90.2289C767.142 88.4592 771.644 79.0572 769.411 69.229C767.177 59.4007 759.054 52.868 751.267 54.6377C743.479 56.4074 738.977 65.8094 741.211 75.6376C743.444 85.4659 751.568 91.9986 759.355 90.2289Z" fill="#E53825" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3.502">
           </path>
          </g>
          <mask height="41" id="mask6_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="35" x="734" y="59">
           <path d="M760.499 97.7407C768.656 93.8312 771.269 82.3172 766.336 72.0234C761.402 61.7297 750.79 56.5543 742.633 60.4639C734.476 64.3734 731.862 75.8874 736.796 86.1811C741.729 96.4749 752.341 101.65 760.499 97.7407Z" fill="white">
           </path>
          </mask>
          <g mask="url(#mask6_1_14)">
           <path d="M759.355 90.2289C767.142 88.4592 771.644 79.0572 769.411 69.229C767.177 59.4007 759.054 52.868 751.267 54.6377C743.479 56.4074 738.977 65.8094 741.211 75.6376C743.444 85.4659 751.568 91.9986 759.355 90.2289Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3.502">
           </path>
          </g>
          <mask height="42" id="mask7_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="34" x="733" y="57">
           <path d="M754.79 98.2687C763.612 96.2641 768.712 85.6151 766.182 74.4836C763.652 63.3522 754.451 55.9534 745.629 57.9581C736.808 59.9628 731.708 70.6118 734.238 81.7432C736.767 92.8747 745.969 100.273 754.79 98.2687Z" fill="white">
           </path>
          </mask>
          <g mask="url(#mask7_1_14)">
           <path d="M759.818 90.1085C767.605 88.3388 772.231 79.4782 770.149 70.3177C768.067 61.1573 760.067 55.1659 752.279 56.9356C744.492 58.7053 739.867 67.566 741.949 76.7264C744.031 85.8869 752.031 91.8782 759.818 90.1085Z" fill="#E53825">
           </path>
           <mask height="34" id="mask8_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="31" x="738" y="56">
            <path d="M760.56 88.339C767.761 84.8877 770.308 75.2228 766.248 66.7518C762.188 58.2807 753.059 54.2114 745.858 57.6627C738.657 61.114 736.11 70.7789 740.17 79.25C744.23 87.721 753.359 91.7903 760.56 88.339Z" fill="white">
            </path>
           </mask>
           <g mask="url(#mask8_1_14)">
            <path d="M754.193 94.0226C758.991 92.9321 762.043 88.3591 761.009 83.8085C759.975 79.2579 755.246 76.4529 750.448 77.5434C745.65 78.6338 742.598 83.2068 743.633 87.7574C744.667 92.308 749.395 95.113 754.193 94.0226Z" fill="#F8CCC2">
            </path>
           </g>
           <mask height="34" id="mask9_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="31" x="740" y="55">
            <path d="M763.133 87.0833C770.334 83.632 772.88 73.9671 768.82 65.4961C764.76 57.025 755.632 52.9557 748.431 56.407C741.229 59.8583 738.683 69.5232 742.743 77.9942C746.803 86.4653 755.932 90.5346 763.133 87.0833Z" fill="white">
            </path>
           </mask>
           <g mask="url(#mask9_1_14)">
            <path d="M748.643 76.4724C750.038 76.1555 750.885 74.6499 750.535 73.1097C750.185 71.5695 748.77 70.5779 747.376 70.8949C745.981 71.2119 745.134 72.7174 745.484 74.2576C745.834 75.7978 747.248 76.7894 748.643 76.4724Z" fill="#F7AC9A">
            </path>
           </g>
           <path d="M759.818 90.1085C767.605 88.3388 772.231 79.4782 770.149 70.3177C768.067 61.1573 760.067 55.1659 752.279 56.9356C744.492 58.7053 739.867 67.566 741.949 76.7264C744.031 85.8869 752.031 91.8782 759.818 90.1085Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="0.758072">
           </path>
          </g>
          <path d="M754.79 98.2687C763.612 96.2641 768.712 85.6151 766.182 74.4836C763.652 63.3522 754.451 55.9534 745.629 57.9581C736.808 59.9628 731.708 70.6118 734.238 81.7432C736.767 92.8747 745.969 100.273 754.79 98.2687Z" stroke="black" stroke-miterlimit="10" stroke-width="1.01066">
          </path>
          <path d="M754.79 98.2687C763.612 96.2641 768.712 85.6151 766.182 74.4836C763.652 63.3522 754.451 55.9534 745.629 57.9581C736.808 59.9628 731.708 70.6118 734.238 81.7432C736.767 92.8747 745.969 100.273 754.79 98.2687Z" fill="white" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3.0322">
          </path>
          <mask height="41" id="mask10_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="35" x="790" y="46">
           <path d="M816.478 85.1559C824.635 81.2464 827.249 69.7324 822.315 59.4387C817.382 49.1449 806.77 43.9695 798.612 47.8791C790.455 51.7886 787.842 63.3026 792.775 73.5964C797.709 83.8901 808.321 89.0655 816.478 85.1559Z" fill="white">
           </path>
          </mask>
          <g mask="url(#mask10_1_14)">
           <path d="M815.881 77.3778C823.669 75.6081 828.171 66.2061 825.937 56.3779C823.704 46.5497 815.58 40.0169 807.793 41.7866C800.006 43.5564 795.504 52.9583 797.737 62.7866C799.971 72.6148 808.094 79.1475 815.881 77.3778Z" fill="#E53825" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3.502">
           </path>
          </g>
          <mask height="42" id="mask11_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="34" x="789" y="44">
           <path d="M810.728 85.5572C819.549 83.5525 824.649 72.9036 822.12 61.7721C819.59 50.6406 810.388 43.2419 801.567 45.2466C792.746 47.2513 787.646 57.9002 790.175 69.0317C792.705 80.1632 801.907 87.5619 810.728 85.5572Z" fill="white">
           </path>
          </mask>
          <g mask="url(#mask11_1_14)">
           <path d="M815.876 77.3695C823.663 75.5998 828.289 66.7391 826.207 57.5787C824.125 48.4182 816.125 42.4268 808.337 44.1965C800.55 45.9663 795.925 54.8269 798.007 63.9873C800.089 73.1478 808.089 79.1392 815.876 77.3695Z" fill="#EE3322">
           </path>
           <mask height="34" id="mask12_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="31" x="793" y="43">
            <path d="M816.298 75.6533C823.499 72.202 826.046 62.5371 821.986 54.0661C817.926 45.595 808.797 41.5257 801.596 44.977C794.395 48.4283 791.848 58.0932 795.908 66.5642C799.968 75.0353 809.097 79.1046 816.298 75.6533Z" fill="white">
            </path>
           </mask>
           <g mask="url(#mask12_1_14)">
            <path d="M810.403 83.0437C815.67 81.8468 819.069 77.0434 817.994 72.3151C816.92 67.5868 811.779 64.724 806.512 65.9209C801.245 67.1179 797.847 71.9212 798.921 76.6496C799.996 81.3779 805.137 84.2406 810.403 83.0437Z" fill="#FFDFD2" opacity="0.69">
            </path>
           </g>
           <mask height="34" id="mask13_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="31" x="796" y="42">
            <path d="M819.016 74.4601C826.218 71.0088 828.764 61.3439 824.704 52.8729C820.644 44.4018 811.515 40.3325 804.314 43.7838C797.113 47.2351 794.567 56.9 798.627 65.371C802.686 73.8421 811.815 77.9114 819.016 74.4601Z" fill="white">
            </path>
           </mask>
           <g mask="url(#mask13_1_14)">
            <path d="M805.181 64.7764C806.683 64.435 807.596 62.8135 807.219 61.1549C806.842 59.4962 805.318 58.4284 803.816 58.7698C802.313 59.1113 801.401 60.7327 801.778 62.3914C802.155 64.0501 803.678 65.1179 805.181 64.7764Z" fill="#F7AC9A">
            </path>
           </g>
           <path d="M815.876 77.3695C823.663 75.5998 828.289 66.7391 826.207 57.5787C824.125 48.4182 816.125 42.4268 808.337 44.1965C800.55 45.9663 795.925 54.8269 798.007 63.9873C800.089 73.1478 808.089 79.1392 815.876 77.3695Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="0.758072">
           </path>
          </g>
          <path d="M810.728 85.5572C819.549 83.5525 824.649 72.9036 822.12 61.7721C819.59 50.6406 810.388 43.2419 801.567 45.2466C792.746 47.2513 787.646 57.9002 790.175 69.0317C792.705 80.1632 801.907 87.5619 810.728 85.5572Z" stroke="black" stroke-miterlimit="10" stroke-width="1.01066">
          </path>
          <mask height="41" id="mask14_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="35" x="734" y="59">
           <path d="M760.499 97.7407C768.656 93.8312 771.269 82.3172 766.336 72.0234C761.402 61.7297 750.79 56.5543 742.633 60.4639C734.476 64.3734 731.862 75.8874 736.796 86.1811C741.729 96.4749 752.341 101.65 760.499 97.7407Z" fill="white">
           </path>
          </mask>
          <g mask="url(#mask14_1_14)">
           <path d="M759.355 90.2289C767.142 88.4592 771.644 79.0572 769.411 69.229C767.177 59.4007 759.054 52.868 751.267 54.6377C743.479 56.4074 738.977 65.8094 741.211 75.6376C743.444 85.4659 751.568 91.9986 759.355 90.2289Z" fill="#E53825" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3.502">
           </path>
          </g>
          <mask height="41" id="mask15_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="35" x="734" y="59">
           <path d="M760.499 97.7407C768.656 93.8312 771.269 82.3172 766.336 72.0234C761.402 61.7297 750.79 56.5543 742.633 60.4639C734.476 64.3734 731.862 75.8874 736.796 86.1811C741.729 96.4749 752.341 101.65 760.499 97.7407Z" fill="white">
           </path>
          </mask>
          <g mask="url(#mask15_1_14)">
           <path d="M759.355 90.2289C767.142 88.4592 771.644 79.0572 769.411 69.229C767.177 59.4007 759.054 52.868 751.267 54.6377C743.479 56.4074 738.977 65.8094 741.211 75.6376C743.444 85.4659 751.568 91.9986 759.355 90.2289Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3.502">
           </path>
          </g>
          <mask height="42" id="mask16_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="34" x="733" y="57">
           <path d="M754.79 98.2687C763.612 96.2641 768.712 85.6151 766.182 74.4836C763.652 63.3522 754.451 55.9534 745.629 57.9581C736.808 59.9628 731.708 70.6118 734.238 81.7432C736.767 92.8747 745.969 100.273 754.79 98.2687Z" fill="white">
           </path>
          </mask>
          <g mask="url(#mask16_1_14)">
           <path d="M759.818 90.1085C767.605 88.3388 772.231 79.4782 770.149 70.3177C768.067 61.1573 760.067 55.1659 752.279 56.9356C744.492 58.7053 739.867 67.566 741.949 76.7264C744.031 85.8869 752.031 91.8782 759.818 90.1085Z" fill="#EE3322">
           </path>
           <mask height="34" id="mask17_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="31" x="738" y="56">
            <path d="M760.56 88.339C767.761 84.8877 770.308 75.2228 766.248 66.7518C762.188 58.2807 753.059 54.2114 745.858 57.6627C738.657 61.114 736.11 70.7789 740.17 79.25C744.23 87.721 753.359 91.7903 760.56 88.339Z" fill="white">
            </path>
           </mask>
           <g mask="url(#mask17_1_14)">
            <path d="M754.193 94.0226C758.991 92.9321 762.043 88.3591 761.009 83.8085C759.975 79.2579 755.246 76.4529 750.448 77.5434C745.65 78.6338 742.598 83.2068 743.633 87.7574C744.667 92.308 749.395 95.113 754.193 94.0226Z" fill="#FFDFD2" opacity="0.69">
            </path>
           </g>
           <mask height="34" id="mask18_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="31" x="740" y="55">
            <path d="M763.133 87.0833C770.334 83.632 772.88 73.9671 768.82 65.4961C764.76 57.025 755.632 52.9557 748.431 56.407C741.229 59.8583 738.683 69.5232 742.743 77.9942C746.803 86.4653 755.932 90.5346 763.133 87.0833Z" fill="white">
            </path>
           </mask>
           <g mask="url(#mask18_1_14)">
            <path d="M748.643 76.4724C750.038 76.1555 750.885 74.6499 750.535 73.1097C750.185 71.5695 748.77 70.5779 747.376 70.8949C745.981 71.2119 745.134 72.7174 745.484 74.2576C745.834 75.7978 747.248 76.7894 748.643 76.4724Z" fill="#F7AC9A">
            </path>
           </g>
           <path d="M759.818 90.1085C767.605 88.3388 772.231 79.4782 770.149 70.3177C768.067 61.1573 760.067 55.1659 752.279 56.9356C744.492 58.7053 739.867 67.566 741.949 76.7264C744.031 85.8869 752.031 91.8782 759.818 90.1085Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="0.758072">
           </path>
          </g>
          <path d="M754.79 98.2687C763.612 96.2641 768.712 85.6151 766.182 74.4836C763.652 63.3522 754.451 55.9534 745.629 57.9581C736.808 59.9628 731.708 70.6118 734.238 81.7432C736.767 92.8747 745.969 100.273 754.79 98.2687Z" stroke="black" stroke-miterlimit="10" stroke-width="1.01066">
          </path>
          <path d="M757.63 71.79C759.883 71.79 761.71 69.9633 761.71 67.71C761.71 65.4567 759.883 63.63 757.63 63.63C755.377 63.63 753.55 65.4567 753.55 67.71C753.55 69.9633 755.377 71.79 757.63 71.79Z" fill="white" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="1.7035">
          </path>
          <path d="M771.98 92.76C771.97 92.76 771.96 92.76 771.95 92.76C771.95 92.77 771.95 92.78 771.95 92.79C772.07 93.18 772.4 93.48 772.73 93.72C774.01 94.59 775.59 94.19 776.88 93.4C777.82 92.87 778.69 92.01 779.49 91.28C780.17 90.69 781.06 90 782.03 89.78C782.6 89.64 783.24 89.69 783.78 89.92C784.67 90.29 785.19 90.94 785.91 91.43C787.89 92.24 790.64 91.79 792.31 90.51C793.03 89.94 793.52 89.2 793.76 88.36C794.03 87.43 795.01 86.89 795.95 87.17C797.02 87.47 797.54 88.75 796.99 89.72C795.06 93.76 790.05 95.13 786 94C785.3 93.82 784.53 93.41 784.01 92.87C783.76 92.65 783.19 92.06 782.92 91.96C782.89 91.94 782.86 91.94 782.84 91.93C782.81 91.92 782.79 91.9 782.76 91.91C782.69 91.89 782.62 91.9 782.55 91.9C781.91 92.04 781.43 92.43 780.85 92.87C779.93 93.67 778.99 94.58 777.9 95.17C775.43 96.65 772.2 96.67 769.64 95.34C768.83 94.74 768.8 93.49 769.5 92.79C770.19 92.1 771.29 92.09 771.98 92.76Z" fill="black">
          </path>
          <path d="M784.89 106.81C780.46 106.81 777.5 103.16 776.45 100.92C776.08 100.13 776.42 99.19 777.21 98.82C778 98.45 778.94 98.79 779.31 99.58C779.41 99.8 781.8 104.68 786.34 103.46C788.11 102.98 789.2 101.36 789.58 98.64C789.91 96.3 789.56 94.07 789.56 94.04C789.42 93.18 790.01 92.37 790.87 92.23C791.73 92.09 792.54 92.68 792.68 93.53C792.7 93.64 793.1 96.19 792.72 98.99C792.02 104.14 789.32 105.92 787.17 106.5C786.37 106.72 785.61 106.81 784.89 106.81Z" fill="black">
          </path>
          <path d="M779.95 86.32C779.73 86.32 779.51 86.3 779.31 86.25C778.7 86.1 778.28 85.72 778.16 85.21C778.04 84.7 778.26 84.18 778.74 83.78C779.07 83.51 779.52 83.3 780.02 83.19C780.51 83.08 781.01 83.07 781.43 83.17C782.04 83.32 782.46 83.7 782.58 84.21C782.7 84.72 782.48 85.24 782 85.64C781.67 85.91 781.22 86.12 780.72 86.23C780.46 86.29 780.2 86.32 779.95 86.32Z" fill="black">
          </path>
          <path d="M765.06 51.87C764.71 51.87 764.36 51.71 764.14 51.4C763.34 50.3 760.5 49.42 758.89 49.12C758.27 49.01 757.87 48.41 757.98 47.8C758.09 47.18 758.68 46.78 759.3 46.89C759.82 46.98 764.38 47.88 765.97 50.07C766.34 50.58 766.23 51.29 765.72 51.65C765.53 51.8 765.29 51.87 765.06 51.87Z" fill="black">
          </path>
          <path d="M779.99 48.47C779.92 48.47 779.85 48.46 779.78 48.45C779.16 48.34 778.76 47.75 778.87 47.13C779.36 44.47 783.09 41.69 783.51 41.38C784.02 41.01 784.73 41.12 785.09 41.63C785.46 42.14 785.35 42.85 784.84 43.21C783.52 44.17 781.34 46.19 781.09 47.53C781.01 48.09 780.53 48.47 779.99 48.47Z" fill="black">
          </path>
          <path d="M732.66 112.15C737.31 112.15 741.08 108.38 741.08 103.73C741.08 99.0798 737.31 95.31 732.66 95.31C728.01 95.31 724.24 99.0798 724.24 103.73C724.24 108.38 728.01 112.15 732.66 112.15Z" fill="#F3D8CB">
          </path>
          <path d="M834.49 89.01C839.14 89.01 842.91 85.2402 842.91 80.59C842.91 75.9398 839.14 72.17 834.49 72.17C829.84 72.17 826.07 75.9398 826.07 80.59C826.07 85.2402 829.84 89.01 834.49 89.01Z" fill="#F3D8CB">
          </path>
          <path d="M813.54 58.95C815.793 58.95 817.62 57.1233 817.62 54.87C817.62 52.6167 815.793 50.79 813.54 50.79C811.287 50.79 809.46 52.6167 809.46 54.87C809.46 57.1233 811.287 58.95 813.54 58.95Z" fill="white" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="1.7035">
          </path>
          <path d="M941.86 60.84L938.32 35.75C938.24 35.21 937.68 34.89 937.18 35.09L913.68 44.57C913.03 44.83 912.97 45.72 913.57 46.07L921.7 50.76C922.1 50.99 922.24 51.5 922.01 51.9L913.08 67.37C912.85 67.77 912.34 67.91 911.94 67.68L896.47 58.75C896.07 58.52 895.56 58.66 895.33 59.06L879.5 86.48C879.27 86.88 879.41 87.39 879.81 87.62L887.7 92.17C888.1 92.4 888.61 92.26 888.84 91.86L899.28 73.77C899.51 73.37 900.02 73.23 900.42 73.46L915.89 82.39C916.29 82.62 916.8 82.48 917.03 82.08L931.35 57.27C931.58 56.87 932.09 56.73 932.49 56.96L940.62 61.65C941.22 62.03 941.96 61.53 941.86 60.84Z" fill="white" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3.2626">
          </path>
          <mask height="58" id="mask19_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="63" x="879" y="35">
           <path d="M941.86 60.84L938.32 35.75C938.24 35.21 937.68 34.89 937.18 35.09L913.68 44.57C913.03 44.83 912.97 45.72 913.57 46.07L921.7 50.76C922.1 50.99 922.24 51.5 922.01 51.9L913.08 67.37C912.85 67.77 912.34 67.91 911.94 67.68L896.47 58.75C896.07 58.52 895.56 58.66 895.33 59.06L879.5 86.48C879.27 86.88 879.41 87.39 879.81 87.62L887.7 92.17C888.1 92.4 888.61 92.26 888.84 91.86L899.28 73.77C899.51 73.37 900.02 73.23 900.42 73.46L915.89 82.39C916.29 82.62 916.8 82.48 917.03 82.08L931.35 57.27C931.58 56.87 932.09 56.73 932.49 56.96L940.62 61.65C941.22 62.03 941.96 61.53 941.86 60.84Z" fill="white">
           </path>
          </mask>
          <g mask="url(#mask19_1_14)">
           <path d="M944.58 63.83L941.04 38.74C940.96 38.2 940.4 37.88 939.9 38.08L916.4 47.56C915.75 47.82 915.69 48.71 916.29 49.06L924.42 53.75C924.82 53.98 924.96 54.49 924.73 54.89L915.8 70.36C915.57 70.76 915.06 70.9 914.66 70.67L899.19 61.74C898.79 61.51 898.28 61.65 898.05 62.05L882.22 89.47C881.99 89.87 882.13 90.38 882.53 90.61L890.42 95.16C890.82 95.39 891.33 95.25 891.56 94.85L902 76.76C902.23 76.36 902.74 76.22 903.14 76.45L918.61 85.38C919.01 85.61 919.52 85.47 919.75 85.07L934.07 60.26C934.3 59.86 934.81 59.72 935.21 59.95L943.34 64.64C943.94 65.02 944.68 64.52 944.58 63.83Z" fill="#EE3322">
           </path>
          </g>
          <mask height="58" id="mask20_1_14" maskunits="userSpaceOnUse" style="mask-type:luminance" width="63" x="879" y="35">
           <path d="M941.86 60.84L938.32 35.75C938.24 35.21 937.68 34.89 937.18 35.09L913.68 44.57C913.03 44.83 912.97 45.72 913.57 46.07L921.7 50.76C922.1 50.99 922.24 51.5 922.01 51.9L913.08 67.37C912.85 67.77 912.34 67.91 911.94 67.68L896.47 58.75C896.07 58.52 895.56 58.66 895.33 59.06L879.5 86.48C879.27 86.88 879.41 87.39 879.81 87.62L887.7 92.17C888.1 92.4 888.61 92.26 888.84 91.86L899.28 73.77C899.51 73.37 900.02 73.23 900.42 73.46L915.89 82.39C916.29 82.62 916.8 82.48 917.03 82.08L931.35 57.27C931.58 56.87 932.09 56.73 932.49 56.96L940.62 61.65C941.22 62.03 941.96 61.53 941.86 60.84Z" fill="white">
           </path>
          </mask>
          <g mask="url(#mask20_1_14)">
           <path d="M888.09 93.65C887.71 93.65 887.33 93.55 887 93.35L879.11 88.8C878.6 88.51 878.24 88.03 878.09 87.47C877.94 86.9 878.02 86.31 878.31 85.8L894.14 58.38C894.43 57.87 894.91 57.51 895.47 57.36C896.04 57.21 896.63 57.29 897.14 57.58L912.16 66.25L920.57 51.69L912.89 47.26C912.16 46.84 911.74 46.05 911.8 45.2C911.86 44.36 912.38 43.63 913.17 43.32L936.67 33.84C937.3 33.59 938 33.63 938.59 33.97C939.18 34.31 939.57 34.89 939.67 35.57L943.21 60.67C943.33 61.51 942.97 62.32 942.26 62.8C941.56 63.27 940.67 63.3 939.93 62.88L932.25 58.45L918.2 82.78C917.59 83.83 916.25 84.19 915.2 83.58L900.18 74.91L890 92.55C889.71 93.06 889.23 93.42 888.67 93.57C888.47 93.62 888.28 93.65 888.09 93.65ZM880.92 86.71L887.9 90.74L898.08 73.11C898.69 72.06 900.03 71.7 901.08 72.31L916.1 80.98L930.16 56.63C930.45 56.12 930.93 55.76 931.49 55.61C932.06 55.46 932.65 55.54 933.16 55.83L940.36 59.99L937.06 36.62L915.17 45.45L922.37 49.61C922.88 49.9 923.24 50.38 923.39 50.94C923.54 51.51 923.46 52.1 923.17 52.61L914.24 68.08C913.95 68.59 913.47 68.95 912.91 69.1C912.34 69.25 911.75 69.17 911.24 68.88L896.22 60.21L880.92 86.71Z" fill="black">
           </path>
          </g>
          <path d="M941.86 60.84L938.32 35.75C938.24 35.21 937.68 34.89 937.18 35.09L913.68 44.57C913.03 44.83 912.97 45.72 913.57 46.07L921.7 50.76C922.1 50.99 922.24 51.5 922.01 51.9L913.08 67.37C912.85 67.77 912.34 67.91 911.94 67.68L896.47 58.75C896.07 58.52 895.56 58.66 895.33 59.06L879.5 86.48C879.27 86.88 879.41 87.39 879.81 87.62L887.7 92.17C888.1 92.4 888.61 92.26 888.84 91.86L899.28 73.77C899.51 73.37 900.02 73.23 900.42 73.46L915.89 82.39C916.29 82.62 916.8 82.48 917.03 82.08L931.35 57.27C931.58 56.87 932.09 56.73 932.49 56.96L940.62 61.65C941.22 62.03 941.96 61.53 941.86 60.84Z" stroke="black" stroke-miterlimit="10" stroke-width="2.7189">
          </path>
          <path d="M866 89.27C866 89.27 898.87 77.16 913.28 101.95C927.69 126.74 908.09 134.82 908.09 134.82" fill="white">
          </path>
          <path d="M908.09 136.86C907.29 136.86 906.52 136.38 906.2 135.59C905.77 134.55 906.27 133.35 907.31 132.92C907.36 132.9 913.51 130.25 915.72 123.65C917.61 117.99 916.2 111.03 911.51 102.97C905.92 93.36 896.74 88.44 884.22 88.35C874.68 88.28 866.79 91.15 866.71 91.18C865.65 91.57 864.48 91.03 864.09 89.97C863.7 88.91 864.24 87.74 865.3 87.35C865.65 87.22 873.97 84.19 884.26 84.27C898.13 84.37 908.78 90.13 915.06 100.93C920.38 110.08 921.9 118.19 919.58 125.03C916.75 133.39 909.2 136.58 908.88 136.72C908.62 136.81 908.35 136.86 908.09 136.86Z" fill="black">
          </path>
          <path d="M904.06 117.52C904.06 117.52 929.43 129.05 932.89 130.78C936.35 132.51 941.54 129.05 947.88 134.24C954.22 139.43 947.88 146.92 942.11 151.54C936.34 156.15 928.85 156.73 914.43 156.73C900.02 156.73 879.83 156.26 853.89 157.41C835.02 158.25 811.88 154.44 800.66 152.68C796.29 151.99 791.84 152.28 787.59 153.5C775.66 156.92 749.76 163.08 735.45 157.01C716.44 148.95 731.92 132.04 733.45 127.47C733.45 127.47 722.07 120.29 723.27 116.39C724.47 112.5 727.16 110.7 731.35 112.5C735.54 114.3 748.41 118.49 752.01 118.49C755.61 118.49 763.26 123.37 761.76 130.86L759.8 136.36" fill="white">
          </path>
          <path d="M904.9 115.66C914.08 120.43 924.14 125.22 933.46 129.77C933.98 130.03 934.65 130.14 935.38 130.16C936.86 130.18 938.54 129.94 940.3 129.97C943.41 129.97 946.64 131.16 949.01 133.22C956.36 139.53 948.83 148.2 943.25 152.64C940.56 154.85 937.18 156.32 933.81 157.04C930.51 157.78 927.17 158.04 923.81 158.19C901.01 158.64 876.8 157.94 854.08 159.2C853.38 159.23 851.11 159.29 850.36 159.32C849.45 159.33 847.5 159.33 846.64 159.33C836.38 159.2 826.09 158.2 815.94 156.85C811.25 156.25 806.07 155.43 801.37 154.7C799.86 154.45 798.28 154.25 796.75 154.22C794.08 154.15 791.39 154.47 788.81 155.15C778.89 157.98 768.85 160.26 758.53 161.2C750.34 161.87 741 161.91 733.44 158.29C730.1 156.7 727.02 154.41 725.32 150.98C721.98 144.11 725.82 136.59 729.36 130.7C729.97 129.66 730.99 128.06 731.46 126.98L731.53 126.81L732.38 129.19C728.97 126.72 722.01 121.66 721.61 117.36C721.58 116.89 721.63 116.39 721.76 115.93C721.77 115.9 721.78 115.88 721.79 115.86L721.84 115.72L722.04 115.17C722.66 113.63 723.6 112.15 725.09 111.23C727.23 109.85 729.91 110.24 732.09 111.24C737.73 113.54 743.61 115.47 749.55 116.83C750.22 116.96 751.17 117.15 751.85 117.17C756.76 117.33 761.96 121.72 763.2 126.44C763.63 127.99 763.7 129.65 763.43 131.24C763.14 132.23 762.83 133.22 762.54 134.22C762.27 135.16 762 136.09 761.75 137.04C761.46 138.13 760.34 138.79 759.24 138.5C758.02 138.19 757.37 136.81 757.9 135.67C758.71 133.89 759.46 132.09 760.2 130.29C760.58 128.67 760.44 126.18 758.83 124C757.29 121.92 754.82 120.1 752.23 119.79L751.16 119.73C750.79 119.69 750.43 119.63 750.06 119.58C748.36 119.28 746.6 118.85 744.96 118.41C740 117.04 735.11 115.5 730.31 113.63C727.74 112.7 725.9 113.61 725 116.29C724.76 117.05 724.79 116.88 724.88 117.3C725.47 118.82 726.85 120.06 728.16 121.22C729.13 122.06 730.18 122.85 731.26 123.61C732.34 124.37 733.52 125.12 734.64 125.79C735.43 126.29 735.73 127.28 735.41 128.12C735.25 128.59 735.01 129.1 734.78 129.53C733.48 131.95 731.76 134.51 730.58 136.95C727.69 142.69 726.37 148.75 732.35 152.98C732.92 153.39 733.61 153.81 734.22 154.13L734.7 154.4L735.21 154.64C737.25 155.64 739.46 156.3 741.68 156.74C743.78 157.14 745.93 157.37 748.07 157.48C757.87 157.88 767.64 156.28 777.23 154.14C781.22 153.28 785.15 152.09 789.11 151.11C793.33 150.18 797.73 150.16 801.97 150.93C806.63 151.66 811.8 152.5 816.45 153.11C842.36 157.44 868.35 154.47 894.38 154.91C902.08 154.89 918.48 155.31 926.08 154.81C928.4 154.65 930.86 154.4 933.11 153.9C936.15 153.26 938.93 152.07 941.27 150.17C942.5 149.19 943.66 148.13 944.74 147.02C946.71 144.97 948.71 142.37 948.97 139.6C949.16 137.59 947.78 135.93 946.19 134.82C943.78 132.93 940.89 132.28 937.84 132.43C936.1 132.46 934.14 132.63 932.47 131.85L931.36 131.33L930.25 130.83C921.36 126.85 912.27 122.93 903.21 119.37C902.16 118.96 901.64 117.77 902.05 116.72C902.48 115.6 903.84 115.1 904.9 115.66Z" fill="black">
          </path>
          <path d="M860.68 80.68C860.68 80.68 884.18 97.39 868.89 118.1C862.97 126.11 858.9 128.09 853.6 131.38C850.41 133.36 843.13 137.38 840 139.46C835.67 142.35 829.05 143.48 823.9 142.19C814.67 139.88 805.06 127.6 810.65 120.4C816.83 112.44 820.09 116.1 815.26 112.94C811.14 110.24 812.72 106.09 817 104.26C821.04 102.53 827.6 110.6 832.79 108.3C837.98 105.99 838.35 102.87 838.35 102.87" fill="white">
          </path>
          <path d="M861.86 79.01C871.02 85.82 878.54 97.36 875.56 109.19C873.39 118 865.64 126.59 857.97 131.15C853.38 134 848.52 136.73 843.9 139.43C842.27 140.35 840.78 141.46 839.1 142.28C834.62 144.41 829.55 145.11 824.68 144.2C821.77 143.65 818.99 142.24 816.66 140.5C811.53 136.64 806.33 129.67 807.99 122.89C808.48 120.89 809.82 119.28 811.18 117.82C812.65 116.22 814.37 114.78 816.37 113.84C816.47 113.79 816.68 113.69 816.76 113.65C816.78 113.64 816.79 113.63 816.73 113.68C816.71 113.7 816.71 113.7 816.66 113.74C816.62 113.78 816.6 113.8 816.54 113.89C816.43 114.06 816.31 114.39 816.36 114.64C816.42 115 816.52 115.11 816.65 115.26C816.69 115.3 816.62 115.23 816.55 115.19C816.4 115.09 816.2 114.97 816 114.86C814.68 114.07 813.33 113.28 812.46 111.96C810.07 108.34 813.01 104.49 816.45 103.06C820.08 101.49 824.63 105.4 828 106.53C829.33 107.02 830.81 107.38 832.03 106.77C832.37 106.61 832.75 106.4 833.08 106.21C834.44 105.39 835.78 104.29 836.28 102.79C836.3 102.7 836.33 102.63 836.33 102.55C836.38 101.72 837.37 100.66 838.58 100.83C839.79 100.96 840.62 102.17 840.33 103.34C839.6 106.11 837.16 108.05 834.75 109.27C832.03 110.78 829.54 110.57 826.85 109.37C824.46 108.35 822.06 106.59 819.68 105.66C818.74 105.34 818.07 105.21 817.36 105.54C814.17 106.91 812.6 109.92 816.15 112.06C816.9 112.58 817.58 112.88 818.33 113.47C818.81 113.86 818.99 114.71 818.58 115.28C818.41 115.53 818.15 115.72 817.95 115.82C816.83 116.36 815.8 116.94 814.85 117.76C813.58 118.82 812.38 120.16 811.43 121.5C810.58 122.76 810.22 124.28 810.34 125.84C810.85 130.79 814.6 135.06 818.56 137.84C821.32 139.75 824.39 140.86 827.76 140.86C831.87 140.91 836.1 139.79 839.45 137.41C843.28 134.95 847.32 132.74 851.17 130.43C852.47 129.65 854.45 128.4 855.75 127.6C860.27 124.88 863.83 121.35 866.95 117.05C867.98 115.68 868.88 114.27 869.63 112.78C875.42 101.68 869.19 90.45 860.44 83.1C860.18 82.89 859.73 82.52 859.49 82.34C857.34 80.69 859.59 77.52 861.86 79.01Z" fill="black">
          </path>
          <path d="M861.58 74.75C861.97 77.47 861.63 80.3 861.13 82.99C860.99 83.65 860.97 84.37 860.88 85.06C860.7 86.55 859.63 87.54 858.34 88.3C857.63 88.69 856.82 89.03 855.71 89.14C855.47 89.16 855.26 89.03 855.17 88.82C854.28 86.65 853.71 84.36 855.19 82.52C855.63 82.04 856.12 81.57 856.48 81.04C857.12 80.1 858.06 78.8 858.71 77.88C859.49 76.82 860.22 75.75 860.96 74.61C861.12 74.37 861.54 74.46 861.58 74.75Z" fill="black">
          </path>
         </symbol>
         <symbol height="512" id="default-user-icon" width="512">
          <path d="M256.271 12c-135.09 0-244.271 109.182-244.271 244.271 0 135.09 109.182 244.271 244.271 244.271 135.09 0 244.271-109.182 244.271-244.271 0-135.09-109.182-244.271-244.271-244.271zm168.399 160.997c-12.954 0-27.758-3.701-40.712-14.804-16.655-12.954-22.206-31.459-18.505-55.516 24.057 18.505 44.413 42.562 59.217 70.321zm-168.399-105.481c25.908 0 49.965 5.552 74.022 14.804-18.505 37.011-53.666 62.918-96.228 75.872-44.413 12.954-90.677 5.552-125.837-16.655 33.31-44.413 86.975-74.022 148.043-74.022zm0 377.51c-103.63 0-188.755-85.125-188.755-188.755 0-29.609 7.402-57.367 18.505-81.424 29.609 18.505 64.769 27.758 99.929 27.758 18.505 0 37.011-1.851 55.516-7.402 35.16-9.253 64.769-27.758 86.975-51.815 5.552 16.655 14.804 31.459 29.609 44.413 18.505 14.804 42.562 22.206 64.769 22.206 3.701 0 9.253 0 12.954-1.851 3.701 14.804 5.552 31.459 5.552 48.114 3.701 103.63-81.424 188.755-185.054 188.755zM256.271 385.809c35.16 0 62.918-27.758 62.918-62.918h-127.687c1.851 33.31 29.609 62.918 64.769 62.918zM147.09 230.364h-33.31v31.459c0 18.505 14.804 31.459 31.459 31.459s31.459-14.804 31.459-31.459-12.954-31.459-29.609-31.459zM333.994 261.823c0 18.505 14.804 31.459 31.459 31.459s31.459-14.804 31.459-31.459v-31.459h-31.459c-16.655 0-31.459 12.954-31.459 31.459z">
          </path>
         </symbol>
         <symbol height="32" id="newsletter-icon" width="32">
          <path d="M20.9076 11.8393L11.352 19.677C11.2131 19.7973 11.1334 19.972 11.1334 20.1558V24.3019C11.1334 24.9108 11.6384 25.49 12.4008 24.9355L13.6559 24.0274M15.5688 21.1337L21.3658 25.1923C21.7379 25.4528 22.2551 25.2487 22.3491 24.8043L26.119 6.97466C26.2187 6.50321 25.7765 6.09743 25.3153 6.23718L4.80261 12.4531C4.2922 12.6078 4.18954 13.2854 4.63123 13.5843L9.23246 16.6983" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2">
          </path>
         </symbol>
         <symbol height="33" id="newsletter-color-icon" width="28">
          <path d="M0.5 32.5V16.501L13.6 9.56575L26.7 16.501V32.5H0.5Z" fill="#FBF6EC" stroke="black">
          </path>
          <path d="M24.8 12.2H2.39999V16.9562L13.6 21L24.8 16.9562V12.2Z" fill="white" stroke="black">
          </path>
          <path clip-rule="evenodd" d="M0.895943 4.52925C1.07778 4.34742 1.37259 4.34742 1.55442 4.52925L4.8468 7.82163C5.02864 8.00347 5.02864 8.29828 4.8468 8.48011C4.66497 8.66194 4.37016 8.66194 4.18833 8.48011L0.895943 5.18773C0.71411 5.00589 0.71411 4.71108 0.895943 4.52925Z" fill="black" fill-rule="evenodd" stroke="black" stroke-linecap="round" stroke-width="0.3">
          </path>
          <path clip-rule="evenodd" d="M26.161 4.67076C26.3428 4.85259 26.3428 5.14741 26.161 5.32924L22.8686 8.62162C22.6868 8.80346 22.392 8.80345 22.2101 8.62162C22.0283 8.43979 22.0283 8.14498 22.2101 7.96314L25.5025 4.67076C25.6844 4.48893 25.9792 4.48893 26.161 4.67076Z" fill="black" fill-rule="evenodd" stroke="black" stroke-linecap="round" stroke-width="0.3">
          </path>
          <path clip-rule="evenodd" d="M13.6027 1C13.8599 1 14.0684 1.20846 14.0684 1.46561L14.0684 6.12175C14.0684 6.3789 13.8599 6.58736 13.6027 6.58736C13.3456 6.58736 13.1371 6.3789 13.1371 6.12175L13.1371 1.46561C13.1371 1.20846 13.3456 1 13.6027 1Z" fill="black" fill-rule="evenodd" stroke="black" stroke-linecap="round" stroke-width="0.3">
          </path>
         </symbol>
         <symbol fill="#E40C78" height="14" id="sparkles-icon" width="14">
          <path d="M13.11 10.023c-1.628 0-2.724-2.3-2.724-3.95 0 1.65-1.096 3.95-2.725 3.95 1.629 0 2.725 2.318 2.725 3.95 0-1.65 1.113-3.95 2.725-3.95zM8.537 6.375C5.994 6.375 4.268 2.66 4.268 0 4.268 2.66 2.54 6.375 0 6.375c2.541 0 4.268 3.715 4.268 6.376 0-2.66 1.726-6.376 4.268-6.376z" fill="#E40C78">
          </path>
         </symbol>
        </defs>
       </svg>
       <div id="js-header-container">
        <header class="header__2w0-b">
         <a class="skipNav__nP6WN" href="#buzz-content">
          Skip To Content
         </a>
         <div class="js-sticky-container bf-sticky-container">
          <div class="mainNavContainer__2iSCa mainNavContainer__19fgC js-main-nav">
           <div class="wrapper__1SCdo">
            <div class="mainNav__1kaRQ">
             <button aria-label="open menu to see more links" class="menuToggle__1pVB_ menuToggle__1re0g" themename="bf_default" type="button">
              <i>
               <svg aria-hidden="true" height="12" viewbox="0 0 16 12" width="16">
                <use xlink:href="#hamburger">
                </use>
               </svg>
              </i>
             </button>
             <div class="logoWrapper__2Uw7a">
              <a class="logoContainer__2-BWv" href="https://www.buzzfeed.com">
               <svg aria-labelledby="js-bfo-logo-title" class="bfo__1VGu2 bfoLogo__8U8zP" role="img" themename="bf_default" viewbox="0 0 315.7 53.2">
                <title id="js-bfo-logo-title">
                 BuzzFeed Homepage
                </title>
                <use xlink:href="#bfo-logo">
                </use>
               </svg>
              </a>
             </div>
             <div class="navIconToggles__1Ye9q">
              <div class="newslettersWrapper__3eAJw">
               <div class="newsletters__XOinl">
                <div>
                 <a class="newslettersLink__1psMk" href="https://www.buzzfeed.com/newsletters?origin=navIcon">
                  <svg aria-label="Newsletters" class="newslettersIcon__mVr13 newslettersIcon__2LvHO" height="20" viewbox="0 0 32 32" width="20">
                   <use xlink:href="#newsletter-icon">
                   </use>
                  </svg>
                 </a>
                </div>
               </div>
              </div>
              <div class="searchWrapper__3k7tl">
               <div class="search__2NNLj">
                <div>
                 <a class="searchLink__BEuOP" href="https://www.buzzfeed.com/search">
                  <svg fill="none" height="14" viewbox="0 0 14 14" width="14" xmlns="http://www.w3.org/2000/svg">
                   <path d="M10.1051 10.125C11.1245 9.13241 11.757 7.74898 11.757 6.21875C11.757 3.19844 9.29299 0.75 6.2535 0.75C3.214 0.75 0.75 3.19844 0.75 6.21875C0.75 9.23906 3.214 11.6875 6.2535 11.6875C7.75305 11.6875 9.11252 11.0916 10.1051 10.125ZM10.1051 10.125L13.25 13.25" stroke="black" stroke-linecap="round" stroke-width="1.5">
                   </path>
                  </svg>
                 </a>
                 <button aria-controls="js-header-search" aria-expanded="false" aria-label="open form to search" class="searchButton__1Cjq6" type="button">
                  <svg fill="none" height="14" viewbox="0 0 14 14" width="14" xmlns="http://www.w3.org/2000/svg">
                   <path d="M10.1051 10.125C11.1245 9.13241 11.757 7.74898 11.757 6.21875C11.757 3.19844 9.29299 0.75 6.2535 0.75C3.214 0.75 0.75 3.19844 0.75 6.21875C0.75 9.23906 3.214 11.6875 6.2535 11.6875C7.75305 11.6875 9.11252 11.0916 10.1051 10.125ZM10.1051 10.125L13.25 13.25" stroke="black" stroke-linecap="round" stroke-width="1.5">
                   </path>
                  </svg>
                 </button>
                </div>
               </div>
              </div>
              <button class="navLoginLink__2G7GX" id="nav-login-btn" type="button">
               Sign In
              </button>
             </div>
             <div class="subNavContainer__2UWL9 isPrimaryNav__o_SGr">
              <nav aria-label="Hot Topics" class="topicNavWrapper__sryE7 wrapper__7JIQ_">
               <div class="bfLogo__1cUlo secondNavLogo__cgJMa">
                <a href="https://www.buzzfeed.com">
                 <svg aria-labelledby="buzzfeed" brand="buzzfeed" role="img" viewbox="0 0 512 512">
                  <title id="buzzfeed">
                   BuzzFeed
                  </title>
                  <use xlink:href="#trending-badge">
                  </use>
                 </svg>
                </a>
               </div>
               <ul class="topicNav__3Ih5Z topicNav__S23Dx">
                <li class="topicNavItem__1GfBa desktopItem__2zT4T">
                 <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/shopping" themename="bf_default">
                  Prime Day
                 </a>
                </li>
                <li class="topicNavItem__1GfBa desktopItem__2zT4T">
                 <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/quizzes" themename="bf_default">
                  Quizzes
                 </a>
                </li>
                <li class="topicNavItem__1GfBa desktopItem__2zT4T">
                 <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/in-the-news" themename="bf_default">
                  Trending News
                 </a>
                </li>
                <li class="topicNavItem__1GfBa desktopItem__2zT4T">
                 <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/celebrity" themename="bf_default">
                  Celebrity
                 </a>
                </li>
                <li class="topicNavItem__1GfBa desktopItem__2zT4T">
                 <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/buzzchat" themename="bf_default">
                  Buzz Chat
                 </a>
                </li>
                <li class="topicNavItem__1GfBa topicNavItemWithLogo__1j1ZJ desktopItem__2zT4T">
                 <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/arcade" themename="bf_default">
                  <svg height="15" viewbox="0 0 13 15" width="13">
                   <path d="M0.115133 8.18548L3.35639 9.99933C3.40739 10.0278 3.4449 10.0717 3.4614 10.1232L4.76398 14.0924C4.83299 14.3025 5.16701 14.3025 5.23602 14.0924L6.5386 10.1232C6.5556 10.0717 6.59311 10.0278 6.64361 9.99933L9.88487 8.18548C10.0384 8.0996 10.0384 7.9004 9.88487 7.81452L6.64361 6.00067C6.59261 5.97221 6.5551 5.92827 6.5386 5.87685L5.23602 1.90764C5.16701 1.69745 4.83299 1.69745 4.76398 1.90764L3.4614 5.87685C3.4444 5.92827 3.40689 5.97221 3.35639 6.00067L0.115133 7.81452C-0.0383778 7.9004 -0.0383778 8.0996 0.115133 8.18548Z" fill="#481F6B">
                   </path>
                   <path d="M9.04605 2.57419L10.3426 3.29973C10.363 3.31112 10.378 3.32869 10.3846 3.34926L10.9056 4.93694C10.9332 5.02102 11.0668 5.02102 11.0944 4.93694L11.6154 3.34926C11.6222 3.32869 11.6372 3.31112 11.6574 3.29973L12.9539 2.57419C13.0154 2.53984 13.0154 2.46016 12.9539 2.42581L11.6574 1.70027C11.637 1.68888 11.622 1.67131 11.6154 1.65074L11.0944 0.0630579C11.0668 -0.0210193 10.9332 -0.0210193 10.9056 0.0630579L10.3846 1.65074C10.3778 1.67131 10.3628 1.68888 10.3426 1.70027L9.04605 2.42581C8.98465 2.46016 8.98465 2.53984 9.04605 2.57419Z" fill="#481F6B">
                   </path>
                   <use xlink:href="#arcade">
                   </use>
                  </svg>
                  Arcade
                 </a>
                </li>
                <li class="topicNavItem__1GfBa mobileItem__3Uyal">
                 <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/shopping" ismobile="" themename="bf_default">
                  Prime Day
                 </a>
                </li>
                <li class="topicNavItem__1GfBa mobileItem__3Uyal">
                 <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/quizzes" ismobile="" themename="bf_default">
                  Quizzes
                 </a>
                </li>
                <li class="topicNavItem__1GfBa mobileItem__3Uyal">
                 <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/arcade" ismobile="" themename="bf_default">
                  Arcade
                 </a>
                </li>
                <li class="topicNavItem__1GfBa mobileItem__3Uyal">
                 <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/celebrity" ismobile="" themename="bf_default">
                  Celebrity
                 </a>
                </li>
                <li class="topicNavItem__1GfBa mobileItem__3Uyal">
                 <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/in-the-news" ismobile="" themename="bf_default">
                  Trending News
                 </a>
                </li>
                <li class="topicNavItem__1GfBa mobileItem__3Uyal">
                 <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/buzzchat" ismobile="" themename="bf_default">
                  Buzz Chat
                 </a>
                </li>
               </ul>
              </nav>
             </div>
            </div>
            <section aria-labelledby="nav-menu-title" aria-modal="true" class="moreNav__3ENzC moreNav__3wavb withDestinations__3set5" id="js-more-nav" role="dialog">
             <div class="moreNavInner__1BIZf">
              <div class="moreNavLabel__2myqk" id="nav-menu-title">
               Browse links
              </div>
              <div class="sectionsSection__CR1Rx">
               <div class="headerSectionContainer__1dPWB">
                <a class="headerLink__306O2 link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/newsletters?origin=nav">
                 <div class="headerSection__t9FBx">
                  <div>
                   <svg aria-hidden="true" class="newsletterIcon__V2uvQ" height="33" viewbox="0 0 28 33" width="28">
                    <use xlink:href="#newsletter-color-icon">
                    </use>
                   </svg>
                  </div>
                  <div class="headerCta__1XoKe">
                   <div class="headerCtaTitle__2ILHr">
                    Newsletters
                   </div>
                   <p>
                    The best of the internet, delivered straight to your inbox!
                   </p>
                  </div>
                  <div>
                   <svg aria-hidden="true" class="rightCaret__3XCOV" viewbox="0 0 38 38">
                    <use xlink:href="#caret-icon">
                    </use>
                   </svg>
                  </div>
                 </div>
                </a>
               </div>
               <nav aria-label="Pop Culture" class="section__3eRH- sectionLinksSection__3Fg2T">
                <div class="sectionTitle__3RT_B text__1i0LC">
                 Pop Culture
                </div>
                <ul class="sectionItems__25qE1">
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/tvandmovies">
                   TV &amp; Movies
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/celebrity">
                   Celebrity
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/bestoftheinternet">
                   Best of the Internet
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/tag/animals">
                   Animals
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/music">
                   Music
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/rewind">
                   Rewind
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/books">
                   Books
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/lgbtq">
                   LGBTQ
                  </a>
                 </li>
                </ul>
               </nav>
               <nav aria-label="Quizzes &amp; Games" class="section__3eRH- sectionLinksSection__3Fg2T">
                <div class="sectionTitle__3RT_B text__1i0LC">
                 <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/quizzes">
                  Quizzes &amp; Games
                 </a>
                </div>
                <ul class="sectionItems__25qE1">
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/arcade">
                   Arcade
                   <span class="navLabel__1qTxb badge__3OvKz">
                    NEW!
                   </span>
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/quizzes">
                   Latest
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/trending/quizzes">
                   Trending
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/quizzes/food">
                   Food
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/quizzes/love">
                   Love
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/quizzes/trivia">
                   Trivia
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/quizparty?utm_source=buzzfeed&amp;utm_campaign=bf_nav_menu">
                   Quiz Party
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/pyramid-scheme?utm_source=buzzfeed&amp;utm_campaign=bf_nav_menu">
                   <svg height="9" viewbox="0 0 114 9" width="114">
                    <symbol height="9" id="pyramid-scheme-logo-2" width="114">
                     <path d="M2.01052 9C3.58666 9 4.03345 8.6087 4.03345 8.06324C4.03345 7.77866 3.90934 7.56522 3.57425 7.31621C3.31363 7.12648 3.10265 6.85376 3.10265 6.43874V5.79842C3.10265 5.66799 3.15229 5.59684 3.25158 5.59684C3.53702 5.59684 3.90934 6.083 4.6788 6.083C6.11843 6.083 7.35949 4.80237 7.35949 3.083C7.35949 1.26877 6.317 0.142293 3.97139 0.142293H1.6382C0.384729 0.142293 0 0.474308 0 0.98419C0 1.2332 0.124106 1.44664 0.40955 1.73123C0.645351 1.96838 0.794279 2.28854 0.794279 3.10672V6.07115C0.794279 6.77075 0.645351 7.09091 0.421961 7.28063C0.148927 7.5415 0 7.79051 0 8.08696C0 8.63241 0.632941 9 2.01052 9ZM3.8597 4.89723C3.46256 4.89723 3.10265 4.70751 3.10265 4.31621V2.00395C3.10265 1.57708 3.25158 1.39921 3.72318 1.39921C4.443 1.39921 4.95183 2.05138 4.95183 3.13044C4.95183 4.28063 4.48023 4.89723 3.8597 4.89723Z" fill="url(#paint0_linear_149_29_2)">
                     </path>
                     <path d="M11.4798 9C12.9318 9 13.4283 8.6087 13.4283 8.0751C13.4283 7.77866 13.2917 7.58893 13.0063 7.29249C12.7953 7.06719 12.634 6.67589 12.634 6.16601V5.35968C13.2421 4.52964 14.2598 3.13044 14.6693 2.54941C14.9051 2.21739 15.1781 1.92095 15.4512 1.73123C15.8111 1.43478 15.9352 1.25692 15.9352 0.960475C15.9352 0.320158 15.2898 0.0237152 14.0736 0.0237152C12.845 0.0237152 12.2492 0.296443 12.2492 0.83004C12.2492 1.11462 12.4106 1.26877 12.5843 1.39921C12.7829 1.55336 12.9691 1.71937 12.9691 1.90909C12.9691 2.12253 12.8822 2.32411 12.6588 2.6087C12.3858 3.03557 12.1003 3.48617 11.7901 3.93676C11.4674 3.47431 11.1323 3 10.8344 2.53755C10.6483 2.3004 10.5366 2.14625 10.5366 1.89723C10.5366 1.69565 10.6607 1.5415 10.822 1.42293C11.033 1.28063 11.1571 1.12648 11.1571 0.865613C11.1571 0.343873 10.6855 0.0237152 9.09695 0.0237152C7.80625 0.0237152 7.08644 0.355731 7.08644 0.901186C7.08644 1.24506 7.18572 1.42292 7.5084 1.66008C7.7442 1.8498 7.89313 2.02767 8.33991 2.64427C8.87356 3.41502 9.82918 4.64822 10.3132 5.39526V6.16601C10.3132 6.67589 10.1643 7.06719 9.95329 7.28063C9.68025 7.55336 9.51892 7.7668 9.51892 8.0751C9.51892 8.6087 10.0278 9 11.4798 9Z" fill="url(#paint1_linear_149_29_2)">
                     </path>
                     <path d="M24.1 5.53755C23.9511 5.53755 23.827 5.63241 23.6656 5.85771C23.4919 6.05929 23.2685 6.23715 23.0203 6.23715C22.648 6.23715 22.4246 5.96443 21.8413 4.80237C22.8838 4.31621 23.7649 3.43874 23.7649 2.32411C23.7649 1.04348 22.6852 0.142293 20.5257 0.142293H17.7458C16.4923 0.142293 16.1076 0.474308 16.1076 0.98419C16.1076 1.2332 16.2317 1.44664 16.5171 1.73123C16.7529 1.96838 16.9018 2.28854 16.9018 3.10672V6.07115C16.9018 6.77075 16.7529 7.09091 16.5295 7.28063C16.2565 7.5415 16.1076 7.79051 16.1076 8.08696C16.1076 8.63241 16.666 9 18.0188 9C19.5453 9 20.0293 8.62055 20.0293 8.08696C20.0293 7.79051 19.868 7.56522 19.6074 7.30435C19.3964 7.07905 19.2102 6.78261 19.2102 6.29644V5.59684C19.2102 5.3004 19.3591 5.1581 19.5329 5.1581C19.7439 5.1581 19.8928 5.28854 20.1162 5.78656C21.1215 7.9921 21.9778 9 22.8341 9C24.0504 9 24.6088 7.67194 24.6088 6.50988C24.6088 5.95257 24.4227 5.53755 24.1 5.53755ZM19.5825 4.25692C19.3467 4.25692 19.2102 4.17391 19.2102 3.93676V1.77866C19.2102 1.50593 19.3219 1.37549 19.8556 1.37549C20.6995 1.37549 21.2828 1.74308 21.2828 2.49012C21.2828 3.5336 20.2899 4.25692 19.5825 4.25692Z" fill="url(#paint2_linear_149_29_2)">
                     </path>
                     <path d="M33.3505 7.29249C33.0154 7.06719 32.8789 6.85375 32.7176 6.34387L30.9677 0.98419C30.7567 0.320158 30.4465 0.142293 29.5777 0.142293H27.5548C26.5868 0.142293 26.2393 0.343873 26.2393 0.87747C26.2393 1.44664 26.5247 1.75494 27.108 1.86166L27.2197 1.88538L25.7056 6.37945C25.5691 6.78261 25.3457 7.09091 24.9361 7.42293C24.5886 7.6838 24.477 7.87352 24.477 8.13439C24.477 8.71542 25.0602 9 26.2641 9C27.5424 9 28.1008 8.71542 28.1008 8.16996C28.1008 7.88538 27.9395 7.71937 27.6665 7.55336C27.4679 7.42293 27.3314 7.29249 27.3314 7.10277C27.3314 6.99605 27.3562 6.88933 27.381 6.81818L27.5424 6.35573C27.6168 6.13044 27.7409 6.03557 28.0264 6.03557H29.7515C30.0493 6.03557 30.1982 6.16601 30.2851 6.40316L30.3844 6.72332C30.4216 6.81818 30.4465 6.94862 30.4465 7.05534C30.4465 7.25692 30.2975 7.43478 30.0865 7.56522C29.7763 7.7668 29.677 7.88538 29.677 8.20553C29.677 8.76285 30.2975 9 31.7868 9C33.2513 9 33.8966 8.71542 33.8966 8.06324C33.8966 7.7668 33.7725 7.57708 33.3505 7.29249ZM28.8455 1.95652L29.8135 4.92095H27.9519L28.8455 1.95652Z" fill="url(#paint3_linear_149_29_2)">
                     </path>
                     <path d="M35.7088 9C37.1857 9 37.6325 8.59684 37.6325 8.06324C37.6325 7.73123 37.4339 7.51779 37.1981 7.28063C36.9747 7.07905 36.8258 6.71146 36.8258 6.05929V3.16601L38.7618 6.80632C38.8735 7.00791 38.9976 7.09091 39.1466 7.09091C39.3451 7.09091 39.4444 6.98419 39.5437 6.80632L41.5418 3.14229V6.01186C41.5418 6.71146 41.3929 7.07905 41.1819 7.28063C40.9337 7.51779 40.7475 7.73123 40.7475 8.0751C40.7475 8.59684 41.2439 9 42.7208 9C44.0239 9 44.5576 8.59684 44.5576 8.0751C44.5576 7.74308 44.4211 7.56522 44.1356 7.28063C43.9246 7.09091 43.7633 6.74704 43.7633 6.01186V2.96443C43.7633 2.35968 43.8874 1.96838 44.1356 1.70751C44.4211 1.42293 44.5824 1.20949 44.5824 0.889328C44.5824 0.533597 44.297 0.142293 43.3413 0.142293H42.5595C41.7652 0.142293 41.3432 0.41502 40.9213 1.20949L39.4568 4.06719L38.0296 1.20949C37.6697 0.486166 37.1857 0.142293 36.379 0.142293H35.2745C34.3685 0.142293 34.021 0.498024 34.021 0.913044C34.021 1.22134 34.1575 1.42293 34.4553 1.70751C34.716 1.95652 34.8277 2.35968 34.8277 2.96443V6.05929C34.8277 6.74704 34.6663 7.07905 34.4678 7.28063C34.1823 7.56522 34.0334 7.74308 34.0334 8.06324C34.0334 8.59684 34.3933 9 35.7088 9Z" fill="url(#paint4_linear_149_29_2)">
                     </path>
                     <path d="M47.0653 9C48.5546 9 49.0262 8.59684 49.0262 8.0751C49.0262 7.77866 48.8896 7.57708 48.5918 7.28063C48.3932 7.09091 48.2195 6.77075 48.2195 5.98814V3.01186C48.2195 2.22925 48.3932 1.86166 48.5918 1.67194C48.8896 1.38735 49.0262 1.17391 49.0262 0.87747C49.0262 0.391304 48.5421 0.0237152 47.0653 0.0237152C45.6008 0.0237152 45.1168 0.391304 45.1168 0.87747C45.1168 1.17391 45.2533 1.38735 45.5388 1.67194C45.7498 1.87352 45.9111 2.22925 45.9111 3.01186V5.98814C45.9111 6.77075 45.7498 7.07905 45.5388 7.28063C45.2533 7.57708 45.1168 7.77866 45.1168 8.0751C45.1168 8.59684 45.6008 9 47.0653 9Z" fill="url(#paint5_linear_149_29_2)">
                     </path>
                     <path d="M53.1453 8.85771C56.4837 8.85771 58.0847 6.96047 58.0847 4.28063C58.0847 1.51779 56.2728 0.142293 53.3315 0.142293H50.8866C50.0302 0.142293 49.571 0.474309 49.571 0.960475C49.571 1.24506 49.7324 1.48221 49.993 1.75494C50.2164 1.96838 50.3653 2.32411 50.3653 3.03557V5.94071C50.3653 6.68775 50.2164 7.00791 49.9806 7.25692C49.7076 7.5415 49.571 7.80237 49.571 8.06324C49.571 8.62055 50.0426 8.85771 50.8617 8.85771H53.1453ZM53.5797 7.57708C52.9715 7.57708 52.6737 7.17391 52.6737 6.43874V2.53755C52.6737 1.79051 52.9095 1.41107 53.5921 1.41107C54.8207 1.41107 55.6026 2.6087 55.6026 4.26877C55.6026 6.35573 54.709 7.57708 53.5797 7.57708Z" fill="url(#paint6_linear_149_29_2)">
                     </path>
                     <path d="M63.0571 9C63.5163 9 63.7893 8.65613 64.2733 8.65613C64.7822 8.65613 65.3655 9 66.1349 9C67.9593 9 69.1755 7.57708 69.1755 6.15415C69.1755 4.89723 68.3936 4.1502 66.2342 3.3913C64.658 2.84585 64.1864 2.49012 64.1864 1.8498C64.1864 1.29249 64.5836 0.901186 65.2662 0.901186C66.0729 0.901186 66.5072 1.47036 67.0533 2.25296C67.438 2.81028 67.7607 3.2253 68.2075 3.2253C68.6294 3.2253 68.8652 2.78656 68.8652 2.11067C68.8652 1.03162 68.3936 0 67.7359 0C67.2891 0 66.954 0.355732 66.6686 0.711463C66.4079 0.41502 65.7998 0 64.6829 0C62.8213 0 61.7912 1.17391 61.7912 2.47826C61.7912 3.74704 62.5607 4.49407 64.6829 5.21739C66.1845 5.71542 66.6437 6.05929 66.6437 6.65217C66.6437 7.38735 66.0977 7.8498 65.3034 7.8498C64.3354 7.8498 63.6652 7.32806 62.9826 6C62.7096 5.47826 62.4738 5.03953 62.0642 5.03953C61.605 5.03953 61.3692 5.64427 61.3692 6.45059C61.3692 7.62451 62.0146 9 63.0571 9Z" fill="url(#paint7_linear_149_29_2)">
                     </path>
                     <path d="M73.7122 9C75.7227 9 77.2988 7.62451 77.2988 6.32016C77.2988 5.89328 77.0506 5.51383 76.7776 5.51383C76.6659 5.51383 76.5542 5.6087 76.4549 5.83399C75.9709 6.78261 75.3255 7.28063 74.4816 7.28063C73.2033 7.28063 72.2477 5.73913 72.2477 3.55731C72.2477 2.0751 72.8558 1.1502 73.6749 1.1502C74.283 1.1502 74.705 1.51779 75.2635 2.50198C75.7847 3.45059 76.0702 3.73518 76.4549 3.73518C76.9389 3.73518 77.274 3.17787 77.274 2.25296C77.274 0.98419 76.7652 0 76.0826 0C75.7227 0 75.3628 0.26087 74.916 0.901186C74.5188 0.308301 73.9107 0 73.1288 0C71.2052 0 69.7532 1.86166 69.7532 4.37549C69.7532 7.11462 71.3665 9 73.7122 9Z" fill="url(#paint8_linear_149_29_2)">
                     </path>
                     <path d="M85.9628 3.01186C85.9628 2.22925 86.1241 1.86166 86.3351 1.67194C86.6205 1.38735 86.7694 1.17391 86.7694 0.87747C86.7694 0.391304 86.273 0.0237152 84.8086 0.0237152C83.3317 0.0237152 82.8477 0.391304 82.8477 0.87747C82.8477 1.17391 82.9842 1.38735 83.2821 1.67194C83.4806 1.87352 83.6544 2.22925 83.6544 3.01186V3.78261H80.8248V3.01186C80.8248 2.22925 80.9985 1.86166 81.1971 1.67194C81.4949 1.38735 81.6315 1.17391 81.6315 0.87747C81.6315 0.391304 81.1474 0.0237152 79.6706 0.0237152C78.2061 0.0237152 77.7221 0.391304 77.7221 0.87747C77.7221 1.17391 77.8586 1.38735 78.1441 1.67194C78.355 1.87352 78.5164 2.22925 78.5164 3.01186V5.98814C78.5164 6.77075 78.355 7.07905 78.1441 7.28063C77.8586 7.57708 77.7221 7.77866 77.7221 8.0751C77.7221 8.59684 78.2061 9 79.6706 9C81.1474 9 81.6315 8.59684 81.6315 8.0751C81.6315 7.77866 81.4949 7.57708 81.1971 7.28063C80.9985 7.09091 80.8248 6.77075 80.8248 5.98814V4.99209H83.6544V5.98814C83.6544 6.77075 83.4806 7.07905 83.2821 7.28063C82.9842 7.57708 82.8477 7.77866 82.8477 8.0751C82.8477 8.59684 83.3317 9 84.8086 9C86.273 9 86.7694 8.59684 86.7694 8.0751C86.7694 7.77866 86.6205 7.57708 86.3351 7.28063C86.1241 7.09091 85.9628 6.77075 85.9628 5.98814V3.01186Z" fill="url(#paint9_linear_149_29_2)">
                     </path>
                     <path d="M93.5083 8.85771C94.3274 8.85771 94.9231 7.8498 94.9231 6.48617C94.9231 5.62055 94.6749 5.03953 94.2902 5.03953C94.0792 5.03953 93.9054 5.21739 93.5083 5.88142C92.9374 6.98419 92.4658 7.42293 91.7956 7.42293H91.3613C90.6911 7.42293 90.4056 7.03162 90.4056 6.37945V5.76285C90.4056 5.27668 90.629 4.95652 90.9517 4.95652H91.3488C91.6095 4.95652 91.7708 5.1581 91.8825 5.47826C91.9818 5.70356 92.0687 5.86957 92.2796 5.86957C92.6395 5.86957 92.925 5.20553 92.925 4.33992C92.925 3.5336 92.6768 2.97629 92.3169 2.97629C92.0562 2.97629 91.9445 3.17787 91.8328 3.3913C91.7212 3.65217 91.5846 3.80632 91.3488 3.80632H90.9641C90.6166 3.80632 90.4056 3.58103 90.4056 3.02372V2.62055C90.4056 1.95652 90.629 1.57708 91.2371 1.57708H91.6839C92.4782 1.57708 93.0243 2.31225 93.471 3.15415C93.6572 3.50988 93.7689 3.6166 93.9178 3.6166C94.2405 3.6166 94.5135 2.917 94.5135 2.06324C94.5135 1.09091 94.0668 0.142293 92.9746 0.142293H88.5813C87.7746 0.142293 87.303 0.474309 87.303 0.960475C87.303 1.2332 87.4395 1.4585 87.7249 1.75494C87.9235 1.95652 88.0973 2.25296 88.0973 3.10672V5.89328C88.0973 6.74704 87.9111 7.05534 87.7125 7.25692C87.4519 7.51779 87.303 7.79051 87.303 8.06324C87.303 8.52569 87.6877 8.85771 88.482 8.85771H93.5083Z" fill="url(#paint10_linear_149_29_2)">
                     </path>
                     <path d="M96.9719 9C98.4488 9 98.8955 8.59684 98.8955 8.06324C98.8955 7.73123 98.697 7.51779 98.4612 7.28063C98.2378 7.07905 98.0888 6.71146 98.0888 6.05929V3.16601L100.025 6.80632C100.137 7.00791 100.261 7.09091 100.41 7.09091C100.608 7.09091 100.707 6.98419 100.807 6.80632L102.805 3.14229V6.01186C102.805 6.71146 102.656 7.07905 102.445 7.28063C102.197 7.51779 102.011 7.73123 102.011 8.0751C102.011 8.59684 102.507 9 103.984 9C105.287 9 105.821 8.59684 105.821 8.0751C105.821 7.74308 105.684 7.56522 105.399 7.28063C105.188 7.09091 105.026 6.74704 105.026 6.01186V2.96443C105.026 2.35968 105.15 1.96838 105.399 1.70751C105.684 1.42293 105.845 1.20949 105.845 0.889328C105.845 0.533597 105.56 0.142293 104.604 0.142293H103.823C103.028 0.142293 102.606 0.41502 102.184 1.20949L100.72 4.06719L99.2927 1.20949C98.9328 0.486166 98.4488 0.142293 97.6421 0.142293H96.5375C95.6315 0.142293 95.284 0.498024 95.284 0.913044C95.284 1.22134 95.4206 1.42293 95.7184 1.70751C95.979 1.95652 96.0907 2.35968 96.0907 2.96443V6.05929C96.0907 6.74704 95.9294 7.07905 95.7308 7.28063C95.4454 7.56522 95.2965 7.74308 95.2965 8.06324C95.2965 8.59684 95.6564 9 96.9719 9Z" fill="url(#paint11_linear_149_29_2)">
                     </path>
                     <path d="M112.585 8.85771C113.404 8.85771 114 7.8498 114 6.48617C114 5.62055 113.752 5.03953 113.367 5.03953C113.156 5.03953 112.982 5.21739 112.585 5.88142C112.014 6.98419 111.543 7.42293 110.873 7.42293H110.438C109.768 7.42293 109.483 7.03162 109.483 6.37945V5.76285C109.483 5.27668 109.706 4.95652 110.029 4.95652H110.426C110.686 4.95652 110.848 5.1581 110.959 5.47826C111.059 5.70356 111.146 5.86957 111.357 5.86957C111.716 5.86957 112.002 5.20553 112.002 4.33992C112.002 3.5336 111.754 2.97629 111.394 2.97629C111.133 2.97629 111.021 3.17787 110.91 3.3913C110.798 3.65217 110.662 3.80632 110.426 3.80632H110.041C109.694 3.80632 109.483 3.58103 109.483 3.02372V2.62055C109.483 1.95652 109.706 1.57708 110.314 1.57708H110.761C111.555 1.57708 112.101 2.31225 112.548 3.15415C112.734 3.50988 112.846 3.6166 112.995 3.6166C113.317 3.6166 113.59 2.917 113.59 2.06324C113.59 1.09091 113.144 0.142293 112.052 0.142293H107.658C106.851 0.142293 106.38 0.474309 106.38 0.960475C106.38 1.2332 106.516 1.4585 106.802 1.75494C107 1.95652 107.174 2.25296 107.174 3.10672V5.89328C107.174 6.74704 106.988 7.05534 106.789 7.25692C106.529 7.51779 106.38 7.79051 106.38 8.06324C106.38 8.52569 106.765 8.85771 107.559 8.85771H112.585Z" fill="url(#paint12_linear_149_29_2)">
                     </path>
                     <defs>
                      <lineargradient gradientunits="userSpaceOnUse" id="paint0_linear_149_29_2" x1="115.793" x2="0" y1="4.5" y2="4.5">
                       <stop stop-color="#EE448B">
                       </stop>
                       <stop offset="1" stop-color="#8541DB">
                       </stop>
                      </lineargradient>
                      <lineargradient gradientunits="userSpaceOnUse" id="paint1_linear_149_29_2" x1="115.793" x2="0" y1="4.5" y2="4.5">
                       <stop stop-color="#EE448B">
                       </stop>
                       <stop offset="1" stop-color="#8541DB">
                       </stop>
                      </lineargradient>
                      <lineargradient gradientunits="userSpaceOnUse" id="paint2_linear_149_29_2" x1="115.793" x2="0" y1="4.5" y2="4.5">
                       <stop stop-color="#EE448B">
                       </stop>
                       <stop offset="1" stop-color="#8541DB">
                       </stop>
                      </lineargradient>
                      <lineargradient gradientunits="userSpaceOnUse" id="paint3_linear_149_29_2" x1="115.793" x2="0" y1="4.5" y2="4.5">
                       <stop stop-color="#EE448B">
                       </stop>
                       <stop offset="1" stop-color="#8541DB">
                       </stop>
                      </lineargradient>
                      <lineargradient gradientunits="userSpaceOnUse" id="paint4_linear_149_29_2" x1="115.793" x2="0" y1="4.5" y2="4.5">
                       <stop stop-color="#EE448B">
                       </stop>
                       <stop offset="1" stop-color="#8541DB">
                       </stop>
                      </lineargradient>
                      <lineargradient gradientunits="userSpaceOnUse" id="paint5_linear_149_29_2" x1="115.793" x2="0" y1="4.5" y2="4.5">
                       <stop stop-color="#EE448B">
                       </stop>
                       <stop offset="1" stop-color="#8541DB">
                       </stop>
                      </lineargradient>
                      <lineargradient gradientunits="userSpaceOnUse" id="paint6_linear_149_29_2" x1="115.793" x2="0" y1="4.5" y2="4.5">
                       <stop stop-color="#EE448B">
                       </stop>
                       <stop offset="1" stop-color="#8541DB">
                       </stop>
                      </lineargradient>
                      <lineargradient gradientunits="userSpaceOnUse" id="paint7_linear_149_29_2" x1="115.793" x2="0" y1="4.5" y2="4.5">
                       <stop stop-color="#EE448B">
                       </stop>
                       <stop offset="1" stop-color="#8541DB">
                       </stop>
                      </lineargradient>
                      <lineargradient gradientunits="userSpaceOnUse" id="paint8_linear_149_29_2" x1="115.793" x2="0" y1="4.5" y2="4.5">
                       <stop stop-color="#EE448B">
                       </stop>
                       <stop offset="1" stop-color="#8541DB">
                       </stop>
                      </lineargradient>
                      <lineargradient gradientunits="userSpaceOnUse" id="paint9_linear_149_29_2" x1="115.793" x2="0" y1="4.5" y2="4.5">
                       <stop stop-color="#EE448B">
                       </stop>
                       <stop offset="1" stop-color="#8541DB">
                       </stop>
                      </lineargradient>
                      <lineargradient gradientunits="userSpaceOnUse" id="paint10_linear_149_29_2" x1="115.793" x2="0" y1="4.5" y2="4.5">
                       <stop stop-color="#EE448B">
                       </stop>
                       <stop offset="1" stop-color="#8541DB">
                       </stop>
                      </lineargradient>
                      <lineargradient gradientunits="userSpaceOnUse" id="paint11_linear_149_29_2" x1="115.793" x2="0" y1="4.5" y2="4.5">
                       <stop stop-color="#EE448B">
                       </stop>
                       <stop offset="1" stop-color="#8541DB">
                       </stop>
                      </lineargradient>
                      <lineargradient gradientunits="userSpaceOnUse" id="paint12_linear_149_29_2" x1="115.793" x2="0" y1="4.5" y2="4.5">
                       <stop stop-color="#EE448B">
                       </stop>
                       <stop offset="1" stop-color="#8541DB">
                       </stop>
                      </lineargradient>
                     </defs>
                    </symbol>
                    <use xlink:href="#pyramid-scheme-logo-2">
                    </use>
                   </svg>
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/topic/showdown">
                   Showdown
                  </a>
                 </li>
                </ul>
               </nav>
               <nav aria-label="Shopping" class="section__3eRH- sectionLinksSection__3Fg2T">
                <div class="sectionTitle__3RT_B text__1i0LC">
                 <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/shopping">
                  Shopping
                 </a>
                </div>
                <ul class="sectionItems__25qE1">
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/shopping">
                   Latest
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/shopping/home">
                   Home
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/shopping/fashion">
                   Fashion
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/shopping/tech">
                   Tech
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/shopping/beauty-personal-care">
                   Beauty &amp; Personal Care
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/pets">
                   Pets
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/shopping/sports-fitness">
                   Sports &amp; Fitness
                  </a>
                 </li>
                </ul>
               </nav>
               <nav aria-label="Video" class="section__3eRH- sectionLinksSection__3Fg2T">
                <div class="sectionTitle__3RT_B text__1i0LC">
                 <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/videos">
                  Video
                 </a>
                </div>
                <ul class="sectionItems__25qE1">
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/videos">
                   Latest
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/cocoabutter">
                   Cocoa Butter
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/mominprogress">
                   Mom In Progress
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/worthit">
                   Worth It
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.youtube.com/@BuzzFeedUnsolvedNetwork">
                   Unsolved
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/multiplayerbybuzzfeed">
                   Multiplayer
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/perolike">
                   Pero Like
                  </a>
                 </li>
                </ul>
               </nav>
               <nav aria-label="Lifestyle" class="section__3eRH- sectionLinksSection__3Fg2T">
                <div class="sectionTitle__3RT_B text__1i0LC">
                 Lifestyle
                </div>
                <ul class="sectionItems__25qE1">
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/tasty">
                   Tasty
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/asis">
                   Style
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/travel">
                   Travel
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/goodful">
                   Goodful: Wellness
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/nifty">
                   Nifty: DIY
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/parents">
                   Parents
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/food">
                   Food
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/weddings">
                   Weddings
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/sex-and-love">
                   Sex And Love
                  </a>
                 </li>
                </ul>
               </nav>
               <nav aria-label="Community" class="section__3eRH- sectionLinksSection__3Fg2T">
                <div class="sectionTitle__3RT_B text__1i0LC">
                 <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/community">
                  Community
                 </a>
                </div>
                <ul class="sectionItems__25qE1">
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/community/contribute">
                   Make a Quiz or Post!
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/community">
                   Featured Posts
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/community/leaderboard">
                   Leaderboard
                  </a>
                 </li>
                </ul>
               </nav>
               <nav aria-label="From Our Partners" class="section__3eRH- sectionLinksSection__3Fg2T">
                <div class="sectionTitle__3RT_B text__1i0LC">
                 From Our Partners
                </div>
                <ul class="sectionItems__25qE1">
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/badge/safe-haven">
                   Safe Haven
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/badge/back-to-school-prep">
                   Back to School Prep
                  </a>
                 </li>
                </ul>
               </nav>
               <nav aria-label="About" class="section__3eRH- sectionLinksSection__3Fg2T">
                <div class="sectionTitle__3RT_B text__1i0LC">
                 About
                </div>
                <ul class="sectionItems__25qE1">
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/about">
                   About Us
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://advertise.buzzfeed.com">
                   Advertise
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://investors.buzzfeed.com">
                   Investor Relations
                  </a>
                 </li>
                 <li>
                  <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/about/jobs">
                   Jobs
                  </a>
                 </li>
                </ul>
               </nav>
               <div class="section__3eRH- footerSectionContainer__2uG21">
                <div class="footerSection__hDmJa">
                 <div class="footerSubSection__2k2Zl">
                  <div class="editionSelect__2jZs3">
                   <label class="text__1i0LC" for="js-header-edition-select">
                    Edition
                   </label>
                   <select class="select__2FQi5" id="js-header-edition-select">
                    <option data-bfa="@a:Main-Nav;@d:US;" selected="" value="us">
                     US
                    </option>
                    <option data-bfa="@a:Main-Nav;@d:UK;" value="uk">
                     UK
                    </option>
                    <option data-bfa="@a:Main-Nav;@d:Australia;" value="au">
                     Australia
                    </option>
                    <option data-bfa="@a:Main-Nav;@d:Canada;" value="ca">
                     Canada
                    </option>
                    <option data-bfa="@a:Main-Nav;@d:Deutschland;" value="de">
                     Deutschland
                    </option>
                    <option data-bfa="@a:Main-Nav;@d:India;" value="in">
                     India
                    </option>
                    <option data-bfa="@a:Main-Nav;@d:Japan;" value="ja-jp">
                     Japan
                    </option>
                    <option data-bfa="@a:Main-Nav;@d:Latam;" value="mx">
                     Latam
                    </option>
                   </select>
                  </div>
                 </div>
                 <div class="CCPAConsentModule__3gLTb" id="CCPAModule">
                  <div class="ccpaCopy__3fMe- text__1i0LC">
                   US residents can opt out of "sales" of personal data.
                  </div>
                  <button class="ot-sdk-show-settings smallSecondaryButton__2VTNl" id="ot-sdk-btn" type="button">
                   Do Not Sell or Share My Personal Information
                  </button>
                 </div>
                 <div class="GDPRConsentModule__3QRDK" id="GDPRModule">
                  <button class="ot-gdpr-btn smallSecondaryButton__2VTNl" type="button">
                   Privacy Settings
                  </button>
                 </div>
                 <div class="IDNMLConsentModule__vi58S" id="IDNMLModule">
                  <button class="idnml-sp-btn smallSecondaryButton__2VTNl" type="button">
                   Privacy Settings
                  </button>
                 </div>
                </div>
                <div class="footerNav__14aBz">
                 <ul>
                  <li class="copyright__3l_gL text__1i0LC">
                   © 2025 BuzzFeed, Inc
                  </li>
                  <li>
                   <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/press">
                    Press
                   </a>
                  </li>
                  <li>
                   <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/rss">
                    RSS
                   </a>
                  </li>
                  <li>
                   <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/about/privacy">
                    Privacy
                   </a>
                  </li>
                  <li>
                   <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/consent-preferences">
                    Consent Preferences
                   </a>
                  </li>
                  <li>
                   <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/about/useragreement">
                    User Terms
                   </a>
                  </li>
                  <li>
                   <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/about/accessibility">
                    Accessibility Statement
                   </a>
                  </li>
                  <li>
                   <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/about/privacy#adchoices">
                    Ad Choices
                   </a>
                  </li>
                  <li>
                   <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/help">
                    Help
                   </a>
                  </li>
                  <li>
                   <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/about/contact">
                    Contact
                   </a>
                  </li>
                  <li>
                   <a class="link__2Q-2j link__1AOx5" href="https://www.buzzfeed.com/archive">
                    Sitemap
                   </a>
                  </li>
                 </ul>
                </div>
               </div>
              </div>
              <nav aria-label="Browse Brands" class="section__3eRH- destinationsSection__2Jh0F">
               <ul class="sectionItems__25qE1">
                <li class="destinationItem__3l9i4">
                 <a class="destinationLink__3Y1ie" href="https://www.tasty.co">
                  <div>
                   <i class="tastyLogo__UPbsS">
                    <svg aria-labelledby="js-destination-item-tasty-more" height="18" role="img" viewbox="0 0 32 18" width="32">
                     <title id="js-destination-item-tasty-more">
                      BuzzFeed Tasty
                     </title>
                     <use xlink:href="#tasty-logo">
                     </use>
                    </svg>
                   </i>
                  </div>
                  <span>
                   Search, watch, and cook every single Tasty recipe and video ever — all in one place!
                  </span>
                 </a>
                </li>
                <li class="destinationItem__3l9i4">
                 <a class="destinationLink__3Y1ie" href="https://www.buzzfeed.com/goodful">
                  <div>
                   <i class="goodfulLogo__2B4fl">
                    <svg aria-labelledby="js-destination-item-goodful-more" height="18" role="img" viewbox="0 0 48 18" width="48">
                     <title id="js-destination-item-goodful-more">
                      BuzzFeed Goodful
                     </title>
                     <use xlink:href="#goodful-logo">
                     </use>
                    </svg>
                   </i>
                  </div>
                  <span>
                   Self care and ideas to help you live a healthier, happier life.
                  </span>
                 </a>
                </li>
                <li class="destinationItem__3l9i4">
                 <a class="destinationLink__3Y1ie" href="https://www.huffpost.com/?referrer=bf">
                  <div>
                   <i class="huffpostLogo__1tb1I">
                    <svg aria-labelledby="js-destination-item-huffpost-more" height="45" role="img" viewbox="0 0 386 45" width="386">
                     <title id="js-destination-item-huffpost-more">
                      HuffPost
                     </title>
                     <use xlink:href="#huffpost-logo">
                     </use>
                    </svg>
                   </i>
                  </div>
                  <span>
                   News, Politics, Culture, Life, Entertainment, and more. Stories that matter to you.
                  </span>
                 </a>
                </li>
               </ul>
              </nav>
             </div>
            </section>
           </div>
           <div class="js-main-nav-flyout">
           </div>
          </div>
         </div>
         <div class="secondSubNavContainer__vYr9W subNavContainer__2UWL9 isSecondNav__GUhoI">
          <nav aria-label="Hot Topics" class="topicNavWrapper__sryE7 wrapper__7JIQ_">
           <div class="bfLogo__1cUlo secondNavLogo__cgJMa">
            <a href="https://www.buzzfeed.com">
             <svg aria-labelledby="buzzfeed" brand="buzzfeed" role="img" viewbox="0 0 512 512">
              <title id="buzzfeed">
               BuzzFeed
              </title>
              <use xlink:href="#trending-badge">
              </use>
             </svg>
            </a>
           </div>
           <ul class="topicNav__3Ih5Z topicNav__S23Dx">
            <li class="topicNavItem__1GfBa desktopItem__2zT4T">
             <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/shopping" themename="bf_default">
              Prime Day
             </a>
            </li>
            <li class="topicNavItem__1GfBa desktopItem__2zT4T">
             <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/quizzes" themename="bf_default">
              Quizzes
             </a>
            </li>
            <li class="topicNavItem__1GfBa desktopItem__2zT4T">
             <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/in-the-news" themename="bf_default">
              Trending News
             </a>
            </li>
            <li class="topicNavItem__1GfBa desktopItem__2zT4T">
             <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/celebrity" themename="bf_default">
              Celebrity
             </a>
            </li>
            <li class="topicNavItem__1GfBa desktopItem__2zT4T">
             <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/buzzchat" themename="bf_default">
              Buzz Chat
             </a>
            </li>
            <li class="topicNavItem__1GfBa topicNavItemWithLogo__1j1ZJ desktopItem__2zT4T">
             <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/arcade" themename="bf_default">
              <svg height="15" viewbox="0 0 13 15" width="13">
               <path d="M0.115133 8.18548L3.35639 9.99933C3.40739 10.0278 3.4449 10.0717 3.4614 10.1232L4.76398 14.0924C4.83299 14.3025 5.16701 14.3025 5.23602 14.0924L6.5386 10.1232C6.5556 10.0717 6.59311 10.0278 6.64361 9.99933L9.88487 8.18548C10.0384 8.0996 10.0384 7.9004 9.88487 7.81452L6.64361 6.00067C6.59261 5.97221 6.5551 5.92827 6.5386 5.87685L5.23602 1.90764C5.16701 1.69745 4.83299 1.69745 4.76398 1.90764L3.4614 5.87685C3.4444 5.92827 3.40689 5.97221 3.35639 6.00067L0.115133 7.81452C-0.0383778 7.9004 -0.0383778 8.0996 0.115133 8.18548Z" fill="#481F6B">
               </path>
               <path d="M9.04605 2.57419L10.3426 3.29973C10.363 3.31112 10.378 3.32869 10.3846 3.34926L10.9056 4.93694C10.9332 5.02102 11.0668 5.02102 11.0944 4.93694L11.6154 3.34926C11.6222 3.32869 11.6372 3.31112 11.6574 3.29973L12.9539 2.57419C13.0154 2.53984 13.0154 2.46016 12.9539 2.42581L11.6574 1.70027C11.637 1.68888 11.622 1.67131 11.6154 1.65074L11.0944 0.0630579C11.0668 -0.0210193 10.9332 -0.0210193 10.9056 0.0630579L10.3846 1.65074C10.3778 1.67131 10.3628 1.68888 10.3426 1.70027L9.04605 2.42581C8.98465 2.46016 8.98465 2.53984 9.04605 2.57419Z" fill="#481F6B">
               </path>
               <use xlink:href="#arcade">
               </use>
              </svg>
              Arcade
             </a>
            </li>
            <li class="topicNavItem__1GfBa mobileItem__3Uyal">
             <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/shopping" ismobile="" themename="bf_default">
              Prime Day
             </a>
            </li>
            <li class="topicNavItem__1GfBa mobileItem__3Uyal">
             <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/quizzes" ismobile="" themename="bf_default">
              Quizzes
             </a>
            </li>
            <li class="topicNavItem__1GfBa mobileItem__3Uyal">
             <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/arcade" ismobile="" themename="bf_default">
              Arcade
             </a>
            </li>
            <li class="topicNavItem__1GfBa mobileItem__3Uyal">
             <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/celebrity" ismobile="" themename="bf_default">
              Celebrity
             </a>
            </li>
            <li class="topicNavItem__1GfBa mobileItem__3Uyal">
             <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/in-the-news" ismobile="" themename="bf_default">
              Trending News
             </a>
            </li>
            <li class="topicNavItem__1GfBa mobileItem__3Uyal">
             <a class="link__32D57 link__1AOx5" href="https://www.buzzfeed.com/buzzchat" ismobile="" themename="bf_default">
              Buzz Chat
             </a>
            </li>
           </ul>
          </nav>
         </div>
        </header>
       </div>
       <script>
        window.BZFD = window.BZFD || {};
      window.BZFD.Config = window.BZFD.Config || {};
      window.BZFD.Config.bfwInfoCookie = 'bf2-b_info';
      window.BZFD.__HEADER_STATE__ = {"edition":"us","config":{"abeagle_url":"https://abeagle-public.buzzfeed.com","bf_url":"https://www.buzzfeed.com","brand_urls":{"default":{"asis":"https://www.buzzfeed.com/asis","bfo":"https://www.buzzfeed.com","cocoabutter":"https://www.buzzfeed.com/cocoabutter","perolike":"https://www.buzzfeed.com/perolike","goodful":"https://www.buzzfeed.com/goodful","news":"https://www.buzzfeed.com/news","nifty":"https://www.buzzfeed.com/nifty","adobe":"https://www.buzzfeed.com/makethefeed","lgbtq":"https://www.buzzfeed.com/lgbtq","work-and-money":"https://www.buzzfeed.com/work-and-money"},"jp":{"asis":"https://www.buzzfeed.com/asis","bfo":"https://www.buzzfeed.com","cocoabutter":"https://www.buzzfeed.com/cocoabutter","perolike":"https://www.buzzfeed.com/perolike","goodful":"https://www.buzzfeed.com/goodful","news":"https://www.buzzfeed.com/news","nifty":"https://www.buzzfeed.com/nifty","adobe":"https://www.buzzfeed.com/makethefeed","lgbtq":"https://www.buzzfeed.com/lgbtq","work-and-money":"https://www.buzzfeed.com/work-and-money"},"us":{"asis":"https://www.buzzfeed.com/asis","bfo":"https://www.buzzfeed.com","cocoabutter":"https://www.buzzfeed.com/cocoabutter","perolike":"https://www.buzzfeed.com/perolike","goodful":"https://www.buzzfeed.com/goodful","news":"https://www.buzzfeednews.com","nifty":"https://www.buzzfeed.com/nifty","adobe":"https://www.buzzfeed.com/makethefeed","lgbtq":"https://www.buzzfeed.com/lgbtq","work-and-money":"https://www.buzzfeed.com/work-and-money"},"br":{"asis":"https://www.buzzfeed.com/asis","bfo":"https://www.buzzfeed.com","cocoabutter":"https://www.buzzfeed.com/cocoabutter","perolike":"https://www.buzzfeed.com/perolike","goodful":"https://www.buzzfeed.com/goodful","news":"https://www.buzzfeednews.com","nifty":"https://www.buzzfeed.com/nifty","adobe":"https://www.buzzfeed.com/makethefeed","lgbtq":"https://www.buzzfeed.com/lgbtq","work-and-money":"https://www.buzzfeed.com/work-and-money"},"mx":{"asis":"https://www.buzzfeed.com/asis","bfo":"https://www.buzzfeed.com","cocoabutter":"https://www.buzzfeed.com/cocoabutter","perolike":"https://www.buzzfeed.com/perolike","goodful":"https://www.buzzfeed.com/goodful","news":"https://www.buzzfeednews.com","nifty":"https://www.buzzfeed.com/nifty","adobe":"https://www.buzzfeed.com/makethefeed","lgbtq":"https://www.buzzfeed.com/lgbtq","work-and-money":"https://www.buzzfeed.com/work-and-money"}},"cms_url":"https://cms.buzzfeed.com","community_url":"https://community.buzzfeed.com","contributors_url":"https://contributors.buzzfeed.com","dashbird_url":"https://dashbird.buzzfeed.io","dashbird_community_url":"https://community-dashbird.buzzfeed.com","image_service_url":"https://img.buzzfeed.com/buzzfeed-static","new_post_path":"/post","settings_path":"/settings","membership_path":"/member-center/me?utm_source=buzzfeed&utm_medium=web&utm_campaign=user_menu","ga":"buzzfeed_ga"},"navItems":{"destinations":[{"url":"https://www.tasty.co","name":"tasty","description":"Search, watch, and cook every single Tasty recipe and video ever — all in one place!"},{"url":"/goodful","name":"goodful","description":"Self care and ideas to help you live a healthier, happier life."},{"url":"https://www.huffpost.com/?referrer=bf","name":"huffpost","description":"News, Politics, Culture, Life, Entertainment, and more. Stories that matter to you."}],"topics":[{"url":"/shopping","name":"Prime Day"},{"url":"/quizzes","name":"Quizzes"},{"url":"/in-the-news","name":"Trending News"},{"url":"/celebrity","name":"Celebrity"},{"url":"/buzzchat","name":"Buzz Chat"},{"url":"/arcade","name":"Arcade"},{"url":"/shopping","name":"Prime Day","isMobile":true},{"url":"/quizzes","name":"Quizzes","isMobile":true},{"url":"/arcade","name":"Arcade ","isMobile":true},{"url":"/celebrity","name":"Celebrity ","isMobile":true},{"url":"/in-the-news","name":"Trending News","isMobile":true},{"url":"/buzzchat","name":"Buzz Chat","isMobile":true}],"topics_mobile":[{"url":"/giftguide","name":"Gift Guides"},{"url":"/quizzes","name":"Quizzes"},{"url":"/trending","name":"Trending"}],"badges":[{"name":"lol","url":"/lol"},{"name":"win","url":"/win"},{"name":"trending","url":"/trending"}],"sections":[{"name":"Pop Culture","subSections":[{"name":"TV & Movies","url":"/tvandmovies"},{"name":"Celebrity","url":"/celebrity"},{"name":"Best of the Internet","url":"/bestoftheinternet"},{"name":"Animals","url":"/tag/animals"},{"name":"Music","url":"/music"},{"name":"Rewind","url":"/rewind"},{"name":"Books","url":"/books"},{"name":"LGBTQ","url":"/lgbtq"}]},{"name":"Quizzes & Games","url":"/quizzes","subSections":[{"name":"Arcade","nav_label":"NEW!","nav_label_type":"badge","url":"/arcade"},{"name":"Latest","url":"/quizzes"},{"name":"Trending","url":"/trending/quizzes"},{"name":"Food","url":"/quizzes/food"},{"name":"Love","url":"/quizzes/love"},{"name":"Trivia","url":"/quizzes/trivia"},{"name":"Quiz Party","url":"/quizparty?utm_source=buzzfeed&utm_campaign=bf_nav_menu"},{"name":"Pyramid Scheme","logo":"PyramidScheme","url":"/pyramid-scheme?utm_source=buzzfeed&utm_campaign=bf_nav_menu"},{"name":"Showdown","url":"/topic/showdown"}]},{"name":"Shopping","url":"/shopping","subSections":[{"name":"Latest","url":"/shopping"},{"name":"Home","url":"/shopping/home"},{"name":"Fashion","url":"/shopping/fashion"},{"name":"Tech","url":"/shopping/tech"},{"name":"Beauty & Personal Care","url":"/shopping/beauty-personal-care"},{"name":"Kids","url":"/shopping/kids"},{"name":"Pets","url":"/pets"},{"name":"Sex Toys","url":"/shopping/sex-toys"},{"name":"Sports & Fitness","url":"/shopping/sports-fitness"},{"name":"Stores","url":"/shopping/stores"},{"name":"Subscriptions","url":"/shopping/subscriptions"}]},{"name":"Video","url":"/videos","subSections":[{"name":"Latest","url":"/videos"},{"name":"Cocoa Butter","url":"/cocoabutter"},{"name":"Mom In Progress","url":"/mominprogress"},{"name":"Worth It","url":"/worthit"},{"name":"Unsolved","url":"https://www.youtube.com/@BuzzFeedUnsolvedNetwork"},{"name":"Multiplayer","url":"/multiplayerbybuzzfeed"},{"name":"Pero Like","url":"/perolike"}]},{"name":"Lifestyle","subSections":[{"name":"Tasty","url":"/tasty"},{"name":"Style","url":"/asis"},{"name":"Travel","url":"/travel"},{"name":"Goodful: Wellness","url":"/goodful"},{"name":"Nifty: DIY","url":"/nifty"},{"name":"Parents","url":"/parents"},{"name":"Food","url":"/food"},{"name":"Weddings","url":"/weddings"},{"name":"Sex And Love","url":"/sex-and-love"}]}],"community":[{"name":"Make a Quiz or Post!","url":"/community/contribute"},{"name":"Featured Posts","url":"/community"},{"name":"Leaderboard","url":"/community/leaderboard"}],"about":[{"url":"/about","name":"About Us"},{"url":"https://advertise.buzzfeed.com","name":"Advertise"},{"url":"https://investors.buzzfeed.com","name":"Investor Relations"},{"url":"/about/jobs","name":"Jobs"}],"header":[{"url":"/newsletters?origin=nav","name":"Newsletters","label":"newsletters","description":"The best of the internet, delivered straight to your inbox!"}],"footer":[{"url":"/press","name":"Press"},{"url":"/rss","name":"RSS"},{"url":"/about/privacy","name":"Privacy"},{"url":"/consent-preferences","name":"Consent Preferences"},{"url":"/about/useragreement","name":"User Terms"},{"url":"/about/accessibility","name":"Accessibility Statement"},{"url":"/about/privacy#adchoices","name":"Ad Choices"},{"url":"/help","name":"Help"},{"url":"/about/contact","name":"Contact"},{"url":"/archive","name":"Sitemap"}],"fromPartners":[{"url":"/badge/safe-haven","name":"Safe Haven"},{"url":"/badge/back-to-school-prep","name":"Back to School Prep"}]},"i18n":{"about":"About","edition":"Edition","or":"or","sign_up":"Sign Up","to_post":"to post and comment!","log_in":"Sign In","browse_sections":"Browse Sections","browse_brands":"Browse Brands","view_profile":"View profile","settings":"Settings","new_post":"New Post","my_drafts":"My Drafts","dashboard":"Dashboard","logout":"Log Out","search":"Search BuzzFeed","more_buzzfeed_brands":"more BuzzFeed brands","buzzfeed_badges":"BuzzFeed badges","hot_topics":"Hot Topics","community":"Community","skip_to_content":"Skip To Content","useful_information":"useful information","hamburger":"open menu to see more links","a11y_search":"open form to search","from_partners":"From Our Partners","browse_links":"Browse links"},"gated":false,"theme":"bf_default"};
       </script>
      </div>
     </div>
    </div>
    <main aria-hidden="false" class="post_main__leFIt post_buzzfeed__G3TlM embed-content" id="buzz-content">
     <div class="post_article__U0mpv post_default__QLsRf post_buzzfeed__G3TlM embed-post">
      <div class="post_content__w3pdf">
       <article>
        <div class="headline_headline__cxqaw headline_buzzfeed__inu8L embed-buzz-header">
         <div class="headline_container__AwL7p">
          <ul class="headline-badges_badgeList__aEnF8">
          </ul>
         </div>
         <div class="headline_container__AwL7p embed-breadcrumbs">
          <nav aria-label="Breadcrumb" class="headline_breadcrumbContainer__6q7__">
           <ol>
            <li class="headline-breadcrumb_breadcrumbItem__qbSnI">
             <a class="metadata-link" href="/tvandmovies">
              TV and Movies
             </a>
            </li>
           </ol>
           <span class="bold headline_middot__CfW0u">
            ·
           </span>
          </nav>
          <span class="headline-timestamp_timestamp__i0hoN embed-timestamp">
           <time datetime="2025-07-07T18:31:02.000Z">
            Posted on
            <!-- -->
            <!-- -->
            Jul. 7, 2025
           </time>
          </span>
         </div>
         <div class="subscriptionButton_emailSubscription__tDniy">
          <button aria-controls="expansionContent" aria-expanded="false" class="subscriptionButton_subscribeButton__0LKcO">
           <svg class="subscriptionButton_newslettersIcon__e4V2r" height="32" viewbox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
            <path d="M20.9076 11.8393L11.352 19.677C11.2131 19.7973 11.1334 19.972 11.1334 20.1558V24.3019C11.1334 24.9108 11.6384 25.49 12.4008 24.9355L13.6559 24.0274M15.5688 21.1337L21.3658 25.1923C21.7379 25.4528 22.2551 25.2487 22.3491 24.8043L26.119 6.97466C26.2187 6.50321 25.7765 6.09743 25.3153 6.23718L4.80261 12.4531C4.2922 12.6078 4.18954 13.2854 4.63123 13.5843L9.23246 16.6983" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2">
            </path>
           </svg>
           <span class="subscriptionButton_buttonText__ujsLG">
            Subscribe
            <!-- -->
            <span class="subscriptionButton_topic__A_l1u">
             to
             <!-- -->
             Screen Time
             <!-- -->
             Newsletter
            </span>
           </span>
           <svg class="subscriptionButton_caretUpIcon__r2JZb" viewbox="0 0 38 38" xmlns="http://www.w3.org/2000/svg">
            <title>
             Caret Down
            </title>
            <path d="M19 29.3L2.6 12.9c-.8-.8-.8-2 0-2.8.8-.8 2-.8 2.8 0L19 23.7l13.6-13.6c.8-.8 2-.8 2.8 0 .8.8.8 2 0 2.8L19 29.3z">
            </path>
           </svg>
          </button>
         </div>
         <br/>
         <div class="headline_container__AwL7p embed-headline">
          <h1 class="headline_title__NbsAE embed-headline-title">
           Old People Are Calling Out The Historical Movie And TV Mistakes That Made Them Scream At The Screen, Because They Actually Lived Through That Era
          </h1>
          <p class="headline_description__xYhEI embed-headline-description">
           I guess we shouldn't trust Hollywood for a history lesson.
          </p>
         </div>
         <div class="headline-byline_adaptiveBylineContainer__fvxcl">
          <div class="">
           <a class="headline-byline_headlineByline__qjInt" href="/abbyzinman">
            <div class="headline-byline_bylineContainer__6XyH1">
             <div class="headline-byline_avatar__6_daG embed-byline-avatar">
              <picture>
               <source srcset="https://img.buzzfeed.com/buzzfeed-static/static/user_images/THEnS0Igb_large.jpg?crop=299:299;102,164&amp;downsize=120:*&amp;output-format=auto&amp;output-quality=auto"/>
               <img alt="Abby Zinman" fetchpriority="auto" height="" loading="eager" src="https://img.buzzfeed.com/buzzfeed-static/static/user_images/THEnS0Igb_large.jpg?crop=299:299;102,164&amp;downsize=120:*&amp;output-format=jpg&amp;output-quality=auto" width=""/>
              </picture>
             </div>
             <div class="headline-byline_bylineText__Ujt6n embed-byline-text">
              <span class="headline-byline_text__2w1eR">
               by
              </span>
              <span class="metadata-link headline-byline_bylineName__D9j7i">
               Abby Zinman
              </span>
              <p class="headline-byline_position__PhIVG">
               BuzzFeed Staff
              </p>
             </div>
            </div>
           </a>
          </div>
         </div>
        </div>
        <div class="action-bar_actionBar__3gHbY action-bar_buzzfeed__uteSM embed-action-bar">
         <div class="floating-share-button_floatingShareButton__zoj_a floating-share-button_inactive__IOXiB">
         </div>
         <div class="action-bar_commentsCtaWrapper__5y7Kb">
         </div>
         <div aria-label="Share" class="post_shareTop___30ep" role="group">
          <ul class="share-bar_shareBar__lT55F share-bar_pageLevelOutlineThick__Ye7Rw share-bar_buzzfeed___uZ4R">
           <li>
            <div class="shareButton__Qwddr bluesky__3-Mks outlineThick__1AiFQ share-bar_shareButton__pNsGr share-bar_pageLevelOutlineThick__Ye7Rw share-bar_top_share_bar__kSang">
             <a aria-label="Blueskyでシェアする" class="shareLink__3GGa8" href="https://bsky.app/intent/compose?text=Old%20People%20Are%20Calling%20Out%20The%20Historical%20Movie%20And%20TV%20Mistakes%20That%20Made%20Them%20Scream%20At%20The%20Screen%2C%20Because%20They%20Actually%20Lived%20Through%20That%20Era%0A%0Ahttps%3A%2F%2Fwww.buzzfeed.com%2Fabbyzinman%2Fmovie-mistakes-about-real-events%3Futm_source%3Ddynamic%26utm_campaign%3Dbfsharebluesky">
              <div aria-hidden="true" class="iconContainer__CG3z3">
               <div class="icon__3VYml">
                <svg class="svg__3re89" fill="none" title="Bluesky" viewbox="0 0 600 530" xmlns="http://www.w3.org/2000/svg">
                 <path d="m135.72 44.03c66.496 49.921 138.02 151.14 164.28 205.46 26.262-54.316 97.782-155.54 164.28-205.46 47.98-36.021 125.72-63.892 125.72 24.795 0 17.712-10.155 148.79-16.111 170.07-20.703 73.984-96.144 92.854-163.25 81.433 117.3 19.964 147.14 86.092 82.697 152.22-122.39 125.59-175.91-31.511-189.63-71.766-2.514-7.3797-3.6904-10.832-3.7077-7.8964-0.0174-2.9357-1.1937 0.51669-3.7077 7.8964-13.714 40.255-67.233 197.36-189.63 71.766-64.444-66.128-34.605-132.26 82.697-152.22-67.108 11.421-142.55-7.4491-163.25-81.433-5.9562-21.282-16.111-152.36-16.111-170.07 0-88.687 77.742-60.816 125.72-24.795z" fill="#fff">
                 </path>
                </svg>
               </div>
              </div>
             </a>
            </div>
           </li>
           <li>
            <div class="shareButton__Qwddr facebook__1CNms outlineThick__1AiFQ share-bar_shareButton__pNsGr share-bar_pageLevelOutlineThick__Ye7Rw share-bar_top_share_bar__kSang">
             <a aria-label="Facebookでシェアする" class="shareLink__3GGa8" href="https://www.facebook.com/dialog/share?href=https%3A%2F%2Fwww.buzzfeed.com%2Fabbyzinman%2Fmovie-mistakes-about-real-events%3Futm_source%3Ddynamic%26utm_campaign%3Dbfsharefacebook&amp;app_id=45075597673">
              <div aria-hidden="true" class="iconContainer__CG3z3">
               <div class="icon__3VYml">
                <svg class="svg__3re89" viewbox="0 0 38 38" xmlns="http://www.w3.org/2000/svg">
                 <title>
                  Facebook
                 </title>
                 <path d="M38,19.12A19,19,0,1,0,16,38V24.64H11.21V19.12H16V14.9c0-4.79,2.84-7.43,7.18-7.43a29.21,29.21,0,0,1,4.25.37v4.7H25.07a2.76,2.76,0,0,0-3.1,3v3.59h5.27l-.84,5.52H22V38A19.08,19.08,0,0,0,38,19.12Z">
                 </path>
                </svg>
               </div>
              </div>
             </a>
            </div>
           </li>
           <li>
            <div class="shareButton__Qwddr pinterest__2XzXt outlineThick__1AiFQ share-bar_shareButton__pNsGr share-bar_pageLevelOutlineThick__Ye7Rw share-bar_top_share_bar__kSang">
             <a aria-label="Pinterestでシェアする" class="shareLink__3GGa8" href="https://pinterest.com/pin/create/button/?url=https%3A%2F%2Fwww.buzzfeed.com%2Fabbyzinman%2Fmovie-mistakes-about-real-events%3Futm_source%3Ddynamic%26utm_campaign%3Dbfsharepinterest&amp;description=Old%20People%20Are%20Calling%20Out%20The%20Historical%20Movie%20And%20TV%20Mistakes%20That%20Made%20Them%20Scream%20At%20The%20Screen%2C%20Because%20They%20Actually%20Lived%20Through%20That%20Era&amp;media=https%3A%2F%2Fimg-mkr.buzzfeed.com%2Fbuzzfeed-static%2Fstatic%2F2025-06%2F17%2F20%2Fthumb%2F3-He8cvGR.jpg%3Fcrop%3D1245%3A830%3B3%2C0%26resize%3D1250%3A830%3Ftemplate%3Dpinterest-vertical-halftone-v1%26title%3DT2xkIFBlb3BsZSBBcmUgQ2FsbGluZyBPdXQgVGhlIEhpc3RvcmljYWwgTW92aWUgQW5kIFRWIE1pc3Rha2VzIFRoYXQgTWFkZSBUaGVtIFNjcmVhbSBBdCBUaGUgU2NyZWVuLCBCZWNhdXNlIFRoZXkgQWN0dWFsbHkgTGl2ZWQgVGhyb3VnaCBUaGF0IEVyYQ%3D%3D%26color1%3Dred">
              <div aria-hidden="true" class="iconContainer__CG3z3">
               <div class="icon__3VYml">
                <svg class="svg__3re89" viewbox="0 0 38 38" xmlns="http://www.w3.org/2000/svg">
                 <title>
                  Pinterest
                 </title>
                 <path d="M19 0C8.5 0 0 8.5 0 19c0 7.8 4.7 14.5 11.4 17.4-.1-1.3 0-2.9.3-4.4.4-1.5 2.4-10.4 2.4-10.4s-.6-1.2-.6-3c0-2.8 1.6-4.9 3.7-4.9 1.7 0 2.6 1.3 2.6 2.9 0 1.7-1.1 4.3-1.7 6.7-.5 2 1 3.7 3 3.7 3.6 0 6-4.6 6-10.1 0-4.2-2.8-7.3-7.9-7.3-5.8 0-9.4 4.3-9.4 9.1 0 1.7.5 2.8 1.3 3.7.4.4.4.6.3 1.1-.1.3-.3 1.2-.4 1.5-.1.5-.5.7-1 .5-2.7-1.1-3.9-4-3.9-7.3 0-5.4 4.6-11.9 13.6-11.9 7.3 0 12 5.3 12 10.9 0 7.5-4.1 13-10.3 13-2 0-4-1.1-4.6-2.4L15.5 33c-.4 1.5-1.2 2.9-1.9 4.1 1.7.5 3.5.8 5.4.8 10.5 0 19-8.5 19-19C38 8.5 29.5 0 19 0">
                 </path>
                </svg>
               </div>
              </div>
             </a>
            </div>
           </li>
           <li>
            <div class="shareButton__Qwddr copy__YNF7L outlineThick__1AiFQ share-bar_shareButton__pNsGr share-bar_pageLevelOutlineThick__Ye7Rw share-bar_top_share_bar__kSang">
             <a aria-label="リンクをコピーする" class="shareLink__3GGa8" href="">
              <div aria-hidden="true" class="iconContainer__CG3z3">
               <div class="icon__3VYml">
                <svg class="svg__3re89" viewbox="0 0 38 38" xmlns="http://www.w3.org/2000/svg">
                 <title>
                  Link
                 </title>
                 <path d="M19.7 31.5l-3.8 3.8c-3.8 3.8-10 3.6-13.6-.5-3.3-3.7-2.9-9.4.7-12.9l7.1-7.1c3.1-3 7.8-3.9 11.6-1.8 1 .6 1.9 1.3 2.5 2.1.6.7.5 1.8-.1 2.4l-.2.2c-.8.8-2 .6-2.7-.2-.3-.3-.5-.6-.9-.8-2.2-1.6-5.3-1.3-7.2.7l-7.5 7.5c-2.2 2.2-2.1 5.9.4 8 2.2 1.8 5.4 1.5 7.4-.5l3.6-3.6c.5-.5 1.2-.7 1.8-.5h.1c1.4.3 1.9 2.1.8 3.2zM35.8 3.2C32.2-.9 26-1 22.2 2.7l-3.8 3.8c-1.1 1.1-.6 2.9.9 3.2h.1c.7.2 1.3 0 1.8-.5l3.6-3.6c2-2 5.2-2.3 7.4-.5 2.5 2 2.6 5.8.4 8l-7.5 7.5c-1.9 1.9-5 2.3-7.2.7-.3-.2-.6-.5-.9-.8-.7-.8-1.9-.9-2.7-.2l-.3.2c-.7.7-.7 1.7-.1 2.4.7.8 1.5 1.5 2.5 2.1 3.8 2.1 8.5 1.2 11.6-1.8l7.1-7.1c3.5-3.5 3.9-9.2.7-12.9z">
                 </path>
                </svg>
               </div>
              </div>
             </a>
            </div>
           </li>
          </ul>
         </div>
        </div>
        <div>
         <div class="buzz--list buzz--Canada subbuzzes-wrapper subbuzzes--buzzfeed">
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-0" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856399">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="js-subbuzz__title-text">
               I love any movie that's based on real-life events. Even if it's a loose interpretation, if it has to do with something that actually happened, I am freakin' SEATED.
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856399
  }
             </script>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-1" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856404">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="js-subbuzz__title-text">
               But as a certified Young Person (for now), I haven't really thought about what it feels like to watch those movies as someone who's lived through the particular cultural event being discussed.
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856404
  }
             </script>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-2" data-keywords="fast fashion" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857754">
             </div>
             <script type="text/x-config">
              {
    "id": 138857754,
    "buzz_id": 7894981,
    "index": 2
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="subbuzz__media--full-width-container">
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom">
                <picture>
                 <source height="277" media="(min-width: 52rem)" srcset="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/RfPRXOs6u.png?downsize=700%3A%2A&amp;output-quality=auto&amp;output-format=auto 1600w" width="495"/>
                 <source height="277" srcset="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/RfPRXOs6u.png?downsize=700%3A%2A&amp;output-quality=auto&amp;output-format=auto 800w" width="495"/>
                 <img alt="Person in a white tank top on stage under bright lights, with an energetic pose" class="subbuzz__media-image--static subbuzz-picture js-subbuzz__media js-pinnable" height="277" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/RfPRXOs6u.png?downsize=700%3A%2A&amp;output-quality=auto&amp;output-format=auto" width="495"/>
                </picture>
                <div data-subbuzz-share="138857754">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                20th Century Fox / Via
                <a href="https://giphy.com/gifs/foxhomeent-bohemian-rhapsody-bohemianrhapsody-borhap-fs2TMBMniTY2RDNSpn" target="_blank">
                 giphy.com
                </a>
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-3" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856408">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="js-subbuzz__title-text">
               So recently on Reddit, older people were answering the question: "What movie
               <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/?sort=top" target="_blank">
                got it really wrong
               </a>
               , and you know, because you were there or lived through it?" — and the responses are pretty mind-boggling. Some of them reference specific movies, and some discuss general trends with movies that are completely false:
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856408
  }
             </script>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-4" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856660">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               1.
              </span>
              <span class="js-subbuzz__title-text">
               "
               <i>
                The Help
               </i>
               with
               <a href="https://www.buzzfeed.com/leylamohammed/viola-davis-husband-julius-tennon-reveals-credit-score">
                Viola Davis
               </a>
               . She's one of my favorite actresses, but even
               <a href="https://www.vanityfair.com/hollywood/2018/09/viola-davis-the-help-regret?srsltid=AfmBOorH1RzgExrxud8_kfP27TPLLCQlgdnSNxEKreoaI_LfjpJv1VwN" target="_blank">
                she admits
               </a>
               it was an inaccurate portrayal of the psychological trauma the Black female domestic workers faced in the 1950s and 1960s."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856660
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxya409/" target="_blank">
               patchouliii
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-5" data-keywords="cleaning" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857758">
             </div>
             <script type="text/x-config">
              {
    "id": 138857758,
    "buzz_id": 7894981,
    "index": 5
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="Three women are sitting at a kitchen table engaged in a serious conversation, likely from a period film or show" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/1KEISdXxR.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 66.46%">
                <img alt="Three women are sitting at a kitchen table engaged in a serious conversation, likely from a period film or show" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="66.40" data-mobile-crop="66.46" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/1KEISdXxR.jpg?resize=990:658" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/1KEISdXxR.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857758">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                Walt Disney Co. / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-6" data-keywords="cleaning" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138857848">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               2.
              </span>
              <span class="js-subbuzz__title-text">
               "Essentially most movies depicting the '80s. A great example is
               <a href="https://www.buzzfeed.com/romcomqueen/rom-com-soulmate-quiz">
                <i>
                 13 Going on 30
                </i>
               </a>
               . Really sweet movie, but they got the fashion and music totally wrong. The first part of the film takes place in 1987. 'Jessie’s Girl' was not popular then. It was popular in 1982–1983. 'Burning Down The House' also not popular in '87, it was popular in 1983–1984. The clothes she and her friends wear were long out of style by the time 1987 rolled around. It’s like these movies take an entire decade and cram it all together — what was cool and current in 1984 was not cool and current in 1987!"
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138857848
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/my14i0b/" target="_blank">
               Ok-Trash-8883
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-7" data-keywords="fast fashion" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857760">
             </div>
             <script type="text/x-config">
              {
    "id": 138857760,
    "buzz_id": 7894981,
    "index": 7
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="Person wearing hair rollers applies makeup at a vanity, smiling with a robe on. Cozy room setting in background" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/n-daCyN3r.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 66.67%">
                <img alt="Person wearing hair rollers applies makeup at a vanity, smiling with a robe on. Cozy room setting in background" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="66.72" data-mobile-crop="66.67" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/n-daCyN3r.jpg?resize=990:660" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/n-daCyN3r.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857760">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                Columbia Pictures / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-8" data-keywords="fast fashion" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856673">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               3.
              </span>
              <span class="js-subbuzz__title-text">
               "There were a couple things I think
               <a href="https://www.buzzfeed.com/marycolussi/which-stranger-things-character-are-you">
                <i>
                 Stranger Things
                </i>
               </a>
               got wrong. A) 'Douchebag' was not a common term for kids, even though it was in
               <i>
                ET
               </i>
               . That wasn't popular until the early 2000s out of nowhere. B) The clothes on the show look like the kids can wear them comfortably, and this wasn't the case. Part of the reason IMO you see kids in the '70s–'80s shirtless was this clothes were just so uncomfortable for so many kids. C) There was a huge obsession with beach culture in the late '80s, no matter where you were in the country, and the show hasn't shown that yet, but that could just be because they're saving it for the last season."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856673
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzj9nf/" target="_blank">
               RotrickP
              </a>
             </p>
             <p>
              "The biggest thing I noticed that
              <i>
               Stranger Things
              </i>
              got wrong was that all the kids wore their backpacks on both shoulders. I graduated from high school in 1984 and EVERYONE wore backpacks off one shoulder. Its why we all have herniated cervical discs in our 50s."
             </p>
             <p>
              –
              <a href="https://www.reddit.com/user/crankyweasels/" target="_blank">
               crankyweasels
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-9" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857764">
             </div>
             <script type="text/x-config">
              {
    "id": 138857764,
    "buzz_id": 7894981,
    "index": 9
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="Three young men have a conversation outdoors, appearing focused and engaged" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfXipsXrx.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 50.00%">
                <img alt="Three young men have a conversation outdoors, appearing focused and engaged" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="50.08" data-mobile-crop="50.00" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfXipsXrx.jpg?resize=990:495" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfXipsXrx.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857764">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                Netflix / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-10" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="*********">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               4.
              </span>
              <span class="js-subbuzz__title-text">
               "
               <a href="https://www.buzzfeednews.com/stephaniesoteriou/glen-powell-puked-hidden-figures-performance">
                <i>
                 Hidden Figures
                </i>
               </a>
               . It’s a romanticized, feel-good 'history' from 2016 about some of the women of color heroes of 1960s NASA. While they strived for all sorts of historical accuracy, they made one decision that ruined the entire movie: the characters don’t smoke. Look at any period footage of 1960s NASA control rooms (or anyplace else) and most folks are smoking."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": *********
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzy4ds/" target="_blank">
               etaxif
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-11" data-keywords="fast fashion" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="*********">
             </div>
             <script type="text/x-config">
              {
    "id": *********,
    "buzz_id": 7894981,
    "index": 11
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="A woman in retro office attire raises her hand in a control room with several men in white shirts observing, map of Africa visible behind them" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/QnsXtP0Qv.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 66.77%">
                <img alt="A woman in retro office attire raises her hand in a control room with several men in white shirts observing, map of Africa visible behind them" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="66.72" data-mobile-crop="66.77" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/QnsXtP0Qv.jpg?resize=990:661" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/QnsXtP0Qv.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="*********">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                20th Century Fox / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-12" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856649">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               5.
              </span>
              <span class="js-subbuzz__title-text">
               "Any and every movie about teaching and/or high school."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856649
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyc2xp/" target="_blank">
               Primary-Holiday-5586
              </a>
             </p>
             <p>
              "Especially any time they are giving a lecture or interacting with a class or colleagues. You have to feel that the writers have never actually felt that there might be a benefit to be anywhere near a university.
             </p>
             <p>
              A good example of the horror is Tilda Swinton's research conference presentation in
              <i>
               Three Thousand Years of Longing
              </i>
              , which is otherwise quite enjoyable. She has a crowded room of experts in her field, and speaks to them like children who have never heard of her discipline. That is not what a career in adding to human knowledge looks like."
             </p>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/my07y9z/" target="_blank">
               sruecker01
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-13" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857775">
             </div>
             <script type="text/x-config">
              {
    "id": 138857775,
    "buzz_id": 7894981,
    "index": 13
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="A group of men, including a teacher in a tie and students in casual and formal attire, pose together in a classroom setting" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/_CbiNDp1j.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 67.47%">
                <img alt="A group of men, including a teacher in a tie and students in casual and formal attire, pose together in a classroom setting" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="67.36" data-mobile-crop="67.47" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/_CbiNDp1j.jpg?resize=990:668" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/_CbiNDp1j.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857775">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                Buena Vista Pictures / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-14" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856667">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               6.
              </span>
              <span class="js-subbuzz__title-text">
               "
               <a href="https://www.buzzfeed.com/jeremyhayes/best-sports-films-to-stream">
                <i>
                 Moneyball
                </i>
               </a>
               . Great movie, but it ignores the fact that the A's had three frontline starting pitchers and Miguel Tejada playing shortstop at an MVP level. Makes you think the A's won all those games because Scott Hatteberg could really work the count."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856667
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyp9jr/" target="_blank">
               SituationAshamed707
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-15" data-keywords="fast fashion" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857776">
             </div>
             <script type="text/x-config">
              {
    "id": 138857776,
    "buzz_id": 7894981,
    "index": 15
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="Two actors on a baseball field; one wearing a visor and polo, the other in glasses with a jacket and shirt" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/bXNPb0RYI.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 65.25%">
                <img alt="Two actors on a baseball field; one wearing a visor and polo, the other in glasses with a jacket and shirt" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="65.28" data-mobile-crop="65.25" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/bXNPb0RYI.jpg?resize=990:646" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/bXNPb0RYI.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857776">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                Columbia Pictures / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-16" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856653">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               7.
              </span>
              <span class="js-subbuzz__title-text">
               "Any movie or documentary on Hurricane Katrina that didn't lay the blame squarely on parish/city/state government for the horrible preparedness and/or response to the hurricane. I was at a hospital for five days before being evacuated by Chinook helicopters; I went through six days of preparedness meetings with local government officials prior to the storm hitting land, and not a single government entity showed up to help until the National Guard showed up after five days to get us out."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856653
  }
             </script>
             <p>
              "Our local police had a dozen boats on trailers in their parking lot to aid in search and rescue, but all ended up under 8 feet of water. All of our search and rescue was done by local residents who didn't evacuate."
             </p>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyipcp/" target="_blank">
               Calzonieman
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-17" data-keywords="fast fashion" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857784">
             </div>
             <script type="text/x-config">
              {
    "id": 138857784,
    "buzz_id": 7894981,
    "index": 17
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="A person in sunglasses points towards a broad water channel flanked by stones and concrete barriers. Nearby houses are visible in the background" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/q5FPI_43w.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 56.26%">
                <img alt="A person in sunglasses points towards a broad water channel flanked by stones and concrete barriers. Nearby houses are visible in the background" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="56.32" data-mobile-crop="56.26" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/q5FPI_43w.jpg?resize=990:557" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/q5FPI_43w.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857784">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                O'Malley Creadon Productions /  Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-18" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856661">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               8.
              </span>
              <span class="js-subbuzz__title-text">
               "Every single medical show, especially
               <i>
                Bones
               </i>
               and
               <i>
                Grey’s Anatomy
               </i>
               . The absolute lack of nurses is hilarious. Doctors do not transport patients and rarely personally read radiology films. Most doctors specialize and do very little outside of their specialty. Nurses, ENTs (ear, nose, and throat doctors), respiratory therapists, and radiology techs do the bulk of the work. Also, you do not shock a flat line. The only two shows that are close to accurate are
               <i>
                ER
               </i>
               and
               <i>
                The Pitt
               </i>
               ."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856661
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzpzbx/" target="_blank">
               Quirky_Living8292
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-19" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856656">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               9.
              </span>
              <span class="js-subbuzz__title-text">
               "
               <i>
                Argo
               </i>
               , the movie, gives the US all the credit, but the Canadian government did the hard part."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856656
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyd6oe/" target="_blank">
               mytthewstew
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-20" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857786">
             </div>
             <script type="text/x-config">
              {
    "id": 138857786,
    "buzz_id": 7894981,
    "index": 20
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="Two men in suits stand in an office setting, with a U.S. emblem on the wall, suggesting a formal, governmental environment" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hjT32XlMh.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 66.67%">
                <img alt="Two men in suits stand in an office setting, with a U.S. emblem on the wall, suggesting a formal, governmental environment" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="66.72" data-mobile-crop="66.67" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hjT32XlMh.jpg?resize=990:660" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hjT32XlMh.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857786">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                Warner Bros / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-21" data-keywords="skincare" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856659">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               10.
              </span>
              <span class="js-subbuzz__title-text">
               "Any moving about drilling for oil where oil comes gushing out of the well and everybody is happy. It’s called a blowout, it is very bad, and everything will be on fire in short order."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856659
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/my0f4pz/" target="_blank">
               pokeysyd
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-22" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856662">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               11.
              </span>
              <span class="js-subbuzz__title-text">
               "While it was before my time,
               <i>
                Walking Tall
               </i>
               (1973), the story of Sheriff Buford Pusser, is about events in the town I grew up in. I grew up with grandchildren of his deputies, and kids with last names that were associated with decades of criminal activity. The story/movie is all lies and nothing but lies. That dude was corrupt as anyone."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856662
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzgfu0/" target="_blank">
               Dazzling-Astronaut88
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-23" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857789">
             </div>
             <script type="text/x-config">
              {
    "id": 138857789,
    "buzz_id": 7894981,
    "index": 23
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="A man and woman smile at each other by a white fence. The woman wears a patterned skirt and cardigan. They stand in a leafy yard with a house in the background" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/oGqnef_-Z.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 75.45%">
                <img alt="A man and woman smile at each other by a white fence. The woman wears a patterned skirt and cardigan. They stand in a leafy yard with a house in the background" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="75.52" data-mobile-crop="75.45" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/oGqnef_-Z.jpg?resize=990:747" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/oGqnef_-Z.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857789">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                Bing Crosby Productions / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-24" data-keywords="cleaning" data-module="subbuzz-text" data-retailers="Tide">
             <div class="subbuzz-anchor" id="138856668">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               12.
              </span>
              <span class="js-subbuzz__title-text">
               "Submarine movies, especially
               <i>
                Crimson Tide
               </i>
               . I was serving on the USS Alabama when the movie was made. Talking about all of the inaccuracies would take me a while."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856668
  }
             </script>
             <p>
              "I'll give one example. At the time, I think there were about a dozen Trident submarines based in Bangor, Washington. At any given time, most of them are underway, standing by to launch their missiles if needed.
             </p>
             <p>
              So, if a rebel Russian general took over a missile base and threatened to launch missiles at the US, would you rush the already very short time to get the USS Alabama refitted from its last patrol and spend 12 days cruising across the Pacific, or would you give that task to one of the other subs that is already in position to launch before the Russian general even decided to rebel?"
             </p>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxz0mjt/" target="_blank">
               ghotiermann
              </a>
             </p>
             <p>
              "
              <i>
               Crimson Tide
              </i>
              is set in 1995. During the birthday party, Commander Hunter is watching the news when the newscaster states 'Threatened to use nuclear arms on the United States and Japan...should anyone, including the Russian Army, attempt to move in on him.' The missile being erected is a US Pershing II, which was eliminated in 1991."
             </p>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyabjz/" target="_blank">
               gadget850
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-25" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857795">
             </div>
             <script type="text/x-config">
              {
    "id": 138857795,
    "buzz_id": 7894981,
    "index": 25
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="Group of focused men in a submarine control room, surrounded by equipment and monitors, depicting an intense scene from a TV show or movie" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfrGFIgJL.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 67.37%">
                <img alt="Group of focused men in a submarine control room, surrounded by equipment and monitors, depicting an intense scene from a TV show or movie" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="67.36" data-mobile-crop="67.37" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfrGFIgJL.jpg?resize=990:667" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfrGFIgJL.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857795">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                Buena Vista Pictures / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-26" data-keywords="fast fashion" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856672">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               13.
              </span>
              <span class="js-subbuzz__title-text">
               "
               <i>
                Twister
               </i>
               . You seriously cannot hold onto a pipe secured by a belt and not get sucked into the eye of a tornado."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856672
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzmnhe/" target="_blank">
               Farmwife71
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-27" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856674">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               14.
              </span>
              <span class="js-subbuzz__title-text">
               "
               <i>
                Studio 54
               </i>
               , set in 1970s New York. It was a LOT more out of control than the movie portrayed."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856674
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxze2w5/" target="_blank">
               bevymartbc
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-28" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857798">
             </div>
             <script type="text/x-config">
              {
    "id": 138857798,
    "buzz_id": 7894981,
    "index": 28
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="People dancing in a lively nightclub with vibrant neon lights and a DJ booth. The atmosphere is energetic and bustling" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/-QTG9i6ei.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 56.77%">
                <img alt="People dancing in a lively nightclub with vibrant neon lights and a DJ booth. The atmosphere is energetic and bustling" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="56.64" data-mobile-crop="56.77" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/-QTG9i6ei.jpg?resize=990:562" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/-QTG9i6ei.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857798">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                Zeitgeist Films / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-29" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856665">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               15.
              </span>
              <span class="js-subbuzz__title-text">
               "I am a software developer, I have to check and fix vulnerabilities in code, and also have to deploy software in test environments. Every movie where someone just breaks into a system and puts virus into something is just not possible. Ask people who do penetration testing or hacking: you have to know the flaws, you have to locate them, it takes a while and things are not just waiting for you to upload/download. My favorite has to be the movie
               <i>
                Independence Day
               </i>
               — it’s a fun watch, but yeah, the guy is not going to create a virus, interface into a spaceship, and upload to it."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856665
  }
             </script>
             <p>
              "For historical accuracy, best to read well researched books because a documentary usually has a single point of view. Multiple books give a fuller, and sometimes contradictory picture."
             </p>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzbipg/" target="_blank">
               RayBuc9882
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-30" data-keywords="fast fashion" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857800">
             </div>
             <script type="text/x-config">
              {
    "id": 138857800,
    "buzz_id": 7894981,
    "index": 30
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="Group of focused people in a busy newsroom. Some wear glasses, others are in patterned shirts and suits, all looking intently at something off-camera" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/FfXeBQoi-.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 67.17%">
                <img alt="Group of focused people in a busy newsroom. Some wear glasses, others are in patterned shirts and suits, all looking intently at something off-camera" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="67.20" data-mobile-crop="67.17" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/FfXeBQoi-.jpg?resize=990:665" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/FfXeBQoi-.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857800">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                20th Century Fox / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-31" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856657">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               16.
              </span>
              <span class="js-subbuzz__title-text">
               "All those stupid survivor movies;
               <i>
                127 Hours
               </i>
               ,
               <i>
                Wild
               </i>
               ,
               <i>
                Into the Wild
               </i>
               ...these people were dumb at best and woefully misguided. We used them as horrible examples in real-life search and rescue (SAR) teams, with much jeering."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856657
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyrazc/" target="_blank">
               DaysOfParadise
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-32" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856658">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               17.
              </span>
              <span class="js-subbuzz__title-text">
               "Any scene depicting people with Alzheimers or any form of dementia. I worked in long-term care homes for 17 years."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856658
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/my009zc/" target="_blank">
               SchemeSquare2152
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-33" data-keywords="fast fashion" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857811">
             </div>
             <script type="text/x-config">
              {
    "id": 138857811,
    "buzz_id": 7894981,
    "index": 33
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="Two people in a heated conversation on a porch. The woman wears a button-up dress and holds letters, while the man wears a striped shirt" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/9_8107oJ1.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 66.67%">
                <img alt="Two people in a heated conversation on a porch. The woman wears a button-up dress and holds letters, while the man wears a striped shirt" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="66.72" data-mobile-crop="66.67" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/9_8107oJ1.jpg?resize=990:660" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/9_8107oJ1.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857811">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                New Line Cinema / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-34" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856671">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               18.
              </span>
              <span class="js-subbuzz__title-text">
               "Every single movie made about broadcast news. I was in news for 20 years. It's not like anything you see on TV or in the movies."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856671
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyud4k/" target="_blank">
               notthatcousingreg
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-35" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857815">
             </div>
             <script type="text/x-config">
              {
    "id": 138857815,
    "buzz_id": 7894981,
    "index": 35
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="A man with a mustache is dressed in a brown suit and striped tie, sitting at a news desk with clocks behind him displaying different time zones" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4uOB3ZED.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 66.67%">
                <img alt="A man with a mustache is dressed in a brown suit and striped tie, sitting at a news desk with clocks behind him displaying different time zones" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="66.72" data-mobile-crop="66.67" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4uOB3ZED.jpg?resize=990:660" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4uOB3ZED.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857815">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                DreamWorks / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-36" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856654">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               19.
              </span>
              <span class="js-subbuzz__title-text">
               "Any movie that features a high-speed chase in downtown Boston or any character who finds a parking place quickly in Boston. Although it would be really boring to watch the actors drive around for 25 minutes just to find a place to leave their car, so I get it."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856654
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxygzf4/" target="_blank">
               North_Artichoke_6721
              </a>
             </p>
             <p>
              "There was a movie —
              <i>
               No Way Out
              </i>
              , I think — that put a Metro stop at the Old Post Office Building, which later became Trump's hotel."
             </p>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyuuf6/" target="_blank">
               AndOneForMahler-
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-37" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857813">
             </div>
             <script type="text/x-config">
              {
    "id": 138857813,
    "buzz_id": 7894981,
    "index": 37
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="Two men in a car, one driving and the other in the passenger seat, appear focused. The scene suggests a tense or serious moment from a TV or movie" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/aU---cTpn.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 66.67%">
                <img alt="Two men in a car, one driving and the other in the passenger seat, appear focused. The scene suggests a tense or serious moment from a TV or movie" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="66.72" data-mobile-crop="66.67" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/aU---cTpn.jpg?resize=990:660" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/aU---cTpn.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857813">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                Apple TV+ / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-38" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856664">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               20.
              </span>
              <span class="js-subbuzz__title-text">
               "I watched
               <i>
                Backdraft
               </i>
               with my great uncle, his two sons, and his son in law, who are firemen. Apparently, no actual firemen were consulted for that movie."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856664
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzznqf/" target="_blank">
               Metella76
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-39" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856652">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               21.
              </span>
              <span class="js-subbuzz__title-text">
               "As a New Yorker, seeing big, spacious apartments."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856652
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyg588/" target="_blank">
               Downtown_Share3802
              </a>
             </p>
             <p>
              "Yeah.
              <i>
               Sex and The City
              </i>
              is a LIE."
             </p>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/my02r72/" target="_blank">
               FloridaGirlMary
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-40" data-keywords="fast fashion" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857814">
             </div>
             <script type="text/x-config">
              {
    "id": 138857814,
    "buzz_id": 7894981,
    "index": 40
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="Group of women in stylish outfits, seated and looking attentive, possibly during a media event or panel" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/8iTX9G2TG.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 62.63%">
                <img alt="Group of women in stylish outfits, seated and looking attentive, possibly during a media event or panel" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="62.56" data-mobile-crop="62.63" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/8iTX9G2TG.jpg?resize=990:620" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/8iTX9G2TG.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857814">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                New Line Cinema / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-41" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856677">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               22.
              </span>
              <span class="js-subbuzz__title-text">
               "
               <i>
                Secretariat
               </i>
               . Good movie, but the financial troubles the farm was having in the movie were fantasy."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856677
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzbtt6/" target="_blank">
               Otters64
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-text subbuzz-index-42" data-module="subbuzz-text">
             <div class="subbuzz-anchor" id="138856676">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="subbuzz__number">
               23.
              </span>
              <span class="js-subbuzz__title-text">
               "
               <i>
                Bonfire of the Vanities
               </i>
               . Tom Hanks and Bruce Willis were woefully miscast. The movie was a travesty. The writers, directors and producers owe their cast and the author a big apology."
              </span>
             </h2>
             <script type="text/x-config">
              {
    "id": 138856676
  }
             </script>
             <p>
              —
              <a href="https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyi3l9/" target="_blank">
               KathAlMyPal
              </a>
             </p>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-image subbuzz-index-43" data-module="subbuzz-image">
             <div class="subbuzz-anchor" id="138857821">
             </div>
             <script type="text/x-config">
              {
    "id": 138857821,
    "buzz_id": 7894981,
    "index": 43
    
    
    
  }
             </script>
             <figure class="subbuzz__media">
              <div class="js-full-size-image subbuzz__media--full-width-container">
               <noscript>
                <img alt="Man in a suit stands against a height chart, arms crossed, holding a police ID board for a mugshot" class="img-nojs" src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4WW206rF.jpg">
                </img>
               </noscript>
               <div class="subbuzz__media-container js-subbuzz__media-container subbuzz__media-container--small-margin-bottom js-progressive-image-container" style="padding-top: 66.57%">
                <img alt="Man in a suit stands against a height chart, arms crossed, holding a police ID board for a mugshot" class="subbuzz__media-image subbuzz__media-image--deferred js-subbuzz__media js-progressive-image js-pinnable" data-bfa="@o:{ignore:[bfaBinder]};" data-crop="66.56" data-mobile-crop="66.57" data-mobile-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4WW206rF.jpg?resize=990:659" data-span="1" data-src="https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4WW206rF.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
                <div data-subbuzz-share="138857821">
                </div>
               </div>
              </div>
              <div class="subbuzz__figure-footer">
               <span class="subbuzz__attribution subbuzz__attribution--component js-subbuzz__attribution">
                Warner Bros. / Everett Collection
               </span>
               <div class="js-inline-share-bar inline-share-bar">
               </div>
              </div>
             </figure>
            </div>
           </div>
           <div>
           </div>
          </div>
          <div class="js-subbuzz-wrapper">
           <div>
            <div class="subbuzz buzz-width--standard__subbuzz buzz-type--list__subbuzz subbuzz-freeform subbuzz-index-44" data-module="embed">
             <div class="subbuzz-anchor" id="138856416">
             </div>
             <h2 class="subbuzz__header subbuzz__header--standard subbuzz__title">
              <span class="js-subbuzz__title-text">
               Now, tell me: what's a movie that got a historical or cultural event really wrong? Maybe it's a biopic that just didn't do a historical figure justice, or a historical fiction movie that didn't capture an event's essence? Tell me in the form below, or post your response in the comments!
              </span>
             </h2>
             <div class="cls-min-size" data-cls-content-id="138856416" data-cls-content-type="subbuzz" style=" 
        --cls-ratio-mobile: 192.61213720316624%;
        --cls-height-desktop: min(730px, 200vh);   
      ">
              <div class="subbuzz-freeform__embed" data-cls-target="">
               <iframe src="https://embed.contagiousmedia.com/embed/7894981/138856416?s=contagiousmedia" style="min-height: 725px;">
               </iframe>
              </div>
             </div>
             <figcaption class="subbuzz__caption">
             </figcaption>
            </div>
           </div>
           <div>
           </div>
          </div>
         </div>
        </div>
        <div style="width:100%;height:1px;clear:both">
        </div>
       </article>
       <div class="Ad Ad--subbuzz Ad--edit Ad--loading">
       </div>
       <div>
        <div class="CommentsWrapper_commentsWrapper__Qe9Al CommentsWrapper_condensedComments__N5mnj">
        </div>
       </div>
       <div class="Ad Ad--story-bpage Ad--edit Ad--loading">
       </div>
       <div>
       </div>
       <div>
       </div>
      </div>
      <section aria-label="Promoted content" class="sidebar_sidebar__s_3t_ sidebar_buzzfeed__F7Tm_">
       <div class="sidebar-paginated_container__Hh7uv" style="top:0px">
       </div>
      </section>
     </div>
     <div>
     </div>
     <div>
     </div>
    </main>
   </div>
  </div>
  <script id="__NEXT_DATA__" type="application/json">
   {"props":{"initialI18nStore":{"en":{"common":{"ad_disclaimer":"This is a personal, non-sponsored post by a member of BuzzFeed's ad content team.","again":"Again","already_reacted":"Oops! It looks like you've already used that reaction on this post.","buzzfeed_community":"BuzzFeed Community","choose_pin":"Choose a Pin to save","close_this_modal":"Close this modal","comments_on":"Comments on","commerce_disclaimer":"We hope you love our recommendations! Some may have been sent as samples, but all were independently selected by our editors. Just FYI, BuzzFeed and its publishing partners may collect a share of sales and/or other compensation from the links on this page.","commerce_disclaimer_news":"BuzzFeed may collect a share of sales or other compensation from the links on this page if you decide to shop from them. All products were independently selected by our editors, and the prices were accurate and items in stock at the time of publication.","commerce_disclaimer_partnership":"We hope you love the products we recommend! All of them were independently selected by our editors. Just so you know, BuzzFeed collects a share of sales and/or other compensation from the links on this page. Oh, and FYI — prices are accurate and items in stock as of time of publication.","COMMERCE_SEO_ACCESSORIES":"Looking for more great accessories? From the \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/anamariaglavan/the-best-places-to-buy-glasses-online\"\u003ebest places to buy glasses online\u003c/a\u003e to the \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/katyherman/best-purses-on-amazon-2018\"\u003ebest purses on Amazon\u003c/a\u003e, we’ve got you covered. ","COMMERCE_SEO_BEDROOM":"Want more for your bedroom? Check out the \u003ca href=\"https://www.buzzfeed.com/sallykaplan/here-are-the-best-places-to-buy-your-bedding\"\u003ebest bedding\u003c/a\u003e you can get online, our favorite \u003ca href=\"https://www.buzzfeed.com/elizabethlilly/the-best-mattresses-you-can-get-on-amazon\"\u003emattresses you can get on Amazon\u003c/a\u003e, the \u003ca href=\"https://www.buzzfeed.com/emmamcanaw/the-best-bed-frames-you-can-get-on-amazon\"\u003ebest bed frames to get on Amazon\u003c/a\u003e if you need somewhere to put that new mattress, and the \u003ca href=\"https://www.buzzfeed.com/anamariaglavan/the-best-pillows-you-can-get-on-amazon\"\u003ebest pillows on Amazon\u003c/a\u003e to finish off your new bedroom setup.","COMMERCE_SEO_CLEANING_ORGANIZATION":"Make your life even tidier. Check out the \u003ca href=\"https://www.buzzfeed.com/maitlandquitmeyer/natural-home-cleaners-people-actually-swear-by\"\u003ebest all-natural cleaning products\u003c/a\u003e for your home, the \u003ca href=\"https://www.buzzfeed.com/katyherman/bathroom-cleaning-products-people-actually-swear-by\"\u003ebest bathroom cleaning products\u003c/a\u003e, \u003ca href=\"https://www.buzzfeed.com/yiyang/organization-products\"\u003eorganization products\u003c/a\u003e perfect for neat freaks, and, of course, our \u003ca href=\"https://www.buzzfeed.com/nataliebrown/ultimate-home-cleaning-guide-kitchen-bathroom-bedroom\"\u003eultimate guide to cleaning\u003c/a\u003e every single room in your house.","COMMERCE_SEO_CLOTHING":"Want more? Check out our favorite \u003ca href=\"https://www.buzzfeed.com/maitlandquitmeyer/the-best-places-to-buy-inexpensive-clothes-online\"\u003eonline clothing stores\u003c/a\u003e for all your shopping needs, perfect places to shop for \u003ca href=\"https://www.buzzfeed.com/jessicamisener/30-flirty-and-surviving\"\u003eclothes if you’re in your 30s\u003c/a\u003e, plus the best places to \u003ca href=\"https://www.buzzfeed.com/jennifertonti/best-places-to-order-custom-t-shirts-online\"\u003eorder custom t-shirts online\u003c/a\u003e and the best \u003ca href=\"https://www.buzzfeed.com/betsydickerson/best-places-to-buy-petite-clothing\"\u003eclothing stores for petite sizes\u003c/a\u003e. Still not satisfied? Check out all of our \u003ca href=\"https://www.buzzfeed.com/shopping/clothing\"\u003eclothing\u003c/a\u003e content for even more.","COMMERCE_SEO_FURNITURE_DECOR":"Looking for more stuff to help make your house a home? Check out the \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/katyherman/best-cheap-furniture-online-2018\"\u003ebest places to buy inexpensive furniture\u003c/a\u003e online, the \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/elenamgarcia/the-best-places-to-buy-a-sofa-online\"\u003ebest places to buy couches\u003c/a\u003e online, \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/mallorymcinnis/totally-underrated-places-to-shop-for-home-decor-online\"\u003ecute home decor\u003c/a\u003e you’ll wish you knew about sooner, or check out all of our \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/shopping/home\"\u003ehome\u003c/a\u003e content for even more great ideas.","COMMERCE_SEO_GENERIC_AMAZON":"Looking for more great Amazon finds? Check out some of our favorite cheap \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/jeffbarron/the-best-things-you-can-buy-on-amazon-under-25\"\u003ethings to buy on Amazon\u003c/a\u003e, some of the \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/betsydickerson/amazingly-weird-products-from-amazon\"\u003eweirdest things on Amazon\u003c/a\u003e you might actually want, or read through all the rest of our incredible \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/shopping/amazon\"\u003eAmazon product recommendations\u003c/a\u003e.","COMMERCE_SEO_GIFTS":"Find the perfect gift for any occasion, from  \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/yiyang/best-white-elephant-gifts-under-20\"\u003ewhite elephant gift\u003c/a\u003e exchanges to \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/nataliebrown/teacher-gifts-that-actual-teachers-loved-and-swear-by\"\u003egifts for teachers\u003c/a\u003e, \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/samanthawieder/original-first-apartment-housewarming-gifts\"\u003ehousewarming gifts\u003c/a\u003e to \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/yiyang/things-that-totally-belong-on-your-birthday-wish-list\"\u003ebirthday gifts\u003c/a\u003e, and even the perfect \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/sarahhan/best-places-to-order-gift-baskets-online\"\u003egift basket\u003c/a\u003e if you’re truly stumped. Plus, check out the BuzzFeed \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/giftguide\"\u003egift guide\u003c/a\u003e for even more incredible gifting ideas.","COMMERCE_SEO_HAIR_PRODUCTS":"Need more for your mane? We’ve got products to give you the \u003ca href=\"https://www.buzzfeed.com/ignaciafulcher/amazing-products-thatll-make-your-hair-silky-af\"\u003esilkiest hair ever\u003c/a\u003e, solutions for \u003ca href=\"https://www.buzzfeed.com/betsydickerson/products-for-dry-or-damaged-hair\"\u003edry or damaged hair\u003c/a\u003e, and incredible \u003ca href=\"https://www.buzzfeed.com/ignaciafulcher/hair-products-that-people-with-short-hair-swear-by\"\u003eproducts for short hair\u003c/a\u003e. Plus, the \u003ca href=\"https://www.buzzfeed.com/brittaneytrent/natural-looking-wigs-you-wont-believe-are-from-amazon\"\u003ebest wigs\u003c/a\u003e for anyone who’d rather improve their ‘do that way. Or check out all of our \u003ca href=\"https://www.buzzfeed.com/shopping/beauty\"\u003ebeauty\u003c/a\u003e content for even more great stuff for your hair, skin, and more.","COMMERCE_SEO_SEX_TOYS":"Looking for more ~pleasure~? Check out the \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/betsydickerson/the-best-sex-toys-on-amazon\"\u003ebest sex toys you can get on Amazon\u003c/a\u003e, the best places to \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/harperhendrickson/best-places-to-buy-sex-toys-online-2018\"\u003ebuy all kinds of sex toys online\u003c/a\u003e, the \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/emmyf/best-vibrators-you-can-buy-online\"\u003ebest vibrators you can get online\u003c/a\u003e, and \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/harperhendrickson/sex-toys-for-anyone-who-wants-to-squirt-their-way-out-of-a\"\u003esex toys that will make you squirt\u003c/a\u003e.","COMMERCE_SEO_SHOES":"Want more? Check out the \u003ca href=\"https://www.buzzfeed.com/mallorymcinnis/choose-some-shoes\"\u003ebest places to buy shoes online\u003c/a\u003e, or the \u003ca href=\"https://www.buzzfeed.com/emmamcanaw/shoes-thatll-actually-fit-people-with-wide-feet\"\u003ebest shoes for wide feet\u003c/a\u003e if you need a little extra room.","COMMERCE_SEO_SKIN_PRODUCTS":"Need more for your skin? Check out \u003ca href=\"https://www.buzzfeed.com/sarahhan/korean-skincare-products-youll-wish-youd-known-about\"\u003eKorean skincare products\u003c/a\u003e you need to know, \u003ca href=\"https://www.buzzfeed.com/emmamcanaw/cheap-skincare-products-that-are-a-lot-better-than\"\u003echeap skincare\u003c/a\u003e products that are even better than luxury brands, products for an \u003ca href=\"https://www.buzzfeed.com/abbykass/23-products-designed-to-help-you-get-a-more-even-skin-tone\"\u003eeven skin tone\u003c/a\u003e or to deal with \u003ca href=\"https://www.buzzfeed.com/elenamgarcia/products-for-people-with-acne-scars\"\u003eacne scars\u003c/a\u003e, or check out all of our \u003ca href=\"https://www.buzzfeed.com/shopping/beauty\"\u003ebeauty\u003c/a\u003e content for even more great stuff for your hair, skin, and more.","COMMERCE_SEO_SWIMWEAR":"Want to keep looking? Check out our favorite places to buy the \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/elenamgarcia/best-places-to-buy-bathing-suits-online\"\u003ebest bathing suits online\u003c/a\u003e, or places to buy the \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/anamariaglavan/the-best-places-to-buy-a-bikini-online\"\u003ebest bikinis online\u003c/a\u003e.","COMMERCE_SEO_TRAVEL":"Wanderlust not satisfied yet? Check out more travel products, like the \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/elenamgarcia/best-carry-on-bags-you-can-get-on-amazon\"\u003ebest carry-on bags you can get on Amazon\u003c/a\u003e, \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/elenamgarcia/ways-to-make-a-long-flight-so-much-better\"\u003etravel accessories for long flights\u003c/a\u003e, and the \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/elizabethlilly/best-places-to-buy-luggage-online\"\u003ebest places to buy luggage online\u003c/a\u003e.","COMMERCE_SEO_UNDERWEAR":"Looking for more? Check out our picks for the places that sell the \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/sallykaplan/here-are-the-best-places-to-get-your-underwear\"\u003ebest underwear for women \u003c/a\u003eonline, the \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/anamariaglavan/the-best-places-to-buy-bras-online\"\u003ebest places to buy bras\u003c/a\u003e online, or our favorite places to \u003ca target=\"_blank\" href=\"https://www.buzzfeed.com/bekoconnell/best-places-cheap-lingerie-online\"\u003ebuy affordable lingerie\u003c/a\u003e online.","community_disclaimer":"This post has not been vetted or endorsed by BuzzFeed's editorial staff. BuzzFeed Community is a place where anyone can create a post or quiz.","community_try":"Try making your own!","contact_at_email":"Contact \u003ca href={{ authorLink }}\u003e{{ display_name }}\u003c/a\u003e at \u003ca href=\"mailto:{{ email }}\"\u003e{{ email }}\u003c/a\u003e.","copy_link":"Copy Link","copy_poll_link":"Copy Poll Link","copyright_buzzfeed_inc":"Copyright BuzzFeed, Inc. All rights reserved.","create_post_cta":"Create a post and earn points!","create_your_own_post":"Create your own post!","email":"Email","join_and_make_own_posts":"You can join and make your own posts and quizzes.","latest_posts_from":"Latest Posts From {{ display_name }}","learn_more":"Learn more","link_copied":"Link copied","load_more_stories":"Load more stories","more_from_buzzfeed":"More from BuzzFeed","more_like_this":"More Like This","more_please":"More Please","more_sharing_options":"More sharing options","newsletter_email_error":"Please enter a valid email address","newsletter_placeholder":"Email Address","newsletter_success_message":"Stay tuned for your first newsletter!","newsletter_success_title":"Thanks for signing up!","page_actions":"Page Actions","paid_post":"Paid Post","pause_video":"Pause video","play":"Play","play_video":"Play video","points":"points","popular":"Popular","popular_on_buzzfeed":"Popular on BuzzFeed","popular_products":"Popular products from this list","post_created_by_member_of":"This post was created by a member of the","posted":"Posted","posted_on":"Posted on","promoted_content":"Promoted content","reaction_cute":"cute","reaction_fail":"fail","reaction_heartbroken":"heartbroken","reaction_lol":"lol","reaction_love":"love","reaction_omg":"omg","reaction_win":"win","reaction_wtf":"wtf","read_article":"Read Article","ready_for_more":"Ready for more?","retake_quiz":"Retake Quiz","return_to_article":"Return To Article","save_pin":"Save","server_error":"Looks like we are having a problem on the server","share":"Share","share_article":"Share This Article","share_discussion":"Share This Discussion","share_quiz":"Share This Quiz","share_on_x":"Share On {{name}}","share_results":"Share Your Results","share_vote":"Share Your Vote!","sign_up":"Sign up","sign_up_to_create_post":"Sign up to create your first post!","trending":"Trending","trending_badge":"Trending badge","trending_on_buzzfeed":"Trending on BuzzFeed","tweet":"Tweet","unable_to_copy_link":"Unable to copy link!","updated":"Updated","updated_on":"Updated on","updates":"Updates","video_duration":"Video Duration","views":"Views","watch":"Watch","what_do_you_think":"What do you think?","who_are_you":"Who are you?","you_got":"You got:"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"namespacesRequired":["common"],"badges":[],"buzz":{"all_classifications":{"editions":["Canada"],"sections":["TVAndMovies","Internet Finds"]},"bylines":[{"avatar":"https://img.buzzfeed.com/buzzfeed-static/static/user_images/THEnS0Igb_large.jpg?output-format=jpg\u0026crop=299:299;102,164","bio":"Hey there! I'm Abby, a Staff Writer for BuzzFeed based in Toronto. In addition to \u003ca href=\"https://www.buzzfeed.com/abbyzinman/canadians-elbows-up-explainer\"\u003eCanadian content\u003c/a\u003e, you'll often find me writing about \u003ca href=\"https://www.buzzfeed.com/abbyzinman/famous-kids-who-grew-up-so-fast\"\u003eceleb culture\u003c/a\u003e, \u003ca href=\"https://www.buzzfeed.com/abbyzinman/90s-00s-tv-show-photo-trivia-quiz\"\u003e'90s–'00s nostalgia\u003c/a\u003e, \u003ca href=\"https://www.buzzfeed.com/abbyzinman/shows-good-until-the-end-and-declined\"\u003eTV/film\u003c/a\u003e, \u003ca href=\"https://www.buzzfeed.com/abbyzinman/new-york-vs-montreal-bagel-showdown\"\u003efood\u003c/a\u003e, \u003ca href=\"https://www.buzzfeed.com/abbyzinman/name-these-super-famous-historical-figures\"\u003ehistory\u003c/a\u003e — and honestly, anything the \u003ca href=\"https://www.buzzfeed.com/abbyzinman/gen-z-style-trends-flipped-dq\"\u003einternet\u003c/a\u003e is buzzing about. After getting my start on the BuzzFeed Community back in 2016, I later became a writer for \u003ca href=\"https://www.hercampus.com/author/abby-zinman/\" target=\"_blank\"\u003eHer Campus\u003c/a\u003e, where I primarily covered wellness and mental health. I also have a degree in digital arts from the University of Waterloo, and further professional experience in user experience (UX) design and marketing. When I'm not writing, you'll probably find me spinning, singing a Taylor Swift song, or eating carrot cake.","description_id":"2","description_visual":"BuzzFeed Staff","display_name":"Abby Zinman","email":"<EMAIL>","facebook_page_url":"","id":"3647075","title":"BuzzFeed Staff","twitter_page_url":"https://x.com/aezinnyy","twitter_widget":"","twitter_widget_id":null,"username":"abbyzinman"}],"canonical_path":"/abbyzinman/movie-mistakes-about-real-events","canonical_url":"https://www.buzzfeed.com/abbyzinman/movie-mistakes-about-real-events","category":"Canada","classification":{"edition":"Canada","section":"TVAndMovies"},"classification_metadata":{"section":{"display_name":"TVAndMovies","name":"TVAndMovies","slug":""}},"client_id":0,"contextual_ad_targeting":[{"clusterID":1,"clusterName":"Test"},{"clusterID":2,"clusterName":"FMK Test 2/5"},{"clusterID":4,"clusterName":"FMK 0%"},{"clusterID":5,"clusterName":"Test - Comedy"},{"clusterID":9,"clusterName":"The Ritual Test"},{"clusterID":20,"clusterName":"Action_Adventure_Thriller_BuzzFeed"},{"clusterID":22,"clusterName":"The Ritual_98%"},{"clusterID":23,"clusterName":"The Ritual_85%"},{"clusterID":24,"clusterName":"The ritual_70%"},{"clusterID":29,"clusterName":"The Office Superfans"},{"clusterID":36,"clusterName":"BF | Core | Emerging Affluent"},{"clusterID":39,"clusterName":"BF | Core | Zillennials "},{"clusterID":42,"clusterName":"Unilever_RadiantTrailblazers_90%"},{"clusterID":44,"clusterName":"EntertainmentEnthusiasts_BuzzFeed_90%"}],"country_code":"en-ca","datelines":[],"description":"I guess we shouldn't trust Hollywood for a history lesson.","destination_display_name":"BuzzFeed","destination_name":"buzzfeed","editorial_status":"Editorial","first_published":"1751913062","flags":{"ad":0,"brand_safe":1,"comments_enabled":1,"developing_mode":0,"nsfw":0,"raw":1,"reactions_enabled":1,"sensitive":0},"format":{"page_type":"list_countup","page_width":"standard"},"id":"7894981","images":{"big":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/3-He8cvGR.jpg?crop=1245:830;3,0\u0026resize=710:472","big_alt_text":"Two women in supermarket uniforms working and looking serious. Text criticizes \"The Help\" for inaccurately portraying Black domestic workers' trauma in the 1950s-60s","dblbig":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/3-He8cvGR.jpg?crop=1245:830;3,0\u0026resize=1250:830","dblbig_alt_text":"Two women in supermarket uniforms working and looking serious. Text criticizes \"The Help\" for inaccurately portraying Black domestic workers' trauma in the 1950s-60s","dblwide":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/hd-dUysa-.jpg?crop=2999:1056;0,0\u0026resize=1250:440","dblwide_alt_text":"Two women in supermarket uniforms working and looking serious. Text criticizes \"The Help\" for inaccurately portraying Black domestic workers' trauma in the 1950s-60s","small":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/3-He8cvGR.jpg?crop=1245:830;3,0\u0026resize=90:60","small_alt_text":"Two women in supermarket uniforms working and looking serious. Text criticizes \"The Help\" for inaccurately portraying Black domestic workers' trauma in the 1950s-60s","square":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/HXdix0e2-.jpg?crop=2000:2000;0,0\u0026resize=625:625","square_alt_text":"Two women in supermarket uniforms working and looking serious. Text criticizes \"The Help\" for inaccurately portraying Black domestic workers' trauma in the 1950s-60s","standard":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/3-He8cvGR.jpg?crop=1245:830;3,0\u0026resize=125:83","standard_alt_text":"Two women in supermarket uniforms working and looking serious. Text criticizes \"The Help\" for inaccurately portraying Black domestic workers' trauma in the 1950s-60s","wide":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/hd-dUysa-.jpg?crop=2999:1056;0,0\u0026resize=710:250","wide_16x9":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/3-He8cvGR.jpg?fill=1200:675","wide_16x9_alt_text":"Two women in supermarket uniforms working and looking serious. Text criticizes \"The Help\" for inaccurately portraying Black domestic workers' trauma in the 1950s-60s","wide_alt_text":"Two women in supermarket uniforms working and looking serious. Text criticizes \"The Help\" for inaccurately portraying Black domestic workers' trauma in the 1950s-60s"},"is_convert_enabled":false,"is_quiz":false,"language":"en","laser_tags":{"bf_content_description":{"frames_and_formats":[{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":1750193575,"deleted_at":null,"id":130982995,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"People Are Sharing","tag_externally_created":1,"tag_id":7539926,"tag_metadata":null,"tag_name":"people_are_sharing","tag_type_name":"frames_and_formats","updated_at":1750193575}],"topic":[{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":**********,"deleted_at":null,"id":130982756,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Stranger Things","tag_externally_created":0,"tag_id":6676892,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"From celebrity confessions to character deaths, this Stranger Things topic page has all the drama and excitement fans need to dive into the world of Hawkins.","updated_at":1693324730507}}}},"tag_name":"stranger_things","tag_type_name":"topic","updated_at":**********},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":**********,"deleted_at":null,"id":130982757,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"The Help","tag_externally_created":0,"tag_id":6720211,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"Discover a collection of articles discussing various aspects of The Help, including memorable movie diss moments, badass female character moments without violence, and actors voicing their regrets on playing certain roles.","updated_at":1693324933502}}}},"tag_name":"the_help","tag_type_name":"topic","updated_at":**********},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":**********,"deleted_at":null,"id":130982758,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Argo","tag_externally_created":0,"tag_id":6720216,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"Discover the truth behind the gripping and unbelievable true story of Argo, as we expose the inaccuracies in this Oscar-winning film!","updated_at":1693324933502}}}},"tag_name":"argo","tag_type_name":"topic","updated_at":**********},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":**********,"deleted_at":null,"id":130982759,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Hidden Figures","tag_externally_created":0,"tag_id":6720254,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"Explore this topic page to discover feel-good family films, movies on female friendships, and the incredible true story of Hidden Figures' Katherine Johnson.","updated_at":1693324933502}}}},"tag_name":"hidden_figures","tag_type_name":"topic","updated_at":**********},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":**********,"deleted_at":null,"id":130982760,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Bones","tag_externally_created":0,"tag_id":6676618,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"Discover everything you need to know about bones, from shocking TV plot twists to unforgettable character deaths. Dive into this intriguing topic now!","updated_at":1693324727126}}}},"tag_name":"bones","tag_type_name":"topic","updated_at":**********},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":**********,"deleted_at":null,"id":130982761,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Grey's Anatomy","tag_externally_created":0,"tag_id":6648965,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"This topic page dives into the world of Grey's Anatomy with articles on TV couples, memorable episodes, ruined characters, axed show details, and more! Don't miss out on the drama and excitement of this long-running medical drama series.","updated_at":1693324725434}}}},"tag_name":"grey_s_anatomy","tag_type_name":"topic","updated_at":**********},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":**********,"deleted_at":null,"id":130982762,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Twister","tag_externally_created":1,"tag_id":10889974,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"Discover everything about the iconic movie \"Twister,\" from its cast members' careers to fan reactions and hidden references within the film.","updated_at":1724176901245}}}},"tag_name":"twister","tag_type_name":"topic","updated_at":**********},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":**********,"deleted_at":null,"id":130982763,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Independence Day","tag_externally_created":0,"tag_id":6729142,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"Discover how Americans from coast to coast celebrate Independence Day in this exciting topic page filled with patriotic traditions and festivities.","updated_at":1693324938592}}}},"tag_name":"independence_day","tag_type_name":"topic","updated_at":**********},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":**********,"deleted_at":null,"id":130982764,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"127 Hours","tag_externally_created":0,"tag_id":6720200,"tag_metadata":null,"tag_name":"127_hours","tag_type_name":"topic","updated_at":**********},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":**********,"deleted_at":null,"id":130982765,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"13 Going on 30","tag_externally_created":0,"tag_id":6729197,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"Discover all the iconic outfits worn by Jenna Rink in \"13 Going on 30\" and find out which one suits your style based on your choices!","updated_at":1693349924911}}}},"tag_name":"13_going_on_30","tag_type_name":"topic","updated_at":**********},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":**********,"deleted_at":null,"id":130982766,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Moneyball","tag_externally_created":0,"tag_id":6720213,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"Discover the groundbreaking world of Moneyball, the topic that defied tradition and changed the way we approach sports. Get inspired by 21 sports films that capture the essence of this game-changing concept.","updated_at":1693324933502}}}},"tag_name":"moneyball","tag_type_name":"topic","updated_at":**********},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":1750193551,"deleted_at":null,"id":130982932,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Documentary","tag_externally_created":1,"tag_id":6998243,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"manual","text":"Discover the world through the lens of captivating documentaries. Explore real stories, hidden gems, and controversial topics that will leave you wanting more.","updated_at":1693346511551}}}},"tag_name":"documentary","tag_type_name":"topic","updated_at":1750193551},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":1750193551,"deleted_at":null,"id":130982933,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Drama","tag_externally_created":0,"tag_id":6867406,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"manual","text":"Discover the fascinating world of drama with quizzes, controversies, film adaptations, and behind-the-scenes stories from popular TV shows and movies!","updated_at":1693342563987}}}},"tag_name":"drama","tag_type_name":"topic","updated_at":1750193551},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":1750193551,"deleted_at":null,"id":130982934,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Horror","tag_externally_created":0,"tag_id":6653001,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"Get ready to be spooked! Explore eerie photos, terrifying mannequins, and shocking facts about real-life Dracula on this horror-packed topic page.","updated_at":1693324725434}}}},"tag_name":"horror","tag_type_name":"topic","updated_at":1750193551},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":1750193551,"deleted_at":null,"id":130982935,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Comedy","tag_externally_created":0,"tag_id":6867407,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"manual","text":"Get ready to laugh! This page is a hilarious collection of quizzes, films, characters, and more that will have you in stitches and rolling on the floor with laughter.","updated_at":1693342916622}}}},"tag_name":"comedy","tag_type_name":"topic","updated_at":1750193551},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":1750193551,"deleted_at":null,"id":130982936,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Romance","tag_externally_created":0,"tag_id":6867410,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"manual","text":"Get ready for some serious romance as we explore quizzes, films, and characters that will make your heart swoon.","updated_at":1693344044521}}}},"tag_name":"romance","tag_type_name":"topic","updated_at":1750193551},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":1750193566,"deleted_at":null,"id":130982948,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Paramount Plus","tag_externally_created":0,"tag_id":6766319,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"manual","text":"Get ready for a binge-watching marathon! Explore the exciting world of Paramount Plus with its amazing lineup of TV shows and movies, including some classic sitcom hangout spots and exceptional series that you can watch in one weekend.","updated_at":1693345067794}}}},"tag_name":"paramount_plus","tag_type_name":"topic","updated_at":1750193566},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":1750193566,"deleted_at":null,"id":130982949,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Netflix","tag_externally_created":0,"tag_id":6225608,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"Discover the best films based on books that outshine their literary counterparts, and learn which TV guest stars went on to become big-name actors on this page about Netflix.","updated_at":1693324725433}}}},"tag_name":"netflix","tag_type_name":"topic","updated_at":1750193566},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":1750193566,"deleted_at":null,"id":130982950,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Disney Plus","tag_externally_created":0,"tag_id":6648923,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"Find out which Disney Princess matches your green flags and which villain matches your red ones, and discover which iconic Disney fairy you are on this exciting Disney Plus topic page.","updated_at":1693324725434}}}},"tag_name":"disney_plus","tag_type_name":"topic","updated_at":1750193566},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":1750193568,"deleted_at":null,"id":130982973,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"TV Shows","tag_externally_created":0,"tag_id":6225604,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"Discover the world of TV shows with a mix of character quizzes, controversial finales, real-life inspirations, and regrets from actors. Get ready for a wild ride!","updated_at":1693324725433}}}},"tag_name":"tv_shows","tag_type_name":"topic","updated_at":1750193568},{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":1750193568,"deleted_at":null,"id":130982974,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Movies","tag_externally_created":0,"tag_id":6225605,"tag_metadata":{"seo_description":{"buzzfeed":{"en-us":{"source":"automated","text":"Discover fascinating insights about movies, including characters based on real people, films better than the books, and disturbing events on set. Plus, find out which heartthrob actors have transformed over the years!","updated_at":1693324725433}}}},"tag_name":"movies","tag_type_name":"topic","updated_at":1750193568}],"workflow":[{"accuracy":null,"canonical_id":"buzz:7894981","count":1,"created_at":1750190555,"deleted_at":null,"id":130976114,"metadata":null,"namespace_name":"bf_content_description","object_id":"7894981","origin_id":49,"origin_name":"questionnaire_api","platform_id":1,"platform_name":"buzz","tag_display_name":"Black Content","tag_externally_created":0,"tag_id":6713690,"tag_metadata":null,"tag_name":"black_content","tag_type_name":"workflow","updated_at":1750190555}]},"watson":{"category":[{"accuracy":0.53436,"canonical_id":"buzz:7894981","count":3,"created_at":**********,"deleted_at":null,"id":130970331,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"/news and politics/law","tag_externally_created":1,"tag_id":10785090,"tag_metadata":null,"tag_name":"news_and_politics_law","tag_type_name":"category","updated_at":**********},{"accuracy":0.529701,"canonical_id":"buzz:7894981","count":8,"created_at":**********,"deleted_at":null,"id":130970334,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"/movies","tag_externally_created":1,"tag_id":10784702,"tag_metadata":null,"tag_name":"movies","tag_type_name":"category","updated_at":1750187960},{"accuracy":0.518922,"canonical_id":"buzz:7894981","count":1,"created_at":**********,"deleted_at":null,"id":130970336,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"/movies/horror movies","tag_externally_created":1,"tag_id":10785142,"tag_metadata":null,"tag_name":"movies_horror_movies","tag_type_name":"category","updated_at":**********},{"accuracy":0.529658,"canonical_id":"buzz:7894981","count":1,"created_at":1750187202,"deleted_at":null,"id":130970379,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"/music and audio/children's music","tag_externally_created":1,"tag_id":10784977,"tag_metadata":null,"tag_name":"music_and_audio_childrens_music","tag_type_name":"category","updated_at":1750187202},{"accuracy":0.527013,"canonical_id":"buzz:7894981","count":76,"created_at":1750187202,"deleted_at":null,"id":130970382,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"/family and relationships","tag_externally_created":1,"tag_id":10784732,"tag_metadata":null,"tag_name":"family_and_relationships","tag_type_name":"category","updated_at":**********},{"accuracy":0.920941,"canonical_id":"buzz:7894981","count":85,"created_at":1750187694,"deleted_at":null,"id":130971330,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"/movies/family and children movies","tag_externally_created":1,"tag_id":10785016,"tag_metadata":null,"tag_name":"movies_family_and_children_movies","tag_type_name":"category","updated_at":**********},{"accuracy":0.875476,"canonical_id":"buzz:7894981","count":80,"created_at":1750187694,"deleted_at":null,"id":130971332,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"/movies/indie and arthouse movies","tag_externally_created":1,"tag_id":10785284,"tag_metadata":null,"tag_name":"movies_indie_and_arthouse_movies","tag_type_name":"category","updated_at":**********},{"accuracy":0.852347,"canonical_id":"buzz:7894981","count":3,"created_at":1750187694,"deleted_at":null,"id":130971334,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"/movies/documentary movies","tag_externally_created":1,"tag_id":10785811,"tag_metadata":null,"tag_name":"movies_documentary_movies","tag_type_name":"category","updated_at":1750188039},{"accuracy":0.657137,"canonical_id":"buzz:7894981","count":2,"created_at":1750187781,"deleted_at":null,"id":130971412,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"/events and attractions/personal celebrations \u0026 life events","tag_externally_created":1,"tag_id":10792702,"tag_metadata":null,"tag_name":"events_and_attractions_personal_celebrations_life_events","tag_type_name":"category","updated_at":**********},{"accuracy":0.650957,"canonical_id":"buzz:7894981","count":10,"created_at":1750187781,"deleted_at":null,"id":130971415,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"/movies/animation movies","tag_externally_created":1,"tag_id":10784954,"tag_metadata":null,"tag_name":"movies_animation_movies","tag_type_name":"category","updated_at":1750198705},{"accuracy":0.765054,"canonical_id":"buzz:7894981","count":4,"created_at":1750187806,"deleted_at":null,"id":130971474,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"/family and relationships/divorce","tag_externally_created":1,"tag_id":10784736,"tag_metadata":null,"tag_name":"family_and_relationships_divorce","tag_type_name":"category","updated_at":**********},{"accuracy":0.722844,"canonical_id":"buzz:7894981","count":2,"created_at":1750187806,"deleted_at":null,"id":130971476,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"/family and relationships/single life","tag_externally_created":1,"tag_id":10784734,"tag_metadata":null,"tag_name":"family_and_relationships_single_life","tag_type_name":"category","updated_at":**********},{"accuracy":0.578564,"canonical_id":"buzz:7894981","count":2,"created_at":**********,"deleted_at":null,"id":132807725,"metadata":{"text_source":"url"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"/medical health/surgery","tag_externally_created":1,"tag_id":10785867,"tag_metadata":null,"tag_name":"medical_health_surgery","tag_type_name":"category","updated_at":**********}],"category_level_1":[{"accuracy":0.53436,"canonical_id":"buzz:7894981","count":3,"created_at":**********,"deleted_at":null,"id":130970332,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"news and politics","tag_externally_created":1,"tag_id":10785089,"tag_metadata":null,"tag_name":"news_and_politics","tag_type_name":"category_level_1","updated_at":**********},{"accuracy":0.529701,"canonical_id":"buzz:7894981","count":88,"created_at":**********,"deleted_at":null,"id":130970335,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"movies","tag_externally_created":1,"tag_id":10784703,"tag_metadata":null,"tag_name":"movies","tag_type_name":"category_level_1","updated_at":**********},{"accuracy":0.529658,"canonical_id":"buzz:7894981","count":1,"created_at":1750187202,"deleted_at":null,"id":130970380,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"music and audio","tag_externally_created":1,"tag_id":10784973,"tag_metadata":null,"tag_name":"music_and_audio","tag_type_name":"category_level_1","updated_at":1750187202},{"accuracy":0.527013,"canonical_id":"buzz:7894981","count":76,"created_at":1750187202,"deleted_at":null,"id":130970383,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"family and relationships","tag_externally_created":1,"tag_id":10784733,"tag_metadata":null,"tag_name":"family_and_relationships","tag_type_name":"category_level_1","updated_at":**********},{"accuracy":0.657137,"canonical_id":"buzz:7894981","count":2,"created_at":1750187781,"deleted_at":null,"id":130971413,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"events and attractions","tag_externally_created":1,"tag_id":10785027,"tag_metadata":null,"tag_name":"events_and_attractions","tag_type_name":"category_level_1","updated_at":**********},{"accuracy":0.578564,"canonical_id":"buzz:7894981","count":2,"created_at":**********,"deleted_at":null,"id":132807726,"metadata":{"text_source":"url"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"medical health","tag_externally_created":1,"tag_id":10784843,"tag_metadata":null,"tag_name":"medical_health","tag_type_name":"category_level_1","updated_at":**********}],"category_level_2":[{"accuracy":0.53436,"canonical_id":"buzz:7894981","count":3,"created_at":**********,"deleted_at":null,"id":130970333,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"law","tag_externally_created":1,"tag_id":10785091,"tag_metadata":null,"tag_name":"law","tag_type_name":"category_level_2","updated_at":**********},{"accuracy":0.518922,"canonical_id":"buzz:7894981","count":1,"created_at":**********,"deleted_at":null,"id":130970337,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"horror movies","tag_externally_created":1,"tag_id":10785143,"tag_metadata":null,"tag_name":"horror_movies","tag_type_name":"category_level_2","updated_at":**********},{"accuracy":0.529658,"canonical_id":"buzz:7894981","count":1,"created_at":1750187202,"deleted_at":null,"id":130970381,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"children's music","tag_externally_created":1,"tag_id":10784978,"tag_metadata":null,"tag_name":"childrens_music","tag_type_name":"category_level_2","updated_at":1750187202},{"accuracy":0.920941,"canonical_id":"buzz:7894981","count":85,"created_at":1750187694,"deleted_at":null,"id":130971331,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"family and children movies","tag_externally_created":1,"tag_id":10785017,"tag_metadata":null,"tag_name":"family_and_children_movies","tag_type_name":"category_level_2","updated_at":**********},{"accuracy":0.875476,"canonical_id":"buzz:7894981","count":80,"created_at":1750187694,"deleted_at":null,"id":130971333,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"indie and arthouse movies","tag_externally_created":1,"tag_id":10785285,"tag_metadata":null,"tag_name":"indie_and_arthouse_movies","tag_type_name":"category_level_2","updated_at":**********},{"accuracy":0.852347,"canonical_id":"buzz:7894981","count":3,"created_at":1750187694,"deleted_at":null,"id":130971335,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"documentary movies","tag_externally_created":1,"tag_id":10785812,"tag_metadata":null,"tag_name":"documentary_movies","tag_type_name":"category_level_2","updated_at":1750188039},{"accuracy":0.657137,"canonical_id":"buzz:7894981","count":2,"created_at":1750187781,"deleted_at":null,"id":130971414,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"personal celebrations \u0026 life events","tag_externally_created":1,"tag_id":10785978,"tag_metadata":null,"tag_name":"personal_celebrations_life_events","tag_type_name":"category_level_2","updated_at":**********},{"accuracy":0.650957,"canonical_id":"buzz:7894981","count":10,"created_at":1750187781,"deleted_at":null,"id":130971416,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"animation movies","tag_externally_created":1,"tag_id":10784955,"tag_metadata":null,"tag_name":"animation_movies","tag_type_name":"category_level_2","updated_at":1750198705},{"accuracy":0.765054,"canonical_id":"buzz:7894981","count":4,"created_at":1750187806,"deleted_at":null,"id":130971475,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"divorce","tag_externally_created":1,"tag_id":10784737,"tag_metadata":null,"tag_name":"divorce","tag_type_name":"category_level_2","updated_at":**********},{"accuracy":0.722844,"canonical_id":"buzz:7894981","count":2,"created_at":1750187806,"deleted_at":null,"id":130971477,"metadata":{"text_source":"parsed text"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"single life","tag_externally_created":1,"tag_id":10784735,"tag_metadata":null,"tag_name":"single_life","tag_type_name":"category_level_2","updated_at":**********},{"accuracy":0.578564,"canonical_id":"buzz:7894981","count":2,"created_at":**********,"deleted_at":null,"id":132807727,"metadata":{"text_source":"url"},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"surgery","tag_externally_created":1,"tag_id":10785868,"tag_metadata":null,"tag_name":"surgery","tag_type_name":"category_level_2","updated_at":**********}],"keyword":[{"accuracy":0.897818,"canonical_id":"buzz:7894981","count":2,"created_at":**********,"deleted_at":null,"id":130970368,"metadata":{"emotion":{"anger":0.099272,"disgust":0.166474,"fear":0.028761,"joy":0.13207,"sadness":0.179502},"sentiment":{"label":"negative","score":-0.834457}},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"Ridiculously Common Movie Mistakes","tag_externally_created":1,"tag_id":12631578,"tag_metadata":null,"tag_name":"ridiculously_common_movie_mistakes","tag_type_name":"keyword","updated_at":1750187202},{"accuracy":0.861851,"canonical_id":"buzz:7894981","count":3,"created_at":**********,"deleted_at":null,"id":130970369,"metadata":{"emotion":{"anger":0.099272,"disgust":0.166474,"fear":0.028761,"joy":0.13207,"sadness":0.179502},"sentiment":{"label":"negative","score":-0.834457}},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"people","tag_externally_created":1,"tag_id":7940,"tag_metadata":null,"tag_name":"people","tag_type_name":"keyword","updated_at":1750187961},{"accuracy":0.986171,"canonical_id":"buzz:7894981","count":89,"created_at":1750187694,"deleted_at":null,"id":130971358,"metadata":{"emotion":{"anger":0.111715,"disgust":0.06033,"fear":0.018476,"joy":0.053845,"sadness":0.726125},"sentiment":{"label":"negative","score":-0.828223}},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"older people","tag_externally_created":1,"tag_id":134107,"tag_metadata":null,"tag_name":"older_people","tag_type_name":"keyword","updated_at":1751922474},{"accuracy":0.902512,"canonical_id":"buzz:7894981","count":87,"created_at":1750187782,"deleted_at":null,"id":130971429,"metadata":{"emotion":{"anger":0.023429,"disgust":0.066809,"fear":0.084523,"joy":0.15998,"sadness":0.447177},"sentiment":{"label":"negative","score":-0.433888}},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"movie mistakes","tag_externally_created":1,"tag_id":1720837,"tag_metadata":null,"tag_name":"movie_mistakes","tag_type_name":"keyword","updated_at":1751913069},{"accuracy":0.937757,"canonical_id":"buzz:7894981","count":81,"created_at":1750187908,"deleted_at":null,"id":130971922,"metadata":{"emotion":{"anger":0.032487,"disgust":0.022885,"fear":0.036907,"joy":0.409136,"sadness":0.296419},"sentiment":{"label":"negative","score":-0.437966}},"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"certified Young Person","tag_externally_created":1,"tag_id":12631738,"tag_metadata":null,"tag_name":"certified_young_person","tag_type_name":"keyword","updated_at":1751922474}],"sentiment":[{"accuracy":0.834457,"canonical_id":"buzz:7894981","count":91,"created_at":**********,"deleted_at":null,"id":130970371,"metadata":null,"namespace_name":"watson","object_id":"7894981","origin_id":77,"origin_name":"qr_watson_tags","platform_id":1,"platform_name":"buzz","tag_display_name":"negative","tag_externally_created":1,"tag_id":1704253,"tag_metadata":null,"tag_name":"negative","tag_type_name":"sentiment","updated_at":1751922474}]}},"last_updated":"1752459909","longform_body":"","metavertical":"buzz","original_owner":{"display_name":"Abby Zinman","id":3647075,"image":"https://img.buzzfeed.com/buzzfeed-static/static/user_images/THEnS0Igb_large.jpg?output-format=jpg\u0026crop=299:299;102,164","username":"abbyzinman"},"promoted_at":1751913062,"published":"1751913062","published_by_user_id":3647075,"raw_translations":[],"related_feeds":{},"socialpromotions":{"apple_news":[{"campaign_id":"7894981","description":"I guess we shouldn't trust Hollywood for a history lesson.","extra_fields":"{\"variants\":{\"titles\":[\"Older People Are Calling Out The Most Ridiculous On-Screen Inaccuracies About Events They Actually Experienced\",\"\"]}}","height":null,"id":"2357999","image":null,"title":"Older People Are Sharing The \"Painfully Obvious\" Mistakes They've Seen In Movies About Real-Life Events ","width":null}],"bpage":[{"campaign_id":"7894981","description":"","extra_fields":null,"height":null,"id":"2358194","image":null,"title":"23 Movie And TV Show Inaccuracies About Real Events","width":null}],"facebook":[{"campaign_id":"7894981","description":"I guess we shouldn't trust Hollywood for a history lesson.","extra_fields":"{\"big\":{\"left\":0,\"top\":0,\"width\":2999,\"height\":1570},\"imagebuilderState\":{\"id\":1750198138142}}","height":"1574","id":"2358000","image":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/thumb/WjfFayDO4.jpg","title":"Older People Are Sharing The Worst On-Screen Inaccuracies About Real Events","width":"2999"}],"homepage":[{"campaign_id":"7894981","description":"I guess we shouldn't trust Hollywood for a history lesson.","extra_fields":"{\"short_headline\":\"Older People Are Calling Out The Worst On-Screen Inaccuracies About Events They Experienced\"}","height":null,"id":"2358193","image":null,"title":"Old People Are Calling Out The Historical Movie And TV Mistakes That Made Them Scream At The Screen, Because They Actually Lived Through That Era","width":null}],"instagram":[],"pinterest":[],"twitter":[{"campaign_id":"7894981","description":"I guess we shouldn't trust Hollywood for a history lesson.","extra_fields":"{\"big\":{\"left\":0,\"top\":0,\"width\":3000,\"height\":1500},\"imagebuilderState\":{\"id\":1750198168329}}","height":"1500","id":"2357998","image":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/thumb/WC2_q0loz.jpg","title":"Older People Are Sharing The \"Painfully Obvious\" Mistakes They've Seen In Movies About Real-Life Events They Lived Through, And All I Can Say Is WOW","width":"3000"}]},"status":"live","sub_buzzes":[{"description":"","form":"text","header":"I love any movie that's based on real-life events. Even if it's a loose interpretation, if it has to do with something that actually happened, I am freakin' SEATED.","id":"138856399","shareable":false},{"description":"","form":"text","header":"But as a certified Young Person (for now), I haven't really thought about what it feels like to watch those movies as someone who's lived through the particular cultural event being discussed.","id":"138856404","shareable":false},{"alt_text":"Person in a white tank top on stage under bright lights, with an energetic pose","attribution":"20th Century Fox / Via \u003ca href=\"https://giphy.com/gifs/foxhomeent-bohemian-rhapsody-bohemianrhapsody-borhap-fs2TMBMniTY2RDNSpn\" target=\"_blank\"\u003egiphy.com\u003c/a\u003e","description":"","form":"image","header":"","id":"138857754","images":{"mobile":{"height":"277","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/RfPRXOs6u.png","width":"495"},"original":{"height":"277","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/RfPRXOs6u.png","width":"495"},"standard":{"height":"277","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/RfPRXOs6u.png","width":"495"},"wide":{"height":"277","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/RfPRXOs6u.png","width":"495"}},"shareable":false},{"description":"","form":"text","header":"So recently on Reddit, older people were answering the question: \"What movie \u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/?sort=top\" target=\"_blank\"\u003egot it really wrong\u003c/a\u003e, and you know, because you were there or lived through it?\" — and the responses are pretty mind-boggling. Some of them reference specific movies, and some discuss general trends with movies that are completely false:","id":"138856408","shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxya409/\" target=\"_blank\"\u003epatchouliii\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"\u003ci\u003eThe Help\u003c/i\u003e with \u003ca href=\"https://www.buzzfeed.com/leylamohammed/viola-davis-husband-julius-tennon-reveals-credit-score\"\u003eViola Davis\u003c/a\u003e. She's one of my favorite actresses, but even \u003ca href=\"https://www.vanityfair.com/hollywood/2018/09/viola-davis-the-help-regret?srsltid=AfmBOorH1RzgExrxud8_kfP27TPLLCQlgdnSNxEKreoaI_LfjpJv1VwN\" target=\"_blank\"\u003eshe admits\u003c/a\u003e it was an inaccurate portrayal of the psychological trauma the Black female domestic workers faced in the 1950s and 1960s.\"","id":"138856660","number":"1","shareable":false},{"alt_text":"Three women are sitting at a kitchen table engaged in a serious conversation, likely from a period film or show","attribution":"Walt Disney Co. / Everett Collection","description":"","form":"image","header":"","id":"138857758","images":{"mobile":{"height":"658","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/1KEISdXxR.jpg?resize=990:658","width":"990"},"original":{"height":"2848","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/1KEISdXxR.jpg","width":"4288"},"standard":{"height":"415","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/1KEISdXxR.jpg?resize=625:415","width":"625"},"wide":{"height":"658","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/1KEISdXxR.jpg?resize=990:658","width":"990"}},"shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/my14i0b/\" target=\"_blank\"\u003eOk-Trash-8883\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"Essentially most movies depicting the '80s. A great example is \u003ca href=\"https://www.buzzfeed.com/romcomqueen/rom-com-soulmate-quiz\"\u003e\u003ci\u003e13 Going on 30\u003c/i\u003e\u003c/a\u003e. Really sweet movie, but they got the fashion and music totally wrong. The first part of the film takes place in 1987. 'Jessie’s Girl' was not popular then. It was popular in 1982–1983. 'Burning Down The House' also not popular in '87, it was popular in 1983–1984. The clothes she and her friends wear were long out of style by the time 1987 rolled around. It’s like these movies take an entire decade and cram it all together — what was cool and current in 1984 was not cool and current in 1987!\"","id":"138857848","number":"2","shareable":false},{"alt_text":"Person wearing hair rollers applies makeup at a vanity, smiling with a robe on. Cozy room setting in background","attribution":"Columbia Pictures / Everett Collection","description":"","form":"image","header":"","id":"138857760","images":{"mobile":{"height":"660","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/n-daCyN3r.jpg?resize=990:660","width":"990"},"original":{"height":"1900","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/n-daCyN3r.jpg","width":"2850"},"standard":{"height":"417","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/n-daCyN3r.jpg?resize=625:417","width":"625"},"wide":{"height":"660","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/n-daCyN3r.jpg?resize=990:660","width":"990"}},"shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzj9nf/\" target=\"_blank\"\u003eRotrickP\u003c/a\u003e\u003c/p\u003e\u003cp\u003e\"The biggest thing I noticed that \u003ci\u003eStranger Things\u003c/i\u003e got wrong was that all the kids wore their backpacks on both shoulders. I graduated from high school in 1984 and EVERYONE wore backpacks off one shoulder. Its why we all have herniated cervical discs in our 50s.\"\u003c/p\u003e\u003cp\u003e–\u003ca href=\"https://www.reddit.com/user/crankyweasels/\" target=\"_blank\"\u003ecrankyweasels\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"There were a couple things I think \u003ca href=\"https://www.buzzfeed.com/marycolussi/which-stranger-things-character-are-you\"\u003e\u003ci\u003eStranger Things\u003c/i\u003e\u003c/a\u003e got wrong. A) 'Douchebag' was not a common term for kids, even though it was in \u003ci\u003eET\u003c/i\u003e. That wasn't popular until the early 2000s out of nowhere. B) The clothes on the show look like the kids can wear them comfortably, and this wasn't the case. Part of the reason IMO you see kids in the '70s–'80s shirtless was this clothes were just so uncomfortable for so many kids. C) There was a huge obsession with beach culture in the late '80s, no matter where you were in the country, and the show hasn't shown that yet, but that could just be because they're saving it for the last season.\"","id":"138856673","number":"3","shareable":false},{"alt_text":"Three young men have a conversation outdoors, appearing focused and engaged","attribution":"Netflix / Everett Collection","description":"","form":"image","header":"","id":"138857764","images":{"mobile":{"height":"495","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfXipsXrx.jpg?resize=990:495","width":"990"},"original":{"height":"1500","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfXipsXrx.jpg","width":"3000"},"standard":{"height":"313","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfXipsXrx.jpg?resize=625:313","width":"625"},"wide":{"height":"495","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfXipsXrx.jpg?resize=990:495","width":"990"}},"shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzy4ds/\" target=\"_blank\"\u003eetaxif\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"\u003ca href=\"https://www.buzzfeednews.com/stephaniesoteriou/glen-powell-puked-hidden-figures-performance\"\u003e\u003ci\u003eHidden Figures\u003c/i\u003e\u003c/a\u003e. It’s a romanticized, feel-good 'history' from 2016 about some of the women of color heroes of 1960s NASA. While they strived for all sorts of historical accuracy, they made one decision that ruined the entire movie: the characters don’t smoke. Look at any period footage of 1960s NASA control rooms (or anyplace else) and most folks are smoking.\"","id":"*********","number":"4","shareable":false},{"alt_text":"A woman in retro office attire raises her hand in a control room with several men in white shirts observing, map of Africa visible behind them","attribution":"20th Century Fox / Everett Collection","description":"","form":"image","header":"","id":"*********","images":{"mobile":{"height":"661","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/QnsXtP0Qv.jpg?resize=990:661","width":"990"},"original":{"height":"2054","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/QnsXtP0Qv.jpg","width":"3075"},"standard":{"height":"417","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/QnsXtP0Qv.jpg?resize=625:417","width":"625"},"wide":{"height":"661","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/QnsXtP0Qv.jpg?resize=990:661","width":"990"}},"shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyc2xp/\" target=\"_blank\"\u003ePrimary-Holiday-5586\u003c/a\u003e\u003c/p\u003e\u003cp\u003e\"Especially any time they are giving a lecture or interacting with a class or colleagues. You have to feel that the writers have never actually felt that there might be a benefit to be anywhere near a university.\u003c/p\u003e\u003cp\u003eA good example of the horror is Tilda Swinton's research conference presentation in \u003ci\u003eThree Thousand Years of Longing\u003c/i\u003e, which is otherwise quite enjoyable. She has a crowded room of experts in her field, and speaks to them like children who have never heard of her discipline. That is not what a career in adding to human knowledge looks like.\"\u003c/p\u003e\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/my07y9z/\" target=\"_blank\"\u003esruecker01\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"Any and every movie about teaching and/or high school.\"","id":"138856649","number":"5","shareable":false},{"alt_text":"A group of men, including a teacher in a tie and students in casual and formal attire, pose together in a classroom setting","attribution":"Buena Vista Pictures / Everett Collection","description":"","form":"image","header":"","id":"138857775","images":{"mobile":{"height":"668","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/_CbiNDp1j.jpg?resize=990:668","width":"990"},"original":{"height":"2023","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/_CbiNDp1j.jpg","width":"3000"},"standard":{"height":"421","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/_CbiNDp1j.jpg?resize=625:421","width":"625"},"wide":{"height":"668","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/_CbiNDp1j.jpg?resize=990:668","width":"990"}},"shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyp9jr/\" target=\"_blank\"\u003eSituationAshamed707\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"\u003ca href=\"https://www.buzzfeed.com/jeremyhayes/best-sports-films-to-stream\"\u003e\u003ci\u003eMoneyball\u003c/i\u003e\u003c/a\u003e. Great movie, but it ignores the fact that the A's had three frontline starting pitchers and Miguel Tejada playing shortstop at an MVP level. Makes you think the A's won all those games because Scott Hatteberg could really work the count.\"","id":"138856667","number":"6","shareable":false},{"alt_text":"Two actors on a baseball field; one wearing a visor and polo, the other in glasses with a jacket and shirt","attribution":"Columbia Pictures / Everett Collection","description":"","form":"image","header":"","id":"138857776","images":{"mobile":{"height":"646","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/bXNPb0RYI.jpg?resize=990:646","width":"990"},"original":{"height":"2037","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/bXNPb0RYI.jpg","width":"3120"},"standard":{"height":"408","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/bXNPb0RYI.jpg?resize=625:408","width":"625"},"wide":{"height":"646","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/bXNPb0RYI.jpg?resize=990:646","width":"990"}},"shareable":false},{"description":"\u003cp\u003e\"Our local police had a dozen boats on trailers in their parking lot to aid in search and rescue, but all ended up under 8 feet of water. All of our search and rescue was done by local residents who didn't evacuate.\"\u003c/p\u003e\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyipcp/\" target=\"_blank\"\u003eCalzonieman\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"Any movie or documentary on Hurricane Katrina that didn't lay the blame squarely on parish/city/state government for the horrible preparedness and/or response to the hurricane. I was at a hospital for five days before being evacuated by Chinook helicopters; I went through six days of preparedness meetings with local government officials prior to the storm hitting land, and not a single government entity showed up to help until the National Guard showed up after five days to get us out.\"","id":"138856653","number":"7","shareable":false},{"alt_text":"A person in sunglasses points towards a broad water channel flanked by stones and concrete barriers. Nearby houses are visible in the background","attribution":"O'Malley Creadon Productions /  Everett Collection","description":"","form":"image","header":"","id":"138857784","images":{"mobile":{"height":"557","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/q5FPI_43w.jpg?resize=990:557","width":"990"},"original":{"height":"1080","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/q5FPI_43w.jpg","width":"1920"},"standard":{"height":"352","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/q5FPI_43w.jpg?resize=625:352","width":"625"},"wide":{"height":"557","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/q5FPI_43w.jpg?resize=990:557","width":"990"}},"shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzpzbx/\" target=\"_blank\"\u003eQuirky_Living8292\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"Every single medical show, especially \u003ci\u003eBones\u003c/i\u003e and \u003ci\u003eGrey’s Anatomy\u003c/i\u003e. The absolute lack of nurses is hilarious. Doctors do not transport patients and rarely personally read radiology films. Most doctors specialize and do very little outside of their specialty. Nurses, ENTs (ear, nose, and throat doctors), respiratory therapists, and radiology techs do the bulk of the work. Also, you do not shock a flat line. The only two shows that are close to accurate are \u003ci\u003eER\u003c/i\u003e and \u003ci\u003eThe Pitt\u003c/i\u003e.\"","id":"138856661","number":"8","shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyd6oe/\" target=\"_blank\"\u003emytthewstew\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"\u003ci\u003eArgo\u003c/i\u003e, the movie, gives the US all the credit, but the Canadian government did the hard part.\"","id":"138856656","number":"9","shareable":false},{"alt_text":"Two men in suits stand in an office setting, with a U.S. emblem on the wall, suggesting a formal, governmental environment","attribution":"Warner Bros / Everett Collection","description":"","form":"image","header":"","id":"138857786","images":{"mobile":{"height":"660","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hjT32XlMh.jpg?resize=990:660","width":"990"},"original":{"height":"3744","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hjT32XlMh.jpg","width":"5616"},"standard":{"height":"417","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hjT32XlMh.jpg?resize=625:417","width":"625"},"wide":{"height":"660","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hjT32XlMh.jpg?resize=990:660","width":"990"}},"shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/my0f4pz/\" target=\"_blank\"\u003epokeysyd\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"Any moving about drilling for oil where oil comes gushing out of the well and everybody is happy. It’s called a blowout, it is very bad, and everything will be on fire in short order.\"","id":"138856659","number":"10","shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzgfu0/\" target=\"_blank\"\u003eDazzling-Astronaut88\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"While it was before my time, \u003ci\u003eWalking Tall\u003c/i\u003e (1973), the story of Sheriff Buford Pusser, is about events in the town I grew up in. I grew up with grandchildren of his deputies, and kids with last names that were associated with decades of criminal activity. The story/movie is all lies and nothing but lies. That dude was corrupt as anyone.\"","id":"138856662","number":"11","shareable":false},{"alt_text":"A man and woman smile at each other by a white fence. The woman wears a patterned skirt and cardigan. They stand in a leafy yard with a house in the background","attribution":"Bing Crosby Productions / Everett Collection","description":"","form":"image","header":"","id":"138857789","images":{"mobile":{"height":"747","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/oGqnef_-Z.jpg?resize=990:747","width":"990"},"original":{"height":"2264","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/oGqnef_-Z.jpg","width":"3000"},"standard":{"height":"472","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/oGqnef_-Z.jpg?resize=625:472","width":"625"},"wide":{"height":"747","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/oGqnef_-Z.jpg?resize=990:747","width":"990"}},"shareable":false},{"description":"\u003cp\u003e\"I'll give one example. At the time, I think there were about a dozen Trident submarines based in Bangor, Washington. At any given time, most of them are underway, standing by to launch their missiles if needed.\u003c/p\u003e\u003cp\u003eSo, if a rebel Russian general took over a missile base and threatened to launch missiles at the US, would you rush the already very short time to get the USS Alabama refitted from its last patrol and spend 12 days cruising across the Pacific, or would you give that task to one of the other subs that is already in position to launch before the Russian general even decided to rebel?\"\u003c/p\u003e\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxz0mjt/\" target=\"_blank\"\u003eghotiermann\u003c/a\u003e\u003c/p\u003e\u003cp\u003e\"\u003ci\u003eCrimson Tide\u003c/i\u003e is set in 1995. During the birthday party, Commander Hunter is watching the news when the newscaster states 'Threatened to use nuclear arms on the United States and Japan...should anyone, including the Russian Army, attempt to move in on him.' The missile being erected is a US Pershing II, which was eliminated in 1991.\"\u003c/p\u003e\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyabjz/\" target=\"_blank\"\u003egadget850\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"Submarine movies, especially \u003ci\u003eCrimson Tide\u003c/i\u003e. I was serving on the USS Alabama when the movie was made. Talking about all of the inaccuracies would take me a while.\"","id":"138856668","number":"12","shareable":false},{"alt_text":"Group of focused men in a submarine control room, surrounded by equipment and monitors, depicting an intense scene from a TV show or movie","attribution":"Buena Vista Pictures / Everett Collection","description":"","form":"image","header":"","id":"138857795","images":{"mobile":{"height":"667","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfrGFIgJL.jpg?resize=990:667","width":"990"},"original":{"height":"2086","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfrGFIgJL.jpg","width":"3094"},"standard":{"height":"421","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfrGFIgJL.jpg?resize=625:421","width":"625"},"wide":{"height":"667","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/hfrGFIgJL.jpg?resize=990:667","width":"990"}},"shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzmnhe/\" target=\"_blank\"\u003eFarmwife71\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"\u003ci\u003eTwister\u003c/i\u003e. You seriously cannot hold onto a pipe secured by a belt and not get sucked into the eye of a tornado.\"","id":"138856672","number":"13","shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxze2w5/\" target=\"_blank\"\u003ebevymartbc\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"\u003ci\u003eStudio 54\u003c/i\u003e, set in 1970s New York. It was a LOT more out of control than the movie portrayed.\"","id":"138856674","number":"14","shareable":false},{"alt_text":"People dancing in a lively nightclub with vibrant neon lights and a DJ booth. The atmosphere is energetic and bustling","attribution":"Zeitgeist Films / Everett Collection","description":"","form":"image","header":"","id":"138857798","images":{"mobile":{"height":"562","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/-QTG9i6ei.jpg?resize=990:562","width":"990"},"original":{"height":"1106","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/-QTG9i6ei.jpg","width":"1950"},"standard":{"height":"354","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/-QTG9i6ei.jpg?resize=625:354","width":"625"},"wide":{"height":"562","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/-QTG9i6ei.jpg?resize=990:562","width":"990"}},"shareable":false},{"description":"\u003cp\u003e\"For historical accuracy, best to read well researched books because a documentary usually has a single point of view. Multiple books give a fuller, and sometimes contradictory picture.\"\u003c/p\u003e\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzbipg/\" target=\"_blank\"\u003eRayBuc9882\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"I am a software developer, I have to check and fix vulnerabilities in code, and also have to deploy software in test environments. Every movie where someone just breaks into a system and puts virus into something is just not possible. Ask people who do penetration testing or hacking: you have to know the flaws, you have to locate them, it takes a while and things are not just waiting for you to upload/download. My favorite has to be the movie \u003ci\u003eIndependence Day\u003c/i\u003e — it’s a fun watch, but yeah, the guy is not going to create a virus, interface into a spaceship, and upload to it.\"","id":"138856665","number":"15","shareable":false},{"alt_text":"Group of focused people in a busy newsroom. Some wear glasses, others are in patterned shirts and suits, all looking intently at something off-camera","attribution":"20th Century Fox / Everett Collection","description":"","form":"image","header":"","id":"138857800","images":{"mobile":{"height":"665","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/FfXeBQoi-.jpg?resize=990:665","width":"990"},"original":{"height":"3471","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/FfXeBQoi-.jpg","width":"5166"},"standard":{"height":"420","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/FfXeBQoi-.jpg?resize=625:420","width":"625"},"wide":{"height":"665","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/FfXeBQoi-.jpg?resize=990:665","width":"990"}},"shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyrazc/\" target=\"_blank\"\u003eDaysOfParadise\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"All those stupid survivor movies; \u003ci\u003e127 Hours\u003c/i\u003e, \u003ci\u003eWild\u003c/i\u003e, \u003ci\u003eInto the Wild\u003c/i\u003e...these people were dumb at best and woefully misguided. We used them as horrible examples in real-life search and rescue (SAR) teams, with much jeering.\"","id":"138856657","number":"16","shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/my009zc/\" target=\"_blank\"\u003eSchemeSquare2152\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"Any scene depicting people with Alzheimers or any form of dementia. I worked in long-term care homes for 17 years.\"","id":"138856658","number":"17","shareable":false},{"alt_text":"Two people in a heated conversation on a porch. The woman wears a button-up dress and holds letters, while the man wears a striped shirt","attribution":"New Line Cinema / Everett Collection","description":"","form":"image","header":"","id":"138857811","images":{"mobile":{"height":"660","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/9_8107oJ1.jpg?resize=990:660","width":"990"},"original":{"height":"2048","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/9_8107oJ1.jpg","width":"3072"},"standard":{"height":"417","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/9_8107oJ1.jpg?resize=625:417","width":"625"},"wide":{"height":"660","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/9_8107oJ1.jpg?resize=990:660","width":"990"}},"shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyud4k/\" target=\"_blank\"\u003enotthatcousingreg\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"Every single movie made about broadcast news. I was in news for 20 years. It's not like anything you see on TV or in the movies.\"","id":"138856671","number":"18","shareable":false},{"alt_text":"A man with a mustache is dressed in a brown suit and striped tie, sitting at a news desk with clocks behind him displaying different time zones","attribution":"DreamWorks / Everett Collection","description":"","form":"image","header":"","id":"138857815","images":{"mobile":{"height":"660","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4uOB3ZED.jpg?resize=990:660","width":"990"},"original":{"height":"2000","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4uOB3ZED.jpg","width":"3000"},"standard":{"height":"417","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4uOB3ZED.jpg?resize=625:417","width":"625"},"wide":{"height":"660","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4uOB3ZED.jpg?resize=990:660","width":"990"}},"shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxygzf4/\" target=\"_blank\"\u003eNorth_Artichoke_6721\u003c/a\u003e\u003c/p\u003e\u003cp\u003e\"There was a movie — \u003ci\u003eNo Way Out\u003c/i\u003e, I think — that put a Metro stop at the Old Post Office Building, which later became Trump's hotel.\"\u003c/p\u003e\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyuuf6/\" target=\"_blank\"\u003eAndOneForMahler-\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"Any movie that features a high-speed chase in downtown Boston or any character who finds a parking place quickly in Boston. Although it would be really boring to watch the actors drive around for 25 minutes just to find a place to leave their car, so I get it.\"","id":"138856654","number":"19","shareable":false},{"alt_text":"Two men in a car, one driving and the other in the passenger seat, appear focused. The scene suggests a tense or serious moment from a TV or movie","attribution":"Apple TV+ / Everett Collection","description":"","form":"image","header":"","id":"138857813","images":{"mobile":{"height":"660","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/aU---cTpn.jpg?resize=990:660","width":"990"},"original":{"height":"2000","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/aU---cTpn.jpg","width":"3000"},"standard":{"height":"417","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/aU---cTpn.jpg?resize=625:417","width":"625"},"wide":{"height":"660","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/aU---cTpn.jpg?resize=990:660","width":"990"}},"shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzznqf/\" target=\"_blank\"\u003eMetella76\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"I watched \u003ci\u003eBackdraft\u003c/i\u003e with my great uncle, his two sons, and his son in law, who are firemen. Apparently, no actual firemen were consulted for that movie.\"","id":"138856664","number":"20","shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyg588/\" target=\"_blank\"\u003eDowntown_Share3802\u003c/a\u003e\u003c/p\u003e\u003cp\u003e\"Yeah. \u003ci\u003eSex and The City\u003c/i\u003e is a LIE.\"\u003c/p\u003e\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/my02r72/\" target=\"_blank\"\u003eFloridaGirlMary\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"As a New Yorker, seeing big, spacious apartments.\"","id":"138856652","number":"21","shareable":false},{"alt_text":"Group of women in stylish outfits, seated and looking attentive, possibly during a media event or panel","attribution":"New Line Cinema / Everett Collection","description":"","form":"image","header":"","id":"138857814","images":{"mobile":{"height":"620","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/8iTX9G2TG.jpg?resize=990:620","width":"990"},"original":{"height":"1690","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/8iTX9G2TG.jpg","width":"2700"},"standard":{"height":"391","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/8iTX9G2TG.jpg?resize=625:391","width":"625"},"wide":{"height":"620","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/8iTX9G2TG.jpg?resize=990:620","width":"990"}},"shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxzbtt6/\" target=\"_blank\"\u003eOtters64\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"\u003ci\u003eSecretariat\u003c/i\u003e. Good movie, but the financial troubles the farm was having in the movie were fantasy.\"","id":"138856677","number":"22","shareable":false},{"description":"\u003cp\u003e—\u003ca href=\"https://www.reddit.com/r/AskOldPeople/comments/1lc7lax/what_movie_got_it_really_wrong_and_you_know/mxyi3l9/\" target=\"_blank\"\u003eKathAlMyPal\u003c/a\u003e\u003c/p\u003e","form":"text","header":"\"\u003ci\u003eBonfire of the Vanities\u003c/i\u003e. Tom Hanks and Bruce Willis were woefully miscast. The movie was a travesty. The writers, directors and producers owe their cast and the author a big apology.\"","id":"138856676","number":"23","shareable":false},{"alt_text":"Man in a suit stands against a height chart, arms crossed, holding a police ID board for a mugshot","attribution":"Warner Bros. / Everett Collection","description":"","form":"image","header":"","id":"138857821","images":{"mobile":{"height":"659","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4WW206rF.jpg?resize=990:659","width":"990"},"original":{"height":"1998","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4WW206rF.jpg","width":"3000"},"standard":{"height":"416","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4WW206rF.jpg?resize=625:416","width":"625"},"wide":{"height":"659","url":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/subbuzz/j4WW206rF.jpg?resize=990:659","width":"990"}},"shareable":false},{"code":"\u003ciframe frameborder=\"0\" src=\"https://docs.google.com/forms/d/e/1FAIpQLScC1tuUArWt__h71P7X9rSCnAJqL29iB_AA292YElcwzIYikg/viewform\" width=\"100%\"\u003eLoading…\u003c/iframe\u003e","description":"","embed_domain":"https://embed.contagiousmedia.com","form":"embed","header":"Now, tell me: what's a movie that got a historical or cultural event really wrong? Maybe it's a biopic that just didn't do a historical figure justice, or a historical fiction movie that didn't capture an event's essence? Tell me in the form below, or post your response in the comments!","height":"725","id":"138856416","metadata":{"cls":{"height":{"desktop":730,"mobile":730},"width":{"desktop":600,"mobile":379}},"plugins":{"AnonymousForm":"{\"formId\":\"1iSDU2enhFpX1I0kLzvMN8snfhdi_w7ZDULssnMN8PEo\"}"}},"mobile_height":"0","mobile_safe":"1","mobile_width":"0","source":"anonymous-comments-google-form","sub_buzz_id":"138856416","sub_buzz_uri":"item-2miyitasdxi28","width":"1","shareable":false}],"summary_sub_buzz":[],"tags":["","black content","evergreen","movies","netflix","tv","TV Shows"],"title":"Old People Are Calling Out The Historical Movie And TV Mistakes That Made Them Scream At The Screen, Because They Actually Lived Through That Era","translated_buzzes":[],"trending_products":[],"uri":"movie-mistakes-about-real-events","user_id":"3647075","username":"abbyzinman","isCommentsPage":false,"isExplicitCommentsPage":false,"title_text":"Old People Are Calling Out The Historical Movie And TV Mistakes That Made Them Scream At The Screen, Because They Actually Lived Through That Era","description_text":"I guess we shouldn't trust Hollywood for a history lesson.","isShopping":false,"isCommerce":false,"isCommunity":false,"isAd":false,"isUS":false,"isBlog":false,"isCommunityPost":false,"isEnglish":true,"isModerated":true,"isPreview":false,"shouldHideBuzzSharing":false,"shouldShowPostContentOnly":false,"shouldHideRecircSection":false,"bfpFormats":[],"isQuiz":false,"isBFPQuiz":false,"disableAds":false,"picture":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/3-He8cvGR.jpg?crop=1245:830;3,0\u0026resize=1250:830","pictureAlt":"Two women in supermarket uniforms working and looking serious. Text criticizes \"The Help\" for inaccurately portraying Black domestic workers' trauma in the 1950s-60s","metadata":{"buzzId":"7894981","canonicalUrl":"https://www.buzzfeed.com/abbyzinman/movie-mistakes-about-real-events","SEOCanonicalUrl":"https://www.buzzfeed.com/abbyzinman/movie-mistakes-about-real-events","category":"Canada","description":"I guess we shouldn't trust Hollywood for a history lesson.","edition":"Canada","imageSrc":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/3-He8cvGR.jpg?crop=1245:830;3,0\u0026resize=1250:830","published":"1751913062","tags":["","black content","evergreen","movies","netflix","tv","TV Shows","stranger_things","the_help","argo","hidden_figures","bones","grey_s_anatomy","twister","independence_day","127_hours","13_going_on_30","moneyball","documentary","drama","horror","comedy","romance","paramount_plus","disney_plus","tv_shows"],"uri":"/abbyzinman/movie-mistakes-about-real-events","userId":"3647075","title":"23 Movie And TV Show Inaccuracies About Real Events","author":"Abby Zinman","canonicalShareUrl":"https://www.buzzfeed.com/abbyzinman/movie-mistakes-about-real-events","alternateLanguages":["en-ca"],"robots":"max-snippet:-1,max-image-preview:large,max-video-preview:-1","siteName":"BuzzFeed","articleOpinion":"false","authorFacebookPageURL":"","fbAppId":45075597673,"ogDescription":"I guess we shouldn't trust Hollywood for a history lesson.","ogImage":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/thumb/WjfFayDO4.jpg?crop=2999:1570;0,0%26downsize=1250:*","ogImageAlt":"Two women in supermarket uniforms working and looking serious. Text criticizes \"The Help\" for inaccurately portraying Black domestic workers' trauma in the 1950s-60s","ogTitle":"Older People Are Sharing The Worst On-Screen Inaccuracies About Real Events","pinDescription":"Old People Are Calling Out The Historical Movie And TV Mistakes That Made Them Scream At The Screen, Because They Actually Lived Through That Era","pinMedia":"https://img-mkr.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/3-He8cvGR.jpg?crop=1245:830;3,0\u0026resize=1250:830?template=pinterest-vertical-halftone-v1\u0026title=T2xkIFBlb3BsZSBBcmUgQ2FsbGluZyBPdXQgVGhlIEhpc3RvcmljYWwgTW92aWUgQW5kIFRWIE1pc3Rha2VzIFRoYXQgTWFkZSBUaGVtIFNjcmVhbSBBdCBUaGUgU2NyZWVuLCBCZWNhdXNlIFRoZXkgQWN0dWFsbHkgTGl2ZWQgVGhyb3VnaCBUaGF0IEVyYQ==\u0026color1=red","twitterAuthorAccount":"@https://x.com/aezinnyy","twitterDescription":"I guess we shouldn't trust Hollywood for a history lesson.","twitterMedia":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/22/thumb/WC2_q0loz.jpg?crop=3000:1500;0,0%26downsize=1250:*","twitterSiteAccount":"@BuzzFeedCanada","twitterTitle":"Older People Are Sharing The \"Painfully Obvious\" Mistakes They've Seen In Movies About Real-Life Events They Lived Through, And All I Can Say Is WOW","richPreview":true,"translationLinks":[]},"shareData":{"bluesky":{"title":"Old People Are Calling Out The Historical Movie And TV Mistakes That Made Them Scream At The Screen, Because They Actually Lived Through That Era"},"copy":{},"email":{"title":"Old People Are Calling Out The Historical Movie And TV Mistakes That Made Them Scream At The Screen, Because They Actually Lived Through That Era"},"facebook":{},"lineapp":{},"pinterest":{"title":"Old People Are Calling Out The Historical Movie And TV Mistakes That Made Them Scream At The Screen, Because They Actually Lived Through That Era","images":[{"url":"https://img-mkr.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/3-He8cvGR.jpg?crop=1245:830;3,0\u0026resize=1250:830?template=pinterest-vertical-halftone-v1\u0026title=T2xkIFBlb3BsZSBBcmUgQ2FsbGluZyBPdXQgVGhlIEhpc3RvcmljYWwgTW92aWUgQW5kIFRWIE1pc3Rha2VzIFRoYXQgTWFkZSBUaGVtIFNjcmVhbSBBdCBUaGUgU2NyZWVuLCBCZWNhdXNlIFRoZXkgQWN0dWFsbHkgTGl2ZWQgVGhyb3VnaCBUaGF0IEVyYQ==\u0026color1=red","alt":"Old People Are Calling Out The Historical Movie And TV Mistakes That Made Them Scream At The Screen, Because They Actually Lived Through That Era"}]},"sms":{},"snapchat":{},"twitter":{"title":"Older People Are Sharing The \"Painfully Obvious\" Mistakes They've Seen In Movies About Real-Life Events They Lived Through, And All I Can Say Is WOW","via":"https://x.com/aezinnyy"},"vk":{"image":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/17/20/thumb/3-He8cvGR.jpg?crop=1245:830;3,0\u0026resize=1250:830","title":"Old People Are Calling Out The Historical Movie And TV Mistakes That Made Them Scream At The Screen, Because They Actually Lived Through That Era"},"whatsapp":{}},"hasQuizSharedResult":false,"hasVideoSubbuzz":false,"isTrivia":false,"isPersonality":false,"isChecklist":false,"isTypeyGame":false,"bfpTheme":"","walmartBundle":null,"hasStudiosPromoUnit":false,"relatedTopicFeeds":{"topicPageTagFeeds":[],"remainingTagFeeds":[]},"topicPagePromo":null,"dynamicZones":{"related_recirc":{"id":400,"name":"related_content","display_name":"Related Content (More Like This)","metadata":{"curation_content_object_types":[{"id":"1","name":"post_promo"}]},"dynamic_feeds":[{"feed_id":327,"params":["buzz_id"]}],"items":[{"id":62685,"owner_id":null,"object_type":"post_promo","content":{"post":{"canonical_path":"/hannahmarder/insufferable-main-character-types-from-this-month","canonical_url":"https://www.buzzfeed.com/hannahmarder/insufferable-main-character-types-from-this-month","classification":{"edition":"US","section":"Internet Finds"},"country_code":"en-us","description":"I swear, the summer brings out the WORST type of entitled people.","destination_name":"buzzfeed","experiments":[],"id":"7900438","images":{"big":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/30/23/thumb/DrXM_mZJ4.jpg?crop=1245:830;2,0\u0026resize=710:472","wide":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/30/23/thumb/DrXM_mZJ4.jpg?crop=1249:440;0,195\u0026resize=710:250","small":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/30/23/thumb/DrXM_mZJ4.jpg?crop=1245:830;2,0\u0026resize=90:60","dblbig":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/30/23/thumb/DrXM_mZJ4.jpg?crop=1245:830;2,0\u0026resize=1250:830","square":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/30/23/thumb/a93xnWYpw.jpg?crop=2000:2000;0,0\u0026resize=625:625","dblwide":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/30/23/thumb/DrXM_mZJ4.jpg?crop=1249:440;0,195\u0026resize=1250:440","standard":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/30/23/thumb/DrXM_mZJ4.jpg?crop=1245:830;2,0\u0026resize=125:83","wide_16x9":"https://img.buzzfeed.com/buzzfeed-static/static/2025-06/30/23/thumb/DrXM_mZJ4.jpg?fill=1200:675","big_alt_text":"Woman stands on a car in traffic, wearing a backless dress. An arrow highlights the scene. Text reads: \"Saw this woman while stuck in traffic.\"","wide_alt_text":"Woman stands on a car in traffic, wearing a backless dress. An arrow highlights the scene. Text reads: \"Saw this woman while stuck in traffic.\"","small_alt_text":"Woman stands on a car in traffic, wearing a backless dress. An arrow highlights the scene. Text reads: \"Saw this woman while stuck in traffic.\"","dblbig_alt_text":"Woman stands on a car in traffic, wearing a backless dress. An arrow highlights the scene. Text reads: \"Saw this woman while stuck in traffic.\"","square_alt_text":"Woman stands on a car in traffic, wearing a backless dress. An arrow highlights the scene. Text reads: \"Saw this woman while stuck in traffic.\"","dblwide_alt_text":"Woman stands on a car in traffic, wearing a backless dress. An arrow highlights the scene. Text reads: \"Saw this woman while stuck in traffic.\"","standard_alt_text":"Woman stands on a car in traffic, wearing a backless dress. An arrow highlights the scene. Text reads: \"Saw this woman while stuck in traffic.\"","wide_16x9_alt_text":"Woman stands on a car in traffic, wearing a backless dress. An arrow highlights the scene. Text reads: \"Saw this woman while stuck in traffic.\""},"language":"en","title":"We Are In An Epidemic Of Self-Obsessed, Entitled People, And These 30 People From The Last Month Prove It","data_source":"feed_api","data_source_algorithm":["327"],"data_source_algorithm_version":["v1"]},"comments":{"count":127},"post_overrides":{},"content_reactions":{"reactions":[{"name":"🙄","count":53,"label":"🙄","proportion":0.4953271028037383},{"name":"😬","count":18,"label":"😬","proportion":0.16822429906542055},{"name":"😡","count":11,"label":"😡","proportion":0.102803738317757},{"name":"👍","count":9,"label":"👍","proportion":0.08411214953271028},{"name":"❤️","count":5,"label":"❤️","proportion":0.04672897196261682},{"name":"😂","count":5,"label":"😂","proportion":0.04672897196261682},{"name":"😭","count":4,"label":"😭","proportion":0.037383177570093455},{"name":"🤯","count":2,"label":"🤯","proportion":0.018691588785046728}],"total_reactions":107}},"sponsorships":[],"feed_source":{"id":327,"name":"default","display_name":"Default"},"created_at":1752451511.842321},{"id":62746,"owner_id":null,"object_type":"post_promo","content":{"post":{"canonical_path":"/kaylayandoli/random-people-ruining-relationships","canonical_url":"https://www.buzzfeed.com/kaylayandoli/random-people-ruining-relationships","classification":{"edition":"US","section":"Sex \u0026 Love"},"country_code":"en-us","description":"The way these people interfered...I swear to god.","destination_name":"buzzfeed","experiments":[],"id":"7905768","images":{"big":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/10/16/thumb/y7zu8gMtd.jpg?crop=2370:1580;24,0\u0026resize=710:472","wide":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/10/16/thumb/7zMlf6k13.jpg?crop=2170:764;253,264\u0026resize=710:250","small":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/10/16/thumb/y7zu8gMtd.jpg?crop=2370:1580;24,0\u0026resize=90:60","dblbig":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/10/16/thumb/y7zu8gMtd.jpg?crop=2370:1580;24,0\u0026resize=1250:830","square":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/10/16/thumb/UD9Z5-OV8.jpg?crop=1721:1721;513,0\u0026resize=625:625","dblwide":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/10/16/thumb/7zMlf6k13.jpg?crop=2170:764;253,264\u0026resize=1250:440","standard":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/10/16/thumb/y7zu8gMtd.jpg?crop=2370:1580;24,0\u0026resize=125:83","wide_16x9":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/10/16/thumb/y7zu8gMtd.jpg?fill=1200:675","big_alt_text":"Dennis Quaid and Lindsay Lohan in \"The Parent Trap\" (1998)","wide_alt_text":"Dennis Quaid and Lindsay Lohan in \"The Parent Trap\" (1998)","small_alt_text":"Dennis Quaid and Lindsay Lohan in \"The Parent Trap\" (1998)","dblbig_alt_text":"Dennis Quaid and Lindsay Lohan in \"The Parent Trap\" (1998)","square_alt_text":"Dennis Quaid and Lindsay Lohan in \"The Parent Trap\" (1998)","dblwide_alt_text":"Dennis Quaid and Lindsay Lohan in \"The Parent Trap\" (1998)","standard_alt_text":"Dennis Quaid and Lindsay Lohan in \"The Parent Trap\" (1998)","wide_16x9_alt_text":"Dennis Quaid and Lindsay Lohan in \"The Parent Trap\" (1998)"},"language":"en","title":"Folks Admitted How Their Loving Relationships Were Destroyed By Random People (With No Sex Involved), And My GOD I'm Boiling","data_source":"feed_api","data_source_algorithm":["327"],"data_source_algorithm_version":["v1"]},"comments":{"count":13},"post_overrides":{},"content_reactions":{"reactions":[{"name":"😬","count":14,"label":"😬","proportion":0.6363636363636364},{"name":"😡","count":4,"label":"😡","proportion":0.18181818181818185},{"name":"🙄","count":2,"label":"🙄","proportion":0.09090909090909093},{"name":"❤️","count":1,"label":"❤️","proportion":0.045454545454545456},{"name":"😂","count":1,"label":"😂","proportion":0.045454545454545456},{"name":"👍","count":0,"label":"👍","proportion":0},{"name":"😭","count":0,"label":"😭","proportion":0},{"name":"🤯","count":0,"label":"🤯","proportion":0}],"total_reactions":22}},"sponsorships":[],"feed_source":{"id":327,"name":"default","display_name":"Default"},"created_at":1752451518.354607},{"id":62725,"owner_id":null,"object_type":"post_promo","content":{"post":{"canonical_path":"/mikespohr/messy-celebrity-breakups-for-co-stars","canonical_url":"https://www.buzzfeed.com/mikespohr/messy-celebrity-breakups-for-co-stars","classification":{"edition":"US","section":"Celebrity"},"country_code":"en-us","description":"I know that whenever I make a big-budget movie with an incredibly sexy costar, I always remain faithful to my wife. But that's just me.","destination_name":"buzzfeed","experiments":[],"id":"7899952","images":{"big":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/09/19/thumb/RGzYlU0gC.jpg?crop=1245:830;3,0\u0026resize=710:472","wide":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/09/19/thumb/RGzYlU0gC.jpg?crop=1250:440;0,195\u0026resize=710:250","small":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/09/19/thumb/RGzYlU0gC.jpg?crop=1245:830;3,0\u0026resize=90:60","dblbig":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/09/19/thumb/RGzYlU0gC.jpg?crop=1245:830;3,0\u0026resize=1250:830","square":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/09/19/thumb/RGzYlU0gC.jpg?crop=830:830;210,0\u0026resize=625:625","dblwide":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/09/19/thumb/RGzYlU0gC.jpg?crop=1250:440;0,195\u0026resize=1250:440","standard":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/09/19/thumb/RGzYlU0gC.jpg?crop=1245:830;3,0\u0026resize=125:83","wide_16x9":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/09/19/thumb/RGzYlU0gC.jpg?fill=1200:675","big_alt_text":"Two people smiling and laughing, close together, with one looking down and the other looking directly at the camera","wide_alt_text":"Two people smiling and laughing, close together, with one looking down and the other looking directly at the camera","small_alt_text":"Two people smiling and laughing, close together, with one looking down and the other looking directly at the camera","dblbig_alt_text":"Two people smiling and laughing, close together, with one looking down and the other looking directly at the camera","square_alt_text":"Two people smiling and laughing, close together, with one looking down and the other looking directly at the camera","dblwide_alt_text":"Two people smiling and laughing, close together, with one looking down and the other looking directly at the camera","standard_alt_text":"Two people smiling and laughing, close together, with one looking down and the other looking directly at the camera","wide_16x9_alt_text":"Two people smiling and laughing, close together, with one looking down and the other looking directly at the camera"},"language":"en","title":"\"She's A Piece Of Trash\": 7 Extremely Messy Times Famous Actors Dumped Their Spouses For Costars","data_source":"feed_api","data_source_algorithm":["327"],"data_source_algorithm_version":["v1"]},"comments":{"count":49},"post_overrides":{},"content_reactions":{"reactions":[{"name":"😡","count":38,"label":"😡","proportion":0.4130434782608696},{"name":"🙄","count":24,"label":"🙄","proportion":0.2608695652173913},{"name":"👍","count":10,"label":"👍","proportion":0.10869565217391304},{"name":"❤️","count":9,"label":"❤️","proportion":0.09782608695652174},{"name":"🤯","count":4,"label":"🤯","proportion":0.043478260869565216},{"name":"😂","count":3,"label":"😂","proportion":0.03260869565217391},{"name":"😭","count":3,"label":"😭","proportion":0.03260869565217391},{"name":"😬","count":1,"label":"😬","proportion":0.010869565217391304}],"total_reactions":92}},"sponsorships":[],"feed_source":{"id":327,"name":"default","display_name":"Default"},"created_at":1752451523.898068},{"id":62873,"owner_id":null,"object_type":"post_promo","content":{"post":{"canonical_path":"/hannahmarder/terrible-wedding-guests-fs","canonical_url":"https://www.buzzfeed.com/hannahmarder/terrible-wedding-guests-fs","classification":{"edition":"US","section":"Weddings"},"country_code":"en-us","description":"Please do not serve yourself some wedding cake BEFORE it is cut. I can't believe I have to say this.","destination_name":"buzzfeed","experiments":[],"id":"7907212","images":{"big":"https://img.buzzfeed.com/buzzfeed-static/static/2024-10/23/2/enhanced/3da8e5455cb7/original-734-1729650831-3.jpg?crop=2988:1992;0,0\u0026resize=710:472","wide":"https://img.buzzfeed.com/buzzfeed-static/static/2024-10/23/2/enhanced/cc1ec67a2fe0/original-2161-1729650217-5.jpg?crop=1250:440;0,0\u0026resize=710:250","small":"https://img.buzzfeed.com/buzzfeed-static/static/2024-10/23/2/enhanced/3da8e5455cb7/original-734-1729650831-3.jpg?crop=2988:1992;0,0\u0026resize=90:60","dblbig":"https://img.buzzfeed.com/buzzfeed-static/static/2024-10/23/2/enhanced/3da8e5455cb7/original-734-1729650831-3.jpg?crop=2988:1992;0,0\u0026resize=1250:830","square":"https://img.buzzfeed.com/buzzfeed-static/static/2024-10/23/2/enhanced/3da8e5455cb7/original-704-1729650289-3.jpg?crop=2000:2000;0,0\u0026resize=625:625","dblwide":"https://img.buzzfeed.com/buzzfeed-static/static/2024-10/23/2/enhanced/cc1ec67a2fe0/original-2161-1729650217-5.jpg?crop=1250:440;0,0\u0026resize=1250:440","standard":"https://img.buzzfeed.com/buzzfeed-static/static/2024-10/23/2/enhanced/3da8e5455cb7/original-734-1729650831-3.jpg?crop=2988:1992;0,0\u0026resize=125:83","wide_16x9":"https://img.buzzfeed.com/buzzfeed-static/static/2024-10/23/2/enhanced/3da8e5455cb7/original-734-1729650831-3.jpg?fill=1200:675","big_alt_text":"RSVP card joke: Adding a cow (Tom), a chicken (Stan), and a fish (Carmen, exchange student) under \"Will attend\" with \"Adding in their 12-year-old kid and exchange student\" overlay text","wide_alt_text":"Text message requests an acquaintance to provide a place to change clothes after a wedding and order pizza. Man in suit with styled hair on the right","small_alt_text":"RSVP card joke: Adding a cow (Tom), a chicken (Stan), and a fish (Carmen, exchange student) under \"Will attend\" with \"Adding in their 12-year-old kid and exchange student\" overlay text","dblbig_alt_text":"RSVP card joke: Adding a cow (Tom), a chicken (Stan), and a fish (Carmen, exchange student) under \"Will attend\" with \"Adding in their 12-year-old kid and exchange student\" overlay text","square_alt_text":"Text message requests an acquaintance to provide a place to change clothes after a wedding and order pizza. Man in suit with styled hair on the right","dblwide_alt_text":"Text message requests an acquaintance to provide a place to change clothes after a wedding and order pizza. Man in suit with styled hair on the right","standard_alt_text":"RSVP card joke: Adding a cow (Tom), a chicken (Stan), and a fish (Carmen, exchange student) under \"Will attend\" with \"Adding in their 12-year-old kid and exchange student\" overlay text","wide_16x9_alt_text":"RSVP card joke: Adding a cow (Tom), a chicken (Stan), and a fish (Carmen, exchange student) under \"Will attend\" with \"Adding in their 12-year-old kid and exchange student\" overlay text"},"language":"en","title":"37 Terrible Wedding Guests Who Should've Been Uninvited","data_source":"feed_api","data_source_algorithm":["327"],"data_source_algorithm_version":["v1"]},"comments":{"count":16},"post_overrides":{},"content_reactions":{"reactions":[{"name":"🤯","count":4,"label":"🤯","proportion":0.3076923076923077},{"name":"😡","count":3,"label":"😡","proportion":0.2307692307692308},{"name":"😬","count":3,"label":"😬","proportion":0.2307692307692308},{"name":"❤️","count":1,"label":"❤️","proportion":0.07692307692307693},{"name":"😂","count":1,"label":"😂","proportion":0.07692307692307693},{"name":"🙄","count":1,"label":"🙄","proportion":0.07692307692307693},{"name":"👍","count":0,"label":"👍","proportion":0},{"name":"😭","count":0,"label":"😭","proportion":0}],"total_reactions":13}},"sponsorships":[],"feed_source":{"id":327,"name":"default","display_name":"Default"},"created_at":1752451527.965815},{"id":62466,"owner_id":null,"object_type":"post_promo","content":{"post":{"canonical_path":"/hannahmarder/brutally-honest-truths-women-dont-know-about-men","canonical_url":"https://www.buzzfeed.com/hannahmarder/brutally-honest-truths-women-dont-know-about-men","classification":{"edition":"US","section":"Internet Finds"},"country_code":"en-us","description":"\"When you call men trash, useless, pigs, etc, the only ones that hear it are the ones that are trying to do better by everyone. The trashy men do not care because what they are doing works for them.\"","destination_name":"buzzfeed","experiments":[],"id":"7904674","images":{"big":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/11/14/thumb/7ZHHxCiBQ.jpg?crop=1245:830;3,0\u0026resize=710:472","wide":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/11/15/thumb/YE3l1YN82.jpg?crop=2993:1054;4,0\u0026resize=710:250","small":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/11/14/thumb/7ZHHxCiBQ.jpg?crop=1245:830;3,0\u0026resize=90:60","dblbig":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/11/14/thumb/7ZHHxCiBQ.jpg?crop=1245:830;3,0\u0026resize=1250:830","square":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/11/15/thumb/xXJ0ziWd9.jpg?crop=2000:2000;0,0\u0026resize=625:625","dblwide":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/11/15/thumb/YE3l1YN82.jpg?crop=2993:1054;4,0\u0026resize=1250:440","standard":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/11/14/thumb/7ZHHxCiBQ.jpg?crop=1245:830;3,0\u0026resize=125:83","wide_16x9":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/11/14/thumb/7ZHHxCiBQ.jpg?fill=1200:675","big_alt_text":"Annoyed man captioned \"You are not 'performing emotional labor'\" and eye close-up with text about not liking long fake eyelashes","wide_alt_text":"Annoyed man captioned \"You are not 'performing emotional labor'\" and eye close-up with text about not liking long fake eyelashes","small_alt_text":"Annoyed man captioned \"You are not 'performing emotional labor'\" and eye close-up with text about not liking long fake eyelashes","dblbig_alt_text":"Annoyed man captioned \"You are not 'performing emotional labor'\" and eye close-up with text about not liking long fake eyelashes","square_alt_text":"Annoyed man captioned \"You are not 'performing emotional labor'\" and eye close-up with text about not liking long fake eyelashes","dblwide_alt_text":"Annoyed man captioned \"You are not 'performing emotional labor'\" and eye close-up with text about not liking long fake eyelashes","standard_alt_text":"Annoyed man captioned \"You are not 'performing emotional labor'\" and eye close-up with text about not liking long fake eyelashes","wide_16x9_alt_text":"Annoyed man captioned \"You are not 'performing emotional labor'\" and eye close-up with text about not liking long fake eyelashes"},"language":"en","title":"33 \"Brutal Truths\" Men Say They Could NEVER Reveal To Women's Faces","data_source":"feed_api","data_source_algorithm":["327"],"data_source_algorithm_version":["v1"]},"comments":{"count":273},"post_overrides":{},"content_reactions":{"reactions":[{"name":"🙄","count":143,"label":"🙄","proportion":0.5088967971530249},{"name":"👍","count":69,"label":"👍","proportion":0.24555160142348753},{"name":"😡","count":32,"label":"😡","proportion":0.11387900355871886},{"name":"❤️","count":14,"label":"❤️","proportion":0.0498220640569395},{"name":"😂","count":9,"label":"😂","proportion":0.03202846975088968},{"name":"😬","count":5,"label":"😬","proportion":0.017793594306049824},{"name":"🤯","count":5,"label":"🤯","proportion":0.017793594306049824},{"name":"😭","count":4,"label":"😭","proportion":0.014234875444839855}],"total_reactions":281}},"sponsorships":[],"feed_source":{"id":327,"name":"default","display_name":"Default"},"created_at":1752365055.422759},{"id":62459,"owner_id":null,"object_type":"post_promo","content":{"post":{"canonical_path":"/michaelabramwell/donald-trump-viral-comments-on-texas-late-flood-alerts","canonical_url":"https://www.buzzfeed.com/michaelabramwell/donald-trump-viral-comments-on-texas-late-flood-alerts","classification":{"edition":"US","section":"In the News"},"country_code":"en-us","description":"\"He f —ked up. They f —ked up. And the kids died. So he’s trying to act like it’s an offensive question because he thinks you are stupid.\"","destination_name":"buzzfeed","experiments":[],"id":"7907357","images":{"big":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/12/15/thumb/4Q_DXPR9u.jpg?crop=1245:830;3,0\u0026resize=710:472","wide":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/12/15/thumb/3cZeLiWQVG.jpg?crop=2993:1054;4,0\u0026resize=710:250","small":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/12/15/thumb/4Q_DXPR9u.jpg?crop=1245:830;3,0\u0026resize=90:60","dblbig":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/12/15/thumb/4Q_DXPR9u.jpg?crop=1245:830;3,0\u0026resize=1250:830","square":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/12/15/thumb/473mXw_o2.jpg?crop=2000:2000;0,0\u0026resize=625:625","dblwide":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/12/15/thumb/3cZeLiWQVG.jpg?crop=2993:1054;4,0\u0026resize=1250:440","standard":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/12/15/thumb/4Q_DXPR9u.jpg?crop=1245:830;3,0\u0026resize=125:83","wide_16x9":"https://img.buzzfeed.com/buzzfeed-static/static/2025-07/12/15/thumb/4Q_DXPR9u.jpg?fill=1200:675","big_alt_text":"Left: Person looking at flooding with a U.S. flag. Right: Man wearing a USA cap. Overlay: Tweet about a mishandled crisis and its tragic consequences","wide_alt_text":"Left: Person looking at flooding with a U.S. flag. Right: Man wearing a USA cap. Overlay: Tweet about a mishandled crisis and its tragic consequences","small_alt_text":"Left: Person looking at flooding with a U.S. flag. Right: Man wearing a USA cap. Overlay: Tweet about a mishandled crisis and its tragic consequences","dblbig_alt_text":"Left: Person looking at flooding with a U.S. flag. Right: Man wearing a USA cap. Overlay: Tweet about a mishandled crisis and its tragic consequences","square_alt_text":"Left: Person looking at flooding with a U.S. flag. Right: Man wearing a USA cap. Overlay: Tweet about a mishandled crisis and its tragic consequences","dblwide_alt_text":"Left: Person looking at flooding with a U.S. flag. Right: Man wearing a USA cap. Overlay: Tweet about a mishandled crisis and its tragic consequences","standard_alt_text":"Left: Person looking at flooding with a U.S. flag. Right: Man wearing a USA cap. Overlay: Tweet about a mishandled crisis and its tragic consequences","wide_16x9_alt_text":"Left: Person looking at flooding with a U.S. flag. Right: Man wearing a USA cap. Overlay: Tweet about a mishandled crisis and its tragic consequences"},"language":"en","title":"Donald Trump Was Asked About His Message To Texas Families Who Are Angry About Late Flood Alerts, And His Response Is Going Viral For Being \"Sick Beyond Belief\"","data_source":"feed_api","data_source_algorithm":["327"],"data_source_algorithm_version":["v1"]},"comments":{"count":498},"post_overrides":{},"content_reactions":{"reactions":[{"name":"😡","count":198,"label":"😡","proportion":0.7764705882352941},{"name":"😭","count":12,"label":"😭","proportion":0.04705882352941176},{"name":"🙄","count":11,"label":"🙄","proportion":0.043137254901960784},{"name":"😬","count":9,"label":"😬","proportion":0.03529411764705882},{"name":"👍","count":8,"label":"👍","proportion":0.03137254901960784},{"name":"❤️","count":6,"label":"❤️","proportion":0.02352941176470588},{"name":"😂","count":6,"label":"😂","proportion":0.02352941176470588},{"name":"🤯","count":5,"label":"🤯","proportion":0.0196078431372549}],"total_reactions":255}},"sponsorships":[],"feed_source":{"id":327,"name":"default","display_name":"Default"},"created_at":1752365059.689823}],"next":"https://api.buzzfeed.com/feed-api/v1/render/zones/400?after=ZmVlZF9pZD0zMjc7bGl2ZV9jdXJzb3I9c2hvd19hdCUzRDE3NTIzNjUwNTIuOTMzODE2JTNCaWQlM0Q2MjQ1OTtkZWR1cD1BUUFBQUFBQUFBQUFBQUFBT2pBQUFBRUFBQUFBQUFVQUVBQUFBUHZ6QXZUZDlBWDFHdldaOVElM0QlM0Q\u0026page_size=6"}}},"giftguideHeader":{},"header":{"html":"","css":"","js":"https://www.buzzfeed.com/static-assets/bf-header-ui/buzzfeed/app.601f2d19b55ed40fb3a8.js"},"subbuzzData":{"assets":{"i18n":"{\"ABOUT\":\"About\",\"ABOUT_PAGE_TITLE\":\"About BuzzFeed Community\",\"ABOUT_US\":\"About Us\",\"ADD_TEXT_IMAGE_BOTH\":\"Add text, image, or both\",\"ADD_YOURS\":\"Add Yours!\",\"ADD_YOUR_VIDEO\":\"Add Your Video\",\"ADVERTISE_WITH_BUZZFEED\":\"Advertise with BuzzFeed\",\"ADVERTISE_WITH_URL\":\"https:\\/\\/advertise.buzzfeed.com\",\"ADVERTISEMENT\":\"Advertisement\",\"AD_DISCLAIMER\":\"This is a personal, non-sponsored post by a member of BuzzFeed's ad content team.\",\"AGAIN\":\"Again!\",\"AGAIN_SPAIN\":\"\",\"ALL_CONTENT_TYPES\":\"All content types\",\"ALL_POSTS\":\"All Posts\",\"ALREADY_REACT\":\"Oops! It looks like you've already used that reaction on this post.\",\"ATTACH_IMAGE\":\"Attach Image\",\"AUTOCOMPLETE_INSTRUCTIONS\":\"Use up and down arrows to browse suggestions when available and enter to select.\",\"BACK\":\"Back\",\"BRANDS_PAGE_TITLE\":\"Community Brand Guidelines\",\"BREAKING\":\"Breaking\",\"BREAKING_NEWS\":\"Breaking News\",\"BUG-BOUNTY-PROGRAM\":\"HackerOne Submission\",\"BUZZFEED_COM\":\"buzzfeed.com\",\"BUZZFEED_HOME\":\"BuzzFeed Home\",\"BUZZFEED_INC\":\"BuzzFeed, Inc\",\"BUZZFEED_QUIZZES\":\"BuzzFeed Quizzes\",\"BUZZFEED_REPORTING_TO_YOU\":\"BuzzFeed, Reporting To You\",\"BUZZFEED_STAFF\":\"BuzzFeed Staff\",\"BY\":\"By\",\"CLEAR_SEARCH\":\"Clear Search\",\"CLICK_FOR_MORE\":\"Click for More...\",\"CLICK_TO_REVEAL\":\"Click to reveal\",\"CLOSE\":\"Close\",\"CLOSE_THIS_MODAL\":\"Close this modal\",\"COMMENTS\":\"Comments\",\"COMMERCE_DISCLAIMER\":\"We hope you love our recommendations! Some may have been sent as samples, but all were independently selected by our editors. Just FYI, BuzzFeed and its\\u00a0publishing\\u00a0partners may collect a share of sales and\\/or other compensation from the links on this page.\",\"COMMERCE_DISCLAIMER_PARTNERSHIP\":\"We hope you love the products we recommend! All of them were independently selected by our editors. Just so you know, BuzzFeed collects a share of sales and\\/or other compensation from the links on this page. Oh, and FYI \\u2014 prices are accurate and items in stock as of time of publication.\",\"COMMERCE_SEO_ACCESSORIES\":\"Looking for more great accessories? From the \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/anamariaglavan\\/the-best-places-to-buy-glasses-online\\\"\u003ebest places to buy glasses online\u003c\\/a\u003e to the \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/katyherman\\/best-purses-on-amazon-2018\\\"\u003ebest purses on Amazon\u003c\\/a\u003e, we\\u2019ve got you covered. \",\"COMMERCE_SEO_BEDROOM\":\"Want more for your bedroom? Check out the \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/sallykaplan\\/here-are-the-best-places-to-buy-your-bedding\\\"\u003ebest bedding\u003c\\/a\u003e you can get online, our favorite \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/elizabethlilly\\/the-best-mattresses-you-can-get-on-amazon\\\"\u003emattresses you can get on Amazon\u003c\\/a\u003e, the \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/emmamcanaw\\/the-best-bed-frames-you-can-get-on-amazon\\\"\u003ebest bed frames to get on Amazon\u003c\\/a\u003e if you need somewhere to put that new mattress, and the \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/anamariaglavan\\/the-best-pillows-you-can-get-on-amazon\\\"\u003ebest pillows on Amazon\u003c\\/a\u003e to finish off your new bedroom setup.\",\"COMMERCE_SEO_CLEANING_ORGANIZATION\":\"Make your life even tidier. Check out the \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/maitlandquitmeyer\\/natural-home-cleaners-people-actually-swear-by\\\"\u003ebest all-natural cleaning products\u003c\\/a\u003e for your home, the \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/katyherman\\/bathroom-cleaning-products-people-actually-swear-by\\\"\u003ebest bathroom cleaning products\u003c\\/a\u003e, \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/yiyang\\/organization-products\\\"\u003eorganization products\u003c\\/a\u003e perfect for neat freaks, and, of course, our \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/nataliebrown\\/ultimate-home-cleaning-guide-kitchen-bathroom-bedroom\\\"\u003eultimate guide to cleaning\u003c\\/a\u003e every single room in your house.\",\"COMMERCE_SEO_CLOTHING\":\"Want more? Check out our favorite \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/maitlandquitmeyer\\/the-best-places-to-buy-inexpensive-clothes-online\\\"\u003eonline clothing stores\u003c\\/a\u003e for all your shopping needs, perfect places to shop for \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/jessicamisener\\/30-flirty-and-surviving\\\"\u003eclothes if you\\u2019re in your 30s\u003c\\/a\u003e, plus the best places to \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/jennifertonti\\/best-places-to-order-custom-t-shirts-online\\\"\u003eorder custom t-shirts online\u003c\\/a\u003e and the best \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/betsydickerson\\/best-places-to-buy-petite-clothing\\\"\u003eclothing stores for petite sizes\u003c\\/a\u003e. Still not satisfied? Check out all of our \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/shopping\\/clothing\\\"\u003eclothing\u003c\\/a\u003e content for even more.\",\"COMMERCE_SEO_FURNITURE_DECOR\":\"Looking for more stuff to help make your house a home? Check out the \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/katyherman\\/best-cheap-furniture-online-2018\\\"\u003ebest places to buy inexpensive furniture\u003c\\/a\u003e online, the \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/elenamgarcia\\/the-best-places-to-buy-a-sofa-online\\\"\u003ebest places to buy couches\u003c\\/a\u003e online, \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/mallorymcinnis\\/totally-underrated-places-to-shop-for-home-decor-online\\\"\u003ecute home decor\u003c\\/a\u003e you\\u2019ll wish you knew about sooner, or check out all of our \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/shopping\\/home\\\"\u003ehome\u003c\\/a\u003e content for even more great ideas.\",\"COMMERCE_SEO_GENERIC_AMAZON\":\"Looking for more great Amazon finds? Check out some of our favorite cheap \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/jeffbarron\\/the-best-things-you-can-buy-on-amazon-under-25\\\"\u003ethings to buy on Amazon\u003c\\/a\u003e, some of the \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/betsydickerson\\/amazingly-weird-products-from-amazon\\\"\u003eweirdest things on Amazon\u003c\\/a\u003e you might actually want, or read through all the rest of our incredible \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/shopping\\/amazon\\\"\u003eAmazon product recommendations\u003c\\/a\u003e.\",\"COMMERCE_SEO_GIFTS\":\"Find the perfect gift for any occasion, from  \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/yiyang\\/best-white-elephant-gifts-under-20\\\"\u003ewhite elephant gift\u003c\\/a\u003e exchanges to \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/nataliebrown\\/teacher-gifts-that-actual-teachers-loved-and-swear-by\\\"\u003egifts for teachers\u003c\\/a\u003e, \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/samanthawieder\\/original-first-apartment-housewarming-gifts\\\"\u003ehousewarming gifts\u003c\\/a\u003e to \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/yiyang\\/things-that-totally-belong-on-your-birthday-wish-list\\\"\u003ebirthday gifts\u003c\\/a\u003e, and even the perfect \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/sarahhan\\/best-places-to-order-gift-baskets-online\\\"\u003egift basket\u003c\\/a\u003e if you\\u2019re truly stumped. Plus, check out the BuzzFeed \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/giftguide\\\"\u003egift guide\u003c\\/a\u003e for even more incredible gifting ideas.\",\"COMMERCE_SEO_HAIR_PRODUCTS\":\"Need more for your mane? We\\u2019ve got products to give you the \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/ignaciafulcher\\/amazing-products-thatll-make-your-hair-silky-af\\\"\u003esilkiest hair ever\u003c\\/a\u003e, solutions for \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/betsydickerson\\/products-for-dry-or-damaged-hair\\\"\u003edry or damaged hair\u003c\\/a\u003e, and incredible \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/ignaciafulcher\\/hair-products-that-people-with-short-hair-swear-by\\\"\u003eproducts for short hair\u003c\\/a\u003e. Plus, the \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/brittaneytrent\\/natural-looking-wigs-you-wont-believe-are-from-amazon\\\"\u003ebest wigs\u003c\\/a\u003e for anyone who\\u2019d rather improve their \\u2018do that way. Or check out all of our \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/shopping\\/beauty\\\"\u003ebeauty\u003c\\/a\u003e content for even more great stuff for your hair, skin, and more.\",\"COMMERCE_SEO_SEX_TOYS\":\"Looking for more ~pleasure~? Check out the \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/betsydickerson\\/the-best-sex-toys-on-amazon\\\"\u003ebest sex toys you can get on Amazon\u003c\\/a\u003e, the best places to \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/harperhendrickson\\/best-places-to-buy-sex-toys-online-2018\\\"\u003ebuy all kinds of sex toys online\u003c\\/a\u003e, the \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/emmyf\\/best-vibrators-you-can-buy-online\\\"\u003ebest vibrators you can get online\u003c\\/a\u003e, and \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/harperhendrickson\\/sex-toys-for-anyone-who-wants-to-squirt-their-way-out-of-a\\\"\u003esex toys that will make you squirt\u003c\\/a\u003e.\",\"COMMERCE_SEO_SHOES\":\"Want more? Check out the \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/mallorymcinnis\\/choose-some-shoes\\\"\u003ebest places to buy shoes online\u003c\\/a\u003e, or the \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/emmamcanaw\\/shoes-thatll-actually-fit-people-with-wide-feet\\\"\u003ebest shoes for wide feet\u003c\\/a\u003e if you need a little extra room.\",\"COMMERCE_SEO_SKIN_PRODUCTS\":\"Need more for your skin? Check out \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/sarahhan\\/korean-skincare-products-youll-wish-youd-known-about\\\"\u003eKorean skincare products\u003c\\/a\u003e you need to know, \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/emmamcanaw\\/cheap-skincare-products-that-are-a-lot-better-than\\\"\u003echeap skincare\u003c\\/a\u003e products that are even better than luxury brands, products for an \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/abbykass\\/23-products-designed-to-help-you-get-a-more-even-skin-tone\\\"\u003eeven skin tone\u003c\\/a\u003e or to deal with \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/elenamgarcia\\/products-for-people-with-acne-scars\\\"\u003eacne scars\u003c\\/a\u003e, or check out all of our \u003ca href=\\\"https:\\/\\/www.buzzfeed.com\\/shopping\\/beauty\\\"\u003ebeauty\u003c\\/a\u003e content for even more great stuff for your hair, skin, and more.\",\"COMMERCE_SEO_SWIMWEAR\":\"Want to keep looking? Check out our favorite places to buy the \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/elenamgarcia\\/best-places-to-buy-bathing-suits-online\\\"\u003ebest bathing suits online\u003c\\/a\u003e, or places to buy the \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/anamariaglavan\\/the-best-places-to-buy-a-bikini-online\\\"\u003ebest bikinis online\u003c\\/a\u003e.\",\"COMMERCE_SEO_TRAVEL\":\"Wanderlust not satisfied yet? Check out more travel products, like the \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/elenamgarcia\\/best-carry-on-bags-you-can-get-on-amazon\\\"\u003ebest carry-on bags you can get on Amazon\u003c\\/a\u003e, \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/elenamgarcia\\/ways-to-make-a-long-flight-so-much-better\\\"\u003etravel accessories for long flights\u003c\\/a\u003e, and the \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/elizabethlilly\\/best-places-to-buy-luggage-online\\\"\u003ebest places to buy luggage online\u003c\\/a\u003e.\",\"COMMERCE_SEO_UNDERWEAR\":\"Looking for more? Check out our picks for the places that sell the \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/sallykaplan\\/here-are-the-best-places-to-get-your-underwear\\\"\u003ebest underwear for women \u003c\\/a\u003eonline, the \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/anamariaglavan\\/the-best-places-to-buy-bras-online\\\"\u003ebest places to buy bras\u003c\\/a\u003e online, or our favorite places to \u003ca target=\\\"_blank\\\" href=\\\"https:\\/\\/www.buzzfeed.com\\/bekoconnell\\/best-places-cheap-lingerie-online\\\"\u003ebuy affordable lingerie\u003c\\/a\u003e online.\",\"COMMUNITY_BLURB\":\"BuzzFeed Community is a hub for BuzzFeeders to create awesome quizzes and posts that people love. Make your own,\",\"COMMUNITY_BROWSE\":\"or browse what other people are making\",\"COMMUNITY_CTA_COPY\":\"Post your own awesome lists and creations and share them with the BuzzFeed Community.\",\"COMMUNITY_CTA_TEXT\":\"Join Us!\",\"COMMUNITY_PAGE_TITLE\":\"BuzzFeed Community\",\"COMPANY\":\"Company\",\"CONFIDENTIAL_TIP\":\"Got a confidential tip? \u003ca href=\\\"https:\\/\\/contact.buzzfeed.com\\\"\u003eSubmit it here\u003c\\/a\u003e.\",\"CONNECT_WITH\":\"Connect With\",\"CONSENT-PREFERENCES\":\"Consent Preferences\",\"CONSENT_CONTINUE\":\"Continue without Opting Out\",\"CONSENT_OPT_OUT\":\"Opt Out\",\"CONTACT_AT_EMAIL\":\"Contact {{ display_name }} at \u003ca href=\\\"mailto:{{ email }}\\\"\u003e{{ email }}\u003c\\/a\u003e.\",\"CONTACT_REPORTER\":\"Contact this reporter at\",\"CONTENT_NO_LONGER_SUPPORTED\":\"Sorry, this content is \u003ci\u003eno longer\u003c\\/i\u003e supported.\",\"CONTINUE\":\"Continue\",\"COOKIE_INFO\":\"Cookie Info\",\"COOKIE_LANGUAGE_PROMPT_MESSAGE\":\"This BuzzFeed homepage is tailored for our readers in {}.\",\"COPYRIGHT_BUZZFEED_INC\":\"Copyright BuzzFeed, Inc. All rights reserved.\",\"COPY_LINK\":\"Copy Link\",\"COPY_LINK_SHARE\":\"Copy Link\",\"COPY_POLL_LINK\":\"Copy Poll Link\",\"COPY_QUIZ_LINK\":\"Copy Quiz Link\",\"CORRECT\":\"Correct!\",\"CUTE\":\"Cute\",\"DASHBOARD\":\"Dashboard\",\"DAYS_AGO\":\"{} days ago\",\"DAY_AGO\":\"{} day ago\",\"DEVELOPING\":\"Developing story\",\"DOWNLOAD_PDF\":\"Download PDF\",\"EMAIL\":\"Email\",\"EMAIL_ADDRESS\":\"Email Address\",\"ENTER_TEXT\":\"Enter text...\",\"ENTER_VALID_EMAIL\":\"Please enter a valid email address\",\"ERROR\":\"Error\",\"FAIL\":\"Fail\",\"FAVORITE\":\"Favorite\",\"FEATURED_POSTS\":\"Featured Posts\",\"FEATURES\":\"Features\",\"FOLLOW_ON_TWITTER\":\"Follow On Twitter\",\"FOLLOW_US_ON\":\"Follow us on\",\"FOLLOW_US_ON_TWITTER\":\"Follow us on Twitter\",\"FOLLOW_US_ON_TWITTER_NEWS\":\"\",\"FULL_VENDOR_LIST\":\"See full vendor list\",\"GDPR_PREFERENCES\":\"GDPR Preferences\",\"GET_FIRST_EMAIL\":\"Stay tuned for your first newsletter!\",\"GET_MORE_NEWS\":\"Get More News\",\"GET_OUR_AWARD_WINNING_NEWS_APP\":\"Get our award-winning News App!\",\"GET_OUR_NEWS_APP\":\"Get our News App!\",\"GIVE_MORE_FEEDBACK\":\"Give More Feedback\",\"GOT_A_CONFIDENTIAL_TIP\":\"Got a confidential tip?\",\"GOT_A_TIP\":\"Got a tip?\",\"GO_BACK_AND_VOTE\":\"Go Back And Vote\",\"GO_TO_THE_RECIPE\":\"Go to the recipe!\",\"GRAPHIC_IMAGE\":\"This image is graphic\",\"GREAT\":\"Great!\",\"GUESS\":\"Guess\",\"GUIDELINES_PAGE_TITLE\":\"BuzzFeed Community Guidelines\",\"HELP\":\"Help and FAQ\",\"HEY_BUZZFEED_COMMUNITY\":\"Hey BuzzFeed Community!\",\"HOURS_AGO\":\"{} hours ago\",\"HOUR_AGO\":\"{} hour ago\",\"IMAGE\":\"Image\",\"IMAGE_UPLOADING_FAILED\":\"Image uploading failed.\",\"I_ACCEPT\":\"I accept\",\"I_AGREE_TO_THE_TERMS\":\"I agree to the \u003ca href=\\\"\\/about\\/useragreement\\\" target=\\\"_blank\\\"\u003eterms\u003c\\/a\u003e\",\"I_DO_NOT_ACCEPT\":\"I do not accept\",\"I_GIVE_UP\":\"I give up!\",\"I_GOT_CORRECT_OUT_OF_TOTAL\":\"I got \u003c%=correct%\u003e out of \u003c%=total%\u003e! How do you measure up?\",\"I_GOT_X\":\"I got \u003c%=\u0026value%\u003e!\",\"I_VERB_OUT_OF_TOTAL\":\"I \u003c%=\u0026verb%\u003e \u003c%=selected%\u003e out of \u003c%=total%\u003e \u003c%=\u0026subject%\u003e\",\"I_VOTED\":\"I voted \u003c%=\u0026vote%\u003e! \\\\\\\"\u003c%=\u0026question%\u003e\\\\\\\"\",\"INSPIRATION_ON_PINTEREST\":\"Inspiration on Pinterest\",\"JOIN_COMMUNITY\":\"Join the Community\",\"JUST_NOW\":\"just now\",\"LAST_UPDATED_ON\":\"Last updated on\",\"LATEST\":\"Latest\",\"LATEST_POSTS_FROM\":\"Latest Posts From {{ name }}\",\"LEADERBOARD\":\"Leaderboard\",\"LEADERBOARD_PAGE_TITLE\":\"BuzzFeed Community Leaderboard\",\"LEARN_MORE\":\"Learn more\",\"LEFT\":\"Left\",\"LEGITIMATE_INTEREST_PURPOSES\":\"Legitimate Interest Purposes\",\"LET_US_KNOW_HOW_WE_CAN_MAKE_THE_NEW_HOMEPAGE_BETTER\":\"Let us know how we can make the new homepage better.\",\"LIKE_ON_FACEBOOK\":\"Like On Facebook\",\"LIKE_US_ON\":\"Like us on \",\"LIKE_US_ON_FACEBOOK\":\"Like us on Facebook\",\"LIKE_US_ON_FACEBOOK_NEWS\":\"\",\"LINE\":\"\",\"LINK_COPIED\":\"Link Copied!\",\"LIVE\":\"LIVE\",\"LOAD_MORE\":\"Load more\",\"LOGO\":\"logo\",\"LOG_IN_OR_SIGN_UP_TO_CREATE_YOUR_OWN_POSTS\":\"\u003ca href=\\\"\\/signin\\\" data-bfa=\\\"@a:vertical-more;@d:login;\\\"\u003eLog in\u003c\\/a\u003e or \u003ca href=\\\"\\/signup\\\" data-bfa=\\\"@a:vertical-more;@d:signup;\\\"\u003esign up\u003c\\/a\u003e to create your own posts.\",\"LOG_IN_TO_BUZZFEED\":\"Log In To BuzzFeed\",\"LOG_OUT\":\"Log Out\",\"LOL\":\"LOL\",\"MAKE_A_POST\":\"Make A Post!\",\"MAKE_IT_YOUR_DEFAULT\":\"Make it your default.\",\"MAKE_POST\":\"Make your own post!\",\"MENU_ABOUT\":\"About\",\"MENU_CONTACT\":\"Contact\",\"MENU_JOBS\":\"Jobs\",\"MENU_MORE\":\"More\",\"MENU_NEWS\":\"News\",\"MENU_PRESS\":\"Press\",\"MENU_PRIVACY\":\"Privacy\",\"MENU_TERMS\":\"User Terms\",\"META_DESCRIPTION\":\"BuzzFeed News has breaking stories and original reporting on politics, world news, social media, viral trends, health, science, technology, entertainment, and LGBTQ issues.\",\"META_DESCRIPTION_ANIMALS\":\"All the best animals on the internet. The snuggle is real.\",\"META_DESCRIPTION_HOME\":\"BuzzFeed has breaking news, vital journalism, quizzes, videos, celeb news, Tasty food videos, recipes, DIY hacks, and all the trending buzz you\\u2019ll want to share with your friends. Copyright BuzzFeed, Inc. All rights reserved.\",\"META_DESCRIPTION_HOME_SPAIN\":\"\",\"META_TITLE\":\"BuzzFeed News | Breaking News | Original Reporting | News Analysis\",\"META_TITLE_ANIMALS\":\"Animal Pictures and Videos - Adorably from BuzzFeed\",\"MINUTES\":\"minutes\",\"MINUTES_AGO\":\"{} minutes ago\",\"MINUTE_AGO\":\"{} minute ago\",\"MONTHS_AGO\":\"{} months ago\",\"MONTH_0\":\"January\",\"MONTH_1\":\"February\",\"MONTH_10\":\"November\",\"MONTH_11\":\"December\",\"MONTH_2\":\"March\",\"MONTH_3\":\"April\",\"MONTH_4\":\"May\",\"MONTH_5\":\"June\",\"MONTH_6\":\"July\",\"MONTH_7\":\"August\",\"MONTH_8\":\"September\",\"MONTH_9\":\"October\",\"MONTH_AGO\":\"{} month ago\",\"MONTH_SHORT_0\":\"Jan.\",\"MONTH_SHORT_1\":\"Feb.\",\"MONTH_SHORT_10\":\"Nov.\",\"MONTH_SHORT_11\":\"Dec.\",\"MONTH_SHORT_2\":\"Mar.\",\"MONTH_SHORT_3\":\"Apr.\",\"MONTH_SHORT_4\":\"May\",\"MONTH_SHORT_5\":\"Jun.\",\"MONTH_SHORT_6\":\"Jul.\",\"MONTH_SHORT_7\":\"Aug.\",\"MONTH_SHORT_8\":\"Sept.\",\"MONTH_SHORT_9\":\"Oct.\",\"MORE\":\"More\",\"MORE_CONTRIBUTIONS\":\"More Contributions\",\"MORE_LIKE_THIS\":\"More like this:\",\"MORE_NEWS\":\"More News\",\"MORE_PLEASE\":\"More Please\",\"MY_DRAFTS\":\"My Drafts\",\"NAME\":\"Name\",\"NEWS\":\"News\",\"NEWSLETTER_TITLE\":\"Stay up to date with our latest BuzzFeed video. Enter your email to subscribe!\",\"NEWSLETTERS\":\"Newsletters\",\"NEWSLETTERS\\/CONFIRM-SIGNUP\":\"Email Newsletter - Confirm Subscription\",\"NEWSLETTERS_CAPTCHA_ERROR\":\"Your sign up request failed. Please retry, and if it happens again please try a different browser.\",\"NEWSLETTERS_EMAIL_INVALID\":\"Please enter a valid email adress.\",\"NEWSLETTERS_FINE_PRINT\":\"By entering your email and clicking Sign Up, you're agreeing to let us send you customized marketing messages about us and our advertising partners. You are also agreeing to our \u003ca href=\\\"\\/about\\/useragreement\\\" class=\\\"link-white decoration-underline\\\"\u003eTerms of Service\u003c\\/a\u003e and \u003ca href=\\\"\\/about\\/privacy\\\" class=\\\"link-white decoration-underline\\\"\u003ePrivacy Policy\u003c\\/a\u003e.\",\"NEWSLETTERS_SELECTED_COUNT_LABEL\":\"newsletter selected\",\"NEWSLETTERS_SELECTED_COUNT_LABEL_LOTS\":\"newsletters selected\",\"NEWSLETTERS_SELECT_NEWSLETTERS\":\"Select the newsletters you'd like dropped in your inbox.\",\"NEWSLETTERS_SERVER_ERROR\":\"Oops! Something went wrong, please try again or refresh the page.\",\"NEWSLETTERS_SIGNUP_CTA\":\"Sign up\",\"NEWSLETTERS_SIGNUP_LABEL\":\"Please enter your email address below:\",\"NEWSLETTERS_SUBSCRIBE_MORE\":\"Subscribe to more newsletters\",\"NEWSLETTERS_SUCCESS_NOTE_DESKTOP\":\"Check your inbox to confirm your subscriptions\",\"NEWSLETTERS_SUCCESS_NOTE_MOBILE\":\"Check your email to confirm\",\"NEWSLETTERS_SUCCESS_TITLE\":\"Thanks for subscribing!\",\"NEWSLETTER_EMAIL_INVALID\":\"Womp. It looks like this is an invalid email!\",\"NEWSLETTER_EMAIL_MISSING\":\"Whoops! You need to enter an email first.\",\"NEWSLETTER_EMAIL_SOMETHING_WRONG\":\"Oh no! Something went wrong.\",\"NEWSLETTER_EMAIL_SUCCESS\":\"You've successfully signed up. Psst: Look out for a confirmation email!\",\"NEWSLETTER_KEEP_UP\":\"Keep up with the BuzzFeed News daily email!\",\"NEWSLETTER_NEWS_MOVES_FAST\":\"News Moves Fast\",\"NEW_POST\":\"New Post\",\"NEW_UPDATE\":\"New Update\",\"NEXT_PAGE\":\"Next page\",\"NEXT_QUIZ\":\"Next Quiz\",\"NOW_BUZZING\":\"Now Buzzing\",\"NOW_PLAYING\":\"Now playing\",\"NO_POSTS\":\"hasn't created any posts yet\",\"NO_RESULTS\":\"Sorry, we couldn't find any results that match that search.\",\"OH_NO_SOMETHING_WENT_WRONG\":\"Oh no, something went wrong.\",\"OMG\":\"OMG\",\"ONLY_QUIZZES\":\"Only quizzes\",\"OOPS_IMAGE_TOO_BIG\":\"Oops! Your Image was too big. It needs to be under 10MB.\",\"OOPS_PLEASE_ENTER_FIELDS_ABOVE\":\"Oops! Please enter the fields above and agree to the terms before continuing.\",\"OOPS_SOMETHING_WRONG\":\"Oops. Something went wrong. Please try again later\",\"OOPS_UPLOAD_IMAGE_OR_TEXT\":\"Oops! Upload an image or add text to submit.\",\"OOPS_WRONG_FILE_TYPE_NEED_IMAGE\":\"Oops! We can\\u2019t accept that file type. It needs to be a jpg, gif, or png.\",\"OR\":\"or\",\"ORIGINALLY_POSTED_ON\":\"Originally posted on\",\"PAGE\":\"Page\",\"PAGE_DESCRIPTION\":\"BuzzFeed has the hottest, most social content on the web. We feature breaking buzz and the kinds of things you'd want to pass along to your friends.\",\"PAGE_TITLE\":\"BuzzFeed Search\",\"PIN\":\"Pin\",\"PLAY\":\"Play\",\"PLEASE_WAIT_WHILE_WE_UPLOAD\":\"Please wait while we upload your video\",\"PODCASTS\":\"BuzzFeed Podcasts\",\"POINTS\":\"points\",\"POLL_CLOSED\":\"This poll is now closed.\",\"POPULAR_CATEGORIES\":\"Popular Categories\",\"POSTED_AT\":\"Posted at \",\"POSTED_ON\":\"Posted on \",\"POST_CREATED_BY_BF_COMMUNITY_MEMBER\":\"This post was created by a member of BuzzFeed Community, where anyone can post awesome lists and creations.\",\"POST_NOT_VETTED\":\"This post has not been vetted or endorsed by BuzzFeed's editorial staff. BuzzFeed Community is a place where anyone can create a post.\",\"POST_YOUR_BUZZ\":\"post your buzz!\",\"PREVIOUS_PAGE\":\"Previous page\",\"PRIVACY_POLICY\":\"Privacy policy\",\"PRODUCT_NAV_TITLE\":\"Skip ahead to your favorite:\",\"PRODUCT_NAV_TITLE_SHOPPABLE\":\"Skip ahead to your favorite look:\",\"PRODUCT_NAV_TITLE_UK\":\"Skip ahead to your favourite:\",\"PROMOTED\":\"Promoted\",\"PROMOTED_BY\":\"Promoted By\",\"PRODUCED_BY\":\"Produced by\",\"PURPOSES\":\"Purposes\",\"QUICKLY_CATCH_UP\":\"Quickly Catch Up\",\"QUIZZES\":\"Quizzes\",\"QUIZZES_ON_BUZZFEED\":\"Quizzes on BuzzFeed\",\"REACT_MAX_3\":\"Sorry, but you can only react up to 3 times!\",\"READ_MORE\":\"Read More\",\"READ_MORE_IN\":\"Read More In\",\"REPLY\":\"Reply\",\"REPORTING_FROM\":\"Reporting From\",\"REPORTING_TO_YOU\":\"Reporting To You\",\"RESPONSES\":\"Responses\",\"RESULTS\":\"Results\",\"RETAKE_QUIZ\":\"Retake Quiz\",\"RETURN_TO_BF_HOMEPAGE\":\"Return to BuzzFeed's Homepage\",\"RETURN_TO_BF_NEWS\":\"Return to BuzzFeed News\",\"RETURN_TO_BF_TASTY_HOMEPAGE\":\"Return to Tasty Homepage\",\"RETWEET\":\"Retweet\",\"RIGHT\":\"Right\",\"RSS\":\"RSS Feeds\",\"RULES\":\"Rules\",\"SEARCH\":\"Search\",\"SEARCH_ALL_QUIZZES\":\"Search All Quizzes\",\"SEASON_AND_EPISODE\":\"S{season} E{episode}\",\"SECTIONS\":\"Sections\",\"SEE_ALL_COMMENTS\":\"See all comments\",\"SEE_FULL_CREDITS\":\"See full \u003ca class=\\\"link-pink bold\\\" href=\\\"{}\\\"\u003evideo credits\u003c\\/a\u003e\",\"SEE_MORE\":\"Show more\",\"SEND_IT_TO_US\":\"Send it to us!\",\"SERVER_ERROR\":\"Looks like we are having a problem on the server\",\"SHARE\":\"Share\",\"SHARE_ARTICLE\":\"Share This Article\",\"SHARE_ON_FB\":\"Share On Facebook\",\"SHARE_ON_X\":\"Share On {{name}}\",\"SHARE_RESULTS\":\"Share Your Results\",\"SHARE_THIS_LINK\":\"Share This Link\",\"SHARE_VOTE\":\"Share Your Vote!\",\"SHOPPABLE_RECAP\":\"Here\\u2019s a recap of all the looks:\",\"SHOW\":\"Show\",\"SHOW_MORE\":\"Show More\",\"SHOW_NEW_IMAGE\":\"Show new image\",\"SHOW_ORIGINAL_IMAGE\":\"Show original image\",\"SHOW_PURPOSES\":\"Show purposes\",\"SIGN_IN_TO_ADD_YOURS\":\"Sign In to Add Yours\",\"SIGN_UP\":\"Sign up\",\"SKIP_TO_CONTENT\":\"Skip To Content\",\"SLIDE\":\"Slide\",\"SMS\":\"SMS\",\"SORRY_SOMETHING_WRONG\":\"Sorry! Something went wrong. Please try again.\",\"SPOILER_ALERT\":\"SPOILER ALERT\",\"SPONSORED_BY\":\"Sponsored By\",\"SUBMIT\":\"Submit\",\"SUBSCRIBE_TO_OUR_RSS\":\"Subscribe to our RSS feed\",\"SUBSCRIBE_TO_US_ON\":\"Subscribe To Us On\",\"SUBSCRIBE_TO_US_ON_YOUTUBE\":\"Subscribe To Us On YouTube\",\"SUBTITLE_ANIMALS\":\"All the best animals on the internet.\",\"SUBTITLE_EMPHASIZED_ANIMALS\":\"The snuggle is real.\",\"SUCCESS\":\"Success\",\"SUGGESTED_SEARCH\":\"Search BuzzFeed\",\"SUPPORT_US\":\"Support Us\",\"SWITCH_TO_ENGLISH\":\"Switch to English\",\"SWITCH_TO_US\":\"Switch to US.\",\"TAP_TO_PLAY_GIF\":\"Tap to play GIF\",\"TAP_TO_REVEAL\":\"Tap to reveal\",\"TAP_TO_VIEW_ENHANCED_PAGE_VERSION\":\"Tap here to view the enhanced version of this page\",\"THANKS_FOR_VOTING\":\"Thanks for voting!\",\"THANKS_FOR_YOUR_FEEDBACK\":\"Thanks for your feedback!\",\"THANKS_YOUR_VIDEO_UPLOADED\":\"Thanks, your video has been added!\",\"THATS_AWESOME\":\"That's awesome!\",\"THERE_ARE_NO_COMMENTS_YET\":\"There are no comments yet.\",\"THIRD_PARTY_VENDORS\":\"Third-party vendors\",\"THIS_COULD_TAKE_A_MINUTE\":\"This could take a minute!\",\"TOGGLE_SHARING_OPTIONS\":\"Toggle Sharing Options\",\"TOPICS_LIST_TITLE\":\"Topics In This Article\",\"TOP_POST\":\"top post\",\"TOP_POSTS\":\"Top Posts\",\"TOP_POSTS_BUZZFEED\":\"Top Posts on BuzzFeed\",\"TOP_POSTS_FROM\":\"Top Posts From\",\"TOP_POSTS_THIS_WEEK\":\"Top Posts This Week\",\"TOP_USERS_THIS_WEEK\":\"Top Users This Week\",\"TRENDING\":\"Trending\",\"TRENDING_NOW\":\"Trending Now\",\"TRENDING_NOW_SPAIN\":\"\",\"TRENDING_ON_BUZZFEED\":\"Trending on BuzzFeed\",\"TRENDING_ON_NEWS\":\"Trending News\",\"TRENDING_QUIZZES\":\"Trending Quizzes\",\"TRENDING_VIDEOS\":\"Trending Videos\",\"TRY\":\"Try \\u201c{{name}}\\u201d\",\"TRY_AGAIN_LATER\":\"Please try again later\",\"TRY_MORE\":\"Try another search or check out these top posts:\",\"TRY_RELOADING\":\"Try reloading this page\",\"TRY_UPLOADING_AGAIN\":\"Try uploading again.\",\"TWEET\":\"Tweet\",\"UNABLE_TO_ADD_COMMENT\":\"Unable to add comment.\",\"UNABLE_TO_COPY_LINK\":\"Unable to copy link!\",\"UNABLE_TO_LOAD_COMMENTS\":\"Unable to load comments.\",\"UNABLE_TO_SHOW_IMAGE_PREVIEW\":\"Unable to show image preview.\",\"UPDATED_ON\":\"Updated on\",\"UPDATES\":\"Updates\",\"UPDATE_CONSENT\":\"Update Consent\",\"USER_AGREEMENT\":\"User Agreement\",\"USER_LANGUAGE_POSTS_de\":\"German Posts\",\"USER_LANGUAGE_POSTS_en\":\"English Posts\",\"USER_LANGUAGE_POSTS_es\":\"Spanish Posts\",\"USER_LANGUAGE_POSTS_fr\":\"French Posts\",\"USER_LANGUAGE_POSTS_ja\":\"Japanese Posts\",\"USER_LANGUAGE_POSTS_pt\":\"Portuguese Posts\",\"VIDEO_DURATION\":\"Video duration\",\"VIEWING_FULL_SITE\":\"viewing the full site.\",\"VIEWS\":\"Views\",\"VIEW_COMMENTS\":\"View comments\",\"VIEW_COMPANIES\":\"View companies\",\"VIEW_FB_COMMENTS\":\"View Comments\",\"VIEW_FULL_SITE_SIGNED_IN\":\"View the full site while signed in to comment!\",\"VIEW_ON_ENHANCED_PAGE\":\"View on enhanced page\",\"VIEW_POST_ON_FB\":\"View this post on Facebook\",\"VIEW_PROFILE\":\"View Profile\",\"VIEW_RESPONSES\":\"View Responses\",\"VIEW_RESULTS\":\"View Results\",\"VIEW_THIS_ON_X\":\"View this {{type}} on {{name}}\",\"VIEW_THIS_VIDEO_ON_YT\":\"View this video on YouTube\",\"VISIT_WEBSITE\":\"Visit Website\",\"VOTE\":\"vote\",\"VOTES\":\"votes\",\"WARNING\":\"Warning\",\"WATCH\":\"Watch\",\"WATCH_IT_LIVE\":\"Watch it live in the Facebook App\",\"WED_LOVE_TO_HEAR_WHAT_ELSE_YOU_HAVE_TO_SAY\":\"We'd love to hear what else you have to say.\",\"WED_LOVE_TO_HEAR_WHAT_ELSE_YOU_HAVE_TO_SAY_SPAIN\":\"\",\"WED_LOVE_YOUR_FEEDBACK\":\"We'd love your feedback!\",\"WEEKS_AGO\":\"{} weeks ago\",\"WEEK_AGO\":\"{} week ago\",\"WELCOME_TO_THE_NEW_BUZZFEED_HOMEPAGE\":\"Welcome to the new BuzzFeed homepage!\",\"WELCOME_TO_THE_NEW_BUZZFEED_HOMEPAGE_FEEDBACK\":\"Welcome to the new BuzzFeed homepage!\",\"WERE_SORRY_TO_HEAR_THAT\":\"We're sorry to hear that.\",\"WERE_SORRY_TO_HEAR_THAT_SPAIN\":\"\",\"WE_VALUE_YOUR_PRIVACY\":\"We value your privacy\",\"WHAT_DO_YOU_THINK\":\"What do you think?\",\"WIN\":\"Win\",\"WRONG\":\"Wrong!\",\"WTF\":\"WTF\",\"YEARS_AGO\":\"{} years ago\",\"YEAR_AGO\":\"{} year ago\",\"YOUR_ANSWER\":\"Your Answer\",\"YOUR_EMAIL\":\"Email address\",\"YOUR_EMAIL_ADDRESS\":\"Your Email Address\",\"YOUR_MESSAGE_WAS_POSTED\":\"Your message was posted successfully\",\"YOU_GOT\":\"You got:\",\"YOU_GOT_CORRECT_OUT_OF_TOTAL\":\"You got \u003c%=correct%\u003e out of \u003c%=total%\u003e right!\",\"YOU_RE_CAUGHT_UP\":\"You're all caught up!\",\"YOU_VERB_OUT_OF_TOTAL\":\"You \u003c%=\u0026verb%\u003e \u003c%=selected%\u003e out of \u003c%=total%\u003e \u003c%=\u0026subject%\u003e\"}","js":["/static-assets/js/vendor.7108a39869c999f3999a.js?brotli=allow","/static-assets/js/subbuzzes.5eb342f74f1c6ba74a8a.js?brotli=allow"],"inline_js":"!function(e){function r(r){for(var n,a,i=r[0],c=r[1],l=r[2],s=0,p=[];s\u003ci.length;s++)a=i[s],Object.prototype.hasOwnProperty.call(o,a)\u0026\u0026o[a]\u0026\u0026p.push(o[a][0]),o[a]=0;for(n in c)Object.prototype.hasOwnProperty.call(c,n)\u0026\u0026(e[n]=c[n]);for(f\u0026\u0026f(r);p.length;)p.shift()();return u.push.apply(u,l||[]),t()}function t(){for(var e,r=0;r\u003cu.length;r++){for(var t=u[r],n=!0,i=1;i\u003ct.length;i++){var c=t[i];0!==o[c]\u0026\u0026(n=!1)}n\u0026\u0026(u.splice(r--,1),e=a(a.s=t[0]))}return e}var n={},o={0:0},u=[];function a(r){if(n[r])return n[r].exports;var t=n[r]={i:r,l:!1,exports:{}};return e[r].call(t.exports,t,t.exports,a),t.l=!0,t.exports}a.e=function(e){var r=[],t=o[e];if(0!==t)if(t)r.push(t[2]);else{var n=new Promise((function(r,n){t=o[e]=[r,n]}));r.push(t[2]=n);var u,i=document.createElement(\"script\");i.charset=\"utf-8\",i.timeout=120,a.nc\u0026\u0026i.setAttribute(\"nonce\",a.nc),i.src=function(e){return a.p+\"js/\"+({1:\"vendor\",3:\"quiz-legacy\"}[e]||e)+\".\"+{1:\"7108a39869c999f3999a\",3:\"f44044ee35875108e369\",11:\"c294c7ab4db1858fc480\",12:\"ae1b0d8c1d5e4909ee9e\"}[e]+\".js?brotli=allow\"}(e);var c=new Error;u=function(r){i.onerror=i.onload=null,clearTimeout(l);var t=o[e];if(0!==t){if(t){var n=r\u0026\u0026(\"load\"===r.type?\"missing\":r.type),u=r\u0026\u0026r.target\u0026\u0026r.target.src;c.message=\"Loading chunk \"+e+\" failed.\\n(\"+n+\": \"+u+\")\",c.name=\"ChunkLoadError\",c.type=n,c.request=u,t[1](c)}o[e]=void 0}};var l=setTimeout((function(){u({type:\"timeout\",target:i})}),12e4);i.onerror=i.onload=u,document.head.appendChild(i)}return Promise.all(r)},a.m=e,a.c=n,a.d=function(e,r,t){a.o(e,r)||Object.defineProperty(e,r,{enumerable:!0,get:t})},a.r=function(e){\"undefined\"!=typeof Symbol\u0026\u0026Symbol.toStringTag\u0026\u0026Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})},a.t=function(e,r){if(1\u0026r\u0026\u0026(e=a(e)),8\u0026r)return e;if(4\u0026r\u0026\u0026\"object\"==typeof e\u0026\u0026e\u0026\u0026e.__esModule)return e;var t=Object.create(null);if(a.r(t),Object.defineProperty(t,\"default\",{enumerable:!0,value:e}),2\u0026r\u0026\u0026\"string\"!=typeof e)for(var n in e)a.d(t,n,function(r){return e[r]}.bind(null,n));return t},a.n=function(e){var r=e\u0026\u0026e.__esModule?function(){return e.default}:function(){return e};return a.d(r,\"a\",r),r},a.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},a.p=\"/static-assets/\",a.oe=function(e){throw console.error(e),e};var i=window.subbuzzJsonp=window.subbuzzJsonp||[],c=i.push.bind(i);i.push=r,i=i.slice();for(var l=0;l\u003ci.length;l++)r(i[l]);var f=c;t()}([]);\n//# sourceMappingURL=webpack-runtime.1803d688810cc0e8fca7.js.map?brotli=allow"},"subbuzzes":["","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","",""],"gateIndex":null},"userGeo":"IT","topComments":[{"datePublished":"2025-07-07T19:27:13.000Z","authorName":"LuluPanda57","text":"If someone wants to call something a travesty, it would be nice to be able to see the reasons. Just saying something is terrible isn't very interesting to read.","upvoteCount":47,"downvoteCount":0},{"datePublished":"2025-07-07T19:17:36.000Z","authorName":"Alchemist1342","text":"#3. We used \"douchebag\" all the time in the 70's. And clothes weren't uncomfortable, but it was stylish for certain cliques to go shirtless.","upvoteCount":36,"downvoteCount":0},{"datePublished":"2025-07-07T19:31:02.000Z","authorName":"thurstonfluff","text":"Every movie where a bell rings to signal the end of class at a college. ","upvoteCount":35,"downvoteCount":0},{"datePublished":"2025-07-07T20:07:31.000Z","authorName":"mechanicalman","text":"Gotta disagree with #3, although it’s not a topic I’m going to invest a lot of energy in. Douchebag was indeed a very commonly used insult in the 80s. I heard it all the time.","upvoteCount":24,"downvoteCount":0},{"datePublished":"2025-07-07T20:31:02.000Z","authorName":"abourque","text":"8. Bones is not a medical hospital show. They work on dead people. There is no need of nurses for Bones. I think you probably meant House.","upvoteCount":21,"downvoteCount":0},{"datePublished":"2025-07-07T20:58:57.000Z","authorName":"lifetheuniverseandeverything","text":"This is post is so lazy. I wanted to see specifics about failings in movies had about real historical events or time periods.\n\nNot just 'I work in X and it's nothing like the movies'","upvoteCount":20,"downvoteCount":0},{"datePublished":"2025-07-07T19:34:50.000Z","authorName":"jannar2","text":"What the hell is #3 talking about? The word \"douchbag\" has been around forever, and yes, believe it or not, children's clothing in the 1970s and 1980s was comfortable. We weren't all walking around naked.","upvoteCount":20,"downvoteCount":0},{"datePublished":"2025-07-07T22:11:12.000Z","authorName":"pamelaehn","text":"19. There hasn’t been a parking space in Boston since 1965 so…","upvoteCount":10,"downvoteCount":0},{"datePublished":"2025-07-08T02:19:42.000Z","authorName":"wickedwalrus90","text":"Wait, Ron Burgundy and the Channel 4 News Team is not based on fact?\nNooooooo!","upvoteCount":8,"downvoteCount":0},{"datePublished":"2025-07-07T21:20:08.000Z","authorName":"messyminion34","text":"Belker from Hill Street Blues made \"douchebag\" even more of a thing in 1981. Twas slang, for sure.","upvoteCount":8,"downvoteCount":0}],"commentCount":100,"isArbitragePage":false}},"page":"/[editionOrAuthor]/[authorOrSlug]","query":{"open_comments":"on","editionOrAuthor":"abbyzinman","authorOrSlug":"movie-mistakes-about-real-events"},"buildId":"P_maViqitDup60wPlQIin","assetPrefix":"/static-assets","runtimeConfig":{"ASSET_PREFIX":"/static-assets","NODE_ENV":"production","QUIZ_CATEGORIES":[{"name":"Latest","url":"/","feed":"quizzes","feed_tags":[],"slug":"latest","title":"BuzzFeed Quizzes","description":"We've got all the quizzes you love to binge! Come on in and hunker down for the long haul."},{"name":"✨ Arcade","url":"/arcade","feed":"arcade","feed_tags":[],"slug":"arcade","title":"BuzzFeed Quizzes","description":"We've got all the quizzes you love to binge! Come on in and hunker down for the long haul."},{"name":"Personality","url":"/personality","feed":"quizzes-personality","feed_tags":["personality_quiz"],"slug":"personality","title":"Personality Quizzes on BuzzFeed","description":"Discover your identity through engaging personality quizzes. Uncover traits, quirks, strengths, and more!"},{"name":"Trivia","url":"/trivia","feed":"quizzes-trivia","feed_tags":["trivia_quiz"],"slug":"trivia","title":"Trivia Quizzes on BuzzFeed","description":"Are you smarter than the average BuzzFeeder? I bet you are. Find out with our endless trivia quizzes."},{"name":"Poll","url":"/poll","feed":"quizzes-poll","feed_tags":["poll"],"slug":"poll","title":"Polls on BuzzFeed","description":"See how your opinion stacks up with our polls on TV, food, and more. Time to cast your vote!"},{"name":"🪄 Generators","url":"/topic/make-yours","feed":"make-yours","feed_tags":[],"slug":"make-yours","title":"Make Yours","description":"Create weird, wonderful, and 100% unique images using our custom generators."},{"name":"Checklist","url":"/checklist","feed":"quizzes-checklist","feed_tags":["checklist_quiz"],"slug":"checklist","title":"Checklist Quizzes on BuzzFeed","description":"All the checklist quizzes you need. From movies to life goals, tick things off and see how you measure up!"},{"name":"Taylor Swift","url":"/taylor-swift","feed":"quizzes-taylor-swift","feed_tags":["taylor_swift"],"slug":"taylor-swift","title":"Taylor Quizzes on BuzzFeed","description":"Think you’re Blondie’s biggest fan? Dive into an endless stream of Taylor Swift quizzes, weigh in with your hottest Taylor opinions, and put your ~reputation~ to the test."},{"name":"Marvel","url":"/marvel","feed":"quizzes-marvel","feed_tags":["marvel","mcu"],"slug":"marvel","title":"Marvel Quizzes on BuzzFeed","description":"All the greatest Marvel and MCU quizzes are right here. Falcon and the Winter Soldier, Captain America, WandaVision, and more!"},{"name":"Zodiac","url":"/zodiac","feed":"quizzes-zodiac","feed_tags":["zodiac"],"slug":"zodiac","title":"Zodiac Quizzes on BuzzFeed","description":"All the best Zodiac quizzes in one place. See what your star sign says about you!"},{"name":"Food","url":"/food","feed":"quizzes-food","feed_tags":["food"],"slug":"food","title":"Food Quizzes on BuzzFeed","description":"Quizzes and food. Is there a better combination? We've got all the food quizzes you can eat!"},{"name":"Love","url":"/love","feed":"quizzes-love","feed_tags":["love"],"slug":"love","title":"Love Quizzes on BuzzFeed","description":"We've got all the answers to your love and relationship questions. You'll thank us later."},{"name":"Harry Potter","url":"/harry-potter","feed":"quizzes-harry-potter","feed_tags":["harry_potter","hogwarts"],"slug":"harry-potter","title":"Harry Potter Quizzes on BuzzFeed","description":"Let the Sorting Hat tell you what house you belong in and then test your Hogwarts knowledge."},{"name":"Anime","url":"/anime","feed":"quizzes-anime","feed_tags":["anime"],"slug":"anime","title":"Anime Quizzes on BuzzFeed","description":"All the anime quizzes you need. Are you more Goku, Asuka, Ryuk or Naruto? Find out!"},{"name":"Disney","url":"/disney","feed":"quizzes-disney","feed_tags":["disney"],"slug":"disney","title":"Disney Quizzes on BuzzFeed","description":"All the best Disney-themed quizzes in one place. Princesses, Princes, Villains, Sidekicks, and more!"},{"name":"K-Pop","url":"/kpop","feed":"quizzes-kpop","feed_tags":["k_pop"],"slug":"kpop","title":"K-Pop Quizzes on BuzzFeed","description":"All the best K-Pop quizzes in one place. BTS, Blackpink, Pentagon, NCT, Monsta X, and more!"},{"name":"Geography","url":"/geography","feed":"quizzes-geography","feed_tags":["geography"],"slug":"geography","title":"Geography Quizzes on BuzzFeed","description":"All the best Geography quizzes in one place. Test your knowledge of the world!"}],"QUIZ_TYPES":{"calculator":{"tags":["calculator_quiz"],"title":"Calculator Quiz","url":"/quizzes/calculator"},"checklist":{"tags":["checklist_quiz"],"title":"Checklist Quiz","url":"/quizzes/checklist"},"personality":{"tags":["personality_quiz"],"title":"Personality Quiz","url":"/quizzes/personality"},"poll":{"tags":["poll"],"title":"Poll Quiz","url":"/quizzes/poll"},"trivia":{"tags":["trivia_quiz"],"title":"Trivia Quiz","url":"/quizzes/trivia"}},"CLUSTER":"prod","RIG_DEPLOYMENT_TYPE":"primary","abeagle_host":"https://abeagle-public.buzzfeed.com","bf_app_id":352969997,"destinations":{"buzzfeed":{"applink_ios_app_name":"BuzzFeed","applink_ios_app_store_id":352969997,"applink_ios_url":"fb45075597673://buzz/","base_url":"https://www.buzzfeed.com","facebook_app_id":45075597673,"facebook_publisher":"https://www.facebook.com/BuzzFeed","facebook_tracking_id":"260954170738952","nielsen_pageview_config":{"apid":"P7A907367-6D97-4DFD-AF90-B51EDF8D9AC5","apn":"BuzzFeed Webapp Static"},"nielsen_video_config":{"apid":"P3816F089-E5B5-4BF1-9D71-A955F54BCDE3","apn":"buzzfeedVideos"},"gtm_enabled":true,"permutive_creds":{"default":{"api_key":"97a551ba-9737-45fd-a258-d536588b60d7","project_id":"2adb5cbf-181d-4c9c-972c-4ab94b4f7cab"},"US":{"api_key":"e275d97e-aa77-43e0-ac22-71c7d5d5ad5a","project_id":"6ef7b2c1-fe43-45ad-8551-5377c13e2f71"},"UK":{"api_key":"c5278f5d-c07e-4d35-b166-2d3e46593966","project_id":"eb84983d-bf1e-49e9-8037-c087c4c77e42"}},"publisher_name":"BuzzFeed"},"buzzfeed_news":{"applink_ios_app_name":"BuzzFeed","applink_ios_app_store_id":352969997,"applink_ios_url":"fb45075597673://buzz/","base_url":"https://www.buzzfeednews.com","facebook_app_id":45075597673,"facebook_publisher":"https://www.facebook.com/BuzzFeedNews","facebook_tracking_id":"696740892007664","nielsen_pageview_config":{"apid":"P77672CB5-D3F4-4EBC-8161-08175209A620","apn":"BuzzFeed News Webapp Static"},"nielsen_video_config":{"apid":"PF3A4E03E-7B01-4FE4-925A-E644F8264623","apn":"BuzzFeed News Webapp Video"},"gtm_enabled":false,"permutive_creds":{"default":{"api_key":"6f0edd3f-33b3-40f3-b512-b749f2d7aba9","project_id":"4156fada-6f75-48ea-9015-69f98aab4271"}},"publisher_name":"BuzzFeed News"}},"image_service_url":"https://img.buzzfeed.com/buzzfeed-static","mango_url":"https://mango.buzzfeed.com","outbrain_ids":["0020946b84687eadc69ca9e8c97ade0b3c","0050b4bef1fd7acac4dc0283f3f481f712","005d5f7b766c31c50fc3ca08b979ce5414","00f5f52824d6816e28152720d667c7faf7"],"pressboard_usernames":["hrblockcanada"],"recsys_api_origin":"https://recsys-api.buzzfeed.com/web","sentry_dsn":"https://<EMAIL>/1768740","site_captcha_key":"6LdijagaAAAAAGSHdtiTSGDTdHj7HsQREh9Oj-hJ","site_component_api_origin":"https://www.buzzfeed.com/site-component/v2","snapchat_tracking_id":"32a4892a-7a98-44df-b23b-7ed05a1b1f6f","social":{"facebook":"https://www.facebook.com/BuzzFeed","twitter":"https://www.twitter.com/buzzfeed","pinterest":"https://www.pinterest.com/buzzfeed","youtube":"https://www.youtube.com/user/buzzfeedvideo"}},"isFallback":false,"customServer":true,"gip":true,"appGip":true,"scriptLoader":[]}
  </script>
 </body>
</html>
