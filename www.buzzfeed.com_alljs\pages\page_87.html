<!DOCTYPE html>
<html lang="en">
 <head>
  <link as="style" data-href="https://use.typekit.net/amc2eom.css" data-optimized-fonts="true" rel="preload"/>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <link href="https://use.typekit.net" rel="dns-prefetch"/>
  <link href="https://p.typekit.net" rel="dns-prefetch"/>
  <link href="https://sentry.io" rel="dns-prefetch"/>
  <script>
   (function(options) {try {if ("Profiler" in window && "Scheduler" in window && (window.location.search.includes("e2e_test") || Math.random() <= options.sample_rate)) {window.__jsProfiler = new window.Profiler({sampleInterval:options.profiler_init_options.sampleInterval || 0,maxBufferSize:options.profiler_init_options.maxBufferSize || 10000 });}}catch (err) {}})({"profiler_init_options":{},"sample_rate":0.1});
  </script>
  <link as="font" crossorigin="" data-href="https://use.typekit.net/af/034166/00000000000000003b9b2056/27/l?primer=b3195baa0f8561ca2f0a3f81377b0c49080c234114cdfff11a59797d1967a943&amp;fvd=n8&amp;v=3" data-optimized-fonts="true" rel="preload" type="font/woff2"/>
  <meta content="Copyright BuzzFeed, Inc. All rights reserved." name="copyright"/>
  <meta content="yes" name="apple-mobile-web-app-capable"/>
  <meta content="BuzzFeed" name="apple-mobile-web-app-title"/>
  <link crossorigin="" href="/static/images/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="https://www.buzzfeed.com/press" rel="canonical"/>
  <meta content="noauto" property="fb:smart_publish:robots"/>
  <meta content="45075597673" property="fb:app_id"/>
  <meta content="https://www.buzzfeed.com/press" property="og:url"/>
  <meta content="BuzzFeed" property="og:site_name"/>
  <meta content="https://www.twitter.com/undefined" name="twitter:site"/>
  <meta content="https://www.buzzfeed.com/press" name="twitter:url"/>
  <meta content="5695632" name="twitter:site:id"/>
  <meta content="summary_large_image" name="twitter:card"/>
  <link href="https://www.buzzfeed.com/obiwan-static/images/favicon.ico?v=0.458219009885" rel="icon"/>
  <title>
   Press about BuzzFeed
  </title>
  <meta content="Articles and press about BuzzFeed, the leading independent digital media company" name="description"/>
  <link href="https://a-us.storyblok.com/f/1001779/625x417/bc03ee8f77/about-share-image.jpg" rel="image_src"/>
  <meta content="Press about BuzzFeed" property="og:title"/>
  <meta content="Articles and press about BuzzFeed, the leading independent digital media company" property="og:description"/>
  <meta content="https://a-us.storyblok.com/f/1001779/625x417/bc03ee8f77/about-share-image.jpg" property="og:image"/>
  <meta content="Press about BuzzFeed" name="twitter:title"/>
  <meta content="Articles and press about BuzzFeed, the leading independent digital media company" name="twitter:description"/>
  <meta content="https://a-us.storyblok.com/f/1001779/625x417/bc03ee8f77/about-share-image.jpg" name="twitter:image"/>
  <meta content="32" name="next-head-count"/>
  <link as="style" href="/static-assets/_next/static/css/1f913c812f430c76.css" rel="preload"/>
  <noscript data-n-css="">
  </noscript>
  <script defer="" nomodule="" src="/static-assets/_next/static/chunks/polyfills-0d1b80a048d4787e.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/webpack-95e8b5411c26045b.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/framework-e4553f9529659f5f.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/main-d77f947e4580a2f7.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/pages/_app-b1180c46476ff804.js">
  </script>
  <script defer="" src="/static-assets/_next/static/chunks/pages/%5B...slug%5D-237a7f073c8f084c.js">
  </script>
  <script defer="" src="/static-assets/_next/static/53TC_MGteSnU0pNC9AKwh/_buildManifest.js">
  </script>
  <script defer="" src="/static-assets/_next/static/53TC_MGteSnU0pNC9AKwh/_ssgManifest.js">
  </script>
 </head>
 <body>
  <div data-reactroot="" id="__next">
   <link as="font" crossorigin="" href="https://use.typekit.net/af/034166/00000000000000003b9b2056/27/l?primer=b3195baa0f8561ca2f0a3f81377b0c49080c234114cdfff11a59797d1967a943&amp;fvd=n8&amp;v=3" rel="preload" type="font/woff2"/>
   <link as="style" href="https://use.typekit.net/amc2eom.css" rel="preload"/>
   <main class="buzzfeed-container">
    <header class="sc-lkDHyp jsgLVJ">
     <a href="https://www.buzzfeed.com">
      <svg fill="#e32" height="26px" id="logo" viewbox="0 0 315.74 53.17" width="128px">
       <path d="M31.8 27.33c2.45.19 4.58 1.23 6.4 3.11 1.81 1.89 2.72 4.48 2.72 7.78 0 4.05-1.34 7.28-4.03 9.69-2.69 2.4-6.55 3.61-11.6 3.61H.69V4.14h24.54c4.34 0 7.81 1.18 10.43 3.54 2.62 2.36 3.92 5.49 3.92 9.4 0 2.97-.82 5.33-2.47 7.07-1.65 1.74-3.42 2.71-5.3 2.9v.28zm-19.44-3.61h10.18c1.74 0 3.13-.48 4.17-1.45s1.56-2.25 1.56-3.85c0-1.46-.51-2.64-1.52-3.54-1.01-.9-2.27-1.34-3.78-1.34H12.36v10.18zm10.89 18.39c1.84 0 3.26-.47 4.28-1.41 1.01-.94 1.52-2.24 1.52-3.89 0-1.56-.52-2.79-1.56-3.71-1.04-.92-2.38-1.38-4.03-1.38h-11.1v10.39h10.89zM81.43 51.51H70.12v-3.39c-2.83 2.97-6.46 4.45-10.89 4.45-4.15 0-7.48-1.36-10.01-4.07-2.52-2.71-3.78-6.28-3.78-10.71V16.02h11.24v19.37c0 2.07.57 3.76 1.7 5.06 1.13 1.3 2.59 1.94 4.38 1.94 2.31 0 4.11-.81 5.41-2.44 1.3-1.63 1.94-4.11 1.94-7.46V16.02h11.31v35.49zM116.27 51.51H86.32v-2.13L100.4 25H86.67v-8.98h29.43v2.13l-14.08 24.38h14.25v8.98zM151.11 51.51h-29.95v-2.13L135.24 25H121.5v-8.98h29.43v2.13l-14.08 24.38h14.25v8.98zM167.93 24.36h20.46v10.18h-20.46v16.97h-11.67V4.14h34.01v10.25h-22.34v9.97zM271.28 36.66h-26.02c.67 4.4 3.96 6.86 8.94 6.86 3.92 0 7.79-1.54 10.69-3.77l4.34 7.52c-3.97 3.41-9.12 5.3-15.03 5.3-11.9 0-20.04-7.25-20.04-18.81 0-5.47 1.82-9.97 5.44-13.51 3.63-3.54 8.08-5.3 13.36-5.3 5.14 0 9.45 1.72 12.94 5.16 3.49 3.44 5.28 7.99 5.37 13.65v2.9zm-22.91-11.03c-1.41.99-2.38 2.36-2.9 4.1h14.64c-.52-1.84-1.41-3.23-2.69-4.17-1.27-.94-2.76-1.41-4.45-1.41-1.65 0-3.18.49-4.6 1.48zM229.91 36.66h-26.02c.67 4.4 3.96 6.86 8.94 6.86 3.92 0 7.79-1.54 10.69-3.77l4.34 7.52c-3.97 3.41-9.12 5.3-15.03 5.3-11.9 0-20.04-7.25-20.04-18.81 0-5.47 1.82-9.97 5.44-13.51 3.63-3.54 8.08-5.3 13.36-5.3 5.14 0 9.45 1.72 12.94 5.16 3.49 3.44 5.28 7.99 5.37 13.65v2.9zM207 25.63c-1.41.99-2.38 2.36-2.9 4.1h14.64c-.52-1.84-1.41-3.23-2.69-4.17-1.27-.94-2.76-1.41-4.45-1.41-1.66 0-3.19.49-4.6 1.48zM315.05 51.51h-11.31v-2.83c-2.88 2.59-6.55 3.89-11.03 3.89-4.81 0-8.9-1.76-12.27-5.27-3.37-3.51-5.06-8.03-5.06-13.54 0-5.47 1.69-9.97 5.06-13.51 3.37-3.54 7.46-5.3 12.27-5.3 4.48 0 8.16 1.3 11.03 3.89V.6h11.31v50.91zm-13.89-11.13c1.72-1.72 2.58-3.92 2.58-6.61 0-2.64-.86-4.83-2.58-6.58-1.72-1.74-3.76-2.62-6.12-2.62-2.5 0-4.55.86-6.15 2.58-1.6 1.72-2.4 3.92-2.4 6.61 0 2.73.8 4.95 2.4 6.65 1.6 1.7 3.65 2.55 6.15 2.55 2.36 0 4.4-.86 6.12-2.58z">
       </path>
      </svg>
     </a>
     <button class="sc-hsaIUA kwQKHf">
      <svg class="sc-bZTyFN jJKWk" viewbox="0 0 24 24">
       <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z">
       </path>
      </svg>
     </button>
     <div class="sc-jnlcPO ghTlNe">
      <a class="sc-dVdSBb iTGzkt" href="/about?edition=us">
       About
      </a>
      <a class="sc-dVdSBb dTgcNZ" href="/press?edition=us">
       Press
      </a>
      <a class="sc-dVdSBb iTGzkt" href="/jobs?edition=us">
       Jobs
      </a>
      <a class="sc-dVdSBb iTGzkt" href="/contact?edition=us">
       Contact
      </a>
      <a class="sc-dVdSBb iTGzkt" href="https://investors.buzzfeed.com/">
       Investors
      </a>
      <a class="sc-dVdSBb iTGzkt" href="/about/privacy?edition=us">
       Legal
      </a>
     </div>
    </header>
    <div class="sc-cmfnrN elvuvk">
     <img alt="" src="https://a-us.storyblok.com/f/1001779/2000x762/c158225aee/press-banner.jpg"/>
    </div>
    <div class="sc-kGLCbq jmyNLn">
     <h1>
      PRESS RESOURCES
     </h1>
     <div class="sc-hNDLBw bjkvNu">
      <div class="sc-drMgrp haVodD">
       <div class="sc-faxByu gevGTZ">
        <div class="sc-EFWon imNcmF">
         <h2>
          In the Press
         </h2>
         <a href="https://www.buzzfeed.com/buzzfeedpress" rel="noopener noreferrer" target="_blank">
          See More
         </a>
        </div>
       </div>
       <div class="sc-UFult jGtxgE">
        <div class="sc-jIYCZY fLeyCb">
         <h2>
          Brand Assets
         </h2>
         <p>
          Use these official logos to represent BuzzFeed Inc. in press and media applications. Logos should appear in their original colors when possible, and should be otherwise unmodified.
          <br/>
          <br/>
          Please contact us if you have questions about proper usage of BuzzFeed Inc. logos.
         </p>
         <a class="sc-bSstmL hYEEdk" href="/press/assets?edition=us">
          Download BuzzFeed Inc. Logos
         </a>
        </div>
        <div class="sc-bbxCgr eMUKzW">
         <div class="sc-hnmNqB iAeUwP">
          <div class="sc-jPQKUW kTwycp" color="#ee3322">
          </div>
          <div class="sc-eMkmGk jvdEa">
           <p>
            #ee3322
           </p>
           <p>
            R:238  G:51 B:34
           </p>
          </div>
         </div>
         <div class="sc-hnmNqB iAeUwP">
          <div class="sc-jPQKUW jKxuVR" color="#0077ee">
          </div>
          <div class="sc-eMkmGk jvdEa">
           <p>
            #ee3322
           </p>
           <p>
            R:0  G:119 B:238
           </p>
          </div>
         </div>
         <div class="sc-hnmNqB iAeUwP">
          <div class="sc-jPQKUW gtitWN" color="#ffee00">
          </div>
          <div class="sc-eMkmGk jvdEa">
           <p>
            #ee3322
           </p>
           <p>
            R:255  G:238 B:0
           </p>
          </div>
         </div>
        </div>
       </div>
      </div>
      <div class="sc-dxjrPO kkAweE">
       <h2>
        Communications
       </h2>
       <p>
        To contact the BuzzFeed Communications team, email
        <a href="mailto:<EMAIL>">
         <EMAIL>
        </a>
        . To contact the HuffPost News PR team, email
        <a href="mailto:<EMAIL>">
         <EMAIL>
        </a>
        .
       </p>
       <h6>
        <b>
         IMPORTANT NOTE:
        </b>
        This email address does not reach editorial staff or the human resources department. Please
        <b>
         do not
        </b>
        send press releases, editorial pitches, or job inquiries to us – they will not get in front of the right people. For press releases and pitches, please see the mastheads on section home pages for other options. For example, go to
        <a href="http://buzzfeed.com/entertainment">
         buzzfeed.com/entertainment
        </a>
        to see a masthead for our entertainment contacts.
       </h6>
       <h4>
        <b>
         Juliana Clifton
        </b>
       </h4>
       <h4>
        Vice President, Communications &amp; Chief of Staff
       </h4>
       <h4>
        <a href="mailto:<EMAIL>">
         <EMAIL>
        </a>
       </h4>
       <p>
       </p>
       <p>
       </p>
       <h4>
        <b>
         Lizzie Grams
        </b>
       </h4>
       <h4>
        <span style="color:rgb(0, 0, 0)">
         Sr. Communications Manager
        </span>
       </h4>
       <h4>
        <a href="mailto:<EMAIL>">
         <EMAIL>
        </a>
       </h4>
       <p>
       </p>
       <p>
       </p>
       <h4>
        <b>
         Sophia Vega
        </b>
       </h4>
       <h4>
        Publicist
       </h4>
       <h4>
        <a href="mailto:<EMAIL>">
         <EMAIL>
        </a>
       </h4>
       <h4>
       </h4>
       <p>
       </p>
       <p>
       </p>
       <h4>
        Follow us on Twitter
       </h4>
       <p>
        <a href="https://twitter.com/buzzfeedpr" target="_blank">
         @buzzfeedpr
        </a>
       </p>
      </div>
     </div>
    </div>
   </main>
  </div>
  <script id="__NEXT_DATA__" type="application/json">
   {"props":{"initialI18nStore":{"en":{"common":{"trending":"Trending on BuzzFeed"}}},"initialLanguage":"en","i18nServerInstance":null,"pageProps":{"story":{"name":"Press","created_at":"2023-07-31T18:38:50.555Z","published_at":"2024-05-20T18:10:44.849Z","updated_at":"2024-05-20T18:10:44.879Z","id":196766,"uuid":"063fd652-52a9-4b37-9063-a55efa12be0e","content":{"SEO":{"_uid":"749ffddb-10e9-4ab0-9ea4-2fb0929d8e05","title":"Press about BuzzFeed","plugin":"seo_metatags","og_image":"https://a-us.storyblok.com/f/1001779/625x417/bc03ee8f77/about-share-image.jpg","og_title":"Press about BuzzFeed","description":"Articles and press about BuzzFeed, the leading independent digital media company","twitter_image":"","twitter_title":"","og_description":"Articles and press about BuzzFeed, the leading independent digital media company","twitter_description":""},"_uid":"ae471450-5bf6-40dc-918f-4ad248e6995f","body":[{"_uid":"f4d5577b-4cf7-42c3-94fc-6ffebfda1af9","component":"buzzfeed_nav","reference":{"name":"Buzzfeed Nav","created_at":"2023-08-10T15:48:43.858Z","published_at":"2023-08-23T13:06:48.891Z","updated_at":"2023-08-23T13:06:48.914Z","id":214317,"uuid":"4b22986e-d117-44ab-812b-e71f455ae947","content":{"_uid":"a047995e-f11c-44f7-8222-3bfa52d8f02c","component":"global","nav_items":[{"_uid":"6003f1c4-ae84-4151-87b5-1c3b912730b4","title":"About","link_url":"/about?edition=us","component":"nav_Item"},{"_uid":"9dd643f4-4e26-407b-8b80-19c9272a3cbc","title":"Press","link_url":"/press?edition=us","component":"nav_Item"},{"_uid":"7167793f-7b64-4874-8d93-fe061739c938","title":"Jobs","link_url":"/jobs?edition=us","component":"nav_Item"},{"_uid":"2f6bfb93-24e2-4bfe-879d-c5e3bfdcad71","title":"Contact","link_url":"/contact?edition=us","component":"nav_Item"},{"_uid":"cdebbad6-39ea-496d-a98d-a4f54238fde0","title":"Investors","link_url":"https://investors.buzzfeed.com/","component":"nav_Item"},{"_uid":"9b398c72-1a62-4a64-b5ed-30d992fa031a","title":"Legal","link_url":"/about/privacy?edition=us","component":"nav_Item"}]},"slug":"buzzfeed-nav","full_slug":"static-pages/buzzfeed/global/buzzfeed-nav","sort_by_date":null,"position":0,"tag_list":[],"is_startpage":false,"parent_id":214316,"meta_data":null,"group_id":"a26597c6-cb35-4b3c-8876-11ead65f58e4","first_published_at":"2023-08-22T14:47:04.696Z","release_id":null,"lang":"default","path":"buzzfeed-nav/","alternates":[],"default_full_slug":null,"translated_slugs":null,"_stopResolving":true}},{"_uid":"bca8c77a-611f-4be7-be72-c6770f943a67","image":{"id":197403,"alt":"","name":"","focus":"","title":"","source":"","filename":"https://a-us.storyblok.com/f/1001779/2000x762/c158225aee/press-banner.jpg","copyright":"","fieldtype":"asset","meta_data":{},"is_external_url":false},"component":"press_background"},{"_uid":"c8955933-ea3c-4b26-8fe9-b421f66e5f21","press":[{"_uid":"e679262e-4d88-4bb3-99b1-ba7b6cbe5cc2","title":"In the Press","link_url":"https://www.buzzfeed.com/buzzfeedpress","component":"in_the_press","button_text":"See More"}],"title":"PRESS RESOURCES","component":"press_page","brand_assets":[{"_uid":"1d2740a7-468d-4f62-ac35-ff0b317b5218","content":{"type":"doc","content":[{"type":"heading","attrs":{"level":2},"content":[{"text":"Brand Assets","type":"text"}]},{"type":"paragraph","content":[{"text":"Use these official logos to represent BuzzFeed Inc. in press and media applications. Logos should appear in their original colors when possible, and should be otherwise unmodified.","type":"text"},{"type":"hard_break"},{"type":"hard_break"},{"text":"Please contact us if you have questions about proper usage of BuzzFeed Inc. logos.","type":"text"}]}]},"color_one":"#ee3322","color_two":"#0077ee","component":"brand_assets","button_link":"/press/assets?edition=us","button_text":"Download BuzzFeed Inc. Logos","color_three":"#ffee00"}],"communications":[{"_uid":"38ae8323-89a6-4a1e-aee3-e4b6004f2c3e","content":{"type":"doc","content":[{"type":"heading","attrs":{"level":2},"content":[{"text":"Communications","type":"text"}]},{"type":"paragraph","content":[{"text":"To contact the BuzzFeed Communications team, email ","type":"text"},{"text":"<EMAIL>","type":"text","marks":[{"type":"link","attrs":{"href":"mailto:<EMAIL>","uuid":null,"anchor":null,"target":null,"linktype":"url"}}]},{"text":". To contact the HuffPost News PR team, email ","type":"text"},{"text":"<EMAIL>","type":"text","marks":[{"type":"link","attrs":{"href":"mailto:<EMAIL>","uuid":null,"anchor":null,"target":null,"linktype":"url"}}]},{"text":".","type":"text"}]},{"type":"heading","attrs":{"level":6},"content":[{"text":"IMPORTANT NOTE:","type":"text","marks":[{"type":"bold"}]},{"text":" This email address does not reach editorial staff or the human resources department. Please ","type":"text"},{"text":"do not","type":"text","marks":[{"type":"bold"}]},{"text":" send press releases, editorial pitches, or job inquiries to us – they will not get in front of the right people. For press releases and pitches, please see the mastheads on section home pages for other options. For example, go to ","type":"text"},{"text":"buzzfeed.com/entertainment","type":"text","marks":[{"type":"link","attrs":{"href":"http://buzzfeed.com/entertainment","uuid":null,"anchor":null,"target":null,"linktype":"url"}}]},{"text":" to see a masthead for our entertainment contacts.","type":"text"}]},{"type":"heading","attrs":{"level":4},"content":[{"text":"Juliana Clifton","type":"text","marks":[{"type":"bold"}]}]},{"type":"heading","attrs":{"level":4},"content":[{"text":"Vice President, Communications \u0026 Chief of Staff","type":"text"}]},{"type":"heading","attrs":{"level":4},"content":[{"text":"<EMAIL>","type":"text","marks":[{"type":"link","attrs":{"href":"mailto:<EMAIL>","uuid":null,"anchor":null,"target":null,"linktype":"url"}}]}]},{"type":"paragraph","content":[{"text":" ","type":"text"}]},{"type":"paragraph","content":[{"text":" ","type":"text"}]},{"type":"heading","attrs":{"level":4},"content":[{"text":"Lizzie Grams","type":"text","marks":[{"type":"bold"}]}]},{"type":"heading","attrs":{"level":4},"content":[{"text":"Sr. Communications Manager","type":"text","marks":[{"type":"textStyle","attrs":{"color":"rgb(0, 0, 0)"}}]}]},{"type":"heading","attrs":{"level":4},"content":[{"text":"<EMAIL>","type":"text","marks":[{"type":"link","attrs":{"href":"mailto:<EMAIL>","uuid":null,"anchor":null,"target":null,"linktype":"url"}}]}]},{"type":"paragraph","content":[{"text":" ","type":"text"}]},{"type":"paragraph","content":[{"text":" ","type":"text"}]},{"type":"heading","attrs":{"level":4},"content":[{"text":"Sophia Vega","type":"text","marks":[{"type":"bold"}]}]},{"type":"heading","attrs":{"level":4},"content":[{"text":"Publicist","type":"text"}]},{"type":"heading","attrs":{"level":4},"content":[{"text":"<EMAIL>","type":"text","marks":[{"type":"link","attrs":{"href":"mailto:<EMAIL>","uuid":null,"anchor":null,"target":null,"linktype":"url"}}]}]},{"type":"heading","attrs":{"level":4}},{"type":"paragraph","content":[{"text":" ","type":"text"}]},{"type":"paragraph","content":[{"text":" ","type":"text"}]},{"type":"heading","attrs":{"level":4},"content":[{"text":"Follow us on Twitter","type":"text"}]},{"type":"paragraph","content":[{"text":"@buzzfeedpr","type":"text","marks":[{"type":"link","attrs":{"href":"https://twitter.com/buzzfeedpr","uuid":null,"anchor":null,"target":"_blank","linktype":"url"}}]}]}]},"component":"press_communications"}]}],"component":"page"},"slug":"press","full_slug":"static-pages/buzzfeed/press","sort_by_date":null,"position":-10,"tag_list":[],"is_startpage":false,"parent_id":8390,"meta_data":null,"group_id":"e7d7dd9e-d216-4b94-ad82-f5c1081d6d93","first_published_at":"2023-08-22T14:47:32.120Z","release_id":null,"lang":"default","path":"press/","alternates":[],"default_full_slug":null,"translated_slugs":null},"key":196766,"site":"buzzfeed"},"__N_SSP":true},"page":"/[...slug]","query":{"edition":"us","slug":["static-pages","buzzfeed","press"]},"buildId":"53TC_MGteSnU0pNC9AKwh","assetPrefix":"/static-assets","runtimeConfig":{"ASSET_PREFIX":"/static-assets","NODE_ENV":"production","CLUSTER":"prod","api_prefix":"/static-pages-ui-api/","base_url":"https://www.buzzfeed.com","recaptcha_site_key":"6LdijagaAAAAAGSHdtiTSGDTdHj7HsQREh9Oj-hJ","storyblok_access_token":"************************","sentry_dsn":"https://<EMAIL>/4504022444408832","one_trust_id":"10383490-509d-42d9-96d6-1a158124bd80"},"isFallback":false,"gssp":true,"customServer":true,"appGip":true,"scriptLoader":[]}
  </script>
 </body>
</html>
