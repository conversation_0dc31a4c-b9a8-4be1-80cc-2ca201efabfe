=== SECURITY VULNERABILITIES ===

=== CRITICAL SEVERITY (1 found) ===

1. Sql Injection
   Files: Unknown
   Lines: 0
   Severity: CRITICAL
   Pattern: 
   Description: SQL Injection - Database queries vulnerable to malicious SQL code
   Fix: Use parameterized queries, never concatenate SQL

=== HIGH SEVERITY (4 found) ===

1. Xss Dom
   Files: Unknown
   Lines: 0
   Severity: HIGH
   Pattern: 
   Description: DOM-based Cross-Site Scripting (XSS) - Client-side script manipulation
   Fix: Use safe DOM methods, avoid innerHTML with user data

2. Csrf Vulnerabilities
   Files: Unknown
   Lines: 0
   Severity: HIGH
   Pattern: 
   Description: Cross-Site Request Forgery (CSRF) - Unauthorized actions on behalf of user
   Fix: Implement CSRF tokens for state-changing operations

3. Weak Crypto
   Files: Unknown
   Lines: 0
   Severity: HIGH
   Pattern: 
   Description: Weak Cryptography - Use of deprecated or weak cryptographic algorithms
   Fix: Review code and implement proper security controls

4. Xss Reflected
   Files: Unknown
   Lines: 0
   Severity: HIGH
   Pattern: 
   Description: Reflected Cross-Site Scripting (XSS) - User input directly reflected in response
   Fix: Encode all user input before displaying

=== MEDIUM SEVERITY (3 found) ===

1. Information Disclosure
   Files: Unknown
   Lines: 0
   Severity: MEDIUM
   Pattern: 
   Description: Information Disclosure - Sensitive data exposed in logs/errors
   Fix: Review code and implement proper security controls

2. Parameter Injection Risk
   Files: Unknown
   Lines: 0
   Severity: MEDIUM
   Pattern: 
   Description: Security vulnerability: Parameter Injection Risk
   Fix: Review code and implement proper security controls

3. Debug Endpoint Exposure
   Files: Unknown
   Lines: 0
   Severity: MEDIUM
   Pattern: 
   Description: Security vulnerability: Debug Endpoint Exposure
   Fix: Review code and implement proper security controls

=== LOW SEVERITY (1 found) ===

1. Insecure Randomness
   Files: Unknown
   Lines: 0
   Severity: LOW
   Pattern: 
   Description: Insecure Randomness - Predictable random number generation
   Fix: Review code and implement proper security controls

